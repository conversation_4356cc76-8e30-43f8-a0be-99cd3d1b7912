<!-- Replace IssueNumber with JIRA issue number for example replace IssueNumber with CTPU1-158 then it becomes 
***JIRA Issue - [CTPU1-158](https://toyotaconnected.atlassian.net/browse/CTPU1-158)***
 -->
***JIRA Issue - [IssueNumber](https://toyotaconnected.atlassian.net/browse/IssueNumber)***

## Description

<!-- 
Please include a summary of the change and also include relevant motivation and context. List any dependencies that are required for this change.
-->


## Type of change
<!--Please check the relevant change type -->

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] This change requires a documentation update

## How Has This Been Tested?

<!-- Please describe the tests that you ran to verify your changes. Provide instructions so we can reproduce. Please also list any relevant details for your test configuration -->

- [ ] Test A
- [ ] Test B

**Test Configuration**:
* OS version(Eg: iOS 14 or Android 12):
* Device model(Eg: iPhone 13 Simulator or iPhone 13 mini real device or Android emulator or Samsung M30s Real device):
* Environment(Stag/Prod) :
* Configuration(debug/release):
* Brand(Toyota/Lexus):

**Test Credentials**:
  - Stage Environment:
     - Email/Phonenumber: 
     - Password:
  - Prod Environment:
     - Email/Phonenumber: 
     - Password:
## Developer Checklist

### MR standard

- [ ] Branch name is in the expected format like "feature/\<Issue number\>-\<short description of Issue\>"
- [ ] The MR title is in the expected format like "[\<Issue number\>]-\<short description of issue\>"
- [ ] JIRA issue link added to the MR description
- [ ] The Milestone selected in this MR?
- [ ] Developer explained the MR changes?
- [ ] Developer attached a screenshot or demo video for review reference?
- [ ] Is there a huge change in MR(here we have to fix some number of lines of changes)?
  - [ ] Does the developer explain why huge MR changes in a single MR?

### Source code
- [ ] I have performed a self-review of my own code?
- [ ] Is the analyze passed?
- [ ] Is the test passed?
- [ ] Is the source code readable?
- [ ] Is the full functionality of the ticket implemented?
  - [ ] Is this building successful?
  - [ ] Is it running on the iOS simulator?
  - [ ] Is it running on the Android emulator?
  - [ ] Is it running on a real device?
  - [ ] Is this brand(Toyota or Lexus) based on changes(Like for one brand it should behave like)?
    - [ ] Is the Toyota app tested?
    - [ ] Is the Lexus app tested?
- [ ] Is a unit test written for these changes?
  - [ ] Is 100% code coverage achieved for the MR changes?
  - [ ] Is the unit test covered all the possible scenarios?
  - [ ] Is existing test cases passed?
