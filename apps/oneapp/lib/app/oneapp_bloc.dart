// Dart imports:
import 'dart:async';
import 'dart:convert';
import 'dart:io';

// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flex_module/flex_global.dart';
import 'package:flex_module/flex_push_handler.dart';
import 'package:flex_module/route/router.dart';
import 'package:get_it/get_it.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oa_network_impl/tfs/entity/account_summary_response_dto.dart';
import 'package:oa_network_impl/tfs_authenticate/entity/authenticate_request.dart';
import 'package:oa_network_impl/tfs_authorize/entity/access_token_response.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/entity/fcm_channel_entity_entity.dart';
import 'package:oneapp_common/entity/fr_token_response.dart';
import 'package:oneapp_common/entity/navigation_entity.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/login_util.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:package_info/package_info.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/features/authentication/domain/authorize_response.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/features/core/handle_new_tokens.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/features/core/utils/utils.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/features/core/utils/widgets/custom_pdf_viewer.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/features/dashboard/domain/account.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/router/navigation_middleware.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/router/route_generator.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/services/locators.dart';
import 'package:vehicle_module/ui/vehicle_finance/wallet/wallet_home/wallet_home_page.dart';

// Project imports:
import '../provider/oneapp_provider_setup.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicle;

const BasicMessageChannel initFcmJsonChannel =
    BasicMessageChannel(nativeInitFcmJson, StandardMessageCodec());
const BasicMessageChannel vehicleListJsonChannel =
    BasicMessageChannel(oneAppVehicleListJson, StandardMessageCodec());
const BasicMessageChannel dkVehicleListJsonChannel =
    BasicMessageChannel(oneAppVehicleDkListJson, StandardMessageCodec());
const BasicMessageChannel pushClickChannel =
    BasicMessageChannel(nativePushClickJson, StandardMessageCodec());
const BasicMessageChannel appRouterChannel =
    BasicMessageChannel(pagePathJson, StandardMessageCodec());
const BasicMessageChannel hyperLinksChannel =
    BasicMessageChannel(rentalHyperLinks, StandardMessageCodec());
const BasicMessageChannel receivePushTokenChannel =
    BasicMessageChannel(receivePushToken, StandardMessageCodec());
const BasicMessageChannel receiveAccessTokenChannel =
    BasicMessageChannel(oneAppTokenJson, StandardMessageCodec());
const BasicMessageChannel receiveTfsTokenChannel =
    BasicMessageChannel(tfsTokenJson, StandardMessageCodec());
const BasicMessageChannel hapticTouchChannel =
    BasicMessageChannel(hapticTouch, StandardMessageCodec());
const BasicMessageChannel receiveLocaleChannel =
    BasicMessageChannel(localeChangedChannelName, StandardMessageCodec());
const BasicMessageChannel receiveNativeFlutterNavigationChannel =
    BasicMessageChannel(nativeFlutterNavigationChannel, StandardMessageCodec());

class OneAppBloc implements BlocBase {
  OneAppClient get api => APIClientConfig.oneAppClient;

  final _refreshController = StreamController<bool>.broadcast();

  Stream<bool> get refreshApp => _refreshController.stream;

  Function? localeChanged;

  Future<void> init() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    Global.getInstance().appVersion = packageInfo.version;
    initFcmJsonChannel.setMessageHandler(addFcmHandler);
    vehicleListJsonChannel.setMessageHandler(addVehicleListHandler);
    dkVehicleListJsonChannel.setMessageHandler(addDkVehicleListHandler);
    pushClickChannel.setMessageHandler(addPushClickHandler);
    hyperLinksChannel.setMessageHandler(addHyperLinksHandler);
    receivePushTokenChannel.setMessageHandler(addPushTokenHandler);
    receiveAccessTokenChannel.setMessageHandler(addRefreshTokenHandler);
    receiveTfsTokenChannel.setMessageHandler(addTfsTokenHandler);
    receiveLocaleChannel.setMessageHandler(addReceiveLocaleHandler);
    receiveNativeFlutterNavigationChannel
        .setMessageHandler(addNativeFlutterNavigationHandler);
    updateAppVersion();
    await Global.getInstance()
        .setFcmDeviceId(await getFcmDeviceId())
        .catchError((e) => logExceptionToFirebase(e));
  }

  void updateAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    Global.getInstance().appVersion = packageInfo.version;
  }

  Future<void> fetchDataDogFeatures() async {
    Global.getInstance().isDatadogLogEnabled = true;
    Global.getInstance().isDarkModeEnabled = true;
    Global.getInstance().isBiggerFontEnabled = false;
    Global.getInstance().isBiggerFont = false;
  }

  Future<String?> refreshAppBrand() async {
    final value = await OneAppLoginChannel.getBrand();
    Global.getInstance().appBrand = value;
    return value;
  }

  Future refreshAppAction() {
    return Future(() {
      _refreshController.add(true);
    });
  }

  @override
  void dispose() {
    _refreshController.close();
  }

  Future addNativeFlutterNavigationHandler(Object? result) async {
    if (result is String) {
      NavigateService.pushNamedRoute("/");
      NavigationEntity route = NavigationEntity.fromJson(json.decode(result));
      debugPrint("NativeFlutterNavigation: route to ${route.routeName}");
      final payload = await _parseVehicleListPayload(route.vinList);
      Global.getInstance().needTelemetryRefresh = true;
      Global.getInstance().needDashboardRefresh =
          false; //Dashboard will get new data only if is true
      if (route.isDarkTheme != null) {
        Global.getInstance().isDarkModeEnabled = route.isDarkTheme!;
        Global.getInstance().isDarkTheme = route.isDarkTheme!;
        try {
          ThemeConfig.reloadThemeData();
        } catch (e) {
          debugPrint(e.toString());
        }
        refreshAppAction();
      }
      GetIt.instance.registerSingletonAsync(() async => payload);
      Global.getInstance().setVin(route.vin);
      debugPrint("NativeFlutterNavigation: FxAccountGlobal before");
      FxAccountGlobal.getInstance().clear();
      debugPrint(
          "NativeFlutterNavigation: FxAccountGlobal.getInstance().clear();");
      await _checkNavigationKeyNotNull();
      if (route.routeName != null) {
        if (route.routeName == RoutePath.WALLET_HOME) {
          showWalletBottomSheet();
        } else if (route.routeName!.toUpperCase().contains("FINANCE_")) {
          debugPrint(
              "TFSIDTOKEN from Native: ${Global.getInstance().tfsIdToken}");
          await startTFSLocators(payload);
          if (route.routeName == RoutePath.FINANCE_OPTIONS_PAGE) {
            final Account? account =
                Account.fromDomain(AccountDTO.fromJson(route.routeData));
            FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                VehicleAnalyticsEvent.TFS_PAYMENT_CARD_CLICK_ELLIPSES);
            Global.getInstance().cuserId = account?.getUcid();
            Widget childWidget = NavigatorMiddleWare(
              account: account,
              flow: RouteFlow.options,
              fromNative: true,
            );
            launchTfsScreens(childWidget);
          } else if (route.routeName == RoutePath.FINANCE_CREATE_PAYMENT_PAGE) {
            final Account? account =
                Account.fromDomain(AccountDTO.fromJson(route.routeData));
            FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                VehicleAnalyticsEvent
                    .TFS_ONETIME_PAYMENT_MAKE_PAYMENT_CARD_CTA);
            Global.getInstance().cuserId = account?.getUcid();
            Widget childWidget = NavigatorMiddleWare(
              account: account,
              flow: RouteFlow.payment,
              fromNative: true,
            );
            launchTfsScreens(childWidget);
          } else if (route.routeName == RoutePath.FINANCE_ACCESS_ACCOUNT_PAGE) {
            AuthenticateRequest? authenticateRequest;

            if (Platform.isIOS) {
              final Map<String, dynamic> jsonData = route.routeData;

              if (jsonData.containsKey('authenticateRequest')) {
                final dynamic authenticateRequestJson =
                    jsonData['authenticateRequest'];
                final Map<String, dynamic> authenticateRequestData =
                    json.decode(authenticateRequestJson);
                authenticateRequest =
                    AuthenticateRequest.fromJson(authenticateRequestData);
              }
            } else {
              authenticateRequest =
                  AuthenticateRequest.fromJson(route.routeData);
            }
            FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                VehicleAnalyticsEvent.TFS_AUTH_SOCIAL_REQ_NEW_CODE);
            Widget childWidget = NavigatorMiddleWare(
              flow: RouteFlow.mfa,
              authenticateRequest: authenticateRequest,
              fromNative: true,
            );
            launchTfsScreens(childWidget);
          } else if (route.routeName == RoutePath.FINANCE_LINK_ACCOUNT_PAGE) {
            FireBaseAnalyticsLogger.logTFSAuthGroupEvent(
                VehicleAnalyticsEvent.TFS_AUTH_DISCLOSURE_CONTINUE_CTA);
            final Widget childWidget = NavigatorMiddleWare(fromNative: true);
            launchTfsScreens(childWidget);
          } else if (route.routeName == RoutePath.FINANCE_ONLINE_AGREEMENT) {
            launchPdfViewer(OneAppString.of().textOnlinePoliciesAgreement,
                URLS.onlinePoliciesAndAgreement());
          } else if (route.routeName ==
              RoutePath.FINANCE_ELECTRONIC_AGREEMENT) {
            launchPdfViewer(
                OneAppString.of().textElectronicCommunicationAgreementTitle,
                URLS.electronicBusinessAgreementsTfsLfs());
          }
        } else {
          NavigateService.pushNamedAndRemoveUtilRoute(route.routeName!,
              result: route.routeData);
        }
      } else {
        debugPrint("NativeFlutterNavigation: RouterName is null");
      }
    }
  }

  void launchTfsScreens(Widget childWidget) {
    Navigator.pushReplacement(
        NavigateService.context,
        FadeRoute(
          isDismissible: false,
          page: PopScope(
              onPopInvoked: (didPop) async {
                if (didPop) return;
                goBackToNative();
                return Future(() => false);
              },
              child: childWidget),
        ));
  }

  void launchPdfViewer(String title, String pdfUrl) {
    CustomPDFViewerScreenArgs args = CustomPDFViewerScreenArgs(
      title: title,
      isCenterTitle: true,
      openPdfUrl: pdfUrl,
      fromNative: true,
    );
    Navigator.pushReplacement(
      NavigateService.context,
      FadeRoute(
        isDismissible: false,
        page: PopScope(
          onPopInvoked: (didPop) async {
            if (didPop) return;
            goBackToNative();
            return Future(() => false);
          },
          child: CustomPDFViewerScreen(
            args: args,
          ),
        ),
      ),
    );
  }

  Future<void> startTFSLocators(List<vehicle.Payload> payload) async {
    final vin = Global.getInstance().vin ?? "";
    vehicle.Payload? vehicleItem;
    List<vehicle.Payload> vehicleList =
        payload.where((element) => element.vin == vin).toList();
    if (vehicleList.isNotEmpty) {
      vehicleItem = vehicleList.first;
      await setupLocators(
        firebaseAppCrashlytics: FirebaseCrashlytics.instance,
        appInitialValues: AppInitialValues(
          vin: vin,
          vehicleImage: vehicleItem.image ?? "",
          vehicleBrandType: vehicleItem.brand == "T"
              ? VehicleBrandType.TOYOTA
              : VehicleBrandType.LEXUS,
          OneTimePaymentDisabled:
              isFeatureEnabled(DISABLE_ONETIME_PAYMENT, vehicleItem.features),
          RecurringPaymentDisabled:
              isFeatureEnabled(DISABLE_RECURRING_PAYMENT, vehicleItem.features),
        ),
        onFinished: () {},
      );
    }
  }

  Future addVehicleListHandler(Object? result) async {
    debugPrint("=_= hasVehicle addVehicleListHandler");
    if (result is String) {
      final payload = await _parseVehicleListPayload(result);
      bool hasVehicle = payload.isNotEmpty == true;
      Global.getInstance().needTelemetryRefresh = true;
      Global.getInstance().needDashboardRefresh =
          false; //Dashboard will get new data only if is true
      GetIt.instance.registerSingletonAsync(() async => payload);
      debugPrint("=_= hasVehicle $hasVehicle");
      if (hasVehicle) {
        final firstVehicle = payload.first;
        debugPrint("=_= firstVehicle $firstVehicle");

        Global.getInstance().setVin(firstVehicle.vin);
      }
      debugPrint("=_= FxAccountGlobal before");
      FxAccountGlobal.getInstance().clear();
      debugPrint("=_= FxAccountGlobal.getInstance().clear();");
      await _checkNavigationKeyNotNull();
    }
  }

  Future addDkVehicleListHandler(Object? result) async {
    List<vehicle.Payload>? vehicleInfoListEntity;
    try {
      vehicleInfoListEntity = GetIt.instance<List<vehicle.Payload>>();
    } catch (e) {}
    if (vehicleInfoListEntity != null && vehicleInfoListEntity.isNotEmpty) {
      final payload = await _parseVehicleListPayload(result as String?);
      GetIt.instance.registerSingletonAsync(() async => payload);
    } else {
      if (result is String) {
        final payload = await _parseVehicleListPayload(result);
        bool hasVehicle = payload.isNotEmpty == true;
        Global.getInstance().needTelemetryRefresh = true;
        Global.getInstance().needDashboardRefresh =
            false; //Dashboard will get new data only if is true
        GetIt.instance.registerSingletonAsync(() async => payload);
        if (hasVehicle) {
          final firstVehicle = payload.first;
          Global.getInstance().setVin(firstVehicle.vin);
        }
        await _checkNavigationKeyNotNull();
        goBackToDashboardNative();
      }
    }
  }

  Future<dynamic> showWalletBottomSheet() {
    GetIt locator = GetIt.instance;
    locator.registerSingletonAsync(
        () async => await VehicleRepo().fetchWalletImages());
    ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: NavigateService.context,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(child: WalletHomePage()),
          ],
        ),
      ),
    ).whenComplete(() async {
      goBackToNative();
    });
  }

  Future addPushTokenHandler(Object? result) async {
    if (result is String) {
      await Global.getInstance().setFcmDeviceId(result);
    }
  }

  void instanceHelper(String enumName, FcmChannelEntityEntity entity) {
    FBroadcast.instance().broadcast(enumName, value: entity);
  }

  Future<dynamic> addFcmHandler(Object? result) async {
    FcmChannelEntityEntity fcmChannelEntityEntity =
        FcmChannelEntityEntity.fromJson(json.decode(result as String));
    if (fcmChannelEntityEntity.status == null) {
      return;
    }
    switch (fcmChannelEntityEntity.category) {
      case REAL_TIME_STATUS:
        {
          instanceHelper(STATUS_SILENCE_ACTION, fcmChannelEntityEntity);
        }
        break;
      case GUEST_DRIVER_PROFILE_ACTIVATION:
        {
          instanceHelper(PROFILE_ACTIVATION_ACTION, fcmChannelEntityEntity);
        }
        break;
      default:
        break;
    }
  }

  Future<dynamic> addPushClickHandler(Object? result) async {
    final ctx = NavigateService.navigator!.overlay!.context;
    debugPrint("---addPushClickHandler: $result");
    FlexPushActions.pushHandler(ctx, result!);
  }

  Future addHyperLinksHandler(Object? result) async {
    if (Global.getInstance().accessToken?.isNotEmpty == true) {
      String? uriStr = result?.toString();
      debugPrint("---addHyperLinksHandler: $uriStr");
      if (uriStr == 'rental_landing') {
        FlexRouter.pushName(
          RoutePath.FX_DETAIL_LANDING,
          arguments: {'allowCache': false},
        );
      }
    } else {
      LoginUtil.forceToLogin();
    }
  }

  Future addReceiveLocaleHandler(Object? event) async {
    debugPrint("---addReceiveLocaleHandler: ${event?.toString()}");
    if (event is String) {
      Global.getInstance().localeString = event;
      localeChanged?.call();
      refreshAppAction();
    }
  }

  Future addTfsTokenHandler(Object? event) async {
    debugPrint("---addTfsTokenHandler: ${event.toString()}");
    Map<String, dynamic> tokens = json.decode(event as String);
    AuthorizeResponse tokenResponse =
        AuthorizeResponse.fromDomain(AccessTokenResponse.fromJson(tokens));
    HandleTfsTokens.handleNewTokensTFS(tokenResponse: tokenResponse);
  }

  Future addRefreshTokenHandler(Object? event) async {
    if (Platform.isAndroid) {
      NavigateService.pushNamedRoute("/");
    }
    final jsonMap = json.decode(event as String);
    FRTokenResponse tokenResponse = FRTokenResponse.fromJson(jsonMap);
    //OneAppLoginChannel.passTokens(event);
    if (tokenResponse.success == true && tokenResponse.payload != null) {
      await LoginUtil.handleNewTokens(tokenResponse)
          .catchError((e) => logExceptionToFirebase(e));
      APIClientConfig.refresh(withProvider: OneAppProvider());
    } else if (tokenResponse.isInvalidGrant == true) {
      await LoginUtil.clearTokens()
          .catchError((e) => logExceptionToFirebase(e));
      LoginUtil.forceToLogin();
    }
  }

  Future<List<vehicle.Payload>> _parseVehicleListPayload(
      String? jsonStr) async {
    debugPrint('----_parseVehicleListPayload: $jsonStr');
    dynamic jsonMap = await compute((String? v) {
      return jsonDecode(jsonStr!);
    }, jsonStr);
    List<vehicle.Payload> payload = [];
    if (jsonMap?.isNotEmpty == true) {
      jsonMap.forEach((v) {
        payload.add(vehicle.Payload.fromJson(v));
      });
    }
    return payload;
  }

  Future<bool> _checkNavigationKeyNotNull() async {
    while (NavigateService.navigator == null) {
      // Until the navigator is not null
      await Future.delayed(Duration(milliseconds: 50));
    }
    return true;
  }
}
