// Dart imports:

// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/oneapp_ui_config.dart';
import 'package:oneapp_common/generated/l10n.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/custom_navigator_observer.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../route/router.dart';
import 'oneapp_bloc.dart';

import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart'
    show MapboxOptions;

// Project imports:

class OneApp extends StatefulWidget {
  OneApp({
    Key? key,
  }) : super(key: key);

  @override
  _OneAppState createState() => _OneAppState();
}

class _OneAppState extends State<OneApp> {
  Locale? _locale;
  dynamic navRouteData;
  late OneAppBloc _applicationBloc;

  late double _uiWidth;
  late double _uiHeight;
  double defaultTxtScaleFactor = 1.0;
  double largerTxtScaleFactor = 1.3;
  late double deviceWidth;
  late double deviceHeight;

  @override
  void initState() {
    _applicationBloc = OneAppBloc();
    _applicationBloc.localeChanged = () {
      _locale = Global.getInstance().locale;
      S.load(_locale!);
    };
    _initializeConfig();
    super.initState();
    MapboxOptions.setAccessToken(MAP_BOX_PUBLIC_TOKEN);
    debugPrint('---app initState');
  }

  void _initializeConfig() async {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _configGlobal().then((value) {
        _applicationBloc.init();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    return _appWidget(context);
  }

  Widget _appWidget(BuildContext context) {
    final botToastBuilder = BotToastInit();
    return ScreenUtilInit(
      minTextAdapt: true,
      builder: (ctx, Widget? child) => BlocProvider(
        child: StreamBuilder(
          stream: _applicationBloc.refreshApp,
          builder: (ctx, appSnapShot) {
            _configGlobal().whenComplete(
              () => debugPrint("at=_configGlobal status=complete"),
            );
            bool isBiggerFont = Global.getInstance().isBiggerFont;

            deviceWidth = ScreenUtil().screenWidth;
            deviceHeight = ScreenUtil().screenHeight;
            if (deviceWidth < 600) {
              _uiWidth = 375;
              _uiHeight = 812;
            } else if (deviceWidth >= 600 && deviceWidth < 850) {
              _uiWidth = deviceWidth;
              _uiHeight = deviceHeight;
              defaultTxtScaleFactor = 1.2;
            } else {
              _uiWidth = deviceWidth;
              _uiHeight = deviceHeight;
              defaultTxtScaleFactor = 1.4;
            }
            double txtScaleFactor = defaultTxtScaleFactor;
            if (isBiggerFont == true) {
              txtScaleFactor = largerTxtScaleFactor;
            }
            return MaterialApp(
              builder: (context, child) {
                ScreenUtil.init(context, designSize: Size(_uiWidth, _uiHeight));
                OneAppUiConfig.appContext = context;
                child = botToastBuilder(context, child);
                _configTheme();
                MediaQuery.of(context).textScaler.scale(txtScaleFactor);
                return child;
              },
              navigatorKey: NavigateService.key,
              localizationsDelegates: [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
                S.delegate
              ],
              supportedLocales: S.delegate.supportedLocales,
              locale: _locale,
              localeResolutionCallback:
                  (Locale? locale, Iterable<Locale> supportedLocales) {
                return _locale;
              },
              debugShowCheckedModeBanner: false,
              theme: ThemeConfig.current().themeUtil,
              // initialRoute: RoutePath.SPLASH_PAGE,
              onGenerateRoute: AppRouter.generateRoute,
              navigatorObservers: [
                BotToastNavigatorObserver(),
                CustomNavigatorObserver()
              ],
            );
          },
        ),
        bloc: _applicationBloc,
      ),
    );
  }

  Future<void> _configGlobal() async {
    debugPrint("at=_configGlobal step=fetchDataDogFeatures step=start");
    await _applicationBloc.fetchDataDogFeatures();
    debugPrint("at=_configGlobal step=fetchDataDogFeatures step=finish");
  }

  void _configTheme() {
    try {
      ThemeConfig.reloadThemeData();
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
