// Flutter imports:

// Dart imports:
import 'dart:convert';
import 'dart:io';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:collection/collection.dart';
import 'package:ev_module/charge_info/electric_vehicle_charge_info_page.dart';
import 'package:flex_module/route/router.dart';
import 'package:geocoding/geocoding.dart' as geoCoding;
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_offers_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/select_preferred_dealer_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_trip_card_helper.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/vehicle_charge_info/clean_assist/vehicle_clean_assist_consent_detail_page.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/vehicle_search_charge_station_location/vehicle_search_charge_station_location_page.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/vehicle_search_station_charging_management/ev_charging_management_page.dart';
import 'package:vehicle_module/ui/maintenance_schedule/service_history/add_service_record/add_service_record.dart';
import 'package:vehicle_module/ui/maintenance_schedule/service_history/vehicle_service_history_page.dart';
import 'package:vehicle_module/ui/maintenance_schedule/vehicle_maintenance_schedule_page.dart';
import 'package:vehicle_module/ui/service_shop_prefer_dealer/preferred_dealer_filter_bar.dart';
import 'package:vehicle_module/ui/service_shop_prefer_dealer/select_preferred_dealer_page.dart';
import 'package:vehicle_module/ui/service_shop_prefer_dealer/vehicle_dealer_detail_page.dart';
import 'package:vehicle_module/ui/vehicle_account/vehicle_account_page.dart';
import 'package:vehicle_module/ui/vehicle_account/vehicle_announcement_list/vehicle_announcements_list_page.dart';
import 'package:vehicle_module/ui/vehicle_charge_management/vehicle_charge_management_page.dart';
import 'package:vehicle_module/ui/vehicle_charge_station_location/vehicle_charge_station_location_page.dart';
import 'package:vehicle_module/ui/vehicle_climate/vehicle_climate_page.dart';
import 'package:vehicle_module/ui/vehicle_driver_alerts/vehicle_driver_alert_page.dart';
import 'package:vehicle_module/ui/vehicle_glove_box/vehicle_glove_box_page.dart';
import 'package:vehicle_module/ui/vehicle_health_alerts/vehicle_health_alert_detail.dart';
import 'package:vehicle_module/ui/vehicle_overview/vehicle_announcement/vehicle_announcement_ev_swap_overview.dart';
import 'package:vehicle_module/ui/vehicle_parked_locator/vehicle_garage_map/vehicle_garage_map_page.dart';
import 'package:vehicle_module/ui/vehicle_parked_locator/vehicle_parked_locator_feedback/vehicle_parked_locator_feedback_page.dart';
import 'package:vehicle_module/ui/vehicle_preferred_service_dealer/vehicle_dealer_filter_page.dart';
import 'package:vehicle_module/ui/vehicle_preferred_service_dealer/vehicle_preferred_service_dealer_page.dart';
import 'package:vehicle_module/ui/vehicle_service_appointment/vehicle_service_appointment_advisor/vehicle_service_appointment_advisor_page.dart';
import 'package:vehicle_module/ui/vehicle_service_appointment/vehicle_service_appointment_dashboard/vehicle_service_appointment_dashboard_page.dart';
import 'package:vehicle_module/ui/vehicle_service_appointment/vehicle_service_appointment_detail/appointment_advisor_detail_page.dart';
import 'package:vehicle_module/ui/vehicle_service_appointment/vehicle_service_appointment_detail/appointment_detail_page.dart';
import 'package:vehicle_module/ui/vehicle_service_appointment/vehicle_service_appointment_initial/vehicle_service_appointment_initial_page.dart';
import 'package:vehicle_module/ui/vehicle_service_appointment/vehicle_service_appointment_odometer_setup/vehicle_service_appointment_odometer_setup_page.dart';
import 'package:vehicle_module/ui/vehicle_service_appointment/vehicle_service_appointment_service/vehicle_service_appointment_service_page.dart';
import 'package:vehicle_module/ui/vehicle_service_appointment/vehicle_service_appointment_transportation/vehicle_service_appointment_transportation_page.dart';
import 'package:vehicle_module/ui/vehicle_service_appointment/vehicle_service_appointment_update/vehicle_service_appointment_update_page.dart';
import 'package:vehicle_module/ui/vehicle_sirius_xm_detail/vehicle_sirius_xm_detail_page.dart';
import 'package:vehicle_module/ui/vehicle_software/vehicle_software_page.dart';
import 'package:vehicle_module/ui/vehicle_subscriptions/vehicle_subscriptions_page.dart';
import 'package:vehicle_module/ui/vehicle_trip/vehicle_trip_page.dart';
import 'package:vehicle_module/ui/vehicle_trip_detail/vehicle_trip_detail_page.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart'
    as serviceHistory;

class AppRouter {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routeValue = FlexRouter.generateRoute(settings);
    if (routeValue != null) {
      return routeValue;
    }
    GetIt locator = GetIt.instance;
    switch (settings.name) {
      case RoutePath.APPOINTMENT_DASHBOARD:
        return MaterialPageRoute(
          builder: (_) => AppointmentDashboardPage(),
        );
      case RoutePath.APPOINTMENT_DETAIL:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        String? appointmentId = map['appointmentId'];
        String? dealerId = map['dealerId'];
        final appointmentUIItem = map['appointmentUIItem'];
        bool? isUpComing = map['isUpComing'];
        return MaterialPageRoute(
          builder: (_) => AppointmentDetailPage(
            appointmentId: appointmentId,
            dealerId: dealerId,
            appointmentUIItem: appointmentUIItem,
            isUpComing: isUpComing,
          ),
        );
      case RoutePath.VEHICLE_SIRIUS_XM_DETAIL_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleSiriusXmDetailPage(
            siriusXmDetailPayload: settings.arguments as VehicleRadio?,
          ),
        );
      case RoutePath.GLOVE_BOX_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleGloveBoxPage(),
        );
      case RoutePath.VEHICLE_TRIP_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleTripPage(tripDetails: settings.arguments),
        );
      case RoutePath.VEHICLE_PARKED_LOCATOR_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleGarageMapPage(),
        );
      case RoutePath.VEHICLE_TRIP_DETAIL_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleTripDetailPage(
              vehicleTripCardHelper:
                  settings.arguments as VehicleTripCardHelper?),
        );
      case RoutePath.PREFERRED_SERVICE_DEALER:
        return MaterialPageRoute(
          builder: (_) => VehiclePreferredServiceDealerPage(
              preferredDealerObject: settings.arguments),
        );
      case RoutePath.DEALER_FILTER:
        return MaterialPageRoute(
          builder: (_) => VehicleDealerFilterPage(),
        );
      case RoutePath.VEHICLE_PARKED_LOCATOR_FEEDBACK_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleParkedLocatorFeedback(),
        );
      case RoutePath.UPDATE_SERVICE_RECORD_PAGE:
        return MaterialPageRoute(
          builder: (_) => AddServiceRecordPage(
              payload: settings.arguments as ServiceHistories?),
        );
      case RoutePath.VEHICLE_SEARCH_CHARGE_STATION_LOCATION:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        bool isFromNative = map['isFromNative'] ?? true;
        return MaterialPageRoute(
          builder: (_) => _findStationScreen(isFromNative),
          fullscreenDialog: true,
          maintainState: true,
        );
      case RoutePath.VEHICLE_CHARGE_MANAGEMENT_PAGE:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        bool isFromNative = map['isFromNative'] ?? true;
        return MaterialPageRoute(
          builder: (_) => EvChargingManagementPage(isFromNative: isFromNative),
          fullscreenDialog: true,
        );
      case RoutePath.VEHICLE_ACCOUNT_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleAccountPage(),
        );
      case RoutePath.FX_SELECTPREFERDEALERPAGE:
        PreferredDealerStatus? preferredDealerStatus;
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        String isFromNative = "false";
        bool isFromOdometerFlow = map['isFromOdometerFlow'] ?? false;
        try {
          String? dealerStatus =
              map['preferredDealerStatus'].toString().split('.').last;
          if (map['isFromNative'] != null) {
            isFromNative = map['isFromNative'];
          }
          preferredDealerStatus = PreferredDealerStatus.values
              .firstWhereOrNull((e) => e.name == dealerStatus);
        } catch (e) {
          preferredDealerStatus = PreferredDealerStatus.EMPTY_PREFERRED_DEALER;
        }

        return MaterialPageRoute(
          settings: RouteSettings(name: RoutePath.FX_SELECTPREFERDEALERPAGE),
          builder: (_) => SelectPreferredDealerPage(
              preferredDealerStatus: preferredDealerStatus,
              isFromNative: isFromNative == 'true',
              isFromOdometerFlow: isFromOdometerFlow),
        );
      case RoutePath.FX_VEHICLEDEALERDETAILPAGE:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        String isFromNative = "false";
        String isFromSelectDealer = "false";
        bool isFromOdometerFlow = map['isFromOdometerFlow'] ?? false;
        PreferredDealerStatus? preferredDealerStatus;
        if (map['isFromNative'] != null) {
          isFromNative = map['isFromNative'];
        }
        if (map['directFromSelectDealer'] != null) {
          isFromSelectDealer = map['directFromSelectDealer'];
        }
        try {
          String? dealerStatus =
              map['preferredDealerStatus'].toString().split('.').last;
          // ignore: unnecessary_null_comparison
          if (dealerStatus != null) {
            preferredDealerStatus = PreferredDealerStatus.values
                // ignore: sdk_version_since
                .firstWhere((e) => e.name == dealerStatus);
          } else {
            preferredDealerStatus = map['preferredDealerStatus'];
          }
        } catch (e) {
          preferredDealerStatus = PreferredDealerStatus.EMPTY_PREFERRED_DEALER;
        }

        String? dealerCode = map['dealerCode'];
        DealerInfoHelper? dealerInfoHelper = map['dealerInfoHelper'];
        if (dealerInfoHelper == null) {
          dealerInfoHelper = DealerInfoHelper(
            dealershipName: map['dealerName'] ?? '',
            zipCode: map['zip'] ?? '',
            state: map['state'] ?? '',
            country: map['country'] ?? '',
            city: map['city'] ?? '',
            line1: map['address'] ?? '',
          );
        }
        String odometerValue = map['odometerValue'] ?? "";
        String odometerUnit = map['odometerUnit'] ?? "";
        return MaterialPageRoute(
          builder: (_) => VehicleDealerDetailPage(
              dealerCode: dealerCode,
              preferredDealerStatus: preferredDealerStatus,
              dealerInfoHelper: dealerInfoHelper,
              isFromNative: isFromNative == 'true',
              FromSelectDealer: isFromSelectDealer == 'true',
              isFromOdometerFlow: isFromOdometerFlow,
              odometerUnit: odometerUnit,
              odometerValue: odometerValue),
        );
      case RoutePath.MAINTANANCE_SCHEDULE:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        String odometerValue = map['odometerValue'] ?? "";
        String odometerUnit = map['odometerUnit'] ?? "";
        String? vin = map['vin'];
        return MaterialPageRoute(
          builder: (_) => VehicleMaintenanceSchedulePage(
            fromNative: true,
            odometerValue: odometerValue,
            odometerUnit: odometerUnit,
            vin: vin,
          ),
        );
      case RoutePath.SERVICE_HISTORY:
        serviceHistory.Payload payload;
        if (Platform.isAndroid) {
          Map<dynamic, dynamic> jsonData =
              json.decode(settings.arguments.toString());
          payload = serviceHistory.Payload.fromJson(jsonData);
        } else {
          final Map map = settings.arguments as Map<dynamic, dynamic>;
          payload = serviceHistory.Payload.fromJson(map);
        }
        locator.registerSingletonAsync(() async => payload);
        return MaterialPageRoute(
          builder: (_) => VehicleServiceHistoryPage(
            fromNative: true,
          ),
        );
      case RoutePath.APPOINTMENTS:
        return MaterialPageRoute(
          builder: (_) => AppointmentDashboardPage(
              fromNative: true, allowCache: false, enableCacheLoader: false),
        );
      case RoutePath.FX_PREFERREDDEALERFILTERBAR:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        int? section = map['sectionIndex'];
        DealerFilterModel? filterModel = map['filterModel'];
        return MaterialPageRoute(
          builder: (_) => PreferredDealerFilterBar(
            sectionIndex: section,
            filterModel: filterModel,
          ),
        );
      case RoutePath.VEHICLE_SERVICE_APPOINTMENT_INITIAL_PAGE:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        String odometerValue = map['odometerValue'] ?? "";
        String odometerUnit = map['odometerUnit'] ?? "";
        return MaterialPageRoute(
          settings: RouteSettings(
              name: RoutePath.VEHICLE_SERVICE_APPOINTMENT_INITIAL_PAGE),
          builder: (_) => VehicleServiceAppointmentInitialPage(
              odometerValue: odometerValue,
              odometerUnit: odometerUnit,
              vehicleHealthPayLoad: null,
              serviceAppointmentSubTitle: null),
        );
      case RoutePath.VEHICLE_ANNOUNCEMENT_EV_SWAP_OVERVIEW:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        String avlCredit = map['avlCredit'] ?? "";
        return MaterialPageRoute(
          builder: (_) =>
              VehicleAnnouncementEVSwapOverView(avlCredit: avlCredit),
        );
      case RoutePath.CLEAN_ASSIST_DETAIL_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleCleanAssistConsentDetailPage(),
        );
      case RoutePath.VEHICLE_SUBSCRIPTIONS_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleSubscriptionsPage(),
        );
      case RoutePath.VEHICLE_SERVICE_APPOINTMENT_SERVICE_PAGE:
        final Map? map = settings.arguments as Map<dynamic, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => VehicleServiceAppointmentServicePage(
            selectedServiceListData: map!["selectedServiceListData"],
          ),
        );
      case RoutePath.VEHICLE_SERVICE_APPOINTMENT_ADVISOR_PAGE:
        final Map? map = settings.arguments as Map<dynamic, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => VehicleServiceAppointmentAdvisorPage(
            serviceId: map!["serviceId"],
            preSelectedAdvisor: map["preSelectedAdvisor"],
          ),
        );
      case RoutePath.VEHICLE_SERVICE_APPOINTMENT_TRANSPORTATION_PAGE:
        final Map? map = settings.arguments as Map<dynamic, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => VehicleServiceAppointmentTransportationPage(
            preSelectedTransportation: map!["selectedTransportationData"],
            serviceId: map["servicesId"],
          ),
        );
      case RoutePath.VEHICLE_SERVICE_APPOINTMENT_TIME_PAGE:
        return MaterialPageRoute(
          builder: (_) => VehicleSubscriptionsPage(),
        );
      case RoutePath.VEHICLE_SERVICE_APPOINTMENT_ODOMETER_SETUP_PAGE:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        bool? isInitialPage = map['isInitialPage'];
        final preferredDealerInfo = map['preferredDealerInfo'];
        String? odometerValue = map['odometerValue'];
        bool? comeFromConfirmPage = map['comeFromConfirmPage'];
        return MaterialPageRoute(
          settings: RouteSettings(
              name: RoutePath.VEHICLE_SERVICE_APPOINTMENT_ODOMETER_SETUP_PAGE),
          builder: (_) => VehicleServiceAppointmentOdometerSetupPage(
            isInitialPage: isInitialPage,
            preferredDealerInfo: preferredDealerInfo,
            odometerValue: odometerValue == "0" ? null : odometerValue,
            comeFromConfirmPage: comeFromConfirmPage,
          ),
        );
      case RoutePath.VEHICLE_SERVICE_APPOINTMENT_UPDATE_PAGE:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        String? appointmentId = map['appointmentId'];
        return MaterialPageRoute(
          settings: RouteSettings(
              name: RoutePath.VEHICLE_SERVICE_APPOINTMENT_UPDATE_PAGE),
          builder: (_) => VehicleServiceAppointmentUpdatePage(
            appointmentId: appointmentId,
          ),
        );
      case RoutePath.VEHICLE_ANNOUNCEMENTS:
        return MaterialPageRoute(
          builder: (_) => VehicleAnnouncementsPage(
              announcementPayloadObj: settings.arguments),
        );
      case RoutePath.VEHICLE_HEALTH_DETAIL_PAGE:
        return PageRouteBuilder(
          pageBuilder: (_, __, ___) =>
              VehicleHealthAlertDetailPage(detailInfo: settings.arguments),
          transitionDuration: Duration.zero,
        );
      case RoutePath.VEHICLE_APPOINTMENT_ADVISOR_DETAIL_PAGE:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        return MaterialPageRoute(
          builder: (_) => AppointmentAdvisorDetailPage(
            advisorVCardDetails: map["advisorVCardDetails"],
          ),
        );

      /// Electric vehicle charge info
      case RoutePath.EV_CHARGEINFO_PAGE:
        bool? shouldStartCharging = false;
        if (Platform.isAndroid) {
          Map<dynamic, dynamic> jsonData =
              json.decode(settings.arguments.toString());
          shouldStartCharging = jsonData["isChargeNow"];
        } else {
          final Map map = settings.arguments as Map<dynamic, dynamic>;
          shouldStartCharging = map['isChargeNow'];
        }
        return MaterialPageRoute(
          builder: (_) => Scaffold(
              backgroundColor: ThemeConfig.current().colorUtil.tile01,
              body: Padding(
                padding: const EdgeInsets.only(top: 30.0),
                child: ElectricVehicleChargeInfoPage(
                    shouldStartCharging: shouldStartCharging),
              )),
          fullscreenDialog: true,
        );
      case RoutePath.VEHICLE_CHARGE_MANAGEMENT_SCREEN:
        // final Map map = settings.arguments as Map<dynamic, dynamic>;
        vehicleInfoNativePayload? nativePayload;
        if (Platform.isAndroid) {
          Map<dynamic, dynamic> jsonData =
              json.decode(settings.arguments.toString());
          nativePayload = vehicleInfoNativePayload.fromJson(jsonData);
        } else {
          nativePayload = null;
        }
        return MaterialPageRoute(
          builder: (_) => Scaffold(
              backgroundColor: ThemeConfig.current().colorUtil.tile01,
              body: Padding(
                padding: const EdgeInsets.only(top: 25.0),
                child: VehicleChargeManagementPage(
                  isCloseToHome: nativePayload?.isCloseToHome,
                  shouldStartCharging: nativePayload?.shouldStartCharging,
                ),
              )),
          fullscreenDialog: true,
        );
      case RoutePath.VEHICLE_CLIMATE_PAGE:
        return MaterialPageRoute(
            builder: (_) => VehicleClimatePage(), fullscreenDialog: true);
      case RoutePath.VEHICLE_DRIVER_ALERT_PAGE:
        return MaterialPageRoute(
            builder: (_) => VehicleDriverAlertPage(), fullscreenDialog: true);
      case RoutePath.GO_TO_VEHICLE_INFO_SOFTWARE_UPDATE:
        final Map map = settings.arguments as Map<dynamic, dynamic>;
        int? notificationStatus = map['notificationStatus'];
        return MaterialPageRoute(
            builder: (_) =>
                VehicleSoftwarePage(softwareStatus: notificationStatus));
      default:
        return defaultRoute(settings);
    }
  }

  static PageRouteBuilder defaultRoute(RouteSettings settings) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          body: Container(),
        );
      },
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return child;
      },
    );
  }
}

Widget _findStationScreen(bool isFromNative) {
  return FutureBuilder<bool>(
      initialData: false,
      future: VehicleRepo().fetchIsEvPublicChargingEnabled(),
      builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
        switch (snapshot.connectionState) {
          case ConnectionState.waiting:
            return Container();
          default:
            return snapshot.data == true
                ? VehicleSearchChargeStationLocationPage(
                    isFromNative: isFromNative)
                : VehicleChargeStationLocationPage(isFromNative: isFromNative);
        }
      });
}

class vehicleInfoNativePayload {
  geoCoding.Location? customerlocation;
  String? devicePosition;
  bool? shouldStartCharging;
  bool? isCloseToHome;

  vehicleInfoNativePayload(
      {geoCoding.Location? customerlocation,
      String? devicePosition,
      bool? shouldStartCharging,
      bool? isCloseToHome});

  vehicleInfoNativePayload.fromJson(dynamic json) {
    customerlocation = json["homeCoordinates"];
    devicePosition = json["currentLocation"];
    isCloseToHome = json["isCloseToHome"];
    shouldStartCharging = json["isChargeNow"];
  }

  Map<String, dynamic> toJson() {
    var map = <String, dynamic>{};
    map["homeCoordinates"] = customerlocation;
    map["currentLocation"] = devicePosition;
    map["isCloseToHome"] = isCloseToHome;
    map["isChargeNow"] = shouldStartCharging;
    return map;
  }
}
