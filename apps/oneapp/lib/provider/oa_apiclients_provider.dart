// Package imports:
import 'package:oa_network/api_client/api_client_config.dart';
import 'package:oa_network/core/oa_headers.dart';
import 'package:oa_network_impl/api_clients.dart';
import 'package:oneapp_common/global.dart';

// Project imports:
import 'oa_network_logger.dart';
import 'oa_token_provider.dart';
import 'tfs_token_provider.dart';

class OAApiClientsProvider extends ApiClientsProvider {
  @override
  OAHeaders get oneappHeaders {
    return OAHeaders(
      locale: Global.getInstance().locale.toLanguageTag(),
      appBrand: Global.getInstance().appBrand,
      appVersion: Global.getInstance().appVersion,
      correlationId: Global.getInstance().correlationId,
      guid: Global.getInstance().guid,
    );
  }

  @override
  OAApiClientConfig get config {
    return OAApiClientConfig(
      proxyAddress: Global.getInstance().proxyAddress,
      handleInvalidConnection: OATokenProvider.forceToLogin,
      refreshToken: OATokenProvider.refreshAccessToken,
      networkLogger: OANetworkLogger.doLog,
      getToken: OATokenProvider.getOAAccessToken,
    );
  }

  @override
  TFSClientConfig get tfsConfig {
    return TFSClientConfig(
      proxyAddress: Global.getInstance().proxyAddress,
      refreshToken: TFSTokenProvider.refreshTFSIdToken,
      tmnaRefreshToken: OATokenProvider.refreshAccessToken,
      networkLogger: OANetworkLogger.doLog,
      getToken: TFSTokenProvider.getTFSIdToken,
    );
  }
}
