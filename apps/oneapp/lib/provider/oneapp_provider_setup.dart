// Package imports:

// Package imports:
import 'package:oneapp_common/global.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/ev_api_client.dart';
import 'package:oneapp_network_implementation/ev/ev_client_factory.dart';
import 'package:oneapp_network_implementation/finance/finance_api_client.dart';
import 'package:oneapp_network_implementation/finance/finance_client_factory.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_cfai_api_client.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_client_factory.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/api_client_factory.dart';

// Project imports:
import 'refresh_action_token.dart';

class OneAppProvider implements APIClientProvider {
  @override
  EVApiClient createEVApiClient() {
    return EVClientFactory.createEVClient(
      correlationId: Global.getInstance().correlationId,
      locale: Global.getInstance().locale.toLanguageTag(),
      guid: Global.getInstance().guid,
      vin: Global.getInstance().vin,
      isCY17: Global.getInstance().isCY17,
      appBrand: Global.getInstance().appBrand,
      appVersion: Global.getInstance().appVersion,
      proxyAddressPort: () => Global.getInstance().proxyAddress ?? "",
      executeRefreshToken: RefreshTokenAction(),
    );
  }

  @override
  FinanceApiClient createFinanceClient() {
    return FinanceClientFactory.createFinanceClient(
        correlationId: Global.getInstance().correlationId,
        locale: Global.getInstance().locale.toLanguageTag(),
        guid: Global.getInstance().guid,
        vin: Global.getInstance().vin,
        isCY17: Global.getInstance().isCY17,
        appBrand: Global.getInstance().appBrand,
        appVersion: Global.getInstance().appVersion,
        proxyAddressPort: () => Global.getInstance().proxyAddress ?? "",
        executeRefreshToken: RefreshFinanceTokenAction());
  }

  @override
  FXCFAIClient createFlexClient() {
    return FlexClientFactory.createCFAIFlex(
        correlationId: Global.getInstance().correlationId,
        locale: Global.getInstance().locale.toLanguageTag(),
        guid: Global.getInstance().guid,
        vin: Global.getInstance().vin,
        isCY17: Global.getInstance().isCY17,
        appBrand: Global.getInstance().appBrand,
        appVersion: Global.getInstance().appVersion,
        executeRefreshToken: RefreshTokenAction(),
        proxyAddressPort: () => Global.getInstance().proxyAddress!,
        executeLogger: ExecuteLogger());
  }

  @override
  OneAppClient createOneAppClient() {
    return ApiClientFactory.createOneAppClient(
        locale: Global.getInstance().locale.toLanguageTag(),
        guid: Global.getInstance().guid,
        vin: Global.getInstance().vin,
        appBrand: Global.getInstance().appBrand,
        appVersion: Global.getInstance().appVersion,
        executeRefreshToken: RefreshTokenAction(),
        proxyAddressPort: () => Global.getInstance().proxyAddress!,
        executeLogger: ExecuteLogger());
  }
}
