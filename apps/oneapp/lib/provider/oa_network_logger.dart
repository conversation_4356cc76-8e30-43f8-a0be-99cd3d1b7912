// Package imports:
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/enum_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';

class OANetworkLogger {
  static Future<void> doLog(String? category, String logLevel, String msg,
      {Map? eventParam}) async {
    LogCategory? cat =
        EnumUtil.fromString(LogCategory.values, category ?? "UNKNWON");
    LogLevel? lvl = EnumUtil.fromString(LogLevel.values, logLevel);

    FireBaseAnalyticsLogger.logger(
        cat ?? LogCategory.FL, lvl ?? LogLevel.INFO, msg,
        eventParam: eventParam);

    return Future.value(null);
  }
}
