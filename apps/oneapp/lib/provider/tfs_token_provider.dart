// Package imports:
import 'package:collection/collection.dart';
import 'package:oa_network/configs/service_config.dart';
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs_authenticate/constants/header_values.dart';
import 'package:oa_network_impl/tfs_authorize/constants/header_keys.dart';
import 'package:oa_network_impl/tfs_authorize/entity/access_token_response.dart';
import 'package:oa_network_impl/tfs_authorize/entity/refresh_token_request.dart';
import 'package:oneapp_common/global.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/features/core/handle_new_tokens.dart';

class TFSTokenProvider {
  static String? getTFSIdToken() {
    return Global.getInstance().tfsIdToken;
  }

  static void _setTfsTokens(String token) {
    Global.getInstance().setTfsIdToken(token);
  }

  static Future<String?> refreshTFSIdToken() async {
    final String refreshToken = Global.getInstance().tfsRefreshToken ?? '';

    final Map<String, String> dataBody = <String, String>{
      GRANT_TYPE: REFRESH_TOKEN_GRANT_VALUE,
      CLIENT_ID: CLIENT_ID_VALUE,
      CLIENT_SECRET: ServiceConfig.keys.tfsFrClientSecret,
      REFRESH_TOKEN: refreshToken
    };

    try {
      RefreshTokenRequest? requestBody = RefreshTokenRequest.fromJson(dataBody);
      final CommonTfsResponse<AccessTokenResponse> tfsFrTokenResponse =
          await APIClients.tfsAuthorizeApiClient
              .refreshToken(body: requestBody);

      if ((tfsFrTokenResponse.response?.error?.isNotEmpty ?? false)) {
        return null;
      }
      await handleNewTokensTFS(tfsFrTokenResponse.response);
      final token = tfsFrTokenResponse.response?.idToken ??
          Global.getInstance().tfsIdToken;
      if (token != null) {
        _setTfsTokens(token);
      }
      return token;
    } catch (e) {
      await HandleTfsTokens.clearTfsTokens();
      return null;
    }
  }

  static List<String> errorStrings = [
    'invalid grant',
    'invalid_grant',
    'invalid scope',
    'invalid_scope',
    'token has expired',
    'invalid id token',
    'expired token'
  ];

  static String? validateTokenGrant(String error) =>
      errorStrings.firstWhereOrNull(
          (element) => error.toLowerCase().contains(element.toLowerCase()));

  static Future<void> handleNewTokensTFS(
      AccessTokenResponse? tokenResponse) async {
    await Global.getInstance().setTfsIdToken(tokenResponse?.idToken);
    await Global.getInstance().setTfsRefreshToken(tokenResponse?.refreshToken);
    await Global.getInstance().setTfsAccessToken(tokenResponse?.accessToken);
    await Global.getInstance()
        .setTfsTokenExpirationTime(tokenResponse?.expiresIn);
  }
}
