// Flutter imports:
import 'package:flutter/foundation.dart';

// Package imports:
import 'package:oneapp_common/entity/fr_token_response.dart';
import 'package:oneapp_common/forgerock/oneapp_fr_channel.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/login_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';

// Project imports:
import 'oneapp_provider_setup.dart';

class OATokenProvider {
  static String? getOAAccessToken() {
    return Global.getInstance().accessToken;
  }

  static void _newAccessToken(String token) {
    Global.getInstance().setAccessToken(token);
    APIClientConfig.refresh(withProvider: OneAppProvider());
    LoginUtil.passTokensToNative();
  }

  static Future<String?> refreshAccessToken() async {
    FRTokenResponse? frTokenResponse = await OneAppFR.refreshToken();
    debugPrint("---refreshAccessToken: ${frTokenResponse?.toJson()}");
    if (frTokenResponse?.success == false &&
        frTokenResponse?.isInvalidGrant == true) {
      await LoginUtil.clearTokens().catchError((e) =>
          FireBaseAnalyticsLogger.logError(e.toString(),
              category: LogCategory.FL_INTERNAL));
      forceToLogin();
      return null;
    }
    await LoginUtil.handleNewTokens(frTokenResponse);
    final token = frTokenResponse?.payload?.accessToken ??
        Global.getInstance().accessToken;
    if (token != null) {
      _newAccessToken(token);
    }
    return token;
  }

  static Future forceToLogin() {
    return LoginUtil.forceToLogin();
  }
}
