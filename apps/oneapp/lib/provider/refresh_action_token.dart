// Dart imports:
import 'dart:convert';

// Flutter imports:
import 'package:flutter/foundation.dart';

// Package imports:
import 'package:oneapp_common/entity/fr_token_response.dart';
import 'package:oneapp_common/forgerock/oneapp_fr_channel.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/enum_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/login_util.dart';
import 'package:oneapp_network/interceptor/execute_refresh_token.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/finance/entity_helper/finance_account_info_helper.dart';

// Project imports:
import 'oneapp_provider_setup.dart';

class RefreshTokenAction implements ExecuteRefreshToken {
  @override
  Future<void> newAccessToken(String token) async {
    await Global.getInstance().setAccessToken(token);
    APIClientConfig.refresh(withProvider: OneAppProvider());
    await LoginUtil.passTokensToNative();
  }

  @override
  Future<String?> refreshAccessToken({bool? forceRefresh}) async {
    FRTokenResponse? frTokenResponse = await OneAppFR.refreshToken();
    debugPrint("---refreshAccessToken: ${frTokenResponse?.toJson()}");
    if (frTokenResponse?.success == false &&
        frTokenResponse?.isInvalidGrant == true) {
      await LoginUtil.clearTokens()
          .catchError((e) => logExceptionToFirebase(e));
      forceToLogin();
      return null;
    }
    await LoginUtil.handleNewTokens(frTokenResponse)
        .catchError((e) => logExceptionToFirebase(e));
    return frTokenResponse?.payload?.accessToken ??
        Global.getInstance().accessToken;
  }

  @override
  Future forceToLogin() {
    return LoginUtil.forceToLogin();
  }

  @override
  Future backToLogin(
      {bool canClearKeepMeSignIn = false, bool canClearBiometric = false}) {
    return LoginUtil.backToLogin(
        canClearKeepMeSignIn: canClearKeepMeSignIn,
        canClearBiometric: canClearBiometric);
  }
}

class RefreshFinanceTokenAction extends RefreshTokenAction {
  @override
  Future<String?> refreshAccessToken({bool? forceRefresh}) async {
    String financeAccountInfo = await OneAppFR.refreshFinanceToken() ?? "";
    if (financeAccountInfo.isEmpty) {
      return null;
    }

    await Global.getInstance().setFinanceAccountInfo(financeAccountInfo);
    FinancialAccountInfoHelper financeInfo =
        FinancialAccountInfoHelper.fromJson(jsonDecode(financeAccountInfo));
    await Global.getInstance().setFinanceAccessToken(financeInfo.accessToken);
    return financeAccountInfo;
  }
}

class ExecuteLogger implements ExecuteLoggerInterface {
  @override
  Future<void> doLog(String? category, String logLevel, String msg,
      {Map? eventParam}) async {
    LogCategory? cat =
        EnumUtil.fromString(LogCategory.values, category ?? "UNKNWON");
    LogLevel? lvl = EnumUtil.fromString(LogLevel.values, logLevel);

    if (eventParam == null) {
      FireBaseAnalyticsLogger.logger(
        cat ?? LogCategory.FL,
        lvl ?? LogLevel.INFO,
        msg,
      );
    } else {
      FireBaseAnalyticsLogger.logger(
          cat ?? LogCategory.FL, lvl ?? LogLevel.INFO, msg,
          eventParam: eventParam);
    }
    return Future.value(null);
  }
}
