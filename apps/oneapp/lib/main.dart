// Dart imports:
import 'dart:async';
import 'dart:convert';
import 'dart:io';

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:oa_network/configs/service_config.dart';
import 'package:oa_network_impl/api_clients.dart';
import 'package:oneapp_common/SecurePreferences.dart';
import 'package:oneapp_common/config/api_config_params.dart';
import 'package:oneapp_common/config/global_config_params.dart';
import 'package:oneapp_common/config/tfs_service_config_params.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_network/oneapp_network.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_object_cache/oneapp_object_cache.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/features/core/handle_new_tokens.dart';

// Project imports:
import 'app/oneapp.dart';
import 'provider/oa_apiclients_provider.dart';
import 'provider/oneapp_provider_setup.dart';

final getIt = GetIt.instance;
const bool isProduction = bool.fromEnvironment('dart.vm.product');

void main(List<String> args) async {
  runZonedGuarded<Future<void>>(() async {
    WidgetsFlutterBinding.ensureInitialized();
    // disable debugPrint in prod
    if (isProduction) {
      debugPrint = (String? message, {int? wrapWidth}) => null;
    }

    await Firebase.initializeApp();
    FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);

    final prefs = await SharedPreferences.getInstance();
    final secureStorage = await _getSecureStorage();
    if (secureStorage != null) {
      await CacheEncryption.init(secureStorage);
      await ObjectDatabases.prepareDatabaseDirectories()
          .catchError((e) => logExceptionToFirebase(e));
    }

    final params = GlobalConfigParams.fromJson(json.decode(args.first));
    ApiConfigParams apiConfigParams =
        ApiConfigParams.fromJson(json.decode(args[1]));
    TFSServiceConfigParams tfsServiceConfigParams =
        TFSServiceConfigParams.fromJson(json.decode(args[2]));

    await Global.init(
      prefs: prefs,
      securePreferences: SecurePreferences(prefs, secureStorage),
      configParams: params,
      apiConfigParams: apiConfigParams,
      tfsServiceConfigParams: tfsServiceConfigParams,
    );
    try {
      await HandleTfsTokens.clearTfsTokens();
    } catch (e) {}
    await OneAppNetwork.init();
    APIClientConfig.refresh(withProvider: OneAppProvider());
    await ServiceConfig.initWith(Global.getInstance().apiFlavorString);
    APIClients.init(provider: OAApiClientsProvider());
    await getApplicationDocumentsDirectory();
    getIt.allowReassignment = true;
    setupNavigateService();
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
    runApp(ProviderScope(child: OneApp(key: appGlobalKey)));
  }, (error, stack) => FirebaseCrashlytics.instance.recordError(error, stack));
  _statusBarSetup();
}

Future<FlutterSecureStorage?> _getSecureStorage() async {
  if (Platform.isAndroid) {
    return null;
  }
  try {
    return await FlutterSecureStorage(
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
        synchronizable: false,
      ),
    );
  } catch (e) {
    return null;
  }
}

void _statusBarSetup() {
  if (Platform.isAndroid) {
    SystemUiOverlayStyle style = SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark);
    SystemChrome.setSystemUIOverlayStyle(style);
  } else if (Platform.isIOS) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
  }
}

void setupNavigateService() {
  getIt.registerSingleton(NavigateService());
}
