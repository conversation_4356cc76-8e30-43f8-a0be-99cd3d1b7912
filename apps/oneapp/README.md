# oneapp_module
OneApp Module.

## Getting Started
This module is the entry module for the flutter OneApp. This module is developed based on oneapp-commonui, oneapp-network, oneapp-network-implementation, and oneapp-vehicle modules and these modules are placed under module folder.


**Purpose of this project**
This module covers flutter side OneApp login and vehicle related functionalities. Some parts of the vehicle related functionalities are linked to native OneApp code. This module is loaded from the native OneApp Android and iOS. 

**Prepare tools environment:**
1. Android Studio 4.0.1 and later(with Flutter plugin) or VS code (with Flutter plugin) and xcode 11.7  
2. Flutter stable environment 1.20.4 and later.
3. Install localization plugin: [https://plugins.jetbrains.com/plugin/13666-flutter-intl/](), restart AS, click 'Tools' -> 'Flutter lntl' -> 'Intialize for the Project'.
4. Install plugin to generate dart class from json response: https://plugins.jetbrains.com/plugin/12562-jsontodart-json-to-dart-

**Major branch introduction:**
1. Master: using for release build.
2. Develop: using for merge all other feature branches.

**Project folders introduction:**
1. lib/app: Initialization for the OneApp Module. This includes the setting up the themes, localization and communication channel between native app.
2. lib/provider: API Client setup for various modules. 
3. lib/route: page navigation controller for flutter oneapp module.
4. lib/ui: OneApp module pages and it's blocs. This includes login, loader and bottom bar navigation pages.
5. ios: ios generated project.
6. android: android generated project.

**If you get any compilation error, try the following and compile again**
flutter clean
flutter pub get
flutter pub upgrade

**Create new Package/module**
flutter create --template=package oneapp_nameofthepkg;  

**Generate translation string files**
cd module/oneapp_common
flutter pub global deactivate intl_utils
flutter pub global activate intl_utils 1.9.0
flutter pub global list
flutter pub global run intl_utils:generate

**Current Build Process**
We put this module (oneapp-module) code inside the native OneApp code and build the final pkg. This process includes one-time changes of few native app files. Configuring CI/CD pipeline is in progress.

**We put the oneapp-module code inside the native app and build. Refer the build document from OneApp 2.0 team**
**flutter build aar**
**flutter build ios-framework**


**Formatting the code**
flutter format lib

**Sorting Imports**
flutter pub run import_sorter:main

**Analyse Code**
flutter analyze


