SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
cd $SCRIPT_DIR/..

FLUTTER="flutter"
if which fvm >/dev/null; then
    FLUTTER="fvm flutter"
fi

$FLUTTER pub upgrade
cd ./module/oneapp_common
$FLUTTER pub upgrade
cd ../oneapp_flex
$FLUTTER pub upgrade
cd ../oneapp_fr
$FLUTTER pub upgrade
cd ../oneapp_network
$FLUTTER pub upgrade
cd ../oneapp_vehicle
$FLUTTER pub upgrade
$FLUTTER pub upgrade
$FLUTTER pub run build_runner build --delete-conflicting-outputs
cd ../
