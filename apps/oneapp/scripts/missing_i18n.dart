#!/usr/bin/env dart
// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart';

// ignore: type_annotate_public_apis
main() async {
  final base = Metadata.load();
  base.sortScreens();
  base.bundle.save();
  final en_CA = Bundle.load('./module/oneapp_common/lib/l10n/intl_en_CA.arb')
    ..save();
  final en_PR = Bundle.load('./module/oneapp_common/lib/l10n/intl_en_PR.arb')
    ..save();
  // ignore: unused_local_variable
  final fr = Bundle.load('./module/oneapp_common/lib/l10n/intl_fr.arb')..save();
  final fr_CA = Bundle.load('./module/oneapp_common/lib/l10n/intl_fr_CA.arb')
    ..save();
  final es_PR = Bundle.load('./module/oneapp_common/lib/l10n/intl_es_PR.arb')
    ..save();
  final es_MX = Bundle.load('./module/oneapp_common/lib/l10n/intl_es_MX.arb')
    ..save();

  await en_CA
    ..copyMissingKeys(base.bundle.strings!)
    ..save();
  await en_PR
    ..copyMissingKeys(base.bundle.strings!)
    ..save();

  final needed = NeededTranslations(base.bundle);
  needed.buildTranslationRequestsFor([es_MX, es_PR, fr_CA]);
  needed.printTranslationRequests();

  // base.ensureMetadataOnNewKeys(fr);
  // base.ensureMetadataOnNewKeys(fr_CA);
  // base.ensureMetadataOnNewKeys(es_PR);
  // base.ensureMetadataOnNewKeys(es_MX);
  // await base.saveWithMetadata();
}

class Bundle {
  final OneAppLocale locale;
  final String filePath;
  final Map<String, dynamic>? strings;

  Bundle._({
    required this.filePath,
    required this.strings,
  }) : locale = OneAppLocale.values.firstWhere(
            (l) =>
                // ignore: sdk_version_since
                l.name ==
                filePath
                    .replaceAll('./module/oneapp_common/lib/l10n/intl_', '')
                    .replaceAll('.arb', ''),
            orElse: () => OneAppLocale.unknown);

  factory Bundle.load(String filePath) {
    final text = File(filePath).readAsStringSync();
    final strings = json.decode(text);
    return Bundle._(filePath: filePath, strings: strings);
  }

  Bundle copyAdding(Map<String, dynamic> newStrings) {
    final finalStrings = Map.fromEntries(strings!.entries);
    finalStrings.addAll(newStrings);
    return Bundle._(filePath: filePath, strings: finalStrings);
  }

  Bundle copyMissingKeys(Map<String, dynamic> newStrings) {
    final missingStrings = Map.fromEntries(newStrings.entries);
    missingStrings.removeWhere((key, value) => strings!.containsKey(key));
    return Bundle._(
        filePath: filePath,
        strings: Map.fromEntries(strings!.entries)..addAll(missingStrings));
  }

  Future<void> save() async {
    final sorted =
        Map.fromEntries(strings!.entries.sortedBy((e) => StringKey(e.key)));
    await File(filePath)
        .writeAsString(JsonEncoder.withIndent('  ').convert(sorted) + '\n');
  }
}

class StringKey implements Comparable<StringKey> {
  final String key;
  final int isGlobal;
  final int isMeta;
  final String rawValue;

  StringKey(String value)
      : key = value,
        isGlobal = value.startsWith('@@') ? 1 : 0,
        isMeta = value.startsWith('@') && !value.startsWith('@@') ? 1 : 0,
        rawValue = value.replaceAll('@', '').toLowerCase();

  @override
  int compareTo(StringKey other) {
    int compare = other.isGlobal - isGlobal;
    if (compare != 0) {
      return compare;
    }
    compare = rawValue.compareTo(other.rawValue);
    if (compare != 0) {
      return compare;
    }
    compare = isMeta - other.isMeta;
    if (compare != 0) {
      return compare;
    }
    return key.compareTo(other.key); // same key, different case
  }
}

class Metadata {
  static final _placeholderPattern = RegExp(r'{([^}]*)}');

  final Bundle bundle;

  Metadata._(
    this.bundle,
  );

  factory Metadata.load() {
    return Metadata._(
      Bundle.load('./module/oneapp_common/lib/l10n/intl_en.arb'),
    );
  }

  Map<String, dynamic> _baseMetadata = {};

  void _addKey(String key) {
    String mainKey, propertiesKey;
    if (key.startsWith('@')) {
      mainKey = key.substring(1);
      propertiesKey = key;
    } else {
      mainKey = key;
      propertiesKey = '@$key';
    }
    if (bundle.strings!.containsKey(propertiesKey)) {
      return;
    }

    Map<String, dynamic> props = {};
    props['description'] = null;
    props['screen'] = null;

    final value = bundle.strings![mainKey];
    final matches = _placeholderPattern.allMatches(value);
    Map<String?, dynamic> placeholders = {};
    for (final m in matches) {
      placeholders[m[1]] = {
        'type': '??',
        'example': '<need example>',
      };
    }
    if (placeholders.isNotEmpty) {
      props['placeholders'] = placeholders;
    }
    _baseMetadata[propertiesKey] = props;
  }

  void ensureMetadataOnNewKeys(Bundle other) {
    final newKeys = bundle.strings!.keysMissingIn(other.strings!);
    newKeys.forEach((key) => _addKey(key));
  }

  void sortScreens() {
    final screens = bundle.strings!['@~screens'] as Map<String, dynamic>?;
    if (screens != null) {
      final sortedScreens = Map.fromEntries(
          screens.entries.sorted((a, b) => a.key.compareTo(b.key)));
      bundle.strings!['@~screens'] = sortedScreens;
    }
  }

  Future<void> saveWithMetadata() async {
    final merged = bundle.copyAdding(_baseMetadata);
    merged.save();
  }
}

class NeededTranslations {
  final Bundle base;
  final Map<String, TranslationRequest> translationRequests = {};
  final Map<String, List<TranslationRequest>> groupedRequests = {};

  NeededTranslations(this.base);

  void buildTranslationRequestsFor(List<Bundle> targets) {
    Set<String> keysToTranslate = {};
    for (final other in targets) {
      final targetKeys = base.strings!.keysMissingIn(other.strings!)
        ..removeWhere((k) => k.startsWith('@'))
        ..removeWhere((k) => base.strings!['@$k'] == null);
      keysToTranslate.addAll(targetKeys);
    }
    for (final other in targets) {
      for (final key in keysToTranslate) {
        final existing = json.encode(other.strings![key] as String?);
        TranslationRequest? request = null;
        if (translationRequests.containsKey(key)) {
          request = translationRequests[key];
        } else {
          final meta = base.strings!['@$key'] as Map<String, dynamic>?;
          if (meta != null) {
            final value = json.encode(base.strings![key] as String?);
            final screen = meta['screen']?.toString() ?? ' ';
            RegExp pattern = RegExp(r'^(\w+)');
            // ignore: sdk_version_since
            RegExpMatch? match = pattern.firstMatch(screen);
            String? screenName = match?.group(1);
            if (screenName == null || screenName == 'https') {
              pattern = RegExp(r'^([^ ]*)');
              match = pattern.firstMatch(screen);
              screenName = match?.group(1);
            }
            request = TranslationRequest(
              key: key,
              screenName: screenName!,
              value: value.substring(1, value.length - 1),
              meta: meta,
            );
            translationRequests[key] = request;
          }
        }
        if (existing == "null") {
          request?.translate(other.locale);
        } else {
          request?.existing(
              other.locale, existing.substring(1, existing.length - 1));
          // print(
          //     'Existing translation: $key -- $existing\n${request.translations[other.locale]}');
        }
      }
    }
    groupedRequests.addAll(
      translationRequests.values.groupListsBy((r) => r.screenName),
    );
  }

  void printTranslationRequests() {
    final screens = groupedRequests.keys.toList();
    for (final screenName in screens) {
      final screenDetails =
          base.strings!['@~screens'][screenName] as Map<String, dynamic>?;
      print('\n\n\n');
      if (screenDetails != null) {
        print('## Screen: ${screenDetails['name']}');
        print('Screen design: [(Figma)](${screenDetails['screen']})');
        print('\n${screenDetails['description']}');
      } else {
        print('## Unnamed Screen');
        print('${screenName.isNotEmpty ? screenName : 'No link available'}');
      }
      print('\n\n');

      final List<TranslationRequest> requests =
          groupedRequests[screenName]!.sortedBy((r) => r.sortKey);
      for (final request in requests) {
        print('|ID|**${request.key}**|');
        print('|--|${'-' * (request.key.length + 4)}|');
        print('|English|`${request.value}`|');
        final description = request.meta['description'] ?? ' ';
        print('|Description|$description|');
        final screen = request.meta['screen']?.toString() ?? ' ';
        final pattern = RegExp('^$screenName(.*)');
        final screenCaveats = pattern.firstMatch(screen)?.group(1) ?? '';
        if (screenCaveats.isNotEmpty) {
          print('|Screen Notes|$screenCaveats|');
        }
        final List<OneAppLocale> sortedKeys = request.translations.keys
            .sorted((a, b) => a.lang.compareTo(b.lang));
        for (final target in sortedKeys) {
          final value = request.translations[target];
          final key =
              value == null ? target.lang : '**Existing** ${target.lang}';
          print('|$key|${value != null ? '`$value`' : ' '}|');
        }
        print('');
      }
    }
  }
}

class TranslationRequest {
  final String key;
  final String screenName;
  final String prop;
  final String value;
  final Map<String, dynamic> meta;
  final Map<OneAppLocale, String?> translations = {};

  String get sortKey => key.toLowerCase();

  TranslationRequest({
    required this.key,
    required this.screenName,
    required this.value,
    required this.meta,
  }) : prop = '@$key';

  void translate(OneAppLocale locale) {
    translations[locale] = null;
  }

  void existing(OneAppLocale locale, String value) {
    translations[locale] = value;
  }
}

enum OneAppLocale {
  es_MX,
  es_PR,
  fr_CA,
  fr,
  unknown,
}

extension LanguageData on OneAppLocale {
  String get lang {
    switch (this) {
      case OneAppLocale.es_MX:
        return 'Spanish (Mexico)';
      case OneAppLocale.es_PR:
        return 'Spanish (Puerto Rico)';
      case OneAppLocale.fr_CA:
        return 'French (Canada)';
      case OneAppLocale.fr:
        return 'French (Generic)';
      default:
        return 'Unknown';
    }
  }
}

extension MapExtensions on Map<String, dynamic> {
  List<String> keysMissingIn(Map<String, dynamic> other) =>
      keys.toSet().difference(other.keys.toSet()).toList();
}
