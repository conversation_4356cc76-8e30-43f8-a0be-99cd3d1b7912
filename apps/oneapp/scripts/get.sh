SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
cd $SCRIPT_DIR/..

FLUTTER="flutter"
DART="dart"
if which fvm >/dev/null; then
    FLUTTER="fvm flutter"
    DART="fvm dart"
fi

$DART pub global activate arb_utils >/dev/null 2>&1
for file in $SCRIPT_DIR/../module/oneapp_common/lib/l10n/*.arb
do
  if [[ ! -f "$file" ]]
  then
      continue
  fi
  $DART pub global run arb_utils:sort -i $file       
done

$FLUTTER pub get
cd ./module/oneapp_common
$FLUTTER pub get
$FLUTTER pub global run intl_utils:generate
cd ../oneapp_flex
$FLUTTER pub get
cd ../oneapp_network
$FLUTTER pub get
cd ../oneapp_vehicle
$FLUTTER pub get
cd ../oneapp_network_implementation
$FLUTTER pub get
$FLUTTER pub run build_runner build --delete-conflicting-outputs
cd ../

echo ""
echo "Finished at $(date)"
echo ""
