SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
cd $SCRIPT_DIR/..

FLUTTER="flutter"
if which fvm >/dev/null; then
    FLUTTER="fvm flutter"
fi

$FLUTTER clean
$FLUTTER pub get
cd ./module/oneapp_common
$FLUTTER format lib
$FLUTTER pub run import_sorter:main
$FLUTTER clean
$FLUTTER pub get
$FLUTTER pub global run intl_utils:generate
$FLUTTER pub run import_sorter:main
$FLUTTER format lib
cd ../oneapp_flex
$FLUTTER clean
$FLUTTER pub get
$FLUTTER pub run import_sorter:main
$FLUTTER format lib
cd ../oneapp_fr
$FLUTTER clean
$FLUTTER pub get
$FLUTTER pub run import_sorter:main
$FLUTTER format lib
cd ../oneapp_network
$FLUTTER clean
$FLUTTER pub get
$FLUTTER pub run import_sorter:main
$FLUTTER format lib
cd ../oneapp_vehicle
$FLUTTER clean
$FLUTTER pub get
$FLUTTER pub run import_sorter:main
$FLUTTER format lib
cd ../oneapp_network_implementation
$FLUTTER clean
$FLUTTER pub get
$FLUTTER pub run build_runner build --delete-conflicting-outputs
$FLUTTER pub run import_sorter:main
$FLUTTER format lib
cd ../
