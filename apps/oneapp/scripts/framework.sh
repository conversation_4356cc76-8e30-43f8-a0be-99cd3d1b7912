SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
cd $SCRIPT_DIR/..

FLUTTER="flutter"
if which fvm >/dev/null; then
    FLUTTER="fvm flutter"
fi

if [ -d .ios ]; then
  cd .ios
  rm Podfile*
  pod init
  pod install
  cd ..
fi
echo "Building iOS Framework"
if [ $1 = "simple" ]; then
  $FLUTTER build ios-framework --no-release --no-profile
else
  $FLUTTER build ios-framework
fi

echo ""
echo "Finished at $(date)"
echo ""
