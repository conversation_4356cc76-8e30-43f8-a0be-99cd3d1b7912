name: oneapp_module
description:
  OneApp Flutter Entry Module. This module is loaded from Native Apps.
  Also, this module loads common, network, network implementation, vehicle and frlogin modules.
version: 0.0.1
homepage: main.dart
publish_to: none

environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: "3.19.1"

dependencies:
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.3.7
  oneapp_common:
    path: ../../packages/oneapp_common
  oneapp_network:
    path: ../../packages/oneapp_network
  oneapp_network_implementation:
    path: ../../packages/oneapp_network_implementation
  oa_network:
    path: ../../packages/oa_network
  oa_network_impl:
    path: ../../packages/oa_network_impl
  oneapp_object_cache:
    path: ../../packages/oneapp_object_cache
  vehicle_module:
    path: ../../features/oneapp_vehicle
  flex_module:
    path: ../../features/oneapp_flex
  collection: ^1.17.0
  flutter_secure_storage: ^8.1.0

dev_dependencies:
  analyzer: ^5.13.0
  arb_utils: ^0.1.0+1
  flutter_test:
    sdk: flutter
  import_sorter: ^4.2.2
  import_path_converter: ^1.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  fonts:
    - family: ToyotaFont
      fonts:
        - asset: packages/oneapp_common/font/toyotatype_regular.otf
        - asset: packages/oneapp_common/font/toyotatype_semibold.otf
          weight: 600
        - asset: packages/oneapp_common/font/toyotatype_bold.otf
          weight: 700

    - family: LexusFont
      fonts:
        - asset: packages/oneapp_common/font/nobel_regular.otf
        - asset: packages/oneapp_common/font/nobel_book.otf
          weight: 500
          #- asset: packages/oneapp_common/font/nobel_semibold.otf
          #weight: 600
        - asset: packages/oneapp_common/font/nobel_bold.otf
          weight: 700
    - family: SFFont
      fonts:
        - asset: packages/oneapp_common/font/sf_pro_text_regular.otf
        - asset: packages/oneapp_common/font/sf_pro_display_regular.otf
          weight: 500
        - asset: packages/oneapp_common/font/sf_pro_text_semibold.otf
          weight: 600
        - asset: packages/oneapp_common/font/sf_pro_text_bold.otf
          weight: 700

    - family: AndroidLexusFont
      fonts:
        - asset: packages/oneapp_common/font/roboto_regular.ttf
        - asset: packages/oneapp_common/font/roboto_medium.ttf
          weight: 500
        - asset: packages/oneapp_common/font/roboto_bold.ttf
          weight: 700

  # This section identifies your Flutter project as a module meant for
  # embedding in a native host app.  These identifiers should _not_ ordinarily
  # be changed after generation - they are used to ensure that the tooling can
  # maintain consistency when adding or modifying assets and plugins.
  # They also do not have any bearing on your native host application's
  # identifiers, which may be completely independent or the same as these.
  module:
    androidX: true
    androidPackage: com.toyota.oneapp_module
    iosBundleIdentifier: com.toyota.oneappModule
