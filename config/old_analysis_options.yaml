# Analyzer settings used previously. Migrate away from this soon.
analyzer:
  exclude:
    - "**.freezed.dart"
    - "**.g.dart"
    - "**.gr.dart"
    - "**.mocks.dart"
    - "**/*.freezed.dart"
    - "**/*.g.dart"
    - "bin/cache/**"
    - "lib/generated/**"
    - "lib/generated_plugin_registrant.dart"
    - "test/.test_coverage.dart"

  errors:
    missing_return: error
    avoid_empty_else: error
    invalid_assignment: warning
    dead_code: info

linter:
  rules:
    avoid_print: true
    unnecessary_new: true
    unnecessary_brace_in_string_interps: true
    avoid_type_to_string: true
    unawaited_futures: false # try to make this true but deprecated the pedantic
    type_annotate_public_apis: true
    prefer_is_not_empty: true
    prefer_is_empty: true
    prefer_null_aware_operators: false # Find a workaround to enable this
    prefer_initializing_formals: true
    tighten_type_of_initializing_formals: true
    avoid_types_as_parameter_names: true
    close_sinks: true
    control_flow_in_finally: true
    empty_statements: true
    no_duplicate_case_values: true
    no_logic_in_create_state: true
    prefer_void_to_null: true
    unnecessary_statements: true
    unrelated_type_equality_checks: true
    prefer_final_in_for_each: true
    curly_braces_in_flow_control_structures: true
