.generated/
*.freezed.dart
*.freezed.dart
*.g.dart
*.gr.dart
*.mocks.dart
bin/cache/*
generated/
lib/generated/intl
test/.test_coverage.dart

# Miscellaneous
*.class
*.log
*.pyc
*.swp
*.swp
.atom
.buildlog
.DS_Store
.env
.history
.sconsign.dblite
.svn
.vagrant
build
DerivedData


# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# Flutter/Dart/Pub related
**/doc/api/
.android
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.ios
.packages
.pub-cache/
.pub/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android related
**/android/**/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java

# iOS/XCode related
xcuserdata
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# Exceptions to above rules.
!**/ios/**/default.mode1v3
!**/ios/**/default.mode2v3
!**/ios/**/default.pbxuser
!**/ios/**/default.perspectivev3

# The .vscode folder contains launch configuration and tasks so we
# keep it.
!.vscode/
