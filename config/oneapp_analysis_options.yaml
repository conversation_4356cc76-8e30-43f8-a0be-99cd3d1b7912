# The `lint` package is very strict, and helps write excellent Dart
# code. It is a stronger starting point than either `lints` or
# `flutter_lints`. See <PERSON>'s in-depth linting guide here:
#
# https://rydmike.com/blog_flutter_linting.html#what-are-the-rule-differences-between-all-these-packages
include: package:lint/analysis_options.yaml

analyzer:
  exclude:
    - "**.freezed.dart"
    - "**.g.dart"
    - "**.gr.dart"
    - "**.mocks.dart"
    - "**/*.freezed.dart"
    - "**/*.g.dart"
    - "bin/cache/**"
    - "lib/generated/**"
    - "lib/generated_plugin_registrant.dart"
    - "test/.test_coverage.dart"

  # For more information see:
  # https://dart.dev/guides/language/analysis-options#enabling-additional-type-checks
  language:
    # Uncomment when we can upgrade Flutter version
    # strict-casts: true
    strict-inference: true
    strict-raw-types: true

  errors:
    # ALWAYS use relative imports for files in lib/.
    #
    # There are two rules which are the opposite of each other. We
    # follow the Effective Dart style of using relative paths for
    # items within the lib/ directory. This makes it much easier to
    # see external dependencies used in a given file, and control them
    # better.
    #
    # We enforce this rule in `melos format` which uses
    # `import_path_converter`.
    #
    # https://dart-lang.github.io/linter/lints/prefer_relative_imports.html
    # https://dart-lang.github.io/linter/lints/always_use_package_imports.html
    #
    # Set relative imports as a warning (don't always impede dev),
    # and turn OFF the package-style imports.
    prefer_relative_imports: warning
    always_use_package_imports: ignore

    # DON'T use @protected members outside of subclasses. Treat as error (instead of hint)
    invalid_use_of_protected_member: error

    # Treat missing returns as an error, not as a hint or a warning.
    missing_return: error

    # If it inherits from an @immutable class, it must be @immutable.
    must_be_immutable: error

    # Make it an error to forget calling super when required.
    must_call_super: error

    # DON'T assign new values to parameters of methods or functions.
    #
    # https://dart-lang.github.io/linter/lints/parameter_assignments.html
    #
    # We desire to keep this at `error`, but if a large amount of code
    # is found inviolation, we reduce it to `warning` until the code
    # can be cleaned.
    parameter_assignments: error

    # We approach Dart file formatting with another tool, so we turn this OFF.
    sort_unnamed_constructors_first: ignore
