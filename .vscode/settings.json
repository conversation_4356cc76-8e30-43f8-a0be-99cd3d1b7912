{"editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": "active", "explorerExclude.backup": {}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/*.freezed.dart": true, "**/*.g.dart": true, "**/.gitkeep": true, "**/*.gr.dart": true, "**/*.mocks.dart": true}, "dart.flutterSdkPath": ".fvm/versions/3.19.1", "search.exclude": {"**/.fvm": true}, "files.watcherExclude": {"**/.fvm": true}, "conventionalCommits.emojiFormat": "emoji", "conventionalCommits.scopes": ["accounts", "api", "dealer", "designsys", "electric", "finance", "global", "intl", "platform", "remote", "rentals", "subscriptions", "util", "vehicle"], "dart.runPubGetOnPubspecChanges": true}