// Dart imports:
import 'dart:convert';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/notification_helper.dart';
import 'package:webview_flutter/webview_flutter.dart';

// Project imports:
import 'flex_global.dart';
import 'route/router.dart';

class FlexPushActions {
  static void pushHandler(BuildContext ctx, Object result) {
    final map = json.decode(result.toString());
    debugPrint("-----FlexPushActions----$map");
    if (map != null) {
      final payload = map['oneapp_payload'];
      if (map['category'] == FLEX_PUSH_RESERVATION ||
          map['oneapp_category'] == FLEX_PUSH_RESERVATION) {
        if (payload != null &&
            (payload['subcategory'] == 'RESERVATION_CANCELED' ||
                payload['subcategory'] == 'VEHICLE_ASSIGNED' ||
                payload['subcategory'] == 'RESERVATION_STARTING_SOON' ||
                payload['subcategory'] == 'RESERVATION_ENDING_SOON' ||
                payload['subcategory'] == 'RENTAL_STARTED' ||
                payload['subcategory'] == 'RENTAL_ENDED' ||
                payload['subcategory'] == 'PAYMENT_RECEIPT' ||
                payload['subcategory'] == 'RENTAL_EXTENDED')) {
          loadNotificationNativePage(); // Inbox page
        } else if (map['subcategory'] == 'RESERVATION_CANCELED' ||
            map['subcategory'] == 'VEHICLE_ASSIGNED' ||
            map['subcategory'] == 'RESERVATION_STARTING_SOON' ||
            map['subcategory'] == 'RESERVATION_ENDING_SOON' ||
            map['subcategory'] == 'RENTAL_STARTED' ||
            map['subcategory'] == 'RENTAL_ENDED' ||
            map['subcategory'] == 'PAYMENT_RECEIPT' ||
            map['subcategory'] == 'RENTAL_EXTENDED') {
          loadNotificationNativePage();
        } else {
          FlexRouter.pushName(RoutePath.FX_DETAIL_LANDING);
        }
      }
    }
  }

  static void contractPushAction(BuildContext ctx, {String? url}) {
    debugPrint("-----contractPushAction----$url");
    if (url != null) {
      WebViewController()..loadRequest(Uri.parse(url));
    } else {
      FlexRouter.pushName(
        RoutePath.FX_DETAIL_LANDING,
        arguments: {'allowCache': false},
      );
    }
  }

  static void customerPushAction(BuildContext ctx,
      {String? title, String? message, required String occurrenceDateUTC}) {
    DateTime dateTime = occurrenceDateUTC.parseYYYYMMDDTHHmmssZ()!;
    FlexRouter.pushName(RoutePath.FX_NOTIFICATION_VIEW,
        arguments: NotificationUI(
          title: OneAppString.of().applicationStatus,
          contentTitle: title ?? "",
          description: message ?? "",
          buttonStr: OneAppString.of().createReservation,
          time: dateTime.formatEEEEMMMMddyyyy(),
        ));
  }
}
