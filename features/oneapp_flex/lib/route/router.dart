// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/notification_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';

// Project imports:
import '../flex_global.dart';
import '../ui/driver_info/fx_driver_info_page.dart';
import '../ui/landing/fx_reservation_landing_page.dart';
import '../ui/notification/fx_show_notification.dart';
import '../ui/search/fx_flex_search_map.dart';
import '../ui/selectdates/fx_date_time_view.dart';
import '../ui/selectdates/fx_same_day_alert_page.dart';
import '../ui/selectdates/fx_select_date_time_page.dart';
import '../ui/selectvechicle/fx_select_vehicle_page.dart';
import '../ui/summary/fx_summary_detail_page.dart';

class FlexRouter {
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case RoutePath.FX_DETAIL_LANDING:
        final data = settings.arguments;
        bool allowCache = true;
        ToastMsgData? toastMsgData;
        CommonResponse? commonResponse;
        if (data != null && data is Map) {
          allowCache = data['allowCache'] ?? true;
          toastMsgData = data['result'];
          commonResponse = data['commonResponse'];
        }
        return MaterialPageRoute(
          settings: data == null
              ? RouteSettings(name: settings.name, arguments: {})
              : settings,
          builder: (_) => FxReservationLandingPage(
            allowCache: allowCache,
            toastMsgData: toastMsgData,
            commonResponse: commonResponse,
          ),
        );
      case RoutePath.FX_SUMMARY_DETAIL:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => FxSummaryDetailPage(intentData: settings.arguments),
        );
      case RoutePath.FX_SELECT_VEHICLE:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => FxSelectVehiclePage(
              intentData: settings.arguments as IntentToSelectVehicle?),
        );
      case RoutePath.FX_SEARCH_DEALER:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) =>
              FxSearchMapLayer(intent: settings.arguments as IntentToSearch?),
        );
      case RoutePath.FX_SELECT_DATES:
        return MaterialWithModalsPageRoute(
          settings: settings,
          builder: (BuildContext context) => FxSelectDateTimePage(
              intentData: settings.arguments as IntentToSelectDate?),
        );
      case RoutePath.FX_NOTIFICATION_VIEW:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => FxNotificationView(
            notificationUI: settings.arguments as NotificationUI?,
          ),
        );

      case RoutePath.FX_CALENDAR_VIEW:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            final IntentToSelectDate? data =
                settings.arguments as IntentToSelectDate?;
            return FxDateTimeView(
              data: data,
            );
          },
        );

      case RoutePath.FX_SAME_DAY_ALERT_PAGE:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => FxSameDayAlertPage(
            phoneNumber: settings.arguments as String?,
          ),
        );

      case RoutePath.FX_DRIVER_INFO:
        final Map? map = settings.arguments as Map<dynamic, dynamic>?;
        Widget page;
        if (map != null) {
          page = FxDriverInfoPage(
            customerDetailResult: map['CustomerEntity'],
            isBookForMyself: map['BookingForMyself'] ?? true,
            profileUI: map['ProfileUI'],
          );
        } else {
          page = FxDriverInfoPage();
        }
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => page,
        );

      default:
        return null;
    }
  }

  static final getIt = GetIt.instance;

  static Future pushName(String routeName, {Object? arguments}) async {
    FxAccountGlobal.getInstance().routeData = null;
    return NavigateService.pushNamedRoute(routeName, arguments: arguments);
  }

  static bool canRoutePop() {
    return NavigateService.canPopRoute();
  }

  static Future popRoute<T>({T? result}) {
    FxAccountGlobal.getInstance().routeData = null;
    return NavigateService.maybePopRoute(result: result);
  }

  static Future pushRouteUtil<T>(String targetRouteName, {T? result}) {
    FxAccountGlobal.getInstance().routeData = null;
    return NavigateService.pushNamedAndRemoveUtilRoute(targetRouteName,
        result: result);
  }

  static Future popAndPush<T>(String routeName, {Object? arguments}) {
    FxAccountGlobal.getInstance().routeData = null;
    return NavigateService.navigator!
        .popAndPushNamed(routeName, arguments: arguments);
  }

  static void popRouteUtil<T>(String? targetRouteName, {T? result}) {
    FxAccountGlobal.getInstance().routeData = null;
    NavigateService.popUtilRoute((Route<dynamic> route) {
      debugPrint("route -> ${route.settings.name}");
      bool resultData = !route.willHandlePopInternally &&
          route is ModalRoute &&
          route.settings.name == targetRouteName;
      if (result != null) {
        FxAccountGlobal.getInstance().routeData = result;
      }
      return resultData;
    });
  }
}
