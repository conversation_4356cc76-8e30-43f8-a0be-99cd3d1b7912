// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';

class FlexSlideTitle extends StatefulWidget {
  final String? title;

  final VoidCallback? onBackPressed;

  final Widget? menuItem;

  final double? paddingTop;

  final double? paddingBottom;

  const FlexSlideTitle({
    Key? key,
    this.title,
    this.onBackPressed,
    this.menuItem,
    this.paddingTop,
    this.paddingBottom,
  }) : super(key: key);

  @override
  _FlexSlideTitleState createState() => _FlexSlideTitleState();
}

class _FlexSlideTitleState extends State<FlexSlideTitle> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: widget.paddingTop ?? 16.h,
        ),
        _slideLine(),
        SizedBox(
          height: 9.h,
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            children: [
              Expanded(
                child: _backActionItem(),
              ),
              _titleItem(),
              Expanded(
                child: _menuActionItem(),
              ),
            ],
          ),
        ),
        SizedBox(
          height: widget.paddingBottom ?? 23.h,
        ),
      ],
    );
  }

  Widget _backActionItem() {
    return Align(
      alignment: Alignment.centerLeft,
      child: AnimatedOpacity(
        opacity: widget.onBackPressed != null ? 1.0 : 0,
        duration: Duration.zero,
        child: IconButton(
          // padding: EdgeInsets.only(left: 16),
          padding: EdgeInsets.zero,
          alignment: Alignment.centerLeft,
          icon: SizedBox(
            width: 48.w,
            height: 48.w,
            child: CommonCircleIconImage(
              arrowLeftIcon,
              iconWidth: 24.w,
              iconHeight: 24.h,
              iconTintColor: ThemeConfig.current().colorUtil.button02a,
              circleBackgroundColor: ThemeConfig.current().colorUtil.button02d,
            ),
          ),
          color: Colors.black,
          onPressed: widget.onBackPressed,
        ),
      ),
    );
  }

  Widget _menuActionItem() {
    return Align(
      alignment: Alignment.centerRight,
      child: widget.menuItem,
    );
  }

  Widget _titleItem() {
    return Align(
      alignment: Alignment.center,
      child: Text(
        widget.title ?? '',
        style: ThemeConfig.current().textStyleUtil.subHeadline3,
      ),
    );
  }

  Widget _slideLine() {
    return Container(
      height: 4.h,
      width: 28.w,
      decoration: BoxDecoration(
        color: ThemeConfig.current().colorUtil.outline01,
        borderRadius: BorderRadius.circular(100),
      ),
    );
  }
}
