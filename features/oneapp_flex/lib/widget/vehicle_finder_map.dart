// Dart imports:
import 'dart:async';
import 'dart:io';

// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/map_util.dart';

class VehicleFinderMap extends StatefulWidget {
  final Position? currentPosition;

  final Position? vehiclePosition;

  const VehicleFinderMap({Key? key, this.currentPosition, this.vehiclePosition})
      : super(key: key);

  @override
  VehicleFinderMapState createState() => VehicleFinderMapState();
}

class VehicleFinderMapState extends State<VehicleFinderMap> {
  MapboxMap? _controller;
  Position? _vehiclePosition;
  PointAnnotationManager? _pointAnnotationManager;

  Future<void> addImageFromAsset(
    String name,
    String assetName, {
    required int width,
    required int height,
  }) async {
    final ByteData bytes = await rootBundle.load(assetName);
    final Uint8List list = bytes.buffer.asUint8List();
    return _controller?.style.addStyleImage(
      name,
      1,
      MbxImage(data: list, width: width, height: height),
      false,
      [],
      [],
      null,
    );
  }

  @override
  void didUpdateWidget(VehicleFinderMap oldWidget) {
    if (_vehiclePosition != widget.vehiclePosition) {
      _vehiclePosition = widget.vehiclePosition;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = isDarkTheme();
    Future(() {
      _removeAll();
      _addAllPIN();
    });
    return MapWidget(
      styleUri: isDarkMode == true ? MapboxStyles.DARK : MapboxStyles.LIGHT,
      androidHostingMode: AndroidPlatformViewHostingMode.TLHC_HC,
      onMapCreated: _onMapCreated,
      onStyleLoadedListener: _onStyleLoaded,
      cameraOptions: CameraOptions(
        center: Point(
            coordinates:
                widget.currentPosition ?? Position(-73.989551, 40.745323)),
        zoom: 2,
      ),
      gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>[
        Factory<OneSequenceGestureRecognizer>(
          () => EagerGestureRecognizer(),
        ),
      ].toSet(),
    );
  }

  Future<void> _onMapCreated(MapboxMap mapboxMapController) async {
    _controller = mapboxMapController;
    _pointAnnotationManager =
        await mapboxMapController.annotations.createPointAnnotationManager();
    await mapboxMapController.scaleBar
        .updateSettings(ScaleBarSettings(enabled: false));
    _removeAll();
    _addAllPIN();
  }

  void _onStyleLoaded(StyleLoadedEventData _) {
    addImageFromAsset(
      "mapCarLocationPin",
      getIconSize(),
      width: 156,
      height: 156,
    );
    addImageFromAsset(
      "currentLocationPin",
      currentLocationImage,
      width: 63,
      height: 74,
    );
  }

  Future<void> _addAllPIN() async {
    if (_controller == null) return;
    final List<PointAnnotationOptions> symbolOptionsList = [];

    try {
      if (widget.vehiclePosition != null) {
        symbolOptionsList.add(PointAnnotationOptions(
          iconSize: 0.85.w,
          geometry: Point(coordinates: _vehiclePosition!),
          iconImage: mapCarLocationPin,
        ));
      }
      if (widget.currentPosition != null) {
        symbolOptionsList.add(PointAnnotationOptions(
          iconSize: 0.85.w,
          geometry: Point(coordinates: widget.currentPosition!),
          iconImage: currentLocationCircleImage,
        ));
      }
      _pointAnnotationManager = addSymbolsToMapboxController(
          _pointAnnotationManager!, symbolOptionsList);

      Future.delayed(const Duration(milliseconds: 100), () {
        // TODO final latLngBounds = latLngBoundsBuilder.build();
        if (_controller != null) {
          if (_vehiclePosition != null) {
            _controller!.flyTo(
              CameraOptions(
                center: Point(
                  coordinates: _vehiclePosition!,
                ),
                zoom: 13,
              ),
              null,
            );
          } else if (widget.currentPosition != null) {
            _controller!.setCamera(
              CameraOptions(
                center: Point(
                  coordinates: widget.currentPosition!,
                ),
                zoom: 13,
              ),
            );
          }
        }
      });
    } catch (e) {}
  }

  void _removeAll() {
    if (_pointAnnotationManager == null) return;
    removeSymbolsFromMapboxController(_pointAnnotationManager!);
  }

  String getIconSize() {
    if (Platform.isAndroid) {
      double mq = MediaQuery.of(context).devicePixelRatio;
      if (mq > 1.5 && mq < 2.5) {
        return navigatorWithBackgroundImage2x;
      } else if (mq >= 2.5) {
        return navigatorWithBackgroundImage;
      }
    }
    // this is for iOS
    return navigatorWithBackgroundImage;
  }
}
