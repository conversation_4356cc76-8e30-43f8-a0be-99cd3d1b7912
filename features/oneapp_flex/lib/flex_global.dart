// Dart imports:
import 'dart:convert';

// Flutter imports:
import 'package:flutter/foundation.dart';

// Package imports:
import 'package:oneapp_common/global.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_customer_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_reserve_request_entity.dart';
import 'package:shared_preferences/shared_preferences.dart';

const String ORG_ID = "ec8c9ba3-8d61-46cb-a153-a1056b8e71c8";
const String CAR_SHARE_PROGRAM_ID = "CAR_SHARE";
const String RENT_PROGRAM_ID = "CAR_RENTAL";

const String CUSTOMER_STATUS_PENDING = "Pending";
const String CUSTOMER_STATUS_ACTIVE = "Active";
const String CUSTOMER_STATUS_REJECTED = "Rejected";
const String CUSTOMER_STATUS_DEACTIVATED = "Deactivated";
const String CUSTOMER_STATUS_ARCHIVED = "Archived";

const String FLEX_PUSH_WALLET = "Wallet";
const String FLEX_PUSH_BILLING = "cfai-billing";
const String FLEX_PUSH_CONTRACTS = "cfai-contracts";
const String FLEX_PUSH_CUSTOMER = "cfai-customer";
const String FLEX_PUSH_RESERVATION = "cfai-reservation";

const String FLEX_RESERVATION_BALANCE = "reservation";
const String FLEX_UPCOMING_BALANCE = "upcoming";
const String FLEX_ADDCREDIT_BALANCE = "addcredit";
const String FLEX_COMPLETED_BALANCE = "completed";
const String FLEX_CORRECTION_DELETECREDIT_BALANCE = "correction-deletecredit";
const String FLEX_CORRECTION_ADDCREDIT_BALANCE = "correction-addcredit";
const String FLEX_RESERVE_DAY_BALANCE = "Reserve Day";
const String FLEX_RESERVATION_UPCOMING_BALANCE = "Reservation (Upcoming)";
const String FLEX_GOODWILL_CREDITS_BALANCE = "Goodwill Credits";
const String FLEX_EXPIRED_BALANCE = "Expired";
const String FLEX_EXPIRED_SOON_BALANCE = "Expiring Soon";
const String FLEX_EXPIRATION_DATE_BALANCE = "Expiration Date";
const String FLEX_TRANSACTIONS_BALANCE = "Transactions";
const String FLEX_RENTALS_EV_SWAP = "EV_SWAP";
const String FLEX_RENTALS_RENTAL = "RENTAL";

class FxAccountGlobal {
  License? _license;

  Insurance? _insurance;

  String? _email, _phone;

  CustomerDetailResult? _customersEntity;

  Customer? requestCustomer;

  SharedPreferences? _prefs;

  bool? _accountBadgeStatus;

  int? _evReserveBalance;

  dynamic routeData;

  static FxAccountGlobal? _instance;

  factory FxAccountGlobal.getInstance() => _getInstance();

  static _getInstance() {
    if (_instance == null) {
      debugPrint("=_= _instance == null");

      _instance = FxAccountGlobal._internal();
    }
    debugPrint("=_= after if _getInstance");
    return _instance;
  }

  FxAccountGlobal._internal() {
    debugPrint("=_= FxAccountGlobal._internal()");
    init();
  }

  Future<void> init() async {
    if (_prefs == null) {
      debugPrint("=_= init_prefs == null");
      _prefs = await SharedPreferences.getInstance();
      debugPrint("=_= await SharedPreferences.getInstance()");
    }
    debugPrint("=_= final init()");
  }

  String? get _guid {
    return Global.getInstance().guid;
  }

  Insurance? get insurance {
    if (_insurance == null) {
      final string = _prefs?.getString("ProfileInsurance: $_guid");
      if (string != null && json.decode(string) != null) {
        _insurance = Insurance.fromJson(json.decode(string));
      }
    }
    return _insurance;
  }

  set insurance(Insurance? value) {
    _insurance = value;
    _prefs?.setString("ProfileInsurance: $_guid", json.encode(value));
  }

  License? get license {
    if (_license == null) {
      final string = _prefs?.getString("ProfileLicense: $_guid");
      if (string != null && json.decode(string) != null) {
        _license = License.fromJson(json.decode(string));
      }
    }
    return _license;
  }

  set license(License? value) {
    _license = value;
    _prefs?.setString("ProfileLicense: $_guid", json.encode(value));
  }

  CustomerDetailResult? get customersEntity {
    if (_customersEntity == null) {
      final string = _prefs?.getString("CustomersEntity: $_guid");
      if (string != null) {
        try {
          _customersEntity = CustomerDetailResult.fromJson(json.decode(string));
        } catch (_) {}
      }
    }
    return _customersEntity;
  }

  set customersEntity(CustomerDetailResult? value) {
    _customersEntity = value;
    _prefs?.setString("CustomersEntity: $_guid", json.encode(value));
  }

  int? get evReserveBalance {
    _evReserveBalance = _prefs?.getInt('ReserveBalance') ?? 0;
    return _evReserveBalance;
  }

  set evReserveBalance(int? evReserveBalance) {
    _evReserveBalance = evReserveBalance;
    _prefs?.setInt("ReserveBalance", evReserveBalance ?? 0);
  }

  String? get email {
    if (_email == null) {
      _email = _prefs?.getString("Email: $_guid");
    }
    return _email;
  }

  set email(String? value) {
    _email = value;
    if (_email != null) {
      _prefs?.setString("Email: $_guid", _email!);
    } else {
      _prefs?.remove("Email: $_guid");
    }
  }

  String? get phone {
    if (_phone == null) {
      _phone = _prefs?.getString("Phone: $_guid");
    }
    return _phone;
  }

  set phone(String? value) {
    _phone = value;
    if (_phone != null) {
      _prefs?.setString("Phone: $_guid", _phone!);
    } else {
      _prefs?.remove("Phone: $_guid");
    }
  }

  bool get accountBadgeStatus {
    if (_accountBadgeStatus == null) {
      _accountBadgeStatus = _prefs?.getBool("accountBadgeStatus: $_guid");
    }
    return _accountBadgeStatus ?? false;
  }

  set accountBadgeStatus(bool? value) {
    _accountBadgeStatus = value;
    if (_accountBadgeStatus != null) {
      _prefs?.setBool("accountBadgeStatus: $_guid", _accountBadgeStatus!);
    } else {
      _prefs?.remove("accountBadgeStatus: $_guid");
    }
  }

  void clear() {
    debugPrint("=_= clear Enter");

    accountBadgeStatus = null;
    email = null;
    phone = null;
    customersEntity = null;
    license = null;
    insurance = null;
    requestCustomer = null;
    debugPrint("=_= clear finish");
  }
}
