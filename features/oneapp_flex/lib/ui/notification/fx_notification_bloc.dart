// Dart imports:
import 'dart:async';

// Package imports:
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/notification_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';

// Project imports:
import '../../flex_global.dart';
import '../landing/fx_detail_landing_bloc.dart';

class FxNotificationBloc extends BlocBase {
  final _notificationController = StreamController<NotificationUI>();

  Stream<NotificationUI> get uiData => _notificationController.stream;

  String? reservationId;

  void initData({NotificationUI? notificationUI}) async {
    if (notificationUI != null) {
      if (notificationUI.data is NotificationData15MinBefore) {
        reservationId =
            (notificationUI.data as NotificationData15MinBefore).reservationId;
      }
      _notificationController.add(notificationUI);
    }
  }

  Future fetchReservation(String? reservationId) async {
    if (reservationId != null) {
      await showProgress(NavigateService.context);
      await APIClientConfig.flexClient
          .sendGetCustomersInfo(customerGuid: Global.getInstance().guid);
      String? vin = Global.getInstance().vin;
      final vehicleInfoEntity = await getLocalPayloadFromVin(vin);
      await APIClientConfig.flexClient.sendGetReservationDetail(
          reservationId,
          isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features)
              ? FLEX_RENTALS_EV_SWAP
              : FLEX_RENTALS_RENTAL,
          vehicleInfoEntity?.brand ?? Global.getInstance().appBrand,
          vehicleInfoEntity?.vin ?? Global.getInstance().vin);
      await dismissProgress(NavigateService.context);
    }
  }

  @override
  void dispose() {
    _notificationController.close();
  }
}
