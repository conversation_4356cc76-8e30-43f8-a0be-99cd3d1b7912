// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/notification_helper.dart';

// Project imports:
import '../../route/router.dart';
import 'fx_notification_bloc.dart';

class FxNotificationView extends StatefulWidget {
  final NotificationUI? notificationUI;

  const FxNotificationView({Key? key, this.notificationUI}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _NotificationViewState();
}

class _NotificationViewState extends State<FxNotificationView> {
  late FxNotificationBloc _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = FxNotificationBloc();
    _bloc.initData(notificationUI: widget.notificationUI);
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        stream: _bloc.uiData,
        builder: (context, AsyncSnapshot<NotificationUI> data) {
          return BlocProvider(
            child: Scaffold(
              appBar: commonTitle(
                data.data!.title!,
                onBackPressed: () {
                  FlexRouter.pushName(RoutePath.FX_DETAIL_LANDING);
                },
              ) as PreferredSizeWidget?,
              backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
              body: _buildBody(context, data.data!),
            ),
            bloc: _bloc,
          );
        });
  }

  Widget _buildBody(BuildContext context, NotificationUI data) {
    return Container(
      padding: EdgeInsets.only(left: 24.w, right: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 40.h),
            child: Text(
              data.time!,
              style: TextStyleExtension().newStyleWithColor(
                  ThemeConfig.current().textStyleUtil.body2,
                  ThemeConfig.current().colorUtil.tertiary05),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 16.h),
            child: Text(
              data.contentTitle!,
              style: ThemeConfig.current().textStyleUtil.subHeadline1,
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 32.h),
            width: double.infinity,
            height: 1,
            color: ThemeConfig.current().colorUtil.tertiary10,
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.only(top: 32.h),
              child: SingleChildScrollView(
                child: Text(
                  data.description!,
                  style: ThemeConfig.current().textStyleUtil.callout2,
                ),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(bottom: 24.h, top: 40.h),
                child: SizedBox(
                  width: 192.w,
                  height: 52.h,
                  child: CustomDefaultButton(
                    backgroundColor: ThemeConfig.current().colorUtil.button01b,
                    buttonTextColor: ThemeConfig.current().colorUtil.button01a,
                    disabledBackgroundColor:
                        ThemeConfig.current().colorUtil.button02c,
                    borderColor: ThemeConfig.current().colorUtil.button01b,
                    verticalPadding: 4.h,
                    text: data.buttonStr,
                    press: () async {
                      if (data.buttonStr ==
                              OneAppString.of().viewReservationSummary &&
                          _bloc.reservationId != null) {
                        final data = {
                          "ReservationId": _bloc.reservationId,
                        };
                        await _bloc.fetchReservation(_bloc.reservationId);
                        FlexRouter.pushName(RoutePath.FX_SUMMARY_DETAIL,
                            arguments: data);
                      } else {
                        goBackToDashboardNative();
                      }
                    },
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
