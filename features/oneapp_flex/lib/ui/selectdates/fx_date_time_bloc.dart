// Flutter imports:
import 'package:flutter/foundation.dart';

// Package imports:
import 'package:intl/intl.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_station_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/select_date_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/time_data_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

// Project imports:
import '../../flex_global.dart';
import '../../route/router.dart';
import '../../util/fx_common_util.dart';
import '../summary/fx_summary_detail_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class FxDateTimeBloc extends BlocBase {
  final _timesController = BehaviorSubject<AvailableTimeScope>();

  Stream<AvailableTimeScope> get uiTimes => _timesController.stream;

  final _pickUpAMPMCtrl = BehaviorSubject<String>();

  Stream<String> get pickUpAMPM => _pickUpAMPMCtrl.stream;

  final _dropOffAMPMCtrl = BehaviorSubject<String>();

  Stream<String> get dropOffAMPM => _dropOffAMPMCtrl.stream;

  final _enableBtnCtrl = BehaviorSubject<bool>();

  Stream<bool> get enableBtn => _enableBtnCtrl.stream;

  final _updatePickerMaxDateCtrl = BehaviorSubject<DateTime?>();

  Stream<DateTime?> get updatePickerMaxDateUI =>
      _updatePickerMaxDateCtrl.stream;

  Stream<bool> get showCurrentDayNoticeUI => _showCurrentDayNoticeCtrl.stream;
  final _showCurrentDayNoticeCtrl = BehaviorSubject<bool>();

  PickerDateRange? initRange;

  List<DateTime>? initDisableDates;

  List<Hours>? businessHours;

  AvailableTimeItem? _selectedPickUpTime;

  AvailableTimeItem? _selectedDropOffTime;

  final List<AvailableTimeItem> pickUpTimeList = [
    AvailableTimeItem(hmm: '0:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '1:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '1:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '2:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '2:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '3:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '3:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '4:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '4:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '5:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '5:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '6:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '6:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '7:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '7:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '8:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '8:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '9:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '9:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '10:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '10:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '11:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '11:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '12:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '12:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '1:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '1:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '2:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '2:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '3:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '3:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '4:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '4:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '5:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '5:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '6:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '6:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '7:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '7:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '8:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '8:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '9:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '9:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '10:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '10:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '11:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '11:30', ampm: 'PM'),
  ];

  final List<AvailableTimeItem> dropOffTimeList = [
    AvailableTimeItem(hmm: '0:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '1:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '1:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '2:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '2:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '3:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '3:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '4:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '4:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '5:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '5:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '6:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '6:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '7:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '7:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '8:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '8:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '9:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '9:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '10:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '10:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '11:00', ampm: 'AM'),
    AvailableTimeItem(hmm: '11:30', ampm: 'AM'),
    AvailableTimeItem(hmm: '12:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '12:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '1:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '1:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '2:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '2:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '3:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '3:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '4:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '4:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '5:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '5:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '6:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '6:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '7:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '7:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '8:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '8:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '9:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '9:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '10:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '10:30', ampm: 'PM'),
    AvailableTimeItem(hmm: '11:00', ampm: 'PM'),
    AvailableTimeItem(hmm: '11:30', ampm: 'PM'),
  ];
  vehicleInfo.Payload? vehicleItem;

  void init(DateTime? pickUpDateTime, DateTime? dropOffDateTime,
      List<Hours>? businessHours) {
    this.businessHours = businessHours;
    initDisableDates = getDisabledDateTimes(businessHours);
    if (pickUpDateTime != null && dropOffDateTime != null) {
      initRange = PickerDateRange(pickUpDateTime, dropOffDateTime);

      final _pickUphmm = DateFormat('h:mm').format(pickUpDateTime);
      final _pickUpamPm = DateFormat('a').format(pickUpDateTime);
      pickUpTimeList.forEach((element) {
        element.isSelected = false;
        if (element.hmm == _pickUphmm && _pickUpamPm == element.ampm) {
          element.isSelected = true;
          _selectedPickUpTime = element;
        }
      });
      Future(() {
        _pickUpAMPMCtrl.add(_pickUpamPm);
      });

      final _dropOffhmm = DateFormat('h:mm').format(dropOffDateTime);
      final _dropOffamPm = DateFormat('a').format(dropOffDateTime);
      dropOffTimeList.forEach((element) {
        element.isSelected = false;
        if (element.hmm == _dropOffhmm && _dropOffamPm == element.ampm) {
          element.isSelected = true;
          _selectedDropOffTime = element;
        }
      });
      Future(() {
        _dropOffAMPMCtrl.add(_dropOffamPm);
      });
    }
    loadData(pickUpDateTime, dropOffDateTime, businessHours);
  }

  void loadData(DateTime? pickUpDateTime, DateTime? dropOffDateTime,
      List<Hours>? businessHours) {
    if (pickUpDateTime != null && dropOffDateTime != null) {
      List<AvailableTimeItem> pickUpTimes = [];
      List<AvailableTimeItem> dropOffTimes = [];
      if (businessHours != null) {
        pickUpTimeList.forEach((element) {
          // final dt =
          //     createDateTime(pickUpDateTime, element.h_mm, element.am_pm);
          bool isAvailable =
              checkAvailable(pickUpDateTime, element, businessHours);
          if (isAvailable) {
            pickUpTimes.add(element);
          }
        });
        dropOffTimeList.forEach((element) {
          // final dt =
          //     createDateTime(dropOffDateTime, element.h_mm, element.am_pm);
          bool isAvailable =
              checkAvailable(dropOffDateTime, element, businessHours);
          if (isAvailable) {
            dropOffTimes.add(element);
          }
        });
      } else {
        pickUpTimes.addAll(pickUpTimeList);
        dropOffTimes.addAll(dropOffTimeList);
      }
      Future(() {
        _timesController.add(AvailableTimeScope(
          pickUpTimeList: pickUpTimes,
          dropOffTimeList: dropOffTimes,
        ));
      });
    } else {
      Future(() async {
        _timesController.add(AvailableTimeScope());
        vehicleItem = await VehicleRepo()
            .getLocalPayloadFromVin(Global.getInstance().vin);
        updateMaxTimeByStartTime(
            DateTime.parse(Global.getInstance().evSwapExpireDate!));
      });
    }
    checkEnableContinue();
  }

  void updateTimeByRange(List<DateTime> selectedDates) {
    final range = PickerDateRange(
      selectedDates.firstOrNull,
      selectedDates.lastOrNull,
    );
    initRange = range;
    if (isInCurrentDate()) {
      _showCurrentDayNoticeCtrl.add(true);
      updateMaxTimeByStartTime(null);
      return;
    } else {
      _showCurrentDayNoticeCtrl.add(false);
    }

    if (range.endDate == null && range.startDate != null) {
      _selectedPickUpTime = null;
      _selectedDropOffTime = null;
      selectPickUpTime(null);
      selectDropOffTime(null);
      updateMaxTimeByStartTime(range.startDate);
    }
    loadData(range.startDate, range.endDate, businessHours);
  }

  void updateMaxTimeByStartTime(DateTime? startTime) {
    _updatePickerMaxDateCtrl.add(startTime);
  }

  void selectPickUpTime(AvailableTimeItem? item) {
    AvailableTimeScope scope = _timesController.value;
    if (scope.pickUpTimeList?.isNotEmpty == true) {
      scope.pickUpTimeList!.forEach((element) {
        element.isSelected = false;
        if (element.hmm == item?.hmm && element.ampm == item?.ampm) {
          element.isSelected = true;
          _selectedPickUpTime = element;
        }
      });
    }
    checkEnableContinue();
    _timesController.add(scope);
    // _pickUpAMPMCtrl.add(amPm);
  }

  List<AvailableTimeItem>? getPickUpList() {
    return _timesController.value.pickUpTimeList;
  }

  List<AvailableTimeItem>? getDropOffList() {
    return _timesController.value.dropOffTimeList;
  }

  void selectDropOffTime(AvailableTimeItem? item) {
    AvailableTimeScope scope = _timesController.value;
    if (scope.dropOffTimeList?.isNotEmpty == true) {
      scope.dropOffTimeList!.forEach((element) {
        element.isSelected = false;
        if (element.hmm == item?.hmm && element.ampm == item?.ampm) {
          element.isSelected = true;
          _selectedDropOffTime = element;
        }
      });
    }
    checkEnableContinue();
    _timesController.add(scope);
  }

  DateTime? getPickUpTime() {
    if (initRange != null) {
      return createDateTime(initRange!.startDate!, _selectedPickUpTime!.hmm,
          _selectedPickUpTime!.ampm);
    }
    return null;
  }

  DateTime? getDropOffTime() {
    if (initRange != null) {
      return createDateTime(initRange!.endDate!, _selectedDropOffTime!.hmm,
          _selectedDropOffTime!.ampm);
    }
    return null;
  }

  bool isIn24Hours() {
    final pickUpTime = getPickUpTime()!;
    final currentTime = DateTime.now().add(Duration(hours: 24));
    return currentTime.compareTo(pickUpTime) > 0;
  }

  bool isInCurrentDate() {
    DateTime? pickUpTime;
    if (_selectedPickUpTime != null) {
      pickUpTime = getPickUpTime();
    } else {
      final date = initRange?.endDate ?? initRange?.startDate;
      if (date != null) {
        pickUpTime = DateTime(date.year, date.month, date.day);
      }
    }
    pickUpTime = pickUpTime ?? DateTime.now();
    final pickUpCompareTime = DateTime(
      pickUpTime.year,
      pickUpTime.month,
      pickUpTime.day,
    );
    final currentTime = DateTime.now();
    final currentCompareTime =
        DateTime(currentTime.year, currentTime.month, currentTime.day);
    return currentCompareTime.compareTo(pickUpCompareTime) == 0;
  }

  void checkEnableContinue() {
    bool enable = initRange?.startDate != null &&
        initRange?.endDate != null &&
        _selectedDropOffTime != null &&
        _selectedPickUpTime != null;

    if (enable && initRange?.startDate?.compareTo(initRange!.endDate!) == 0) {
      // Date same, compare the time
      enable = (_selectedDropOffTime!
                  .toDateTime()
                  ?.compareTo(_selectedPickUpTime!.toDateTime()!) ??
              0) >
          0;
    }
    _enableBtnCtrl.add(enable);
  }

  Future<void> onContinueClick({IntentToSelectDate? intentData}) async {
    int? comeFrom = intentData?.comeFrom;
    int? requestCode = intentData?.requestCode;
    final pickUpDateTime = getPickUpTime()!;
    final dropOffDateTime = getDropOffTime()!;
    final timeData = TimeData(
      pickUpDate: DateFormat("EEE, MMM d").format(pickUpDateTime),
      dropOffDate: DateFormat("EEE, MMM d").format(dropOffDateTime),
      pickUpTime: DateFormat("h:mm a").format(pickUpDateTime),
      dropOffTime: DateFormat("h:mm a").format(dropOffDateTime),
      pickUpDateTime: pickUpDateTime,
      dropOffDateTime: dropOffDateTime,
    );
    IntentToSelectVehicle data = IntentToSelectVehicle(
      status: intentData?.status,
      reservationId: intentData?.reservationId,
      orgID: intentData?.orgID,
      businessHour: intentData?.dealerInfo?.businessHours,
      dealerId: intentData?.dealerInfo?.dealerID,
      timeData: timeData,
      stationId: intentData?.dealerInfo?.stationId,
      phone: intentData?.dealerInfo?.dealerPhone,
      locationName: intentData?.dealerInfo?.dealerName,
      formattedAddress: intentData?.dealerDetailInfo?.address,
      dealerDetailInfo: intentData?.dealerDetailInfo,
      demoIndex: intentData?.demoIndex,
      resetVehicle: requestCode,
      comeFrom: comeFrom ?? comeFromLanding,
    );
    switch (comeFrom) {
      case comeFromLanding:
        await fetchVehiclesFromDates(data);
        FxCommonUtils.showSelectVehiclePage(data: data);
        break;
      case comeFromSelectVehicle:
      case comeFromEditVehicle:
        await fetchVehiclesFromDates(data);
        final result = await FxCommonUtils.showSelectVehiclePage(data: data);
        if (result != null && result is Map) {
          Future(() {
            FlexRouter.popRoute(result: result);
          });
        }
        break;
      case comeFromSummary:
        if (requestCode == null) {
          await fetchVehiclesFromDates(data);
          FxCommonUtils.showSelectVehiclePage(data: data);
        } else if (requestCode == RESET_DROP_REQUEST_CODE) {
          FlexRouter.popRoute(result: timeData);
        } else {
          data.resetVehicle = requestCode;
          await fetchVehiclesFromDates(data);
          final result = await FxCommonUtils.showSelectVehiclePage(data: data);
          if (result != null && result is Map) {
            Future(() {
              FlexRouter.popRoute(result: result);
            });
          } else if (result != null && result is IntentToSelectDate) {
            FlexRouter.popRoute(result: result);
          }
        }
        break;
    }
  }

  Future fetchVehiclesFromDates(IntentToSelectVehicle intentData) async {
    await showProgress(NavigateService.context);
    final timeData = intentData.timeData;
    int? dealerTimezoneMinOffset =
        intentData.dealerDetailInfo?.getTimezoneMinutesOffset();
    DateTime requestPickUpDate = _convertDealerTime(
        timeData?.pickUpDateTime ?? DateTime.now(),
        dealerTimezoneMinOffset ?? 0);
    DateTime requestDropOffDate = _convertDealerTime(
        timeData?.dropOffDateTime ?? DateTime.now(),
        dealerTimezoneMinOffset ?? 0);
    String? vin = Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);

    await APIClientConfig.flexClient.sendGetFlexStationPricing(
      intentData.stationId ?? "",
      intentData.orgID ?? ORG_ID,
      isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
          ? FLEX_RENTALS_RENTAL
          : FLEX_RENTALS_RENTAL,
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(requestPickUpDate),
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(requestDropOffDate),
      vehicleItem?.brand ?? Global.getInstance().appBrand,
      vehicleItem?.vin ?? Global.getInstance().vin,
      demoIndex: intentData.demoIndex,
    );
    await dismissProgress(NavigateService.context);
  }

  @override
  void dispose() {
    _timesController.close();
    _dropOffAMPMCtrl.close();
    _pickUpAMPMCtrl.close();
    _enableBtnCtrl.close();
    _showCurrentDayNoticeCtrl.close();
  }
}

List<DateTime> getDisabledDateTimes(List<Hours>? businessHours) {
  List<DateTime> disableDates = [];
  if (businessHours?.isNotEmpty == true) {
    for (int i = 10101; i <= 11231; i++) {
      int realIntValue = i % 10000;
      int month = realIntValue ~/ 100;
      int monthDay = realIntValue % 100;
      int currentYear = DateTime.now().year;
      int indexWeekday = DateTime(currentYear, month, monthDay).weekday % 7;
      bool containDate = false;
      businessHours!.forEach((element) {
        if (element.fromDate?.isNotEmpty == true &&
            element.toDate?.isNotEmpty == true &&
            element.dayOfWeekOrdinal == indexWeekday) {
          int fromDateInt = int.tryParse("1${element.fromDate}")!;
          int toDateInt = int.tryParse("1${element.toDate}")!;
          bool hasOpenCloseTime = element.openingTime?.isNotEmpty == true &&
              element.closingTime?.isNotEmpty == true;
          if (fromDateInt <= toDateInt) {
            if (i >= fromDateInt && i <= toDateInt && hasOpenCloseTime) {
              containDate = true;
            }
          } else if (toDateInt <= fromDateInt) {
            if (i >= toDateInt && i <= fromDateInt && hasOpenCloseTime) {
              containDate = true;
            }
          }
        }
      });

      if (!containDate) {
        for (int year = currentYear; year < (currentYear + 10); year++) {
          final compareDateTime = DateTime(year, month, monthDay);
          debugPrint('----compareDateTime: ${compareDateTime.toString()}');
          disableDates.add(compareDateTime);
        }
      }
    }
  }
  return disableDates;
}

DateTime getDate(DateTime dateTime) {
  return DateTime(dateTime.year, dateTime.month, dateTime.day, 0, 0, 0, 0, 0);
}

DateTime createDateTime(DateTime date, String? hmm, String? amPm) {
  DateTime _dt = DateFormat('h:mm a').parse('$hmm $amPm');
  return DateTime(date.year, date.month, date.day)
      .add(Duration(hours: _dt.hour, minutes: _dt.minute));
}

bool checkDateTimeAvailable(DateTime dateTime, List<Hours> businessHours) {
  bool result = false;
  if (businessHours.isNotEmpty == true) {
    final String currentDay =
        dateTime.day < 10 ? "0${dateTime.day}" : dateTime.day.toString();
    double currentValue =
        (int.tryParse("${dateTime.month}$currentDay") ?? 0) * 0.0001;
    double currentTimeValue =
        (int.tryParse(DateFormat('HHmm').format(dateTime)) ?? 0) * 0.0001;
    businessHours
        .where((element) => (dateTime.weekday % 7) == element.dayOfWeekOrdinal)
        .forEach((element) {
      double fromDateValue = (int.tryParse(element.fromDate!) ?? 0) * 0.0001;
      double toDateValue = (int.tryParse(element.toDate!) ?? 0) * 0.0001;
      bool isInDateRange =
          (currentValue >= fromDateValue && currentValue <= toDateValue);
      if (isInDateRange) {
        double openTimeValue =
            (int.tryParse(element.openingTime?.replaceAll(":", "") ?? "0") ??
                    0) *
                0.0001;
        double closeTimeValue =
            (int.tryParse(element.closingTime?.replaceAll(":", "") ?? "0") ??
                    0) *
                0.0001;
        bool isInTimeRange = (currentTimeValue >= openTimeValue &&
                currentTimeValue <= closeTimeValue) ||
            closeTimeValue == openTimeValue;
        if (isInTimeRange) {
          debugPrint(
              '---openTimeValue: $openTimeValue --- closeTimeValue: $closeTimeValue --- currentTimeValue: $currentTimeValue----');
          debugPrint('===${dateTime.toString()}');
          result = true;
        }
      }
    });
  }
  return result;
}

bool checkAvailable(
    DateTime dateTime, AvailableTimeItem element, List<Hours> businessHours) {
  bool result = false;
  businessHours.forEach((hour) {
    try {
      if (hour.dayOfWeekOrdinal == dateTime.weekday % 7 &&
          _betweenDates(hour.fromDate, hour.toDate, dateTime)) {
        final openTime = DateFormat('HH:mm').parse(hour.openingTime!);
        final closeTime = DateFormat('HH:mm').parse(hour.closingTime!);
        final compareTime =
            DateFormat('h:mm a').parse('${element.hmm} ${element.ampm}');
        if (closeTime.compareTo(openTime) >= 0) {
          bool isAvailable = (compareTime.compareTo(openTime) >= 0) &&
              (compareTime.compareTo(closeTime) <= 0);
          if (isAvailable) {
            result = true;
          }
        } else {
          bool isAvailable = (compareTime.compareTo(openTime) >= 0) ||
              (compareTime.compareTo(closeTime) <= 0);
          if (isAvailable) {
            result = true;
          }
        }
      }
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_RENT);
    }
  });
  return result;
}

bool _betweenDates(String? startDate, String? endDate, DateTime dateTime) {
  if (startDate != null && endDate != null) {
    final now = dateTime;
    int nowBaseLine = 10000 + now.day + now.month * 100;
    int date1 = int.parse("1${startDate.replaceAll(":", "")}");
    int date2 = int.parse("1${endDate.replaceAll(":", "")}");
    if (date2 < date1 && nowBaseLine <= date1 && nowBaseLine >= date2) {
      return true;
    } else if (date1 <= date2 && nowBaseLine >= date1 && nowBaseLine <= date2) {
      return true;
    } else if (date1 == date2) {
      return false;
    }
    return false;
  }
  return true;
}

DateTime _convertDealerTime(
    DateTime dealerDateTime, int dealerTimezoneMinOffset) {
  return dealerDateTime.add(Duration(minutes: -dealerTimezoneMinOffset));
}

/// Defines a range of dates, covers the dates in between the given [startDate]
/// and [endDate] as a range.
@immutable
class PickerDateRange with Diagnosticable {
  /// Creates a picker date range with the given start and end date.
  const PickerDateRange(this.startDate, this.endDate);

  /// The start date of the range.
  final DateTime? startDate;

  /// The end date of the range.
  final DateTime? endDate;

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty<DateTime>('startDate', startDate));
    properties.add(DiagnosticsProperty<DateTime>('endDate', endDate));
  }
}
