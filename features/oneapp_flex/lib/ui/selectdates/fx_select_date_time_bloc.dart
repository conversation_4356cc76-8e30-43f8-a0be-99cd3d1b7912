// Package imports:

// Package imports:
import 'package:intl/intl.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/select_date_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/time_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_cfai_api_client.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

// Project imports:
import '../../flex_global.dart';
import '../../route/router.dart';
import '../summary/fx_summary_detail_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class FxSelectDateTimeBloc extends BlocBase {
  SelectDatesUI datesUI = SelectDatesUI();
  int comeFrom = comeFromLanding;
  int? _requestCode;
  int? demoIndex;
  String? reservationId;
  String? status;

  FXCFAIClient api = APIClientConfig.flexClient;

  final _selectDatesController = BehaviorSubject<SelectDatesUI>();

  Stream<SelectDatesUI> get uiSelectDates => _selectDatesController.stream;

  final _showPickerCtrl = BehaviorSubject<bool>();

  Stream<bool> get showPicker => _showPickerCtrl.stream;

  final _mapReady = BehaviorSubject<bool>();

  Stream<bool> get mapReady => _mapReady.stream;

  void loadData(Object? intentData) {
    if (intentData != null && intentData is IntentToSelectDate) {
      reservationId = intentData.reservationId;
      status = intentData.status;
      demoIndex = intentData.demoIndex;
      datesUI.orgID = intentData.orgID;
      datesUI.dealerDetailInfo = intentData.dealerDetailInfo;
      datesUI.dealerInfo = intentData.dealerInfo;
      if (intentData.comeFrom == comeFromSummary) {
        comeFrom = comeFromSummary;
        _requestCode = intentData.requestCode;
        if (intentData.pickUpDate != null && intentData.dropOffDate != null) {
          DateTime? pickUpOnly = intentData.pickUpDate;
          DateTime? dropOffOnly = intentData.dropOffDate;
          FxCalendarRangeData fxCalendarRangeData = FxCalendarRangeData(
            pickUpDate: pickUpOnly,
            dropOffDate: dropOffOnly,
          );
          dateChanged(fxCalendarRangeData);
        }
      } else if (intentData.comeFrom == comeFromLanding) {
        comeFrom = intentData.comeFrom ?? comeFromLanding;
        if (intentData.pickUpDate != null && intentData.dropOffDate != null) {
          DateTime? pickUpOnly = intentData.pickUpDate;
          DateTime? dropOffOnly = intentData.dropOffDate;
          FxCalendarRangeData fxCalendarRangeData = FxCalendarRangeData(
            pickUpDate: pickUpOnly,
            dropOffDate: dropOffOnly,
          );
          dateChanged(fxCalendarRangeData);
        }
        _showPickerCtrl.add(true);
      } else if (intentData.comeFrom == comeFromSelectVehicle) {
        comeFrom = comeFromSelectVehicle;
        datesUI.dealerInfo = intentData.dealerInfo;
        if (intentData.pickUpDate != null && intentData.dropOffDate != null) {
          DateTime? pickUpOnly = intentData.pickUpDate;
          DateTime? dropOffOnly = intentData.dropOffDate;
          FxCalendarRangeData fxCalendarRangeData = FxCalendarRangeData(
            pickUpDate: pickUpOnly,
            dropOffDate: dropOffOnly,
          );
          dateChanged(fxCalendarRangeData);
        }
      }
    }
    Future.delayed(Duration(milliseconds: 500), () {
      _mapReady.add(true);
    });
  }

  void dateChanged(FxCalendarRangeData changedDate) {
    if (changedDate.pickUpDate != null) {
      datesUI.pickUpDate = changedDate.pickUpDate;
      datesUI.pickUpDateStr =
          DateFormat("EEE, MMM d").format(datesUI.pickUpDate!);
      datesUI.pickUpTimeStr = DateFormat("h:mm a").format(datesUI.pickUpDate!);
    }
    if (changedDate.dropOffDate != null) {
      datesUI.dropOffDate = changedDate.dropOffDate;
      datesUI.dropOffDateStr =
          DateFormat("EEE, MMM d").format(datesUI.dropOffDate!);
      datesUI.dropOffTimeStr =
          DateFormat("h:mm a").format(datesUI.dropOffDate!);
    }
    if (datesUI.pickUpDate != null &&
        datesUI.dropOffDate != null &&
        datesUI.pickUpDate!.millisecondsSinceEpoch <
            datesUI.dropOffDate!.millisecondsSinceEpoch) {
      datesUI.btnEnable = true;
    } else {
      datesUI.btnEnable = false;
    }
    _selectDatesController.sink.add(datesUI);
  }

  Future fetchVehiclesFromDates(IntentToSelectVehicle intentData) async {
    await showProgress(NavigateService.context);
    final timeData = intentData.timeData;
    int? dealerTimezoneMinOffset =
        intentData.dealerDetailInfo?.getTimezoneMinutesOffset();
    DateTime requestPickUpDate = _convertDealerTime(
        timeData?.pickUpDateTime ?? DateTime.now(),
        dealerTimezoneMinOffset ?? 0);
    DateTime requestDropOffDate = _convertDealerTime(
        timeData?.dropOffDateTime ?? DateTime.now(),
        dealerTimezoneMinOffset ?? 0);
    String? vin = Global.getInstance().vin;
    vehicleInfo.Payload? vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(vin);
    await api.sendGetFlexStationPricing(
      intentData.stationId ?? "",
      intentData.orgID ?? ORG_ID,
      isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
          ? FLEX_RENTALS_EV_SWAP
          : FLEX_RENTALS_RENTAL,
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(requestPickUpDate),
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(requestDropOffDate),
      vehicleItem?.brand ?? Global.getInstance().appBrand,
      vehicleItem?.vin ?? Global.getInstance().vin,
      demoIndex: intentData.demoIndex,
    );
    await dismissProgress(NavigateService.context);
  }

  Future<void> onContinueClick() async {
    final timeData = TimeData(
        pickUpDate: datesUI.pickUpDateStr ?? "",
        dropOffDate: datesUI.dropOffDateStr ?? "",
        pickUpTime: DateFormat("h:mm a").format(datesUI.pickUpDate!),
        dropOffTime: DateFormat("h:mm a").format(datesUI.dropOffDate!),
        pickUpDateTime: datesUI.pickUpDate,
        dropOffDateTime: datesUI.dropOffDate);
    IntentToSelectVehicle data = IntentToSelectVehicle(
      status: status,
      reservationId: reservationId,
      orgID: datesUI.orgID,
      businessHour: datesUI.dealerInfo!.businessHours,
      dealerId: datesUI.dealerInfo?.dealerID,
      timeData: timeData,
      stationId: datesUI.dealerInfo?.stationId,
      phone: datesUI.dealerInfo?.dealerPhone,
      locationName: datesUI.dealerInfo?.dealerName,
      formattedAddress: datesUI.dealerDetailInfo?.address,
      dealerDetailInfo: datesUI.dealerDetailInfo,
      demoIndex: demoIndex,
      resetVehicle: _requestCode,
      comeFrom: comeFrom,
    );
    // if (DateTime.now()
    //         .add(Duration(hours: 24))
    //         .compareTo(timeData.pickUpDateTime) >
    //     0) {
    //   _showOver24HoursCtrl.add(true);
    //   return;
    // }
    // if (timeData.pickUpDate == timeData.dropOffDate) {
    //   showCommonDialog(context,
    //       message: FlexString.of().flexSameDateWarning,
    //       confirm: OneAppString.of().commonOK,
    //       confirmClick: () {});
    //   return;
    // }
    // if (timeData.pickUpTimeStamp - (2 * 3600000) < DateTime.now().millisecondsSinceEpoch) {
    //   showCommonDialog(context,
    //       message: FlexString.of().flexSameDateWarning,
    //       confirm: FlexString.of().commonOK,
    //       confirmClick: () {});
    //   return;
    // }

    switch (comeFrom) {
      case comeFromLanding:
        await fetchVehiclesFromDates(data);
        FlexRouter.pushName(RoutePath.FX_SELECT_VEHICLE, arguments: data);
        break;
      case comeFromSelectVehicle:
        await fetchVehiclesFromDates(data);
        FlexRouter.pushName(RoutePath.FX_SELECT_VEHICLE, arguments: data);
        break;
      case comeFromSummary:
        if (_requestCode == null) {
          await fetchVehiclesFromDates(data);
          FlexRouter.pushName(RoutePath.FX_SELECT_VEHICLE, arguments: data);
        } else if (_requestCode == RESET_DROP_REQUEST_CODE) {
          FlexRouter.popRoute(result: timeData);
        } else {
          data.resetVehicle = _requestCode;
          await fetchVehiclesFromDates(data);
          final result = await FlexRouter.pushName(RoutePath.FX_SELECT_VEHICLE,
              arguments: data);
          if (result != null && result is Map) {
            Future(() {
              FlexRouter.popRoute(result: result);
            });
          }
        }
        break;
    }
  }

  @override
  void dispose() {
    _selectDatesController.close();
    _showPickerCtrl.close();
    _mapReady.close();
  }
}

DateTime _convertDealerTime(
    DateTime dealerDateTime, int dealerTimezoneMinOffset) {
  return dealerDateTime.add(Duration(minutes: -dealerTimezoneMinOffset));
}
