// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/dealer_search_helper.dart';

// Project imports:
import '../../route/router.dart';
import '../../util/fx_analytics_event.dart';
import '../../widget/flex_slide_title_widget.dart';

class FxSameDayAlertPage extends StatelessWidget {
  final String? phoneNumber;

  final DealerDetailInfo? dealerDetailInfo;

  const FxSameDayAlertPage({Key? key, this.phoneNumber, this.dealerDetailInfo})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FlexSlideTitle(
            title: OneAppString.of().sameDayNotice,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 32.w),
            alignment: Alignment.center,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  OneAppString.of().sameDayAlertPageTips,
                  style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                        color: ThemeConfig.current().colorUtil.tertiary05,
                      ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: 66.h,
                ),
                SizedBox(
                  width: 48.w,
                  height: 48.w,
                  child: CommonCircleIconImage(
                    'packages/oneapp_common/res/image/svg/toyota/ic_flex_red_clock.svg',
                    iconWidth: 24.w,
                    iconHeight: 24.w,
                    iconPadding: EdgeInsets.all(12.w),
                    circleBackgroundColor:
                        ThemeConfig.current().colorUtil.primary02,
                    iconTintColor: ThemeConfig.current().colorUtil.primary01,
                  ),
                ),
                SizedBox(
                  height: 86.h,
                ),
                _selectDatesButton(
                  OneAppString.of().changeDate,
                  () {
                    FlexRouter.popRoute();
                  },
                ),
                SizedBox(
                  height: 4.h,
                ),
                SizedBox(
                  width: 192.w,
                  height: 52.h,
                  child: CustomDefaultButton(
                    backgroundColor: ThemeConfig.current().colorUtil.button01b,
                    buttonTextColor: ThemeConfig.current().colorUtil.button01a,
                    borderColor: ThemeConfig.current().colorUtil.button01b,
                    disabledBackgroundColor:
                        ThemeConfig.current().colorUtil.button02c,
                    verticalPadding: 4.h,
                    text: OneAppString.of().callDealer,
                    press: () {
                      if (phoneNumber?.isNotEmpty == true) {
                        FireBaseAnalyticsLogger.logMarketingEvent(
                            AnalyticsEvent.FLEX_CALL_DEALER);
                      }
                      dialerLauncher(phoneNumber!);
                    },
                  ),
                ),
                SizedBox(
                  height: 32.h,
                ),
              ],
            ),
          ),
        ],
      ),
      color: ThemeConfig.current().colorUtil.tertiary15,
    );
  }

  Widget _selectDatesButton(String text, VoidCallback onPressed) {
    return TextButton(
      onPressed: onPressed,
      child: Text(
        formatTextForLexusAndToyota(text),
        style: ThemeConfig.current()
            .textStyleUtil
            .buttonLink1
            .copyWith(color: ThemeConfig.current().colorUtil.button02a),
      ),
      style: TextButton.styleFrom(
        foregroundColor: ThemeConfig.current().colorUtil.button05b,
        minimumSize: Size(MIN_BUTTON_WIDTH.w, MIN_BUTTON_HEIGHT.h),
        backgroundColor: Colors.transparent,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(56)),
        ),
        padding: EdgeInsets.symmetric(horizontal: 32, vertical: 4),
      ),
    );
  }
}
