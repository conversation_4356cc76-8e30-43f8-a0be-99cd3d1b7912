// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/appbar/empty_appbar.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/card/simple_card.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_station_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/select_date_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_cfai_api_client.dart';

// Project imports:
import '../../route/router.dart';
import '../../util/fx_analytics_event.dart';
import '../../util/fx_common_util.dart';
import '../../widget/flex_slide_title_widget.dart';
import '../landing/fx_detail_landing_bloc.dart';
import '../summary/fx_summary_dialog_view.dart';
import 'fx_date_time_bloc.dart';

class FxDateTimeView extends StatefulWidget {
  final IntentToSelectDate? data;

  const FxDateTimeView({
    Key? key,
    this.data,
  }) : super(key: key);

  @override
  _FxDateTimeViewState createState() => _FxDateTimeViewState();
}

class _FxDateTimeViewState extends State<FxDateTimeView> {
  final FxDateTimeBloc _bloc = FxDateTimeBloc();
  final FxDetailLandingBloc _creditBloc = FxDetailLandingBloc();
  FXCFAIClient cfaiApi = APIClientConfig.flexClient;
  int? credits;
  DateTime? fixedPickUpDate;
  DateTime? fixedDropOffDate;
  List<Hours>? businessHours;
  DateTime? startingDate;
  DateTime? endingDate;
  int? selectedDays;
  Color calenderSelectedRange = ThemeConfig.current().colorUtil.secondary02;
  int initialDaysSelected = 0;

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logMarketingEvent(
        AnalyticsEvent.FLEX_SELECT_DATES_PAGE);
    fixedPickUpDate = widget.data?.pickUpDate;
    fixedDropOffDate = widget.data?.dropOffDate;
    businessHours = widget.data?.dealerInfo?.businessHours ??
        (widget.data?.dealerDetailInfo?.businessHours);
    _bloc.init(fixedPickUpDate, fixedDropOffDate, businessHours);
    if (fixedPickUpDate != null && fixedDropOffDate != null) {
      initialDaysSelected =
          fixedDropOffDate!.difference(fixedPickUpDate!).inDays;
    }
    _bloc.showCurrentDayNoticeUI.listen((event) {
      if (event == true) {
        FxCommonUtils.showSameDayAlertPage(
            phoneNumber: widget.data?.dealerInfo?.dealerPhone ??
                widget.data?.dealerDetailInfo?.phoneNumber);
      }
    });
    () async {
      credits = await _creditBloc.availableCredits();
    }();
  }

  _showEnoughCreditDialog() {
    return showModalBottomSheet(
      enableDrag: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return FxVehicleDialogViewState(
            imageUrl: errorIconImage,
            title: OneAppString.of().notEnoughCredits,
            description: OneAppString.of().notEnoughCreditsSummary,
            primaryButtonText: OneAppString.of().dismissText,
            primaryButtonPressed: () async {
              await FlexRouter.popRoute();
            });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      child: Scaffold(
        appBar: EmptyAppBar(
          backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
        ),
        body: Stack(
          alignment: Alignment.bottomCenter,
          fit: StackFit.expand,
          children: [
            ListView(
              children: [
                FlexSlideTitle(
                  title: OneAppString.of().dateTime,
                  onBackPressed: () {
                    FlexRouter.popRoute();
                  },
                  menuItem: Text(
                    OneAppString.of().twoOf3,
                    style: ThemeConfig.current().textStyleUtil.body1,
                  ),
                ),
                commonSimpleCard(
                  margin: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Container(
                      height: 344.w,
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(
                          vertical: 16.h, horizontal: 10.w),
                      child: StreamBuilder(
                        stream: _bloc.updatePickerMaxDateUI,
                        builder: (ctx, AsyncSnapshot<DateTime?> snapData) {
                          final selectedHighlight =
                              isDarkTheme() ? Colors.black : Colors.white;
                          final selectedHighlightText =
                              isDarkTheme() ? Colors.white : Colors.black;
                          return Theme(
                            data: Theme.of(context).copyWith(
                                colorScheme: isDarkTheme()
                                    ? ColorScheme.dark()
                                    : ColorScheme.light()),
                            child: CalendarDatePicker2(
                              config: CalendarDatePicker2Config(
                                daySplashColor: Colors.transparent,
                                calendarType: CalendarDatePicker2Type.range,
                                controlsTextStyle: ThemeConfig.current()
                                    .textStyleUtil
                                    .callout3,
                                selectedDayHighlightColor:
                                    selectedHighlightText,
                                selectedDayTextStyle: ThemeConfig.current()
                                    .textStyleUtil
                                    .callout1
                                    .copyWith(color: selectedHighlight),
                                selectedMonthTextStyle: ThemeConfig.current()
                                    .textStyleUtil
                                    .callout1
                                    .copyWith(color: selectedHighlight),
                                selectedYearTextStyle: ThemeConfig.current()
                                    .textStyleUtil
                                    .callout1
                                    .copyWith(color: selectedHighlight),
                                yearTextStyle: ThemeConfig.current()
                                    .textStyleUtil
                                    .subHeadline2,
                                monthTextStyle: ThemeConfig.current()
                                    .textStyleUtil
                                    .subHeadline2,
                                weekdayLabelTextStyle: ThemeConfig.current()
                                    .textStyleUtil
                                    .callout1,
                                dayTextStyle: ThemeConfig.current()
                                    .textStyleUtil
                                    .callout1,
                                disabledDayTextStyle: ThemeConfig.current()
                                    .textStyleUtil
                                    .callout1
                                    .copyWith(
                                      color: ThemeConfig.current()
                                          .colorUtil
                                          .tertiary07,
                                    ),
                                selectedRangeHighlightColor:
                                    ThemeConfig.current().colorUtil.secondary02,
                                selectableDayPredicate: (dateTime) {
                                  final today = DateTime.now();
                                  final normalizedToday = DateTime(
                                      today.year, today.month, today.day);
                                  final normalizedDate = DateTime(dateTime.year,
                                      dateTime.month, dateTime.day);
                                  final isPast =
                                      normalizedDate.isBefore(normalizedToday);
                                  return !isPast &&
                                      !(_bloc.initDisableDates
                                              ?.contains(dateTime) ??
                                          false);
                                },
                              ),
                              value: [],
                              displayedMonthDate: DateTime.now(),
                              onValueChanged: (selectedDates) {
                                _bloc.updateTimeByRange(selectedDates);
                              },
                            ),
                          );
                        },
                      )),
                ),
                SizedBox(
                  height: 16.h,
                ),
                commonSimpleCard(
                  margin: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 21.h),
                    child: StreamBuilder(
                      stream: _bloc.uiTimes,
                      builder:
                          (ctx, AsyncSnapshot<AvailableTimeScope> snapData) {
                        AvailableTimeScope? scope = snapData.data;
                        if (scope?.dropOffTimeList != null &&
                            scope?.pickUpTimeList != null) {
                          // Show time area
                          return _timeArea(
                              scope!.pickUpTimeList, scope.dropOffTimeList);
                        } else {
                          return _emptyTimeArea();
                        }
                      },
                    ),
                  ),
                ),
                SizedBox(
                  height: 24.h,
                ),
                Center(
                  child: AnimatedOpacity(
                    opacity: 0,
                    duration: Duration.zero,
                    child: SizedBox(
                      width: 192.w,
                      height: 52.h,
                      child: CustomDefaultButton(
                        backgroundColor:
                            ThemeConfig.current().colorUtil.button02a,
                        buttonTextColor:
                            ThemeConfig.current().colorUtil.button02b,
                        borderColor: ThemeConfig.current().colorUtil.button02a,
                        disabledBackgroundColor:
                            ThemeConfig.current().colorUtil.button02d,
                        disabledButtonTextColor:
                            ThemeConfig.current().colorUtil.button05a,
                        text: OneAppString.of().commonConfirm,
                        primaryButtonState: PrimaryButtonState.INACTIVE,
                        verticalPadding: 4.h,
                        press: null,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 32.h,
                ),
              ],
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 24.h,
                  ),
                  StreamBuilder(
                    stream: _bloc.enableBtn,
                    builder: (ctx, snapData) {
                      return SizedBox(
                        width: 192.w,
                        height: 52.h,
                        child: CustomDefaultButton(
                          backgroundColor:
                              ThemeConfig.current().colorUtil.button02a,
                          buttonTextColor:
                              ThemeConfig.current().colorUtil.button02b,
                          borderColor:
                              ThemeConfig.current().colorUtil.button02a,
                          disabledBackgroundColor:
                              ThemeConfig.current().colorUtil.button02d,
                          disabledButtonTextColor:
                              ThemeConfig.current().colorUtil.button05a,
                          text: OneAppString.of().commonConfirm,
                          verticalPadding: 4.h,
                          primaryButtonState: snapData.data == true
                              ? PrimaryButtonState.ACTIVE
                              : PrimaryButtonState.INACTIVE,
                          press: snapData.data == true
                              ? (selectedDays != null &&
                                          credits != null &&
                                          selectedDays! >=
                                              ((credits ?? 0) +
                                                  initialDaysSelected) &&
                                          credits != 0 ||
                                      (credits == 0 &&
                                          selectedDays! > initialDaysSelected))
                                  ? () async {
                                      setState(() {
                                        calenderSelectedRange =
                                            ThemeConfig.current()
                                                .colorUtil
                                                .secondary03;
                                      });
                                      _showEnoughCreditDialog();
                                    }
                                  : () async {
                                      if (_bloc.isInCurrentDate()) {
                                        FxCommonUtils.showSameDayAlertPage(
                                            phoneNumber: widget.data?.dealerInfo
                                                    ?.dealerPhone ??
                                                widget.data?.dealerDetailInfo
                                                    ?.phoneNumber);
                                      } else {
                                        _bloc.onContinueClick(
                                            intentData: widget.data);
                                      }
                                    }
                              : null,
                        ),
                      );
                    },
                  ),
                  SizedBox(
                    height: 32.h,
                  ),
                ],
              ),
            )
          ],
        ),
        backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      ),
      bloc: _bloc,
    );
  }

  Widget _emptyTimeArea() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            OneAppString.of().pickUpDropOff,
            style: ThemeConfig.current().textStyleUtil.subHeadline2,
          ),
          SizedBox(
            height: 20.h,
          ),
          Text(
            OneAppString.of().dateTimeViewTips,
            style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary05,
                ),
          ),
          SizedBox(
            height: 46.h,
          ),
          Stack(
            fit: StackFit.loose,
            alignment: Alignment.center,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: SizedBox(
                  width: double.infinity,
                  child: SvgPicture.asset(
                    'packages/oneapp_common/res/image/svg/toyota/ic_flex_dash_line.svg',
                  ),
                ),
              ),
              Row(
                children: [
                  SizedBox(
                    width: 48.w,
                    height: 48.w,
                    child: CommonCircleIconImage(
                      'packages/oneapp_common/res/image/svg/toyota/ic_flex_pickup_dropoff1.svg',
                      iconWidth: 24.w,
                      iconHeight: 24.w,
                      iconTintColor: ThemeConfig.current().colorUtil.tertiary03,
                      circleBackgroundColor:
                          ThemeConfig.current().colorUtil.button05b,
                    ),
                  ),
                  Spacer(),
                  SizedBox(
                    height: 48.w,
                    width: 48.w,
                    child: CommonCircleIconImage(
                      'packages/oneapp_common/res/image/svg/toyota/ic_flex_pickup_dropoff2.svg',
                      iconWidth: 24.w,
                      iconHeight: 24.w,
                      iconTintColor: ThemeConfig.current().colorUtil.tertiary03,
                      circleBackgroundColor:
                          ThemeConfig.current().colorUtil.button05b,
                    ),
                  ),
                  Spacer(),
                  SizedBox(
                    height: 48.w,
                    width: 48.w,
                    child: CommonCircleIconImage(
                      'packages/oneapp_common/res/image/svg/toyota/ic_flex_pickup_dropoff3.svg',
                      iconWidth: 24.w,
                      iconHeight: 24.w,
                      iconTintColor: ThemeConfig.current().colorUtil.tertiary03,
                      circleBackgroundColor:
                          ThemeConfig.current().colorUtil.button05b,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
        ],
      ),
    );
  }

  Widget _timeArea(List<AvailableTimeItem>? pickUpTimeList,
      List<AvailableTimeItem>? dropOffTimeList) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        StreamBuilder(
          stream: _bloc.pickUpAMPM,
          builder: (ctx, snapData) {
            return _timerSelection(
              title: OneAppString.of().pickUp,
              amOrPm: '',
              timeList: snapData.data != null
                  ? _bloc.getPickUpList()
                  : pickUpTimeList,
              onItemClick: (item) {
                _bloc.selectPickUpTime(item);
              },
            );
          },
        ),
        Container(
          height: 1.h,
          margin: EdgeInsets.symmetric(vertical: 10.h, horizontal: 16.w),
          color: ThemeConfig.current().colorUtil.tertiary10,
        ),
        StreamBuilder(
          stream: _bloc.dropOffAMPM,
          builder: (ctx, snapData) {
            return _timerSelection(
              title: OneAppString.of().dropOff,
              amOrPm: '',
              timeList: snapData.data != null
                  ? _bloc.getDropOffList()
                  : dropOffTimeList,
              onItemClick: (item) {
                _bloc.selectDropOffTime(item);
              },
            );
          },
        ),
      ],
    );
  }

  Widget _timerSelection(
      {String? title,
      String? amOrPm,
      List<AvailableTimeItem>? timeList,
      Function(AvailableTimeItem i)? onItemClick}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              child: Text(
                title ?? '',
                style: ThemeConfig.current().textStyleUtil.subHeadline2,
              ),
            ),
            Text(
              amOrPm ?? '',
              style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                    color: ThemeConfig.current().colorUtil.tertiary07,
                  ),
            ),
            SizedBox(
              width: 16.w,
            ),
          ],
        ),
        SizedBox(
          height: 9.h,
        ),
        SizedBox(
          height: 40.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemBuilder: (ctx, index) {
              AvailableTimeItem item = timeList![index];
              return _timeItem(item, onItemClick);
            },
            itemCount: timeList?.length ?? 0,
          ),
        ),
      ],
    );
  }

  Widget _timeItem(
      AvailableTimeItem item, Function(AvailableTimeItem i)? onItemClick) {
    return GestureDetector(
      onTap: () {
        onItemClick?.call(item);
      },
      child: Container(
        height: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Center(
          child: Text(
            "${item.hmm ?? ''}${item.ampm?.toLowerCase() ?? ''}",
            style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                  color: (item.isSelected ?? false)
                      ? ThemeConfig.current().colorUtil.button05b
                      : ThemeConfig.current().colorUtil.button02a,
                ),
          ),
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(100),
          color: (item.isSelected ?? false)
              ? ThemeConfig.current().colorUtil.button02a
              : Colors.transparent,
        ),
      ),
    );
  }
}
