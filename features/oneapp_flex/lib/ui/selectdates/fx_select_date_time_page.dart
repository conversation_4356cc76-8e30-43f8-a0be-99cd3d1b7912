// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/card/simple_card.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/select_date_helper.dart';
import 'package:sliding_sheet/sliding_sheet.dart';

// Project imports:
import '../../route/router.dart';
import '../../widget/flex_slide_title_widget.dart';
import '../search/fx_search_map.dart';
import 'fx_select_date_time_bloc.dart';

class FxSelectDateTimePage extends StatefulWidget {
  final IntentToSelectDate? intentData;

  const FxSelectDateTimePage({Key? key, this.intentData}) : super(key: key);

  @override
  _FxSelectDateTimePageState createState() => _FxSelectDateTimePageState();
}

class _FxSelectDateTimePageState extends State<FxSelectDateTimePage> {
  final FxSelectDateTimeBloc _bloc = FxSelectDateTimeBloc();

  @override
  void initState() {
    super.initState();
    _bloc.loadData(widget.intentData);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      bloc: _bloc,
      child: Scaffold(
        body: SlidingSheet(
          color: ThemeConfig.current().colorUtil.tertiary15,
          cornerRadiusOnFullscreen: 0,
          addTopViewPaddingOnFullscreen: true,
          elevation: 0,
          cornerRadius: 30,
          snapSpec: const SnapSpec(
            snap: true,
            positioning: SnapPositioning.relativeToAvailableSpace,
            snappings: const [
              0.6,
              1.0,
            ],
          ),
          body: _mapArea(),
          headerBuilder: (context, state) {
            return FlexSlideTitle(
              title: OneAppString.of().dateTime,
              onBackPressed: () {
                FlexRouter.popRoute(result: true);
              },
              menuItem: Text(
                OneAppString.of().twoOf3,
                style: ThemeConfig.current().textStyleUtil.body1,
              ),
            );
          },
          builder: (context, state) {
            return _dateTimeContent();
          },
        ),
      ),
    );
  }

  Widget _mapArea() {
    return StreamBuilder(
      stream: _bloc.mapReady,
      builder: (ctx, snapData) {
        double screenHeight = MediaQuery.of(context).size.height;
        return FXSearchMap(
          dealerDetailInfoList: snapData.data != null
              ? [widget.intentData!.dealerDetailInfo]
              : null,
          paddingBottom: snapData.data != null ? screenHeight * 0.6 : 60,
        );
      },
    );
  }

  Widget _dateTimeContent() {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 0.h, 16.w, 24.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 116.h,
            child: Image.asset(
                'packages/oneapp_common/res/images/ic_flex_dt_car.png'),
          ),
          SizedBox(
            height: 32.h,
          ),
          StreamBuilder(
            stream: _bloc.uiSelectDates,
            builder: (ctx, AsyncSnapshot<SelectDatesUI> snapData) {
              return commonSimpleCard(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _timePickContent(
                      title: OneAppString.of().pickUp,
                      dateTime: snapData.data?.pickUpDate,
                      onItemTap: (dateTime) async {
                        final intentData = widget.intentData!;
                        intentData.pickUpDate = snapData.data?.pickUpDate;
                        intentData.dropOffDate = snapData.data?.dropOffDate;

                        final result = await FlexRouter.pushName(
                            RoutePath.FX_CALENDAR_VIEW,
                            arguments: intentData);
                        if (result != null && result is Map) {
                          FlexRouter.popRoute(result: result);
                        }
                      },
                    ),
                    Container(
                      height: 1.h,
                      color: Color(0xFFDEDEDE),
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                    ),
                    _timePickContent(
                      title: OneAppString.of().dropOff,
                      dateTime: snapData.data?.dropOffDate,
                      onItemTap: (dateTime) async {
                        final intentData = widget.intentData!;
                        intentData.pickUpDate = snapData.data?.pickUpDate;
                        intentData.dropOffDate = snapData.data?.dropOffDate;

                        final result = await FlexRouter.pushName(
                            RoutePath.FX_CALENDAR_VIEW,
                            arguments: intentData);
                        if (result != null && result is Map) {
                          FlexRouter.popRoute(result: result);
                        }
                      },
                    ),
                  ],
                ),
              );
            },
          ),
          SizedBox(
            height: 24.h,
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 32.h),
            child: StreamBuilder(
                stream: _bloc.uiSelectDates,
                builder: (ctx, AsyncSnapshot<SelectDatesUI> snapData) {
                  bool enable = snapData.data?.pickUpDate != null &&
                      snapData.data?.dropOffDate != null;
                  return SizedBox(
                    width: 192.w,
                    height: 52.h,
                    child: CustomDefaultButton(
                      backgroundColor:
                          ThemeConfig.current().colorUtil.button01b,
                      buttonTextColor:
                          ThemeConfig.current().colorUtil.button01a,
                      borderColor: ThemeConfig.current().colorUtil.button01b,
                      disabledBackgroundColor:
                          ThemeConfig.current().colorUtil.button02c,
                      text: OneAppString.of().commonContinue,
                      verticalPadding: 4.h,
                      primaryButtonState: enable
                          ? PrimaryButtonState.ACTIVE
                          : PrimaryButtonState.INACTIVE,
                      press: enable
                          ? () {
                              _bloc.onContinueClick();
                            }
                          : null,
                    ),
                  );
                }),
          ),
        ],
      ),
    );
  }

  Widget _timePickContent(
      {String? title,
      DateTime? dateTime,
      Function(DateTime? dateTime)? onItemTap}) {
    String? _strMMM;
    String? _dd;
    String? _timeFormatValue;
    if (dateTime != null) {
      _strMMM = DateFormat.MMM().format(dateTime);
      _dd = DateFormat('dd').format(dateTime);
      _timeFormatValue = DateFormat("MMMM d 'a't hh:mma")
          .format(dateTime)
          .toLowerCase()
          .capitalize();
    }
    return GestureDetector(
      onTap: () {
        onItemTap?.call(dateTime);
      },
      child: Padding(
        padding: EdgeInsets.fromLTRB(15.w, 13.h, 16.w, 13.h),
        child: Row(
          children: [
            Container(
              width: 42.w,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: ThemeConfig.current().colorUtil.secondary01,
                  width: 2.w,
                ),
                color: ThemeConfig.current().colorUtil.tile01,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: double.infinity,
                    height: 20.h,
                    color: ThemeConfig.current().colorUtil.secondary01,
                    child: Text(
                      _strMMM ?? '--',
                      style: ThemeConfig.current()
                          .textStyleUtil
                          .tabLabel01
                          .copyWith(
                              color: ThemeConfig.current().colorUtil.button03a),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    height: 30.h,
                    child: Center(
                      child: Text(
                        _dd ?? '--',
                        style: ThemeConfig.current()
                            .textStyleUtil
                            .subHeadline4
                            .copyWith(
                                color:
                                    ThemeConfig.current().colorUtil.tertiary03),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: 15.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title ?? '--',
                  style: ThemeConfig.current().textStyleUtil.body4,
                ),
                SizedBox(
                  height: 6.h,
                ),
                Text(
                  _timeFormatValue ?? '--',
                  style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                        color: ThemeConfig.current().colorUtil.tertiary05,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
