// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:add_2_calendar/add_2_calendar.dart' as addCalendar;
import 'package:collection/collection.dart' show IterableExtension;
import 'package:date_format/date_format.dart';
import 'package:intl/intl.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/custom_navigator_observer.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network/oneapp_network_packages.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_customer_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_ev_swap_balance.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_pricing_group_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_reservation_detail_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_reservation_list_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_reserve_request_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_station_pricing_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/dealer_search_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/primary_driver_detail_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/rent_car_info_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/reservation_detail_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/time_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_cfai_api_client.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

// Project imports:
import '../../flex_global.dart';
import '../../route/router.dart';
import '../../util/fx_analytics_event.dart';
import '../../util/fx_common_util.dart';
import '../landing/fx_detail_landing_bloc.dart';
import '../landing/fx_landing_bottom_sheet.dart';
import '../search/fx_search_dealer_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

const String MODIFY_MODE = "MODIFY_MODE";
const String RESERVE_MODE = "RESERVE_MODE";
const String CANCEL_MODE = "CANCEL_MODE";

const String LICENSE_TAG = "LICENSE_TAG";
const String INSURANCE_TAG = "INSURANCE_TAG";

const int SELECT_DATE_REQUEST_CODE = 0;
const int SELECT_VEHICLE_REQUEST_CODE = 1;
const int RESET_DATE_REQUEST_CODE = 2;
const int RESET_DROP_REQUEST_CODE = 3;
const int RESET_VEHICLE_REQUEST_CODE = 4;

CommonResponse<FxEvSwapBalance>? EvSwapBalanceResponse;

class FxSummaryDetailBloc implements BlocBase {
  FXCFAIClient cfaiApi = APIClientConfig.flexClient;

  final _detailInfoController = BehaviorSubject<RentCarInfo?>();

  Stream<RentCarInfo?> get uiDetailInfo => _detailInfoController.stream;

  final _driverLicenseController = BehaviorSubject<String?>();

  Stream<String?> get uiDriverLicense => _driverLicenseController.stream;

  final _insuranceController = BehaviorSubject<String?>();

  Stream<String?> get uiInsurance => _insuranceController.stream;

  final _visibleController = BehaviorSubject<bool>();

  Stream<bool> get uiVisible => _visibleController.stream;

  final _routePathController = BehaviorSubject<String?>();

  Stream<String?> get uiRoutePath => _routePathController.stream;

  final _toastController = BehaviorSubject<SummaryToast>();

  Stream<SummaryToast> get uiToast => _toastController.stream;

  final _contractLinkController = BehaviorSubject<String>();

  Stream<String> get uiContractLink => _contractLinkController.stream;

  final _contractShowController = BehaviorSubject<bool>();

  Stream<bool> get uiContractShow => _contractShowController.stream;

  final _reserveMode = BehaviorSubject<String>();

  Stream<String> get uiReserveMode => _reserveMode.stream;

  final _driverDetailController = BehaviorSubject<PrimaryDriverInfo>();

  Stream<PrimaryDriverInfo> get uiDriverDetail =>
      _driverDetailController.stream;

  final _isEditableCtl = BehaviorSubject<bool>();

  Stream<bool> get uiEditable => _isEditableCtl.stream;

  final _summaryEditStatusCtl = BehaviorSubject<SummaryUIStatus?>();

  Stream<SummaryUIStatus?> get uiSummaryStatus => _summaryEditStatusCtl.stream;

  final _profileUICtl = BehaviorSubject<ProfileUI?>();

  Stream<ProfileUI?> get uiProfile => _profileUICtl.stream;

  Stream<int?> get evRemainingBalanceCredits =>
      _evRemainingBalanceCredits.stream;
  final _evRemainingBalanceCredits = BehaviorSubject<int?>();

  ToastMsgData? passToast;

  String? _reservationId, status;
  RentCarInfo? detailInfo;

  FxCfaiReservationListItem? reservationDetail;

  License? driverLicenseInfo;

  Insurance? insuranceInfo;

  String? driverLicenseError, insuranceError;

  String? commitButtonTitle;

  String? specialNotes;

  bool allowEditDriverOpt = true;

  bool isLicenseExpired = false, isInsuranceExpired = false;

  bool? activeStatus,
      btnVisible = true,
      noteVisible = true,
      isIn24Hours = false;

  bool notHistory = true;

  bool isCancel = false;

  bool isModifyComplete = false;

  bool isCarShare = false;

  bool isEndAvailable = false;

  bool isCanEditable = true;

  SummaryUIStatus? summaryUIStatus;

  String? routePath;

  String? contractId;

  String? selectedPaymentMethod;

  String? timeZone;

  PrimaryDriverInfo primaryDriverInfo = PrimaryDriverInfo();

  Customer? customer;

  ProfileUI? profileUI;

  vehicleInfo.Payload? vehicleItem;
  String? pickTime;
  String? dropTime;
  bool isModify = false;

  void loadData(BuildContext context, Object intentData) async {
    String? vin = Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);
    if (intentData is Map) {
      isModify = intentData['IsModify'] ?? false;
      isCarShare = intentData['IsCarShare'] ?? false;
      detailInfo = intentData['CreateReservation'] ?? RentCarInfo();
      _reservationId = intentData['ReservationId'];
      isEndAvailable = intentData['IsEndAvailable'] ?? false;
      status = status ?? intentData['Status'];
      PricingGroups? priceGroup = intentData['PricingGroup'];
      detailInfo!.status = status;
      reservationDetail = intentData['ReservationDetail'];
      timeZone = intentData['timeZone'];
      pickTime = intentData['pickUpTime'];
      dropTime = intentData['dropUpTime'];
      if (!isModify) {
        DateTime? pickUpDateTime =
            reservationDetail?.claimedAt ?? reservationDetail?.startsAt;
        _checkEditable(pickTime: pickUpDateTime);
      }
      refreshSummaryDetail();
      // if (AccountGlobal.getInstance().customersEntity == null) {
      await loadAccount(context);
      // }
      if (_reservationId != null) {
        reservationDetail = await _getReservationDetail(
          context,
          _reservationId!,
        );
        _getStationInfo(context, reservationDetail!);
      }
      if (isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)) {
        EvSwapBalanceResponse = await cfaiApi.sendgetEVSwapBalance(
            Global.getInstance().guid ?? "",
            Global.getInstance().vin ?? "",
            true,
            true);
        if (EvSwapBalanceResponse?.response?.payload != null &&
            EvSwapBalanceResponse?.response?.payload?.first.remainingBalance !=
                null) {
          _evRemainingBalanceCredits.add(
              EvSwapBalanceResponse?.response?.payload?.first.remainingBalance);
        } else {
          _evRemainingBalanceCredits.add(0);
        }
      }
      if (priceGroup != null) {
        detailInfo!.vehicleClass =
            priceGroup.name ?? OneAppString.of().commonNA;
        var description = priceGroup.description;
        final fuelCapacity = _parsingFeature(priceGroup.features, "mpg");
        final seatNumber = _parsingFeature(priceGroup.features, "passengers");
        final luggageCount = _parsingFeature(priceGroup.features, "bags");
        detailInfo!.model =
            FxCommonUtils.getDescriptionInfo(description, TYPE_MODEL);
        detailInfo!.fuelCapacity = fuelCapacity ??
            FxCommonUtils.getDescriptionInfo(description, TYPE_FUEL);
        detailInfo!.seatNumber = seatNumber ??
            FxCommonUtils.getDescriptionInfo(description, TYPE_SEAT);
        detailInfo!.luggage = luggageCount ??
            FxCommonUtils.getDescriptionInfo(description, TYPE_LUGGAGE);
        detailInfo!.vehiclePicture = priceGroup.imageUrl ?? "";
      }
      Future(() {
        if (isModify) {
          resetReservationVehicle(context: context, intentData: intentData);
        } else {
          refresh();
        }
      });
    }
    refreshStatus();
  }

  _checkEditable({DateTime? pickTime}) {
    bool isEditable = true;
    String mode = RESERVE_MODE;
    if (_reservationId != null) {
      final comparedPickUpDate =
          DateTime(pickTime!.year, pickTime.month, pickTime.day);
      final now = DateTime.now();
      final comparedNowDate = DateTime(now.year, now.month, now.day);
      if (isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)) {
        isEditable = true;
      } else {
        isEditable = comparedNowDate.compareTo(comparedPickUpDate) < 0;
      }
      if (status == null) {
        status = detailInfo!.status;
      }
      switch (status) {
        case IN_PROGRESS_ACTION_KEY:
        case OVERDUE_ACTION_KEY:
        case COMPLETED_ACTION_KEY:
        case CANCELLED_ACTION_KEY:
        case CANCELED_ACTION_KEY:
        case ABANDOND_ACTION_KEY:
          isEditable = false;
          primaryDriverInfo.editable = false;
          break;
        default:
          isEditable = true;
          primaryDriverInfo.editable = true;
      }
      mode = CANCEL_MODE;
      allowEditDriverOpt = false;
    }
    this.isCanEditable = isEditable;
    Future(() {
      _reserveMode.add(mode);
      _isEditableCtl.add(isEditable);
    });
  }

  void startModify(bool start) {
    _reserveMode.add(start ? MODIFY_MODE : CANCEL_MODE);
  }

  Future<void> loadAccount(BuildContext context) async {
    final commonResponse = await APIClientConfig.flexClient
        .sendGetCustomersInfo(
            customerGuid: Global.getInstance().guid, allowCache: false);
    if (commonResponse.response != null &&
        commonResponse.response!.payload != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_GET_ACCOUNT_SUCCESS,
          category: LogCategory.FL_RENT);
      FxAccountGlobal.getInstance().insurance =
          commonResponse.response!.payload!.result?.insurance;
      FxAccountGlobal.getInstance().license =
          commonResponse.response!.payload!.result?.license;
      FxAccountGlobal.getInstance().customersEntity =
          commonResponse.response!.payload!.result;
    } else {
      FxAccountGlobal.getInstance().insurance = null;
      FxAccountGlobal.getInstance().license = null;
      FxAccountGlobal.getInstance().customersEntity = null;
      FireBaseAnalyticsLogger.logErrorAPI(
          AnalyticsEvent.FLEX_GET_ACCOUNT_FAILURE,
          category: LogCategory.FL_RENT);
    }
  }

  void checkLicenseExpire() {
    isLicenseExpired = true;
    isInsuranceExpired = true;
    if (FxAccountGlobal.getInstance().customersEntity != null &&
        detailInfo?.pastDropTime != null &&
        detailInfo?.pastPickTime != null) {
      String licenseExpireStr = FxAccountGlobal.getInstance()
              .customersEntity!
              .license!
              .licenseDateOfExpiry ??
          "";
      String insuranceExpireStr = FxAccountGlobal.getInstance()
              .customersEntity!
              .insurance!
              .insuranceDateOfExpiry ??
          "";
      try {
        final licenseTimes = DateFormat('yyyy-MM-dd').parse(licenseExpireStr);
        final nowTime = DateTime.now();
        if ((detailInfo!.pastDropTime!.compareTo(licenseTimes) <= 0 ||
                detailInfo!.pastPickTime!.compareTo(licenseTimes) >= 0) &&
            licenseTimes.compareTo(nowTime) > 0) {
          isLicenseExpired = false;
        }
      } catch (e) {
        debugPrint(e.toString());
        FireBaseAnalyticsLogger.logError(e.toString(),
            category: LogCategory.FL_RENT);
      }
      try {
        final insuranceTime =
            DateFormat('yyyy-MM-dd').parse(insuranceExpireStr);
        final nowTime = DateTime.now();
        if ((detailInfo!.pastDropTime!.compareTo(insuranceTime) <= 0 ||
                detailInfo!.pastPickTime!.compareTo(insuranceTime) >= 0) &&
            insuranceTime.compareTo(nowTime) > 0) {
          isInsuranceExpired = false;
        }
      } catch (e) {
        debugPrint(e.toString());
        FireBaseAnalyticsLogger.logError(e.toString(),
            category: LogCategory.FL_RENT);
      }
    }
  }

  Future<FxCfaiReservationListItem?> _getReservationDetail(
      BuildContext context, String reservationId) async {
    final commonResponse = await cfaiApi.sendGetReservationDetail(
        reservationId,
        isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
            ? FLEX_RENTALS_EV_SWAP
            : FLEX_RENTALS_RENTAL,
        vehicleItem?.brand ?? Global.getInstance().appBrand,
        vehicleItem?.vin ?? Global.getInstance().vin,
        allowCache: false);
    if (commonResponse.response != null) {
      var originStation = commonResponse.response?.payload?.station;
      var vehicle = commonResponse.response?.payload?.vehicle;
      final rppVehicle = commonResponse
          .response?.payload?.rppReservationDetails?.vehicleDetails;

      isCarShare =
          commonResponse.response?.payload?.reservationQuote?.programId ==
              CAR_SHARE_PROGRAM_ID;
      if (reservationDetail == null) {
        reservationDetail = FxCfaiReservationListItem();
      }
      reservationDetail =
          reservationDetail!.copyContent(commonResponse.response!.payload!);
      if (detailInfo != null) {
        final vehicleDetails = commonResponse
            .response?.payload?.rppReservationDetails?.vehicleDetails;
        String mode = vehicleDetails?.getVehicleClassDisplay() ??
            OneAppString.of().commonNA;
        detailInfo!.licensePlate = vehicle?.licensePlate;
        detailInfo!.organizationId =
            commonResponse.response?.payload?.organization?.id;
        detailInfo!.status =
            commonResponse.response?.payload?.reservationQuote?.status;
        detailInfo!.address = originStation?.formattedAddress ?? "--";
        detailInfo!.locationName = originStation?.name ?? "--";
        detailInfo!.dealerPhone = originStation?.supportPhoneNumber;
        detailInfo!.vehicleClass =
            vehicleDetails?.vehicleClass ?? OneAppString.of().commonNA;
        detailInfo!.vehicleGroup =
            vehicleDetails?.vehicleGroup ?? OneAppString.of().vehicle;
        detailInfo!.model = mode;
        detailInfo!.seatNumber =
            "${rppVehicle?.seatingCapacity ?? "--"} ${OneAppString.of().passengers}";
        detailInfo!.luggage =
            "${rppVehicle?.luggage ?? "--"} ${OneAppString.of().bags}";
        detailInfo!.fuelCapacity =
            "${rppVehicle?.cityMpg ?? '-'}/${rppVehicle?.hWympg ?? '-'} ${OneAppString.of().eSTMPG}";
        detailInfo!.totalCharge = _getTotalCharge(
            commonResponse.response?.payload?.reservationQuote?.lineItems);
        detailInfo!.priceList = _getPriceList(
            commonResponse.response?.payload?.reservationQuote?.lineItems);
        detailInfo!.vehiclePicture = vehicle?.images?.first.url ??
            reservationDetail!
                .rppReservationDetails?.vehicleDetails?.largeImgUrl ??
            reservationDetail
                ?.rppReservationDetails?.vehicleDetails?.smallImgUrl;
        detailInfo!.businessHour =
            commonResponse.response?.payload?.station?.hours;
        detailInfo!.parseRppReservationDetails(
          commonResponse.response!.payload!.rppReservationDetails!,
        );
        if (detailInfo!.dealerDetailInfo == null) {
          List<dynamic>? latLons = commonResponse.response?.payload?.station
              ?.geofences?.first.polygon?.coordinates;
          num? lat = latLons?.first?.first?.last;
          num? lon = latLons?.first?.first?.first;
          final dealerLocation = FxLatLng(
              lat: lat?.toDouble() ?? 0.0, lon: lon?.toDouble() ?? 0.0);
          detailInfo!.dealerDetailInfo = DealerDetailInfo(
            timezone: commonResponse.response?.payload?.station?.timezone,
            locationId: commonResponse.response?.payload?.station?.id,
            locationName: commonResponse.response?.payload?.station?.name,
            phoneNumber:
                commonResponse.response?.payload?.station?.supportPhoneNumber,
            address:
                commonResponse.response?.payload?.station?.formattedAddress,
            distance: null,
            distanceValue: null,
            latLng: dealerLocation,
            travelTime: null,
            zipCode: null,
            totalVehicles:
                commonResponse.response?.payload?.station?.totalVehicles,
            stationDetail: commonResponse.response?.payload?.station,
            businessHours: commonResponse.response?.payload?.station?.hours,
            generalBusinessHours:
                commonResponse.response?.payload?.station?.generalBusinessHours,
          );
        }
      }

      FxAccountGlobal.getInstance().insurance =
          commonResponse.response?.payload?.customer?.insurance;
      FxAccountGlobal.getInstance().license =
          commonResponse.response?.payload?.customer?.license;
      FxAccountGlobal.getInstance().customersEntity =
          commonResponse.response?.payload?.customer;
      FxAccountGlobal.getInstance().email =
          commonResponse.response?.payload?.customer?.emailId ??
              commonResponse.response?.payload?.guestDriver?.emailAddress;
      FxAccountGlobal.getInstance().phone =
          commonResponse.response?.payload?.customer?.phoneNumber ??
              commonResponse.response?.payload?.guestDriver?.phoneNumber;

      primaryDriverInfo.phoneNumber =
          commonResponse.response?.payload?.guestDriver?.phoneNumber;
      primaryDriverInfo.email =
          commonResponse.response?.payload?.guestDriver?.emailAddress;
      primaryDriverInfo.firstName =
          commonResponse.response?.payload?.guestDriver?.firstName;
      primaryDriverInfo.lastName =
          commonResponse.response?.payload?.guestDriver?.lastName;
      primaryDriverInfo.bookingForSomeone =
          commonResponse.response?.payload?.guestDriver?.isEmptyContent() ==
              false;
      bool isBookingForSomeone = primaryDriverInfo.bookingForSomeone;
      primaryDriverInfo.provideAdditonalDriver =
          commonResponse.response?.payload?.guestDriver?.isEmptyContent() ==
              true;
      String userName =
          '${primaryDriverInfo.firstName ?? ""} ${primaryDriverInfo.lastName ?? ""}'
              .trim();
      String? firstName =
          FxAccountGlobal.getInstance().license?.firstName?.isNotEmpty == true
              ? FxAccountGlobal.getInstance().license?.firstName
              : (Global.getInstance().userFirstName ?? "");
      String? lastName =
          FxAccountGlobal.getInstance().license?.lastName?.isNotEmpty == true
              ? FxAccountGlobal.getInstance().license?.lastName
              : (Global.getInstance().userLastName ?? "");
      String myselfName = '$firstName $lastName'.trim();
      profileUI = ProfileUI(
        bookingForMyself: isBookingForSomeone != true,
        userName: isBookingForSomeone == true ? userName : myselfName,
        driverLicense: FxAccountGlobal.getInstance().license?.licenseNumber,
        insuranceName:
            FxAccountGlobal.getInstance().insurance?.insuranceCompanyName,
        someoneFirstName:
            isBookingForSomeone == true ? primaryDriverInfo.firstName : null,
        someoneLastName:
            isBookingForSomeone == true ? primaryDriverInfo.lastName : null,
        someoneEmail:
            isBookingForSomeone == true ? primaryDriverInfo.email : null,
        someonePhone:
            isBookingForSomeone == true ? primaryDriverInfo.phoneNumber : null,
      );
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_GET_RESERVATION_SUCCESS,
          category: LogCategory.FL_RENT);
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          AnalyticsEvent.FLEX_GET_RESERVATION_FAILURE,
          category: LogCategory.FL_RENT);
    }
    if (commonResponse.error?.errorMessage?.isNotEmpty == true) {
      _toastController
          .add(SummaryToast(commonResponse.error?.errorMessage, true));
    }
    return reservationDetail;
  }

  void updatePrimaryDriverInfo(PrimaryDriverInfo info) {
    primaryDriverInfo = info;
    Future(() {
      _driverDetailController.add(primaryDriverInfo);
    });
  }

  bool allowReserve() {
    if (!primaryDriverInfo.bookingForSomeone &&
        !primaryDriverInfo.provideAdditonalDriver &&
        !primaryDriverInfo.hasPrimaryDriver()) {
      return false;
    }
    return true;
  }

  void resetDropOffDate(
      {BuildContext? context, TimeData? timeData, int? modifiedBalance}) {
    if (timeData != null) {
      detailInfo?.dropOffDate = timeData.dropOffDate;
      detailInfo?.dropOffTime = timeData.dropOffTime;
      detailInfo?.pastDropTime = timeData.dropOffDateTime;
      isModifyComplete = true;
      startModify(true);
      refresh();
      refreshStatus(
          isEditMode: true,
          isEditSubmitEnable: true,
          modifiedBalance: modifiedBalance);
    }
  }

  void resetReservationVehicle(
      {BuildContext? context, Object? intentData, int? modifiedBalance}) {
    if (intentData != null && intentData is Map) {
      _reservationId = detailInfo?.reservationId;
      detailInfo = intentData['Reservation'];
      String? rppRateId = detailInfo?.rppPricingDetails?.rateId;
      String? signature = detailInfo?.rppPricingDetails?.signature;
      detailInfo?.reservationId = _reservationId;
      if (detailInfo?.rppPricingDetails == null) {
        detailInfo?.rppPricingDetails =
            RppPricingDetails(rateId: rppRateId, signature: signature);
      }
      if (intentData['RequestCode'] == RESET_DATE_REQUEST_CODE ||
          intentData['RequestCode'] == RESET_VEHICLE_REQUEST_CODE) {
        isModifyComplete = true;
      }
      startModify(true);
      refresh();
      refreshStatus(
          isEditMode: true,
          isEditSubmitEnable: true,
          modifiedBalance: modifiedBalance);
      // updateReservation(context, finishCurrentPage: false);
    }
  }

  FxReserveRequest _createReserveRequest(String? rentalType,
      {Customer? customer}) {
    final body = FxReserveRequest();
    body.referenceNumber = reservationDetail?.referenceNumber;
    body.organizationId = detailInfo!.organizationId ?? ORG_ID;
    body.destinationStationId = detailInfo!.destinationStationId;
    body.paymentMethod = selectedPaymentMethod ?? "";
    body.details = summaryUIStatus?.comment ?? "";
    body.originStationId = detailInfo!.originStationId;
    body.type = "RESERVATION";
    body.vehicleId = detailInfo!.vehicleId;
    body.pricingId = detailInfo!.pricingId;
    int dealerOffsetMin =
        detailInfo!.dealerDetailInfo?.getTimezoneMinutesOffset() ?? 0;
    final endDealerDateTime = _convertDealerTime(
        detailInfo?.pastDropTime ?? DateTime.now(), dealerOffsetMin);
    body.endsAt =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(endDealerDateTime);
    final startDealerDateTime = _convertDealerTime(
        detailInfo?.pastPickTime ?? DateTime.now(), dealerOffsetMin);
    body.startsAt =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(startDealerDateTime);
    body.vehicleClassCode = detailInfo!.vehicleClass!;
    body.vehicleModel = detailInfo
            ?.rppPricingDetails?.vehicleDetails?.vehicleModel ??
        reservationDetail?.rppReservationDetails?.vehicleDetails?.vehicleModel;
    body.guestFlag = customer?.guestFlag ?? primaryDriverInfo.bookingForSomeone;
    body.rentalType = rentalType?.toLowerCase() != 'null'
        ? rentalType
        : isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
            ? FLEX_RENTALS_EV_SWAP
            : FLEX_RENTALS_RENTAL;
    // body.vinBrand = vehicleItem?.brand ?? Global.getInstance().appBrand;
    body.rppRateId = detailInfo?.rppPricingDetails?.rateId ?? "";
    body.signature = detailInfo?.rppPricingDetails?.signature ?? "";

    if (customer != null) {
      body.customer = customer;
    } else {
      String? firstName =
          FxCommonUtils.createLicense().firstName?.isNotEmpty == true
              ? FxCommonUtils.createLicense().firstName
              : Global.getInstance().userFirstName;
      String? lastName =
          FxCommonUtils.createLicense().lastName?.isNotEmpty == true
              ? FxCommonUtils.createLicense().lastName
              : Global.getInstance().userLastName;
      String? emailId =
          FxAccountGlobal.getInstance().email ?? Global.getInstance().userEmail;
      String? phoneNumber =
          FxAccountGlobal.getInstance().phone ?? Global.getInstance().userPhone;

      body.customer = primaryDriverInfo.provideAdditonalDriver == true
          ? Customer(
              guid: Global.getInstance().guid,
              firstName: firstName,
              lastName: lastName,
              phoneNumber: phoneNumber,
              emailId: emailId,
              guestFlag: primaryDriverInfo.bookingForSomeone,
              insurance: primaryDriverInfo.bookingForSomeone == true
                  ? null
                  : FxCommonUtils.createInsurance(),
              license: primaryDriverInfo.bookingForSomeone == true
                  ? null
                  : FxCommonUtils.createLicense(),
            )
          : Customer(
              guid: Global.getInstance().guid,
              firstName: primaryDriverInfo.firstName ?? firstName,
              lastName: primaryDriverInfo.lastName ?? lastName,
              phoneNumber: primaryDriverInfo.phoneNumber ?? phoneNumber,
              emailId: primaryDriverInfo.email ?? emailId,
              guestFlag: primaryDriverInfo.bookingForSomeone,
              insurance: primaryDriverInfo.bookingForSomeone == true
                  ? null
                  : FxCommonUtils.createInsurance(),
              license: primaryDriverInfo.bookingForSomeone == true
                  ? null
                  : FxCommonUtils.createLicense(),
            );
    }
    return body;
  }

  _showMessageBottomSheet(BuildContext context, bool success, String message) {
    showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      builder: (context) {
        return FxMessageBottomView(
          success: success,
          message: message,
        );
      },
    );
  }

  FxReserveRequest _createCancelReserveRequest(String? runTimeRentalType) {
    final body = FxReserveRequest();
    body.claimedAt = null;
    body.status = "CANCELED";
    body.rppRateId = detailInfo!.rppPricingDetails?.rateId;
    body.signature = detailInfo!.rppPricingDetails?.signature;
    body.destinationStationId = detailInfo!.destinationStationId;
    body.originStationId = detailInfo!.originStationId;
    body.referenceNumber = reservationDetail?.referenceNumber;
    body.vehicleId = "";
    body.rentalType = runTimeRentalType;
    body.type = "";
    body.driverId = "";
    body.details = summaryUIStatus?.comment ?? "";
    body.pricingGroupIId = "";
    body.paymentMethod = "";
    body.organizationId = detailInfo!.organizationId ?? ORG_ID;
    body.vehicleClassCode = detailInfo?.vehicleClass;
    body.vehicleModel = detailInfo
            ?.rppPricingDetails?.vehicleDetails?.vehicleModel ??
        reservationDetail?.rppReservationDetails?.vehicleDetails?.vehicleModel;
    return body;
  }

  void createReservation(BuildContext context,
      {bool finishCurrentPage = true}) async {
    SummaryToast? apiToast;
    final body = _createReserveRequest('Null', customer: customer);
    // FxCFAICreateCustomerBody request = FxCFAICreateCustomerBody(
    //   driverRecords: FxCommonUtils.createDriverRecord(),
    //   insurance: FxCommonUtils.createInsurance(),
    //   license: FxCommonUtils.createLicense(),
    //   guid: Global.getInstance().guid,
    //   organizationId: detailInfo.organizationId ?? ORG_ID,
    //   programId: isCarShare ? CAR_SHARE_PROGRAM_ID : RENT_PROGRAM_ID,
    // );
    // CommonResponse<FxCfaiCustomersResponseEntity> customerResponse =
    //     await cfaiApi.sendUpdateCustomers(request);
    // if (customerResponse?.response != null) {
    await showProgress(context);
    final commonResponse = await cfaiApi.sendCreateFlexReserve(
        body,
        Global.getInstance().vin ?? "",
        vehicleItem?.brand ?? Global.getInstance().appBrand);
    await dismissProgress(context);
    if (commonResponse.error == null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_CREATE_RESERVATION_SUCCESS,
          category: LogCategory.FL_RENT);
      if (finishCurrentPage) {
        passToast = ToastMsgData(OneAppString.of().summaryReserveSuccess,
            success: true);
        FlexRouter.pushName(RoutePath.FX_DETAIL_LANDING);
        _showMessageBottomSheet(
            context, true, OneAppString.of().summaryReserveSuccess);
      }
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          AnalyticsEvent.FLEX_CREATE_RESERVATION_FAILURE,
          category: LogCategory.FL_RENT);
      apiToast = SummaryToast(OneAppString.of().summaryReserveFailed, true);
    }
    if (commonResponse.error?.errorMessage?.isNotEmpty == true) {
      _toastController
          .add(SummaryToast(commonResponse.error?.errorMessage, true));
    } else if (apiToast != null) {
      _toastController.add(apiToast);
    }
    // } else {
    //   _toastController
    //       .add(SummaryToast(customerResponse.error?.errorMessage, true));
    // }
  }

  void updateReservation(BuildContext context, String? runTimeRentalType,
      {bool finishCurrentPage = true}) async {
    if (_reservationId == null) return;
    SummaryToast? apiToast;
    await showProgress(context);
    final body = _createReserveRequest(runTimeRentalType, customer: customer);
    final commonResponse = await cfaiApi.sendUpdateReservationDetail(
        _reservationId ?? "",
        vehicleItem?.brand ?? Global.getInstance().appBrand,
        Global.getInstance().vin ?? "",
        body);
    await dismissProgress(context);
    if (commonResponse.error == null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_UPDATE_RESERVATION_SUCCESS,
          category: LogCategory.FL_RENT);
      if (finishCurrentPage) {
        passToast =
            ToastMsgData(OneAppString.of().summaryModifySuccess, success: true);
        FlexRouter.pushName(RoutePath.FX_DETAIL_LANDING);
      }
      FlexRouter.pushName(RoutePath.FX_DETAIL_LANDING);
      _showMessageBottomSheet(
          context, true, OneAppString.of().summaryModifySuccess);

      _checkEditable(pickTime: detailInfo?.pastPickTime);
      refreshStatus();
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          AnalyticsEvent.FLEX_UPDATE_RESERVATION_FAILURE,
          category: LogCategory.FL_RENT);
      apiToast = SummaryToast(OneAppString.of().summaryReserveFailed, true);
    }
    if (commonResponse.error?.errorMessage?.isNotEmpty == true) {
      _toastController
          .add(SummaryToast(commonResponse.error?.errorMessage, true));
    } else if (apiToast != null) {
      _toastController.add(apiToast);
    }
  }

  void cancelReservation(BuildContext context,
      [String? runTimeRentalType]) async {
    if (_reservationId == null) return;
    await showProgress(context);
    final body = _createCancelReserveRequest(runTimeRentalType);
    debugPrint("body: ${body.toJson().toString()}");
    final commonResponse = await cfaiApi.sendUpdateReservationDetail(
        _reservationId ?? "",
        vehicleItem?.brand ?? Global.getInstance().appBrand,
        Global.getInstance().vin ?? "",
        body);
    await dismissProgress(context);
    if (commonResponse.error == null) {
      passToast = ToastMsgData(OneAppString.of().reservationCancelSuccess,
          success: true);
      if (CustomNavigatorObserver.isPageExisting(RoutePath.FX_DETAIL_LANDING)) {
        FlexRouter.pushName(RoutePath.FX_DETAIL_LANDING);
      } else {
        goBackToDashboardNative();
      }
      _showMessageBottomSheet(
          context, true, OneAppString.of().reservationCancelSuccess);
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_CANCEL_RESERVATION_SUCCESS,
          category: LogCategory.FL_EVT);
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          AnalyticsEvent.FLEX_CANCEL_RESERVATION_FAILURE,
          category: LogCategory.FL_EVT);
    }
    if (commonResponse.error?.errorMessage?.isNotEmpty == true) {
      _toastController
          .add(SummaryToast(commonResponse.error?.errorMessage, true));
    }
  }

  Future searchDealers() async {
    if (await MapUtil.checkLocationGranted()) {
      await showProgress(NavigateService.context);
      var obtaionPosition = await MapUtil.getCurrentLocation();
      await cfaiApi.sendGetFlexStations(
        lat: obtaionPosition.lat.toDouble(),
        lon: obtaionPosition.lng.toDouble(),
        radius: searchLimitRadius,
        limitCount: null,
        vinBrand: vehicleItem?.brand ?? Global.getInstance().appBrand,
        rentalType: isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
            ? FLEX_RENTALS_EV_SWAP
            : FLEX_RENTALS_RENTAL,
        vin: vehicleItem?.vin ?? Global.getInstance().vin,
      );
      await dismissProgress(NavigateService.context);
    }
  }

  Future fetchVehiclesFromDates(IntentToSelectVehicle intentData) async {
    await showProgress(NavigateService.context);
    final timeData = intentData.timeData;
    int dealerTimezoneMinOffset =
        intentData.dealerDetailInfo!.getTimezoneMinutesOffset();
    DateTime requestPickUpDate =
        _convertDealerTime(timeData!.pickUpDateTime!, dealerTimezoneMinOffset);
    DateTime requestDropOffDate =
        _convertDealerTime(timeData.dropOffDateTime!, dealerTimezoneMinOffset);
    await cfaiApi.sendGetFlexStationPricing(
      intentData.stationId ?? "",
      intentData.orgID ?? ORG_ID,
      isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
          ? FLEX_RENTALS_EV_SWAP
          : FLEX_RENTALS_RENTAL,
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(requestPickUpDate),
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(requestDropOffDate),
      vehicleItem?.brand ?? Global.getInstance().appBrand,
      vehicleItem?.vin ?? Global.getInstance().vin,
      demoIndex: intentData.demoIndex,
    );
    await dismissProgress(NavigateService.context);
  }

  _getStationInfo(
      BuildContext context, FxCfaiReservationListItem reservation) async {
    DateTime pickUpDateTime = reservation.startsAt ?? DateTime.now();
    DateTime dropOffDateTime = reservation.endsAt ?? DateTime.now();
    int originStationTimeMinOffset =
        reservation.originStationDetails?.getTimezoneMinutesOffset() ?? 240;
    pickUpDateTime =
        _addDealerTimezone(pickUpDateTime, originStationTimeMinOffset);

    int destinationStationTimeMinOffset =
        reservation.destinationStationDetails?.getTimezoneMinutesOffset() ??
            240;
    dropOffDateTime =
        _addDealerTimezone(dropOffDateTime, destinationStationTimeMinOffset);

    detailInfo!.pickUpDate = DateFormat("MMM d").format(pickUpDateTime);
    detailInfo!.dropOffDate = DateFormat("MMM d").format(dropOffDateTime);
    detailInfo!.pickUpTime = isModify
        ? DateFormat("h:mma").format(pickUpDateTime).toLowerCase()
        : pickTime;
    detailInfo!.dropOffTime = isModify
        ? DateFormat("h:mma").format(dropOffDateTime).toLowerCase()
        : dropTime;
    detailInfo!.locationId = reservation.originStationId;
    isIn24Hours = pickUpDateTime.millisecondsSinceEpoch -
            DateTime.now().millisecondsSinceEpoch <
        (24 * 3600 * 1000);
    // detailInfo.pricePer = OneAppString.of()
    //     .flexFlutterSummaryValuePerUnit(
    //         it?.assignments?.first?.pricingRules?.first?.amount ?? "",
    //         pricePreUnit);
    detailInfo!.totalTaxes = "——";
    // detailInfo.fuelCapacity =
    //     fuelCapacity ?? reservationDetail?.vehicle?.fuelLevel;
    // detailInfo.seatNumber = seatNumber ??
    //     reservationDetail?.vehicle?.passengerCount?.toString();
    // detailInfo.luggage =
    //     luggage ?? reservationDetail?.vehicle?.luggageCount?.toString();
    // detailInfo.totalCharge = it?.assignments?.first?.price;
    // detailInfo.vehiclePicture = reservation?.vehicle?.images?.first?.url;
    detailInfo!.reservationId = reservation.id;
    detailInfo!.confirmationNumber = reservation.referenceNumber ?? "--";
    detailInfo!.pastPickTime = pickUpDateTime;
    detailInfo!.pastDropTime = dropOffDateTime;
    // detailInfo.claimedAt = reservation.claimedAt;
    // detailInfo.releasedAt = reservation.releasedAt;
    // detailInfo.vehicleId = reservation.vehicleId;
    // detailInfo.pricingGroupId = reservation.pricingGroupId;
    detailInfo!.originStationId = reservation.originStationId;
    detailInfo!.destinationStationId = reservation.destinationStationId;
    detailInfo!.driverId = Global.getInstance().guid;
    detailInfo!.details = reservation.details;
    detailInfo!.type = reservation.type;
    // showProgress(context);
    // final commonResponse =
    //     await cfaiApi.sendGetVehiclePricingInfo(reservation.originStationId,
    //         pickUpDateTime, dropOffDateTime);
    // dismissProgress(context);
    // if (commonResponse?.response?.payload?.pricingGroups != null) {
    //   final pricingGroup =
    //       commonResponse?.response?.payload?.pricingGroups;
    //   pricingGroup?.forEach((it) {
    //     if (it.id == reservation.pricingGroupId ||
    //         Global.getInstance().isDemo) {
    //       List<String> description = it?.description?.split(";");
    //       String vehicleModel;
    //       String seatNumber;
    //       String luggage;
    //       String fuelCapacity;
    //       String pricePreUnit;
    //       switch (it.assignments?.first?.pricingRules?.first?.type ?? "") {
    //         case "HOURLY_RATE":
    //           pricePreUnit = OneAppString.of().flexFlutterCommonHour;
    //           break;
    //         case "DAILY_RATE":
    //         default:
    //           pricePreUnit = OneAppString.of().flexFlutterCommonDay;
    //           break;
    //       }
    //       if (description?.isNotEmpty == true && description.length >= 4) {
    //         vehicleModel = description[0];
    //         seatNumber = description[1];
    //         fuelCapacity = description[2];
    //         luggage = description[3];
    //       }
    //       detailInfo.pricingId = it.assignments?.first?.id;
    //       detailInfo.organizationId = it.organizationId;
    //       // specialNotes: response?.notes,
    //       // dealerPhone: response?.location?.phoneNumber,
    //       // geo: Geo(reservation?.vehicle?.locationPayload?.latitude ?? "0.0",
    //       //     reservation?.vehicle?.locationPayload?.longitude ?? "0.0"),
    //       // _getReservationContract(context, reservation, it);
    //     }
    //   });
    //   refresh();
    // } else {
    //   FireBaseAnalyticsLogger.logEvent(
    //       AnalyticsEvent.FLEX_GET_RESERVATION_FAILURE,category : LogCategory.FL_RENT);
    //   if (commonResponse.error?.errorMessage?.isNotEmpty == true) {
    //     showBaseDialog(context, message: commonResponse.error?.errorMessage);
    //   }
    // }
    // if (commonResponse.error?.errorMessage?.isNotEmpty == true) {
    //   _toastController
    //       .add(SummaryToast(commonResponse.error?.errorMessage, true));
    // }
  }

  Future fetchRentalList() async {
    await showProgress(NavigateService.context);
    await cfaiApi.sendGetReservationsList(
        driverId: Global.getInstance().guid,
        rentalType: isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
            ? FLEX_RENTALS_EV_SWAP
            : FLEX_RENTALS_RENTAL,
        vinBrand: vehicleItem?.brand ?? Global.getInstance().appBrand,
        vin: Global.getInstance().vin ?? "");
    await dismissProgress(NavigateService.context);
  }

  // void _getReservationContract(
  //     BuildContext context,
  //     FxCfaiReservationListPayloadReservation reservation,
  //     FxCfaiGetVehiclePricePayloadPricingGroup pricingGroup) async {
  //   showProgress(context);
  //   final commonResponse =
  //       await cfaiApi.sendGetContactById(detailInfo.reservationId);
  //   dismissProgress(context);
  //   if (commonResponse?.response?.payload?.contract != null) {
  //     final contract = commonResponse?.response?.payload?.contract;
  //     contractId = contract.id;
  //   }
  // }

  void refresh() {
    refreshSummaryDetail();
    refreshDriverLicense();
    refreshInsurance();
    refreshCommitButton();
    checkLicenseExpire();
    refreshProfile();
  }

  void refreshProfile({ProfileUI? profile}) {
    if (profile != null) {
      profileUI = profile;
    } else if (profileUI == null &&
        FxAccountGlobal.getInstance().customersEntity != null) {
      String myselfName =
          '${Global.getInstance().userFirstName ?? ""} ${Global.getInstance().userLastName ?? ""}'
              .trim();
      final license = FxAccountGlobal.getInstance().license;
      if (license?.firstName?.isNotEmpty == true &&
          license?.lastName?.isNotEmpty == true) {
        myselfName = '${license?.firstName} ${license?.lastName}'.trim();
      }
      profileUI = ProfileUI(
        bookingForMyself: true,
        userName: myselfName,
        driverLicense: FxAccountGlobal.getInstance().license?.licenseNumber,
        insuranceName:
            FxAccountGlobal.getInstance().insurance?.insuranceCompanyName,
      );
    }
    _profileUICtl.add(profileUI);
  }

  void refreshDriverLicense() {
    driverLicenseInfo = FxAccountGlobal.getInstance().license;
    if (driverLicenseInfo != null) {
      if (driverLicenseInfo!.licenseDateOfExpiry != null &&
          DateTime.parse(driverLicenseInfo!.licenseDateOfExpiry!)
                  .millisecondsSinceEpoch >
              DateTime.now().millisecondsSinceEpoch) {
        driverLicenseError = null;
      } else if (FxAccountGlobal.getInstance().email?.isNotEmpty != true ||
          FxAccountGlobal.getInstance().phone?.isNotEmpty != true) {
        driverLicenseError = OneAppString.of().summaryAdd;
      } else {
        driverLicenseError = OneAppString.of().commonExpired;
      }
    } else {
      driverLicenseError = OneAppString.of().summaryAdd;
    }
    _driverLicenseController.sink.add(driverLicenseError);
  }

  void refreshCommitButton() {
    isCancel = false;
    if (CREATE_ACTION_KEY == status) {
      noteVisible = false;
      commitButtonTitle = OneAppString.of().commonReserve;
    } else if (status == OVERDUE_ACTION_KEY ||
        status == COMPLETED_ACTION_KEY ||
        status == CANCELLED_ACTION_KEY ||
        status == CANCELED_ACTION_KEY ||
        status == ABANDOND_ACTION_KEY) {
      btnVisible = false;
      noteVisible = false;
    } else if (UPCOMING_ACTION_KEY == status) {
      isCancel = true;
      commitButtonTitle = OneAppString.of().summaryToCalendar;
      activeStatus = false;
    } else if (IN_PROGRESS_ACTION_KEY == status) {
      commitButtonTitle = OneAppString.of().summaryToCalendar;
      btnVisible = isModifyComplete;
      activeStatus = true;
    } else if (READY_ACTION_KEY == status) {
      commitButtonTitle = OneAppString.of().summaryToCalendar;
      btnVisible = true;
    }
    if (isCarShare) {
      noteVisible = false;
    }
    _visibleController.sink.add(true);
  }

  void refreshInsurance() {
    insuranceInfo = FxAccountGlobal.getInstance().insurance;
    if (insuranceInfo != null) {
      if (insuranceInfo?.insuranceDateOfExpiry != null &&
          DateTime.parse(insuranceInfo!.insuranceDateOfExpiry!)
                  .millisecondsSinceEpoch >
              DateTime.now().millisecondsSinceEpoch) {
        insuranceError = null;
      } else {
        insuranceError = OneAppString.of().commonExpired;
      }
    } else {
      insuranceError = OneAppString.of().summaryAdd;
    }
    _insuranceController.sink.add(insuranceError);
  }

  void refreshStatus(
      {bool isEditMode = false,
      bool isEditSubmitEnable = false,
      String? comment,
      int? modifiedBalance}) {
    if (summaryUIStatus == null) {
      summaryUIStatus = SummaryUIStatus(
        isEditable: isCanEditable,
        isEditMode: isEditMode,
        isEditSubmitEnable: isEditSubmitEnable,
        isCreateReservation: _reservationId == null,
      );
    } else {
      summaryUIStatus!.isEditMode = isEditMode;
      summaryUIStatus!.isEditSubmitEnable = isEditSubmitEnable;
    }
    if (modifiedBalance != null) {
      summaryUIStatus!.modifiedBalance = modifiedBalance;
    }
    if (comment != null) {
      summaryUIStatus!.comment = comment;
    }
    if (summaryUIStatus!.comment == null) {
      summaryUIStatus!.comment = reservationDetail?.details;
    }
    _summaryEditStatusCtl.add(summaryUIStatus);
  }

  bool isCreated() {
    return driverLicenseInfo != null && insuranceInfo != null;
  }

  bool isForbidden() {
    return status == CREATE_ACTION_KEY || status == UPCOMING_ACTION_KEY;
  }

  bool isEditable() {
    switch (status) {
      case OVERDUE_ACTION_KEY:
      case COMPLETED_ACTION_KEY:
      case CANCELLED_ACTION_KEY:
      case CANCELED_ACTION_KEY:
      case ABANDOND_ACTION_KEY:
        return false;
      default:
        break;
    }
    return true;
  }

  IntentToDriverLicense getIntentToLicenseData() {
    return IntentToDriverLicense(
      insurance: insuranceInfo,
      license: driverLicenseInfo,
      emailId: FxAccountGlobal.getInstance().email,
      phoneNumber: FxAccountGlobal.getInstance().phone,
      isCreated: isCreated(),
      forbidModify: false,
      //isForbidden(),
      isEditable: isEditable(),
      programId: isCarShare ? CAR_SHARE_PROGRAM_ID : RENT_PROGRAM_ID,
      guid: Global.getInstance().guid,
      id: reservationDetail?.driverId,
      organizationId:
          reservationDetail?.organizationId ?? detailInfo?.organizationId,
    );
  }

  String getDisplayDate(String dateStr) {
    if (detailInfo?.pickUpDate != null) {
      final date = stringMonthWithDayToDate(dateStr);
      return date == null ? dateStr : formatDate(date, [M, " ", d]);
    }
    return OneAppString.of().commonNA;
  }

  String _getTotalCharge(List<LineItems>? lineItems) {
    if (lineItems?.isNotEmpty == true) {
      var total = 0;
      lineItems?.forEach((element) {
        total += element.amount!;
      });
      return OneAppString.of()
          .summaryChargesValue("\$ ${(total / 100).toStringAsFixed(2)}");
    }
    return OneAppString.of().commonNA;
  }

  List<PriceItem> _getPriceList(List<LineItems>? lineItems) {
    List<PriceItem> priceList = [];
    if (lineItems?.isNotEmpty == true) {
      lineItems!.forEach((element) {
        var item = PriceItem(
            description: element.description ?? "", value: element.price ?? "");
        priceList.add(item);
      });
    }
    return priceList;
  }

  String getTotalTaxes() {
    if (detailInfo?.totalTaxes?.isNotEmpty == true) {
      return OneAppString.of().summaryFuelChargeValue(detailInfo!.totalTaxes!);
    }
    return OneAppString.of().commonNA;
  }

  void refreshSummaryDetail() {
    if (detailInfo?.vehiclePicture == null ||
        detailInfo?.vehiclePicture?.isEmpty == true) {
      detailInfo?.vehiclePicture = reservationDetail
              ?.rppReservationDetails?.vehicleDetails?.largeImgUrl ??
          reservationDetail?.rppReservationDetails?.vehicleDetails?.smallImgUrl;
    }
    _detailInfoController.sink.add(detailInfo);
  }

  Future<void> checkCalendarPermission(BuildContext context) async {
    if (await Permission.calendarFullAccess.request().isGranted) {
      int? startTimestamp;
      if (detailInfo?.pastPickTime != null) {
        startTimestamp = detailInfo!.pastPickTime!.millisecondsSinceEpoch -
            DateTime.now().timeZoneOffset.inMilliseconds;
      }

      int? endTimestamp;
      if (detailInfo?.pastDropTime != null) {
        endTimestamp = detailInfo!.pastDropTime!.millisecondsSinceEpoch -
            DateTime.now().timeZoneOffset.inMilliseconds;
      }

      if (startTimestamp != null) {
        addCalendar.Event event = addCalendar.Event(
          title: detailInfo!.dealerDetailInfo?.locationName ?? "",
          location: detailInfo!.address!,
          description:
              "${detailInfo?.seatNumber ?? ''} \n${detailInfo?.fuelCapacity ?? ''} \n${detailInfo?.luggage ?? ''}",
          // timeZone: IO.Platform.isIOS ? null : detailInfo?.pastPickTime?.timeZoneName,
          startDate:
              DateTime.fromMillisecondsSinceEpoch(startTimestamp, isUtc: true),
          endDate: endTimestamp != null
              ? DateTime.fromMillisecondsSinceEpoch(endTimestamp, isUtc: true)
              : DateTime.fromMillisecondsSinceEpoch(startTimestamp, isUtc: true)
                  .add(Duration(minutes: 1)),
        );
        addCalendar.Add2Calendar.addEvent2Cal(event);
      }
    }
  }

  String? _parsingFeature(List<Features>? features, String name) {
    if (features != null && features.isNotEmpty) {
      return features.firstWhereOrNull((element) => element.icon == name)?.text;
    }
    return null;
  }

  String getDLDefaultText() {
    String result = OneAppString.of().driverLicenseNotSaved;
    if (reservationDetail != null) {
      if (reservationDetail!.status == UPCOMING_ACTION_KEY) {
        result = OneAppString.of().provideAtPickUp;
      } else if (reservationDetail!.status == CANCELED_ACTION_KEY ||
          reservationDetail!.status == CANCELLED_ACTION_KEY ||
          reservationDetail!.status == COMPLETED_ACTION_KEY) {
        result = OneAppString.of().driverLicenseNotSaved;
      }
    } else {
      result = OneAppString.of().driversLicense;
    }
    return result;
  }

  String getInsuranceDefaultText() {
    String result = OneAppString.of().insuranceNotSaved;
    if (reservationDetail != null) {
      if (reservationDetail!.status == UPCOMING_ACTION_KEY) {
        result = OneAppString.of().provideAtPickUp;
      } else if (reservationDetail!.status == CANCELED_ACTION_KEY ||
          reservationDetail!.status == CANCELLED_ACTION_KEY ||
          reservationDetail!.status == COMPLETED_ACTION_KEY) {
        result = OneAppString.of().insuranceNotSaved;
      }
    } else {
      result = OneAppString.of().registerInsurance;
    }
    return result;
  }

  @override
  void dispose() {
    _contractLinkController.close();
    _driverDetailController.close();
    _detailInfoController.close();
    _driverLicenseController.close();
    _insuranceController.close();
    _toastController.close();
    _visibleController.close();
    _isEditableCtl.close();
    _driverDetailController.close();
    _contractShowController.close();
    _reserveMode.close();
    _summaryEditStatusCtl.close();
    _profileUICtl.close();
    _routePathController.close();
  }
}

class SummaryToast {
  String? message;
  bool error = false;

  SummaryToast(this.message, this.error);
}

double getPricePerDay(RppPricingDetails? element) {
  double rateDayFinal = 0.0;
  if (element?.ratePeriod != null) {
    rateDayFinal =
        double.tryParse((element?.ratePeriod?.rate1PerDay ?? "0")) ?? 0.0;
    if (rateDayFinal == 0.0) {
      rateDayFinal =
          double.tryParse((element?.ratePeriod?.rate2PerDay ?? "0")) ?? 0.0;
    }
  }
  return rateDayFinal;
}

int getDayCount(RppPricingDetails? element) {
  return int.tryParse(element?.ratePeriod?.rate1Days ??
          (element?.ratePeriod?.rate2Days ?? '0')) ??
      0;
}

DateTime? stringMonthWithDayToDate(String monthWithDay) {
  DateTime? date;
  if (monthWithDay.isNotEmpty) {
    try {
      date = DateTime.parse("${DateTime.now().year}$monthWithDay");
    } on Exception catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_RENT);
      date = null;
    }
  }
  return date;
}

DateTime _convertDealerTime(
    DateTime dealerDateTime, int dealerTimezoneMinOffset) {
  return dealerDateTime.add(Duration(minutes: -dealerTimezoneMinOffset));
}

DateTime _addDealerTimezone(
    DateTime dealerDateTime, int dealerTimezoneMinOffset) {
  return dealerDateTime.add(Duration(minutes: dealerTimezoneMinOffset));
}
