// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/card/simple_card.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/reservation_detail_helper.dart';

// Project imports:
import 'fx_summary_detail_bloc.dart';

class CommentsWidget extends StatefulWidget {
  final FxSummaryDetailBloc? bloc;

  const CommentsWidget({Key? key, this.bloc}) : super(key: key);

  @override
  _CommentsWidgetState createState() => _CommentsWidgetState();
}

class _CommentsWidgetState extends State<CommentsWidget> {
  TextEditingController commentsTFController = TextEditingController();

  StreamSubscription<bool>? keyboardSubscription;

  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    final keyboardController = KeyboardVisibilityController();
    keyboardSubscription = keyboardController.onChange.listen((bool visible) {
      if (!visible) {
        widget.bloc?.refreshStatus(
          isEditMode: true,
          isEditSubmitEnable: true,
          comment: commentsTFController.text,
        );
        focusNode.unfocus();
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    keyboardSubscription?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return _commentsContent();
  }

  Widget _commentsContent() {
    return StreamBuilder(
      stream: widget.bloc?.uiSummaryStatus,
      initialData: widget.bloc?.summaryUIStatus,
      builder: (ctx, AsyncSnapshot<SummaryUIStatus?> snapData) {
        bool isCanEdit;
        isCanEdit = snapData.data?.isEditMode == true ||
            snapData.data?.isCreateReservation == true;
        String? textStr = snapData.data?.comment;
        if (textStr != null) {
          commentsTFController.value = TextEditingValue(
            text: textStr,
            selection: TextSelection.fromPosition(
              TextPosition(
                affinity: TextAffinity.downstream,
                offset: textStr.length,
              ),
            ),
          );
        }
        return commonSimpleCard(
            margin: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 0),
            child: Padding(
              padding: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 36.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    OneAppString.of().comments,
                    style: ThemeConfig.current().textStyleUtil.subHeadline2,
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Container(
                    height: 109.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(8.r)),
                      border: Border.all(
                          color: ThemeConfig.current().colorUtil.tertiary10,
                          width: 1.h),
                      color: isCanEdit
                          ? ThemeConfig.current().colorUtil.tertiary15
                          : ThemeConfig.current().colorUtil.tile02,
                    ),
                    child: TextField(
                      focusNode: focusNode,
                      scrollPadding: EdgeInsets.all(0),
                      controller: commentsTFController,
                      enabled: isCanEdit,
                      maxLines: 100,
                      minLines: 1,
                      textCapitalization: TextCapitalization.none,
                      keyboardType: TextInputType.multiline,
                      decoration: InputDecoration(
                        counterText: '',
                        alignLabelWithHint: false,
                        contentPadding: EdgeInsets.symmetric(
                            vertical: 16.h, horizontal: 16.w),
                        border: InputBorder.none,
                        hintText: OneAppString.of().anyAdditionalComment,
                        hintStyle: ThemeConfig.current()
                            .textStyleUtil
                            .body3
                            .copyWith(
                                color:
                                    ThemeConfig.current().colorUtil.tertiary07),
                      ),
                      style: ThemeConfig.current().textStyleUtil.body3.copyWith(
                          color: ThemeConfig.current().colorUtil.tertiary03),
                      cursorColor: ThemeConfig.current().colorUtil.tertiary07,
                    ),
                  ),
                ],
              ),
            ));
      },
    );
  }
}
