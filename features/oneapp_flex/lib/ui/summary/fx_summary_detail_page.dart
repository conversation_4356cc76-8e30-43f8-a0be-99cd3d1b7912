// Flutter imports:

// Dart imports:

// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:map_launcher/map_launcher.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/appbar/empty_appbar.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/card/simple_card.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';
import 'package:oneapp_common/widget/image/no_transparent_image.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_reserve_request_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/rent_car_info_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/reservation_detail_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/time_data_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';

// Project imports:
import '../../flex_global.dart';
import '../../route/router.dart';
import '../../util/fx_analytics_event.dart';
import '../../util/fx_common_util.dart';
import '../../widget/flex_slide_title_widget.dart';
import '../landing/fx_detail_landing_bloc.dart';
import 'comments_widget.dart';
import 'fx_summary_detail_bloc.dart';
import 'fx_summary_dialog_view.dart';

class FxSummaryDetailPage extends StatefulWidget {
  final Object? intentData;

  //final FxDetailLandingBloc? bloc;

  const FxSummaryDetailPage({Key? key, this.intentData}) : super(key: key);

  @override
  _FxSummaryDetailPageState createState() => _FxSummaryDetailPageState();
}

class _FxSummaryDetailPageState extends State<FxSummaryDetailPage> {
  final FxSummaryDetailBloc _bloc = FxSummaryDetailBloc();
  int evSwapCreditBalance = 0;
  int totalAvailableCredits = 0;
  int modifiedBalance = 0;

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logMarketingEvent(AnalyticsEvent.FLEX_SUMMARY_PAGE);
    _handleSummaryEvents();
    _bloc.loadData(context, widget.intentData ?? Object());
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      child: KeyboardDismissOnTap(
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: EmptyAppBar(
            backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
          ),
          body: StreamBuilder(
            stream: _bloc.uiDetailInfo,
            initialData: _bloc.detailInfo,
            builder: (ctx, AsyncSnapshot<RentCarInfo?> snapData) {
              final RentCarInfo? rentCarInfo = snapData.data;
              return ListView(
                padding: EdgeInsets.zero,
                children: [
                  FlexSlideTitle(
                    paddingBottom: 14.h,
                    title: OneAppString.of().reservation,
                    onBackPressed: () {
                      FlexRouter.popRoute();
                    },
                  ),
                  _timeContent(rentCarInfo),
                  _locationContent(
                    rentCarInfo,
                    rentCarInfo?.reservationId == null,
                  ),
                  _vehicleContent(rentCarInfo),
                  SizedBox(
                    height: 16.h,
                  ),
                  _userProfileContent(),
                  SizedBox(
                    height: 16.h,
                  ),
                  _invoiceSectionForRenatalsAndEvSwap(rentCarInfo),
                  CommentsWidget(
                    bloc: _bloc,
                  ),
                  _bottomTips(),
                  _bottomButtons(
                    status: rentCarInfo?.status,
                    isCreateReservation: rentCarInfo?.reservationId == null,
                  ),
                ],
              );
            },
          ),
        ),
      ),
      bloc: _bloc,
    );
  }

  _handleSummaryEvents() {
    _bloc.uiToast.listen((event) {
      showSimpleToast(event.error, event.message ?? "", durationSeconds: 7);
    });
    _bloc.uiRoutePath.listen((event) {
      if (event == RoutePath.FX_DETAIL_LANDING) {
        FBroadcast.instance().broadcast(REFRESH_RENTAL_LIST_NOTIFICATION);
      }
      Future(() {
        FlexRouter.popRouteUtil(event, result: _bloc.passToast);
      });
    });
  }

  Widget _bottomTips() {
    return StreamBuilder(
        stream: _bloc.uiSummaryStatus,
        initialData: _bloc.summaryUIStatus,
        builder: (ctx, AsyncSnapshot<SummaryUIStatus?> snapData) {
          final SummaryUIStatus? uiStatus = snapData.data;
          return Visibility(
            visible: true,
            child: Padding(
              padding: EdgeInsets.only(left: 32.w, right: 32.w, top: 32.h),
              child: RichText(
                  text: TextSpan(children: [
                TextSpan(
                  text: uiStatus?.isCreateReservation == true ||
                          uiStatus?.isEditMode == true
                      ? OneAppString.of().summaryDetailPageTipsCreateModify(
                          _bloc.vehicleItem?.brand?.toUpperCase() == "L"
                              ? OneAppString.of().lexusName
                              : OneAppString.of().toyotaName)
                      : OneAppString.of()
                          .summaryDetailPageTips(_bloc.vehicleItem?.brand == "L"
                              ? OneAppString.of().lexusName
                              : OneAppString.of().toyotaName)
                          .replaceAll("\n", " "),
                  style: ThemeConfig.current().textStyleUtil.caption1.copyWith(
                        color: ThemeConfig.current().colorUtil.tertiary05,
                        height: 1.5,
                      ),
                ),
                uiStatus?.isCreateReservation == true ||
                        uiStatus?.isEditMode == true
                    ? TextSpan(
                        text: OneAppString.of().summaryDetailPageViewMore,
                        style: ThemeConfig.current()
                            .textStyleUtil
                            .caption1
                            .copyWith(
                              color:
                                  ThemeConfig.current().colorUtil.secondary01,
                              height: 1.5,
                            ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            _showTermsAndConditinoViewMore();
                          })
                    : TextSpan()
              ])),
            ),
          );
        });
  }

  Widget _timeContent(RentCarInfo? rentCarInfo) {
    String? _strMMM;
    String? _dd;
    String? _pickUpTimeStr;
    String? _pickUpYearStr;
    String? _pickUpMMMM;
    String? _dropOffTimeStr;
    String? _dropOffYearStr;
    String? _dropOffMMMM;
    String? fromToTime;
    if (rentCarInfo != null && rentCarInfo.pastPickTime != null) {
      _strMMM =
          DateFormat('MMM').format(rentCarInfo.pastPickTime!).toUpperCase();
      _pickUpMMMM = DateFormat('MMMM').format(rentCarInfo.pastPickTime!);
      // _pickUpD = DateFormat('d').format(rentCarInfo.pastPickTime);
      _dd = DateFormat('dd').format(rentCarInfo.pastPickTime!);
      _pickUpYearStr = DateFormat('yyyy').format(rentCarInfo.pastPickTime!);
      _pickUpTimeStr =
          DateFormat('MMMM d, yyyy').format(rentCarInfo.pastPickTime!);
      // _pickUpMMMMddStr = DateFormat('MMMM d').format(rentCarInfo.pastPickTime);
    }
    if (rentCarInfo != null && rentCarInfo.pastDropTime != null) {
      _dropOffMMMM = DateFormat('MMMM').format(rentCarInfo.pastDropTime!);
      // _dropOffD = DateFormat('d').format(rentCarInfo.pastDropTime);
      _dropOffTimeStr =
          DateFormat('MMMM d, yyyy').format(rentCarInfo.pastDropTime!);
      _dropOffYearStr = DateFormat('yyyy').format(rentCarInfo.pastDropTime!);
    }
    if (_pickUpTimeStr != null && _dropOffTimeStr != null) {
      if (_pickUpMMMM == _dropOffMMMM) {
        _dropOffTimeStr = _dropOffTimeStr.replaceAll(_dropOffMMMM ?? "", '');
      }
      if (_pickUpYearStr == _dropOffYearStr) {
        _pickUpTimeStr = _pickUpTimeStr
            .replaceAll(_dropOffYearStr ?? "", '')
            .replaceAll(',', '');
      }
      fromToTime = '${_pickUpTimeStr.trim()}-${_dropOffTimeStr.trim()}';
    }

    // String _MMM, String _dd, String fromToTime, bool isEditable
    return Align(
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 90.w,
            height: 64.w,
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    width: 46.w,
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: ThemeConfig.current().colorUtil.secondary01,
                        width: 2.w,
                      ),
                      color: ThemeConfig.current().colorUtil.tile01,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 18.w,
                          color: ThemeConfig.current().colorUtil.secondary01,
                          alignment: Alignment.center,
                          child: Text(
                            _strMMM ?? '--',
                            style: ThemeConfig.current()
                                .textStyleUtil
                                .tabLabel01
                                .copyWith(
                                    color: ThemeConfig.current()
                                        .colorUtil
                                        .button03a),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Container(
                          height: 28.h,
                          alignment: Alignment.center,
                          child: Text(
                            _dd ?? '--',
                            style: ThemeConfig.current()
                                .textStyleUtil
                                .subHeadline4
                                .copyWith(
                                    color: ThemeConfig.current()
                                        .colorUtil
                                        .tertiary03),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                (_bloc.reservationDetail?.status == CANCELLED_ACTION_KEY ||
                        _bloc.reservationDetail?.status ==
                            CANCELED_ACTION_KEY ||
                        _bloc.reservationDetail?.status ==
                            COMPLETED_ACTION_KEY ||
                        _bloc.reservationDetail?.status ==
                            IN_PROGRESS_ACTION_KEY)
                    ? Container()
                    : Align(
                        alignment: Alignment.topRight,
                        child: _editButton(
                          isWhite: false,
                          showAddEdit: _bloc.reservationDetail != null,
                          onClick: (isAddCalendar) {
                            if (_bloc.reservationDetail != null &&
                                isAddCalendar) {
                              _bloc.checkCalendarPermission(context);
                            } else {
                              _showEditDateTimeDialog();
                            }
                          },
                        ),
                      )
              ],
            ),
          ),
          SizedBox(
            height: 8.h,
          ),
          Text(
            fromToTime ?? '',
            style: ThemeConfig.current().textStyleUtil.body4,
          ),
        ],
      ),
    );
  }

  Widget _locationContent(RentCarInfo? rentCarInfo, bool isEditable) {
    return Container(
      clipBehavior: Clip.antiAlias,
      margin: EdgeInsets.fromLTRB(16.w, 32.h, 16.w, 16.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 12,
            offset: Offset(0, 1),
          ),
        ],
        color: ThemeConfig.current().colorUtil.tile01,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              double? lat = _bloc.detailInfo?.dealerDetailInfo?.latLng?.lat;
              double? lon = _bloc.detailInfo?.dealerDetailInfo?.latLng?.lon;
              String? title = _bloc.detailInfo?.dealerDetailInfo?.locationName;
              if (lat != null && lon != null && title != null) {
                _openStationOnMap(lat, lon, title);
              }
            },
            child: Padding(
              padding: EdgeInsets.fromLTRB(12.w, 12.h, 16.w, 12.h),
              child: Row(
                children: [
                  SizedBox(
                    height: 48.w,
                    width: 48.w,
                    child: CommonCircleIconImage(
                      'packages/oneapp_common/res/image/svg/toyota/ic_flex_detail_location.svg',
                      iconHeight: 24.w,
                      iconWidth: 24.w,
                      circleBackgroundColor:
                          ThemeConfig.current().colorUtil.secondary02,
                      iconTintColor:
                          ThemeConfig.current().colorUtil.secondary01,
                    ),
                  ),
                  SizedBox(
                    width: 12.w,
                  ),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                rentCarInfo?.locationName ?? '',
                                style:
                                    ThemeConfig.current().textStyleUtil.body4,
                              ),
                            ),
                            _editButton(
                              isWhite: false,
                              isDealer: true,
                              onClick: (_) {
                                _showEditDealerDialog();
                              },
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 2.h,
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                rentCarInfo?.address ?? '',
                                style: ThemeConfig.current()
                                    .textStyleUtil
                                    .callout1
                                    .copyWith(
                                      color: ThemeConfig.current()
                                          .colorUtil
                                          .tertiary05,
                                    ),
                              ),
                            ),
                            SizedBox(
                              width: 24.w,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // SizedBox(
                  //   width: 8.w,
                  // ),
                ],
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              if (_bloc.detailInfo?.dealerPhone?.isNotEmpty == true) {
                FireBaseAnalyticsLogger.logMarketingEvent(
                    AnalyticsEvent.FLEX_CALL_DEALER);
              }
              dialerLauncher(_bloc.detailInfo?.dealerPhone ?? "");
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: 9.h, bottom: 7.h),
              color: ThemeConfig.current().colorUtil.tile05,
              child: Row(
                children: [
                  SizedBox(
                    width: 70.w,
                  ),
                  SvgPicture.asset(
                    'packages/oneapp_common/res/image/svg/toyota/ic_phone_small.svg',
                    width: 24.w,
                    height: 24.w,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    OneAppString.of().callDealership,
                    style: ThemeConfig.current().textStyleUtil.callout2,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _vehicleContent(RentCarInfo? rentCarInfo) {
    String? reservationNo = rentCarInfo?.confirmationNumber;
    String? model = rentCarInfo?.model;
    String vehicleImageUrl = rentCarInfo?.vehiclePicture ?? "";
    return commonSimpleCard(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Container(
        margin:
            EdgeInsets.only(left: 16.w, top: 12.h, right: 16.w, bottom: 11.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    OneAppString.of().vehicle,
                    style: ThemeConfig.current().textStyleUtil.subHeadline2,
                  ),
                ),
                SizedBox(
                  width: 17.w,
                ),
                _editButton(
                  isWhite: true,
                  onClick: (_) async {
                    final data = IntentToSelectVehicle(
                      status: _bloc.detailInfo?.status,
                      reservationId: _bloc.detailInfo?.reservationId,
                      orgID: _bloc.detailInfo?.organizationId,
                      phone: _bloc.detailInfo?.dealerPhone,
                      resetVehicle: RESET_VEHICLE_REQUEST_CODE,
                      stationId: _bloc.detailInfo?.originStationId,
                      dealerId: _bloc.detailInfo?.locationId,
                      businessHour: _bloc.detailInfo?.businessHour,
                      timeData: TimeData(
                        pickUpDate: _bloc.detailInfo?.pickUpDate,
                        dropOffDate: _bloc.detailInfo?.dropOffDate,
                        pickUpTime: _bloc.detailInfo?.pickUpTime,
                        dropOffTime: _bloc.detailInfo?.dropOffTime,
                        pickUpDateTime: _bloc.detailInfo?.pastPickTime,
                        dropOffDateTime: _bloc.detailInfo?.pastDropTime,
                      ),
                      locationName: _bloc.detailInfo?.locationName,
                      formattedAddress: _bloc.detailInfo?.address,
                      dealerDetailInfo: _bloc.detailInfo?.dealerDetailInfo,
                      comeFrom: comeFromEditVehicle,
                    );
                    await _bloc.fetchVehiclesFromDates(data);
                    FxCommonUtils.showSelectVehiclePage(data: data)
                        .then((value) {
                      if (value is Map && value.containsKey('result')) {
                        _bloc.resetReservationVehicle(
                            context: context, intentData: value['result']);
                      } else {
                        _bloc.refreshStatus(
                            isEditMode: true, isEditSubmitEnable: true);
                        _bloc.resetReservationVehicle(
                            context: context, intentData: value);
                      }
                    });
                  },
                ),
              ],
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 2.h,
                      ),
                      _commonSimilar(model),
                      SizedBox(
                        height: 8.h,
                      ),
                      _vehicleContentStatusTime(rentCarInfo),
                      SizedBox(
                        height: 5.h,
                      ),
                      reservationNo != null
                          ? _commonContainerImageText(
                              'packages/oneapp_common/res/image/svg/toyota/ic_flex_landing_order.svg',
                              reservationNo,
                            )
                          : Container(),
                    ],
                  ),
                ),
                Container(
                  width: 140,
                  child: NoTransparentNetworkImage(
                    vehicleImageUrl,
                    placeHolder: carPlaceHolderImage,
                    fit: BoxFit.fitWidth,
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _vehicleContentStatusTime(RentCarInfo? rentCarInfo) {
    if (rentCarInfo == null) {
      return Container();
    }
    String? status = rentCarInfo.status;
    String statusDisplay = _getStatusDisplay(status);
    String pickUpDisplayTime =
        '${OneAppString.of().pickUp} • ${rentCarInfo.pickUpTime?.replaceAll(' ', '').toLowerCase() ?? "--"}';
    String dropOffDisplayTime =
        '${OneAppString.of().dropOff} • ${rentCarInfo.dropOffTime?.replaceAll(' ', '').toLowerCase() ?? "--"}';
    bool showStatus = status == CANCELLED_ACTION_KEY ||
        status == CANCELED_ACTION_KEY ||
        status == COMPLETED_ACTION_KEY;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Visibility(
          visible: showStatus,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 5.h,
              ),
              _commonContainerImageText(
                _getStatusImagePath(status),
                statusDisplay,
                // rotateDegree: 180,
              ),
            ],
          ),
        ),
        Visibility(
          visible: showStatus == false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 5.h,
              ),
              _commonContainerImageText(
                'packages/oneapp_common/res/image/svg/toyota/ic_flex_detail_clock.svg',
                pickUpDisplayTime,
              ),
              SizedBox(
                height: 5.h,
              ),
              _commonContainerImageText(
                'packages/oneapp_common/res/image/svg/toyota/ic_flex_drop_off_clock.svg',
                dropOffDisplayTime,
                // rotateDegree: 180,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _userProfileContent() {
    return StreamBuilder(
      stream: _bloc.uiProfile,
      builder: (ctx, AsyncSnapshot<ProfileUI?> snapData) {
        final ProfileUI? profileUI = snapData.data;
        String? userName;
        String? driverLicense;
        String? insuranceName;
        final account = FxAccountGlobal.getInstance().customersEntity;
        if (profileUI != null) {
          userName = profileUI.userName ??
              '${account?.firstName ?? ""} ${account?.lastName ?? ""}';
          driverLicense = profileUI.bookingForMyself == true
              ? profileUI.driverLicense
              : null;
          insuranceName = profileUI.bookingForMyself == true
              ? profileUI.insuranceName
              : null;
        } else {
          driverLicense = account?.license?.licenseNumber;
          insuranceName = account?.insurance?.insuranceCompanyName;
        }
        String insuranceDefaultText = _bloc.getInsuranceDefaultText();
        String licenseDefaultText = _bloc.getDLDefaultText();
        bool showWarning = false;
        if (profileUI != null) {
          if (profileUI.bookingForMyself == true) {
            showWarning = _bloc.reservationDetail == null &&
                (userName == null || userName.trim().isEmpty);
          } else {
            showWarning = profileUI.someoneFirstName == null ||
                profileUI.someoneLastName == null;
          }
        }

        if (_bloc.isLicenseExpired && driverLicense?.isNotEmpty == true) {
          driverLicense = OneAppString.of().expiredLicense;
        }
        if (_bloc.isInsuranceExpired && insuranceName?.isNotEmpty == true) {
          insuranceName = OneAppString.of().expiredInsurance;
        }

        return commonSimpleCard(
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          child: Container(
            margin: EdgeInsets.only(
                left: 16.w, top: 12.h, right: 16.w, bottom: 0.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        OneAppString.of().driver,
                        style: ThemeConfig.current().textStyleUtil.subHeadline2,
                      ),
                    ),
                    SizedBox(
                      width: 17.w,
                    ),
                    _editButton(
                      isWhite: true,
                      showWarning: showWarning,
                      onClick: (_) async {
                        Customer? customer = await (FlexRouter.pushName(
                          RoutePath.FX_DRIVER_INFO,
                          arguments: {
                            'CustomerEntity':
                                FxAccountGlobal.getInstance().customersEntity,
                            'BookingForMyself': profileUI?.bookingForMyself,
                            'ProfileUI': profileUI,
                          },
                        ));
                        _bloc.checkLicenseExpire();
                        if (customer == null) {
                          customer =
                              FxAccountGlobal.getInstance().requestCustomer;
                        }
                        if (customer != null) {
                          _bloc.customer = customer;
                          String userName;
                          if (customer.guestFlag == true) {
                            userName =
                                '${customer.firstName ?? ""} ${customer.lastName ?? ""}'
                                    .trim();
                          } else {
                            String? licenseFirstName =
                                customer.license?.firstName;
                            String? licenseLastName =
                                customer.license?.lastName;
                            userName =
                                '${licenseFirstName?.isNotEmpty == true ? licenseFirstName : (Global.getInstance().userFirstName ?? (customer.firstName ?? ""))} ${licenseLastName?.isNotEmpty == true ? licenseLastName : (Global.getInstance().userLastName ?? (customer.lastName ?? ""))}'
                                    .trim();
                          }
                          _bloc.refreshProfile(
                            profile: ProfileUI(
                              userName: userName,
                              driverLicense: customer.license?.licenseNumber,
                              insuranceName:
                                  customer.insurance?.insurancePolicyNumber,
                              bookingForMyself: customer.guestFlag != true,
                              someoneFirstName: customer.guestFlag == true
                                  ? customer.firstName
                                  : null,
                              someoneLastName: customer.guestFlag == true
                                  ? customer.lastName
                                  : null,
                              someoneEmail: customer.guestFlag == true
                                  ? customer.emailId
                                  : null,
                              someonePhone: customer.guestFlag == true
                                  ? customer.phoneNumber
                                  : null,
                            ),
                          );
                          _bloc.refreshStatus(
                              isEditMode: true, isEditSubmitEnable: true);
                        }
                      },
                    ),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 2.h,
                          ),
                          _commonSimilar(
                            userName?.trim().isNotEmpty == true
                                ? OneAppString.of().hereTheDriverInfo
                                : OneAppString.of().addDriverInfo,
                          ),
                          SizedBox(
                            height: 13.h,
                          ),
                          _commonContainerImageText(
                            'packages/oneapp_common/res/image/svg/toyota/ic_flex_profile.svg',
                            userName,
                            defaultText: profileUI != null
                                ? OneAppString.of().whoTheDriver
                                : '--',
                          ),
                          SizedBox(
                            height: 5.h,
                          ),
                          _commonContainerImageText(
                            'packages/oneapp_common/res/image/svg/toyota/ic_flex_landing_vehicle.svg',
                            driverLicense,
                            defaultText: licenseDefaultText,
                          ),
                          SizedBox(
                            height: 5.h,
                          ),
                          _commonContainerImageText(
                            'packages/oneapp_common/res/image/svg/toyota/ic_flex_landing_order.svg',
                            insuranceName,
                            defaultText: insuranceDefaultText,
                          ),
                          SizedBox(
                            height: 15.h,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 20.w,
                    ),
                    Padding(
                      padding: EdgeInsets.only(bottom: 18.h),
                      child: SvgPicture.asset(
                        'packages/oneapp_common/res/image/svg/toyota/ic_flex_user_photo.svg',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _invoiceSectionForRenatalsAndEvSwap(RentCarInfo? rentCarInfo) {
    if (rentCarInfo != null) {
      if (isFeatureEnabled(FLEX_EV_SWAP, _bloc.vehicleItem?.features)) {
        return _invoiceContent(rentCarInfo);
      } else {
        return _invoiceRentalsContent(rentCarInfo);
      }
    }
    return Container();
  }

  Widget _invoiceRentalsContent(RentCarInfo rentCarInfo) {
    double pricePerDay = getPricePerDay(rentCarInfo.rppPricingDetails);
    String? fuelPrice =
        rentCarInfo.rppReservationDetails?.pricingDetails?.fuelCharge?.amount;
    int totalDayAmount = getDayCount(rentCarInfo.rppPricingDetails);
    String? taxPrice = rentCarInfo.rppPricingDetails?.totalPricing?.totalTaxes;
    String? totalPrice =
        rentCarInfo.rppPricingDetails?.totalPricing?.totalCharges;
    double daysPrice = pricePerDay * totalDayAmount;
    String dayCountString = totalDayAmount > 1
        ? '${OneAppString.of().dailyRate} • $totalDayAmount ${OneAppString.of().lowCapDays}'
        : '${OneAppString.of().dailyRate} • $totalDayAmount ${OneAppString.of().commonDay}';
    return commonSimpleCard(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Container(
        margin: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 17.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    OneAppString.of().invoice,
                    style: ThemeConfig.current().textStyleUtil.subHeadline2,
                  ),
                ),
                SizedBox(
                  width: 17.w,
                ),
                Text(
                  // ignore: unnecessary_null_comparison
                  pricePerDay != null
                      ? isFeatureEnabled(
                              FLEX_EV_SWAP, _bloc.vehicleItem?.features)
                          ? OneAppString.of().xPricePerDay(
                              '\$${pricePerDay.toStringAsFixed(2)}')
                          : OneAppString.of().xPricePerReserveDay(
                              '\$${pricePerDay.toStringAsFixed(2)}')
                      : '',
                  style: ThemeConfig.current().textStyleUtil.body4,
                ),
              ],
            ),
            SizedBox(
              height: 2.h,
            ),
            _commonSimilar(
              rentCarInfo.status == COMPLETED_ACTION_KEY
                  ? OneAppString.of().rates
                  : OneAppString.of().estimatedRates,
            ),
            SizedBox(
              height: 12.h,
            ),
            _commonContainerImageText2(
              'packages/oneapp_common/res/image/svg/toyota/ic_flex_light.svg',
              dayCountString,
              '\$${daysPrice.toStringAsFixed(2)}',
            ),
            Visibility(
              visible: fuelPrice?.isNotEmpty == true,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 4.h,
                  ),
                  _commonContainerImageText2(
                    'packages/oneapp_common/res/image/svg/toyota/ic_flex_mpg.svg',
                    OneAppString.of().fuel,
                    fuelPrice != null ? '\$$fuelPrice' : '--',
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 4.h,
            ),
            _commonContainerImageText2(
              'packages/oneapp_common/res/image/svg/toyota/ic_flex_dollar.svg',
              OneAppString.of().tax,
              taxPrice != null ? '\$$taxPrice' : '--',
            ),
            SizedBox(
              height: 16.h,
            ),
            Container(
              height: 1.h,
              color: Color(0xFFDEDEDE),
            ),
            SizedBox(
              height: 17.h,
            ),
            Row(
              children: [
                Expanded(
                  child: Text(
                    rentCarInfo.status == COMPLETED_ACTION_KEY
                        ? OneAppString.of().total
                        : OneAppString.of().estimateTotal,
                    style: ThemeConfig.current().textStyleUtil.body4,
                  ),
                ),
                SizedBox(
                  width: 16.w,
                ),
                Text(
                  totalPrice != null ? '\$$totalPrice' : '--',
                  style: ThemeConfig.current().textStyleUtil.body4,
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _invoiceContent(RentCarInfo rentCarInfo) {
    String? runTimeRentalType = _bloc.reservationDetail?.rentalType;
    String? rentalType;
    if (runTimeRentalType != null) {
      rentalType = runTimeRentalType;
    } else {
      rentalType = isFeatureEnabled(FLEX_EV_SWAP, _bloc.vehicleItem?.features)
          ? FLEX_RENTALS_EV_SWAP
          : runTimeRentalType == null
              ? FLEX_RENTALS_RENTAL
              : runTimeRentalType;
    }
    //Rental Type
    double pricePerDay = getPricePerDay(rentCarInfo.rppPricingDetails);
    String? fuelPrice =
        rentCarInfo.rppReservationDetails?.pricingDetails?.fuelCharge?.amount;
    int totalDayAmount = getDayCount(rentCarInfo.rppPricingDetails);
    String? taxPrice = rentCarInfo.rppPricingDetails?.totalPricing?.totalTaxes;
    String? totalPrice =
        rentCarInfo.rppPricingDetails?.totalPricing?.totalCharges;
    double daysPrice = pricePerDay * totalDayAmount;
    String dayCountString = totalDayAmount > 1
        ? '${OneAppString.of().dailyRate} • $totalDayAmount ${OneAppString.of().lowCapDays}'
        : '${OneAppString.of().dailyRate} • $totalDayAmount ${OneAppString.of().commonDay}';

    // EV Swap Type
    double evSwapPricePerDay = 1;
    int evSwapTotalDayAmount = getDayCount(rentCarInfo.rppPricingDetails);
    double evSwapDaysPrice = evSwapPricePerDay * evSwapTotalDayAmount;
    String? evSwapTotalPrice =
        '${evSwapDaysPrice.round()} ${evSwapDaysPrice > 1 ? OneAppString.of().lowCapCredits : OneAppString.of().lowCapCredit}';
    String evSwapDayCountString = evSwapTotalDayAmount > 1
        ? '${OneAppString.of().dailyRate} • $evSwapTotalDayAmount ${OneAppString.of().lowCapDays}'
        : '${OneAppString.of().dailyRate} • $evSwapTotalDayAmount ${OneAppString.of().commonDay}';

    return StreamBuilder(
        stream: _bloc.uiSummaryStatus,
        builder: (ctx, snapData) {
          final SummaryUIStatus? uiStatus = snapData.data;
          final item = _bloc.detailInfo ?? null;
          modifiedBalance = uiStatus?.modifiedBalance ?? 0;

          return commonSimpleCard(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            child: StreamBuilder(
                stream: _bloc.evRemainingBalanceCredits,
                builder: (context, snapBalancedata) {
                  if (snapBalancedata.data != null) {
                    var localevSwapCreditBalance = snapBalancedata.data as int;
                    if (uiStatus?.isCreateReservation == true) {
                      modifiedBalance =
                          localevSwapCreditBalance - evSwapTotalDayAmount;
                    }
                    if (uiStatus?.isEditMode == true &&
                        uiStatus?.isEditSubmitEnable == false) {
                      totalAvailableCredits =
                          localevSwapCreditBalance + evSwapTotalDayAmount;
                    }
                    if (uiStatus?.isCreateReservation == false &&
                        uiStatus?.isEditMode == true &&
                        uiStatus?.isEditSubmitEnable == true) {
                      modifiedBalance =
                          totalAvailableCredits - evSwapTotalDayAmount;
                    }
                    return Container(
                      margin: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 17.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  OneAppString.of().invoice,
                                  style: ThemeConfig.current()
                                      .textStyleUtil
                                      .subHeadline2,
                                ),
                              ),
                              SizedBox(
                                width: 17.w,
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 2.h,
                          ),
                          _commonSimilar(
                            rentCarInfo.status == COMPLETED_ACTION_KEY
                                ? OneAppString.of().rates
                                : OneAppString.of().estimatedRates,
                          ),
                          SizedBox(
                            height: 12.h,
                          ),
                          rentalType == 'RENTAL'
                              ? _commonContainerImageText2(
                                  fxLightIcon,
                                  dayCountString,
                                  '\$${daysPrice.toStringAsFixed(2)}',
                                )
                              : _commonContainerImageText2(
                                  fxLightIcon,
                                  evSwapDayCountString,
                                  '${evSwapDaysPrice.round()} ${evSwapDaysPrice > 1 ? OneAppString.of().lowCapCredits : OneAppString.of().lowCapCredit}',
                                ),
                          Visibility(
                            visible: fuelPrice?.isNotEmpty == true,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  height: 4.h,
                                ),
                                _commonContainerImageText2(
                                  'packages/oneapp_common/res/image/svg/toyota/ic_flex_mpg.svg',
                                  OneAppString.of().fuel,
                                  fuelPrice != null ? '\$$fuelPrice' : '--',
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 4.h,
                          ),
                          rentalType == 'RENTAL'
                              ? _commonContainerImageText2(
                                  'packages/oneapp_common/res/image/svg/toyota/ic_flex_dollar.svg',
                                  OneAppString.of().tax,
                                  taxPrice != null ? '\$$taxPrice' : '--',
                                )
                              : Container(),
                          SizedBox(
                            height: 16.h,
                          ),
                          Container(
                            height: 1.h,
                            color: Color(0xFFDEDEDE),
                          ),
                          SizedBox(
                            height: 17.h,
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  rentCarInfo.status == COMPLETED_ACTION_KEY
                                      ? OneAppString.of().total
                                      : OneAppString.of().estimateTotal,
                                  style:
                                      ThemeConfig.current().textStyleUtil.body4,
                                ),
                              ),
                              SizedBox(
                                width: 16.w,
                              ),
                              rentalType == 'RENTAL'
                                  ? Text(
                                      totalPrice != null
                                          ? '\$$totalPrice'
                                          : '--',
                                      style: ThemeConfig.current()
                                          .textStyleUtil
                                          .body4,
                                    )
                                  : Text(
                                      // ignore: unnecessary_null_comparison
                                      evSwapTotalPrice != null
                                          ? '$evSwapTotalPrice'
                                          : '--',
                                      style: ThemeConfig.current()
                                          .textStyleUtil
                                          .body4,
                                    )
                            ],
                          ),
                          SizedBox(
                            height: 16.h,
                          ),
                          (rentalType == 'EV_SWAP' &&
                                  (item?.status == UPCOMING_ACTION_KEY ||
                                      item?.status == CREATE_ACTION_KEY))
                              ? Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        OneAppString.of().estRemainingBalance,
                                        style: ThemeConfig.current()
                                            .textStyleUtil
                                            .callout1,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 8.w,
                                    ),
                                    Text(
                                      '${uiStatus?.isCreateReservation == true || uiStatus?.isEditSubmitEnable == true ? modifiedBalance : localevSwapCreditBalance} ${localevSwapCreditBalance > 1 ? OneAppString.of().lowCapCredits : OneAppString.of().lowCapCredit}',
                                      style: ThemeConfig.current()
                                          .textStyleUtil
                                          .body4,
                                    )
                                  ],
                                )
                              : Container(),
                        ],
                      ),
                    );
                  } else {
                    return Container();
                  }
                }),
          );
        });
  }

  Widget _bottomButtons({String? status, bool isCreateReservation = true}) {
    return StreamBuilder(
      stream: _bloc.uiSummaryStatus,
      builder: (ctx, snapData) {
        final SummaryUIStatus? uiStatus = snapData.data;
        final List<Widget> widgets = [];
        if (uiStatus?.isCreateReservation == true) {
          widgets.add(
            SizedBox(
              width: 192.w,
              height: 52.h,
              child: CustomDefaultButton(
                backgroundColor: ThemeConfig.current().colorUtil.button01b,
                buttonTextColor: ThemeConfig.current().colorUtil.button01a,
                borderColor: ThemeConfig.current().colorUtil.button01b,
                disabledBackgroundColor:
                    ThemeConfig.current().colorUtil.button02c,
                text: OneAppString.of().commonConfirm,
                verticalPadding: 4.h,
                press: () {
                  _bloc.createReservation(context);
                },
              ),
            ),
          );
        } else if (uiStatus?.isEditMode == true) {
          if (uiStatus?.isEditSubmitEnable == true) {
            widgets.add(
              SizedBox(
                width: 192.w,
                height: 52.h,
                child: CustomDefaultButton(
                  backgroundColor: ThemeConfig.current().colorUtil.button01b,
                  buttonTextColor: ThemeConfig.current().colorUtil.button01a,
                  borderColor: ThemeConfig.current().colorUtil.button01b,
                  disabledBackgroundColor:
                      ThemeConfig.current().colorUtil.button02c,
                  text: OneAppString.of().commonConfirm,
                  verticalPadding: 4.h,
                  press: () {
                    String? runTimeRentalType =
                        _bloc.reservationDetail?.rentalType;
                    _bloc.updateReservation(context, runTimeRentalType);
                  },
                ),
              ),
            );
          } else {
            widgets.add(
              SizedBox(
                width: 192.w,
                height: 52.h,
                child: CustomDefaultButton(
                  backgroundColor: ThemeConfig.current().colorUtil.button01b,
                  buttonTextColor: ThemeConfig.current().colorUtil.button01a,
                  borderColor: ThemeConfig.current().colorUtil.button01b,
                  disabledBackgroundColor:
                      ThemeConfig.current().colorUtil.button02c,
                  text: OneAppString.of().commonConfirm,
                  primaryButtonState: PrimaryButtonState.INACTIVE,
                  verticalPadding: 4.h,
                  press: null,
                ),
              ),
            );
          }
        } else if (uiStatus?.isEditable == true &&
            _bloc.reservationDetail?.allowUpdates == true) {
          widgets
            ..add(
              _transparentButton(
                OneAppString.of().cancelReservation,
                () {
                  _showCancelReservationDialog();
                },
              ),
            )
            ..add(
              SizedBox(
                height: 4.h,
              ),
            )
            ..add(
              SizedBox(
                width: 192.w,
                height: 52.h,
                child: CustomDefaultButton(
                  backgroundColor: ThemeConfig.current().colorUtil.button01b,
                  buttonTextColor: ThemeConfig.current().colorUtil.button01a,
                  borderColor: ThemeConfig.current().colorUtil.button01b,
                  disabledBackgroundColor:
                      ThemeConfig.current().colorUtil.button02c,
                  text: OneAppString.of().edit,
                  verticalPadding: 4.h,
                  press: () {
                    _bloc.refreshStatus(
                        isEditMode: true, isEditSubmitEnable: false);
                  },
                ),
              ),
            );
        }

        return Padding(
          padding: EdgeInsets.fromLTRB(32.w, 34.h, 32.w, 32.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: widgets,
          ),
        );
      },
    );
  }

  Widget _editButton({
    bool? isWhite,
    bool? isDealer,
    bool? showWarning,
    bool? showAddEdit,
    Function(bool isShowAdd)? onClick,
  }) {
    return StreamBuilder(
      stream: _bloc.uiSummaryStatus,
      initialData: _bloc.summaryUIStatus,
      builder: (ctx, AsyncSnapshot<SummaryUIStatus?> snapData) {
        bool visible;
        if (isDealer == true) {
          visible = snapData.data?.isCreateReservation == true;
        } else {
          if (snapData.data?.isEditMode == true) {
            showAddEdit = false;
          }
          visible = snapData.data?.isEditMode == true ||
              snapData.data?.isCreateReservation == true;
        }
        String imageName;
        if (showWarning == true) {
          imageName = 'ic_flex_edit_warning';
        } else if (showAddEdit == true) {
          imageName = 'ic_flex_edit_plus';
        } else if (isWhite == true) {
          imageName = 'ic_flex_edit_white';
        } else {
          imageName = 'ic_flex_edit';
        }
        return Visibility(
          visible: visible || showAddEdit == true,
          child: GestureDetector(
            onTap: () {
              onClick?.call(imageName == 'ic_flex_edit_plus');
            },
            child: SvgPicture.asset(
              'packages/oneapp_common/res/image/svg/toyota/$imageName.svg',
              width: 24.w,
              height: 24.w,
            ),
          ),
        );
      },
    );
  }

  _showTermsAndConditinoViewMore() async {
    await showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return FxSummaryDialogView(
          title: OneAppString.of().summaryDetailPageReservationTCs,
          description: OneAppString.of().summaryDetailPageTipsDetails(
              _bloc.vehicleItem?.brand == "L"
                  ? OneAppString.of().lexusName
                  : OneAppString.of().toyotaName),
          descriptionTextAlign: TextAlign.start,
          primaryButtonText: OneAppString.of().closeText,
          showIcon: false,
          primaryButtonPressed: () async {
            await FlexRouter.popRoute();
          },
        );
      },
    );
  }

  _showCancelReservationDialog() async {
    await showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return FxSummaryDialogView(
          title: OneAppString.of().cancelReservation,
          description: OneAppString.of().cancelReservationDescription,
          primaryButtonText: OneAppString.of().yesCancel,
          additionalButtonText: OneAppString.of().goBack,
          primaryButtonPressed: () async {
            await FlexRouter.popRoute();
            String? runTimeRentalType = _bloc.reservationDetail?.rentalType;
            _bloc.cancelReservation(this.context, runTimeRentalType);
          },
          additionalButtonPressed: () {
            FlexRouter.popRoute();
          },
        );
      },
    );
  }

  _showEditDateTimeDialog() async {
    await showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      builder: (context) {
        return FxSummaryDialogView(
          title: OneAppString.of().editDateAndTime,
          description: OneAppString.of().editDateAndTimeDescription,
          primaryButtonText: OneAppString.of().yesEdit,
          additionalButtonText: OneAppString.of().goBack,
          primaryButtonPressed: () async {
            await FlexRouter.popRoute();
            final item = _bloc.detailInfo ?? null;
            final dealerItem = DealerInfo(
                dealerName: item?.locationName,
                dealerID: item?.locationId,
                dealerGeo: item?.geo,
                dealerPhone: item?.dealerPhone,
                pickUpDate: item?.status == IN_PROGRESS_ACTION_KEY
                    ? item?.pastPickTime
                    : null,
                stationId: item?.originStationId,
                businessHours: item?.businessHour);
            int requestCode = SELECT_DATE_REQUEST_CODE;
            if (_bloc.status == UPCOMING_ACTION_KEY) {
              requestCode = RESET_DATE_REQUEST_CODE;
            } else if (_bloc.status == IN_PROGRESS_ACTION_KEY) {
              requestCode = RESET_DROP_REQUEST_CODE;
            }
            final intentData = IntentToSelectDate(
              reservationId: _bloc.detailInfo?.reservationId,
              status: _bloc.status,
              orgID: item?.organizationId,
              dealerInfo: dealerItem,
              dealerDetailInfo: _bloc.detailInfo?.dealerDetailInfo,
              pickUpDate: item?.pastPickTime,
              // 1610503980000
              dropOffDate: item?.pastDropTime,
              // 1610763180000
              comeFrom: comeFromSummary,
              requestCode: requestCode,
            );
            final result = await FlexRouter.pushName(RoutePath.FX_CALENDAR_VIEW,
                arguments: intentData);
            if (result != null && result is TimeData) {
              _bloc.resetDropOffDate(
                  context: this.context,
                  timeData: result,
                  modifiedBalance: modifiedBalance);
            } else if (result != null && result is Map) {
              _bloc.refreshStatus(
                  isEditMode: true,
                  isEditSubmitEnable: true,
                  modifiedBalance: modifiedBalance);
              _bloc.resetReservationVehicle(
                  context: this.context,
                  intentData: result,
                  modifiedBalance: modifiedBalance);
            } else if (result != null && result is IntentToSelectDate) {
              final result1 = await FlexRouter.pushName(
                  RoutePath.FX_CALENDAR_VIEW,
                  arguments: result);
              if (result1 != null && result1 is Map) {
                _bloc.refreshStatus(
                    isEditMode: true,
                    isEditSubmitEnable: true,
                    modifiedBalance: modifiedBalance);
                _bloc.resetReservationVehicle(
                    context: this.context,
                    intentData: result1,
                    modifiedBalance: modifiedBalance);
              }
            }
          },
          additionalButtonPressed: () {
            FlexRouter.popRoute();
          },
        );
      },
    );
  }

  _showEditDealerDialog() async {
    await showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      builder: (context) {
        return FxSummaryDialogView(
          title: OneAppString.of().editDealer,
          description: OneAppString.of().editDealerDescription,
          primaryButtonText: OneAppString.of().yesEdit,
          additionalButtonText: OneAppString.of().goBack,
          primaryButtonPressed: () async {
            await FlexRouter.popRoute();
            final data = IntentToSearch(
              reservationId: _bloc.detailInfo?.reservationId,
              comeFrom: comeFromSummary,
              timeData: TimeData(
                pickUpDate: _bloc.detailInfo?.pickUpDate,
                dropOffDate: _bloc.detailInfo?.dropOffDate,
                pickUpTime: _bloc.detailInfo?.pickUpTime,
                dropOffTime: _bloc.detailInfo?.dropOffTime,
                pickUpDateTime: _bloc.detailInfo?.pastPickTime,
                dropOffDateTime: _bloc.detailInfo?.pastDropTime,
              ),
              isRental: true,
              vehicleType: "",
              requestCode: SELECT_DATE_REQUEST_CODE,
              status: _bloc.detailInfo?.status,
            );
            await _bloc.searchDealers();
            final result = await FlexRouter.pushName(RoutePath.FX_SEARCH_DEALER,
                arguments: data);
            if (result != null) {
              _bloc.loadData(this.context, result);
            }
          },
          additionalButtonPressed: () {
            FlexRouter.popRoute();
          },
        );
      },
    );
  }
}

Widget _commonContainerImageText(String image, String? text,
    {int rotateDegree = 0, String defaultText = ''}) {
  return Container(
    child: Row(
      children: [
        RotatedBox(
          quarterTurns: rotateDegree,
          child: SvgPicture.asset(
            image,
            width: 24,
            height: 24,
            alignment: Alignment.center,
            fit: BoxFit.scaleDown,
            colorFilter: ColorFilter.mode(
              ThemeConfig.current().colorUtil.tertiary03,
              BlendMode.srcIn,
            ),
          ),
        ),
        Expanded(
          child: Container(
            margin: EdgeInsets.only(left: 11),
            child: Text(
              text?.trim().isNotEmpty == true ? text! : defaultText,
              softWrap: false,
              textAlign: TextAlign.left,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: ThemeConfig.current()
                  .textStyleUtil
                  .callout1
                  .copyWith(fontSize: 14.sp),
            ),
          ),
        ),
      ],
    ),
  );
}

Widget _commonContainerImageText2(String image, String text, String text2,
    {int rotateDegree = 0}) {
  return Container(
    child: Row(
      children: [
        RotatedBox(
          quarterTurns: rotateDegree,
          child: SvgPicture.asset(
            image,
            width: 24,
            height: 24,
            alignment: Alignment.center,
            fit: BoxFit.scaleDown,
            colorFilter: ColorFilter.mode(
              ThemeConfig.current().colorUtil.tertiary03,
              BlendMode.srcIn,
            ),
          ),
        ),
        Expanded(
          child: Container(
            margin: EdgeInsets.only(left: 8),
            child: Text(
              text.isNotEmpty == true ? text : '--',
              softWrap: false,
              textAlign: TextAlign.left,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: ThemeConfig.current().textStyleUtil.callout1,
            ),
          ),
        ),
        Text(
          text2.isNotEmpty == true ? text2 : '--',
          softWrap: false,
          textAlign: TextAlign.left,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          style: ThemeConfig.current().textStyleUtil.callout1,
        ),
      ],
    ),
  );
}

Widget _commonSimilar(String? brand) {
  return Text(
    brand != null && brand != '--' ? "$brand" : "--",
    style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
          color: ThemeConfig.current().colorUtil.tertiary05,
        ),
  );
}

Widget _transparentButton(String text, VoidCallback onPressed) {
  return TextButton(
    onPressed: onPressed,
    child: Text(
      formatTextForLexusAndToyota(text),
      style: ThemeConfig.current()
          .textStyleUtil
          .buttonLink1
          .copyWith(color: ThemeConfig.current().colorUtil.button02a),
    ),
    style: TextButton.styleFrom(
      foregroundColor: ThemeConfig.current().colorUtil.button05b,
      minimumSize: Size(MIN_BUTTON_WIDTH.w, MIN_BUTTON_HEIGHT.h),
      backgroundColor: Colors.transparent,
      elevation: 0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(56)),
      ),
      padding: EdgeInsets.symmetric(horizontal: 32, vertical: 4),
    ),
  );
}

String _getStatusImagePath(String? status) {
  if (status == IN_PROGRESS_ACTION_KEY) {
    return "packages/oneapp_common/res/image/svg/toyota/ic_flex_drop_off_clock.svg";
  } else if (status == UPCOMING_ACTION_KEY) {
    return "packages/oneapp_common/res/image/svg/toyota/ic_flex_detail_clock.svg";
  } else if (status == COMPLETED_ACTION_KEY) {
    return "packages/oneapp_common/res/image/svg/toyota/ic_flex_status_new_complete.svg";
  } else if (status == CANCELLED_ACTION_KEY || status == CANCELED_ACTION_KEY) {
    return "packages/oneapp_common/res/image/svg/toyota/ic_flex_status_cancel.svg";
  } else {
    return 'packages/oneapp_common/res/image/svg/toyota/ic_flex_landing_clock.svg';
  }
}

String _getStatusDisplay(String? status) {
  if (status == CANCELLED_ACTION_KEY || status == CANCELED_ACTION_KEY) {
    return OneAppString.of().statusCancelled;
  } else if (status == COMPLETED_ACTION_KEY) {
    return OneAppString.of().statusComplete;
  }
  return '';
}

void _openStationOnMap(double lat, double lon, String title,
    {String? description}) async {
  final availableMaps = await MapLauncher.installedMaps;
  if (availableMaps.isNotEmpty == true) {
    await availableMaps.first.showMarker(
      coords: Coords(lat, lon),
      title: title,
      description: description,
    );
  }
}
