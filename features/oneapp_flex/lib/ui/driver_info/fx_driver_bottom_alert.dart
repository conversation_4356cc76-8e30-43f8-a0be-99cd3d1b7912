// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';

// Project imports:
import '../../route/router.dart';

class DriverInfoBottomAlertView extends StatefulWidget {
  const DriverInfoBottomAlertView({Key? key}) : super(key: key);

  @override
  _DriverInfoBottomAlertViewState createState() =>
      _DriverInfoBottomAlertViewState();
}

class _DriverInfoBottomAlertViewState extends State<DriverInfoBottomAlertView> {
  @override
  Widget build(BuildContext context) {
    return shareBottomView();
  }

  Widget shareBottomView() {
    return Container(
      color: ThemeConfig.current().colorUtil.tile03,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 24.h,
          ),
          SizedBox(
            width: 48.w,
            height: 48.w,
            child: CommonCircleIconImage(
              'packages/oneapp_common/res/image/svg/toyota/ic_flex_status_indicators.svg',
              iconWidth: 18.w,
              iconHeight: 18.h,
              circleBackgroundColor:
                  ThemeConfig.current().colorUtil.secondary02,
            ),
          ),
          SizedBox(
            height: 16.h,
          ),
          Text(
            OneAppString.of().driverInformation,
            style: ThemeConfig.current().textStyleUtil.subHeadline1,
          ),
          SizedBox(
            height: 8.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 32.w, right: 32.w),
            child: Text(
              OneAppString.of().driverInfoBottomAlertTips,
              style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                    color: ThemeConfig.current().colorUtil.tertiary05,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(
            height: 70.h,
          ),
          SizedBox(
            width: 192.w,
            height: 52.h,
            child: CustomDefaultButton(
              backgroundColor: ThemeConfig.current().colorUtil.button01b,
              buttonTextColor: ThemeConfig.current().colorUtil.button01a,
              borderColor: ThemeConfig.current().colorUtil.button01b,
              disabledBackgroundColor:
                  ThemeConfig.current().colorUtil.button02c,
              text: OneAppString.of().commonContinue,
              verticalPadding: 4.h,
              press: () async {
                FlexRouter.popRoute(result: true);
              },
            ),
          ),
          SizedBox(
            height: 32.h,
          ),
        ],
      ),
    );
  }
}
