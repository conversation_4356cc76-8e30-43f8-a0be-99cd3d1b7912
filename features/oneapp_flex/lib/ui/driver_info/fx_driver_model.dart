// Package imports:
import 'package:oneapp_common/util/common_util.dart';

class DriverModel {
  String? ownerFirstName;
  String? ownerLastName;
  String? ownerEmail;
  String? ownerPhoneNumber;

  String? firstName;
  String? lastName;
  String? email;
  String? phoneNumber;
  bool isBookingForMyself;

  bool invalidFirstName;
  bool invalidLastName;
  bool invalidEmail;
  bool invalidPhoneNumber;

  DriverModel({
    this.ownerFirstName,
    this.ownerLastName,
    this.ownerEmail,
    this.ownerPhoneNumber,
    this.firstName,
    this.lastName,
    this.email,
    this.phoneNumber,
    this.isBookingForMyself = true,
    this.invalidFirstName = false,
    this.invalidLastName = false,
    this.invalidEmail = false,
    this.invalidPhoneNumber = false,
  });

  void resetAllFieldCheckings() {
    firstName = null;
    lastName = null;
    email = null;
    phoneNumber = null;
    invalidFirstName = false;
    invalidLastName = false;
    invalidEmail = false;
    invalidPhoneNumber = false;
  }

  String getName() {
    return '${ownerFirstName ?? ""} ${ownerLastName ?? ""}'.trim();
  }

  bool validateFirstName({bool checkField = true}) {
    bool invalid = !(firstName?.isNotEmpty == true);
    if (checkField == true) {
      invalidFirstName = invalid;
      return invalidFirstName;
    }
    return invalid;
  }

  bool validateLastName({bool checkField = true}) {
    bool invalid = !(lastName?.isNotEmpty == true);
    if (checkField == true) {
      invalidLastName = invalid;
      return invalidLastName;
    }
    return invalid;
  }

  bool validateEmail({bool checkField = true}) {
    bool invalid = !(email?.isNotEmpty == true) || isValidEmail(email!) != true;
    if (checkField == true) {
      invalidEmail = invalid;
      return invalidEmail;
    }
    return invalid;
  }

  bool validatePhoneNumber({bool checkField = true}) {
    bool invalid = !(phoneNumber?.isNotEmpty == true) ||
        isValidPhoneNumber(phoneNumber!) != true;
    if (checkField == true) {
      invalidPhoneNumber = invalid;
      return invalidPhoneNumber;
    }
    return invalid;
  }

  void checkAll() {
    validateFirstName();
    validateLastName();
    validateEmail();
    validatePhoneNumber();
  }

  bool canSubmit({bool checkField = true}) {
    if (checkField == true) {
      validateFirstName(checkField: true);
      validateLastName(checkField: true);
      validateEmail(checkField: true);
      validatePhoneNumber(checkField: true);
      return invalidPhoneNumber != true &&
          invalidEmail != true &&
          invalidLastName != true &&
          invalidFirstName != true;
    } else {
      return validateFirstName(checkField: false) != true &&
          validateLastName(checkField: false) != true &&
          validateEmail(checkField: false) != true &&
          validatePhoneNumber(checkField: false) != true;
    }
  }
}
