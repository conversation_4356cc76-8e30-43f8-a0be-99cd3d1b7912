// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/widget/appbar/empty_appbar.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/keyboard/keyboard_widget.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_customer_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/reservation_detail_helper.dart';

// Project imports:
import '../../flex_global.dart';
import '../../route/router.dart';
import '../../util/fx_analytics_event.dart';
import '../../widget/flex_slide_title_widget.dart';
import 'fx_driver_bottom_alert.dart';
import 'fx_driver_info_bloc.dart';
import 'fx_driver_model.dart';
import 'fx_driver_widget.dart';
import 'fx_insurance_model.dart';
import 'fx_insurance_widget.dart';
import 'fx_license_model.dart';
import 'fx_license_widget.dart';

class FxDriverInfoPage extends StatefulWidget {
  final CustomerDetailResult? customerDetailResult;

  final bool isBookForMyself;

  final ProfileUI? profileUI;

  const FxDriverInfoPage({
    Key? key,
    this.customerDetailResult,
    this.isBookForMyself = true,
    this.profileUI,
  }) : super(key: key);

  @override
  _FxDriverInfoPageState createState() => _FxDriverInfoPageState();
}

class _FxDriverInfoPageState extends State<FxDriverInfoPage> {
  final DriverInfoBloc _bloc = DriverInfoBloc();

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logMarketingEvent(
        AnalyticsEvent.FLEX_DRIVER_LICENSE_PAGE);
    _bloc.init(
        widget.customerDetailResult, widget.isBookForMyself, widget.profileUI);
    _bloc.uiToast.listen((event) {
      showSimpleToast(true, event.message);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      child: KeyboardHiddenGesture(
        child: Scaffold(
          appBar: EmptyAppBar(
            backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
          ),
          backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
          body: Stack(
            fit: StackFit.expand,
            children: [
              _sectionArea(),
              _bottomArea(),
            ],
          ),
        ),
      ),
      bloc: _bloc,
    );
  }

  Widget _sectionArea() {
    return ListView(
      // shrinkWrap: true,
      // physics: NeverScrollableScrollPhysics(),
      children: [
        FlexSlideTitle(
          title: OneAppString.of().driverInfo,
          onBackPressed: () {
            FxAccountGlobal.getInstance().requestCustomer = null;
            FlexRouter.popRoute();
          },
        ),
        StreamBuilder(
          stream: _bloc.uiDriverInfo,
          builder: (ctx, AsyncSnapshot<DriverModel?> snapData) {
            final DriverModel? model = snapData.data;
            return model != null
                ? DriverInfoUI(
                    driverModel: model,
                    bloc: _bloc,
                    isBookingForMyself: model.isBookingForMyself,
                    onChanged: (driverModel, bookMyself, checkChanged) {
                      _bloc.calculate(
                        driverModel: driverModel,
                        bookMyself: bookMyself,
                        checkChanged: checkChanged,
                        checkField: false,
                      );
                    },
                  )
                : Container();
          },
        ),
        StreamBuilder(
          stream: _bloc.uiShowLicenseInsurance,
          initialData: true,
          builder: (ctx, AsyncSnapshot<bool> snapData) {
            final bool? show = snapData.data;
            return _licenseInsurance(show);
          },
        ),
        SizedBox(
          height: 166.h,
        ),
      ],
    );
  }

  Widget _licenseInsurance(bool? show) {
    return show == true
        ? Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              StreamBuilder(
                stream: _bloc.uiLicenseInfo,
                builder: (ctx, AsyncSnapshot<LicenseModel?> snapData) {
                  final LicenseModel? model = snapData.data;
                  return model != null
                      ? LicenseUI(
                          licenseModel: model,
                          bloc: _bloc,
                          onChanged: (licenseModel) {
                            _bloc.calculate(licenseModel: licenseModel);
                          },
                        )
                      : Container();
                },
              ),
              StreamBuilder(
                stream: _bloc.uiInsuranceInfo,
                builder: (ctx, AsyncSnapshot<InsuranceModel?> snapData) {
                  final InsuranceModel? model = snapData.data;
                  return model != null
                      ? InsuranceUI(
                          insuranceModel: model,
                          bloc: _bloc,
                          onChanged: (insuranceModel) {
                            _bloc.calculate(insuranceModel: insuranceModel);
                          },
                        )
                      : Container();
                },
              ),
            ],
          )
        : SizedBox(height: 216.h);
  }

  Widget _bottomArea() {
    return KeyboardVisibilityBuilder(builder: (context, isKeyboardVisible) {
      return Visibility(
        visible: !isKeyboardVisible,
        child: Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
                gradient: LinearGradient(
              colors: [
                ThemeConfig.current().colorUtil.tertiary15,
                ThemeConfig.current().colorUtil.tertiary15,
                ThemeConfig.current().colorUtil.tertiary15.withOpacity(0.0)
              ],
              stops: [0, 0.8, 1],
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
            )),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 16.h,
                ),
                StreamBuilder(
                  stream: _bloc.uiBottomTips,
                  initialData: '',
                  builder: (ctx, snapData) {
                    return Text(
                      snapData.hasData ? snapData.data as String : "",
                      style: ThemeConfig.current()
                          .textStyleUtil
                          .caption1
                          .copyWith(
                            color: ThemeConfig.current().colorUtil.tertiary05,
                            height: 1.5,
                          ),
                      textAlign: TextAlign.center,
                    );
                  },
                ),
                SizedBox(
                  height: 26.h,
                ),
                StreamBuilder(
                  stream: _bloc.uiBottomBtnEnable,
                  initialData: true,
                  builder: (ctx, AsyncSnapshot<bool> snapData) {
                    bool btnEnable = snapData.data == true;
                    return SizedBox(
                      width: 192.w,
                      height: 52.h,
                      child: CustomDefaultButton(
                        backgroundColor:
                            ThemeConfig.current().colorUtil.button01b,
                        buttonTextColor:
                            ThemeConfig.current().colorUtil.button01a,
                        disabledBackgroundColor:
                            ThemeConfig.current().colorUtil.button02c,
                        borderColor: ThemeConfig.current().colorUtil.button01b,
                        verticalPadding: 4.h,
                        text: OneAppString.of().save,
                        primaryButtonState: btnEnable
                            ? PrimaryButtonState.ACTIVE
                            : PrimaryButtonState.INACTIVE,
                        press: btnEnable
                            ? () async {
                                if (_bloc.calculate(finallyCheck: true)) {
                                  final result =
                                      await showMaterialModalBottomSheet(
                                    expand: false,
                                    context: context,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(CARD_RADIUS),
                                      ),
                                    ),
                                    clipBehavior: Clip.antiAliasWithSaveLayer,
                                    barrierColor: ThemeConfig.current()
                                        .colorUtil
                                        .barrierColor,
                                    backgroundColor: ThemeConfig.current()
                                        .colorUtil
                                        .tertiary15,
                                    builder: (context) {
                                      return DriverInfoBottomAlertView();
                                    },
                                  );
                                  if (result != null) {
                                    final customer = await _bloc.saveInfo();
                                    if (customer != null) {
                                      FxAccountGlobal.getInstance()
                                          .requestCustomer = customer;
                                      FlexRouter.popRoute(result: customer);
                                    }
                                  }
                                }
                              }
                            : null,
                      ),
                    );
                  },
                ),
                SizedBox(
                  height: 32.h,
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
