// Package imports:
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';

class FxTextInputFormat extends MaskedInputFormatter {
  FxTextInputFormat() : super('00/00/0000');

  @override
  FormattedValue applyMask(String text) {
    var fv = super.applyMask(text);
    var result = fv.toString();
    var numericString = toNumericString(result);
    var numAddedLeadingSymbols = 0;
    String? amendedMonth;

    var dayStringBuffer = StringBuffer();
    String? amendedDay;
    bool isDayLastDigit = false;

    if (numericString.isNotEmpty) {
      var allDigits = numericString.split('');
      var stringBuffer = StringBuffer();
      var firstDigit = int.parse(allDigits[0]);

      if (firstDigit > 1) {
        stringBuffer.write('0');
        stringBuffer.write(firstDigit);
        amendedMonth = stringBuffer.toString();
        numAddedLeadingSymbols = 1;
      } else if (firstDigit == 1) {
        if (allDigits.length > 1) {
          stringBuffer.write(firstDigit);
          var secondDigit = int.parse(allDigits[1]);
          if (secondDigit > 2) {
            stringBuffer.write(2);
          } else {
            stringBuffer.write(secondDigit);
          }
          amendedMonth = stringBuffer.toString();
        }
      }

      if (allDigits.length == 3) {
        var dayFirstDigit = int.parse(allDigits[2]);
        if (dayFirstDigit >= 3) {
          dayStringBuffer.write('3');
        } else {
          dayStringBuffer.write(dayFirstDigit);
        }
        amendedDay = dayStringBuffer.toString();
      }
      if (allDigits.length == 4) {
        var dayFirstDigit = int.parse(allDigits[2]);
        var daySecondDigit = int.parse(allDigits[3]);
        if (dayFirstDigit >= 3) {
          if (daySecondDigit > 1) {
            dayStringBuffer.write(1);
          } else {
            dayStringBuffer.write(daySecondDigit);
          }
        } else {
          dayStringBuffer.write(daySecondDigit);
        }
        amendedDay = dayStringBuffer.toString();
        isDayLastDigit = true;
      }
    }
    if (amendedMonth != null) {
      if (result.length < amendedMonth.length) {
        result = amendedMonth;
      } else {
        var sub = result.substring(2, result.length);
        result = '$amendedMonth$sub';
      }
    }
    if (amendedDay != null) {
      int num = isDayLastDigit ? 4 : 3;
      var sub = result.substring(0, num);
      result = '$sub$amendedDay';
    }

    fv = super.applyMask(result);

    /// a little hack to be able to move caret by one
    /// symbol to the right if a leading zero was added automatically
    for (var i = 0; i < numAddedLeadingSymbols; i++) {
      fv.increaseNumberOfLeadingSymbols();
    }
    return fv;
  }
}
