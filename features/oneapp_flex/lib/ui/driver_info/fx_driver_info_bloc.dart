// Package imports:

// Package imports:
import 'package:intl/intl.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_customer_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_reserve_request_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/reservation_detail_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_cfai_api_client.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../flex_global.dart';
import '../../util/fx_analytics_event.dart';
import '../landing/fx_detail_landing_bloc.dart';
import '../summary/fx_summary_detail_bloc.dart';
import 'fx_driver_model.dart';
import 'fx_insurance_model.dart';
import 'fx_license_model.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class DriverInfoBloc extends BlocBase {
  final _driverInfoCtrl = BehaviorSubject<DriverModel?>();

  Stream<DriverModel?> get uiDriverInfo => _driverInfoCtrl.stream;

  final _licenseInfoCtrl = BehaviorSubject<LicenseModel?>();

  Stream<LicenseModel?> get uiLicenseInfo => _licenseInfoCtrl.stream;

  final _insuranceInfoCtrl = BehaviorSubject<InsuranceModel?>();

  Stream<InsuranceModel?> get uiInsuranceInfo => _insuranceInfoCtrl.stream;

  final _bottomTipsCtrl = BehaviorSubject<String>();

  Stream<String> get uiBottomTips => _bottomTipsCtrl.stream;

  final _bottomBtnCtrl = BehaviorSubject<bool>();

  Stream<bool> get uiBottomBtnEnable => _bottomBtnCtrl.stream;

  final _showLicenseInsuranceCtrl = BehaviorSubject<bool>();

  Stream<bool> get uiShowLicenseInsurance => _showLicenseInsuranceCtrl.stream;

  final _toastController = BehaviorSubject<ToastMsgData>();

  Stream<ToastMsgData> get uiToast => _toastController.stream;

  final _showDriverInfoController = BehaviorSubject<bool?>();

  Stream<bool?> get showDriverInfoContent => _showDriverInfoController.stream;

  final _showInsuranceContentController = BehaviorSubject<bool>();

  Stream<bool> get showInsuranceContent =>
      _showInsuranceContentController.stream;

  final _showLicenseContentController = BehaviorSubject<bool>();

  Stream<bool> get showLicenseContent => _showLicenseContentController.stream;

  FXCFAIClient cfaiAPI = APIClientConfig.flexClient;

  DriverModel? _driverModel;
  LicenseModel? _licenseModel;
  InsuranceModel? _insuranceModel;

  bool _isBookForMyself = true;
  bool _checkChanged = false;
  vehicleInfo.Payload? vehicleInfoEntity;

  void init(CustomerDetailResult? customerDetailResult, bool bookMyself,
      ProfileUI? profileUI) {
    _isBookForMyself = bookMyself;
    initForDriverInfo(customerDetailResult, profileUI);
    initForLicense(customerDetailResult);
    initForInsurance(customerDetailResult);
    calculate(checkField: false);
  }

  void initForDriverInfo(
      CustomerDetailResult? customerDetail, ProfileUI? profileUI) {
    _driverModel = DriverModel(
      isBookingForMyself: _isBookForMyself == true,
      ownerEmail: Global.getInstance().userEmail,
      ownerFirstName: Global.getInstance().userFirstName,
      ownerLastName: Global.getInstance().userLastName,
      ownerPhoneNumber: customerDetail?.phoneNumber,
      email: profileUI?.someoneEmail,
      firstName: profileUI?.someoneFirstName,
      lastName: profileUI?.someoneLastName,
      phoneNumber: profileUI?.someonePhone,
    );
    _driverInfoCtrl.add(_driverModel);
  }

  void initForLicense(CustomerDetailResult? customerDetailResult) {
    if (customerDetailResult?.license != null) {
      _licenseModel = LicenseModel(
        firstName: customerDetailResult?.license?.firstName ?? "",
        lastName: customerDetailResult?.license?.lastName ?? "",
        birthDate: convertyyyyMMddToMMddyy(
            customerDetailResult?.license?.dateOfBirthInLicense ?? ""),
        license: customerDetailResult?.license?.licenseNumber ?? "",
        expiration: convertyyyyMMddToMMddyy(
            customerDetailResult?.license?.licenseDateOfExpiry ?? ""),
        address: customerDetailResult?.license?.addressLine1 ??
            customerDetailResult?.license?.addressLine2,
        city: customerDetailResult?.license?.city,
        country: customerDetailResult?.license?.country,
        zipCode: customerDetailResult?.license?.zipCode,
        state: customerDetailResult?.license?.state,
      );
    } else {
      _licenseModel = LicenseModel();
    }
    _licenseInfoCtrl.add(_licenseModel);
  }

  void initForInsurance(CustomerDetailResult? customerDetailResult) {
    if (customerDetailResult?.insurance != null) {
      _insuranceModel = InsuranceModel(
        company: customerDetailResult?.insurance?.insuranceCompanyName,
        policy: customerDetailResult?.insurance?.insurancePolicyNumber,
        expiration: convertyyyyMMddToMMddyy(
            customerDetailResult?.insurance?.insuranceDateOfExpiry ?? ""),
      );
    } else {
      _insuranceModel = InsuranceModel();
    }
    _insuranceInfoCtrl.add(_insuranceModel);
  }

  bool calculate({
    DriverModel? driverModel,
    LicenseModel? licenseModel,
    InsuranceModel? insuranceModel,
    bool? checkChanged,
    bool? bookMyself,
    bool? finallyCheck,
    bool checkField = true,
  }) {
    if (bookMyself != null) {
      _isBookForMyself = bookMyself;
    }
    if (driverModel != null) {
      _driverModel = driverModel;
    }
    if (licenseModel != null) {
      _licenseModel = licenseModel;
    }
    if (insuranceModel != null) {
      _insuranceModel = insuranceModel;
    }
    if (checkChanged != null) {
      _checkChanged = checkChanged;
    }

    if (finallyCheck == true) {
      _driverModel?.checkAll();
      _licenseModel?.checkAll();
      _insuranceModel?.checkAll();
    }

    // if (bookMyself == true) {
    //   _driverModel.resetAllFieldCheckings();
    //   _driverInfoCtrl.add(_driverModel);
    // }
    bool available = false;
    bool isAllEmpty = false;

    if (_isBookForMyself == true) {
      bool canSubmit = _licenseModel?.canSubmit() == true &&
          _insuranceModel?.canSubmit() == true &&
          (_licenseModel?.hasChanged == true ||
              _insuranceModel?.hasChanged == true ||
              _checkChanged == true);
      if (_licenseModel!.isEmpty() && _licenseModel!.isEmpty()) {
        available = true;
        isAllEmpty = true;
      } else {
        available = canSubmit;
      }
    } else {
      available = _driverModel?.canSubmit(checkField: checkField) ?? false;
    }
    _bottomBtnCtrl.add(available);
    Future(() async {
      vehicleInfoEntity =
          await getLocalPayloadFromVin(Global.getInstance().vin);
    }).then((value) => refreshTips());
    if (!isAllEmpty || finallyCheck != true) {
      refreshDLInsurance();
    }
    return available;
  }

  void refreshTips() {
    String tipsValue = _isBookForMyself == true
        ? OneAppString.of().driverInfoBlocTipsValue1(
            vehicleInfoEntity?.brand?.toUpperCase() == "L"
                ? OneAppString.of().lexusName
                : OneAppString.of().toyotaName)
        : OneAppString.of().driverInfoBlocTipsValue2;
    _bottomTipsCtrl.add(tipsValue);
  }

  void refreshDLInsurance() {
    _showLicenseInsuranceCtrl.add(_isBookForMyself == true);
  }

  Future<Customer?> saveInfo() async {
    Insurance? insurance;
    if (_insuranceModel?.isEmpty() == false) {
      insurance = Insurance(
        insuranceCompanyName: _insuranceModel?.company,
        insurancePolicyNumber: _insuranceModel?.policy,
        insuranceDateOfExpiry:
            convertMMddyToyyyyMMddy(_insuranceModel?.expiration ?? ""),
      );
    }

    License? license;
    if (_licenseModel?.isEmpty() == false) {
      license = License(
        firstName: _licenseModel?.firstName,
        lastName: _licenseModel?.lastName,
        fullName:
            '${_licenseModel?.firstName ?? ''} ${_licenseModel?.lastName ?? ''}'
                .trim(),
        licenseNumber: _licenseModel?.license,
        dateOfBirthInLicense:
            convertMMddyToyyyyMMddy(_licenseModel?.birthDate ?? ""),
        licenseDateOfExpiry:
            convertMMddyToyyyyMMddy(_licenseModel?.expiration ?? ""),
        addressLine1: _licenseModel?.address,
        city: _licenseModel?.city,
        country: _licenseModel?.country,
        zipCode: _licenseModel?.zipCode,
        state: _licenseModel?.state,
      );
    }
    final customer = Customer(
      guid: Global.getInstance().guid,
      firstName: _isBookForMyself == true
          ? _driverModel?.ownerFirstName
          : _driverModel?.firstName,
      lastName: _isBookForMyself == true
          ? _driverModel?.ownerLastName
          : _driverModel?.lastName,
      phoneNumber: _isBookForMyself == true
          ? _driverModel?.ownerPhoneNumber
          : _driverModel?.phoneNumber,
      emailId: _isBookForMyself == true
          ? _driverModel?.ownerEmail
          : _driverModel?.email,
      guestFlag: _isBookForMyself != true,
      insurance: _isBookForMyself == true ? insurance : null,
      license: _isBookForMyself == true ? license : null,
    );
    if (_isBookForMyself == true) {
      bool isSentSuccess = await commitRequest(customer);
      FxSummaryDetailBloc()
          .refreshStatus(isEditMode: true, isEditSubmitEnable: true);
      return isSentSuccess == true ? customer : null;
    }
    return customer;
  }

  Future<bool> commitRequest(Customer customer) async {
    bool isSuccess = false;
    await showProgress(NavigateService.context);
    CommonResponse<FxCfaiCustomerResponse> commonResponse =
        await cfaiAPI.sendUpdateCustomers(customer);
    if (commonResponse.response != null) {
      FxAccountGlobal.getInstance().insurance =
          commonResponse.response?.payload?.result?.insurance;
      FxAccountGlobal.getInstance().license =
          commonResponse.response?.payload?.result?.license;
      FxAccountGlobal.getInstance().customersEntity =
          commonResponse.response?.payload?.result;
      isSuccess = true;
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_UPDATE_ACCOUNT_SUCCESS,
          category: LogCategory.FL_RENT);
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          AnalyticsEvent.FLEX_UPDATE_ACCOUNT_FAILURE,
          category: LogCategory.FL_RENT);
      _toastController
          .add(ToastMsgData(commonResponse.error?.errorMessage ?? ""));
    }
    await dismissProgress(NavigateService.context);
    return isSuccess;
  }

  void showDriverInfo(bool? isShow) {
    _showDriverInfoController.add(isShow);
  }

  void showInsurance(bool isShow) {
    _showInsuranceContentController.add(isShow);
  }

  void showLicense(bool isShow) {
    _showLicenseContentController.add(isShow);
  }

  @override
  void dispose() {
    _driverInfoCtrl.close();
    _licenseInfoCtrl.close();
    _insuranceInfoCtrl.close();
    _bottomTipsCtrl.close();
    _bottomBtnCtrl.close();
    _showLicenseInsuranceCtrl.close();
    _toastController.close();
    _showDriverInfoController.close();
    _showInsuranceContentController.close();
    _showLicenseContentController.close();
  }
}

String convertyyyyMMddToMMddyy(String yyyyMMdd) {
  try {
    final dateTime = DateFormat('yyyy-MM-dd').parse(yyyyMMdd);
    return DateFormat('MM/dd/yy').format(dateTime);
  } catch (e) {
    FireBaseAnalyticsLogger.logError(e.toString(),
        category: LogCategory.FL_RENT);
  }
  return yyyyMMdd;
}

String convertMMddyToyyyyMMddy(String _strMMddyy) {
  try {
    final dateTime = DateFormat('MM/dd/yy').parse(_strMMddyy);
    return DateFormat('yyyy-MM-dd').format(dateTime);
  } catch (e) {
    FireBaseAnalyticsLogger.logError(e.toString(),
        category: LogCategory.FL_RENT);
  }
  return _strMMddyy;
}
