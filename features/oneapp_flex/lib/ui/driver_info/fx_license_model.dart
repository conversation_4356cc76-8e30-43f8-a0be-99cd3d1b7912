// Package imports:
import 'package:collection/collection.dart' show IterableExtension;
import 'package:intl/intl.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';

List<String>? countryList = [
  OneAppString.of().unitedStates,
  OneAppString.of().loginCanada
];

class LicenseModel {
  String? firstName;
  String? lastName;
  String? birthDate;
  String? license;
  String? expiration;
  String? address;
  String? city;
  String? state;
  String? zipCode;
  String? country;

  bool? hasChanged;

  bool invalidFirstName;
  bool invalidLastName;
  bool invalidBirthDate;
  bool invalidLicense;
  bool invalidExpiration;
  bool invalidAddress;
  bool invalidCity;
  bool invalidState;
  bool invalidZipCode;
  bool invalidCountry;

  LicenseModel({
    this.firstName,
    this.lastName,
    this.birthDate,
    this.license,
    this.expiration,
    this.address,
    this.city,
    this.state,
    this.zipCode,
    this.country,
    this.hasChanged,
    this.invalidFirstName = false,
    this.invalidLastName = false,
    this.invalidBirthDate = false,
    this.invalidLicense = false,
    this.invalidExpiration = false,
    this.invalidAddress = false,
    this.invalidCity = false,
    this.invalidState = false,
    this.invalidZipCode = false,
    this.invalidCountry = false,
  });

  void validateFirstName() {
    invalidFirstName = !(firstName?.isNotEmpty == true);
  }

  void validateLastName() {
    invalidLastName = !(lastName?.isNotEmpty == true);
  }

  void validateBirthDate() {
    if (birthDate != null) {
      if (birthDate != "") {
        final birthTime = DateFormat('MM/dd/yy').parse(birthDate!);
        final year = birthTime.year + 18;
        final birthCompareTime = DateTime(year, birthTime.month, birthTime.day);
        final currentTime = DateTime.now();
        invalidBirthDate = birthCompareTime.compareTo(currentTime) >= 0;
      }
    } else {
      invalidBirthDate = true;
    }
  }

  void validateLicense() {
    invalidLicense = !(license?.isNotEmpty == true);
  }

  void validateExpiration() {
    if (expiration != null) {
      if (expiration != "") {
        invalidExpiration = DateFormat('MM/dd/yy')
                .parse(expiration!)
                .compareTo(DateTime.now()) <=
            0;
      }
    } else {
      invalidExpiration = true;
    }
  }

  void validateAddress() {
    invalidAddress = !(address?.isNotEmpty == true);
  }

  void validateCity() {
    invalidCity = !(city?.isNotEmpty == true);
  }

  void validateState() {
    invalidState = !(state?.isNotEmpty == true) || getDisplayState() == null;
  }

  void validateZipCode() {
    invalidZipCode = !(zipCode?.isNotEmpty == true) ||
        isValidZipcode(zipCode!,
                isCanada: (country == OneAppString.of().loginCanada)) !=
            true;
  }

  void validateCountry() {
    invalidCountry = !(country?.isNotEmpty == true);
  }

  bool canSubmit() {
    bool noError = !invalidLicense &&
        !invalidCountry &&
        !invalidZipCode &&
        !invalidState &&
        !invalidCity &&
        !invalidAddress &&
        !invalidExpiration &&
        !invalidLicense &&
        !invalidBirthDate &&
        !invalidLastName &&
        !invalidFirstName;
    return noError || isEmpty();
  }

  bool isEmpty() {
    bool allEmpty = !(firstName?.isNotEmpty == true) &&
        !(lastName?.isNotEmpty == true) &&
        !(birthDate?.isNotEmpty == true) &&
        !(license?.isNotEmpty == true) &&
        !(expiration?.isNotEmpty == true) &&
        !(address?.isNotEmpty == true) &&
        !(city?.isNotEmpty == true) &&
        !(state?.isNotEmpty == true) &&
        !(zipCode?.isNotEmpty == true) &&
        !(country?.isNotEmpty == true);
    return allEmpty;
  }

  void checkAll() {
    validateFirstName();
    validateLastName();
  }

  String? getDisplayState() {
    String? displayState;
    if (state?.isNotEmpty == true) {
      if (country == OneAppString.of().loginCanada) {
        displayState = CANADA_STATE_CODE_LIST.firstWhereOrNull((element) =>
            state?.toUpperCase() != null && element.contains(state!) == true);
      } else {
        displayState = US_STATE_CODE_LIST.firstWhereOrNull((element) =>
            state?.toUpperCase() != null && element.contains(state!) == true);
      }
    }
    return displayState;
  }

  void setDisplayState(String displayState) {
    if (displayState.isNotEmpty == true) {
      state = displayState.split('-').first.trim();
    } else {
      state = null;
    }
  }
}
