// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/text/custom_textfield.dart';

// Project imports:
import 'fx_driver_info_bloc.dart';
import 'fx_insurance_model.dart';

class InsuranceUI extends StatefulWidget {
  final InsuranceModel? insuranceModel;

  final Function(InsuranceModel? insuranceModel)? onChanged;

  final DriverInfoBloc? bloc;

  const InsuranceUI({
    Key? key,
    this.insuranceModel,
    this.onChanged,
    this.bloc,
  }) : super(key: key);

  @override
  _InsuranceUIState createState() => _InsuranceUIState();
}

class _InsuranceUIState extends State<InsuranceUI> {
  bool isExpand = false;

  InsuranceModel? _insuranceModel;

  TextEditingController insuranceCompanyController = TextEditingController();
  TextEditingController insurancePolicyController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _insuranceModel = widget.insuranceModel;
    insuranceCompanyController.text = _insuranceModel?.company ?? '';
    insurancePolicyController.text = _insuranceModel?.policy ?? '';

    widget.bloc!.showDriverInfoContent.listen((event) {
      if (event == true && isExpand == true) {
        widget.bloc!.showInsurance(false);
      }
    });
    widget.bloc!.showLicenseContent.listen((event) {
      if (event == true && isExpand == true) {
        widget.bloc!.showInsurance(false);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    insuranceCompanyController.dispose();
    insurancePolicyController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        stream: widget.bloc!.showInsuranceContent,
        builder: (ctx, AsyncSnapshot<bool> snapData) {
          isExpand = snapData.data ?? false;
          return _insuranceContent();
        });
  }

  Widget _insuranceContent() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 48.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _collapseTitle(),
          _expandedContent(),
        ],
      ),
    );
  }

  Widget _collapseTitle() {
    return Padding(
      padding: EdgeInsets.only(top: 8.h),
      child: Container(
        height: 60.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.r)),
          color: ThemeConfig.current().colorUtil.tile02,
        ),
        padding: EdgeInsets.only(left: 16.w, right: 16.w),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              'packages/oneapp_common/res/image/svg/toyota/ic_calendar.svg',
              colorFilter: ColorFilter.mode(
                ThemeConfig.current().colorUtil.tertiary03,
                BlendMode.srcIn,
              ),
              width: 24.w,
              height: 24.h,
            ),
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              child: Text(
                OneAppString.of().registerInsurance,
                textAlign: TextAlign.start,
                style: ThemeConfig.current().textStyleUtil.body4,
              ),
            ),
            SizedBox(
              width: 16.w,
            ),
            InkWell(
              child: SvgPicture.asset(
                'packages/oneapp_common/res/image/svg/toyota/${isExpand == false ? 'ic_flex_user_plus' : 'ic_flex_user_minimize'}.svg',
                colorFilter: ColorFilter.mode(
                  ThemeConfig.current().colorUtil.tertiary03,
                  BlendMode.srcIn,
                ),
                width: 24.w,
                height: 24.h,
              ),
              onTap: () {
                isExpand = !isExpand;
                widget.bloc!.showInsurance(isExpand);
                // setState(() {});
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _expandedContent() {
    return Visibility(
      visible: isExpand == true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 16.h,
          ),
          CustomTextField(
            controller: insuranceCompanyController,
            placeholder: OneAppString.of().insuranceCompany,
            isError: _insuranceModel!.invalidCompany,
            errMessage: OneAppString.of().invalidInsuranceProvider,
            onChanged: (value) {
              _insuranceModel!.company = value;
              _insuranceModel!.hasChanged = true;
              _insuranceModel!.validateCompany();
              setState(() {
                widget.onChanged?.call(_insuranceModel);
              });
            },
          ),
          SizedBox(
            height: 8.h,
          ),
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: insurancePolicyController,
                    placeholder: OneAppString.of().policy,
                    isError: _insuranceModel!.invalidPolicy,
                    errMessage: OneAppString.of().invalidPolicy,
                    onChanged: (value) {
                      _insuranceModel!.policy = value;
                      _insuranceModel!.hasChanged = true;
                      _insuranceModel!.validatePolicy();
                      setState(() {
                        widget.onChanged?.call(_insuranceModel);
                      });
                    },
                  ),
                ),
                SizedBox(
                  width: 9.w,
                ),
                Expanded(
                  child: CustomTextField(
                    controller: TextEditingController(
                        text: _insuranceModel?.expiration),
                    placeholder: OneAppString.of().expiration,
                    isError: _insuranceModel!.invalidExpiration,
                    errMessage: OneAppString.of().expired,
                    isEditable: false,
                    onClick: () {
                      showDatePicker(
                        context: context,
                        builder: (BuildContext context, Widget? child) {
                          return Theme(
                            data: ThemeData.light(),
                            child: child!,
                          );
                        },
                        initialDate: _insuranceModel?.expiration != ""
                            ? DateFormat('MM/dd/yy')
                                .parse(_insuranceModel?.expiration ?? "")
                            : DateTime.now(),
                        firstDate: DateTime(1900),
                        lastDate: DateTime(2100),
                      ).then((dateTime) {
                        _insuranceModel?.expiration =
                            DateFormat('MM/dd/yy').format(dateTime!);
                        _insuranceModel?.hasChanged = true;
                        _insuranceModel?.validateExpiration();
                        setState(() {
                          widget.onChanged?.call(_insuranceModel);
                        });
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
