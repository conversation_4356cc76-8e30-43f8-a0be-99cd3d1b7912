// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/text/custom_textfield.dart';

// Project imports:
import '../../util/fx_common_util.dart';
import 'fx_driver_info_bloc.dart';
import 'fx_driver_model.dart';

class DriverInfoUI extends StatefulWidget {
  final DriverModel? driverModel;

  final bool isBookingForMyself;
  final DriverInfoBloc? bloc;

  final Function(
          DriverModel? driverModel, bool isBookingForMyself, bool checkChanged)?
      onChanged;

  const DriverInfoUI({
    Key? key,
    this.driverModel,
    this.onChanged,
    this.isBookingForMyself = true,
    this.bloc,
  }) : super(key: key);

  @override
  _DriverInfoUIState createState() => _DriverInfoUIState();
}

class _DriverInfoUIState extends State<DriverInfoUI> {
  bool? isExpand;

  bool _isBookingForMyself = true;

  DriverModel? _driverModel;

  TextEditingController driverFirstNameController = TextEditingController();
  TextEditingController driverLastNameController = TextEditingController();
  TextEditingController driverEmailController = TextEditingController();
  TextEditingController driverPhoneNumberController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _isBookingForMyself = widget.isBookingForMyself;
    _driverModel = widget.driverModel;
    driverFirstNameController.text = _driverModel?.firstName ?? '';
    driverLastNameController.text = _driverModel?.lastName ?? '';
    driverEmailController.text = _driverModel?.email ?? '';
    driverPhoneNumberController.text = _driverModel?.phoneNumber ?? '';

    widget.bloc!.showInsuranceContent.listen((event) {
      if (event == true && isExpand == true) {
        widget.bloc!.showDriverInfo(false);
      }
    });
    widget.bloc!.showLicenseContent.listen((event) {
      if (event == true && isExpand == true) {
        widget.bloc!.showDriverInfo(false);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    driverFirstNameController.dispose();
    driverLastNameController.dispose();
    driverEmailController.dispose();
    driverPhoneNumberController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        stream: widget.bloc!.showDriverInfoContent,
        builder: (ctx, AsyncSnapshot<bool?> snapData) {
          isExpand = snapData.data ?? true;
          return driverWidget();
        });
  }

  Widget driverWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(8.r)),
              color: ThemeConfig.current().colorUtil.tile02,
            ),
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  'packages/oneapp_common/res/image/svg/toyota/ic_flex_profile.svg',
                  colorFilter: ColorFilter.mode(
                    ThemeConfig.current().colorUtil.tertiary03,
                    BlendMode.srcIn,
                  ),
                  width: 24.w,
                  height: 24.h,
                ),
                SizedBox(
                  width: 16.w,
                ),
                Expanded(
                  child: Text(
                    OneAppString.of().driver,
                    textAlign: TextAlign.start,
                    style: ThemeConfig.current().textStyleUtil.body4,
                  ),
                ),
                SizedBox(
                  width: 16.w,
                ),
                InkWell(
                  child: SvgPicture.asset(
                    'packages/oneapp_common/res/image/svg/toyota/${isExpand == false ? 'ic_flex_user_plus' : 'ic_flex_user_minimize'}.svg',
                    colorFilter: ColorFilter.mode(
                      ThemeConfig.current().colorUtil.tertiary03,
                      BlendMode.srcIn,
                    ),
                    width: 24.w,
                    height: 24.h,
                  ),
                  onTap: () {
                    isExpand = !isExpand!;
                    widget.bloc!.showDriverInfo(isExpand);
                    // setState(() {});
                  },
                ),
              ],
            ),
          ),
          Visibility(
            visible: isExpand == true,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 16.h,
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 16.w,
                    ),
                    Text(
                      OneAppString.of().iAmBookingForMyself,
                      textAlign: TextAlign.start,
                      style: ThemeConfig.current().textStyleUtil.body3,
                    ),
                  ],
                ),
                SizedBox(
                  height: 24.h,
                ),
                _tabButtons(),
                _isBookingForMyself != true
                    ? _editableContent()
                    : _readOnlyContent(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _tabButtons() {
    return Row(
      children: [
        Expanded(
          child: SizedBox(
            width: double.infinity,
            height: 44.h,
            child: CustomDefaultButton(
                backgroundColor: _isBookingForMyself == true
                    ? ThemeConfig.current().colorUtil.button02a
                    : ThemeConfig.current().colorUtil.button05b,
                disabledBackgroundColor:
                    ThemeConfig.current().colorUtil.button02c,
                borderColor: Colors.transparent,
                verticalPadding: 4.h,
                textStyle:
                    ThemeConfig.current().textStyleUtil.callout2.copyWith(
                          color: _isBookingForMyself == true
                              ? ThemeConfig.current().colorUtil.button05b
                              : ThemeConfig.current().colorUtil.button02a,
                        ),
                text: OneAppString.of().myself,
                primaryButtonState: PrimaryButtonState.ACTIVE,
                press: () {
                  _isBookingForMyself = true;
                  setState(() {
                    widget.onChanged!
                        .call(_driverModel, _isBookingForMyself, true);
                  });
                }),
          ),
        ),
        SizedBox(
          width: 7.w,
        ),
        Expanded(
          child: SizedBox(
            width: double.infinity,
            height: 44.h,
            child: CustomDefaultButton(
                backgroundColor: _isBookingForMyself == true
                    ? ThemeConfig.current().colorUtil.button05b
                    : ThemeConfig.current().colorUtil.button02a,
                disabledBackgroundColor:
                    ThemeConfig.current().colorUtil.button02c,
                borderColor: Colors.transparent,
                verticalPadding: 4.h,
                textStyle:
                    ThemeConfig.current().textStyleUtil.callout2.copyWith(
                          color: _isBookingForMyself == true
                              ? ThemeConfig.current().colorUtil.button02a
                              : ThemeConfig.current().colorUtil.button05b,
                        ),
                text: OneAppString.of().someoneElse,
                primaryButtonState: PrimaryButtonState.ACTIVE,
                press: () {
                  _isBookingForMyself = false;
                  setState(() {
                    widget.onChanged!
                        .call(_driverModel, _isBookingForMyself, true);
                  });
                }),
          ),
        ),
      ],
    );
  }

  Widget _readOnlyContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 36.w, top: 32.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.driverModel?.getName() ?? '',
                textAlign: TextAlign.start,
                style: ThemeConfig.current().textStyleUtil.body4,
              ),
              SizedBox(
                height: 8.w,
              ),
              Text(
                widget.driverModel?.ownerEmail ?? '',
                textAlign: TextAlign.start,
                style: ThemeConfig.current().textStyleUtil.body3,
              ),
              SizedBox(
                height: 8.w,
              ),
              Text(
                FxCommonUtils.phoneDisplayFormat(
                    widget.driverModel?.ownerPhoneNumber ?? ''),
                textAlign: TextAlign.start,
                style: ThemeConfig.current().textStyleUtil.body3,
              ),
            ],
          ),
        ),
        SizedBox(
          height: 33.h,
        ),
        Row(
          children: [
            Expanded(
              child: Container(
                height: 1.h,
                color: ThemeConfig.current().colorUtil.button02c,
              ),
            ),
            Text(
              OneAppString.of().saveTimeAtTheDealer,
              textAlign: TextAlign.start,
              style: ThemeConfig.current().textStyleUtil.subHeadline2,
            ),
            Expanded(
              child: Container(
                height: 1.h,
                color: ThemeConfig.current().colorUtil.button02c,
              ),
            ),
          ],
        ),
        SizedBox(
          height: 24.h,
        ),
      ],
    );
  }

  Widget _editableContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 32.h,
        ),
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: CustomTextField(
                  inputType: TextInputType.name,
                  controller: driverFirstNameController,
                  placeholder: OneAppString.of().driverFirstName,
                  isError: _driverModel!.invalidFirstName,
                  errMessage: OneAppString.of().invalidFirstname,
                  onChanged: (value) {
                    _driverModel!.firstName = value;
                    _driverModel!.validateFirstName();
                    setState(() {
                      widget.onChanged!
                          .call(_driverModel, _isBookingForMyself, false);
                    });
                  },
                ),
              ),
              SizedBox(
                width: 9.w,
              ),
              Expanded(
                child: CustomTextField(
                  inputType: TextInputType.name,
                  controller: driverLastNameController,
                  placeholder: OneAppString.of().driverLastName,
                  isError: _driverModel!.invalidLastName,
                  errMessage: OneAppString.of().invalidLastname,
                  onChanged: (value) {
                    _driverModel!.lastName = value;
                    _driverModel!.validateLastName();
                    setState(() {
                      widget.onChanged!
                          .call(_driverModel, _isBookingForMyself, false);
                    });
                  },
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 8.w,
        ),
        CustomTextField(
          inputType: TextInputType.emailAddress,
          controller: driverEmailController,
          placeholder: OneAppString.of().email,
          isError: _driverModel!.invalidEmail,
          errMessage: OneAppString.of().invalidEmail,
          onChanged: (value) {
            _driverModel!.email = value;
            _driverModel!.validateEmail();
            setState(() {
              widget.onChanged!.call(_driverModel, _isBookingForMyself, false);
            });
          },
        ),
        SizedBox(
          height: 8.w,
        ),
        CustomTextField(
          inputType: TextInputType.phone,
          maxLength: 10,
          controller: driverPhoneNumberController,
          placeholder: OneAppString.of().phoneNumber,
          isError: _driverModel!.invalidPhoneNumber,
          errMessage: OneAppString.of().invalidPhonenumber,
          onChanged: (value) {
            _driverModel!.phoneNumber = value;
            _driverModel!.validatePhoneNumber();
            setState(() {
              widget.onChanged!.call(_driverModel, _isBookingForMyself, false);
            });
          },
        ),
      ],
    );
  }
}
