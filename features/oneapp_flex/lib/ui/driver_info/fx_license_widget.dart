// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/widget/button/custom_dropdown_button.dart';
import 'package:oneapp_common/widget/text/custom_textfield.dart';

// Project imports:
import 'fx_driver_info_bloc.dart';
import 'fx_license_model.dart';

class LicenseUI extends StatefulWidget {
  final LicenseModel? licenseModel;

  final Function(LicenseModel? driverModel)? onChanged;

  final DriverInfoBloc? bloc;

  const LicenseUI({
    Key? key,
    this.licenseModel,
    this.onChanged,
    this.bloc,
  }) : super(key: key);

  @override
  _LicenseUIState createState() => _LicenseUIState();
}

class _LicenseUIState extends State<LicenseUI> {
  bool isExpand = false;

  LicenseModel? _licenseModel;

  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController licenseController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController zipCodeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _licenseModel = widget.licenseModel;
    firstNameController.text = _licenseModel?.firstName ?? '';
    lastNameController.text = _licenseModel?.lastName ?? '';
    licenseController.text = _licenseModel?.license ?? '';
    addressController.text = _licenseModel?.address ?? '';
    cityController.text = _licenseModel?.city ?? '';
    zipCodeController.text = _licenseModel?.zipCode ?? '';

    widget.bloc!.showInsuranceContent.listen((event) {
      if (event == true && isExpand == true) {
        widget.bloc!.showLicense(false);
      }
    });
    widget.bloc!.showDriverInfoContent.listen((event) {
      if (event == true && isExpand == true) {
        widget.bloc!.showLicense(false);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    licenseController.dispose();
    addressController.dispose();
    cityController.dispose();
    zipCodeController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        stream: widget.bloc!.showLicenseContent,
        builder: (ctx, AsyncSnapshot<bool> snapData) {
          isExpand = snapData.data ?? false;
          return _licenseContent();
        });
  }

  Widget _licenseContent() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _collapseTitleContent(),
          _expandedContent(),
        ],
      ),
    );
  }

  Widget _collapseTitleContent() {
    return Padding(
      padding: EdgeInsets.only(top: 8.h),
      child: Container(
        height: 60.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.r)),
          color: ThemeConfig.current().colorUtil.tile02,
        ),
        padding: EdgeInsets.only(left: 16.w, right: 16.w),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              'packages/oneapp_common/res/image/svg/toyota/ic_flex_car_icon.svg',
              colorFilter: ColorFilter.mode(
                ThemeConfig.current().colorUtil.tertiary03,
                BlendMode.srcIn,
              ),
              width: 24.w,
              height: 24.h,
            ),
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              child: Text(
                OneAppString.of().license,
                textAlign: TextAlign.start,
                style: ThemeConfig.current().textStyleUtil.body4,
              ),
            ),
            SizedBox(
              width: 16.w,
            ),
            InkWell(
              child: SvgPicture.asset(
                'packages/oneapp_common/res/image/svg/toyota/${isExpand == false ? 'ic_flex_user_plus' : 'ic_flex_user_minimize'}.svg',
                colorFilter: ColorFilter.mode(
                  ThemeConfig.current().colorUtil.tertiary03,
                  BlendMode.srcIn,
                ),
                width: 24.w,
                height: 24.h,
              ),
              onTap: () {
                isExpand = !isExpand;
                widget.bloc!.showLicense(isExpand);
                // setState(() {});
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _expandedContent() {
    return Visibility(
      visible: isExpand == true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 16.h,
          ),
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: CustomTextField(
                    inputType: TextInputType.name,
                    controller: firstNameController,
                    placeholder: OneAppString.of().driverFirstName,
                    isError: _licenseModel!.invalidFirstName,
                    errMessage: OneAppString.of().invalidFirstname,
                    onChanged: (value) {
                      _licenseModel!.firstName = value;
                      _licenseModel!.hasChanged = true;
                      _licenseModel!.validateFirstName();
                      setState(() {
                        widget.onChanged?.call(_licenseModel);
                      });
                    },
                  ),
                ),
                SizedBox(
                  width: 9.w,
                ),
                Expanded(
                  child: CustomTextField(
                    inputType: TextInputType.name,
                    controller: lastNameController,
                    placeholder: OneAppString.of().driverLastName,
                    isError: _licenseModel!.invalidLastName,
                    errMessage: OneAppString.of().invalidLastname,
                    onChanged: (value) {
                      _licenseModel!.lastName = value;
                      _licenseModel!.hasChanged = true;
                      _licenseModel!.validateLastName();
                      setState(() {
                        widget.onChanged?.call(_licenseModel);
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 8.h,
          ),
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: CustomTextField(
                    placeholder: OneAppString.of().dob,
                    isError: _licenseModel!.invalidBirthDate,
                    errMessage: OneAppString.of().mustbe18,
                    isEditable: false,
                    controller:
                        TextEditingController(text: _licenseModel!.birthDate),
                    onClick: () {
                      showDatePicker(
                        context: context,
                        builder: (BuildContext context, Widget? child) {
                          return Theme(
                            data: ThemeData.light(),
                            child: child!,
                          );
                        },
                        initialDate: _getInitialDate(_licenseModel?.birthDate),
                        firstDate: DateTime(1900),
                        lastDate: DateTime(2100),
                      ).then((dateTime) {
                        _licenseModel!.birthDate =
                            DateFormat('MM/dd/yy').format(dateTime!);
                        _licenseModel!.hasChanged = true;
                        _licenseModel!.validateBirthDate();
                        setState(() {
                          widget.onChanged?.call(_licenseModel);
                        });
                      });
                    },
                  ),
                ),
                SizedBox(
                  width: 8.w,
                ),
                Expanded(
                  child: CustomTextField(
                    paddingLeft: 4.w,
                    paddingRight: 4.w,
                    controller: licenseController,
                    placeholder: OneAppString.of().license2,
                    isError: _licenseModel!.invalidLicense,
                    errMessage: OneAppString.of().invalidLicenseNumber,
                    onChanged: (value) {
                      _licenseModel!.license = value;
                      _licenseModel!.hasChanged = true;
                      _licenseModel!.validateLicense();
                      setState(() {
                        widget.onChanged?.call(_licenseModel);
                      });
                    },
                  ),
                ),
                SizedBox(
                  width: 8.w,
                ),
                Expanded(
                  child: CustomTextField(
                    placeholder: OneAppString.of().expiration,
                    isError: _licenseModel!.invalidExpiration,
                    errMessage: OneAppString.of().expired,
                    paddingLeft: 8.w,
                    paddingRight: 8.w,
                    isEditable: false,
                    controller:
                        TextEditingController(text: _licenseModel!.expiration),
                    onClick: () {
                      showDatePicker(
                        context: context,
                        builder: (BuildContext context, Widget? child) {
                          return Theme(
                            data: ThemeData.light(),
                            child: child!,
                          );
                        },
                        initialDate: _getInitialDate(_licenseModel?.expiration),
                        firstDate: DateTime(1900),
                        lastDate: DateTime(2100),
                      ).then((dateTime) {
                        _licenseModel!.expiration =
                            DateFormat('MM/dd/yy').format(dateTime!);
                        _licenseModel!.hasChanged = true;
                        _licenseModel!.validateExpiration();
                        setState(() {
                          widget.onChanged?.call(_licenseModel);
                        });
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 8.h,
          ),
          CustomTextField(
            inputType: TextInputType.streetAddress,
            controller: addressController,
            placeholder: OneAppString.of().address,
            isError: _licenseModel!.invalidAddress,
            errMessage: OneAppString.of().invalidAddress,
            onChanged: (value) {
              _licenseModel!.address = value;
              _licenseModel!.hasChanged = true;
              _licenseModel!.validateAddress();
              setState(() {
                widget.onChanged?.call(_licenseModel);
              });
            },
          ),
          SizedBox(
            height: 8.h,
          ),
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: cityController,
                    placeholder: OneAppString.of().driverCity,
                    isError: _licenseModel!.invalidCity,
                    errMessage: OneAppString.of().invalidCity,
                    onChanged: (value) {
                      _licenseModel!.city = value;
                      _licenseModel!.hasChanged = true;
                      _licenseModel!.validateCity();
                      setState(() {
                        widget.onChanged?.call(_licenseModel);
                      });
                    },
                  ),
                ),
                SizedBox(
                  width: 9.w,
                ),
                Expanded(
                  child: CustomDropdownButton(
                    itemList:
                        _licenseModel!.country == OneAppString.of().loginCanada
                            ? CANADA_STATE_CODE_LIST
                            : US_STATE_CODE_LIST,
                    selectString: _licenseModel!.getDisplayState(),
                    hintText: OneAppString.of().state,
                    isError: _licenseModel!.invalidState,
                    errMessage: OneAppString.of().invalidState,
                    onChanged: (value) {
                      _licenseModel!.setDisplayState(value);
                      _licenseModel!.hasChanged = true;
                      _licenseModel!.validateState();
                      setState(() {
                        widget.onChanged?.call(_licenseModel);
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 8.h,
          ),
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: CustomTextField(
                    textCapitalization: TextCapitalization.characters,
                    maxLength:
                        _licenseModel!.country == OneAppString.of().loginCanada
                            ? 6
                            : 5,
                    inputType:
                        _licenseModel!.country == OneAppString.of().loginCanada
                            ? null
                            : TextInputType.number,
                    controller: zipCodeController,
                    placeholder: OneAppString.of().zipCode,
                    isError: _licenseModel!.invalidZipCode,
                    errMessage: OneAppString.of().invalidZIP,
                    onChanged: (value) {
                      _licenseModel!.zipCode = value;
                      _licenseModel!.hasChanged = true;
                      _licenseModel!.validateZipCode();
                      setState(() {
                        widget.onChanged?.call(_licenseModel);
                      });
                    },
                  ),
                ),
                SizedBox(
                  width: 9.w,
                ),
                Expanded(
                  child: CustomDropdownButton(
                    itemList: countryList,
                    selectString: _licenseModel?.country,
                    isError: _licenseModel!.invalidCountry,
                    errMessage: OneAppString.of().invalidCountry,
                    hintText: OneAppString.of().country,
                    expandMenu: true,
                    onChanged: (value) {
                      _licenseModel!.country = value;
                      // if (_licenseModel.getDisplayState() == null) {
                      //   _licenseModel.state = null;
                      // }
                      _licenseModel!.validateState();
                      _licenseModel!.validateCountry();
                      _licenseModel!.validateZipCode();
                      _licenseModel!.hasChanged = true;
                      setState(() {
                        widget.onChanged?.call(_licenseModel);
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 4.h,
          ),
        ],
      ),
    );
  }

  _getInitialDate(String? date) {
    DateTime expirationDate = DateTime.now();
    try {
      expirationDate =
          date != "" ? DateFormat('MM/dd/yy').parse(date!) : DateTime.now();
    } catch (e) {
      expirationDate = DateTime.now();
    }
    return expirationDate;
  }
}
