// Package imports:
import 'package:intl/intl.dart';

class InsuranceModel {
  String? company;
  String? policy;
  String? expiration;

  bool invalidCompany;
  bool invalidPolicy;
  bool invalidExpiration;

  bool hasChanged;

  InsuranceModel({
    this.company,
    this.policy,
    this.expiration,
    this.hasChanged = false,
    this.invalidCompany = false,
    this.invalidPolicy = false,
    this.invalidExpiration = false,
  });

  void validateCompany() {
    invalidCompany = !(company?.isNotEmpty == true);
  }

  void validatePolicy() {
    invalidPolicy = !(policy?.isNotEmpty == true);
  }

  void validateExpiration() {
    if (expiration != null) {
      if (expiration != "") {
        invalidExpiration = DateFormat('MM/dd/yy')
                .parse(expiration!)
                .compareTo(DateTime.now()) <=
            0;
      }
    } else {
      invalidExpiration = true;
    }
  }

  bool canSubmit() {
    bool noError = !invalidCompany && !invalidPolicy && !invalidExpiration;
    bool isEmpty = !(company?.isNotEmpty == true) &&
        !(policy?.isNotEmpty == true) &&
        !(expiration?.isNotEmpty == true);
    return noError || isEmpty;
  }

  bool isEmpty() {
    return (company == null || company!.isEmpty) &&
        (policy == null || policy!.isEmpty) &&
        (expiration == null || expiration!.isEmpty);
  }

  void checkAll() {
    validateCompany();
    validatePolicy();
    validateExpiration();
  }
}
