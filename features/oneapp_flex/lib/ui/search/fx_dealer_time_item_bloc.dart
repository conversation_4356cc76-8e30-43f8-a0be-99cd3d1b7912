// Dart imports:
import 'dart:async';

// Package imports:
import 'package:intl/intl.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_station_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/dealer_search_helper.dart';
import 'package:rxdart/rxdart.dart';

class DealerTimeScopeBloc extends BlocBase {
  final _timeScopeListController = BehaviorSubject<List<TimeScope>>();

  Stream<List<TimeScope>> get uiTimeScopeList =>
      _timeScopeListController.stream;

  List<TimeScope> timeScopeList = [];

  @override
  void dispose() {
    _timeScopeListController.close();
  }

  void initData(DealerDetailInfo? item) {
    if (item?.generalBusinessHours?.isNotEmpty == true) {
      Map<int?, TimeScope> resultMap = {};
      final weekday = DateTime.now().weekday;
      for (final e in item!.generalBusinessHours!) {
        final map = _getWeekdayName(e);
        resultMap[e.dayOfWeekOrdinal] = TimeScope(
          index: map['index'],
          weekday: map['value'],
          openTimeScope: _getTimeScope(e),
          hours: e,
          isCurrent: (e.dayOfWeekOrdinal ?? 0) == weekday % 7,
        );
      }
      timeScopeList = resultMap.values.toList();
      timeScopeList.sort((left, right) {
        return left.index?.compareTo(right.index ?? 0) ?? 0;
      });
      _timeScopeListController.add(timeScopeList);
    }
  }

  Map<String, dynamic> _getWeekdayName(Hours hours) {
    String value;
    switch ((hours.dayOfWeekOrdinal ?? 0) % 7) {
      case 1:
        value = OneAppString.of().monday;
        break;
      case 2:
        value = OneAppString.of().tuesday;
        break;
      case 3:
        value = OneAppString.of().wednesday;
        break;
      case 4:
        value = OneAppString.of().thursday;
        break;
      case 5:
        value = OneAppString.of().friday;
        break;
      case 6:
        value = OneAppString.of().saturday;
        break;
      case 0:
        value = OneAppString.of().sunday;
        break;
      default:
        value = '';
        break;
    }
    Map<String, dynamic> map = {};
    map['index'] = hours.dayOfWeekOrdinal;
    map['value'] = value;
    return map;
  }

  String _getTimeScope(Hours hours) {
    try {
      String openField = "";
      if ((hours.openingTime == "00:00" || hours.openingTime == "0000") &&
          (hours.closingTime == "00:00" || hours.closingTime == "0000")) {
        return OneAppString.of().closed.capitalize();
      }
      if (hours.openingTime != null) {
        final openDate = DateFormat("HH:mm").parse(hours.openingTime!);
        openField =
            "${(openDate.hour % 13) + openDate.hour ~/ 13}:${openDate.minute < 10 ? '0${openDate.minute}' : openDate.minute}${(openDate.hour ~/ 13) == 1 ? ' pm' : ' am'}";
      }
      String closeField = "";
      if (hours.closingTime != null) {
        final closeDate = DateFormat("HH:mm").parse(hours.closingTime!);
        closeField =
            "${(closeDate.hour % 13) + (closeDate.hour ~/ 13)}:${closeDate.minute < 10 ? '0${closeDate.minute}' : closeDate.minute}${(closeDate.hour ~/ 13) == 1 ? ' pm' : ' am'}";
      }
      if (openField.isNotEmpty == true || closeField.isNotEmpty == true) {
        return "$openField - $closeField";
      } else {
        return OneAppString.of().closed.capitalize();
      }
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_RENT);
    }
    return OneAppString.of().closed.capitalize();
  }

  bool isOpen(Hours hour) {
    return _betweenDates(hour.fromDate, hour.toDate) &&
        _betweenTimes(hour.openingTime, hour.closingTime);
  }
}

bool _betweenTimes(String? openTime, String? closeTime) {
  try {
    if (openTime != null && closeTime != null) {
      final now = DateTime.now();
      int nowBaseLine = 10000 + now.minute + now.hour * 100;
      int date1 = int.parse("1${openTime.replaceAll(":", "")}");
      int date2 = int.parse("1${closeTime.replaceAll(":", "")}");
      if (date2 < date1 && nowBaseLine <= date1 && nowBaseLine >= date2) {
        return true;
      } else if (date1 < date2 &&
          nowBaseLine >= date1 &&
          nowBaseLine <= date2) {
        return true;
      } else if (date1 == date2) {
        return false;
      }
    }
    return false;
  } catch (e) {
    FireBaseAnalyticsLogger.logError(e.toString(),
        category: LogCategory.FL_RENT);
  }
  return false;
}

bool _betweenDates(String? startDate, String? endDate) {
  try {
    if (startDate != null && endDate != null) {
      final now = DateTime.now();
      int nowBaseLine = 10000 + now.day + now.month * 100;
      int date1 = int.parse("1${startDate.replaceAll(":", "")}");
      int date2 = int.parse("1${endDate.replaceAll(":", "")}");
      if (date2 < date1 && nowBaseLine <= date1 && nowBaseLine >= date2) {
        return true;
      } else if (date1 < date2 &&
          nowBaseLine >= date1 &&
          nowBaseLine <= date2) {
        return true;
      } else if (date1 == date2) {
        return false;
      }
      return false;
    }
    return false;
  } catch (e) {
    FireBaseAnalyticsLogger.logError(e.toString(),
        category: LogCategory.FL_RENT);
  }
  return false;
}
