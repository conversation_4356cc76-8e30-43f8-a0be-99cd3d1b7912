// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/foundation.dart';

// Package imports:
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart' as geolocator;
import 'package:intl/intl.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_station_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/dealer_search_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_cfai_api_client.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

// Project imports:
import '../../flex_global.dart';
import '../../util/fx_analytics_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

const int searchLimitRadius = 3000 * 1610;

const int PAGE_NUM = 10;

class FxSearchDealerBloc implements BlocBase {
  FXCFAIClient cfaiAPI = APIClientConfig.flexClient;

  Position? distanceLocation;

  final _currentLocController = BehaviorSubject<Position?>();

  Stream<Position?> get uiCurrentLocation => _currentLocController.stream;

  final _dealerListController = BehaviorSubject<List<DealerDetailInfo>>();

  Stream<List<DealerDetailInfo>> get uiDealerList =>
      _dealerListController.stream;

  final _isShowSingleDetail = BehaviorSubject<bool>();

  bool get isShowSingleDetailList => _isShowSingleDetail.value;

  Stream<bool> get isShowSingleDetail => _isShowSingleDetail.stream;

  final _titleController = BehaviorSubject<String>();

  Stream<String> get uiTitle => _titleController.stream;

  final _toastController = BehaviorSubject<ToastMsgData>();

  Stream<ToastMsgData> get uiToast => _toastController.stream;

  Position? vehicleLoc;
  List<DealerDetailInfo> filterInfoList = [];
  String? reservationId;
  String? status;

  bool hasError = false;
  bool hasNoSearchResults = false;

  int currentOffset = 0;

  bool isNoMore = false;

  int dealerCount = 0;

  bool getNewData = false;

  void loadData(IntentToSearch? intent) async {
    reservationId = intent?.reservationId;
    status = intent?.status;
    _refreshTitle(intent);
    _isShowSingleDetail.add(false);
    await checkLocationPermission();
  }

  _refreshTitle(IntentToSearch? intent) {
    String date = "";
    if (intent != null && intent.timeData != null) {
      date =
          "${DateFormat("MMM d, h:mm a").format(intent.timeData!.pickUpDateTime!)} - ${DateFormat("MMM d, h:mm a").format(intent.timeData!.dropOffDateTime!)}";
    }
    _titleController.sink.add(date);
  }

  Future parseAddress(String address) async {
    if (address.isNotEmpty) {
      await showProgress(NavigateService.context);
      try {
        List<Location> locations = await locationFromAddress(address);
        final element = locations.first;
        distanceLocation = Position(element.longitude, element.latitude);
        filterInfoList.clear();
        currentOffset = 0;
        isNoMore = false;
        await _getMapStations(
            lat: element.latitude,
            lon: element.longitude,
            radius: searchLimitRadius,
            limitCount: PAGE_NUM,
            offset: currentOffset);
      } on Exception catch (value) {
        debugPrint(value.toString());
        FireBaseAnalyticsLogger.logError(value.toString(),
            category: LogCategory.FL_RENT);
      }
      await dismissProgress(NavigateService.context);
    }
  }

  void getStationsMore() {
    if (distanceLocation == null) {
      return;
    }
    if (isNoMore == true) {
      return;
    }
    _getMapStations(
        lat: distanceLocation?.lat.toDouble(),
        lon: distanceLocation?.lng.toDouble(),
        radius: searchLimitRadius,
        offset: currentOffset * PAGE_NUM,
        limitCount: PAGE_NUM);
  }

  Future _getMapStations({
    double? lat,
    double? lon,
    int? radius,
    String? searchText,
    bool loading = true,
    bool allowCache = false,
    int? limitCount,
    int? offset,
  }) async {
    hasError = false;
    hasNoSearchResults = false;
    if (loading == true && !allowCache) {
      await showProgress(NavigateService.context);
    }
    String? vin = Global.getInstance().vin;
    vehicleInfo.Payload? vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(vin);
    CommonResponse<FxStationResponse> commonResponse =
        await cfaiAPI.sendGetFlexStations(
      lat: lat,
      lon: lon,
      radius: radius,
      searchText: searchText,
      limitCount: limitCount,
      offset: offset.toString(),
      allowCache: allowCache,
      vinBrand: vehicleItem?.brand ?? Global.getInstance().appBrand,
      rentalType: isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
          ? FLEX_RENTALS_EV_SWAP
          : FLEX_RENTALS_RENTAL,
      vin: vehicleItem?.vin ?? Global.getInstance().vin,
    );
    if (loading == true && !allowCache) {
      await dismissProgress(NavigateService.context);
    }
    if (commonResponse.response != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_GET_DEALER_DETAIL_SUCCESS,
          category: LogCategory.FL_RENT);
      if (commonResponse.response?.payload?.listStations != null) {
        await Future.forEach(
            commonResponse.response?.payload?.listStations ?? [],
            (dynamic it) async {
          List<dynamic> latLons = it?.geofences?.first?.polygon?.coordinates;
          num? lat = latLons.first?.first?.last;
          num? lon = latLons.first?.first?.first;
          Position dealerLocation =
              Position(lon?.toDouble() ?? 0.0, lat?.toDouble() ?? 0.0);

          final dealerDetailInfo =
              await _createMapStationDetailInfo(dealerLocation, it);
          filterInfoList.add(dealerDetailInfo);
          if (!_dealerListController.isClosed) {
            _dealerListController.add(filterInfoList);
          }
        });
        if (filterInfoList.isEmpty) {
          hasNoSearchResults = true;
        }
        if (filterInfoList.length <
            (commonResponse.response?.payload?.pagination?.total ?? 0)) {
          currentOffset++;
        } else {
          isNoMore = true;
        }
      }
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          AnalyticsEvent.FLEX_GET_DEALER_DETAIL_FAILURE,
          category: LogCategory.FL_RENT);
    }
    if (commonResponse.error != null) {
      hasError = true;
      if (!_toastController.isClosed) {
        _toastController.add(ToastMsgData(commonResponse.error!.errorMessage!));
      }
    }
  }

  Future<DealerDetailInfo> _createMapStationDetailInfo(
    Position target,
    ListStations it,
  ) async {
    bool isCarShare = it.programIds?.contains(CAR_SHARE_PROGRAM_ID) == true;
    if (distanceLocation != null) {
      double distanceInMeters = 0;
      distanceInMeters = await geolocator.Geolocator.distanceBetween(
        distanceLocation!.lat.toDouble(),
        distanceLocation!.lng.toDouble(),
        target.lat.toDouble(),
        target.lng.toDouble(),
      );
      final disValue =
          double.tryParse((distanceInMeters * 0.0006214).toStringAsFixed(2))!;
      String distance = OneAppString.of().searchMile(disValue);
      return (DealerDetailInfo(
        locationId: it.id,
        locationName: it.name,
        phoneNumber: it.supportPhoneNumber,
        address: it.formattedAddress,
        distance: distance,
        distanceValue: disValue,
        latLng:
            FxLatLng(lat: target.lat.toDouble(), lon: target.lng.toDouble()),
        travelTime: "",
        zipCode: "",
        isCarShare: isCarShare,
        stationDetail: it,
        demoIndex: it.demoIndex,
        totalVehicles: it.totalVehicles ?? 0,
        businessHours: it.hours,
        generalBusinessHours: it.generalBusinessHours,
        timezone: it.timezone,
      ));
    } else {
      return (DealerDetailInfo(
        locationId: it.id,
        locationName: it.name,
        phoneNumber: it.supportPhoneNumber,
        address: it.formattedAddress,
        distance: "",
        latLng: FxLatLng(
          lat: target.lat.toDouble(),
          lon: target.lng.toDouble(),
        ),
        travelTime: "",
        zipCode: "",
        isCarShare: isCarShare,
        stationDetail: it,
        demoIndex: it.demoIndex,
        totalVehicles: it.totalVehicles ?? 0,
        businessHours: it.hours,
        generalBusinessHours: it.generalBusinessHours,
        timezone: it.timezone,
      ));
    }
  }

  Future<void> checkLocationPermission() async {
    if (Global.getInstance().currentLocation != null &&
        !_currentLocController.isClosed) {
      _currentLocController.add(Global.getInstance().currentLocation);
    }
    bool isGranted = await MapUtil.checkLocationGranted();
    if (isGranted) {
      obtainCurrentLocation(allowCache: false);
    }
  }

  Future<void> obtainCurrentLocation(
      {bool showLoading = true, bool allowCache = false}) async {
    var obtaionPosition = await MapUtil.getCurrentLocation();
    if (!_currentLocController.isClosed) {
      _currentLocController.add(obtaionPosition);
    }
    distanceLocation = obtaionPosition;
    Global.getInstance().currentLocation = obtaionPosition;
    filterInfoList.clear();
    currentOffset = 0;
    isNoMore = false;
    await _getMapStations(
        lat: obtaionPosition.lat.toDouble(),
        lon: obtaionPosition.lng.toDouble(),
        radius: searchLimitRadius,
        loading: showLoading,
        allowCache: allowCache,
        limitCount: PAGE_NUM,
        offset: currentOffset);
  }

  void filterById(String? locationId) {
    final List<DealerDetailInfo> list = [];
    filterInfoList.forEach((element) {
      if (element.locationId == locationId) {
        DealerDetailInfo value = element.clone();
        value.showTimeDetail = true;
        list.add(value);
      }
    });
    if (list.first.showTimeDetail) {
      _isShowSingleDetail.add(true);
    } else {
      _isShowSingleDetail.add(false);
    }
    dealerCount = list.length;
    _dealerListController.add(list);
  }

  void resetList() {
    _isShowSingleDetail.add(false);
    dealerCount = 0;
    _dealerListController.add(filterInfoList);
  }

  @override
  void dispose() {
    _isShowSingleDetail.close();
    _dealerListController.close();
    _titleController.close();
    _toastController.close();
    _currentLocController.close();
  }
}
