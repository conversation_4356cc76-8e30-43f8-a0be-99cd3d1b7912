// Dart imports:
import 'dart:io';

// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:collection/collection.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/config/oneapp_ui_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/dealer_search_helper.dart';

typedef OnMapPinTap = void Function(DealerDetailInfo item);

class FXSearchMap extends StatefulWidget {
  final List<DealerDetailInfo?>? dealerDetailInfoList;

  final Position? currentLocation;

  final OnMapPinTap? onMapPinTap;

  final double paddingBottom;

  final double paddingTop;

  final double paddingLeft;

  final double paddingRight;

  const FXSearchMap({
    Key? key,
    this.dealerDetailInfoList,
    this.currentLocation,
    this.onMapPinTap,
    this.paddingBottom = 60,
    this.paddingTop = 60,
    this.paddingLeft = 60,
    this.paddingRight = 60,
  }) : super(key: key);

  @override
  FXSearchMapState createState() => FXSearchMapState();
}

class FXSearchMapState extends State<FXSearchMap> {
  MapboxMap? controller;
  PointAnnotationManager? _pointAnnotationManager;

  GlobalKey mapGlobalKey = GlobalKey();

  double _offsetY = 0;

  Future<void>? addImageFromAsset(
    String name,
    String assetName, {
    required int width,
    required int height,
  }) async {
    final ByteData bytes = await rootBundle.load(assetName);

    final Uint8List list = bytes.buffer.asUint8List();
    return controller?.style.addStyleImage(
        name,
        1,
        MbxImage(data: list, width: width, height: height),
        false,
        [],
        [],
        null);
  }

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = isDarkTheme();
    Future(() {
      addAllPIN();
    });
    return MapWidget(
      key: mapGlobalKey,
      androidHostingMode: AndroidPlatformViewHostingMode.TLHC_HC,
      styleUri: isDarkMode == true ? MapboxStyles.DARK : MapboxStyles.LIGHT,
      onMapCreated: _onMapCreated,
      onStyleLoadedListener: _onStyleLoaded,
      cameraOptions: CameraOptions(
        center: Point(
            coordinates: widget.currentLocation ??
                Position(
                  -73.989551,
                  40.745323,
                )),
        zoom: 2,
      ),
      gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>[
        Factory<OneSequenceGestureRecognizer>(
          () => EagerGestureRecognizer(),
        ),
      ].toSet(),
      onTapListener: (_) {
        FocusScope.of(OneAppUiConfig.appContext).requestFocus(FocusNode());
      },
    );
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  Future<void> _onMapCreated(MapboxMap controller) async {
    this.controller = controller;
    controller.location
        .updateSettings(LocationComponentSettings(enabled: true));
    _pointAnnotationManager =
        await controller.annotations.createPointAnnotationManager();
    _pointAnnotationManager?.setIconAllowOverlap(true);
    _pointAnnotationManager?.addOnPointAnnotationClickListener(
      CallbackOnPointAnnotationClickListener(_onSymbolTapped),
    );
    addAllPIN();
  }

  void _onStyleLoaded([StyleLoadedEventData? data]) {
    addImageFromAsset(
      "pinFlexImage",
      "packages/oneapp_common/res/image/png/toyota/ic_flex_map_pin.webp",
      width: 126,
      height: 126,
    );
  }

  Future<void> addAllPIN({double? dx, double? dy}) async {
    if (controller == null) return;
    if (dy != null && Platform.isAndroid) {
      _offsetY = dy;
    }
    await _removeAll();
    final latLngBoundsBuilder = LatLngBoundsBuilder();

    _onStyleLoaded();

    try {
      if (widget.dealerDetailInfoList != null &&
          widget.dealerDetailInfoList!.isNotEmpty) {
        await _pointAnnotationManager?.createMulti(widget.dealerDetailInfoList!
            .where((e) => e?.latLng != null)
            .mapIndexed(
          (i, info) {
            String iconImage = 'pinFlexImage';
            latLngBoundsBuilder.includeLatLon(
                info!.latLng!.lat!, info.latLng!.lon!);
            final options = PointAnnotationOptions(
              symbolSortKey: i.toDouble(),
              iconSize: 0.3,
              textAnchor: TextAnchor.BOTTOM,
              textField: '',
              textSize: 14,
              textMaxWidth: 50,
              textOffset: [0.0, -0.5],
              textColor: 0xFFFFFFFF,
              iconAnchor: IconAnchor.BOTTOM,
              geometry: Point(
                  coordinates: Position(info.latLng!.lon!, info.latLng!.lat!)),
              iconImage: iconImage,
            );

            return options;
          },
        ).toList());
      }
    } catch (e) {
      debugPrint(e.toString());
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_RENT);
    }

    final latLngBounds = latLngBoundsBuilder.build();
    if (controller != null) {
      if (widget.dealerDetailInfoList != null &&
          widget.dealerDetailInfoList!.isNotEmpty) {
        if (widget.dealerDetailInfoList!.length == 1) {
          double deltaY = (widget.paddingBottom) - (widget.paddingTop);
          await _executeCamera(
            CameraOptions(
              center: Point(
                coordinates: Position(
                  latLngBoundsBuilder.points.first.lng,
                  latLngBoundsBuilder.points.first.lat,
                ),
              ),
              zoom: 13,
            ),
            animation: deltaY == 0,
          );
          if (deltaY != 0) {
            await offsetY(deltaY);
          }
        } else {
          await _executeCamera(
            CameraOptions(
                center: Point(
                  coordinates: getCenter(latLngBounds),
                  bbox: latLngBounds,
                ),
                zoom: LatLngBoundsBuilder.getMapBoundZoom(
                  bounds: latLngBounds,
                ),
                padding: MbxEdgeInsets(
                  left: widget.paddingLeft,
                  right: widget.paddingRight,
                  top: widget.paddingTop,
                  bottom: (_offsetY == 0 || Platform.isIOS)
                      ? widget.paddingBottom
                      : _offsetY,
                )),
            animation: true,
          );
        }
      } else {
        if (widget.currentLocation != null) {
          await _executeCamera(
            CameraOptions(
              center: Point(
                coordinates: Position(
                  widget.currentLocation!.lng,
                  widget.currentLocation!.lat,
                ),
              ),
              zoom: 13,
            ),
            animation: true,
          );
        }
      }
      if (dy != null && Platform.isIOS) {
        await offsetY(dy);
      }
    }
  }

  Future<void> offsetY(double y) async {
    if (widget.dealerDetailInfoList?.isNotEmpty == true &&
        controller != null &&
        _offsetY != y) {
      debugPrint(' ------ _offsetY: $_offsetY ---- y: $y');
      double yValue = (_offsetY - y) / 2;
      _offsetY = y;
      await animateOffset(0.0, Platform.isIOS ? -yValue : yValue);
    }
  }

  _executeCamera(CameraOptions cameraOptions, {bool animation = false}) async {
    Future.delayed(Duration(milliseconds: 100), () {
      if (animation && Platform.isAndroid) {
        controller!.flyTo(
          cameraOptions,
          MapAnimationOptions(),
        );
      } else {
        controller!.setCamera(cameraOptions);
      }
    });
  }

  Future<void> animateOffset(double dx, double dy) async {
    final current = (await controller?.getCameraState())?.toCameraOptions();
    final center = current?.center;
    final newCenter = center == null
        ? null
        : Point(
            coordinates: Position(
              center.coordinates.lng + dy,
              center.coordinates.lat + dx,
            ),
          );

    return await controller?.flyTo(
      CameraOptions(
        zoom: current?.zoom,
        anchor: current?.anchor,
        bearing: current?.bearing,
        center: newCenter,
        padding: current?.padding,
        pitch: current?.pitch,
      ),
      MapAnimationOptions(),
    );
  }

  Future _removeAll() async {
    await removeSymbolsFromMapboxController(_pointAnnotationManager!);
  }

  void _onSymbolTapped(PointAnnotation annotation) {
    DealerDetailInfo? it;
    try {
      if (annotation.symbolSortKey != -1 &&
          annotation.symbolSortKey != -2 &&
          widget.dealerDetailInfoList != null) {
        for (int i = 0; i < widget.dealerDetailInfoList!.length; i++) {
          if (i == annotation.symbolSortKey) {
            it = widget.dealerDetailInfoList![i];
            break;
          }
        }
      }
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_RENT);
    }
    if (it != null) {
      widget.onMapPinTap?.call(it);
    }
  }
}
