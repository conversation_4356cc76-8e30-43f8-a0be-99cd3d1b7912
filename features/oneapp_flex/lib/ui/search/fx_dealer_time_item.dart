// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/dealer_search_helper.dart';

// Project imports:
import 'fx_dealer_time_item_bloc.dart';

typedef DealerTimeChange = Function(bool showDetail);

class DealerTimeScope extends StatefulWidget {
  final DealerDetailInfo? item;

  const DealerTimeScope({Key? key, this.item}) : super(key: key);

  @override
  _DealerTimeScopeState createState() => _DealerTimeScopeState();
}

class _DealerTimeScopeState extends State<DealerTimeScope> {
  final DealerTimeScopeBloc _bloc = DealerTimeScopeBloc();

  DealerDetailInfo? item;

  @override
  Widget build(BuildContext context) {
    item = widget.item;
    _bloc.initData(item);
    return BlocProvider(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          // setState(() {
          //   _showTimeDetail = !_showTimeDetail;
          //   widget.onDealerTimeChanged?.call(_showTimeDetail);
          // });
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _displayOpenClose(),
                SizedBox(width: 4),
                Expanded(child: Container()),
              ],
            ),
            Visibility(
              visible: item!.showTimeDetail == true,
              child: _bloc.timeScopeList.isNotEmpty == true
                  ? ListView.separated(
                      padding: EdgeInsets.only(top: 7),
                      physics: NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemBuilder: (ctx, index) {
                        final item = _bloc.timeScopeList[index];
                        return _availableTimeItem(
                          item.weekday,
                          item.openTimeScope,
                          item.isCurrent!,
                        );
                      },
                      separatorBuilder: (ctx, index) {
                        return SizedBox(height: 2);
                      },
                      itemCount: _bloc.timeScopeList.length,
                    )
                  : Container(),
            ),
          ],
        ),
      ),
      bloc: _bloc,
    );
  }

  Widget _displayOpenClose() {
    return item!.isOpen()
        ? Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: OneAppString.of().openNow,
                  style: ThemeConfig.current().textStyleUtil.caption1.copyWith(
                        color: ThemeConfig.current().colorUtil.secondary01,
                        fontSize: 14.sp,
                      ),
                ),
              ],
            ),
          )
        : Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: OneAppString.of().closed.capitalize(),
                  style: ThemeConfig.current().textStyleUtil.caption1.copyWith(
                        color: ThemeConfig.current().colorUtil.secondary01,
                        fontSize: 14.sp,
                      ),
                ),
                TextSpan(
                  text: " • ",
                  style: ThemeConfig.current().textStyleUtil.caption1.copyWith(
                        color: ThemeConfig.current().colorUtil.tertiary05,
                        fontSize: 14.sp,
                      ),
                ),
                TextSpan(
                  text: '${OneAppString.of().opens}',
                  style: ThemeConfig.current().textStyleUtil.caption1.copyWith(
                        color: ThemeConfig.current().colorUtil.tertiary05,
                        fontSize: 14.sp,
                      ),
                ),
                TextSpan(
                  text: " ${item!.getOpenTime()}",
                  style: ThemeConfig.current().textStyleUtil.caption1.copyWith(
                        color: ThemeConfig.current().colorUtil.tertiary05,
                        fontSize: 14.sp,
                      ),
                )
              ],
            ),
          );
  }

  Widget _availableTimeItem(
      String? weekday, String? openTimeScope, bool isCurrent) {
    final style =
        isCurrent && openTimeScope != OneAppString.of().closed.capitalize()
            ? ThemeConfig.current().textStyleUtil.caption1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary03,
                )
            : ThemeConfig.current().textStyleUtil.caption1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary07,
                );
    final openTimeStyle = isCurrent == true &&
            openTimeScope != OneAppString.of().closed.capitalize()
        ? ThemeConfig.current().textStyleUtil.caption1.copyWith(
              color: ThemeConfig.current().colorUtil.tertiary03,
            )
        : ThemeConfig.current().textStyleUtil.caption1.copyWith(
              color: ThemeConfig.current().colorUtil.tertiary07,
            );
    return Row(
      children: [
        Expanded(
          child: Text(
            weekday ?? '',
            style: style,
            softWrap: true,
            textAlign: TextAlign.start,
          ),
        ),
        Expanded(
          child: Text(
            openTimeScope ?? '',
            style: openTimeStyle,
            softWrap: true,
            textAlign: TextAlign.start,
          ),
        ),
      ],
    );
  }
}
