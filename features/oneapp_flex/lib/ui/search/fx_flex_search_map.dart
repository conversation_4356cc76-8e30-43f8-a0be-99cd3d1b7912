// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/card/simple_card.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/dealer_search_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/rent_car_info_helper.dart';
import 'package:sliding_sheet/sliding_sheet.dart';

// Project imports:
import '../../route/router.dart';
import '../../util/fx_analytics_event.dart';
import '../../widget/flex_slide_title_widget.dart';
import 'fx_dealer_time_item.dart';
import 'fx_search_dealer_bloc.dart';
import 'fx_search_map.dart';

class FxSearchMapLayer extends StatefulWidget {
  final IntentToSearch? intent;

  const FxSearchMapLayer({Key? key, this.intent}) : super(key: key);

  @override
  _FxSearchMapLayerState createState() => _FxSearchMapLayerState();
}

class _FxSearchMapLayerState extends State<FxSearchMapLayer> {
  FxSearchDealerBloc _bloc = FxSearchDealerBloc();

  final _controller = TextEditingController();

  final SheetController _scrollSheetController = SheetController();

  final GlobalKey<FXSearchMapState> _mapKey = GlobalKey();

  final GlobalKey _slideHeaderKey = GlobalKey();

  ValueNotifier<SheetState> sheetState = ValueNotifier(SheetState.inital());

  SheetState get state => sheetState.value;

  set state(SheetState value) => sheetState.value = value;

  bool get isAtBottom => state.isAtBottom;

  DealerDetailInfo? firstItem;

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logMarketingEvent(
        AnalyticsEvent.FLEX_SEARCH_DEALER_PAGE);
    Future(() {
      _bloc.loadData(widget.intent);
    });
    _bloc.uiToast.listen((event) {
      showSimpleToast(true, event.message);
    });
    _bloc.uiDealerList.listen((event) {
      Future.delayed(Duration(milliseconds: 400), () {
        try {
          if (event.isEmpty) {
            _scrollSheetController.snapToExtent(0.6);
          } else {
            _scrollSheetController.rebuild();
          }
        } catch (_) {}
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      child: Scaffold(
        backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
        body: SlidingSheet(
            color: ThemeConfig.current().colorUtil.tertiary15,
            cornerRadiusOnFullscreen: 0,
            addTopViewPaddingOnFullscreen: true,
            controller: _scrollSheetController,
            elevation: 0,
            cornerRadius: 30,
            snapSpec: const SnapSpec(
              snap: true,
              positioning: SnapPositioning.relativeToAvailableSpace,
              snappings: const [
                SnapSpec.headerSnap,
                0.6,
                SnapSpec.expanded,
              ],
            ),
            body: StreamBuilder<List<DealerDetailInfo>>(
                stream: _bloc.uiDealerList,
                builder: (ctx, AsyncSnapshot<List<DealerDetailInfo>> snapData) {
                  if (snapData.hasData) {
                    firstItem = snapData.data!.isNotEmpty == true
                        ? snapData.data!.first
                        : null;
                    return _mapArea(snapData.data!);
                  } else {
                    return Container();
                  }
                }),
            headerBuilder: (context, state) {
              return Column(
                key: _slideHeaderKey,
                mainAxisSize: MainAxisSize.min,
                children: [
                  FlexSlideTitle(
                    title: OneAppString.of().selectADealer,
                    onBackPressed: () {
                      if (_bloc.isShowSingleDetailList) {
                        _bloc.resetList();
                      } else {
                        FlexRouter.popRoute();
                      }
                    },
                    menuItem: Text(OneAppString.of().oneOf3,
                        style: ThemeConfig.current().textStyleUtil.body1),
                  ),
                  _searchArea(),
                  SizedBox(height: 16.h),
                ],
              );
            },
            footerBuilder: (context, state) {
              return _footerButton();
            },
            builder: (context, state) {
              return _contentList();
            },
            listener: (state) {
              final needsRebuild = (this.state.isAtBottom != state.isAtBottom);
              this.state = state;
              if (needsRebuild) {
                if (state.isAtBottom == true &&
                    state.isAtTop == false &&
                    _bloc.currentOffset > 0 &&
                    _bloc.dealerCount != 1) {
                  _bloc.getNewData = true;
                  _bloc.getStationsMore();
                } else {
                  _bloc.getNewData = false;
                }
              }
            }),
      ),
      bloc: _bloc,
    );
  }

  Widget _footerButton() {
    return StreamBuilder<bool>(
        stream: _bloc.isShowSingleDetail,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data == true) {
            return Padding(
              padding: EdgeInsets.only(bottom: 32.h, top: 29.h),
              child: SizedBox(
                width: 192.w,
                height: 52.h,
                child: CustomDefaultButton(
                  backgroundColor: ThemeConfig.current().colorUtil.button01b,
                  buttonTextColor: ThemeConfig.current().colorUtil.button01a,
                  borderColor: ThemeConfig.current().colorUtil.button01b,
                  disabledBackgroundColor:
                      ThemeConfig.current().colorUtil.button02c,
                  text: OneAppString.of().selectDealer,
                  verticalPadding: 4.h,
                  press: () {
                    _onItemClick(context, firstItem);
                  },
                ),
              ),
            );
          } else {
            return Container(
              width: 0.w,
              height: 0.h,
            );
          }
        });
  }

  Widget _mapArea(List<DealerDetailInfo>? dealerDetailInfo) {
    return StreamBuilder(
      stream: _bloc.uiCurrentLocation,
      builder: (ctx, snapData) {
        return FXSearchMap(
          key: _mapKey,
          dealerDetailInfoList: dealerDetailInfo,
          currentLocation: snapData.data,
          onMapPinTap: (item) {
            _bloc.filterById(item.locationId);
          },
        );
      },
    );
  }

  Widget _searchArea() {
    return Container(
      height: 56.h,
      margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 0),
      decoration: BoxDecoration(
        color: ThemeConfig.current().colorUtil.tertiary12,
        borderRadius: BorderRadius.all(Radius.circular(50.r)),
      ),
      alignment: Alignment.center,
      child: TextField(
        style:
            ThemeConfig.current().textStyleUtil.body3.copyWith(fontSize: 16.sp),
        autocorrect: false,
        textAlignVertical: TextAlignVertical.center,
        decoration: InputDecoration(
          prefixIcon: Icon(
            Icons.search,
            color: ThemeConfig.current().colorUtil.tertiary03,
          ),
          border: InputBorder.none,
          hintText: OneAppString.of().searchNameCity,
          hintStyle: ThemeConfig.current().textStyleUtil.body3.copyWith(
                color: ThemeConfig.current().colorUtil.tertiary05,
                fontSize: 16.sp,
              ),
        ),
        textInputAction: TextInputAction.search,
        controller: _controller,
        onSubmitted: (value) {
          _bloc.parseAddress(value);
        },
      ),
    );
  }

  Widget _contentList() {
    return StreamBuilder<List<DealerDetailInfo>>(
        stream: _bloc.uiDealerList,
        builder: (ctx, AsyncSnapshot<List<DealerDetailInfo>> dealerDetailInfo) {
          if (dealerDetailInfo.hasData) {
            if (!_bloc.getNewData) {
              _scrollSheetController.snapToExtent(0.6);
              _bloc.getNewData = false;
            }
            return Container(
              child: dealerDetailInfo.data?.isNotEmpty == true
                  ? ListView.separated(
                      padding: EdgeInsets.only(
                          bottom: 24.h, top: 0.h, left: 16.w, right: 16.w),
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: dealerDetailInfo.data!.length,
                      itemBuilder: (BuildContext context, int position) {
                        return _dealerItem(context,
                            dealerDetailInfo.data![position], position);
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return SizedBox(
                          height: 16.h,
                        );
                      },
                    )
                  : _exceptionResult(
                      searchNearbyClick: () {
                        _bloc.loadData(widget.intent);
                      },
                    ),
            );
          } else {
            return Container();
          }
        });
  }

  Widget _dealerItem(BuildContext context, DealerDetailInfo item, int index) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        _bloc.filterById(item.locationId);
      },
      child: commonSimpleCard(
          child: Container(
        padding: EdgeInsets.fromLTRB(16.w, 15.h, 16.w, 11.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Expanded(
                  child: Container(
                    margin: EdgeInsets.fromLTRB(0, 0, 4, 0),
                    child: Text(
                      item.locationName ?? "",
                      style: ThemeConfig.current().textStyleUtil.body4,
                    ),
                  ),
                ),
                Text(
                  item.distance ?? "",
                  style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                        color: ThemeConfig.current().colorUtil.tertiary05,
                      ),
                )
              ],
            ),
            SizedBox(
              height: 4.h,
            ),
            Text(
              item.address?.replaceAll("\n", "") ?? "",
              style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                    color: ThemeConfig.current().colorUtil.tertiary05,
                  ),
            ),
            Visibility(
              visible: item.showOpenClosed(),
              child: Container(
                height: 1,
                color: Color(0xFFDEDEDE),
                margin: EdgeInsets.only(top: 21.h, bottom: 9.h),
              ),
            ),
            Visibility(
              visible: item.showOpenClosed(),
              child: DealerTimeScope(
                item: item,
              ),
            ),
          ],
        ),
      )),
    );
  }

  Future<void> _onItemClick(
      BuildContext context, DealerDetailInfo? item) async {
    final intentData = IntentToSelectDate(
      reservationId: _bloc.reservationId,
      status: _bloc.status,
      orgID: item?.stationDetail?.organizationId,
      comeFrom: widget.intent?.comeFrom ?? comeFromLanding,
      dealerInfo: DealerInfo(
        dealerName: item?.locationName,
        dealerID: item?.locationId,
        dealerGeo:
            Geo(item!.latLng!.lat!.toString(), item.latLng!.lon!.toString()),
        dealerPhone: item.phoneNumber,
        stationId: item.stationDetail?.id,
        businessHours: item.businessHours,
      ),
      demoIndex: item.demoIndex,
      dealerDetailInfo: item,
      requestCode: widget.intent?.requestCode,
    );
    if (widget.intent?.comeFrom == comeFromSelectVehicle) {
      IntentToSelectVehicle data = IntentToSelectVehicle(
        status: _bloc.status,
        reservationId: _bloc.reservationId,
        orgID: item.stationDetail?.organizationId,
        businessHour: item.businessHours,
        dealerId: item.locationId,
        stationId: item.stationDetail!.id,
        phone: item.phoneNumber,
        locationName: item.locationName,
        formattedAddress: item.address,
        dealerDetailInfo: item,
        demoIndex: item.demoIndex,
        resetVehicle: widget.intent?.requestCode,
        comeFrom: widget.intent?.comeFrom ?? comeFromLanding,
      );
      FlexRouter.popRoute(result: data);
    } else {
      final result = await FlexRouter.pushName(RoutePath.FX_CALENDAR_VIEW,
          arguments: intentData);
      if (result != null && result is Map) {
        FlexRouter.popRoute(result: result);
      }
    }
  }

  Widget _exceptionResult({VoidCallback? searchNearbyClick}) {
    return _bloc.hasError == true
        ? _errorResult(searchNearbyClick: searchNearbyClick)
        : _bloc.hasNoSearchResults == true
            ? _noResult(searchNearbyClick: searchNearbyClick)
            : Container();
  }

  Widget _noResult({VoidCallback? searchNearbyClick}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 43.h,
          ),
          SizedBox(
            width: 48.w,
            height: 48.w,
            child: CommonCircleIconImage(
              'packages/oneapp_common/res/image/svg/toyota/ic_flex_error_search.svg',
              iconWidth: 24.w,
              iconHeight: 24.w,
              iconPadding: EdgeInsets.all(12.w),
              circleBackgroundColor: ThemeConfig.current().colorUtil.primary02,
              iconTintColor: ThemeConfig.current().colorUtil.primary01,
            ),
          ),
          SizedBox(
            height: 24.h,
          ),
          Text(
            OneAppString.of().noResultsFound,
            style: ThemeConfig.current().textStyleUtil.subHeadline1,
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 16.h,
          ),
          Text(
            OneAppString.of().searchMapTips,
            style: ThemeConfig.current()
                .textStyleUtil
                .body1
                .copyWith(color: ThemeConfig.current().colorUtil.tertiary07),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 56.h,
          ),
          SizedBox(
            width: 192.w,
            height: 52.h,
            child: CustomDefaultButton(
              backgroundColor: ThemeConfig.current().colorUtil.button01b,
              buttonTextColor: ThemeConfig.current().colorUtil.button01a,
              borderColor: ThemeConfig.current().colorUtil.button01b,
              disabledBackgroundColor:
                  ThemeConfig.current().colorUtil.button02c,
              text: OneAppString.of().searchNearby,
              verticalPadding: 4.h,
              primaryButtonState: searchNearbyClick != null
                  ? PrimaryButtonState.ACTIVE
                  : PrimaryButtonState.INACTIVE,
              press: searchNearbyClick,
            ),
          ),
          SizedBox(
            height: 32.h,
          ),
        ],
      ),
    );
  }

  Widget _errorResult({VoidCallback? searchNearbyClick}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 55.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 23.h,
          ),
          SizedBox(
            height: 48.w,
            width: 48.w,
            child: CommonCircleIconImage(
              'packages/oneapp_common/res/image/svg/toyota/ic_flex_landing_error.svg',
              iconWidth: 24.w,
              iconHeight: 24.w,
              iconPadding: EdgeInsets.all(12.w),
              iconTintColor: ThemeConfig.current().colorUtil.primary01,
              circleBackgroundColor: ThemeConfig.current().colorUtil.primary02,
            ),
          ),
          SizedBox(
            height: 24.h,
          ),
          Text(
            OneAppString.of().searchError,
            style: ThemeConfig.current().textStyleUtil.body4,
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 8.h,
          ),
          Text(
            OneAppString.of().searchMapTipsTwo,
            style: ThemeConfig.current()
                .textStyleUtil
                .callout1
                .copyWith(color: ThemeConfig.current().colorUtil.tertiary05),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 172.h,
          ),
          SizedBox(
            width: 192.w,
            height: 52.h,
            child: CustomDefaultButton(
              backgroundColor: ThemeConfig.current().colorUtil.button01b,
              buttonTextColor: ThemeConfig.current().colorUtil.button01a,
              borderColor: ThemeConfig.current().colorUtil.button01b,
              disabledBackgroundColor:
                  ThemeConfig.current().colorUtil.button02c,
              text: OneAppString.of().searchNearby,
              verticalPadding: 4.h,
              primaryButtonState: searchNearbyClick != null
                  ? PrimaryButtonState.ACTIVE
                  : PrimaryButtonState.INACTIVE,
              press: searchNearbyClick,
            ),
          ),
          SizedBox(
            height: 32.h,
          ),
        ],
      ),
    );
  }
}
