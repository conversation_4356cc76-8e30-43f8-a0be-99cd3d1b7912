// Package imports:

// Package imports:
import 'package:intl/intl.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_station_pricing_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/filters_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/rent_car_info_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_cfai_api_client.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

// Project imports:
import '../../flex_global.dart';
import '../../util/fx_analytics_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class FxSelectVehicleNewBloc extends BlocBase {
  final _filterListController = BehaviorSubject<List<CheckBoxFilter>>();

  Stream<List<CheckBoxFilter>> get uiFilterList => _filterListController.stream;

  final _rentCarListController = BehaviorSubject<List<RentCarInfo>>();

  Stream<List<RentCarInfo>> get uiRentCarList => _rentCarListController.stream;

  final _toastController = BehaviorSubject<ToastMsgData>();

  Stream<ToastMsgData> get uiToast => _toastController.stream;

  final _showEmptyUICtrl = BehaviorSubject<bool>();

  Stream<bool> get showEmptyUI => _showEmptyUICtrl.stream;

  FXCFAIClient api = APIClientConfig.flexClient;

  String? dealerPhone = "";
  IntentToSelectVehicle? intentData;
  List<CheckBoxFilter> filterList = [];
  List<RentCarInfo> rentCarInfoList = [];
  vehicleInfo.Payload? vehicleItem;

  Future<void> loadData(IntentToSelectVehicle? data) async {
    intentData = data;
    dealerPhone = intentData!.phone;

    final timeData = intentData!.timeData;
    DateTime? pickUpDate = (timeData?.pickUpDateTime);
    DateTime? dropOffDate = (timeData?.dropOffDateTime);

    int? dealerTimezoneMinOffset =
        intentData!.dealerDetailInfo?.getTimezoneMinutesOffset();
    DateTime requestPickUpDate = (_convertDealerTime(
      timeData!.pickUpDateTime!,
      dealerTimezoneMinOffset!,
    ));
    DateTime requestDropOffDate = (_convertDealerTime(
        timeData.dropOffDateTime ?? DateTime.now(), dealerTimezoneMinOffset));
    String? vin = Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);
    await showProgress(NavigateService.context);
    CommonResponse<FxStationPricingGroupResponse> vehicleResponse =
        await api.sendGetFlexStationPricing(
      intentData!.stationId!,
      intentData!.orgID ?? ORG_ID,
      isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
          ? FLEX_RENTALS_EV_SWAP
          : FLEX_RENTALS_RENTAL,
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(requestPickUpDate),
      DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(requestDropOffDate),
      vehicleItem?.brand ?? Global.getInstance().appBrand,
      vehicleItem?.vin ?? Global.getInstance().vin,
      demoIndex: intentData?.demoIndex,
      allowCache: false,
    );
    await dismissProgress(NavigateService.context);
    if (vehicleResponse.response != null) {
      final vehicleClasses = vehicleResponse.response!;
      List<RentCarInfo> rentCarInfo = [];
      filterList.clear();
      if (vehicleClasses.payload?.pricingGroups?.isNotEmpty == true &&
          vehicleClasses.payload?.pricingGroups?.first.rppPricingDetails
                  ?.isNotEmpty ==
              true) {
        vehicleClasses.payload?.pricingGroups?.first.rppPricingDetails
            ?.forEach((element) {
          String? fuelCapacity;
          String? seatNumber;
          String? luggageCount;
          if (element.vehicleDetails != null) {
            var filterItem = CheckBoxFilter(
                isCheck: false,
                enable: true,
                title: element.vehicleDetails!.vehicleGroup ?? "");
            initFilterList(filterItem);
            fuelCapacity = element.vehicleDetails!.mpgCity != null
                ? "${element.vehicleDetails!.mpgCity ?? '-'}/${element.vehicleDetails!.mpgHighway ?? '-'} ${OneAppString.of().eSTMPG}"
                : "-/- ${OneAppString.of().eSTMPG}";
            seatNumber = element.vehicleDetails!.passengers != null
                ? "${element.vehicleDetails!.passengers} ${OneAppString.of().passengers}"
                : "- ${OneAppString.of().passengers}";
            luggageCount = element.vehicleDetails!.luggage != null
                ? "${element.vehicleDetails!.luggage} ${OneAppString.of().bags}"
                : "- ${OneAppString.of().bags}";
          }

          var item = RentCarInfo(
            status: intentData!.status,
            reservationId: intentData!.reservationId,
            model: element.vehicleDetails?.modelDescription,
            pickUpDate: timeData.pickUpDate,
            dropOffDate: timeData.dropOffDate,
            pickUpTime: timeData.pickUpTime,
            dropOffTime: timeData.dropOffTime,
            locationName: intentData?.locationName,
            locationId: intentData?.stationId,
            address: intentData?.formattedAddress,
            pricePerWithTax: _getDailyPrice(element, true),
            pricePer: _getDailyPrice(element, false),
            // totalTaxes: element.pricing[0].charges.totalTaxes.amount,
            fuelCapacity: fuelCapacity ?? '--',
            seatNumber: seatNumber ?? '--',
            luggage: luggageCount ?? '--',
            totalCharge: _getTotalPrice(element),
            vehiclePicture: element.vehicleDetails?.largeImgURL,
            pastPickTime: pickUpDate,
            pastDropTime: dropOffDate,
            vehicleClass: element.vehicleDetails?.classCode,
            vehicleGroup: element.vehicleDetails?.vehicleGroup,
            // classId: element.id,
            // pricingId: element.pricing[0].id,
            destinationStationId: intentData?.stationId,
            details: element.vehicleDetails?.modelDescription,
            originStationId: intentData?.stationId,
            type: "RESERVATION",
            pricingId: null,
            pricingGroupId: null,
            driverId: Global.getInstance().guid,
            vehicleId: null,
            dealerPhone: dealerPhone,
            organizationId: intentData?.orgID ?? ORG_ID,
            // geo: geo,
            businessHour: intentData!.businessHour,
            rppPricingDetails: element,
            dealerDetailInfo: intentData!.dealerDetailInfo,
          );
          rentCarInfo.add(item);
        });
        rentCarInfoList.clear();
        rentCarInfoList.addAll(rentCarInfo);
      }

      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_GET_VEHICLE_CLASSES_SUCCESS,
          category: LogCategory.FL_RENT);
    } else {
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_GET_VEHICLE_CLASSES_FAILURE,
          category: LogCategory.FL_RENT);
    }
    _filterListController.add(filterList);
    _rentCarListController.add(rentCarInfoList);
    if (vehicleResponse.error != null) {
      _toastController.add(ToastMsgData(vehicleResponse.error!.errorMessage!));
    }
    _showEmptyUICtrl.add(rentCarInfoList.isEmpty);
  }

  void initFilterList(CheckBoxFilter filterItem) {
    var canAdd = true;
    if (filterList.isEmpty) {
      filterList.add(filterItem);
    }
    filterList.forEach((element) {
      if (element.title == filterItem.title) {
        canAdd = false;
      }
    });
    if (canAdd) {
      filterList.add(filterItem);
    }
  }

  void updateFilter(List<CheckBoxFilter> filters) {
    _filterListController.add(filters);
    doFilter();
  }

  void doFilter() {
    final list = rentCarInfoList
        .where((element) => _isRetain(element, getFilteredList()))
        .toList();
    if (list.isEmpty) {
      list.clear();
      list.addAll(rentCarInfoList);
    }
    _rentCarListController.add(list);
  }

  List<CheckBoxFilter> getFilteredList() {
    final results = <CheckBoxFilter>[];
    results.addAll(filterList);
    results.retainWhere((element) => element.isCheck == true);
    return results;
  }

  @override
  void dispose() {
    _filterListController.close();
    _rentCarListController.close();
    _showEmptyUICtrl.close();
  }
}

String? _getDailyPrice(RppPricingDetails element, bool withTax) {
  double rateDayFinal = 0.0;
  if (withTax) {
    if (element.ratePeriod != null) {
      rateDayFinal =
          double.tryParse((element.ratePeriod?.rate1PerDayWithTax ?? "0")) ??
              0.0;
    }
  } else {
    if (element.ratePeriod != null) {
      rateDayFinal =
          double.tryParse((element.ratePeriod?.rate1PerDay ?? "0")) ?? 0.0;
      if (rateDayFinal == 0.0) {
        rateDayFinal =
            double.tryParse((element.ratePeriod?.rate2PerDay ?? "0")) ?? 0.0;
      }
    }
  }

  double rateHour =
      double.tryParse((element.ratePeriod?.amtPerHour ?? "0")) ?? 0.0;
  double rateWeek =
      double.tryParse((element.ratePeriod?.amtPerWeek ?? "0")) ?? 0.0;
  double rateMonth =
      double.tryParse((element.ratePeriod?.amtPerMonth ?? "0")) ?? 0.0;
  if (rateDayFinal != 0.0) {
    return "\$${rateDayFinal.toStringAsFixed(2)} / ${OneAppString.of().commonDay}";
  } else if (rateHour != 0.0) {
    return "\$${rateHour.toStringAsFixed(2)} / ${OneAppString.of().commonHour}";
  } else if (rateWeek != 0.0) {
    return "\$${rateWeek.toStringAsFixed(2)} / ${OneAppString.of().commonWeek}";
  } else {
    return "\$${rateMonth.toStringAsFixed(2)} / ${OneAppString.of().commonMonth}";
  }
}

String? _getTotalPrice(RppPricingDetails element) {
  if (element.totalPricing?.totalCharges != null) {
    return "\$${element.totalPricing?.totalCharges}";
  }
  return null;
}

bool _isRetain(RentCarInfo rentCarInfo, List<CheckBoxFilter> list) {
  if (list.isNotEmpty == true) {
    bool isInList = false;
    list.forEach((element) {
      if (rentCarInfo.vehicleGroup == element.title) {
        isInList = true;
        return;
      }
    });
    return isInList;
  }
  return true;
}

DateTime _convertDealerTime(
    DateTime dealerDateTime, int dealerTimezoneMinOffset) {
  return dealerDateTime.add(Duration(minutes: -dealerTimezoneMinOffset));
}
