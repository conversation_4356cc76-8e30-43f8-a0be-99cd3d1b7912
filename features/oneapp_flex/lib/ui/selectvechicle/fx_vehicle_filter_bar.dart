// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/filters_data_helper.dart';

class FxVehicleFilterBar extends StatefulWidget {
  final Function(List<CheckBoxFilter>? filtered)? onFilterChanged;

  final List<CheckBoxFilter>? filters;

  const FxVehicleFilterBar({Key? key, this.onFilterChanged, this.filters})
      : super(key: key);

  @override
  _FxVehicleFilterBarState createState() => _FxVehicleFilterBarState();
}

class _FxVehicleFilterBarState extends State<FxVehicleFilterBar> {
  List<CheckBoxFilter>? _filters;

  @override
  void initState() {
    super.initState();
    _filters = widget.filters;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 44.h,
      child: ListView.separated(
        padding: EdgeInsets.only(left: 16.w, right: 16.w),
        scrollDirection: Axis.horizontal,
        itemBuilder: (ctx, index) {
          return _filterItem(index);
        },
        separatorBuilder: (ctx, index) {
          return SizedBox(
            width: 8.w,
          );
        },
        itemCount: _filters?.length ?? 0,
      ),
    );
  }

  Widget _filterItem(int index) {
    final itemData = _filters![index];
    return GestureDetector(
      onTap: () {
        itemData.isCheck = !itemData.isCheck!;
        widget.onFilterChanged?.call(_filters);
      },
      child: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          color: itemData.isCheck == true
              ? ThemeConfig.current().colorUtil.button02a
              : ThemeConfig.current().colorUtil.button05b,
          borderRadius: BorderRadius.circular(100),
        ),
        padding: EdgeInsets.symmetric(horizontal: 32.w),
        child: Center(
          child: Text(
            itemData.title ?? '',
            style: ThemeConfig.current().textStyleUtil.callout2.copyWith(
                  color: itemData.isCheck == true
                      ? ThemeConfig.current().colorUtil.button05b
                      : ThemeConfig.current().colorUtil.button02a,
                ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
