// Package imports:
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_cfai_api_client.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

// Project imports:
import '../../flex_global.dart';
import '../search/fx_search_dealer_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class FxSelectVehicleEmptyBloc extends BlocBase {
  FXCFAIClient cfaiApi = APIClientConfig.flexClient;

  Future searchDealers() async {
    if (await MapUtil.checkLocationGranted()) {
      await showProgress(NavigateService.context);
      var obtaionPosition = await MapUtil.getCurrentLocation();
      String? vin = Global.getInstance().vin;
      vehicleInfo.Payload? vehicleItem =
          await VehicleRepo().getLocalPayloadFromVin(vin);
      await cfaiApi.sendGetFlexStations(
        lat: obtaionPosition.lat.toDouble(),
        lon: obtaionPosition.lng.toDouble(),
        radius: searchLimitRadius,
        limitCount: null,
        vinBrand: vehicleItem?.brand ?? Global.getInstance().appBrand,
        rentalType: isFeatureEnabled(FLEX_EV_SWAP, vehicleItem?.features)
            ? FLEX_RENTALS_EV_SWAP
            : FLEX_RENTALS_RENTAL,
        vin: vehicleItem?.vin ?? Global.getInstance().vin,
      );
      await dismissProgress(NavigateService.context);
    }
  }

  @override
  void dispose() {}
}
