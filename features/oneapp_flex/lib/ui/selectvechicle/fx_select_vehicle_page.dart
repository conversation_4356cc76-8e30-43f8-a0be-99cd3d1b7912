// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/appbar/empty_appbar.dart';
import 'package:oneapp_common/widget/button/custom_outlined_button.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/card/simple_card.dart';
import 'package:oneapp_common/widget/image/no_transparent_image.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/filters_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/rent_car_info_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';

// Project imports:
import '../../route/router.dart';
import '../../util/fx_analytics_event.dart';
import '../../widget/flex_slide_title_widget.dart';
import '../summary/fx_summary_detail_bloc.dart';
import '../summary/fx_summary_dialog_view.dart';
import 'fx_select_vehicle_empty_view.dart';
import 'fx_select_vehicle_page_bloc.dart';
import 'fx_vehicle_filter_bar.dart';

class FxSelectVehiclePage extends StatefulWidget {
  final IntentToSelectVehicle? intentData;

  const FxSelectVehiclePage({Key? key, this.intentData}) : super(key: key);

  @override
  _FxSelectVehiclePageState createState() => _FxSelectVehiclePageState();
}

class _FxSelectVehiclePageState extends State<FxSelectVehiclePage> {
  final FxSelectVehicleNewBloc _bloc = FxSelectVehicleNewBloc();
  bool? showHideTaxSwitch = false;

  @override
  void initState() {
    FireBaseAnalyticsLogger.logMarketingEvent(
        AnalyticsEvent.FLEX_SELECT_VEHICLE_PAGE);
    super.initState();
    _bloc.loadData(widget.intentData);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      child: Scaffold(
        appBar: EmptyAppBar(
          backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
        ),
        body: Container(
          color: ThemeConfig.current().colorUtil.tertiary15,
          child: Column(
            children: [
              FlexSlideTitle(
                paddingBottom: 22.h,
                title: OneAppString.of().selectVehicleTitle,
                onBackPressed: () {
                  FlexRouter.popRoute(result: true);
                },
                menuItem: Text(
                  OneAppString.of().threeOf3,
                  style: ThemeConfig.current().textStyleUtil.body1,
                ),
              ),
              Expanded(
                child: StreamBuilder(
                  stream: _bloc.showEmptyUI,
                  builder: (ctx, AsyncSnapshot<bool> snapData) {
                    return snapData.data == true
                        ? FxSelectVehicleEmptyView(
                            dealerPhone: widget
                                .intentData?.dealerDetailInfo?.phoneNumber,
                          )
                        : _mainContent();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      bloc: _bloc,
    );
  }

  Widget showTaxWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Spacer(),
        Padding(
          padding: EdgeInsets.only(left: 164.w, top: 21.h),
          child: Text(
            OneAppString.of().viewTax,
            style: TextStyleExtension().newStyleWithColor(
                ThemeConfig.current().textStyleUtil.callout1,
                ThemeConfig.current().colorUtil.tertiary07),
          ),
        ),
        Spacer(),
        Padding(
          padding: EdgeInsets.only(right: 16.w, top: 21.h),
          child: FlutterSwitch(
            width: 60.w,
            height: 34.h,
            activeColor: Colors.white,
            inactiveColor: Colors.white,
            inactiveToggleColor: ThemeConfig.current().colorUtil.tertiary05,
            activeToggleColor: ThemeConfig.current().colorUtil.button03b,
            switchBorder:
                Border.all(color: ThemeConfig.current().colorUtil.tertiary10),
            toggleSize: 20.w,
            value: showHideTaxSwitch!,
            borderRadius: 30.w,
            showOnOff: false,
            onToggle: (val) {
              setState(() {
                showHideTaxSwitch = val;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _mainContent() {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: StreamBuilder(
            stream: _bloc.uiFilterList,
            builder: (ctx, AsyncSnapshot<List<CheckBoxFilter>> snapData) {
              final filterList = snapData.data;
              return filterList != null
                  ? FxVehicleFilterBar(
                      filters: filterList,
                      onFilterChanged: (list) {
                        _bloc.updateFilter(list!);
                      },
                    )
                  : Container();
            },
          ),
        ),
        SliverToBoxAdapter(
          child: !isFeatureEnabled(FLEX_EV_SWAP, _bloc.vehicleItem?.features)
              ? showTaxWidget()
              : Container(),
        ),
        SliverToBoxAdapter(
          child: SizedBox(
            height: 8.h,
          ),
        ),
        StreamBuilder(
          stream: _bloc.uiRentCarList,
          builder: (ctx, AsyncSnapshot<List<RentCarInfo>> snapData) {
            List<RentCarInfo>? list = snapData.data;
            return SliverList(
              delegate: SliverChildBuilderDelegate(
                (ctx, index) {
                  return Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    child: list?.isNotEmpty == true
                        ? _getItem(list![index])
                        : Container(),
                  );
                },
                childCount: list?.length ?? 0,
              ),
            );
          },
        ),
        SliverToBoxAdapter(
          child: SizedBox(
            height: 8.h,
          ),
        ),
        SliverToBoxAdapter(
          child: _buildLastItem(),
        ),
        SliverToBoxAdapter(
          child: SizedBox(
            height: 52.h,
          ),
        )
      ],
    );
  }

  Widget _getItem(RentCarInfo rentCarInfo) {
    return InkWell(
      onTap: () async {
        await _vehicleClassAvailability(rentCarInfo);
      },
      child: commonSimpleCard(
        child: Container(
          margin:
              EdgeInsets.only(left: 16.w, top: 12.h, right: 16.w, bottom: 11.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      rentCarInfo.vehicleGroup ?? OneAppString.of().vehicle,
                      style: ThemeConfig.current().textStyleUtil.subHeadline2,
                    ),
                  ),
                  SizedBox(
                    width: 17.w,
                  ),
                  Text(
                    isFeatureEnabled(FLEX_EV_SWAP, _bloc.vehicleItem?.features)
                        ? OneAppString.of().oneCreditPerDay
                        : showHideTaxSwitch == true
                            ? rentCarInfo.pricePerWithTax ?? '--'
                            : rentCarInfo.pricePer ?? '--',
                    style: ThemeConfig.current().textStyleUtil.body4,
                  ),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 2.h,
                        ),
                        _commonSimilar(rentCarInfo.model),
                        SizedBox(
                          height: 13.h,
                        ),
                        _commonContainerImageText(
                          'packages/oneapp_common/res/image/svg/toyota/ic_flex_profile.svg',
                          rentCarInfo.seatNumber,
                        ),
                        SizedBox(
                          height: 5.h,
                        ),
                        _commonContainerImageText(
                          'packages/oneapp_common/res/image/svg/toyota/ic_flex_luggage.svg',
                          rentCarInfo.luggage,
                        ),
                        SizedBox(
                          height: 5.h,
                        ),
                        _commonContainerImageText(
                          'packages/oneapp_common/res/image/svg/toyota/ic_flex_mpg.svg',
                          rentCarInfo.fuelCapacity,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 140,
                    child: NoTransparentNetworkImage(
                      rentCarInfo.vehiclePicture ?? "",
                      placeHolder: carPlaceHolderImage,
                      fit: BoxFit.fitWidth,
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLastItem() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          color: ThemeConfig.current().colorUtil.tertiary12,
          borderRadius: BorderRadius.all(Radius.circular(8.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              margin: EdgeInsets.fromLTRB(24.w, 32.h, 24.w, 0),
              child: StreamBuilder(
                stream: _bloc.uiRentCarList,
                builder: (context, snapshot) {
                  return Text(
                    OneAppString.of().selectVehicle,
                    textAlign: TextAlign.center,
                    style: ThemeConfig.current()
                        .textStyleUtil
                        .body4
                        .copyWith(fontSize: 16.sp),
                  );
                },
              ),
            ),
            Container(
              margin: EdgeInsets.fromLTRB(24.w, 10.h, 24.w, 0),
              child: StreamBuilder(
                stream: _bloc.uiRentCarList,
                builder: (context, snapshot) {
                  return Text(
                    OneAppString.of().selectVehiclePage,
                    style: ThemeConfig.current().textStyleUtil.body1.copyWith(
                        color: ThemeConfig.current().colorUtil.tertiary05,
                        fontWeight: FontWeight.w400,
                        fontSize: 14.sp),
                    textAlign: TextAlign.center,
                  );
                },
              ),
            ),
            SizedBox(height: 40.h),
            Center(
              child: SizedBox(
                width: 192.w,
                height: 52.h,
                child: CustomOutlinedButton(
                  buttonText: OneAppString.of().callDealer,
                  borderColor: ThemeConfig.current().colorUtil.button02c,
                  textColor: ThemeConfig.current().colorUtil.button01b,
                  backgroundColor: ThemeConfig.current().colorUtil.button01a,
                  borderWidth: 2.w,
                  clickCallBack: () {
                    if (_bloc.dealerPhone?.isNotEmpty == true) {
                      FireBaseAnalyticsLogger.logMarketingEvent(
                          AnalyticsEvent.FLEX_CALL_DEALER);
                    }
                    dialerLauncher(_bloc.dealerPhone!);
                  },
                ),
              ),
            ),
            SizedBox(height: 8.h),
            Center(
              child: StreamBuilder(
                stream: _bloc.uiRentCarList,
                builder: (context, snapshot) {
                  String btnTitle = OneAppString.of().changeDates;
                  return SizedBox(
                    width: 192.w,
                    height: 52.h,
                    child: CustomDefaultButton(
                      backgroundColor:
                          ThemeConfig.current().colorUtil.button01b,
                      buttonTextColor:
                          ThemeConfig.current().colorUtil.button01a,
                      borderColor: ThemeConfig.current().colorUtil.button01b,
                      disabledBackgroundColor:
                          ThemeConfig.current().colorUtil.button02c,
                      verticalPadding: 4.h,
                      text: btnTitle,
                      press: () {
                        _clickChangeDates(context);
                      },
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }

  void _clickChangeDates(BuildContext context) async {
    final intentData = IntentToSelectDate(
      reservationId: widget.intentData?.reservationId,
      status: widget.intentData?.status,
      orgID: widget.intentData?.orgID,
      comeFrom: widget.intentData?.comeFrom == comeFromEditVehicle
          ? comeFromEditVehicle
          : comeFromSelectVehicle,
      dealerInfo: DealerInfo(
        dealerName: widget.intentData?.dealerDetailInfo?.locationName,
        dealerID: widget.intentData?.dealerDetailInfo?.locationId,
        dealerGeo: Geo(
            widget.intentData!.dealerDetailInfo!.latLng!.lat!.toString(),
            widget.intentData!.dealerDetailInfo!.latLng!.lon!.toString()),
        dealerPhone: widget.intentData?.dealerDetailInfo?.phoneNumber,
        stationId: widget.intentData?.dealerDetailInfo?.stationDetail?.id,
        businessHours: widget.intentData?.dealerDetailInfo?.businessHours,
      ),
      dealerDetailInfo: widget.intentData?.dealerDetailInfo,
      requestCode: widget.intentData?.resetVehicle,
    );
    if (widget.intentData?.comeFrom == comeFromEditVehicle) {
      final result = await FlexRouter.pushName(RoutePath.FX_CALENDAR_VIEW,
          arguments: intentData);
      if (result != null && result is Map) {
        FlexRouter.popRoute(result: result);
      }
    } else {
      FlexRouter.popRoute(result: intentData);
    }
  }

  Widget _commonContainerImageText(String image, String? text) {
    return Container(
      child: Row(
        children: [
          SvgPicture.asset(
            image,
            width: 24,
            height: 24,
            alignment: Alignment.center,
            fit: BoxFit.scaleDown,
            colorFilter: ColorFilter.mode(
              ThemeConfig.current().colorUtil.tertiary03,
              BlendMode.srcIn,
            ),
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: 11),
              child: Text(
                text?.isNotEmpty == true ? text! : '--',
                softWrap: false,
                textAlign: TextAlign.left,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: ThemeConfig.current()
                    .textStyleUtil
                    .callout1
                    .copyWith(fontSize: 14.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void itemClick(RentCarInfo rentCarInfo) {
    if (widget.intentData!.resetVehicle == RESET_DATE_REQUEST_CODE ||
        widget.intentData!.resetVehicle == SELECT_DATE_REQUEST_CODE ||
        widget.intentData!.resetVehicle == RESET_VEHICLE_REQUEST_CODE ||
        widget.intentData!.comeFrom == comeFromSummary) {
      final data = Map.of({
        "Reservation": rentCarInfo,
        "ReservationId": rentCarInfo.reservationId,
        "RequestCode": widget.intentData!.resetVehicle,
        "IsModify": true,
        "Status": rentCarInfo.status,
      });
      FlexRouter.popRoute(result: data);
    } else {
      final data = Map.of({
        'Status': CREATE_ACTION_KEY,
        "CreateReservation": rentCarInfo,
        'IsCarShare': _bloc.intentData?.isCarShare ?? false
      });
      FlexRouter.pushName(RoutePath.FX_SUMMARY_DETAIL, arguments: data);
    }
  }

  Future _vehicleClassAvailability(RentCarInfo rentCarInfo) {
    return showModalBottomSheet(
      enableDrag: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return Container(
          width: double.maxFinite,
          child: FxVehicleDialogViewState(
              imageUrl: vehicleIcon,
              title: OneAppString.of().vehicleClassAvailability,
              description:
                  isFeatureEnabled(FLEX_EV_SWAP, _bloc.vehicleItem?.features)
                      ? OneAppString.of().lexusVehicleDescription
                      : OneAppString.of().vehicleDescription,
              primaryButtonText: OneAppString.of().commonContinue,
              primaryButtonPressed: () async {
                await FlexRouter.popRoute();
                itemClick(rentCarInfo);
              }),
        );
      },
    );
  }
}

Widget _commonSimilar(String? brand) {
  String similar = brand != null && brand != '--' ? "$brand" : "--";
  return Text(
    similar,
    style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
          color: ThemeConfig.current().colorUtil.tertiary05,
        ),
  );
}
