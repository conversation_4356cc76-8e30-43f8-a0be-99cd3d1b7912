// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';

// Project imports:
import '../../route/router.dart';
import '../../util/fx_analytics_event.dart';
import 'fx_select_vehicle_empty_bloc.dart';

class FxSelectVehicleEmptyView extends StatelessWidget {
  final String? dealerPhone;

  final FxSelectVehicleEmptyBloc _bloc = FxSelectVehicleEmptyBloc();

  FxSelectVehicleEmptyView({Key? key, this.dealerPhone}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 32.w),
        child: Column(
          children: [
            Spacer(
              flex: 205,
            ),
            Text(
              OneAppString.of().noResultsFound,
              style: ThemeConfig.current().textStyleUtil.subHeadline1,
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: 24.h,
            ),
            Text(
              OneAppString.of().selectVehicleEmptyViewTips,
              style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                    color: ThemeConfig.current().colorUtil.tertiary05,
                  ),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: 26.h,
            ),
            SizedBox(
              height: 48.w,
              width: 48.w,
              child: CommonCircleIconImage(
                'packages/oneapp_common/res/image/svg/toyota/ic_flex_filter_car.svg',
                iconWidth: 24.w,
                iconHeight: 24.w,
                iconTintColor: ThemeConfig.current().colorUtil.primary01,
                circleBackgroundColor:
                    ThemeConfig.current().colorUtil.primary02,
              ),
            ),
            Spacer(
              flex: 170,
            ),
            _callDealerButton(
              OneAppString.of().callDealer,
              () {
                if (dealerPhone?.isNotEmpty == true) {
                  FireBaseAnalyticsLogger.logMarketingEvent(
                      AnalyticsEvent.FLEX_CALL_DEALER);
                }
                dialerLauncher(dealerPhone!);
              },
            ),
            SizedBox(
              height: 4.h,
            ),
            SizedBox(
              width: 192.w,
              height: 52.h,
              child: CustomDefaultButton(
                backgroundColor: ThemeConfig.current().colorUtil.button01b,
                buttonTextColor: ThemeConfig.current().colorUtil.button01a,
                borderColor: ThemeConfig.current().colorUtil.button01b,
                disabledBackgroundColor:
                    ThemeConfig.current().colorUtil.button02c,
                text: OneAppString.of().searchNearby,
                verticalPadding: 4.h,
                press: () async {
                  await _bloc.searchDealers();
                  FlexRouter.popRouteUtil(RoutePath.FX_SEARCH_DEALER);
                },
              ),
            ),
            SizedBox(
              height: 32.h,
            ),
          ],
        ),
      ),
      bloc: _bloc,
    );
  }

  Widget _callDealerButton(String text, VoidCallback onPressed) {
    return TextButton(
      onPressed: onPressed,
      child: Text(
        formatTextForLexusAndToyota(text),
        style: ThemeConfig.current()
            .textStyleUtil
            .buttonLink1
            .copyWith(color: ThemeConfig.current().colorUtil.button02a),
      ),
      style: TextButton.styleFrom(
        foregroundColor: ThemeConfig.current().colorUtil.button05b,
        minimumSize: Size(MIN_BUTTON_WIDTH.w, MIN_BUTTON_HEIGHT.h),
        backgroundColor: Colors.transparent,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(56)),
        ),
        padding: EdgeInsets.symmetric(horizontal: 32, vertical: 4),
      ),
    );
  }
}
