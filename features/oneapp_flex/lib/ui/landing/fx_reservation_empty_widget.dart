// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';

// Project imports:
import '../../flex_global.dart';
import '../../route/router.dart';
import '../summary/fx_summary_dialog_view.dart';
import 'fx_detail_landing_bloc.dart';
import 'fx_landing_bottom_sheet.dart';

class LandingEmptyUIWidget extends StatefulWidget {
  final bool hasError;

  final VoidCallback? onRefresh;

  const LandingEmptyUIWidget({Key? key, this.hasError = false, this.onRefresh})
      : super(key: key);

  @override
  _LandingEmptyUIWidgetState createState() => _LandingEmptyUIWidgetState();
}

class _LandingEmptyUIWidgetState extends State<LandingEmptyUIWidget> {
  FxDetailLandingBloc? _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of(context);
  }

  @override
  Widget build(BuildContext context) {
    return widget.hasError == true ? _errorUI() : _emptyUI();
  }

  Widget _emptyUI() {
    bool isEVSwap =
        isFeatureEnabled(FLEX_EV_SWAP, _bloc?.vehicleInfoEntity?.features);
    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(12.w, 23.h, 12.w, 14.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            isEVSwap
                ? OneAppString.of().ExploreVehiclesToReserve
                : OneAppString.of().findYourFavoriteVehicleToRentIn,
            textAlign: TextAlign.center,
            style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary05,
                ),
          ),
          Text(
            isEVSwap ? "" : OneAppString.of().threeEasySteps,
            textAlign: TextAlign.center,
            style: ThemeConfig.current().textStyleUtil.callout2,
          ),
          SizedBox(
            height: 38.h,
          ),
          Container(
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.r)),
            child: CachedNetworkImage(
              imageUrl: _bloc?.vehicleInfoEntity?.brand == "L"
                  ? flexLexusLandingFPOImageUrl
                  : flexToyotaLandingFPOImageUrl,
              fit: BoxFit.fitWidth,
              placeholder: (context, url) => Container(),
              errorWidget: (context, url, error) => Container(),
            ),
          ),
          SizedBox(
            height: 24.h,
          ),
          _makeReservation(),
        ],
      ),
    );
  }

  Widget _errorUI() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(55.w, 23.h, 55.w, 14.h),
      child: Column(
        children: [
          CommonCircleIconImage(
            'packages/oneapp_common/res/image/svg/toyota/ic_flex_landing_error.svg',
            iconWidth: 24.w,
            iconHeight: 24.w,
            iconPadding: EdgeInsets.all(12.w),
            iconTintColor: ThemeConfig.current().colorUtil.primary01,
            circleBackgroundColor: ThemeConfig.current().colorUtil.primary02,
          ),
          SizedBox(
            height: 24.h,
          ),
          Text(
            OneAppString.of().error,
            style: ThemeConfig.current().textStyleUtil.body4,
          ),
          SizedBox(
            height: 8.h,
          ),
          Text(
            OneAppString.of().reservationEmtpyWidgetTips,
            style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary05,
                ),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 64.h,
          ),
          GestureDetector(
            onTap: widget.onRefresh,
            child: SvgPicture.asset(
              'packages/oneapp_common/res/image/svg/toyota/ic_flex_landing_refresh.svg',
              width: 48.w,
              height: 48.w,
            ),
          ),
          SizedBox(
            height: 6.h,
          ),
          Text(
            _getUpdatedTime(),
            style: ThemeConfig.current().textStyleUtil.caption1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary05,
                ),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 16.h,
          ),
          _makeReservation(),
        ],
      ),
    );
  }

  String _getUpdatedTime() {
    if (_bloc!.updatedTime != null) {
      String timeStr =
          DateFormat("MMMM d 'a't h:mm a").format(_bloc!.updatedTime!);
      return 'Last updated $timeStr';
    }
    return '';
  }

  Widget _makeReservation() {
    return StreamBuilder(
        stream: _bloc?.EVCredits,
        builder: (c, creditData) {
          int? remainingCredits = creditData.data;
          FxAccountGlobal.getInstance().evReserveBalance = remainingCredits;
          return Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: EdgeInsets.only(bottom: 18.h),
              child: SizedBox(
                width: 192.w,
                height: 52.h,
                child: CustomDefaultButton(
                  primaryButtonState:
                      remainingCredits != null && remainingCredits == 0
                          ? PrimaryButtonState.INACTIVE
                          : PrimaryButtonState.ACTIVE,
                  backgroundColor: ThemeConfig.current().colorUtil.button01b,
                  buttonTextColor: ThemeConfig.current().colorUtil.button01a,
                  disabledBackgroundColor:
                      ThemeConfig.current().colorUtil.button02c,
                  borderColor: ThemeConfig.current().colorUtil.button01b,
                  text: OneAppString.of().makeAReservation,
                  verticalPadding: 4.h,
                  press: remainingCredits != null && remainingCredits == 0
                      ? () {}
                      : () async {
                          if (isFeatureEnabled(FLEX_EV_SWAP,
                              _bloc?.vehicleInfoEntity?.features)) {
                            int? credits = await _bloc?.availableCredits();
                            await _showEVSwapCredits(credits ?? 0);
                          } else {
                            await FlexRouter.pushName(
                                RoutePath.FX_SEARCH_DEALER);
                            final result =
                                FxAccountGlobal.getInstance().routeData;
                            if (result != null && result is ToastMsgData) {
                              Future(() {
                                _showMessageBottomSheet(context,
                                    result.success ?? true, result.message);
                              });
                            }
                          }
                          _bloc?.refreshAllData();
                        },
                ),
              ),
            ),
          );
        });
  }

  _showMessageBottomSheet(BuildContext context, bool success, String message) {
    showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      builder: (context) {
        return FxMessageBottomView(
          success: success,
          message: message,
        );
      },
    );
  }

  Future _showEVSwapCredits(int credits) {
    return showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return FxSummaryDialogView(
          title: "$credits ${OneAppString.of().evSwapCredits}",
          description: OneAppString.of().bookUsingAvailableEVCredits,
          primaryButtonText: OneAppString.of().reserveWithCredits,
          additionalButtonText: OneAppString.of().notNow,
          primaryButtonPressed: () async {
            await FlexRouter.pushName(RoutePath.FX_SEARCH_DEALER);
            final result = FxAccountGlobal.getInstance().routeData;
            if (result != null && result is ToastMsgData) {
              Future(() {
                _showMessageBottomSheet(
                    context, result.success ?? true, result.message);
              });
            }
          },
          additionalButtonPressed: () async {
            FlexRouter.popRoute();
          },
        );
      },
    );
  }
}
