// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

class FxLandingTabBar extends StatefulWidget {
  final Function(String value)? onTabChanged;

  final List<String> tabNames;

  const FxLandingTabBar({Key? key, this.onTabChanged, required this.tabNames})
      : super(key: key);

  @override
  _FxLandingTabBarState createState() => _FxLandingTabBarState();
}

class _FxLandingTabBarState extends State<FxLandingTabBar> {
  int _selectedIndex = 0;

  int _getUpcomingTagIndex() {
    int value = widget.tabNames.indexOf(OneAppString.of().upcomingHeading);
    if (value < 0) {
      value = 0;
    }
    return value;
  }

  int _getPastTagIndex() {
    int value = widget.tabNames.indexOf(OneAppString.of().registerPast);
    if (value < 0) {
      value = 0;
    }
    return value;
  }

  @override
  Widget build(BuildContext context) {
    bool showBar = (widget.tabNames.length) > 1;
    if (showBar && widget.tabNames.isNotEmpty == true) {
      if (widget.tabNames.length > _selectedIndex) {
        widget.onTabChanged?.call(widget.tabNames[_selectedIndex]);
      }
    } else if (!showBar && widget.tabNames.isNotEmpty == true) {
      widget.onTabChanged?.call(widget.tabNames.first);
    }
    return showBar
        ? Container(
            height: 44.h,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Visibility(
                  visible: widget.tabNames
                      .contains(OneAppString.of().registerActive),
                  child: Expanded(
                    child: _TabContainer(
                      indexTag: 0,
                      name: OneAppString.of().registerActive,
                      selectedIndex: _selectedIndex,
                      onTabClicked: (_, __, ___) => _update(_, __ ?? "", ___),
                    ),
                  ),
                ),
                SizedBox(
                  width: 8.w,
                ),
                Visibility(
                  visible: widget.tabNames
                      .contains(OneAppString.of().upcomingHeading),
                  child: Expanded(
                    child: _TabContainer(
                      indexTag: _getUpcomingTagIndex(),
                      name: OneAppString.of().upcomingHeading,
                      selectedIndex: _selectedIndex,
                      onTabClicked: (_, __, ___) => _update(_, __ ?? "", ___),
                    ),
                  ),
                ),
                SizedBox(
                  width: 8.w,
                ),
                Visibility(
                  visible:
                      widget.tabNames.contains(OneAppString.of().registerPast),
                  child: Expanded(
                    child: _TabContainer(
                      indexTag: _getPastTagIndex(),
                      name: OneAppString.of().registerPast,
                      selectedIndex: _selectedIndex,
                      onTabClicked: (_, __, ___) => _update(_, __ ?? "", ___),
                    ),
                  ),
                ),
              ],
            ),
          )
        : Container();
  }

  _update(int index, String choiceName, bool isInitial) {
    if (isInitial) {
      _selectedIndex = index;
    } else {
      setState(() {
        _selectedIndex = index;
      });
    }
    widget.onTabChanged?.call(choiceName);
  }
}

class _TabContainer extends StatefulWidget {
  final int indexTag;

  final int selectedIndex;

  final String? name;

  final Function(int index, String? choiceName, bool isInitial)? onTabClicked;

  const _TabContainer(
      {Key? key,
      this.indexTag = 0,
      this.selectedIndex = 0,
      this.onTabClicked,
      this.name})
      : super(key: key);

  @override
  _TabContainerState createState() => _TabContainerState();
}

class _TabContainerState extends State<_TabContainer> {
  @override
  void initState() {
    super.initState();
    if (widget.selectedIndex == widget.indexTag) {
      widget.onTabClicked?.call(widget.indexTag, widget.name, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onTabClicked?.call(widget.indexTag, widget.name, false);
      },
      child: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          color: widget.selectedIndex == widget.indexTag
              ? ThemeConfig.current().colorUtil.button02a
              : ThemeConfig.current().colorUtil.button05b,
          borderRadius: BorderRadius.all(Radius.circular(100)),
        ),
        child: Center(
          child: Text(
            widget.name ?? '',
            style: ThemeConfig.current().textStyleUtil.callout2.copyWith(
                  color: widget.selectedIndex == widget.indexTag
                      ? ThemeConfig.current().colorUtil.button05b
                      : ThemeConfig.current().colorUtil.button02a,
                ),
          ),
        ),
      ),
    );
  }
}
