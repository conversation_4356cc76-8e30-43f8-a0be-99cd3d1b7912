// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/card/simple_card.dart';
import 'package:oneapp_common/widget/image/no_transparent_image.dart';
import 'package:oneapp_common/widget/list_tile/custom_list_tile.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/landing_list_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:sliding_sheet/sliding_sheet.dart';
import 'package:vehicle_module/ui/vehicle_overview/vehicle_announcement/vehicle_announcement_ev_swap_overview.dart';

// Project imports:
import '../../flex_global.dart';
import '../../route/router.dart';
import '../../util/fx_analytics_event.dart';
import '../../widget/flex_slide_title_widget.dart';
import '../../widget/vehicle_finder_map.dart';
import '../summary/fx_summary_dialog_view.dart';
import 'fx_detail_landing_bloc.dart';
import 'fx_landing_bottom_sheet.dart';
import 'fx_landing_tab_bar.dart';
import 'fx_reservation_empty_widget.dart';
import 'fx_reserve_Balance_model.dart';
import 'fx_reserve_balance_page.dart';

class FxReservationLandingPage extends StatefulWidget {
  final bool allowCache;

  final ToastMsgData? toastMsgData;

  final CommonResponse? commonResponse;

  const FxReservationLandingPage(
      {Key? key,
      this.allowCache = true,
      this.toastMsgData,
      this.commonResponse})
      : super(key: key);

  @override
  _FxReservationLandingPageState createState() =>
      _FxReservationLandingPageState();
}

class _FxReservationLandingPageState extends State<FxReservationLandingPage>
    with TickerProviderStateMixin {
  final FxDetailLandingBloc _bloc = FxDetailLandingBloc();
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  ValueNotifier<SheetState> sheetState = ValueNotifier(SheetState.inital());

  SheetState get state => sheetState.value;

  set state(SheetState value) => sheetState.value = value;

  final SheetController _scrollSheetController = SheetController();

  bool get isAtBottom => state.isAtBottom;

  final GlobalKey _slideHeaderKey = GlobalKey();

  late AnimationController accountBadgeCtrl;

  String tabSelected = "";

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logMarketingEvent(AnalyticsEvent.FLEX_LANDING_PAGE);
    _bloc.uiToast.listen((event) {
      showSimpleToast(true, event.message);
    });
    accountBadgeCtrl =
        AnimationController(vsync: this, duration: Duration(seconds: 1));
    accountBadgeCtrl.repeat(reverse: true);

    // _bloc.readyToShow.listen((event) {
    //   _scrollSheetController.expand();
    // });
    // _scrollSheetController.collapse();

    _bloc.init().then((value) {
      Future(() async {
        int? credits = await _bloc.availableCredits();
        if (credits == 0) {
          _showEVSwapNoCredits();
        }
        if (_bloc.isLexusReserveExpired == true) {
          _showEVSwapExpired();
        }
      });
    });
    FBroadcast.instance().register(REFRESH_RENTAL_LIST_NOTIFICATION,
        (value, callback) {
      _bloc.init().then((value) {
        if (widget.toastMsgData != null) {
          Future(() {
            _showMessageBottomSheet(
                context,
                widget.toastMsgData?.success ?? true,
                widget.toastMsgData?.message ?? "");
          });
        }
      });
    });
  }

  @override
  void dispose() {
    accountBadgeCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _bloc.refreshAllData(
      allowCache: widget.allowCache,
      commonResponse: widget.commonResponse,
    );
    //willpopscope is used to prevent back button from going to previous page in reservation flow
    return PopScope(
        child: BlocProvider(
          child: Scaffold(
            backgroundColor: Colors.transparent,
            body: Stack(
              children: [
                SlidingSheet(
                  color: ThemeConfig.current().colorUtil.tertiary15,
                  cornerRadiusOnFullscreen: 0,
                  addTopViewPaddingOnFullscreen: true,
                  controller: _scrollSheetController,
                  listener: (state) {
                    final needsRebuild =
                        (this.state.isCollapsed != state.isCollapsed) ||
                            (this.state.isExpanded != state.isExpanded) ||
                            (this.state.isAtTop != state.isAtTop) ||
                            (this.state.isAtBottom != state.isAtBottom);
                    this.state = state;

                    if (needsRebuild) {
                      setState(() {
                        if (this.state.isAtBottom == true) {
                          switch (tabSelected) {
                            case "Active":
                              {
                                if (_bloc.progressOffset > 0) {
                                  _bloc.getAllReservationsListMore(
                                      status: IN_PROGRESS_ACTION_KEY);
                                }
                                break;
                              }
                            case "Upcoming":
                              {
                                if (_bloc.upcomingOffset > 0) {
                                  _bloc.getAllReservationsListMore(
                                      status: UPCOMING_ACTION_KEY);
                                }
                                break;
                              }
                            case "Past":
                              {
                                if (_bloc.cancelledOffset > 0) {
                                  _bloc.getAllReservationsListMore(
                                      status: IN_CANCELED_COMPLETED_KEY);
                                }
                                break;
                              }
                          }
                        }
                      });
                    }
                  },
                  elevation: 0,
                  cornerRadius: 30,
                  snapSpec: const SnapSpec(
                    snap: true,
                    positioning: SnapPositioning.relativeToAvailableSpace,
                    snappings: const [
                      0.6,
                      1.0,
                    ],
                  ),
                  body: StreamBuilder(
                    stream: _bloc.vehiclePosition,
                    builder: (ctx, snapData) {
                      return StreamBuilder(
                        stream: _bloc.currentLocation,
                        builder: (ctx, currentLoc) {
                          return VehicleFinderMap(
                            vehiclePosition: snapData.data,
                            currentPosition: currentLoc.data,
                          );
                        },
                      );
                    },
                  ),
                  headerBuilder: (context, state) {
                    return FlexSlideTitle(
                      key: _slideHeaderKey,
                      title: isFeatureEnabled(
                              FLEX_EV_SWAP, _bloc.vehicleInfoEntity?.features)
                          ? OneAppString.of().evSwap
                          : OneAppString.of().rentals,
                      onBackPressed: () {
                        goBackToNative();
                      },
                      menuItem: Visibility(
                          visible: isFeatureEnabled(
                              FLEX_EV_SWAP, _bloc.vehicleInfoEntity?.features),
                          child: actionInformationWidget()),
                    );
                  },
                  builder: (context, state) {
                    return _reservationContent();
                  },
                ),
                _listFloatButton(),
              ],
            ),
          ),
          bloc: _bloc,
        ),
        onPopInvoked: (didPop) async {
          if (didPop) return;
          goBackToNative();
        });
  }

  Future _showEVSwapExpired() async {
    return await showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return FxSummaryDialogView(
          success: false,
          title: OneAppString.of().evSwapExpiredTitle,
          description: OneAppString.of().evSwapExpiredDesc,
          primaryButtonText: OneAppString.of().dismissText,
          additionalButtonText: "",
          additionalButtonPressed: () {},
          primaryButtonPressed: () async {
            Navigator.pop(context);
          },
        );
      },
    );
  }

  Widget actionInformationWidget() {
    return MaterialButton(
      onPressed: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => VehicleAnnouncementEVSwapOverView(
                      avlCredit: "",
                      comeFromWallet: true,
                    )));
      },
      child: SvgPicture.asset(
        informationIcon,
        width: 30.w,
        colorFilter: ColorFilter.mode(
          _colorUtil.tertiary03,
          BlendMode.srcIn,
        ),
        allowDrawingOutsideViewBox: true,
      ),
      padding: EdgeInsets.all(5.w),
      shape: CircleBorder(),
    );
  }

  Widget _listFloatButton() {
    return StreamBuilder(
        stream: _bloc.uiRecord,
        builder: (ctx, snapData) {
          List<String> tabNames = _bloc.tabNames;
          if (tabNames.isNotEmpty == true) {
            return StreamBuilder(
                stream: _bloc.EVCredits,
                builder: (c, creditData) {
                  int? remainingCredits = creditData.data;
                  return Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 18.h),
                      child: SizedBox(
                        width: 192.w,
                        height: 52.h,
                        child: CustomDefaultButton(
                          primaryButtonState: remainingCredits != null &&
                                      remainingCredits == 0 ||
                                  _bloc.isLexusReserveExpired
                              ? PrimaryButtonState.INACTIVE
                              : PrimaryButtonState.ACTIVE,
                          backgroundColor:
                              ThemeConfig.current().colorUtil.button01b,
                          buttonTextColor:
                              ThemeConfig.current().colorUtil.button01a,
                          disabledBackgroundColor:
                              ThemeConfig.current().colorUtil.button02c,
                          borderColor:
                              ThemeConfig.current().colorUtil.button01b,
                          text: OneAppString.of().makeAReservation,
                          verticalPadding: 4.h,
                          press: remainingCredits != null &&
                                      remainingCredits == 0 ||
                                  _bloc.isLexusReserveExpired
                              ? () {}
                              : () async {
                                  if (isFeatureEnabled(FLEX_EV_SWAP,
                                      _bloc.vehicleInfoEntity?.features)) {
                                    await _showEVSwapCredits(
                                        remainingCredits ?? 0);
                                  } else {
                                    await FlexRouter.pushName(
                                        RoutePath.FX_SEARCH_DEALER);
                                    final result =
                                        FxAccountGlobal.getInstance().routeData;
                                    if (result != null &&
                                        result is ToastMsgData) {
                                      Future(() {
                                        _showMessageBottomSheet(
                                            context,
                                            result.success ?? true,
                                            result.message);
                                      });
                                    }
                                  }
                                  _bloc.refreshAllData();
                                },
                        ),
                      ),
                    ),
                  );
                });
          }
          return Container();
        });
  }

  Widget _reservationContent() {
    return StreamBuilder(
      stream: _bloc.uiRecord,
      builder: (ctx, AsyncSnapshot<LandingUI> snapData) {
        List<String> tabNames = _bloc.tabNames;
        Widget widget;
        if (snapData.data?.hasError == true) {
          widget = LandingEmptyUIWidget(
            hasError: snapData.data?.hasError ?? false,
            onRefresh: () {
              _bloc.refreshAllData();
              _bloc.init();
            },
          );
        } else {
          widget = _tabView(snapData.data, tabNames);
        }
        return widget;
      },
    );
  }

  Widget _tabView(LandingUI? landingUI, List<String> tabNames) {
    bool isInitialTab = true;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: InkWell(
            onTap: () {
              _LexusReserveTransactionsottomSheet();
            },
            child: StreamBuilder(
              stream: _bloc.reserveBalancecards,
              builder: (ctx, snapData) {
                if (snapData.data != null) {
                  var reservedBalanceCard =
                      snapData.data as List<ReserveBalanceCards>;
                  if (reservedBalanceCard.isNotEmpty) {
                    return CustomListTile(
                      displayBottomIcon: false,
                      tileState: DisplayStyleState.GENERAL_STATUS,
                      tapOption: ListTileTapOption.DEFAULT,
                      svgAvatarIconName: reservedBalanceCard.first.icon,
                      titleText: OneAppString.of().lexusReserveBalance,
                      subTitleText:
                          "${reservedBalanceCard.first.title} Days Remaining • Exp ${reservedBalanceCard[1].title}",
                    );
                  }
                }
                return Container();
              },
            ),
          ),
        ),
        Container(
          height: 10,
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: FxLandingTabBar(
            tabNames: tabNames,
            onTabChanged: (String choice) {
              // _scrollSheetController.collapse();
              isInitialTab = false;
              tabSelected = choice;
              _bloc.refreshTabChoice(choice);
            },
          ),
        ),
        StreamBuilder(
          stream: _bloc.refreshTab,
          initialData: OneAppString.of().registerActive,
          builder: (ctx, snapData) {
            return _contentTab(snapData.data, isInitialTab);
          },
        ),
      ],
    );
  }

  Widget _contentTab(String? choice, bool isInitialTab) {
    List<LandingCardUI>? list;
    String? noReservationDescription;
    if (choice == OneAppString.of().registerActive) {
      list = _bloc.progressList;
      if (list.isEmpty) {
        noReservationDescription = OneAppString.of().noActiveReservations;
      }
    } else if (choice == OneAppString.of().upcomingHeading) {
      list = _bloc.upcomingList;
      if (list.isEmpty) {
        noReservationDescription = OneAppString.of().noUpcomingReservations;
      }
    } else if (choice == OneAppString.of().registerPast) {
      list = _bloc.cancelledList;
      if (list.isEmpty) {
        noReservationDescription = OneAppString.of().noPastReservations;
      }
    }
    return list?.isNotEmpty == true
        ? LandingTabView(
            listData: list,
          )
        : Container(
            height: 200.h,
            child: Visibility(
              visible: !isInitialTab,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 32.0, 8.0, 8.0),
                child: Text(noReservationDescription ?? "",
                    style: ThemeConfig.current().textStyleUtil.body4,
                    textAlign: TextAlign.center),
              ),
            ),
          );
  }

  Future _showEVSwapNoCredits() {
    return showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return FxSummaryDialogView(
          success: false,
          title: "0 ${OneAppString.of().evSwapCredits}",
          description: OneAppString.of().evNoCredits,
          primaryButtonText: OneAppString.of().dismissText,
          additionalButtonText: "",
          additionalButtonPressed: () {},
          primaryButtonPressed: () async {
            FlexRouter.popRoute();
          },
        );
      },
    );
  }

  Future _showEVSwapCredits(int credits) {
    return showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return FxSummaryDialogView(
          title: "$credits ${OneAppString.of().evSwapCredits}",
          description: OneAppString.of().bookUsingAvailableEVCredits,
          primaryButtonText: OneAppString.of().reserveWithCredits,
          additionalButtonText: OneAppString.of().notNow,
          primaryButtonPressed: () async {
            await FlexRouter.pushName(RoutePath.FX_SEARCH_DEALER);
            final result = FxAccountGlobal.getInstance().routeData;
            if (result != null && result is ToastMsgData) {
              Future(() {
                _showMessageBottomSheet(
                    context, result.success ?? true, result.message);
              });
            }
          },
          additionalButtonPressed: () async {
            FlexRouter.popRoute();
          },
        );
      },
    );
  }

  _LexusReserveTransactionsottomSheet() {
    return showMaterialModalBottomSheet(
        expand: false,
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20.r),
          ),
        ),
        clipBehavior: Clip.antiAliasWithSaveLayer,
        backgroundColor: _colorUtil.tertiary15,
        builder: (context) => SafeArea(
                child: FXReserveBalancePage(
              buildContext: context,
              bloc: _bloc,
            )));
  }
}

class LandingTabView extends StatefulWidget {
  final List<LandingCardUI>? listData;

  const LandingTabView({Key? key, this.listData}) : super(key: key);

  @override
  _LandingTabViewState createState() => _LandingTabViewState();
}

class _LandingTabViewState extends State<LandingTabView> {
  FxDetailLandingBloc? _bloc;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of(context);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _listView(widget.listData),
      ],
    );
  }

  Widget _listView(List<LandingCardUI>? record) {
    return ListView.separated(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 94.h),
      itemCount: record?.length ?? 0,
      itemBuilder: (context, position) {
        final widget = _getItem(
          record![position],
        );
        return widget;
      },
      separatorBuilder: (BuildContext context, int index) {
        return SizedBox(height: 16.h);
      },
    );
  }

  Widget _getItem(LandingCardUI record) {
    return InkWell(
      onTap: () async {
        final data = {
          'Status': record.status,
          // 'Status': UPCOMING_ACTION_KEY,
          'ReservationDetail': record.reservation,
          "ReservationId": record.reservationId,
          "PricingGroup": record.pricingGroup,
          "IsCarShare": record.isCarShare,
          "IsEndAvailable": record.isDigitalStyle,
          "timeZone": record.reservation?.originStationDetails?.timezone,
          "pickUpTime": record.pickUpTime,
          "dropUpTime": record.dropOffTime
        };
        await _bloc?.fetchReservation(record.reservationId ?? "");
        await FlexRouter.pushName(RoutePath.FX_SUMMARY_DETAIL, arguments: data)
            .then((value) => _bloc?.init());
        final result = FxAccountGlobal.getInstance().routeData;
        if (result != null && result is ToastMsgData) {
          try {
            _showMessageBottomSheet(
                context, result.success ?? true, result.message);
          } catch (e) {
            debugPrint(e.toString());
          }
        }
        _bloc?.refreshAllData();
      },
      child: commonSimpleCard(
        child: Container(
          margin:
              EdgeInsets.only(left: 16.w, top: 12.h, right: 16.w, bottom: 11.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      record.displayDate ?? '--',
                      style: ThemeConfig.current().textStyleUtil.subHeadline2,
                    ),
                  ),
                  SizedBox(
                    width: 17.w,
                  ),
                  record.reservation?.rentalType == 'EV_SWAP'
                      ? Container(
                          width: 30.w,
                          height: 30.h,
                          decoration: BoxDecoration(
                            color: _colorUtil.tertiary15,
                            shape: BoxShape.circle,
                          ),
                          child: Image.asset(
                            lexusChargeIcon,
                            height: 16.h,
                            width: 20.w,
                            color: _colorUtil.tertiary03,
                          ),
                        )
                      : Container(),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 2.h,
                        ),
                        _commonSimilar(record.vehicleMode),
                        SizedBox(
                          height: 13.h,
                        ),
                        _commonContainerImageText(
                          getDisplayImage(record),
                          getDisplayTime(record),
                        ),
                        SizedBox(
                          height: 5.h,
                        ),
                        _commonContainerImageText(
                          fxDealerLocationIcon,
                          record.dealerName,
                        ),
                        SizedBox(
                          height: 5.h,
                        ),
                        _commonContainerImageText(
                          'packages/oneapp_common/res/image/svg/toyota/ic_flex_landing_order.svg',
                          "#${record.confirmNumber}",
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 140,
                    child: NoTransparentNetworkImage(
                      record.vehiclePicture ?? carPlaceHolderImage,
                      placeHolder: carPlaceHolderImage,
                      fit: BoxFit.fitWidth,
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget _commonSimilar(String? brand) {
  return Text(
    brand != null && brand != '--' ? "$brand" : "--",
    style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
          color: ThemeConfig.current().colorUtil.tertiary05,
        ),
  );
}

Widget _commonContainerImageText(String image, String? text) {
  return Container(
    child: Row(
      children: [
        SvgPicture.asset(
          image,
          width: 24,
          height: 24,
          colorFilter: ColorFilter.mode(
            ThemeConfig.current().colorUtil.tertiary03,
            BlendMode.srcIn,
          ),
          alignment: Alignment.center,
          fit: BoxFit.scaleDown,
        ),
        Expanded(
          child: Container(
            margin: EdgeInsets.only(left: 8),
            child: Text(
              text?.isNotEmpty == true ? text! : '--',
              softWrap: false,
              textAlign: TextAlign.left,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: ThemeConfig.current().textStyleUtil.callout1,
            ),
          ),
        ),
      ],
    ),
  );
}

Widget bottomSheetHandle() {
  return Semantics(
    label: SWIPE_BAR,
    child: Container(
      margin: EdgeInsets.only(top: 16.h, bottom: 4.h),
      height: 4.h,
      width: 28.w,
      decoration: BoxDecoration(
          border: Border.all(
            color: ThemeConfig.current().colorUtil.tertiary10,
          ),
          color: ThemeConfig.current().colorUtil.tertiary10,
          borderRadius: BorderRadius.all(Radius.circular(CARD_RADIUS_SMALL))),
    ),
  );
}

_showMessageBottomSheet(BuildContext context, bool success, String message) {
  showMaterialModalBottomSheet(
    expand: false,
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(CARD_RADIUS),
      ),
    ),
    clipBehavior: Clip.antiAliasWithSaveLayer,
    backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
    builder: (context) {
      return FxMessageBottomView(
        success: success,
        message: message,
      );
    },
  );
}
