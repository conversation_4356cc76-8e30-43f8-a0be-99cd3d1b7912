// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:vehicle_module/ui/vehicle_overview/vehicle_announcement/vehicle_announcement_ev_swap_overview.dart';

// Project imports:
import '../../flex_global.dart';
import '../../route/router.dart';
import '../summary/fx_summary_dialog_view.dart';
import 'fx_detail_landing_bloc.dart';
import 'fx_landing_bottom_sheet.dart';
import 'fx_reserve_Balance_model.dart';

class FXReserveBalancePage extends StatefulWidget {
  final BuildContext? buildContext;
  final FxDetailLandingBloc? bloc;

  const FXReserveBalancePage({Key? key, this.buildContext, this.bloc})
      : super(key: key);

  @override
  State<FXReserveBalancePage> createState() => _FXReserveBalancePageState();
}

class _FXReserveBalancePageState extends State<FXReserveBalancePage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;

  @override
  Widget build(BuildContext context) {
    return OneAppScaffold(
      backgroundColor: _colorUtil.tertiary15,
      body: Column(
        children: [
          bottomSheetCustomAppBar(OneAppString.of().lexusReserveBalance,
              onBackPressed: () {
            Navigator.of(context).pop();
          }, elevation: 0, actionWidget: actionInformationWidget()),
          Expanded(
            child: StreamBuilder(
                stream: widget.bloc?.reserveBalancecards,
                builder: (ctx, snapData) {
                  var reservedBalanceCard =
                      snapData.data as List<ReserveBalanceCards>;
                  return Container(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 23.h,
                          ),
                          Container(
                            margin: EdgeInsets.all(16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Expanded(
                                  child: Container(
                                    height: 104.h,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          margin: EdgeInsets.only(
                                            left: 10.w,
                                            top: 14.h,
                                          ),
                                          child: SvgPicture.asset(
                                            reservedBalanceCard.first.icon ??
                                                "",
                                            colorFilter: ColorFilter.mode(
                                              _colorUtil.secondary01,
                                              BlendMode.srcIn,
                                            ),
                                            semanticsLabel: CUSTOM_TILE_ICON +
                                                getIconNameFromPath(
                                                    lexusChargeIconBlue,
                                                    ".svg"),
                                          ),
                                        ),
                                        Container(
                                          margin: EdgeInsets.only(
                                              left: 10.w,
                                              top: 5.h,
                                              bottom: 5.h),
                                          child: Text(
                                            reservedBalanceCard.first.title ??
                                                "",
                                            style: TextStyleExtension()
                                                .newStyleWithColor(
                                                    _textStyleUtil.subHeadline1,
                                                    _colorUtil.tertiary00),
                                          ),
                                        ),
                                        Container(
                                          margin: EdgeInsets.only(
                                              left: 10, bottom: 0),
                                          child: Text(
                                            reservedBalanceCard
                                                    .first.subTitle ??
                                                "",
                                            style: TextStyleExtension()
                                                .newStyleWithColor(
                                                    _textStyleUtil.caption2,
                                                    _colorUtil.tertiary00),
                                          ),
                                        )
                                      ],
                                    ),
                                    decoration: BoxDecoration(
                                      color: _colorUtil.tile02,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(CARD_RADIUS_SMALL)),
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  width: 16.w,
                                ),
                                (reservedBalanceCard.isNotEmpty &&
                                        reservedBalanceCard.isNotEmpty)
                                    ? Expanded(
                                        child: Container(
                                          height: 104.h,
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                margin: EdgeInsets.only(
                                                    left: 10, top: 14),
                                                child: SvgPicture.asset(
                                                  reservedBalanceCard[1].icon ??
                                                      "",
                                                  colorFilter: ColorFilter.mode(
                                                    (reservedBalanceCard[1]
                                                                .subTitle ==
                                                            FLEX_EXPIRATION_DATE_BALANCE)
                                                        ? _colorUtil.secondary01
                                                        : _colorUtil.error01,
                                                    BlendMode.srcIn,
                                                  ),
                                                  semanticsLabel:
                                                      CUSTOM_TILE_ICON +
                                                          getIconNameFromPath(
                                                              lexusChargeIconBlue,
                                                              ".svg"),
                                                ),
                                              ),
                                              Container(
                                                margin: EdgeInsets.only(
                                                    left: 10,
                                                    top: 5,
                                                    bottom: 5),
                                                child: Text(
                                                  reservedBalanceCard[1]
                                                          .title ??
                                                      "",
                                                  style: TextStyleExtension()
                                                      .newStyleWithColor(
                                                          _textStyleUtil
                                                              .subHeadline1,
                                                          _colorUtil
                                                              .tertiary00),
                                                ),
                                              ),
                                              Container(
                                                margin: EdgeInsets.only(
                                                    left: 10, bottom: 0),
                                                child: Text(
                                                  reservedBalanceCard[1]
                                                          .subTitle ??
                                                      "",
                                                  style: TextStyleExtension()
                                                      .newStyleWithColor(
                                                          _textStyleUtil
                                                              .caption2,
                                                          _colorUtil
                                                              .tertiary00),
                                                ),
                                              )
                                            ],
                                          ),
                                          decoration: BoxDecoration(
                                            color: _colorUtil.tile02,
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(
                                                    CARD_RADIUS_SMALL)),
                                          ),
                                        ),
                                      )
                                    : Container(),
                              ],
                            ),
                            height: 110.h,
                          ),
                          StreamBuilder(
                            stream: widget.bloc?.reserveBalance,
                            builder: (ctx, snapData) {
                              if (snapData.data != null) {
                                var reservedBalance =
                                    snapData.data as List<ReserveBalance>;
                                if (reservedBalance.isNotEmpty) {
                                  return Container(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          margin: EdgeInsets.only(left: 16),
                                          child:
                                              Text(FLEX_TRANSACTIONS_BALANCE),
                                        ),
                                        Container(
                                          child: ListView.builder(
                                              shrinkWrap: true,
                                              itemCount: reservedBalance.length,
                                              physics:
                                                  NeverScrollableScrollPhysics(),
                                              padding: EdgeInsets.only(
                                                  left: 16.w,
                                                  right: 16.w,
                                                  bottom: 8.h),
                                              itemBuilder:
                                                  (BuildContext context,
                                                      int index) {
                                                return Container(
                                                  margin: EdgeInsets.only(
                                                      bottom: 8.h),
                                                  height: 72.h,
                                                  child: Container(
                                                    child: Column(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Container(
                                                          margin:
                                                              EdgeInsets.only(
                                                                  left: 16,
                                                                  top: 12,
                                                                  bottom: 2),
                                                          child: Text(
                                                            reservedBalance[
                                                                        index]
                                                                    .title ??
                                                                "",
                                                            style: TextStyleExtension()
                                                                .newStyleWithColor(
                                                                    _textStyleUtil
                                                                        .body4,
                                                                    _colorUtil
                                                                        .tertiary03),
                                                          ),
                                                        ),
                                                        Container(
                                                          margin:
                                                              EdgeInsets.only(
                                                                  left: 16,
                                                                  bottom: 12),
                                                          child: Text(
                                                            reservedBalance[
                                                                        index]
                                                                    .subTitle ??
                                                                "",
                                                            style: TextStyleExtension()
                                                                .newStyleWithColor(
                                                                    _textStyleUtil
                                                                        .callout1,
                                                                    _colorUtil
                                                                        .tertiary05),
                                                          ),
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: _colorUtil.tile02,
                                                    borderRadius: BorderRadius
                                                        .all(Radius.circular(
                                                            CARD_RADIUS_SMALL)),
                                                  ),
                                                );
                                              }),
                                        )
                                      ],
                                    ),
                                  );
                                } else {
                                  return Container();
                                }
                              }
                              return Container();
                            },
                          ),
                          // _transactions(),
                        ],
                      ),
                    ),
                  );
                }),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: StreamBuilder(
                stream: widget.bloc?.EVCredits,
                builder: (ctx, creditData) {
                  int? remainingCredits = creditData.data;
                  return StreamBuilder(
                      stream: widget.bloc?.reserveBalancecards,
                      builder: (ctx, balancedata) {
                        var reservedBalanceCard =
                            balancedata.data as List<ReserveBalanceCards>;
                        return Padding(
                          padding: EdgeInsets.only(bottom: 18.h),
                          child: SizedBox(
                            width: 192.w,
                            height: 52.h,
                            child: CustomDefaultButton(
                              primaryButtonState: remainingCredits != null &&
                                          remainingCredits != 0 ||
                                      (reservedBalanceCard.isNotEmpty &&
                                          reservedBalanceCard[1].subTitle !=
                                              FLEX_EXPIRED_BALANCE)
                                  ? PrimaryButtonState.ACTIVE
                                  : PrimaryButtonState.INACTIVE,
                              backgroundColor:
                                  ThemeConfig.current().colorUtil.button01b,
                              buttonTextColor:
                                  ThemeConfig.current().colorUtil.button01a,
                              disabledBackgroundColor:
                                  ThemeConfig.current().colorUtil.button02c,
                              borderColor:
                                  ThemeConfig.current().colorUtil.button01b,
                              text: OneAppString.of().makeAReservation,
                              verticalPadding: 4.h,
                              press: remainingCredits != null &&
                                          remainingCredits != 0 ||
                                      (reservedBalanceCard.isNotEmpty &&
                                          reservedBalanceCard[1].subTitle !=
                                              "Expired")
                                  ? () async {
                                      if (isFeatureEnabled(
                                          FLEX_EV_SWAP,
                                          widget.bloc?.vehicleInfoEntity
                                              ?.features)) {
                                        await _showEVSwapCredits(
                                            remainingCredits ?? 0);
                                      } else {
                                        await FlexRouter.pushName(
                                            RoutePath.FX_SEARCH_DEALER);
                                        final result =
                                            FxAccountGlobal.getInstance()
                                                .routeData;
                                        if (result != null &&
                                            result is ToastMsgData) {
                                          Future(() {
                                            _showMessageBottomSheet(
                                                context,
                                                result.success ?? true,
                                                result.message);
                                          });
                                        }
                                      }
                                    }
                                  : () {},
                            ),
                          ),
                        );
                      });
                }),
          )
        ],
      ),
    );
  }

  Widget actionInformationWidget() {
    return MaterialButton(
      onPressed: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => VehicleAnnouncementEVSwapOverView(
                      avlCredit: "",
                      comeFromWallet: true,
                    )));
      },
      child: SvgPicture.asset(
        informationIcon,
        width: 30.w,
        colorFilter: ColorFilter.mode(
          _colorUtil.tertiary03,
          BlendMode.srcIn,
        ),
        allowDrawingOutsideViewBox: true,
      ),
      padding: EdgeInsets.all(5.w),
      shape: CircleBorder(),
    );
  }

  Future _showEVSwapCredits(int credits) {
    return showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return FxSummaryDialogView(
          title: "$credits ${OneAppString.of().evSwapCredits}",
          description: OneAppString.of().bookUsingAvailableEVCredits,
          primaryButtonText: OneAppString.of().reserveWithCredits,
          additionalButtonText: OneAppString.of().notNow,
          primaryButtonPressed: () async {
            await FlexRouter.pushName(RoutePath.FX_SEARCH_DEALER);
            final result = FxAccountGlobal.getInstance().routeData;
            if (result != null && result is ToastMsgData) {
              Future(() {
                _showMessageBottomSheet(
                    context, result.success ?? true, result.message);
              });
            }
          },
          additionalButtonPressed: () async {
            FlexRouter.popRoute();
          },
        );
      },
    );
  }

  _showMessageBottomSheet(BuildContext context, bool success, String message) {
    showMaterialModalBottomSheet(
      expand: false,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      builder: (context) {
        return FxMessageBottomView(
          success: success,
          message: message,
        );
      },
    );
  }
}
