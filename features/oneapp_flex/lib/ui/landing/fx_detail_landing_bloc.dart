// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/foundation.dart';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_customer_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_ev_swap_balance.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_reservation_list_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/landing_list_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/rent_car_info_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';
import 'package:oneapp_network_implementation/flexcfai/flex_cfai_api_client.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../flex_global.dart';
import '../../util/fx_analytics_event.dart';
import '../../util/fx_common_util.dart';
import '../search/fx_search_dealer_bloc.dart';
import 'fx_reserve_Balance_model.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_location_entity.dart'
    as vehicleLocation;

const String ACTIVE_RESPONSE_STATUS = "Booked";
const String PAST_RESPONSE_STATUS = "Cancelled";
const String PAST_ACTION_KEY = "Past";

const String UPCOMING_ACTION_KEY = "UPCOMING";
const String READY_ACTION_KEY = "READY";
const String IN_PROGRESS_ACTION_KEY = "IN_PROGRESS";

const String IN_CANCELED_COMPLETED_KEY =
    "COMPLETED,CANCELED,CANCELLED,UPCOMING,NO_SHOW";

const String OVERDUE_ACTION_KEY = "OVERDUE";
const String COMPLETED_ACTION_KEY = "COMPLETED";
const String CANCELLED_ACTION_KEY = "CANCELLED";
const String CANCELED_ACTION_KEY = "CANCELED";
const String ABANDOND_ACTION_KEY = "ABANDOND";
const String NO_SHOW_ACTION_KEY = "NO_SHOW";

const int PAGE_NUM = 10;
CommonResponse<FxEvSwapBalance>? EvSwapBalanceResponse;
GetIt locator = GetIt.instance;

class FxDetailLandingBloc implements BlocBase {
  FXCFAIClient cfaiApi = APIClientConfig.flexClient;

  OneAppClient api = APIClientConfig.oneAppClient;

  final _recordController = BehaviorSubject<LandingUI>();

  Stream<LandingUI> get uiRecord => _recordController.stream;

  final _toastController = BehaviorSubject<ToastMsgData>();

  Stream<ToastMsgData> get uiToast => _toastController.stream;

  Stream<Position> get vehiclePosition => _vehiclePosition.stream;
  final _vehiclePosition = BehaviorSubject<Position>();

  Stream<String> get refreshTab => _refreshTabSubject.stream;
  final _refreshTabSubject = BehaviorSubject<String>();

  Stream<Position?> get currentLocation => _currentLocation.stream;
  final _currentLocation = BehaviorSubject<Position?>();

  Stream<bool> get readyToShow => _readyToShow.stream;
  final _readyToShow = BehaviorSubject<bool>();

  Stream<int?> get EVCredits => _EVCredits.stream;
  final _EVCredits = BehaviorSubject<int?>();

  Stream<List<ReserveBalance>> get reserveBalance => _reserveBalance.stream;
  final _reserveBalance = BehaviorSubject<List<ReserveBalance>>();

  Stream<List<ReserveBalanceCards>> get reserveBalancecards =>
      _reserveBalancecards.stream;
  final _reserveBalancecards = BehaviorSubject<List<ReserveBalanceCards>>();

  String EVExpDate = "";
  bool isLexusReserveExpired = false;

  CustomerStatus? customerStatus;

  int progressOffset = 0;
  int upcomingOffset = 0;
  int cancelledOffset = 0;

  bool progressNoMore = false;
  bool upcomingNoMore = false;
  bool cancelledNoMore = false;

  List<String> tabNames = [];

  List<LandingCardUI> progressList = [];
  List<LandingCardUI> upcomingList = [];
  List<LandingCardUI> cancelledList = [];
  List<ReserveBalance> reserveBalanceData = [];
  List<ReserveBalanceCards> reserveBalanceCardsData = [];

  DateTime? updatedTime;

  Future<void> init() async {
    progressOffset = 0;
    upcomingOffset = 0;
    cancelledOffset = 0;

    progressNoMore = false;
    upcomingNoMore = false;
    cancelledNoMore = false;

    progressList.clear();
    upcomingList.clear();
    cancelledList.clear();

    await getAllReservationsList(status: IN_PROGRESS_ACTION_KEY);
    await getAllReservationsList(status: UPCOMING_ACTION_KEY);
    await getAllReservationsList(status: IN_CANCELED_COMPLETED_KEY);
  }

  vehicleInfo.Payload? vehicleInfoEntity;

  Future refreshAllData(
      {bool allowCache = false, CommonResponse? commonResponse}) async {
    vehicleInfoEntity = await getLocalPayloadFromVin(Global.getInstance().vin);
    locator.isReady<List<vehicleInfo.Payload>>().then((value) async {
      // ignore: unused_local_variable
      List<vehicleInfo.Payload> vehicleEntityList = [];
      try {
        vehicleEntityList = locator<List<vehicleInfo.Payload>>();
      } catch (e) {}
    });
    fetchCurrentLocation();
    loadAccount(allowCache: allowCache);
    updatedTime = DateTime.now();
    if (!_readyToShow.isClosed) {
      _readyToShow.add(true);
    }
  }

  void refreshTabChoice(String choice) {
    _refreshTabSubject.add(choice);
  }

  Future<void> loadAccount({bool allowCache = false}) async {
    CommonResponse<FxCfaiCustomerResponse>? commonResponse;
    try {
      commonResponse = await cfaiApi.sendGetCustomersInfo(
          allowCache: allowCache, customerGuid: Global.getInstance().guid);
    } catch (e) {
      debugPrint(e.toString());
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_RENT);
    }
    if (commonResponse?.response != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_GET_ACCOUNT_SUCCESS,
          category: LogCategory.FL_RENT);
      FxAccountGlobal.getInstance().insurance =
          commonResponse?.response?.payload?.result?.insurance;
      FxAccountGlobal.getInstance().license =
          commonResponse?.response?.payload?.result?.license;
      FxAccountGlobal.getInstance().customersEntity =
          commonResponse?.response?.payload?.result;
      customerStatus = FxCommonUtils.getCustomerStatus(
          commonResponse?.response?.payload?.result?.associations);
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          AnalyticsEvent.FLEX_GET_ACCOUNT_FAILURE,
          category: LogCategory.FL_RENT);
      FxAccountGlobal.getInstance().insurance = null;
      FxAccountGlobal.getInstance().license = null;
      FxAccountGlobal.getInstance().customersEntity = null;
      customerStatus = CustomerStatus(status: CUSTOMER_STATUS_REJECTED);
    }
  }

  Future fetchCurrentLocation() async {
    if (Global.getInstance().currentLocation != null &&
        !this._currentLocation.isClosed) {
      this._currentLocation.add(Global.getInstance().currentLocation);
    }
    bool isGranted = await MapUtil.checkLocationGranted();
    if (isGranted) {
      Position _currentLocation = await MapUtil.getCurrentLocation();
      Global.getInstance().currentLocation = _currentLocation;
      if (!this._currentLocation.isClosed) {
        this._currentLocation.add(_currentLocation);
      }
    }
  }

  Future<void> fetchVehicleLocation() async {
    String? vin = Global.getInstance().vin;
    if (vin?.isNotEmpty == true) {
      if (vehicleInfoEntity != null) {
        bool isCY17 = isCY17Vehicle(vehicleInfoEntity?.generation);
        CommonResponse commonResponse;
        try {
          commonResponse = await api.fetchVehicleLocation(isCY17, vin ?? "",
              vehicleInfoEntity?.brand ?? Global.getInstance().appBrand);
        } catch (e) {
          FireBaseAnalyticsLogger.logError(e.toString(),
              category: LogCategory.FL_RENT);
          return;
        }
        final payLoad = commonResponse.response?.payload;
        if (payLoad != null) {
          vehicleLocation.Payload vehicleLocationPayload = payLoad;

          if (vehicleLocationPayload.latitude != null &&
              vehicleLocationPayload.longitude != null) {
            double vehicleLatitude = vehicleLocationPayload.latitude ?? 0.00;
            double vehicleLongitude = vehicleLocationPayload.longitude ?? 0.00;
            Position vehiclePosition = Position(
              vehicleLongitude,
              vehicleLatitude,
            );
            _vehiclePosition.add(vehiclePosition);
          }
        }
      }
    }
  }

  Future<void> getAllReservationsList({String? status}) async {
    final nowTimeStamp = DateTime.now().millisecondsSinceEpoch;
    final threeHoursStamp = Duration(hours: 3).inMilliseconds;
    if (status == IN_PROGRESS_ACTION_KEY) {
      await showProgress(NavigateService.context);
    }
    final commonResponse = await cfaiApi.sendGetReservationsList(
        status: status,
        driverId: Global.getInstance().guid,
        rentalType: isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features)
            ? FLEX_RENTALS_EV_SWAP
            : FLEX_RENTALS_RENTAL,
        vinBrand: vehicleInfoEntity?.brand ?? Global.getInstance().appBrand,
        vin: vehicleInfoEntity?.vin ?? Global.getInstance().vin,
        offset: "0",
        limit: PAGE_NUM.toString());
    if (status == IN_CANCELED_COMPLETED_KEY) {
      await dismissProgress(NavigateService.context);
    }
    final landingUI =
        _handleReservations(commonResponse.response?.payload?.reservations);

    if ((!isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features) &&
            commonResponse.response?.payload?.reservations?.isNotEmpty ==
                true &&
            status == IN_PROGRESS_ACTION_KEY) ||
        (status == IN_PROGRESS_ACTION_KEY)) {
      if (!tabNames.contains(OneAppString.of().registerActive)) {
        tabNames.add(OneAppString.of().registerActive);
      }
      progressList.addAll(landingUI.totalList);
      if (progressList.length <
          (commonResponse.response?.payload?.pagination?.total ?? 0)) {
        progressOffset++;
      } else {
        progressNoMore = true;
      }
    }
    if ((!isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features) &&
            commonResponse.response?.payload?.reservations?.isNotEmpty ==
                true &&
            status == UPCOMING_ACTION_KEY) ||
        (status == UPCOMING_ACTION_KEY)) {
      upcomingList.addAll(landingUI.totalList.where((element) =>
          (element.pickUpTimeStamp ?? 0) > nowTimeStamp - threeHoursStamp &&
          element.status == UPCOMING_ACTION_KEY));

      if (!tabNames.contains(OneAppString.of().upcomingHeading)) {
        tabNames.add(OneAppString.of().upcomingHeading);
      }
      if (upcomingList.length <
          (commonResponse.response?.payload?.pagination?.total ?? 0)) {
        upcomingOffset++;
      } else {
        upcomingNoMore = true;
      }
    }
    if ((!isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features) &&
            commonResponse.response?.payload?.reservations?.isNotEmpty ==
                true &&
            status == IN_CANCELED_COMPLETED_KEY) ||
        (status == IN_CANCELED_COMPLETED_KEY)) {
      if (!tabNames.contains(OneAppString.of().registerPast)) {
        tabNames.add(OneAppString.of().registerPast);
      }
      cancelledList.addAll(landingUI.totalList.where((element) =>
          element.status !=
          UPCOMING_ACTION_KEY)); //Condition to avoid duplicate results
      cancelledList.addAll(landingUI.totalList.where((element) =>
          (element.pickUpTimeStamp ?? 0) < nowTimeStamp - threeHoursStamp &&
          element.status == UPCOMING_ACTION_KEY));
      if (cancelledList.length <
          (commonResponse.response?.payload?.pagination?.total ?? 0)) {
        cancelledOffset++;
      } else {
        cancelledNoMore = true;
      }
    }
    if (commonResponse.error != null && !_toastController.isClosed) {
      _toastController
          .add(ToastMsgData(commonResponse.error?.errorMessage ?? ""));
      landingUI.hasError = true;
    }
    if (!_recordController.isClosed && status == IN_CANCELED_COMPLETED_KEY) {
      _recordController.add(landingUI);
    }
  }

  Future<void> getAllReservationsListMore({String? status}) async {
    final nowTimeStamp = DateTime.now().millisecondsSinceEpoch;
    final threeHoursStamp = Duration(hours: 3).inMilliseconds;
    if (progressNoMore == true && status == IN_PROGRESS_ACTION_KEY) {
      return;
    }
    if (upcomingNoMore == true && status == UPCOMING_ACTION_KEY) {
      return;
    }
    if (cancelledNoMore == true && status == IN_CANCELED_COMPLETED_KEY) {
      return;
    }
    int? currentOffset;
    switch (status) {
      case IN_PROGRESS_ACTION_KEY:
        {
          currentOffset = progressOffset * PAGE_NUM;
          break;
        }
      case UPCOMING_ACTION_KEY:
        {
          currentOffset = upcomingOffset * PAGE_NUM;
          break;
        }
      case IN_CANCELED_COMPLETED_KEY:
        {
          currentOffset = cancelledOffset * PAGE_NUM;
          break;
        }
    }
    await showProgress(NavigateService.context);
    final commonResponse = await cfaiApi.sendGetReservationsList(
        status: status,
        rentalType: isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features)
            ? FLEX_RENTALS_EV_SWAP
            : FLEX_RENTALS_RENTAL,
        vinBrand: vehicleInfoEntity?.brand ?? Global.getInstance().appBrand,
        vin: vehicleInfoEntity?.vin ?? Global.getInstance().vin,
        driverId: Global.getInstance().guid,
        limit: PAGE_NUM.toString(),
        offset: currentOffset.toString());
    await dismissProgress(NavigateService.context);
    final landingUI =
        _handleReservations(commonResponse.response?.payload?.reservations);

    if (commonResponse.response?.payload?.reservations?.isNotEmpty == true &&
        status == IN_PROGRESS_ACTION_KEY) {
      progressList.addAll(landingUI.totalList);
      if (progressList.length <
          (commonResponse.response?.payload?.pagination?.total ?? 0)) {
        progressOffset++;
      } else {
        progressNoMore = true;
      }
    }
    if (commonResponse.response?.payload?.reservations?.isNotEmpty == true &&
        status == UPCOMING_ACTION_KEY) {
      upcomingList.addAll(landingUI.totalList.where((element) =>
          (element.pickUpTimeStamp ?? 0) > nowTimeStamp - threeHoursStamp &&
          element.status == UPCOMING_ACTION_KEY));
      if (upcomingList.length <
          (commonResponse.response?.payload?.pagination?.total ?? 0)) {
        upcomingOffset++;
      } else {
        upcomingNoMore = true;
      }
    }
    if (commonResponse.response?.payload?.reservations?.isNotEmpty == true &&
        status == IN_CANCELED_COMPLETED_KEY) {
      cancelledList.addAll(landingUI.totalList.where((element) =>
          element.status !=
          UPCOMING_ACTION_KEY)); //Condition to avoid duplicate results
      cancelledList.addAll(landingUI.totalList.where((element) =>
          (element.pickUpTimeStamp ?? 0) < nowTimeStamp - threeHoursStamp &&
          element.status == UPCOMING_ACTION_KEY));
      if (cancelledList.length <
          (commonResponse.response?.payload?.pagination?.total ?? 0)) {
        cancelledOffset++;
      } else {
        cancelledNoMore = true;
      }
    }

    if (commonResponse.error != null && !_toastController.isClosed) {
      _toastController
          .add(ToastMsgData(commonResponse.error?.errorMessage ?? ""));
      landingUI.hasError = true;
    }
    if (!_recordController.isClosed) {
      _recordController.add(landingUI);
    }
  }

  Future<DateTime> creditExpireDate() async {
    vehicleInfoEntity = await getLocalPayloadFromVin(Global.getInstance().vin);
    if (isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features) &&
        Global.getInstance().guid != null &&
        Global.getInstance().vin != null) {
      EvSwapBalanceResponse = await cfaiApi.sendgetEVSwapBalance(
          Global.getInstance().guid!, Global.getInstance().vin!, true, true);
    }
    return DateTime.parse(
        EvSwapBalanceResponse?.response?.payload?.first.validTo ?? "");
  }

  Future<int?> availableCredits() async {
    reserveBalanceData.clear();
    reserveBalanceCardsData.clear();
    vehicleInfoEntity = await getLocalPayloadFromVin(Global.getInstance().vin);
    if (isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features)) {
      EvSwapBalanceResponse = await cfaiApi.sendgetEVSwapBalance(
          Global.getInstance().guid ?? "",
          Global.getInstance().vin ?? "",
          true,
          true);
      if (EvSwapBalanceResponse?.response?.payload != null &&
          EvSwapBalanceResponse?.response?.payload?.first.remainingBalance !=
              null) {
        _EVCredits.add(
            EvSwapBalanceResponse?.response?.payload?.first.remainingBalance);
        reserveBalanceCardsData.add(ReserveBalanceCards(
            lexusChargeIconBlue,
            "${EvSwapBalanceResponse?.response?.payload?.first.remainingBalance}",
            "Reserve Days Remaining"));
        String? evValidTo =
            EvSwapBalanceResponse?.response?.payload?.first.validTo;
        if (EvSwapBalanceResponse?.response?.payload?.first.validTo != null &&
            EvSwapBalanceResponse?.response?.payload?.first.validTo != "") {
          Global.getInstance().evSwapExpireDate = evValidTo ?? "";
          DateTime todayDate = DateFormat('yyyy-MM-dd')
              .parse(DateTime.now().toLocal().toString());
          final evSwapExpireDate = DateTime.parse(evValidTo ?? "").toLocal();
          isLexusReserveExpired = todayDate.isAfter(evSwapExpireDate);
          var outputFormat = DateFormat('MM/dd/yyyy');
          var outputDate = outputFormat.format(evSwapExpireDate);
          reserveBalanceCardsData.add(ReserveBalanceCards(
              dataIcon(evSwapExpireDate),
              outputDate,
              dataComprationInExp(evSwapExpireDate)));
          _reserveBalancecards.add(reserveBalanceCardsData);
        }
        if (EvSwapBalanceResponse?.response?.payload?.first.reservations !=
            null) {
          listReserveBalance(
              EvSwapBalanceResponse?.response?.payload?.first.reservations);
        }
      } else {
        _EVCredits.add(0);
      }
    }
    return EvSwapBalanceResponse?.response?.payload?.first.remainingBalance;
  }

  String dataIcon(DateTime? expireDate) {
    if (expireDate != null) {
      DateTime todayDate =
          DateFormat('yyyy-MM-dd').parse(DateTime.now().toLocal().toString());
      if (todayDate.isAfter(expireDate)) {
        return svgAlertIcon;
      } else if (expireDate.difference(todayDate).inDays <= 60) {
        return svgAlertIcon;
      }
    }
    return scheduleIcon;
  }

  String dataComprationInExp(DateTime? expireDate) {
    if (expireDate != null) {
      DateTime todayDate =
          DateFormat('yyyy-MM-dd').parse(DateTime.now().toLocal().toString());
      if (todayDate.isAfter(expireDate)) {
        return FLEX_EXPIRED_BALANCE;
      } else if (expireDate.difference(todayDate).inDays <= 60) {
        return FLEX_EXPIRED_SOON_BALANCE;
      }
    }
    return FLEX_EXPIRATION_DATE_BALANCE;
  }

  String pickDataCompartion(String? pickFromData) {
    if (pickFromData != null && pickFromData != "") {
      final pickData = DateTime.parse(pickFromData).toLocal();
      var outputFormat = DateFormat('MMM d, yyyy');
      var outputDate = outputFormat.format(pickData);
      return "$outputDate";
    }
    return "";
  }

  // ignore: type_annotate_public_apis
  listReserveBalance(List<Reservations>? reservations) {
    if (reservations != null) {
      for (final element in reservations) {
        String? dateTime = element.pickupDateTime != null &&
                element.pickupDateTime?.isNotEmpty == true
            ? element.pickupDateTime
            : element.timestamp;
        var balanceAndTime =
            "${element.rentalDays} $FLEX_RESERVE_DAY_BALANCE • ${pickDataCompartion(dateTime)}";
        if (element.type?.toLowerCase() == FLEX_RESERVATION_BALANCE &&
            element.status?.toLowerCase() == FLEX_UPCOMING_BALANCE) {
          reserveBalanceData.add(ReserveBalance(
              FLEX_RESERVATION_UPCOMING_BALANCE, "-$balanceAndTime"));
        } else if (element.type?.toLowerCase() == FLEX_ADDCREDIT_BALANCE) {
          reserveBalanceData.add(ReserveBalance(
              FLEX_GOODWILL_CREDITS_BALANCE, "+$balanceAndTime"));
        } else if (element.type?.toLowerCase() == FLEX_RESERVATION_BALANCE &&
            element.status?.toLowerCase() == FLEX_COMPLETED_BALANCE) {
          reserveBalanceData.add(ReserveBalance(
              FLEX_RESERVATION_BALANCE.capitalize(), "-$balanceAndTime"));
        } else if (element.type?.toLowerCase() ==
            FLEX_CORRECTION_DELETECREDIT_BALANCE) {
          reserveBalanceData
              .add(ReserveBalance("Correction", "-$balanceAndTime"));
        } else if (element.type?.toLowerCase() ==
            FLEX_CORRECTION_ADDCREDIT_BALANCE) {
          reserveBalanceData
              .add(ReserveBalance("Correction", "+$balanceAndTime"));
        }
      }
      _reserveBalance.add(reserveBalanceData);
    }
  }

  LandingUI _handleReservations(List<FxCfaiReservationListItem>? reservations) {
    List<LandingCardUI> mTotalList = [];
    LandingUI landingUI;
    if (reservations?.isNotEmpty == true) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          AnalyticsEvent.FLEX_GET_RESERVATIONS_SUCCESS,
          category: LogCategory.FL_RENT);
      reservations?.forEach((element) {
        _setRentCarInfo(element, mTotalList);
      });
      mTotalList.sort(
        ((left, right) =>
            left.pickUpTimeStamp!.compareTo(right.pickUpTimeStamp ?? 0)),
      );
      landingUI = LandingUI(null, null, mTotalList, true, false);
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          AnalyticsEvent.FLEX_GET_RESERVATIONS_FAILURE,
          category: LogCategory.FL_RENT);
      landingUI = LandingUI(null, null, mTotalList, false, false);
    }
    return landingUI;
  }

  Future searchDealers() async {
    bool isGranted = await MapUtil.checkLocationGranted();
    if (isGranted) {
      await showProgress(NavigateService.context);
      var obtaionPosition = await MapUtil.getCurrentLocation();
      await cfaiApi.sendGetFlexStations(
        lat: obtaionPosition.lat.toDouble(),
        lon: obtaionPosition.lng.toDouble(),
        radius: searchLimitRadius,
        limitCount: null,
        vinBrand: vehicleInfoEntity?.brand ?? Global.getInstance().appBrand,
        rentalType: isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features)
            ? FLEX_RENTALS_EV_SWAP
            : FLEX_RENTALS_RENTAL,
        vin: vehicleInfoEntity?.vin ?? Global.getInstance().vin,
      );
      await dismissProgress(NavigateService.context);
    }
  }

  Future fetchReservation(String reservationId) async {
    await showProgress(NavigateService.context);
    await cfaiApi.sendGetCustomersInfo(customerGuid: Global.getInstance().guid);
    await cfaiApi.sendGetReservationDetail(
        reservationId,
        isFeatureEnabled(FLEX_EV_SWAP, vehicleInfoEntity?.features)
            ? FLEX_RENTALS_EV_SWAP
            : FLEX_RENTALS_RENTAL,
        vehicleInfoEntity?.brand ?? Global.getInstance().appBrand,
        vehicleInfoEntity?.vin ?? Global.getInstance().vin);
    await dismissProgress(NavigateService.context);
  }

  void _setRentCarInfo(
      FxCfaiReservationListItem reservation, List<LandingCardUI> list) {
    String vehicleMode = reservation.rppReservationDetails?.vehicleDetails
            ?.getVehicleClassDisplay() ??
        "--";
    DateTime pickUpDateTime =
        reservation.claimedAt ?? reservation.startsAt ?? DateTime.now();
    DateTime dropOffDateTime =
        reservation.releasedAt ?? reservation.endsAt ?? DateTime.now();

    int originStationTimeMinOffset =
        reservation.originStationDetails?.getTimezoneMinutesOffset() ?? 0;
    pickUpDateTime =
        _addDealerTimezone(pickUpDateTime, originStationTimeMinOffset);

    int destinationStationTimeMinOffset =
        reservation.destinationStationDetails?.getTimezoneMinutesOffset() ?? 0;
    dropOffDateTime =
        _addDealerTimezone(dropOffDateTime, destinationStationTimeMinOffset);

    list.add(LandingCardUI(
      confirmNumber: reservation.referenceNumber ?? "——",
      pickUpDate: DateFormat("MMM d").format(pickUpDateTime),
      dropOffDate: DateFormat("MMM d").format(dropOffDateTime),
      pickUpTime: DateFormat("h:mma").format(pickUpDateTime).toLowerCase(),
      dropOffTime: DateFormat("h:mma").format(dropOffDateTime).toLowerCase(),
      displayDate: FxCommonUtils.combinedDatePickUpDropOff(
          pickUpDateTime, dropOffDateTime,
          showYear: true),
      vehicleMode: vehicleMode,
      dealerName: reservation.originStationDetails?.name,
      vehiclePicture:
          reservation.rppReservationDetails?.vehicleDetails?.largeImgUrl ??
              reservation.rppReservationDetails?.vehicleDetails?.smallImgUrl,
      reservationId: reservation.id,
      status: reservation.status,
      // licensePlate: reservation.vehicle?.licensePlate,
      isCarShare: CAR_SHARE_PROGRAM_ID.equals(reservation.programId ?? "",
          ignoreCase: true),
      pickUpTimeStamp: pickUpDateTime.millisecondsSinceEpoch,
      dropOffTimeStamp: dropOffDateTime.millisecondsSinceEpoch,
      reservation: reservation,
      hasVehicle: false,
    ));
  }

  String parseDateType(String type, int timeStamp) {
    if (type == "MMM") {
      DateFormat dateFormat = DateFormat("MMM");
      return dateFormat.format(DateTime.fromMillisecondsSinceEpoch(timeStamp));
    } else if (type == "d") {
      DateFormat dateFormat = DateFormat("d");
      return dateFormat.format(DateTime.fromMillisecondsSinceEpoch(timeStamp));
    } else if (type == "yyyy") {
      DateFormat dateFormat = DateFormat("yyyy");
      return dateFormat.format(DateTime.fromMillisecondsSinceEpoch(timeStamp));
    } else {
      return "";
    }
  }

  List<LandingCardUI>? getListFromStream() {
    return _recordController.hasValue
        ? _recordController.value.totalList
        : null;
  }

  List<LandingCardUI>? getListByStatus(List<LandingCardUI>? list, String status,
      {String? status2, String? status3, String? status4}) {
    if (list?.isNotEmpty == true) {
      return list
          ?.where((element) =>
              element.status == status ||
              (status2 != null && element.status == status2) ||
              (status3 != null && element.status == status3) ||
              (status4 != null && element.status == status4))
          .toList();
    }
    return null;
  }

  List<LandingCardUI>? getPastList() {
    if (_recordController.hasValue == true) {
      return filterRentalReservations(_recordController.value.totalList, false);
    }
    return null;
  }

  List<LandingCardUI>? getFutureList() {
    if (_recordController.hasValue == true) {
      return filterRentalReservations(_recordController.value.totalList, true);
    }
    return null;
  }

  @override
  void dispose() {
    _recordController.close();
    _toastController.close();
    _vehiclePosition.close();
    _refreshTabSubject.close();
    _currentLocation.close();
    _readyToShow.close();
    _reserveBalance.close();
  }
}

class CustomerStatus {
  String? createdAt;
  String? status;

  CustomerStatus({this.createdAt, this.status});
}

class RentRecordOld {
  List<RentCarInfo> activeRecord;
  List<RentCarInfo> pastRecord;
  bool retrieved;

  RentRecordOld(this.activeRecord, this.pastRecord, this.retrieved);
}

class LandingUI {
  List<LandingCardUI>? activeRecord;
  List<LandingCardUI>? pastRecord;
  List<LandingCardUI> totalList;
  bool retrieved;
  bool hasError;

  LandingUI(this.activeRecord, this.pastRecord, this.totalList, this.retrieved,
      this.hasError);
}

String getDisplayImage(LandingCardUI landingCardUI) {
  if (landingCardUI.status == IN_PROGRESS_ACTION_KEY) {
    return "packages/oneapp_common/res/image/svg/toyota/ic_flex_drop_off_clock.svg";
  } else if (landingCardUI.status == UPCOMING_ACTION_KEY) {
    return "packages/oneapp_common/res/image/svg/toyota/ic_flex_detail_clock.svg";
  } else if (landingCardUI.status == COMPLETED_ACTION_KEY) {
    return "packages/oneapp_common/res/image/svg/toyota/ic_flex_status_new_complete.svg";
  } else if (landingCardUI.status == CANCELLED_ACTION_KEY ||
      landingCardUI.status == CANCELED_ACTION_KEY) {
    return "packages/oneapp_common/res/image/svg/toyota/ic_flex_status_cancel.svg";
  } else {
    return 'packages/oneapp_common/res/image/svg/toyota/ic_flex_landing_clock.svg';
  }
}

String getDisplayTime(LandingCardUI landingCardUI) {
  if (landingCardUI.status == IN_PROGRESS_ACTION_KEY) {
    return "${OneAppString.of().dropOff} ${OneAppString.of().at} ${landingCardUI.dropOffTime}";
  } else if (landingCardUI.status == UPCOMING_ACTION_KEY) {
    return "${OneAppString.of().pickUp} • ${landingCardUI.pickUpTime}";
  } else if (landingCardUI.status == CANCELLED_ACTION_KEY ||
      landingCardUI.status == CANCELED_ACTION_KEY) {
    return OneAppString.of().statusCancelled;
  }
  return OneAppString.of().statusComplete;
}

bool isCY17Vehicle(String? generation) {
  generation = generation ?? "";
  return ("17CY".toLowerCase() == generation ||
          "PRE17CY".toLowerCase() == generation)
      ? true
      : false;
}

Future<vehicleInfo.Payload?> getLocalPayloadFromVin(String? vin) async {
  List<vehicleInfo.Payload> vehicleInfoListEntity =
      GetIt.I<List<vehicleInfo.Payload>>();
  vehicleInfo.Payload? vehicleInfoEntity;
  if (vehicleInfoListEntity.isNotEmpty) {
    List<vehicleInfo.Payload> vehicleList =
        vehicleInfoListEntity.where((element) => element.vin == vin).toList();
    if (vehicleList.isNotEmpty) {
      vehicleInfoEntity = vehicleList.first;
    }
  }
  return vehicleInfoEntity;
}

List<LandingCardUI> filterRentalReservations(
    List<LandingCardUI> list, bool filterFuture) {
  final nowTimeStamp = DateTime.now().millisecondsSinceEpoch;
  final threeHoursStamp = Duration(hours: 3).inMilliseconds;
  List<LandingCardUI> pastList = [];
  if (list.isNotEmpty == true) {
    if (filterFuture) {
      return list
          .where((element) =>
              (element.pickUpTimeStamp ?? 0) > (nowTimeStamp - threeHoursStamp))
          .toList();
    } else {
      pastList.addAll(list
          .where((element) =>
              (element.pickUpTimeStamp ?? 0) <=
              (nowTimeStamp - threeHoursStamp))
          .toList());
      pastList.addAll(list
          .where((element) =>
              (element.pickUpTimeStamp ?? 0) >
                  (nowTimeStamp - threeHoursStamp) &&
              (element.status == CANCELED_ACTION_KEY ||
                  element.status == CANCELLED_ACTION_KEY ||
                  element.status == NO_SHOW_ACTION_KEY ||
                  element.status == COMPLETED_ACTION_KEY))
          .toList());
      return pastList;
    }
  }
  return list;
}

DateTime _addDealerTimezone(
    DateTime dealerDateTime, int dealerTimezoneMinOffset) {
  return dealerDateTime.add(Duration(minutes: dealerTimezoneMinOffset));
}
