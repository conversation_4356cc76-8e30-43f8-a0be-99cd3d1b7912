// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';

// Project imports:
import '../../route/router.dart';

class FxMessageBottomView extends StatefulWidget {
  final String? message;

  final bool success;

  const FxMessageBottomView({Key? key, this.message, this.success = true})
      : super(key: key);

  @override
  _FxMessageBottomViewState createState() => _FxMessageBottomViewState();
}

class _FxMessageBottomViewState extends State<FxMessageBottomView> {
  @override
  Widget build(BuildContext context) {
    bool success = widget.success;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 24.h,
          ),
          CommonCircleIconImage(
            success
                ? 'packages/oneapp_common/res/image/svg/toyota/ic_toast_check.svg'
                : 'packages/oneapp_common/res/image/svg/toyota/ic_toast_check.svg',
            iconWidth: 24.w,
            iconHeight: 24.w,
            iconPadding: EdgeInsets.all(12.w),
            iconTintColor: success
                ? ThemeConfig.current().colorUtil.secondary01
                : ThemeConfig.current().colorUtil.primary01,
            circleBackgroundColor: success
                ? ThemeConfig.current().colorUtil.success02
                : ThemeConfig.current().colorUtil.primary02,
          ),
          SizedBox(
            height: 16.h,
          ),
          Text(
            success
                ? '${OneAppString.of().success}!'
                : OneAppString.of().failure,
            style: ThemeConfig.current().textStyleUtil.subHeadline1,
          ),
          SizedBox(
            height: 8.h,
          ),
          Text(
            widget.message ?? '',
            textAlign: TextAlign.center,
            style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary05,
                ),
          ),
          SizedBox(
            height: 110.h,
          ),
          _done(),
        ],
      ),
    );
  }

  Widget _done() {
    return Padding(
      padding: EdgeInsets.only(bottom: 32.h),
      child: SizedBox(
        width: 192.w,
        height: 52.h,
        child: CustomDefaultButton(
          backgroundColor: ThemeConfig.current().colorUtil.button01b,
          buttonTextColor: ThemeConfig.current().colorUtil.button01a,
          disabledBackgroundColor: ThemeConfig.current().colorUtil.button02c,
          borderColor: ThemeConfig.current().colorUtil.button01b,
          text: OneAppString.of().commonDone,
          verticalPadding: 4.h,
          press: () {
            FlexRouter.popRoute();
          },
        ),
      ),
    );
  }
}
