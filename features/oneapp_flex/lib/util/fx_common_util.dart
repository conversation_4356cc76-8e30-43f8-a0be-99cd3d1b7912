// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:collection/collection.dart' show IterableExtension;
import 'package:intl/intl.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_network_implementation/flexcfai/entity/fx_customer_info_entity.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/intent_data_helper.dart';

// Project imports:
import '../flex_global.dart';
import '../ui/landing/fx_detail_landing_bloc.dart';
import '../ui/selectdates/fx_same_day_alert_page.dart';
import '../ui/selectvechicle/fx_select_vehicle_page.dart';

const String TYPE_FUEL = "fuel";
const String TYPE_SEAT = "seat";
const String TYPE_LUGGAGE = "luggage";
const String TYPE_MODEL = "model";

class FxCommonUtils {
  static Future showSameDayAlertPage({String? phoneNumber}) {
    return showMaterialModalBottomSheet(
      expand: false,
      context: NavigateService.context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      builder: (context) {
        return FxSameDayAlertPage(phoneNumber: phoneNumber);
      },
    );
  }

  static Future showSelectVehiclePage({IntentToSelectVehicle? data}) {
    return showMaterialModalBottomSheet(
      expand: false,
      context: NavigateService.context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      builder: (context) {
        return FxSelectVehiclePage(intentData: data);
      },
    );
  }

  static String getDescriptionInfo(String? description, String type) {
    if (description != null) {
      var infos = description.split(";");
      if (infos.isNotEmpty == true) {
        switch (type) {
          case TYPE_FUEL:
            return infos.length >= 3
                ? infos[2]
                : "-/- ${OneAppString.of().eSTMPG}";
          case TYPE_SEAT:
            return infos.length >= 2
                ? infos[1]
                : "- ${OneAppString.of().passengers}";
          case TYPE_LUGGAGE:
            return infos.length >= 4 ? infos[3] : "- ${OneAppString.of().bags}";
          case TYPE_MODEL:
            return infos[0];
        }
      }
    }
    switch (type) {
      case TYPE_FUEL:
        return "-/- ${OneAppString.of().eSTMPG}";
      case TYPE_SEAT:
        return "- ${OneAppString.of().passengers}";
      case TYPE_LUGGAGE:
        return "- ${OneAppString.of().bags}";
      default:
        return "";
    }
  }

  static Insurance createInsurance({Insurance? opt}) {
    final driverInsurance =
        opt == null ? FxAccountGlobal.getInstance().insurance : opt;
    return Insurance(
      insuranceCompanyName: driverInsurance?.insuranceCompanyName,
      insuranceDateOfExpiry: driverInsurance?.insuranceDateOfExpiry,
      insuranceImage: driverInsurance?.insuranceImage,
      insurancePolicyNumber: driverInsurance?.insurancePolicyNumber,
    );
  }

  static License createLicense({License? opt}) {
    final driverLicense =
        opt == null ? FxAccountGlobal.getInstance().license : opt;
    List<String>? licenseImages = driverLicense?.licenseImages;

    return License(
        addressLine1: driverLicense?.addressLine1,
        addressLine2: driverLicense?.addressLine2,
        city: driverLicense?.city,
        state: driverLicense?.state,
        country: driverLicense?.country,
        dateOfBirthInLicense: driverLicense?.dateOfBirthInLicense,
        firstName: driverLicense?.firstName,
        lastName: driverLicense?.lastName,
        licenseDateOfExpiry: driverLicense?.licenseDateOfExpiry,
        licenseImages: licenseImages,
        licenseNumber: driverLicense?.licenseNumber);
  }

  static CustomerStatus? getCustomerStatus(List<Association>? associations) {
    if (associations != null && associations.isNotEmpty) {
      Association? association = associations.firstWhereOrNull((element) =>
          ORG_ID.equals(element.organizationId!, ignoreCase: true));
      if (association == null) {
        association = associations.first;
      }
      // ignore: unnecessary_null_comparison
      if (association == null) {
        return null;
      }
      String? createAt;
      if (association.createdAt != null) {
        final splitList = association.createdAt.toString().split(" ");
        if (splitList.isNotEmpty == true) {
          DateTime dateTime =
              DateFormat("yyyy-MM-dd").parseLoose(splitList.first);
          createAt = DateFormat("MM/dd/yyyy").format(dateTime);
        }
      }
      return CustomerStatus(status: association.status, createdAt: createAt);
    }
    return null;
  }

  static String combinedDatePickUpDropOff(
      DateTime pickUpTime, DateTime dropOffTime,
      {bool showYear = false}) {
    String? _pickUpTimeStr;
    String? _pickUpYearStr;
    String? _pickUpMMMM;
    String? _dropOffTimeStr;
    String? _dropOffYearStr;
    String? _dropOffMMMM;
    String fromToTime;
    const String _mmmm = 'MMM';
    const String _mmmmD = 'MMM d';
    const String _yyyy = 'yyyy';
    _pickUpMMMM = DateFormat(_mmmm).format(pickUpTime);
    _pickUpYearStr = DateFormat(_yyyy).format(pickUpTime);
    _pickUpTimeStr =
        DateFormat(showYear == true ? mmmmDyyyy : _mmmmD).format(pickUpTime);
    _dropOffMMMM = DateFormat(_mmmm).format(dropOffTime);
    // _dropOffD = DateFormat('d').format(dropOffTime);
    _dropOffTimeStr =
        DateFormat(showYear == true ? mmmmDyyyy : _mmmmD).format(dropOffTime);
    _dropOffYearStr = DateFormat(_yyyy).format(dropOffTime);
    if (_pickUpMMMM == _dropOffMMMM) {
      _dropOffTimeStr = _dropOffTimeStr.replaceAll(_dropOffMMMM, '');
    }
    if (_pickUpYearStr == _dropOffYearStr) {
      _pickUpTimeStr =
          _pickUpTimeStr.replaceAll(_dropOffYearStr, '').replaceAll(',', '');
    }
    fromToTime = '${_pickUpTimeStr.trim()}-${_dropOffTimeStr.trim()}';
    return fromToTime;
  }

  static String cleanPhoneFormat(String marker) {
    return marker
        .replaceAll("(", "")
        .replaceAll(")", "")
        .replaceAll(" ", "")
        .replaceAll("-", "")
        .replaceAll("+", "")
        .trim();
  }

  static String phoneDisplayFormat(String phone) {
    String result = phone;
    if (result.length > 7) {
      result =
          "${result.substring(0, 3)}-${result.substring(3, 6)}-${result.substring(6)}";
    }
    return result;
  }

  static String phoneFormat(String marker, {bool isMexico = false}) {
    String result = cleanPhoneFormat(marker);
    if (result.length == 12 && result.substring(0, 2) == "52") {
      final countryCode = result.substring(0, 2);
      final firstCharacter = result.substring(2, 4);
      final lastCharacter = result.substring(4);
      return "$countryCode $firstCharacter-$lastCharacter";
    } else if (isMexico && result.length == 10) {
      final firstCharacter = result.substring(0, 2);
      final lastCharacter = result.substring(2);
      return "$firstCharacter-$lastCharacter";
    } else {
      if (result.length >= 3 && result.length <= 5) {
        result =
            "(${result.substring(0, result.length - 2)}) ${result.substring(result.length - 2, result.length - 1)}-${result.substring(result.length - 1, result.length)}";
        return result;
      }
      if (result.length >= 6 && result.length <= 7) {
        result =
            "(${result.substring(0, 3)}) ${result.substring(3, result.length - 1)}-${result.substring(result.length - 1)}";
        return result;
      }
      if (result.length > 7) {
        result =
            "(${result.substring(0, 3)}) ${result.substring(3, 6)}-${result.substring(6)}";
        return result;
      }
      return result;
    }
  }
}
