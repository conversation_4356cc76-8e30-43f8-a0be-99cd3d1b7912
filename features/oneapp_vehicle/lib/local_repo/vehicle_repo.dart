// Dart imports:
import 'dart:async';
import 'dart:convert';

// Flutter imports:
import 'package:flutter/foundation.dart';

// Package imports:
import 'package:collection/collection.dart';
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/wallet/wallet_config_entity.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';

// Project imports:
import '../log/vehicle_analytic_event.dart';
import '../log/vehicle_marketing_event.dart';
import '../ui/electric_vehicle_management/vehicle_search_station_charging_management/ev_vehicle_info_repository.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/announcement_center_entity.dart'
    as vehicleAlerts;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/document_collision_entity.dart'
    as documentCollision;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/driver_score_detail_entity.dart'
    as driverScoreDetail;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/maintenance_timeline_entity.dart'
    as maintenanceTimeline;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/odometer_detail_entity.dart'
    as odometerDetail;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart'
    as serviceHistory;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_health_detail_entity.dart'
    as vehiclHealthDetail;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_marketing_banner_entity.dart'
    as marketingBanners;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_marketing_card_entity.dart'
    as marketingCards;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_parking_card_entity.dart'
    as vehicleParkingCard;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_subscriptions_entity.dart'
    as vehicleSubscriptions;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_trip_list_entity.dart'
    as vehicleTripList;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/wifi_trial_remainder_card_entity.dart'
    as wifiTrialRemainderEntity;

class VehicleRepo {
  OneAppClient api = APIClientConfig.oneAppClient;
  static const String HEALTH = "HEALTH_";
  static const String ODOMETER = "ODOMETER_";
  static const String VEHICLE_LIST = "VEHICLE_LIST";
  static const String REFRESH_VEHICLE_LIST = "REFRESH_VEHICLE_LIST";
  GetIt locator = GetIt.instance;

  // Fetch Vehicle List Associated to User
  Future<List<vehicleInfo.Payload>> fetchVehicleList(
      {bool? dontUpdateVin}) async {
    List<vehicleInfo.Payload> vehicleListInfoPayload = [];
    FireBaseAnalyticsLogger.logMarketingEvent(
        VehicleMarketingEvent.VIN_LIST_CALL_LAUNCH);
    final commonResponse = await api.fetchVehicleAllList();
    final List<vehicleInfo.Payload>? payLoad = commonResponse.response?.payload;

    if (commonResponse.error?.errorCode != null) {
      FireBaseAnalyticsLogger.logMarketingEvent(
          VehicleMarketingEvent.VIN_LIST_FAILURE);
      Global.getInstance().vinListStatusCode = commonResponse.error?.errorCode;
    } else {
      FireBaseAnalyticsLogger.logMarketingEvent(
          VehicleMarketingEvent.VIN_LIST_SUCCESS);
      Global.getInstance().vinListStatusCode =
          200; // If error code is null setting the status code as 200.
    }

    if (payLoad != null && payLoad.isNotEmpty) {
      vehicleListInfoPayload = payLoad;
      sendVehicleListToNativePage(jsonEncode(vehicleListInfoPayload));

      if (dontUpdateVin == null || (!dontUpdateVin)) {
        bool isVinAvailable = false;
        if (Global.getInstance().vin != null &&
            Global.getInstance().vin!.isNotEmpty &&
            vehicleListInfoPayload.isNotEmpty) {
          String? selectedVin = Global.getInstance().vin;
          vehicleInfo.Payload? vehicleItem =
              vehicleListInfoPayload.firstWhereOrNull((element) =>
                  element.vin!.toLowerCase() == selectedVin!.toLowerCase());
          isVinAvailable = vehicleItem != null ? true : false;
        }
        if (!isVinAvailable) {
          Global.getInstance().setVin(vehicleListInfoPayload.first.vin);
        }
      } else if (dontUpdateVin) {
        //This if condition will be executed when the selected vehicle is removed from another device and pull down to refresh in overview page fetching the vin list.
        bool isVinAvailable = true;
        if (Global.getInstance().vin != null &&
            Global.getInstance().vin!.isNotEmpty &&
            vehicleListInfoPayload.isNotEmpty) {
          String? selectedVin = Global.getInstance().vin;
          vehicleInfo.Payload? vehicleItem =
              vehicleListInfoPayload.firstWhereOrNull((element) =>
                  element.vin!.toLowerCase() == selectedVin!.toLowerCase());
          isVinAvailable = vehicleItem != null ? true : false;
        }
        if (!isVinAvailable) {
          await Global.getInstance().setVin(vehicleListInfoPayload.first.vin);
        }
      }
    }
    return vehicleListInfoPayload;
  }

  // Fetch Vehicle Gauge and Mileage Details
  Future<odometerDetail.OdometerDetailPayload?> fetchTelemetry() async {
    String? vin = Global.getInstance().vin;
    odometerDetail.OdometerDetailPayload? odometerDetailsPayload;
    vehicleInfo.Payload? vehicleInfoPayload = await getLocalPayloadFromVin(vin);
    if (vehicleInfoPayload != null &&
        isFeatureEnabled(TELEMETRY, vehicleInfoPayload.features)) {
      String? generation = vehicleInfoPayload.generation;
      String? region = vehicleInfoPayload.region;
      String vehicleBrand =
          vehicleInfoPayload.brand ?? Global.getInstance().appBrand;
      bool isNonCvtVehicle = vehicleInfoPayload.nonCvtVehicle == true;
      if (generation != null && !isNonCvtVehicle) {
        try {
          final commonResponse = await api.getOdometerDetails(
              vin!, region!, generation, vehicleBrand);
          odometerDetailsPayload = commonResponse.response?.payload;
        } catch (e) {
          FireBaseAnalyticsLogger.logError(e.toString(),
              category: LogCategory.FL_VEHI);
        }
      }
    }
    return odometerDetailsPayload;
  }

  Future<bool> fetchIsEvPublicChargingEnabled() async {
    final vin = Global.getInstance().vin;
    final vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
    final features = vehicleInfoEntity?.features;
    if (features != null) {
      return isEvPublicChargingControlEnabled(vehicleInfoEntity!.features!);
    } else {
      return false;
    }
  }

  // Fetch Vehicle Health Details
  Future<vehiclHealthDetail.Payload> fetchHealthReport(
      vehicleInfo.Payload? vehicleInfoPayload) async {
    vehiclHealthDetail.Payload vehicleHealthDetailsPayload =
        vehiclHealthDetail.Payload();
    String? vin = Global.getInstance().vin;
    bool isNonCvtVehicle = vehicleInfoPayload?.nonCvtVehicle == true;
    if (vehicleInfoPayload != null &&
        vehicleInfoPayload.generation != null &&
        !isNonCvtVehicle &&
        isRemoteOnlyUser(vehicleInfoPayload) == false &&
        isFeatureEnabled(VEHICLE_HEALTH_REPORT, vehicleInfoPayload.features)) {
      try {
        final commonResponse = await api.fetchVehicleHealthReportDetails(
            Global.getInstance().guid!,
            vin!,
            vehicleInfoPayload.brand ?? Global.getInstance().appBrand,
            vehicleInfoPayload.generation ?? "",
            vehicleInfoPayload.region!);
        vehiclHealthDetail.Payload? payLoad = commonResponse.response?.payload;
        if (payLoad != null) {
          vehicleHealthDetailsPayload = payLoad;
          //setupVehicleHealthLocator(payLoad);
          FireBaseAnalyticsLogger.logMarketingEvent(
            VehicleMarketingEvent.VHR_HISTORY_GET_HEALTH_REPORTS_SUCCESSFUL,
          );
          FireBaseAnalyticsLogger.logMarketingEvent(
            VehicleMarketingEvent.VHR_HISTORY_GET_DIAGNOSTIC_REPORTS_SUCCESSFUL,
          );
          FireBaseAnalyticsLogger.logSuccessAPI(
              VehicleAnalyticsEvent.VEHICLE_HEALTH_PAGE_SUCCESS,
              category: LogCategory.FL_VEHI);
        }
        if (commonResponse.error != null) {
          FireBaseAnalyticsLogger.logMarketingEvent(
            VehicleMarketingEvent.VHR_HISTORY_GET_HEALTH_REPORTS_UNSUCCESSFUL,
          );
          FireBaseAnalyticsLogger.logMarketingEvent(
            VehicleMarketingEvent
                .VHR_HISTORY_GET_DIAGNOSTIC_REPORTS_UNSUCCESSFUL,
          );
          FireBaseAnalyticsLogger.logErrorAPI(
              VehicleAnalyticsEvent.VEHICLE_HEALTH_PAGE_FAILURE,
              category: LogCategory.FL_VEHI);
        }
      } catch (e) {
        FireBaseAnalyticsLogger.logError(e.toString(),
            category: LogCategory.FL_VEHI);
      }
    }
    return vehicleHealthDetailsPayload;
  }

  // Fetch Vehicle Alerts Details or Announcement Center details.
  Future<vehicleAlerts.AnnouncementCenterPayload> fetchVehicleAlerts(
      vehicleInfo.Payload? vehicleInfoPayload) async {
    vehicleAlerts.AnnouncementCenterPayload vehicleAnnouncementCenterPayload =
        vehicleAlerts.AnnouncementCenterPayload();
    if (vehicleInfoPayload != null) {
      String? vin = vehicleInfoPayload.vin;
      if (isFeatureEnabled(CRITICAL_ALERT, vehicleInfoPayload.features)) {
        try {
          final commonResponse = await api.fetchVehicleCriticalAlerts(
              Global.getInstance().correlationId,
              vin!,
              vehicleInfoPayload.region!,
              vehicleInfoPayload.brand!,
              vehicleInfoPayload.subscriptionExpirationStatus.toString());
          vehicleAlerts.AnnouncementCenterPayload? payLoad =
              commonResponse.response?.payload;
          if (payLoad != null) {
            vehicleAnnouncementCenterPayload = payLoad;
            FireBaseAnalyticsLogger.logSuccessAPI(
                VehicleAnalyticsEvent.VEHICLE_ALERTS_SUCCESS,
                category: LogCategory.FL_VEHI);
          }
          if (commonResponse.error != null) {
            FireBaseAnalyticsLogger.logErrorAPI(
                VehicleAnalyticsEvent.VEHICLE_ALERTS_FAILURE,
                category: LogCategory.FL_VEHI);
          }
        } catch (e) {
          FireBaseAnalyticsLogger.logError(
              VehicleAnalyticsEvent.VEHICLE_ALERTS_FAILURE,
              category: LogCategory.FL_VEHI);
        }
      }
    }
    return vehicleAnnouncementCenterPayload;
  }

  // Fetch trips score
  Future<driverScoreDetail.DriverScoreDetailPayload> fetchScoreDetail(
      vehicleInfo.Payload vehicleInfoPayload) async {
    driverScoreDetail.DriverScoreDetailPayload driverScoreDetailPayload =
        driverScoreDetail.DriverScoreDetailPayload();
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.fetchDriverScoreDetail(
        vin,
        vehicleInfoPayload.generation!,
        vehicleInfoPayload.brand ?? Global.getInstance().appBrand);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      driverScoreDetailPayload = payLoad;
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_DRIVER_SCORE_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_DRIVER_SCORE_FAILURE,
          category: LogCategory.FL_VEHI);
    }
    return driverScoreDetailPayload;
  }

  // Fetch trips list
  Future<List<vehicleTripList.Payload>> fetchTripList(
      vehicleInfo.Payload vehicleInfoPayload) async {
    List<vehicleTripList.Payload> vehicleTripListPayload = [];
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.fetchTripList(
        vin,
        vehicleInfoPayload.generation!,
        vehicleInfoPayload.brand ?? Global.getInstance().appBrand);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      vehicleTripListPayload = payLoad;
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_DRIVER_LIST_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_DRIVER_LIST_FAILURE,
          category: LogCategory.FL_VEHI);
    }
    return vehicleTripListPayload;
  }

  Future<marketingCards.Payload?> fetchVehicleMarketingCard(
      vehicleInfo.Payload vehicleInfoPayload) async {
    marketingCards.Payload? vehicleMarktingCard;
    String vin = Global.getInstance().vin!;
    String locale = Global.getInstance().localeString;
    String region = vehicleInfoPayload.region!;
    String generation = vehicleInfoPayload.generation!;
    String correlationId = Global.getInstance().correlationId;
    final commonResponse = await api.fetchVehicleMarketingCard(
        locale, vin, region, generation, correlationId);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      vehicleMarktingCard = payLoad;
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_MARKETING_CARD_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_MARKETING_CARD_FAILURE,
          category: LogCategory.FL_VEHI);
    }

    return vehicleMarktingCard;
  }

  Future<marketingBanners.Payload> fetchVehicleMarketingBanner(
      String vin, String stateCode, String region, String generation) async {
    marketingBanners.Payload vehicleMarktingBanner = marketingBanners.Payload();
    final commonResponse =
        await api.fetchMarketingBanner(vin, stateCode, region, generation);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      vehicleMarktingBanner = payLoad;
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_MARKETING_CARD_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_MARKETING_CARD_FAILURE,
          category: LogCategory.FL_VEHI);
    }

    return vehicleMarktingBanner;
  }

  Future<vehicleParkingCard.Payload> fetchParkWithParkopediaCard() async {
    vehicleParkingCard.Payload parkWithParkopediaPayload =
        vehicleParkingCard.Payload();
    String? vin = Global.getInstance().vin;
    vehicleInfo.Payload? vehicleInfoPayload = await getLocalPayloadFromVin(vin);
    if (vehicleInfoPayload != null) {
      final commonResponse = await api.fetchVehicleParkingCard(
          ((vehicleInfoPayload.brand == "L")
              ? OneAppString.of().lexusName
              : OneAppString.of().toyotaName));
      final payLoad = commonResponse.response?.payload;
      if (payLoad != null) {
        parkWithParkopediaPayload = payLoad;
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.VEHICLE_PARKING_CARD_SUCCESS,
            category: LogCategory.FL_VEHI);
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleMarketingEvent.PARKING,
          childEventName:
              VehicleMarketingEvent.PARKING_DASHBOARD_CARD_SUCCESSFUL,
        );
      }
      if (commonResponse.error != null) {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_PARKING_CARD_FAILURE,
            category: LogCategory.FL_VEHI);
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleMarketingEvent.PARKING,
          childEventName:
              VehicleMarketingEvent.PARKING_DASHBOARD_CARD_UNSUCCESSFUL,
        );
      }
    }
    return parkWithParkopediaPayload;
  }

  // Fetch Vehicle Collision Document Details
  Future<documentCollision.DocumentCollisionPayload?> fetchCollisionDocument(
      vehicleInfo.Payload? vehicleInfoPayload) async {
    documentCollision.DocumentCollisionPayload? documentCollisionPayload;
    String? vin = Global.getInstance().vin;
    try {
      if (vehicleInfoPayload != null &&
          isFeatureEnabled(COLLISION_ASSISTANCE, vehicleInfoPayload.features)) {
        final commonResponse = await api.getCollisionData(
            vin!, vehicleInfoPayload.brand ?? Global.getInstance().appBrand);
        documentCollision.DocumentCollisionPayload? payLoad =
            commonResponse.response?.payload;
        if (payLoad != null) {
          documentCollisionPayload = payLoad;
          FireBaseAnalyticsLogger.logSuccessAPI(
              VehicleAnalyticsEvent.COLLISION_ASSISTANCE_SUCCESS,
              category: LogCategory.FL_VEHI);
        }
        if (commonResponse.error != null) {
          FireBaseAnalyticsLogger.logErrorAPI(
              VehicleAnalyticsEvent.COLLISION_ASSISTANCE_FAILURE,
              category: LogCategory.FL_VEHI);
        }
      }
    } catch (e) {
      FireBaseAnalyticsLogger.logError(
          VehicleAnalyticsEvent.COLLISION_ASSISTANCE_FAILURE,
          category: LogCategory.FL_VEHI);
    }

    return documentCollisionPayload;
  }

  Future<void> storeVehicleHealth(
      vehicleInfo.Payload? vehicleInfoPayload) async {
    locator.registerSingletonAsync<vehiclHealthDetail.Payload>(
        () async => VehicleRepo().fetchHealthReport(vehicleInfoPayload));
  }

  Future<void> storeDriverScoreList(
      vehicleInfo.Payload? vehicleInfoPayload) async {
    locator.registerSingletonAsync(
        () async => VehicleRepo().fetchScoreDetail(vehicleInfoPayload!));
    try {
      locator
          .isReady<driverScoreDetail.DriverScoreDetailPayload>()
          .then((value) async {
        driverScoreDetail.DriverScoreDetailPayload? payLoad;
        try {
          payLoad = locator<driverScoreDetail.DriverScoreDetailPayload>();
        } catch (e) {}
        if (payLoad!.vin == null) {
          FBroadcast.instance().broadcast(BROADCAST_DRIVER_SCORE_API_OUTCOME,
              value: BroadcastApiStatus.FAILURE);
        } else {
          FBroadcast.instance().broadcast(BROADCAST_DRIVER_SCORE_API_OUTCOME,
              value: BroadcastApiStatus.SUCCESS);
        }
      });
    } catch (e) {
      debugPrint(e.toString());
      FBroadcast.instance().broadcast(BROADCAST_DRIVER_SCORE_API_OUTCOME,
          value: BroadcastApiStatus.FAILURE);
    }
  }

  Future<void> storeVehicleTripsList(
      vehicleInfo.Payload? vehicleInfoPayload) async {
    locator.registerSingletonAsync(
        () async => VehicleRepo().fetchTripList(vehicleInfoPayload!));
    locator.isReady<List<vehicleTripList.Payload>>().then((value) async {
      List<vehicleTripList.Payload>? payLoad;
      try {
        payLoad = locator<List<vehicleTripList.Payload>>();
      } catch (e) {}
      try {
        if (payLoad != null && payLoad.isNotEmpty) {
          FBroadcast.instance().broadcast(BROADCAST_TRIPS_LIST_API_OUTCOME,
              value: BroadcastApiStatus.SUCCESS);
        } else {
          FBroadcast.instance().broadcast(BROADCAST_TRIPS_LIST_API_OUTCOME,
              value: BroadcastApiStatus.FAILURE);
        }
      } catch (e) {
        debugPrint(e.toString());
        FBroadcast.instance().broadcast(BROADCAST_TRIPS_LIST_API_OUTCOME,
            value: BroadcastApiStatus.FAILURE);
      }
    });
  }

  Future<void> storeMarketingCards(
      vehicleInfo.Payload vehicleInfoPayload) async {
    final marketingCards.Payload? payLoad =
        await fetchVehicleMarketingCard(vehicleInfoPayload);
    if (payLoad != null) {
      locator.registerSingleton(payLoad);
    }
  }

  Future<void> storeParkWithParkopediaCard() async {
    vehicleParkingCard.Payload payload =
        await VehicleRepo().fetchParkWithParkopediaCard();
    locator.registerSingleton(payload, signalsReady: true);
  }

  //Fetch Maintenance Timeline list.
  Future<maintenanceTimeline.MaintenanceTimelinePayload?>
      fetchMaintenanceTimeline(
          String currentMileage, String currentMileageUnit) async {
    maintenanceTimeline.MaintenanceTimelinePayload? maintenancePayload;
    final commonResponse = await api.fetchMaintenanceTimeline(
        Global.getInstance().vin!,
        odometerWithoutComma(currentMileage),
        currentMileageUnit);
    final payLoad = commonResponse.response?.payload;
    try {
      if (payLoad != null) {
        maintenancePayload = payLoad;
        maintenancePayload.lastKnownMileage = double.parse(currentMileage);
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.VEHICLE_MAINTENANCE_TIMELINE_SUCCESS,
            category: LogCategory.FL_VEHI);
      } else {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_MAINTENANCE_TIMELINE_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_VEHI);
    }

    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_MAINTENANCE_TIMELINE_FAILURE,
          category: LogCategory.FL_VEHI);
    }
    return maintenancePayload;
  }

  String odometerWithoutComma(String odometer) {
    String odometerValue = odometer;
    if (odometerValue.contains(",")) {
      odometerValue = odometerValue.replaceAll(",", "");
    }
    return odometerValue;
  }

  //Fetch Service history list.
  Future<serviceHistory.Payload> fetchServiceHistory() async {
    String vin = Global.getInstance().vin!;
    vehicleInfo.Payload? vehicleInfoPayload = await getLocalPayloadFromVin(vin);
    serviceHistory.Payload serviceHistoryPayload = serviceHistory.Payload();
    try {
      CommonResponse<serviceHistory.ServiceHistoryEntity> commonResponse =
          await api.fetchServiceHistory(
              vin, vehicleInfoPayload?.brand ?? Global.getInstance().appBrand);
      final payLoad = commonResponse.response?.payload;
      if (payLoad != null) {
        serviceHistoryPayload = payLoad;
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.SERVICE_HISTORY_SUCCESS,
            category: LogCategory.FL_VEHI);
      } else {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.SERVICE_HISTORY_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    } catch (e) {
      debugPrint(e.toString());
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_VEHI);
    }
    return serviceHistoryPayload;
  }

  Future<vehicleInfo.Payload?> cachedGlobalVehicleInfo() async {
    String? vin = Global.getInstance().vin;
    return getLocalPayloadFromVin(vin);
  }

  Future<vehicleInfo.Payload?> getLocalPayloadFromVin(String? vin) async {
    List<vehicleInfo.Payload> vehicleInfoListEntity = [];
    vehicleInfo.Payload? vehicleInfoEntity;
    try {
      vehicleInfoListEntity = locator<List<vehicleInfo.Payload>>();
    } catch (e) {}
    if (vehicleInfoListEntity.isNotEmpty) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_VEHICLE_INFO_FROM_LOCALDB_SUCCESS,
          category: LogCategory.FL_VEHI);
      List<vehicleInfo.Payload> vehicleList =
          vehicleInfoListEntity.where((element) => element.vin == vin).toList();
      if (vehicleList.isNotEmpty) {
        vehicleInfoEntity = vehicleList.first;
      }
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_VEHICLE_INFO_FROM_LOCALDB_FAILURE,
          category: LogCategory.FL_VEHI);
    }
    return vehicleInfoEntity;
  }

  Future<EVVehicleInfoRepository> getEVVehicleInfoRepository(
      String? vin) async {
    EVVehicleInfoRepository? repository;
    try {
      repository = locator<EVVehicleInfoRepository>();
    } catch (e) {}
    if (repository != null && repository.vin != vin) {
      locator.unregister(instance: repository);
      repository = null;
    }
    if (repository == null) {
      final entity = await getLocalPayloadFromVin(vin);
      repository = EVVehicleInfoRepository(
        vin: vin,
        generation: entity?.generation,
        brand: entity?.brand ?? Global.getInstance().appBrand,
      );
      await repository.init();
      locator.registerSingleton(repository);
    }
    return repository;
  }

  Future<void> clearEVVehicleInfoRepository() async {
    try {
      final EVVehicleInfoRepository? repository =
          locator<EVVehicleInfoRepository>();
      await locator.unregister(instance: repository);
    } catch (e) {}
  }

  // Fetch Vehicle Gauge and Mileage Details
  Future<wifiTrialRemainderEntity.Payload?> fetchWifiTrialRemainderCard(
      String generation,
      String attToken,
      String vehicleBrand,
      String correlationId) async {
    String vin = Global.getInstance().vin!;
    wifiTrialRemainderEntity.Payload? wifiTrialRemainderPayload;
    try {
      final commonResponse = await api.fetchWifiTrialRemainderCard(
          vin, generation, correlationId, attToken, vehicleBrand);
      wifiTrialRemainderPayload = commonResponse.response?.payload;
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_VEHI);
    }
    return wifiTrialRemainderPayload;
  }

  void setupVehicleHealthLocator(
      vehiclHealthDetail.Payload vehicleHealthDetailsPayload) {
    locator.registerSingleton<vehiclHealthDetail.Payload>(
        vehicleHealthDetailsPayload);
  }

  Future<void> storeVehicleCollisionDocument(
      vehicleInfo.Payload? vehicleInfoPayload) async {
    documentCollision.DocumentCollisionPayload? payLoad =
        await fetchCollisionDocument(vehicleInfoPayload);
    if (payLoad != null) {
      locator.registerSingleton<documentCollision.DocumentCollisionPayload>(
          payLoad,
          signalsReady: true);
    }
  }

  Future<void> storeAnnouncementCenter(
      vehicleInfo.Payload? vehicleInfoPayload) async {
    locator.registerSingletonAsync<vehicleAlerts.AnnouncementCenterPayload>(
        () async => VehicleRepo().fetchVehicleAlerts(vehicleInfoPayload));
  }

  Future<void> storeMaintenanceTimeline(
      String currentMileage, String currentMileageUnit) async {
    final result = await VehicleRepo()
        .fetchMaintenanceTimeline(currentMileage, currentMileageUnit);
    if (result != null) {
      locator.registerSingletonAsync<
          maintenanceTimeline.MaintenanceTimelinePayload>(() async => result);
    }
  }

  Future<void> storeServiceHistory() async {
    locator.registerSingletonAsync<serviceHistory.Payload>(
        () async => VehicleRepo().fetchServiceHistory());
  }

  Future<void> storeWifiTrialExpiringData(String? generation, String? attToken,
      String vehicleBrand, String correlationId) async {
    if (generation != null &&
        generation.isNotEmpty &&
        attToken != null &&
        attToken.isNotEmpty) {
      final result = await VehicleRepo().fetchWifiTrialRemainderCard(
        generation,
        attToken,
        vehicleBrand,
        correlationId,
      );
      if (result != null) {
        locator.registerSingletonAsync<wifiTrialRemainderEntity.Payload>(
            () async => result);
      }
    }
  }

  Future<vehicleSubscriptions.Payload> fetchVehicleSubscriptions(
      vehicleInfo.Payload vehicleInfoPayload) async {
    vehicleSubscriptions.Payload vehicleSub = vehicleSubscriptions.Payload();
    String vin = Global.getInstance().vin!;
    String correlationId = Global.getInstance().correlationId;
    String contentType = "application/json";
    final commonResponse = await api.fetchVehicleSubscriptions(
        vin,
        vehicleInfoPayload.region!,
        vehicleInfoPayload.generation!,
        vehicleInfoPayload.asiCode!,
        vehicleInfoPayload.hwType!,
        contentType,
        correlationId,
        vehicleInfoPayload.brand!);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      vehicleSub = payLoad;
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_SUBSCRIPTIONS_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_SUBSCRIPTIONS_FAILURE,
          category: LogCategory.FL_VEHI);
    }

    return vehicleSub;
  }

  Future<void> storeVehicleSubscriptions(
      vehicleInfo.Payload? vehicleInfoPayload) async {
    locator.registerSingletonAsync(() async =>
        VehicleRepo().fetchVehicleSubscriptions(vehicleInfoPayload!));
  }

  // Fetch Wallet Images
  Future<WalletImages> fetchWalletImages() async {
    WalletImages vehicleTripListPayload;
    final commonResponse = await api.walletConfig();
    final payLoad = commonResponse.response?.payload?.images;
    if (payLoad != null) {
      vehicleTripListPayload = payLoad;
    } else {
      vehicleTripListPayload = WalletImages();
    }
    return vehicleTripListPayload;
  }

  Future<String> checkPreferredDealerSmartPath(String dealerCode) async {
    String dealerSPMImage = '';
    vehicleInfo.Payload? vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    if (vehicleItem?.region != null &&
        vehicleItem?.brand != null &&
        dealerCode.isNotEmpty) {
      final commonResponse = await api.getDealerShipsDetailById(
        dealerCode,
        vehicleItem?.region,
        headerBrand: vehicleItem?.brand,
      );
      bool? smartPath =
          commonResponse.response?.payload?.dealership?.smartPath ?? false;
      String? spmLogo =
          commonResponse.response?.payload?.dealership?.spmLogo ?? '';
      if (smartPath && spmLogo.isNotEmpty == true) {
        bool isDarkTheme = Global.getInstance().isDarkTheme;
        String spmImage =
            isDarkTheme ? spmLogo.split(',').last : spmLogo.split(',').first;
        dealerSPMImage = spmImage;
      }
    }

    return dealerSPMImage;
  }
}
