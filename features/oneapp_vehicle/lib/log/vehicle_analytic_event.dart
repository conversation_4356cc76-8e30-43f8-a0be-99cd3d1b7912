class VehicleAnalyticsEvent {
  static const VEHICLE_PREFERRED_SERVICE_PAGE =
      "vehicle_preferred_service_dealer";
  static const VEHICLE_PREFERRED_SERVICE_MAP_PAGE =
      "vehicle_preferred_service_dealer_map";
  static const VEHICLE_FETCH_SERVICEDEALER_SUCCESS =
      "vehicle_fetch_servicedealer_success";
  static const VEHICLE_FETCH_SERVICEDEALER_FAILURE =
      "vehicle_fetch_servicedealer_failure";
  static const VEHICLE_FETCH_SERVICEDEALER_LATLONG_SUCCESS =
      "vehicle_fetch_servicedealer_by_latlong_success";
  static const VEHICLE_FETCH_SERVICEDEALER_LATLONG_FAILURE =
      "vehicle_fetch_servicedealer_by_latlong_failure";
  static const VEHICLE_FETCH_SERVICEDEALER_ZIP_SUCCESS =
      "vehicle_fetch_servicedealer_by_zip_success";
  static const VEHICLE_FETCH_SERVICEDEALER_ZIP_FAILURE =
      "vehicle_fetch_servicedealer_by_zip_failure";
  static const VEHICLE_FETCH_SERVICEDEALER_CITY_SUCCESS =
      "vehicle_fetch_servicedealer_by_city_success";
  static const VEHICLE_FETCH_SERVICEDEALER_CITY_FAILURE =
      "vehicle_fetch_servicedealer_by_city_failure";
  static const VEHICLE_UPDATE_PREFERRED_SERVICEDEALER_SUCCESS =
      "vehicle_update_preferred_servicedealer_success";
  static const VEHICLE_UPDATE_PREFERRED_SERVICEDEALER_FAILURE =
      "vehicle_update_preferred_servicedealer_failure";

  static const VEHICLE_PAGE = "vehicle";
  static const VEHICLE_FETCH_VEHICLE_INFO_FROM_LOCALDB_SUCCESS =
      "vehicle_fetch_info_from_localdb_success";
  static const VEHICLE_FETCH_VEHICLE_INFO_FROM_LOCALDB_FAILURE =
      "vehicle_fetch_info_from_localdb_failure";

  static const VEHICLE_CAPABILITY_PAGE = "vehicle_capability";
  static const VEHICLE_FETCH_DETAIL_SUCCESS = "vehicle_fetch_detail_success";
  static const VEHICLE_FETCH_DETAIL_FAILURE = "vehicle_fetch_detail_failure";

  static const VEHICLE_DASHBOARD_LIGHTS_PAGE = "vehicle_dashboard_lights";
  static const VEHICLE_FETCH_DASHBOARD_LIGHTS_SUCCESS =
      "vehicle_fetch_dashboard_lights_success";
  static const VEHICLE_FETCH_DASHBOARD_LIGHTS_FAILURE =
      "vehicle_fetch_dashboard_lights_failure";

  static const VEHICLE_MANUAL_WARRANTY_PAGE = "vehicle_manual_warranty";
  static const VEHICLE_FETCH_MANUAL_WARRANTY_SUCCESS =
      "vehicle_fetch_manual_warranty_success";
  static const VEHICLE_FETCH_MANUAL_WARRANTY_FAILURE =
      "vehicle_fetch_manual_warranty_description_failure";
  static const VEHICLE_FETCH_MANUAL_WARRANTY_DESCRIPTION_SUCCESS =
      "vehicle_fetch_manual_warranty_success";
  static const VEHICLE_FETCH_MANUAL_WARRANTY_DESCRIPTION_FAILURE =
      "vehicle_fetch_manual_warranty_description_failure";

  static const VEHICLE_NICKNAME_PAGE = "vehicle_nickname";
  static const VEHICLE_UPDATE_NICKNAME_SUCCESS =
      "vehicle_update_nickname_success";
  static const VEHICLE_UPDATE_NICKNAME_FAILURE =
      "vehicle_update_nickname_failure";

  static const VEHICLE_ADVANCED_REMOTE_PAGE = "vehicle_advance_remote";
  static const VEHICLE_GLOVE_BOX_PAGE = "vehicle_glove_box";

  static const VEHICLE_SOFTWARE_PAGE = "vehicle_software";
  static const VEHICLE_FETCH_SOFTWARE_UPDATE_SUCCESS =
      "vehicle_fetch_software_update_success";
  static const VEHICLE_FETCH_SOFTWARE_UPDATE_FAILURE =
      "vehicle_fetch_software_update_failure";

  static const VEHICLE_SOFTWARE_UPDATE_DETAIL_PAGE = "vehicle_software_update";
  static const VEHICLE_FETCH_SOFTWARE_UPDATE_DETAIL_SUCCESS =
      "vehicle_fetch_software_update_detail_success";
  static const VEHICLE_FETCH_SOFTWARE_UPDATE_DETAIL_FAILURE =
      "vehicle_fetch_software_update_detail_failure";

  static const VEHICLE_SPEC_PAGE = "vehicle_spec";
  static const VEHICLE_FETCH_SPEC_SUCCESS = "vehicle_fetch_spec_success";
  static const VEHICLE_FETCH_SPEC_FAILURE = "vehicle_fetch_spec_failure";

  static const VEHICLE_STATUS_PAGE = "vehicle_status";
  static const VEHICLE_STATUS_FETCH_SUCCESS = "vehicle_status_fetch_success";
  static const VEHICLE_STATUS_FETCH_FAILURE = "vehicle_status_fetch_failure";
  static const VEHICLE_FETCH_ODOMETER_FROM_LOCALDB_SUCCESS =
      "vehicle_fetch_odomter_from_localdb_success";
  static const VEHICLE_FETCH_ODOMETER_FROM_LOCALDB_FAILURE =
      "vehicle_fetch_odomter_from_localdb_failure";

  static const VEHICLE_TRIP_PAGE = "vehicle_trip";
  static const VEHICLE_FETCH_DRIVER_SCORE_SUCCESS =
      "vehicle_fetch_driver_score_success";
  static const VEHICLE_FETCH_DRIVER_SCORE_FAILURE =
      "vehicle_fetch_driver_score_failure";
  static const VEHICLE_FETCH_DRIVER_LIST_SUCCESS =
      "vehicle_fetch_driver_list_success";
  static const VEHICLE_FETCH_DRIVER_LIST_FAILURE =
      "vehicle_fetch_driver_list_failure";

  static const VEHICLE_TRIP_DRIVER_PAGE = "vehicle_trip_driver_score";
  static const VEHICLE_DRIVER_SCORE_OPTOUT_SUCCESS =
      "vehicle_driver_score_optout_success";
  static const VEHICLE_DRIVER_SCORE_OPTIN_SUCCESS =
      "vehicle_driver_score_optin_success";
  static const VEHICLE_DRIVER_SCORE_OPTOUT_FAILED =
      "vehicle_driver_score_optout_failed";
  static const VEHICLE_DRIVER_SCORE_OPTIN_FAILED =
      "vehicle_driver_score_optin_failed";
  static const VEHICLE_TRIP_DETAIL_PAGE = "vehicle_trip_detail";
  static const VEHICLE_TRIP_DETAIL_SUCCESS =
      "vehicle_fetch_trip_detail_success";
  static const VEHICLE_TRIP_DETAIL_FAILURE =
      "vehicle_fetch_trip_detail_failure";
  static const VEHICLE_REMOVE_TRIP_SUCCESS = "vehicle_trip_remove_success";
  static const VEHICLE_REMOVE_TRIP_FAILURE = "vehicle_trip_remove_failed";

  static const VEHICLE_FINDER_PAGE = "vehicle_finder";
  static const VEHICLE_FINDER_FAILED = "vehicle_finder_failed";
  static const VEHICLE_FINDER_SUCCESS = "vehicle_finder_success";
  static const VEHICLE_HEALTH_PAGE = "vehicle_health";
  static const VEHICLE_HEALTH_PAGE_SUCCESS = "vehicle_health_success";
  static const VEHICLE_HEALTH_PAGE_FAILURE = "vehicle_health_failure";
  static const VEHICLE_HEALTH_LIST_PAGE = "vehicle_health_list";
  static const VEHICLE_HEALTH_DETAIL_PAGE = "vehicle_health_detail"; //
  static const VEHICLE_MAINTENANCE_TIMELINE_PAGE =
      "vehicle_maintenance_timeline_page"; //
  static const VEHICLE_MAINTENANCE_TIMELINE_SUCCESS =
      "vehicle_maintenance_timeline_success"; //
  static const VEHICLE_MAINTENANCE_TIMELINE_FAILURE =
      "vehicle_maintenance_timeline_failure"; //
  static const VEHICLE_REMOTE_LOCK_SUCCESS = "vehicle_remote_lock_success";
  static const VEHICLE_REMOTE_LOCK_FAILURE = "vehicle_remote_lock_failure";
  static const VEHICLE_MARKETING_CONSENT_PAGE = "marketing_consent_page";
  static const VEHICLE_WIFI_EXPIRY_REMAINDER_PAGE =
      "vehicle_wifi_expiry_remainder_page";
  static const VEHICLE_MARKETING_CARD_SUCCESS =
      "vehicle_marketing_card_success";
  static const VEHICLE_MARKETING_CARD_FAILURE =
      "vehicle_marketing_card_failure";
  static const VEHICLE_PARKING_CARD_SUCCESS = "vehicle_parking_card_success";
  static const VEHICLE_PARKING_CARD_FAILURE = "vehicle_parking_card_failure";

  static const VEHICLE_TIMELINE_DETAIL_PAGE = "vehicle_timeline_detail_page";
  static const VEHICLE_HEALTH_REPORT_PAGE = "vehicle_health_report_page";
  static const GUEST_DRIVER_SETTINGS_PAGE = "guest_driver_settings_page";
  static const GUEST_DRIVER_SETTINGS_PAGE_SUCCESS =
      "guest_driver_settings_success";
  static const GUEST_DRIVER_SETTINGS_PAGE_FAILURE =
      "guest_driver_settings_failure";
  static const DRIVER_SETTINGS_DETAIL_PAGE = "driver_settings_detail_page";
  static const DRIVER_SETTINGS_DETAIL_PAGE_SUCCESS =
      "driver_settings_detail_success";
  static const DRIVER_SETTINGS_DETAIL_PAGE_FAILURE =
      "driver_settings_detail_failure";
  static const DRIVER_SPEED_LIMIT_PAGE = "driver_speed_limit_page";
  static const DRIVER_TOTAL_MILES_PAGE = "driver_total_miles_page";
  static const DRIVER_TIME_LIMIT_PAGE = "driver_time_limit_page";
  static const DRIVER_SETTINGS_CURFEW_PAGE = "driver_settings_curfew_page";
  static const DRIVER_AREA_LIMIT_PAGE = "driver_area_limit_page";
  static const AREA_LIMIT_SEARCH_SUCCESS = "area_limit_search_success";
  static const AREA_LIMIT_SEARCH_FAILURE = "area_limit_search_failure";
  static const DRIVER_SPEED_SAVE_SUCCESS = "driver_speed_save_success";
  static const DRIVER_SPEED_SAVE_FAILURE = "driver_speed_save_failure";
  static const DRIVER_CURFEW_SAVE_SUCCESS = "driver_curfew_save_success";
  static const DRIVER_CURFEW_SAVE_FAILURE = "driver_curfew_save_failure";
  static const VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS =
      "vehicle_charge_management_success";
  static const VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE =
      "vehicle_charge_management_failure";
  static const VEHICLE_CHARGE_MANAGEMENT_PAGE =
      "vehicle_charge_management_page";
  static const VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE =
      "vehicle_post_electric_realtime_status_failure";
  static const VEHICLE_POST_ELECTRIC_REALTIME_STATUS_SUCCESS =
      "vehicle_post_electric_realtime_status_success";

  static const VEHICLE_CHARGE_STATION_PAGE = "vehicle_charge_station_page";
  static const VEHICLE_FETCH_CHARGE_STATION_LIST_SUCCESS =
      "vehicle_charge_station_list_success";
  static const VEHICLE_FETCH_CHARGE_STATION_LIST_FAILURE =
      "vehicle_charge_station_list_failure";

  static const VEHICLE_CHARGE_TIMER_SETTING_PAGE =
      "vehicle_charge_timer_setting_page";
  static const VEHICLE_CHARGE_TIMER_SETTING_SUCCESS =
      "vehicle_charge_timer_setting_success";
  static const VEHICLE_CHARGE_TIMER_SETTING_FAILURE =
      "vehicle_charge_timer_setting_failure";

  static const VEHICLE_FETCH_ECO_HISTORY_SUCCESS =
      "vehicle_fetch_eco_history_success";
  static const VEHICLE_FETCH_ECO_HISTORY_FAILURE =
      "vehicle_fetch_eco_history_failure";
  static const SERVICE_HISTORY_PAGE = "service_history_page";
  static const SERVICE_HISTORY_SUCCESS = "service_history_success";
  static const SERVICE_HISTORY_FAILURE = "service_history_failure";
  static const SERVICE_APPOINTMENT_PAGE = "service_appointment_page";
  static const ADD_SERVICE_SUCCESS = "add_service_success";
  static const ADD_SERVICE_FAILURE = "add_service_failure";
  static const UPDATE_SERVICE_SUCCESS = "update_service_success";
  static const UPDATE_SERVICE_FAILURE = "update_service_failure";
  static const DELETE_SERVICE_SUCCESS = "delete_service_success";
  static const DELETE_SERVICE_FAILURE = "delete_service_failure";
  static const COLLISION_DOCUMENT_START_PAGE = "collision_document_start_page";
  static const COLLISION_ASSISTANCE_SUCCESS = "collision_assistance_success";
  static const COLLISION_ASSISTANCE_FAILURE = "collision_assistance_failure";
  static const COLLISION_DOCUMENT_READY_PAGE = "collision_document_ready_page";
  static const COLLISION_DOCUMENT_PROGRESS_PAGE =
      "collision_document_progress_page";
  static const VEHICLE_REMOTE_SHARING_PAGE = "vehicle_remote_sharing_page";
  static const VEHICLE_REMOTE_SHARING_SUCCESS = "collision_assistance_success";
  static const VEHICLE_REMOTE_SHARING_FAILURE = "collision_assistance_failure";
  static const VEHICLE_REMOTE_INVITE_PAGE = "vehicle_remote_invite_page";
  static const VEHICLE_EMAIL_SEARCH_SUCCESS = "vehicle_email_search_success";
  static const VEHICLE_EMAIL_SEARCH_FAILURE = "vehicle_email_search_failure";
  static const VEHICLE_REMOTE_INVITE_SUCCESS = "vehicle_remote_invite_success";
  static const VEHICLE_REMOTE_INVITE_FAILURE = "vehicle_remote_invite_failure";
  static const VEHICLE_SIRIUS_XM_DETAIL_PAGE = "vehicle_sirius_xm_detail_page";
  static const VEHICLE_ALERTS_SUCCESS = "vehicle_alerts_success";
  static const VEHICLE_ALERTS_FAILURE = "vehicle_alerts_failure";
  static const VEHICLE_FETCH_VEHICLE_ALERTS_FROM_LOCALDB_SUCCESS =
      "vehicle_fetch_alerts_from_localdb_success";
  static const VEHICLE_FETCH_VEHICLE_ALERTS_FROM_LOCALDB_FAILURE =
      "vehicle_fetch_alerts_from_localdb_failure";
  static const VEHICLE_ALERT_OTA_UPDATE_SUCCESS = "vehicle_alert_ota_success";
  static const VEHICLE_ALERT_OTA_UPDATE_FAILURE = "vehicle_alert_ota_failure";
  static const VEHICLE_ALERT_DISMISS_UPDATE_SUCCESS =
      "vehicle_alert_dismiss_success";
  static const VEHICLE_ALERT_DISMISS_UPDATE_FAILURE =
      "vehicle_alert_dismiss_failure";
  static const VEHICLE_SERVICE_DETAIL_PAGE = "vehicle_service_detail_page";
  static const VEHICLE_PRIMARY_USER_INFO_SUCCESS =
      "vehicle_primary_user_info_success";
  static const VEHICLE_PRIMARY_USER_INFO_FAILURE =
      "vehicle_primary_user_info_failure";
  static const VEHICLE_FETCH_SERVICE_APPOINTMENT_SUCCESS =
      "vehicle_fetch_service_appointment_success";
  static const VEHICLE_FETCH_SERVICE_APPOINTMENT_FAILURE =
      "vehicle_fetch_service_appointment_failure";
  static const VEHICLE_SAVE_PARKING_NOTES_SUCCESS =
      "vehicle_save_parking_notes_success";
  static const VEHICLE_SAVE_PARKING_NOTES_FAILURE =
      "vehicle_save_parking_notes_failure";
  static const VEHICLE_CLIMATE_PAGE = "vehicle_climate_page";
  static const VEHICLE_ACCOUNT_PAGE = "vehicle_account";
  static const VEHICLE_ACCOUNCEMENT_DETAIL_PAGE =
      "vehicle_announcement_detail_page";
  static const VEHICLE_CLIMATE_SETTING_SUCCESS =
      "vehicle_climate_setting_success";
  static const VEHICLE_CLIMATE_SETTING_FAILURE =
      "vehicle_climate_setting_failure";
  static const VEHICLE_CLIMATE_SETTING_PAGE = "vehicle_charge_setting_page";
  static const VEHICLE_FETCH_CLIMATE_STATUS_SUCCESS =
      "vehicle_climate_status_success";
  static const VEHICLE_FETCH_CLIMATE_STATUS_FAILURE =
      "vehicle_climate_status_failure";
  static const VEHICLE_FETCH_CLIMATE_PRE_CY17_STATUS_SUCCESS =
      "vehicle_climate_pre_cy17_status_success";
  static const VEHICLE_FETCH_CLIMATE_PRE_CY17_STATUS_FAILURE =
      "vehicle_climate_pre_cy17_status_failure";
  static const DEALER_SERVICE_APPOINTMENT_INFO_SUCCESS =
      "dealer_service_appoinment_info_success";
  static const DEALER_SERVICE_APPOINTMENT_INFO_FAILURE =
      "dealer_service_appoinment_info_failure";

  static const VEHICLE_CLIMATE_SCHEDULE_LIST_PAGE =
      "vehicle_charge_schedule_list_page";
  static const VEHICLE_CLIMATE_SCHEDULE_LIST_SUCCESS =
      "vehicle_charge_schedule_list_success";
  static const VEHICLE_CLIMATE_SCHEDULE_LIST_FAILURE =
      "vehicle_charge_schedule_list_failure";

  static const VEHICLE_CLIMATE_SCHEDULE_UPDATE_SUCCESS =
      "vehicle_charge_schedule_update_success";
  static const VEHICLE_CLIMATE_SCHEDULE_UPDATE_FAILURE =
      "vehicle_charge_schedule_update_failure";

  static const VEHICLE_CLIMATE_SCHEDULE_CREATE_SUCCESS =
      "vehicle_charge_schedule_create_success";
  static const VEHICLE_CLIMATE_SCHEDULE_CREATE_FAILURE =
      "vehicle_charge_schedule_create_failure";

  static const VEHICLE_CLIMATE_SCHEDULE_DELETE_SUCCESS =
      "vehicle_charge_schedule_delete_success";
  static const VEHICLE_CLIMATE_SCHEDULE_DELETE_FAILURE =
      "vehicle_charge_schedule_delete_failure";

  static const VEHICLE_CLIMATE_SCHEDULE_TIMER_SETTING_PAGE =
      "vehicle_climate_schedule_timer_setting_page";
  static const VEHICLE_CLIMATE_SCHEDULE_CLIMATE_SETTING_PAGE =
      "vehicle_climate_schedule_climate_setting_page";
  static const VEHICLE_FETCH_UBI_OFFERS_SUCCESS = "vehicle_ubi_offers_success";
  static const VEHICLE_FETCH_UBI_OFFERS_FAILURE = "vehicle_ubi_offers_failure";

  static const VEHICLE_SERVICE_APPOINTMENT_ODOMETER_SETUP_PAGE =
      "vehicle_service_appointment_odometer_setup_page";

  static const VEHICLE_SERVICE_APPOINTMENT_CONFIRMATION_PAGE =
      "vehicle_service_appointment_confirmation_page";

  static const DEALER_SERVICE_APPOINTMENT_INITIALIZE_PAGE =
      "dealer_service_appointment_initialize_page";

  static const DEALER_SERVICE_APPOINTMENT_INITIALIZE_SUCCESS =
      "dealer_service_appointment_initialize_success";
  static const DEALER_SERVICE_APPOINTMENT_INITIALIZE_FAILURE =
      "dealer_service_appointment_initialize_failure";

  static const VEHICLE_SERVICE_APPOINTMENT_SERVICE_PAGE =
      "vehicle_service_appointment_service_page";

  static const VEHICLE_SERVICE_APPOINTMENT_SERVICE_LIST_SUCCESS =
      "dealer_service_appointment_service_list_success";
  static const VEHICLE_SERVICE_APPOINTMENT_SERVICE_LIST_FAILURE =
      "dealer_service_appointment_service_list_failure";

  static const VEHICLE_SERVICE_APPOINTMENT_ADVISOR_PAGE =
      "vehicle_service_appointment_advisor_page";

  static const VEHICLE_SERVICE_APPOINTMENT_ADVISOR_LIST_SUCCESS =
      "dealer_service_appointment_advisor_list_success";
  static const VEHICLE_SERVICE_APPOINTMENT_ADVISOR_LIST_FAILURE =
      "dealer_service_appointment_advisor_list_failure";

  static const VEHICLE_SERVICE_APPOINTMENT_TRANSPORTATION_PAGE =
      "vehicle_service_appointment_transportation_page";
  static const VEHICLE_SERVICE_APPOINTMENT_TRANSPORTATION_PICKUP_PAGE =
      "vehicle_service_appointment_transportation_pickup_page";
  static const VEHICLE_SERVICE_APPOINTMENT_TRANSPORTATION_DELIVERY_PAGE =
      "vehicle_service_appointment_transportation_delivery_page";

  static const VEHICLE_SERVICE_APPOINTMENT_TRANSPORTATION_LIST_SUCCESS =
      "dealer_service_appointment_transportation_list_success";
  static const VEHICLE_SERVICE_APPOINTMENT_TRANSPORTATION_LIST_FAILURE =
      "dealer_service_appointment_transportation_list_failure";

  static const VEHICLE_SERVICE_APPOINTMENT_TIME_PAGE =
      "vehicle_service_appointment_time_page";

  static const DEALER_SERVICE_APPOINTMENT_UPDATE_PAGE =
      "dealer_service_appointment_update_page";

  static const VEHICLE_SERVICE_APPOINTMENT_TIME_SLOT_SUMMARY_LIST_SUCCESS =
      "vehicle_service_appointment_time_slot_summary_success";
  static const VEHICLE_SERVICE_APPOINTMENT_TIME_SLOT_SUMMARY_LIST_FAILURE =
      "vehicle_service_appointment_time_slot_summary_failure";
  static const VEHICLE_SERVICE_APPOINTMENT_TIME_SLOT_LIST_SUCCESS =
      "vehicle_service_appointment_time_slot_success";
  static const VEHICLE_SERVICE_APPOINTMENT_TIME_SLOT_LIST_FAILURE =
      "vehicle_service_appointment_time_slot_failure";

  static const VEHICLE_SERVICE_APPOINTMENT_CREATE_SUCCESS =
      "vehicle_service_appointment_create_success";
  static const VEHICLE_SERVICE_APPOINTMENT_CREATE_FAILURE =
      "vehicle_service_appointment_create_failure";

  static const VEHICLE_SERVICE_APPOINTMENT_UPDATE_SUCCESS =
      "vehicle_service_appointment_update_success";
  static const VEHICLE_SERVICE_APPOINTMENT_UPDATE_FAILURE =
      "vehicle_service_appointment_update_failure";

  static const VEHICLE_SERVICE_APPOINTMENT_DETAIL_SUCCESS =
      "vehicle_service_appointment_detail_success";
  static const VEHICLE_SERVICE_APPOINTMENT_DETAIL_FAILURE =
      "vehicle_service_appointment_detail_failure";

  static const VEHICLE_DIGITAL_KEY_SETUP_PAGE =
      "vehicle_digital_key_setup_page";
  static const VEHICLE_PARKED_LOCATOR_PAGE = "vehicle_parked_locator_page";

  static const GOOGLE_PLACE_AUTO_COMPLETE_SUCCESS =
      "google_place_auto_complete_success";
  static const GOOGLE_PLACE_AUTO_COMPLETE_FAILURE =
      "google_place_auto_complete_failure";

  static const GOOGLE_PLACE_DETAIL_SUCCESS = "google_place_detail_success";
  static const GOOGLE_PLACE_DETAIL_FAILURE = "google_place_detail_failure";

  static const GET_DEALER_BY_COORDINATES_SUCCESS =
      "get_dealer_by_coordinates_success";
  static const GET_DEALER_BY_COORDINATES_FAILURE =
      "get_dealer_by_coordinates_failure";

  static const GET_TOTAL_DEALER_WITH_FILTER_MODEL_SUCCESS =
      "get_total_dealer_with_filtermodel_success";
  static const GET_TOTAL_DEALER_WITH_FILTER_MODEL_FAILURE =
      "get_total_dealer_with_filter_model_failure";

  static const GET_DEALER_BY_DEALER_ID_SUCCESS =
      "get_dealer_by_dealerid_success";
  static const GET_DEALER_BY_DEALER_ID_FAILURE =
      "get_dealer_by_dealer_id_failure";

  static const SELECT_PREFERRED_DEALER_PAGE = "select_preferred_dealer_page";
  static const PREFERRED_DEALER_FILTER_BAR_PAGE =
      "preferred_dealer_filter_bar_page";
  static const VEHICLE_DEALER_DETAIL_PAGE = "vehicle_dealer_detail_page";
  static const SEARCH_PREFERRED_DEALER_PAGE = "search_preferred_dealer_page";
  static const VEHICLE_SERVICE_APPOINTMENT_DELETE_SUCCESS =
      "dealer_service_appointment_delete_success";
  static const VEHICLE_SERVICE_APPOINTMENT_DELETE_FAILURE =
      "dealer_service_appointment_delete_failure";

  static const VEHICLE_FETCH_PARKING_NOTES_SUCCESS =
      "vehicle_fetch_parking_notes_success";
  static const VEHICLE_FETCH_PARKING_NOTES_FAILURE =
      "vehicle_fetch_parking_notes_failure";
  static const VEHICLE_FETCH_DIAGNOSTICS_SUCCESS =
      "vehicle_fetch_diagnostics_success";
  static const VEHICLE_FETCH_DIAGNOSTICS_FAILURE =
      "vehicle_fetch_diagnostics_failure";

  static const VEHICLE_FETCH_SUBSCRIPTIONS_SUCCESS =
      "vehicle_fetch_subscriptions_success";
  static const VEHICLE_FETCH_SUBSCRIPTIONS_FAILURE =
      "vehicle_fetch_subscriptions_failure";

  static const VEHICLE_FETCH_SOFTWARE_UPDATE_NOTIFICATION_FAILURE =
      "vehicle_fetch_software_update_notification_failure";

  static const VEHICLE_WIFI_TRIAL_REMAINDER_SUCCESS =
      "vehicle_wifi_trial_remainder_success";
  static const VEHICLE_WIFI_TRIAL_REMAINDER_FAILURE =
      "vehicle_wifi_trial_remainder_failure";

  static const INAPP_MARKETING_OPTIN_START_PAGE =
      "inapp_marketing_optin_start_page";

  static const VEHICLE_MARKETING_ACKNOWLEDGEMENT_SUCCESS =
      "vehicle_marketing_acknowledgement_success";
  static const VEHICLE_MARKETING_ACKNOWLEDGEMENT_FAILURE =
      "vehicle_marketing_acknowledgement_failure";

  static const VEHICLE_LCFS_ELIGIBILITY_SUCCESS =
      "vehicle_vehicle_eligibility_success";
  static const VEHICLE_LCFS_ELIGIBILITY_FAILURE =
      "vehicle_vehicle_eligibility_failure";

  static const VEHICLE_WIFI_TRIAL_ACKNOWLEDGEMENT_SUCCESS =
      "vehicle_wifi_trial_acknowledgement_success";
  static const VEHICLE_WIFI_TRIAL_ACKNOWLEDGEMENT_FAILURE =
      "vehicle_wifi_trial_acknowledgement_failure";

  static const VEHICLE_EV_ENROLMENT_FAILURE = "vehicle_ev_enrolment_failure";
  static const VEHICLE_EV_CHARGING_SESSIONS_FAILURE =
      "vehicle_ev_charging_sessions_failure";

  static const VEHICLE_EV_MULTIDAY_SCHEDULE_PAGE =
      "vechicle_ev_multiday_schedule_page";

  static const VEHICLE_EV_PUB_FIND_STATIONS_PAGE =
      "vehicle_ev_pub_find_stations_page";
  static const VEHICLE_EV_PUB_FIND_FROM_CHARGEINFO_LINK =
      "vehicle_ev_pub_find_from_chargeinfo_link";
  static const VEHICLE_EV_PUB_FIND_FROM_FINDTAB_LINK =
      "vehicle_ev_pub_find_from_findtab_link";
  static const VEHICLE_EV_PUB_INVOICES_PAGE = "vehicle_ev_pub_invoices_page";
  static const VEHICLE_EV_PUB_PRICING_PAGE = "vehicle_ev_pub_pricing_page";
  static const VEHICLE_EV_PUB_CHARGE_SESSION_PAGE =
      "vehicle_ev_pub_charge_session_page";
  static const VEHICLE_EV_PUB_CDR_PAGE = "vehicle_ev_pub_cdr_page";
  static const VEHICLE_EV_PUB_CHARGE_PENDING_PAGE =
      "vehicle_ev_pub_charge_pending_page";
  static const VEHICLE_EV_PUB_CHARGE_CONFIRMED_PAGE =
      "vehicle_ev_pub_charge_confirmed_page";
  static const VEHICLE_EV_PUB_CHARGE_CANCELED_PAGE =
      "vehicle_ev_pub_charge_canceled_page";

  static const VEHICLE_EV_PUB_CHG_GROUP = "vehicle_ev_pub_chg_group";
  static const VEHICLE_EV_PUB_START_CHARGING_SUCCESS =
      "vehicle_ev_pub_start_charging_success";
  static const VEHICLE_EV_PUB_START_CHARGING_FAILURE =
      "vehicle_ev_pub_start_charging_failure";
  static const VEHICLE_EV_PUB_STOP_CHARGING_SUCCESS =
      "vehicle_ev_pub_stop_charging_success";
  static const VEHICLE_EV_PUB_STOP_CHARGING_CANCELED =
      "vehicle_ev_pub_stop_charging_canceled";
  static const VEHICLE_EV_PUB_STOP_CHARGING_FAILURE =
      "vehicle_ev_pub_stop_charging_failure";
  static const VEHICLE_EV_PUB_SESSION_STOPPED_SUCCESS =
      "vehicle_ev_pub_session_stopped_success";
  static const VEHICLE_EV_PUB_SESSION_STOPPED_FAILURE =
      "vehicle_ev_pub_session_stopped_failure";
  static const VEHICLE_EV_ECO_CHARGE_TOGGLE = "vehicle_ev_eco_charge_toggle";
  static const VEHICLE_EV_CREATE_A_SCHEDULE_BUTTON_CTA =
      "vehicle_ev_create_a_schedule_button_cta";
  static const VEHICLE_EV_WALLET_SET_UP = "vehicle_ev_wallet_set_up";
  static const VEHICLE_EV_PUB_REG_GROUP = "vehicle_ev_pub_reg_group";
  static const VEHICLE_EV_PUB_CP_REG_LINKED = "vehicle_ev_pub_cp_reg_linked";
  static const VEHICLE_EV_PUB_CP_REG_CREATED = "vehicle_ev_pub_cp_reg_created";
  static const VEHICLE_EV_PUB_CP_REG_FAILED = "vehicle_ev_pub_cp_reg_failed";
  static const VEHICLE_EV_PUB_EVGO_REG_LINKED =
      "vehicle_ev_pub_evgo_reg_linked";
  static const VEHICLE_EV_PUB_EVGO_REG_CREATED =
      "vehicle_ev_pub_evgo_reg_created";
  static const VEHICLE_EV_PUB_EVGO_REG_FAILED =
      "vehicle_ev_pub_evgo_reg_failed";
  static const VEHICLE_EV_CHARGE_POINT_REGISTER =
      "vehicle_ev_charge_point_register";
  static const VEHICLE_EV_EVGO_REGISTER = "vehicle_ev_evgo_register";
  static const VEHICLE_EV_CLEAN_ASSIST_ENROLL =
      "vehicle_ev_clean_assist_enroll";
  static const VEHICLE_EV_PUB_NAV_GROUP = "vehicle_ev_pub_nav_group";
  static const VEHICLE_EV_PUB_DIRECTIONS_DETAIL =
      "vehicle_ev_pub_directions_detail";
  static const VEHICLE_EV_PUB_DIRECTIONS_LIST =
      "vehicle_ev_pub_directions_list";
  static const VEHICLE_EV_PUB_SENDTOCAR_LIST_SUCCESS =
      "vehicle_ev_pub_sendtocar_list_success";
  static const VEHICLE_EV_PUB_SENDTOCAR_LIST_FAILURE =
      "vehicle_ev_pub_sendtocar_list_failure";
  static const VEHICLE_EV_PUB_SENDTOCAR_DETAIL_SUCCESS =
      "vehicle_ev_pub_sendtocar_detail_success";
  static const VEHICLE_EV_PUB_SENDTOCAR_DETAIL_FAILURE =
      "vehicle_ev_pub_sendtocar_detail_failure";
  static const VEHICLE_EV_CHARGE_INFO_STATS_CARD_TAP =
      "vehicle_ev_charge_info_stats_card_tap";
  static const VEHICLE_EV_WHAT_IS_CLEAN_ASSIST =
      "vehicle_ev_what_is_clean_assist";

  static const WALLET_GROUP = "wallet_group";
  static const WALLET_ADD_CARD_SUCCESS = "wallet_add_card_success";
  static const WALLET_ADD_CARD_FAILURE = "wallet_add_card_failure";
  static const ASSIGN_REMOTEUSER_INVITE = "assign_remoteuser_invite";
  static const ASSIGN_REMOTEUSER_SEARCH = "assign_remoteuser_search";
  static const ASSIGN_REMOTEUSER_SELF = "assign_remoteuser_self";
  static const GSETTINGS_ACTIVE = "gsettings_active";
  static const GSETTINGS_ACTIVE1 = "gsettings_active1";
  static const GSETTINGS_ACTIVE4 = "gsettings_active4";
  static const GSETTINGS_EDIT = "gsettings_edit";
  static const GSETTINGS_EDIT1 = "gsettings_edit1";
  static const GSETTINGS_EDIT4 = "gsettings_edit4";
  static const VEHICLE_HEALTH_ALERTS_DETAIL_SUCCESS =
      "health_alerts_detail_success";
  static const VEHICLE_HEALTH_ALERTS_DETAIL_FAILURE =
      "health_alerts_detail_failure";

  ///TFS
  static const TFS_ONETIME_PAYMENT_MAKE_PAYMENT_CARD_CTA =
      "tfs_OneTimePymt_make_payment_card_cta";
  static const TFS_PAYMENT_PAY_FROM = "tfs_payment_payfrom";
  static const TFS_PAYMENT_DATE = "tfs_payment_date";
  static const TFS_PAYMENT_FREQUENCY = "tfs_RecurringPMT_Frequency";
  static const TFS_PAYMENT_AMOUNT = "tfs_payment_amount";
  static const TFS_PAYMENT_SELECT_BANK_ACCT = "tfs_payment_select_bank_acct";
  static const TFS_PAYMENT_ADD_BANK_ACCT_CTA = "tfs_payment_add_bank_acct_cta";
  static const TFS_PAYMENT_SELECT_DATE = "tfs_payment_select_date_continue_cta";
  static const TFS_PAYMENT_SELECT_DATE_CONTINUE_CTA =
      "tfs_payment_select_date_continue_cta";
  static const TFS_PAYMENT_TOOLTIP_AMOUNT = "tfs_payment_tooltip_amount";
  static const TFS_PAYMENT_ENTER_AMOUNT_CONTINUE_CTA =
      "tfs_payment_EnterAmount_continue_cta";
  static const TFS_ONETIME_PAYMENT_SWIPE_TO_PAY_SUCCESSFUL =
      "tfs_OneTimePymt_SwipeToPay_successful";
  static const TFS_ONETIME_PAYMENT_SWIPE_TO_PAY_FAILED =
      "tfs_OneTimePymt_SwipeToPay_failed";
  static const TFS_ONETIME_PAYMENT_RECEIVED_SCREENSHOT_CTA =
      "tfs_OneTimePymt_received_screenshot_cta";
  static const TFS_ONETIME_PAYMENT_RECEIVED_CLOSE_CTA =
      "tfs_OneTimePymt_received_close_cta";
  static const TFS_PAYMENT_VIEW_HISTORY_CTA = "tfs_payment_view_history_cta";
  static const TFS_PAYMENT_CARD_CLICK_ELLIPSES =
      "tfs_PaymentCard_click_ellipses";
  static const TFS_CLICK_MANAGE_BANK_ACCOUNT = "tfs_click_manage_bank_account";
  static const TFS_CLICK_UNLINK_BANK_ACCT = "tfs_click_unlink_bank_acct";
  static const TFS_CLICK_TRANSACTION_HISTORY = "tfs_Transactions_CTA";
  static const TFS_MANAGE_BANK_ADD_BANK_ACCT = "tfs_ManageBank_add_bank_acct";
  static const TFS_MANAGE_BANK_SELECT_ACCT_FROM_LIST =
      "tfs_ManageBank_select_acct_fromlist";
  static const TFS_ONETIME_PAYMENT_EDIT_PAYMENT =
      "tfs_OneTimePymt_edit_payment";
  static const TFS_ONETIME_PAYMENT_EDIT_PAYMENT_CONFIRM_CTA =
      "tfs_OneTimePymt_edit_payment_confirm_cta";
  static const TFS_ONETIME_PAYMENT_CANCEL_PAYMENT_CTA =
      "tfs_OneTimePymt_cancel_payment_cta";
  static const TFS_ONETIME_PAYMENT_CANCEL_PAYMENT_GO_BACK_CTA =
      "tfs_OneTimePymt_CancelPymt_GoBack_cta";
  static const TFS_ONETIME_PAYMENT_CANCEL_PAYMENT_SUCCESSFUL =
      "tfs_OneTimePymt_CancelPayment_successful";
  static const TFS_ONETIME_PAYMENT_CANCEL_PAYMENT_FAILED =
      "tfs_OneTimePymt_CancelPayment_failed";
  static const TFS_ONETIME_PAYMENT_PAYMENT_CANCEL_CLOSE_CTA =
      "tfs_OneTimePymt_PaymentCancel_close_cta";
  static const TFS_ADD_BANK_MAKE_DEFAULT_CTA = "tfs_AddBank_make_default_cta";
  static const TFS_ADD_BANK_COMPLETE_CTA = "tfs_AddBank_complete_cta";
  static const TFS_ADD_BANK_BANK_ACCT_ADD_SUCCESSFUL =
      "tfs_AddBank_bank_acct_add_successful";
  static const TFS_ADD_BANK_BANK_ACCT_ADD_FAILED =
      "tfs_AddBank_bank_acct_add_failed";
  static const TFS_ADD_BANK_BANK_ACCT_ADD_CLOSE_CTA =
      "tfs_AddBank_bank_acct_add_close_cta";
  static const TFS_MANAGE_BANK_CLICK_OPTIONS_ICON =
      "tfs_ManageBank_click_options_icon";
  static const TFS_EDIT_BANK_VIEW_BANK_ACCT = "tfs_EditBank_view_bank_acct";
  static const TFS_EDIT_BANK_DELETE_BANK_ACCT = "tfs_EditBank_remove_bank_acct";
  static const TFS_EDIT_BANK_CLICK_MAKE_DEFAULT_CTA =
      "tfs_EditBank_click_make_default_cta";
  static const TFS_EDIT_BANK_CLICK_SAVE_CHANGES_CTA =
      "tfs_EditBank_click_SaveChanges_cta";
  static const TFS_EDIT_BANK_POPUP_CLICK_CANCEL_CTA =
      "tfs_EditBank_popup_click_cancel_cta";
  static const TFS_EDIT_BANK_POPUP_CLICK_SAVE_CHANGES_CTA =
      "tfs_EditBank_popup_click_SaveChanges_cta";
  static const TFS_DELETE_BANK_POPUP_CLICK_REMOVE_CTA =
      "tfs_DeleteBank_popup_click_remove_cta";
  static const TFS_DELETE_BANK_POPUP_CLICK_CANCEL_CTA =
      "tfs_DeleteBank_popup_click_cancel_cta";
  static const TFS_DELETE_BANK_REMOVE_ACCT_SUCCESSFUL =
      "tfs_DeleteBank_remove_acct_successful";
  static const TFS_DELETE_BANK_REMOVE_ACCT_FAILED =
      "tfs_DeleteBank_remove_acct_failed";
  static const TFS_LEASE_END_EXPERIENCE_CLICK = "tfs_LeaseEndExperience_CTA";
  static const TFS_FAQ_CLICK = "tfs_FAQS_CTA";
  static const TFS_UNLINK_ACCT_CLICK = "tfs_unlink_acct_click";
  static const TFS_UNLINK_ACCT_CLICK_CANCEL_CTA =
      "tfs_unlink_acct_click_cancel_cta";
  static const TFS_UNLINK_ACCT_CTA = "tfs_unlink_acct_cta";
  static const TFS_UNLINK_ACCT_SUCCESSFUL = "tfs_unlink_acct_successful";
  static const TFS_UNLINK_ACCT_FAILED = "tfs_unlink_acct_failed";
  static const TFS_ONETIME_PAYMENT_FAILED_TRY_AGAIN_CTA =
      "tfs_OneTimePymt_failed_TryAgain_cta";
  static const TFS_AUTH_TC_CONTINUE_CTA = "tfs_auth_T&C_continue_cta";
  static const TFS_AUTH_ACCT_LOCKED_CLOSE_CTA =
      "tfs_auth_acct_locked_close_cta";
  static const TFS_AUTH_INCORRECT_CODE_TRY_AGAIN_CTA =
      "tfs_auth_incorrect_code_try_again_cta";
  static const TFS_AUTH_CODE_ERROR_TRY_AGAIN_CTA =
      "tfs_auth_code_error_try_again_cta";
  static const TFS_AUTH_START_LINK_ACCOUNT_CTA =
      "tfs_auth_start_link_account_cta";
  static const TFS_AUTH_DISCLOSURE_CONTINUE_CTA =
      "tfs_auth_disclosure_continue_cta";
  static const TFS_AUTH_DISCLOSURE_POLICY_AGREEMENTS =
      "tfs_auth_disclosure_Policy&Agreements";
  static const TFS_AUTH_SSN_POLICY_AGREEMENTS =
      "tfs_auth_ssn_Policy&Agreements";
  static const TFS_AUTH_SOCIAL_REQ_NEW_CODE = "tfs_auth_social_req_new_code";
  static const TFS_AUTH_SOCIAL_TRUST_DEVICE_CTA =
      "tfs_auth_social_trust_device_cta";
  static const TFS_AUTH_SOCIAL_DONT_TRUST_DEVICE_CTA =
      "tfs_auth_dont_trust_device_cta";
  static const TFS_AUTH_ENTER_SSN_SUCCESS = "tfs_auth_enter_ssn_success";
  static const TFS_ADD_BANK_EXIT_SCREEN_CONFIRM_CTA =
      "tfs_AddBank_exit_screen_confirm_cta";
  static const TFS_ADD_BANK_EXIT_SCREEN_CANCEL_CTA =
      "tfs_AddBank_exit_screen_cancel_cta";

  static const TFS_EDIT_PAYMENT_PAY_FROM = "tfs_EditPayment_payfrom";
  static const TFS_EDIT_PAYMENT_DATE = "tfs_EditPayment_date";
  static const TFS_EDIT_PAYMENT_AMOUNT = "tfs_EditPayment_amount";
  static const TFS_EDIT_PAYMENT_SELECT_BANK_ACCT =
      "tfs_EditPayment_select_bank_acct";
  static const TFS_EDIT_PAYMENT_ADD_BANK_ACCT_CTA =
      "tfs_EditPayment_add_bank_acct_cta";
  static const TFS_EDIT_PAYMENT_SELECT_DATE = "tfs_EditPayment_select_date";
  static const TFS_EDIT_PAYMENT_TOOLTIP_AMOUNT =
      "tfs_EditPayment_tooltip_amount";
  static const TFS_EDIT_PAYMENT_ENTER_AMOUNT_CONTINUE_CTA =
      "tfs_EditPayment_EnterAmount_continue_cta";
  static const TFS_EDIT_PAYMENT_SWIPE_TO_EDIT_SUCCESSFUL =
      "tfs_EditPayment_SwipeToEdit_successful";
  static const TFS_TFS_EDIT_PAYMENT_SWIPE_TO_EDIT_FAILED =
      "tfs_tfs_EditPayment_SwipeToEdit_failed";
  static const TFS_EDIT_PAYMENT_RECEIVED_SCREENSHOT_CTA =
      "tfs_EditPayment_received_screenshot_cta";
  static const TFS_TFS_EDIT_PAYMENT_RECEIVED_CLOSE_CTA =
      "tfs_EditPayment_received_close_cta";
  static const TFS_MANAGE_BANK_ADD_ACCT_EXIT_SCREEN_CONFIRM_CTA =
      "tfs_ManageBank_add_acct_exit_screen_confirm_cta";
  static const TFS_MANAGE_BANK_ADD_ACCT_EXIT_SCREEN_CANCEL_CTA =
      "tfs_ManageBank_add_acct_exit_screen_cancel_cta";

  static const TFS_AUTH_ACCOUNT_REGISTERED_SSO_SUCCESS =
      "tfs_auth_account_registered_sso_success";
  static const TFS_AUTH_ACCOUNT_LINKED_SSO_SUCCESS =
      "tfs_auth_account_linked_sso_success";
  static const TFS_AUTH_ACCOUNT_LINKED_SUCCESSFULLY =
      "tfs_auth_account_linked_successfully";
  static const TFS_RECURRING_PMT_FREQUENCY_WEEKLY =
      "tfs_RecurringPMT_Frequency_Weekly";
  static const TFS_RECURRING_PMT_FREQUENCY_MONTHLY =
      "tfs_RecurringPMT_Frequency_ Monthly";
  static const TFS_RECURRING_PMT_FREQUENCY_BI_WEEKLY =
      "tfs_RecurringPMT_Frequency_ Bi-Weekly";
  static const TFS_SELECT_PMT_TYPE_CTA = "tfs_SelectPMT_Type_CTA";
  static const TFS_CANCEL_RECURRING_PMT_PLAN_CTA =
      "tfs_CancelRecurringPMT_Plan-CTA";
  static const TFS_RECURRING_PMT_SWIPE_TO_PAY_SUCCESSFUL =
      "tfs_RecurringPMT_SwipeToPay_successful";
  static const TFS_RECURRING_PMT_SWIPEPAY_UNSUCCESSFUL =
      "tfs_RecurringPMT_SwipePay_unsuccessful";
  static const TFS_RECURRING_PMT_PLAN_SUCCESSFUL_CTA =
      "tfs_RecurringPMTPlan-Successful-CTA";
  static const TFS_CANCEL_RECURRING_PMT_ONE_TIME_CTA =
      "tfs_CancelRecurringPMT_One-Time-CTA";
  static const TFS_AUTH_ENTER_SSN_FAILED = "tfs_auth_enter_ssn_failed";
  static const TFS_SUBMIT_RECURRING_PMT_CTA = "tfs_SubmitRecurringPMT_CTA";
}
