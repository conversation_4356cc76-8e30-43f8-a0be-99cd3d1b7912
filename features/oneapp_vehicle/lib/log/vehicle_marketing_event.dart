// This class have all the flutter side vehicle related marketing events.
class VehicleMarketingEvent {
  // The following api related marketing events will me moved to oneapp_network or network_implementation
  // static const VIN_LIST_COMPLETE_FAILURE = "vin_list_complete_failure";
  // static const VIN_LIST_FAILURE = "vin_list_failure";

  static const LOGOUT = "logout";
  static const VIN_LIST_CALL_LAUNCH = "vin_list_call_launch";
  static const VIN_LIST_SUCCESS = "vin_list_success";
  static const VIN_LIST_FAILURE = "vin_list_failure";

  static const SELECT_PREFERRED_DEALER = "select_preferred_dealer";
  static const VIEW_VEHICLE_CAPABILITIES = "view_vehicle_capabilities";
  static const EDIT_VEHICLE_NAME = "edit_vehicle_name";
  static const DASHBOARD_CARD = "dashboard_card"; // Event Group
  static const DASHBOARD_REFRESH = "dashboard_refresh";
  static const DASHBOARD_CAMPAIGN = "dashboard_campaign";
  static const DASHBOARD_GARAGE = "dashboard_garage";
  static const DASHBOARD_NHISTORY = "dashboard_nhistory";
  static const DASHBOARD_PROFILE = "dashboard_profile";
  static const DASHBOARD_RECALL = "dashboard_recall";
  static const DASHBOARD_SCORE = "dashboard_score";
  static const DASHBOARD_TRIPS = "dashboard_trips";
  static const DASHBOARD_VALERTS = "dashboard_valerts";
  static const DASHBOARD_CALERTS = "dashboard_criticalalerts";
  static const DASHBOARD_VHR = "dashboard_vhr";
  static const DASHBOARD_VLOCATION = "dashboard_vlocation";
  static const GARAGE_SELECT = "garage_select"; // Event Group
  static const GARAGE_DEALER = "garage_dealer";
  static const GARAGE_REMOTEUSER = "garage_remoteuser";
  static const GARAGE_REMOVE = "garage_remove";
  static const GARAGE_REMOVE_SUCCESSFUL = "garage_remove_successful";
  static const GARAGE_REMOVE_UNSUCCESSFUL = "garage_remove_unsuccessful";
  static const GARAGE_TIRE = "garage_tire";
  static const GARAGE_FAQ = "garage_faq";
  static const GARAGE_PERSONAL_SETTINGS =
      "MG_GloveBox_PersonalSettings_LDlink_CTA";
  static const GARAGE_MANUALS = "garage_manuals";
  static const GARAGE_SERVICE_HISTORY = "garage_service_history";
  static const GARAGE_HOW_TO_VIDEOS = "garage_how_to_videos";
  static const GARAGE_MAINTENANCE_TIMELINE = "garage_maintenance_timeline";
  static const GARAGE_VEHICLE_CAPABILITY = "garage_vehicle_capability";
  static const DASHBOARD_UBIOFFER = "dashboard_ubioffer";
  static const DASHBOARD_OWNER_MANUAL = "dashboard_owner_manual";
  static const DASHBOARD_SERVICE_HISTORY = "dashboard_service_history";
  static const DASHBOARD_ROADSIDE_ASSISTANCE = "dashboard_roadside_assistance";
  static const DASHBOARD_OPTOUT_DRIVERSCORE = "dashboard_optout_driverscore";
  static const DASHBOARD_OPTIN_DRIVERSCORE = "dashboard_optin_driverscore";
  static const DASHBOARD_COLLISION_ASSIST_DISPLAYED =
      "dashboard_collision_assist_displayed";
  static const REMOTE_GSETTINGS = "remote_gsettings";
  static const REMOTE_ADVANCED_CONTROLS = "remote_advanced_controls";
  static const REMOTE_TIMER_SETTINGS = "remote_timer_settings";
  static const REMOTE_CONNECT = "remote_connect";
  static const REMOTE_DIGITAL_KEY_CONNECTED = "remote_digital_key_connected";
  static const REMOTE_SETTINGS_TOGGLE_ON =
      "Remote_settings_toggle_ON"; //Event Group
  static const REMOTE_SETTINGS_TOGGLE_OFF =
      "Remote_settings_toggle_OFF"; //Event Group
  static const FRONT_DEFROST = "front_defrost";
  static const BACK_DEFROST = "back_defrost";
  static const STEERING_WHEEL = "steering_wheel";
  static const FRONT_SEAT = "front_seat";
  static const PASSENGER_SEAT = "passenger_seat";
  static const REAR_SEAT = "rear_seat";
  static const REAR_PASSENGER = "rear_passenger";
  static const SEAT_VENT = "seat_vent";
  static const SEAT_VENT_PASSENGER = "seat_vent_passenger";
  static const REMOTE_AIR_CIRC_INSIDE = "Remote_Air_Circ_Total_inside";
  static const REMOTE_AIR_CIRC_OUTSIDE = "Remote_Air_Circ_Total_outside";
  static const GSETTINGS_ACTIVE1 = "gsettings_active1";
  static const GSETTINGS_ACTIVE2 = "gsettings_active2";
  static const GSETTINGS_ACTIVE3 = "gsettings_active3";
  static const GSETTINGS_ACTIVE4 = "gsettings_active4";
  static const GSETTINGS_ACTIVE5 = "gsettings_active5";
  static const GSETTINGS_EDIT = "gsettings_edit"; // Event Group
  static const GSETTINGS_EDIT1 = "gsettings_edit1";
  static const GSETTINGS_EDIT2 = "gsettings_edit2";
  static const GSETTINGS_EDIT3 = "gsettings_edit3";
  static const GSETTINGS_EDIT4 = "gsettings_edit4";
  static const GSETTINGS_EDIT5 = "gsettings_edit5";
  static const GSETTINGS_AREA = "gsettings_area";
  static const GSETTINGS_AREA_SETLIMIT = "gsettings_area_setlimit";
  static const GSETTINGS_FEATURE = "gssettings_feature"; // Event Group
  static const GSETTINGS_CURFEW = "gsettings_curfew";
  static const GSETTINGS_CURFEW_SETCURFEW = "gsettings_curfew_setCurfew";
  static const GSETTINGS_SPEED = "gsettings_speed";
  static const GSETTINGS_SPEED_SETLIMIT = "gsettings_speed_setlimit";
  static const GSETTINGS_TIME = "gsettings_time";
  static const GSETTINGS_TIME_SETLIMIT = "gsettings_time_setlimit";
  static const GSETTINGS_MILE = "gsettings_mile";
  static const GSETTINGS_MILE_SETLIMIT = "gsettings_mile_setlimit";
  static const VEHICLE_SPECIFICATIONS = "vehicle_specifications";
  static const DRIVER_SCORE_OPTIN_SUCCESS = "driver_score_optin_success";
  static const DRIVER_SCORE_OPTIN_FAILED = "driver_score_optin_failed";
  static const DRIVER_SCORE_OPTOUT_SUCCESS = "driver_score_optout_success";
  static const DRIVER_SCORE_OPTOUT_FAILED = "driver_score_optout_failed";
  static const ASSIGN_REMOTEUSER_INVITE = "assign_remoteuser_invite";
  static const ASSIGN_REMOTEUSER_SEARCH = "assign_remoteuser_search";
  static const ASSIGN_REMOTEUSER_SELF = "assign_remoteuser_self";
  static const ASSIGN_REMOTEUSER_INVITE_COMPLETED =
      "assign_remoteuser_invite_completed";
  static const LASTTRIP_CLEAR_RECENT_TRIP_HISTORY =
      "lasttrip_clear_recent_trip_history";
  static const ADD_MILEAGE_CLICKED = "add_mileage";
  static const RECOMMENDED_SERVICES_UNAVAILABLE =
      "no_recommended_services_available";
  static const SCHEDULE_MAINTENANCE_CLICKED = "schedule_maintenance_clicked";
  static const WARNING_LIGHTS_GUIDE = "tap_warning_lights_guide";
  static const TFS_LINKING_PROMPT = "tfs_user_prompted_to_link_account";
  static const TFS_ACCOUNT_MANUAL_UNLINK = "tfs_account_manual_unlink";
  static const PARKING = "parking"; //Event Group
  static const PARKING_DASHBOARD_FIND_PARKING_CLICK =
      "parking_dashboard_find_parking";
  static const PARKING_DASHBOARD_VIEW_DETAILS_CLICK =
      "parking_dashboard_view_details";
  static const PARKING_DASHBOARD_RESERVATIONS_CLICK =
      "parking_dashboard_reservations";
  static const Users_Auto_Collision_DashboardCard_CTA =
      "Users_Auto_Collision_DashboardCard_CTA";
  static const VHR_HISTORY_GET_HEALTH_REPORTS_SUCCESSFUL =
      "Api_Get_Health_Rpt_His_Success";
  static const VHR_HISTORY_GET_HEALTH_REPORTS_UNSUCCESSFUL =
      "Api_GetHealth_Rpt_His_Unsuccess";
  static const VHR_HISTORY_GET_DIAGNOSTIC_REPORTS_SUCCESSFUL =
      "Api_Get_Diagnostic_Rpt_His_Success";
  static const VHR_HISTORY_GET_DIAGNOSTIC_REPORTS_UNSUCCESSFUL =
      "Api_Get_Diagnostic_Rpt_His_Unsuccess";
  static const VHR_HISTORY_EMAIL_REPORT_SUCCESSFUL =
      "Api_Health_Rpt_His_Email_Success";
  static const VHR_HISTORY_EMAIL_REPORT_UNSUCCESSFUL =
      "Api_Health_Rpt_His_Email_Unsuccess";
  static const VHR_HISTORY_GET_CONSENTED_FACILITIES_SUCCESSFUL =
      "Api_Health_Diag_Get_Fac_Success";
  static const VHR_HISTORY_GET_CONSENTED_FACILITIES_UNSUCCESSFUL =
      "Api_Health_Diag_Get_Fac_Unsuccess";
  static const VHR_HISTORY_SEARCH_FACILITIES_SUCCESSFUL =
      "Api_Health_Diag_Search_Fac_Success";
  static const VHR_HISTORY_SEARCH_FACILITIES_UNSUCCESSFUL =
      "Api_Health_Diag_Search_Fac_Unsuccess";
  static const VHR_HISTORY_CONSENT_FACILITY_SUCCESSFUL =
      "Api_Health_Diag_Consent_Fac_Success";
  static const VHR_HISTORY_CONSENT_FACILITY_UNSUCCESSFUL =
      "Api_Health_Diag_Consent_Fac_Unsuccess";
  static const VHR_HISTORY_REVOKE_CONSENTED_FACILITY_SUCCESSFUL =
      "Api_Health_Diag_Revoke_Fac_Success";
  static const VHR_HISTORY_REVOKE_CONSENTED_FACILITY_UNSUCCESSFUL =
      "Api_Health_Diag_Revoke_Fac_Unsuccess";
  static const VHR_HISTORY_USER_CLICK_HEALTH_REPORT_HISTORY =
      "Users_Health_Rpt_Cta";
  static const VHR_HISTORY_USER_CLICK_DIAGNOSTIC_REPORT_HISTORY =
      "Users_Diagnostic_Rpt_Cta";
  static const VHR_HISTORY_USER_EMAIL_HEALTH_REPORT_SUCCESS =
      "Users_Health_Rpt_Email_Success";
  static const VHR_HISTORY_USER_EMAIL_DIAGNOSTIC_REPORT_SUCCESS =
      "Users_Diagnostic_Rpt_Email_Success";
  static const VHR_HISTORY_USER_CLICK_HEALTH_AND_DIAGNOSTIC_ACCESS =
      "Users_Health_Diag_Access_Cta";
  static const VHR_HISTORY_USER_GRANT_REPORT_ACCESS =
      "Users_Health_Diag_Grant_Access";
  static const VHR_HISTORY_USER_REVOKE_REPORT_ACCESS =
      "Users_Health_Diag_Revoke_Access";
  static const SIENNA_USERMANUAL_CLICKED = "sienna_user_manual_clicked";
  static const TOYOTA_FOR_FAMILIES_CLICKED = "toyota_for_families_clicked";
  static const MYDEST_USERS_SELECT = "mydest_users_select"; //Event group
  static const MY_DESTINATIONS_DASHBOARD_VIEW_DESTINATIONS =
      "users_total_viewdestination_cta";
  static const USERS_INVITE_REMOTE_AUTHORIZED_CTA =
      "USERS_INVITE_REMOTE_AUTHORIZED_CTA";
  static const LCFS_DASHBOPARD_TAP_CARD = "lcfs_dashboard_tap_card";
  static const LCFS_DESCRIPTION_CONSENT_ACCEPTED =
      "lcfs_description_consent_accepted";
  static const GSETTINGS_ACTIVE = "gsettings_active"; //Event Group
  static const GARAGE_SUBSCRIPTIONS = "garage_subscriptions";

  static const UBI_OFFER_CONSENT_MESSAGE_DISPLAYED =
      "ubioffer_consent_message_displayed";
  static const UBI_OFFER_CONSENT_MESSAGE_SUCCESS =
      "ubioffer_consent_message_success";
  static const UBI_OFFER_CONSENT_MESSAGE_UNSUCCESSFUL =
      "ubioffer_consent_message_unsuccessful";
  static const UBI_OFFER_GET_QUOTE = "ubioffer_get_quote";
  static const UBI_OFFER_GET_QUOTE_SUCCESS = "ubioffer_get_quote_success";
  static const UBI_OFFER_GET_QUOTE_UNSUCCESSFUL =
      "ubioffer_get_quote_unsuccessful";
  static const UBI_OFFER_AUTHORIZATION_TO_SHARE_CONSENT =
      "bioffer_on_tap_authorization_to_share_consent";
  static const UBI_OFFER_GET_QUOTE_UN_AVAILABLE =
      "ubi_offer_get_quote_un_available";
  static const ONBOARDING_COMPLETE = "onboarding_complete";

  static const USERS_CLICK_ENABLE_ALL_TRIALS = "Users_Click_Enable_All_Trials";
  static const USERS_ADD_SERVICECTA = "Users_Add_ServiceCTA";
  static const USERS_VIEW_TRIAL_PRODUCTS = "Users_View_Trial_Products";
  static const USERS_VIEW_TRIAL_ENBSERVICE = "Users_View_Trial_EnbService";
  static const USERS_PAID_SERVICE = "Users_Paid_Service";

  static const MAPBOX_ONEAPP = "mapbox_oneapp";
  static const OVERVIEW_LASTPARKED_LOCATION = "overview_lastparked_location";
  static const TRIPS_RECENT_TRIPS = "trips_recent_trips";
  static const FIND_MENU = "find_menu";
  static const CHARGE_STATIONS = "charge_stations";
  static const RENTALS = "rentals";
  static const VEH_20TM_INFO_SUCCESSFUL = "veh_20tm_info_successful";
  static const VEH_20TM_INFO_UNSUCCESSFUL = "veh_20tm_info_unsuccessful";
  static const VEH_20TM_HISTORY_SUCCESSFUL = "veh_20tm_history_successful";
  static const VEH_20TM_HISTORY_UNSUCCESSFUL = "veh_20tm_history_unsuccessful";
  static const DASHBOARD_TFS = "dashboard_tfs";
  static const DASHBOARD_TFS_MAKE_A_PAYMENT_SHOWN =
      "dashboard_tfs_make_a_payment_shown";
  static const DASHBOARD_TFS_LINK_SHOWN = "dashboard_tfs_link_shown";
  static const DASHBOARD_TFS_LINK_CLICKED = "dashboard_tfs_link_clicked";
  static const DASHBOARD_TFS_MANAGE_CLICKED = "dashboard_tfs_manage_clicked";
  static const DASHBOARD_TFS_DISABLED_CARD_SHOWN =
      "dashboard_tfs_disabled_card_shown";
  static const DASHBOARD_TFS_DISABLED_CARD_CLICKED =
      "dashboard_tfs_disabled_card_clicked";
  static const DASHBOARD_REVALIDATE_ACCOUNT_HIDE =
      "dashboard_revalidate_account_hide";
  static const DASHBOARD_TFS_VALIDATE_CREDENTIALS_SHOWN =
      "dashboard_tfs_validate_credentials_shown";
  static const DASHBOARD_TFS_VALIDATE_CARD_CLICKED =
      "dashboard_tfs_validate_card_clicked";
  static const USERS_COLLISION_NOTIFICATION_INSU_ACCEPT =
      "Users_Collision_Notification_Insu_Accept";
  static const USERS_COLLISION_NOTIFICATION_INSU_REJECT =
      "Users_Collision_Notification_Insu_Reject";
  static const USERS_AUTO_COLLISION_ACKOWLEDGE_CTA =
      "Users_Auto_Collision_Acknwldg_CTA";
  static const Users_AUTO_COLLISION_DENIED_CTA =
      "Users_Auto_Collision_Denied_CTA";
  static const Users_AUTO_COLLISION_DASHBOARDCARD_CTA =
      "Users_Auto_Collision_DashboardCard_CTA";
  static const PARKING_DASHBOARD_CARD_SUCCESSFUL =
      "parking_dashboard_card_successful";
  static const PARKING_DASHBOARD_CARD_UNSUCCESSFUL =
      "parking_dashboard_card_unsuccessful";
  static const SERVICE_SCHEDULE = "ServiceSchedule"; // EventGroup
  static const APPOINTMENT_FROM_PREFEREDDEALER =
      "appointment_from_preffereddealer";
  static const APPOINTMENT_FROM_MAINTENANCESCHEDULE =
      "appointment_from_maintenanceschedule";
  static const APPOINTMENT_FROM_HEALTHNALERTS =
      "appointment_from_healthnalerts";
  static const APPOINTMENT_FROM_RECALL = "appointment_from_recall";
  static const APPOINTMENT_FROM_VEHICLESUPPORT =
      "appointment_from_vehiclesupport";
  static const PREFERREDDEALER_CALL = "preferreddealer_call";
  static const PREFERREDDEALER_WEBSITE = "preferreddealer_website";
  static const PREFERREDDEALER_DIRECTIONS = "preferreddealer_directions";
  static const PREFERREDDEALER_CHANGE = "preferreddealer_change";
  static const SET_AS_PREFERREDDEALER = "set_as_preferreddealer";
  static const APPOINTMENTEDIT_CANCEL_APPOINTMENT =
      "appointmentedit_cancel_appointment";
  static const APPOINTMENTEDIT_CANCEL_APPOINTMENT_BACK =
      "appointmentedit_cancel_appointment_back";
  static const APPOINTMENTEDIT_FACTORYRECOMMEND_SERVICE =
      "appointmentedit_factoryrecommend_service";
  static const APPOINTMENTEDIT_DEALERECOMMEND_SERVICES =
      "appointmentedit_dealerecommend_services";
  static const APPOINTMENTEDIT_ALL_SERVICES = "appointmentedit_all_services";
  static const APPOINTMENTEDIT_EDIT_SERVICEADVISOR =
      "appointmentedit_edit_serviceadvisor";
  static const APPOINTMENTEDIT_EDIT_TRANSPORTATION =
      "appointmentedit_edit_transportation";
  static const APPOINTMENTEDIT_EDIT_DATETIME = "appointmentedit_edit_datetime";
  static const APPOINTMENTEDIT_CONFIRM_APPOINTMENT =
      "appointmentedit_confirm_appointment";
  static const APPOINTMENTVIEW_SELECTSERVICE = "appointmentview_selectservice";
  static const APPOINTMENTVIEW_UPCOMINGSERVICE =
      "appointmentview_upcomingservice";
  static const APPOINTMENTVIEW_PASTSERVICE = "appointmentview_pastservice";
  static const APPOINTMENTVIEW_SERVICEDETAILS =
      "appointmentview_servicedetails";
  static const SUPPORTDC_MAKEAPPOINTMENT_UPDATEMILEAGE =
      "supportDC_makeappointment_updatemileage";
  static const SUPPORTDC_MAKEAPPOINTMENT_CHANGEDEALER =
      "supportDC_makeappointment_changedealer";
  static const SUPPORTDC_MAKEAPPOINTMENT_SELECTDEALER =
      "supportDC_makeappointment_selectdealer";
  static const SUPPORTDC_REPAIRS_FACTORYRECOMMENDED =
      "supportDC_repairs_factoryrecommended";
  static const SUPPORTDC_REPAIRS_DEALERRECOMMENDED =
      "supportDC_repairs_dealerrecommended";
  static const SUPPORTDC_REPAIRS_ALLSERVICES = "supportDC_repairs_allservices";
  static const SUPPORTDC_MAKEAPPOINTMENT_SERVICEADVISOR =
      "supportDC_makeappointment_serviceadvisor";
  static const SUPPORTDC_MAKEAPPOINTMENT_TRANSPORTATION =
      "supportDC_makeappointment_transportation";
  static const SUPPORTDC_VIEWAVAILABLEDATETIME =
      "supportDC_viewavailabledatetime";
  static const SUPPORTDC_MAKEAPPOINTMENT_SELECTDATE =
      "supportDC_makeappointment_selectdate";
  static const SUPPORTDC_MAKEAPPOINTMENT_SELECTTIME =
      "supportDC_makeappointment_selecttime";
  static const SUPPORTDC_CONFIRMSERVICEAPPOINTMENT =
      "supportDC_confirmserviceappointment";
  static const SUPPORTDC_APPOINTMENTCONFIRMED =
      "supportDC_appointmentconfirmed";
  static const SUPPORTDC_ADDTOCALENDAR = "supportDC_addtocalendar";
  static const SUPPORTDC_SERVICEDETAILS = "supportDC_servicedetails";
  static const ADVISOR_V_CARD = "appointmentview_selectserviceVcard";
  static const SXM_RADIO = "sirius_xm_radio"; //Event Group
  static const SXM_RADIO_CARD_CLICKED = "sirius_xm_radio_click_card";
  static const SXM_RADIO_DETAILS_PAGE_CLICK_CTA =
      "sirius_xm_radio_details_page_click_cta";
  static const SERVICE_PACKAGE_FACTORY_RECOMMENDED =
      "select_servicepackage_factoryrec";
  static const SERVICE_PACKAGE_DEALER_RECOMMENDED =
      "select_servicepackage_dealerrec";
  static const APPOINTMENT_SELECT_ADVISOR_CALL =
      "appointmentview_selectadvisor_call";
  static const APPOINTMENT_SELECT_ADVISOR_TEXT =
      "appointmentview_selectadvisor_text";
  static const APPOINTMENT_SELECT_ADVISOR_EMAIL =
      "appointmentview_selectadvisor_email";
  static const APPOINTMENT_SELECT_ADVISOR_DETAIL =
      "appointmentview_selectadvisor_details";
  static const APPOINTMENT_VIEW_SAVE_CONTACT = "appointmentview_save_contact";
  static const APPOINTMENT_NEW_BIDIR_SYNC = "appointmentnew_bidir_sync";
  static const APPOINTMENT_MODIFIED_BIDIR_SYNC =
      "appointmentmodified_bidir_sync";
  static const APPOINTMENT_CANCEL_BIDIR_SYNC = "appointmentcancel_bidir_sync";
  static const APPOINTMENT_CREATE_RENTAL_RESERVE =
      "appointmentcreate_rental_reserve";
  static const APPOINTMENT_MODIFIED_RENTAL_RESERVE =
      "appointmentmodified_rental_reserve";
  static const APPOINTMENT_CANCEL_RENTAL_RESERVE =
      "appointmentcancel_rental_reserve";
  static const SPM_DEALER_LOGO = "SPM_dealer_logo";
  static const SPM_NON_DEALER_LOGO = "SPM_nondealer_logo";
}
