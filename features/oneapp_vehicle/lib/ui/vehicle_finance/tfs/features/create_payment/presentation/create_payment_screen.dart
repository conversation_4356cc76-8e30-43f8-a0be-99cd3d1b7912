// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../core/mixins/mixins.dart';
import '../../core/utils/utils.dart';
import '../../dashboard/application/account_summary_state.dart';
import '../../dashboard/application/application.dart';
import '../../dashboard/domain/account.dart';
import '../../dashboard/domain/account_summary.dart';
import '../../payment_history/application/application.dart';
import '../../payment_history/domain/payment_history.dart';
import '../../payment_history/presentation/widget/car_info_card_area.dart';
import '../../payment_methods/application/application.dart';
import '../../payment_methods/domain/domain.dart';
import '../../payment_methods/presentation/widgets/pay_from_tile.dart';
import '../application/application.dart';
import '../domain/payment_bre.dart';
import '../domain/payment_types.dart';
import 'payment_functions.dart';
import 'payment_history_handler.dart';
import 'widget/build_pay_button.dart';
import 'widget/pay_on_tile_widget.dart';
import 'widget/payment_amount_tile.dart';
import 'widget/payment_type_selection_widget.dart';
import 'widget/terms_conditions_payments.dart';

class CreatePaymentScreen extends ConsumerStatefulWidget {
  const CreatePaymentScreen(this.account, this.fromNative, {Key? key})
      : super(key: key);
  final Account? account;
  final bool? fromNative;

  @override
  CreatePaymentScreenState createState() => CreatePaymentScreenState();
}

class CreatePaymentScreenState extends ConsumerState<CreatePaymentScreen>
    with
        GenericError,
        GenericEmptyErrorStateWidget,
        TFSHelperWidget,
        BottomSheetWrapperAroundScaffold {
  final DateTime dateToday = DateFormatterUtil.getNextAvailableDay();
  DateTime? _paymentDate;
  Account? accountUser;
  late double _paymentAmount;
  BankAccountInfo? _selectedBank;
  BankAccountInfo? _previouslySelectedBank;
  PaymentFrequency _paymentFrequency = PaymentFrequency.oneTime;
  PaymentTypeEnum paymentType = PaymentTypeEnum.none;
  String buttonLabel = OneAppString.of().textSelectPaymentType;
  static bool? showOneTimeButton;
  static bool? paymentEnabled;
  static bool? showRecurringButton;
  bool isAmountChanged = false;

  @override
  void initState() {
    super.initState();
    accountUser = widget.account;
    _paymentAmount = accountUser?.paymentAmount ?? 0.0;
    if (widget.fromNative == true) {
      loadPaymentHistory();
    }
    loadPaymentServices();
    ref.read(accountSummaryStateProvider.notifier).getAccountSummaryDetails();
  }

  Future<void> loadPaymentHistory() async {
    await ref
        .read(paymentHistoryProvider.notifier)
        .fetchPaymentHistory(fullAccountNumber: accountUser!.fullAccountNumber);
  }

  Future<void> loadPaymentServices() async {
    await ref.read(listOfBanksStateProvider.notifier).getListOfBankDetails(
        accountUser?.customer.ucid,
        selectDefaultBank: true,
        needsRefresh: true);
    final bool paymentsHasError = await ref
        .read(paymentHistoryProvider)
        .maybeMap(
            success: (value) =>
                value.paymentHistory.error != null ||
                (value.paymentHistory.recurringPaymentPlans?.hasError ?? false),
            failure: (_) => true,
            orElse: () => false);
    if (accountUser != null) {
      if (paymentsHasError) {
        await ref.read(paymentHistoryProvider.notifier).fetchPaymentHistory(
            fullAccountNumber: accountUser!.fullAccountNumber);
      }
      ref
          .read(paymentBreStateProvider.notifier)
          .getBreResponse(account: accountUser!);
    }
  }

  bool checkToCompleteSteps() {
    bool recurringFrequencySelected =
        paymentType == PaymentTypeEnum.recurring &&
            (_paymentFrequency != PaymentFrequency.oneTime &&
                _paymentFrequency != PaymentFrequency.recurring);
    return _paymentDate != null &&
        _paymentAmount > 0 &&
        _selectedBank != null &&
        (recurringFrequencySelected ||
            (paymentType == PaymentTypeEnum.oneTime));
  }

  void _accountSummaryHandler(
      AccountSummaryState? previous, AccountSummaryState? next) {
    if (next != null) {
      next.maybeWhen(
        success: (AccountSummary accountSummary, Account account) {
          setState(() {
            accountUser = account;
            _paymentAmount = accountUser?.paymentAmount ?? 0.0;
          });
        },
        orElse: () {},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (accountUser != null) {
      ref.listen<AccountSummaryState>(
          accountSummaryStateProvider, _accountSummaryHandler);
      PaymentHistoryHandler(context: context, accountUser: accountUser!)
          .paymentHistoryStateListener(ref);
    }
    return BaseScaffold(
      onBackPressed: () async {
        ref.read(listOfBanksStateProvider.notifier).resetState();
        if (widget.fromNative == true) {
          goBackToNative();
        }
      },
      isRemoveLineTop: true,
      label: OneAppString.of().textMakeAPayment,
      dismissibleTopLine: widget.fromNative == true ? false : true,
      showSwipeIcon: widget.fromNative == true ? false : true,
      action: accountUser != null
          ? IconButton(
              key: const Key('buttonPaymentHistory'),
              onPressed: () {
                FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                    VehicleAnalyticsEvent.TFS_PAYMENT_VIEW_HISTORY_CTA);
                ref.read(paymentHistoryProvider).maybeWhen(
                      success: (paymentHistory) => PaymentHistoryHandler(
                              context: context, accountUser: accountUser!)
                          .navigatePaymentHistoryScreen(
                              paymentHistory.transactions),
                      orElse: () async => await PaymentHistoryHandler(
                              context: context, accountUser: accountUser!)
                          .fetchPaymentHistory(forceLoading: true, ref: ref),
                    );
              },
              icon: TFSIcons.listIcon(),
            )
          : SizedBox(),
      body: accountUser != null
          ? SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  const SizedBox(height: 20),
                  CarInfoCardArea(
                    accountUser: accountUser!,
                    paymentMethod: paymentType,
                  ),
                  const SizedBox(height: 7.0),
                  selectPaymentWidget(),
                  const SizedBox(height: 25.0),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: <Widget>[
                        PayFromTile(
                          fromScreen: OneAppString.of().textMakeAPayment,
                          account: accountUser!,
                          selectedBank: (selectedBank) {
                            _selectedBank = selectedBank;
                            if (_previouslySelectedBank?.id !=
                                _selectedBank?.id) {
                              _previouslySelectedBank = _selectedBank;
                              SchedulerBinding.instance
                                  .addPostFrameCallback((_) {
                                checkToCompleteSteps();
                                setState(() {});
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 8),
                        PayOnTileWidget(accountUser!,
                            fromScreen: OneAppString.of().textMakeAPayment,
                            frequency: paymentType == PaymentTypeEnum.oneTime
                                ? PaymentFrequency.oneTime
                                : _paymentFrequency,
                            postDate: _paymentDate,
                            paymentType: paymentType,
                            selectedDate: (selectedDate, _frequency) {
                          _paymentDate = selectedDate;
                          _paymentFrequency = _frequency;
                          SchedulerBinding.instance.addPostFrameCallback((_) {
                            checkToCompleteSteps();
                            setState(() {});
                          });
                        }, onAmountChanged: (double amount) {
                          _paymentAmount = amount;
                          isAmountChanged = true;
                          setState(() {});
                        }, isAmountChanged: isAmountChanged),
                        const SizedBox(height: 8),
                        PaymentAmountTile(
                          paymentDate: _paymentDate,
                          selectedBank: _selectedBank,
                          account: accountUser!,
                          paymentAmount: _paymentAmount,
                          paymentFrequency: _paymentFrequency,
                          resetRecurringSelection: _resetRecurringSelection,
                          onAmountChanged: (double amount) {
                            _paymentAmount = amount;
                            isAmountChanged = true;
                            setState(() {});
                          },
                          paymentTypeEnum: paymentType,
                          isAmountChanged: isAmountChanged,
                        ),
                        TermsAndConditionsPayments(),
                        SizedBox(
                            height: MediaQuery.of(context).size.height / 6),
                      ],
                    ),
                  ),
                ],
              ),
            )
          : showGenericErrorStateWidget(),
      paddingBottomNavigation: 0,
      bottomNavigationBarWidget: accountUser != null
          ? BuildPayButton(
              selectedBank: _selectedBank,
              paymentFrequency: _paymentFrequency,
              paymentAmount: _paymentAmount,
              accountUser: accountUser!,
              checkToCompleteSteps: checkToCompleteSteps(),
              paymentDate: _paymentDate,
              resetRecurringSelection: _resetRecurringSelection,
            )
          : SizedBox(),
    );
  }

  Widget disabledPaymentTypeButton({bool? paymentIsEnabled}) {
    if (mounted) {
      setState(() {
        showOneTimeButton = null;
        showRecurringButton = null;
        paymentEnabled = paymentIsEnabled;
      });
    }
    return _frequencyPaymentButton();
  }

  Widget selectPaymentWidget() {
    return ref.watch(paymentBreStateProvider).maybeWhen(
        loaded: (PaymentBre breResponse) {
      if (breResponse.paymentEnabled) {
        return ref.watch(paymentHistoryProvider).maybeWhen(
            success: (PaymentHistory paymentHistory) {
          if (paymentHistory.error != null) {
            return disabledPaymentTypeButton();
          } else if (paymentHistory.recurringPaymentPlans?.hasError ?? false) {
            paymentEnabled = breResponse.paymentEnabled;
            showOneTimeButton = breResponse.paymentType.oneTime;
            showRecurringButton = null;
          } else {
            paymentEnabled = breResponse.paymentEnabled;
            showOneTimeButton = breResponse.paymentType.oneTime;
            showRecurringButton = breResponse.paymentType.recurring;
          }
          if (mounted) {
            setState(() {});
          }
          return _frequencyPaymentButton();
        }, orElse: () {
          return disabledPaymentTypeButton();
        });
      } else {
        return disabledPaymentTypeButton(
            paymentIsEnabled: breResponse.paymentEnabled);
      }
    }, orElse: () {
      return disabledPaymentTypeButton();
    });
  }

  Widget _frequencyPaymentButton() {
    return PaymentTypeSelectionWidget(
            context: context,
            showOneTimeButton: showOneTimeButton,
            showRecurringButton: showRecurringButton,
            onTapActionOfFrequencyButtons: (frequency, overlayEntry) =>
                onTapActionOfFrequencyButtons(frequency, overlayEntry),
            loadPaymentServices: () => loadPaymentServices(),
            buttonLabel: buttonLabel,
            paymentEnabled: paymentEnabled)
        .frequencyPaymentButton();
  }

  Future<void> setElementsWhenSwitched(PaymentFrequency frequency) async {
    setState(() {
      _paymentAmount = accountUser?.paymentAmount ?? 0.0;
      isAmountChanged = false;
      bool isOneTimeButtonSelected = frequency == PaymentFrequency.oneTime;
      paymentType = !isOneTimeButtonSelected
          ? PaymentTypeEnum.recurring
          : PaymentTypeEnum.oneTime;
      buttonLabel = !isOneTimeButtonSelected
          ? OneAppString.of().textRecurringPayment
          : OneAppString.of().textOneTimePayment;
    });
  }

  Future<void> onTapActionOfFrequencyButtons(
      PaymentFrequency frequency, OverlayEntry? overlayEntry) async {
    await setElementsWhenSwitched(frequency);
    checkToCompleteSteps();
    setState(() {});
    overlayEntry?.remove();
    overlayEntry = null;
    if (frequency == PaymentFrequency.recurring) {
      onRecurringPaymentClick();
    }
  }

  void onRecurringPaymentClick() {
    ref.read(paymentHistoryProvider).maybeWhen(
          success: (paymentHistory) {
            Transaction? transaction = paymentHistory.getRecurringPayment();
            if (accountUser != null && transaction != null) {
              PaymentFunctions(
                      context: context,
                      resetRecurringSelection: _resetRecurringSelection,
                      account: accountUser!,
                      paymentDate: _paymentDate,
                      paymentAmount: _paymentAmount,
                      paymentFrequency: _paymentFrequency,
                      selectedBank: _selectedBank,
                      ref: ref)
                  .showActiveRecurringPlan(
                      transaction: transaction,
                      accountInfo: accountUser!,
                      recurringPlans: paymentHistory.recurringPaymentPlans);
            }
          },
          orElse: () => {},
        );
  }

  void _resetRecurringSelection() {
    setState(() {
      paymentType = PaymentTypeEnum.none;
      buttonLabel = OneAppString.of().textSelectPaymentType;
      _paymentDate = null;
    });
  }
}
