// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports:
import '../domain/domain.dart';
import '../infrastructure/infrastructure.dart';
import 'default_bre_repo.dart';
import 'default_create_payment_repo.dart';
import 'default_kinto_repo.dart';
import 'default_payment_parameter_repo.dart';

final createPaymentRepoProvider = Provider<CreatePaymentRepo>(
  (ref) => DefaultCreatePaymentRepo(
    ref.watch(createPaymentApiProvider),
  ),
);

final paymentParameterRepoProvider = Provider<PaymentParameterRepo>(
  (ref) => DefaultPaymentParameterRepo(
    ref.watch(paymentParameterApiProvider),
  ),
);

final kintoRepoProvider = Provider<KintoRepo>(
  (ref) => DefaultKintoRepo(
    ref.watch(kintoApiProvider),
  ),
);

final breRepoProvider = Provider<BreRepo>(
  (ref) => DefaultBreRepo(
    ref.watch(breApiProvider),
  ),
);
