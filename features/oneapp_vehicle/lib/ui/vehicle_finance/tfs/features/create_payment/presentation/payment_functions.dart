// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';

// Project imports:
import '../../../../../../log/vehicle_analytic_event.dart';
import '../../../router/navigation_service.dart';
import '../../../router/routes.dart';
import '../../../services/locators.dart';
import '../../core/utils/tfs_helper_function.dart';
import '../../core/utils/widgets/custom_bottom_sheet.dart';
import '../../core/utils/widgets/generic_bottomsheets.dart';
import '../../core/utils/widgets/primary_button.dart';
import '../../dashboard/domain/account.dart';
import '../../handle_payment/presentation/single_transaction_details_screen.dart';
import '../../payment_history/application/application.dart';
import '../../payment_history/domain/payment_history.dart';
import '../../payment_history/domain/recurring_payment_plans.dart';
import '../../payment_methods/application/application.dart';
import '../../payment_methods/domain/bank_account_info.dart';
import '../application/application.dart';
import '../domain/payment_types.dart';
import '../domain/payment_validation_status.dart';
import 'payment_confirmation_screen.dart';
import 'widget/custom_rich_text_payment_failed.dart';

class PaymentFunctions {
  PaymentFunctions({
    required this.context,
    required this.ref,
    required this.paymentDate,
    required this.paymentAmount,
    required this.paymentFrequency,
    required this.selectedBank,
    required this.account,
    required this.resetRecurringSelection,
  });
  final BuildContext context;
  final WidgetRef ref;
  final DateTime? paymentDate;
  final double paymentAmount;
  final PaymentFrequency? paymentFrequency;
  final BankAccountInfo? selectedBank;
  final Account account;
  final Function? resetRecurringSelection;
  TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  void handleValidationStatus(PaymentValidationStatus status) {
    if (paymentFrequency == PaymentFrequency.oneTime) {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_ONETIME_PAYMENT_SWIPE_TO_PAY_FAILED);
    } else {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_RECURRING_PMT_SWIPEPAY_UNSUCCESSFUL);
    }
    status.when(
      paymentNotAllowed: () =>
          showBottomDialog(message: OneAppString.of().textPaymentNotAllowed),
      invalidAmount: () =>
          showBottomDialog(message: OneAppString.of().textSelectValidAmount),
      invalidDate: () =>
          showBottomDialog(message: OneAppString.of().textSelectValidDate),
      selectBank: () =>
          showBottomDialog(message: OneAppString.of().textSelectBank),
      selectBankAndAmount: () =>
          showBottomDialog(message: OneAppString.of().textSelectBankAndAmount),
      activeRecurringPlan: (Transaction recurringTransaction,
          RecurringPaymentPlans? _recurringPlans) {
        showActiveRecurringPlan(
            transaction: recurringTransaction,
            recurringPlans: _recurringPlans,
            accountInfo: account);
      },
      unKnown: () {
        showBottomDialogWhenPaymentFailed(context,
            title: OneAppString.of().textPaymentFailed,
            message:
                CustomRichWidgetTextFailedContent(isOnMakePaymentScreen: true));
      },
      duplicatePayment: (String kintoServiceId) async {
        final bool isUserAccepted =
            await showAcceptanceOfDuplicateDialog() ?? false;
        if (isUserAccepted) {
          getIt<TFSNavigationService>().pushNamed(Routes.processingScreen);
          if (paymentDate != null &&
              selectedBank != null &&
              paymentFrequency != null) {
            ref.read(createPaymentStateProvider.notifier).submitPayment(
                  paymentDate: paymentDate!,
                  paymentAmount: paymentAmount,
                  account: account,
                  defaultBank: selectedBank!,
                  frequency: paymentFrequency!,
                  kintoServiceId: kintoServiceId,
                  isDuplicateAccepted: isUserAccepted,
                );
          } else {
            showBottomDialogWhenPaymentFailed(context,
                title: OneAppString.of().textPaymentFailed,
                message: CustomRichWidgetTextFailedContent(
                    isOnMakePaymentScreen: true));
          }
        }
      },
    );
  }

  Future<bool?> showAcceptanceOfDuplicateDialog() {
    return CustomBottomSheet.showAndReturn<bool>(
      context,
      sizedBoxHeight: 10,
      title: OneAppString.of().textDuplicatePayment,
      body: Builder(
        builder: (BuildContext ctx) {
          return Center(
            child: Text(
              OneAppString.of().textDuplicateConfirmationDescription,
              textAlign: TextAlign.center,
              style: _textStyleUtil.callout1,
            ),
          );
        },
      ),
      secondaryButton: Builder(builder: (BuildContext ctx) {
        return TextButton(
          onPressed: () {
            Navigator.pop(context, false);
          },
          child: Text(
            OneAppString.of().textCancel,
            style:
                _textStyleUtil.callout2.copyWith(color: _colorUtil.button02a),
          ),
        );
      }),
      primaryButton: PrimaryButton(
        onTap: () => Navigator.pop(context, true),
        label: OneAppString.of().textConfirmText,
      ),
    );
  }

  void showBottomDialog({
    required String message,
    String? title,
  }) {
    GenericBottomSheets.showGenericErrorBottomSheet(context,
        title: title, error: message);
  }

  void showActiveRecurringPlan({
    required Transaction transaction,
    required RecurringPaymentPlans? recurringPlans,
    required Account accountInfo,
  }) {
    GenericBottomSheets.customizableBottomSheet(context,
        title: OneAppString.of().textRecurringPayment,
        needAFit: false,
        isDismissible: false,
        enableDrag: false,
        description: OneAppString.of().textRecurringPaymentExist,
        primaryButtonLabel: OneAppString.of().textViewPlan,
        onTapActionPrimary: () {
          FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
              VehicleAnalyticsEvent.TFS_RECURRING_PMT_PLAN_SUCCESSFUL_CTA);
          Navigator.pop(context);
          resetRecurringSelection?.call();
          getIt<TFSNavigationService>().pushNamed(
            Routes.singleTransactionDetailsScreen,
            arguments: SingleTransactionDetailsArgs(
              accountUser: accountInfo,
              paymentInfo: transaction,
              recurringPlan: recurringPlans,
              popTheScreen: false,
            ),
          );
        },
        secondaryTextLabel: OneAppString.of().textGoBack,
        onTapActionSecondary: () {
          resetRecurringSelection?.call();
          Navigator.pop(context);
        });
  }

  void paymentHandler(CreatePaymentState? previous, CreatePaymentState? next) {
    if (next != null) {
      next.maybeWhen(
          validationStatus: (PaymentValidationStatus status) {
            Navigator.pop(context);
            handleValidationStatus(status);
          },
          paymentCreated: (String paymentConfirmationID) {
            ref.read(paymentHistoryProvider.notifier).fetchPaymentHistory(
                fullAccountNumber: account.fullAccountNumber);
            ref.read(listOfBanksStateProvider.notifier).resetState();
            Navigator.pop(context);
            navigateToPaymentConfirmation(paymentConfirmationID);
          },
          orElse: () {});
    }
  }

  void navigateToPaymentConfirmation(String confirmationId) {
    if (paymentFrequency == PaymentFrequency.oneTime) {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_ONETIME_PAYMENT_SWIPE_TO_PAY_SUCCESSFUL);
    } else {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_RECURRING_PMT_SWIPE_TO_PAY_SUCCESSFUL);
    }
    if (paymentDate != null &&
        selectedBank != null &&
        paymentFrequency != null) {
      getIt<TFSNavigationService>().pushNamed(
        Routes.paymentConfirmationScreen,
        arguments: PaymentConfirmationArgs(
          fromScreen: OneAppString.of().textMakeAPayment,
          paymentDate: paymentDate!,
          paymentAmount: paymentAmount,
          account: account,
          accountNo: TFSHelperFunctions.replaceStars(
              selectedBank?.achAccountNumberMask ?? ''),
          paymentConfirmationId: confirmationId,
          frequency: paymentFrequency!,
        ),
      );
    } else {
      showBottomDialogWhenPaymentFailed(context,
          title: OneAppString.of().textPaymentFailed,
          message:
              CustomRichWidgetTextFailedContent(isOnMakePaymentScreen: true));
    }
  }
}
