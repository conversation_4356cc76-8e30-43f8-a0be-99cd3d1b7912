// Package imports:
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/payment_bre_response_dto.dart';

// Project imports:
import '../../../services/firebase_service.dart';
import '../../core/domain/api_failure.dart';
import '../../core/utils/utils.dart';
import '../domain/domain.dart';
import '../infrastructure/bre_api.dart';

class DefaultBreRepo extends BreRepo {
  DefaultBreRepo(this.api);
  final BreApi api;

  @override
  Future<Result<PaymentBre, ApiFailure>> getBreData({
    required String accountNumber,
    required String last4SSN,
  }) async {
    try {
      final CommonTfsResponse<PaymentBreResponseDTO> breResponse =
          await api.get(
        accountNumber: accountNumber,
        lastFourSSN: last4SSN,
      );

      if (breResponse.response?.response?.paymentOptions != null) {
        PaymentOptionsDTO paymentOptions =
            breResponse.response!.response!.paymentOptions!;
        PaymentBre breData = PaymentBre.fromDomain(paymentOptions);
        return Result.success(breData);
      }
      return Result.failure(ApiFailure.network(
          errorMessage: ErrorMessage.handleError(breResponse.response?.error)));
    } catch (e) {
      FirebaseService().setFireBaseNonFatalErrors(exception: e);
      return Result.failure(
          ApiFailure.network(errorMessage: ErrorMessage.handleError(e)));
    }
  }
}
