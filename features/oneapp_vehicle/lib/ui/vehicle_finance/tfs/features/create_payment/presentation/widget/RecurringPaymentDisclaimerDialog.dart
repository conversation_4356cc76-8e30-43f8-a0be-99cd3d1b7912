// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../core/utils/extensions/extensions.dart';
import '../../../core/utils/utils.dart';

class RecurringPaymentDisclaimerDialog extends StatelessWidget {
  const RecurringPaymentDisclaimerDialog({Key? key}) : super(key: key);

  static Future<bool?> show(BuildContext context) async {
    return showModalBottomSheet<bool>(
      isDismissible: false,
      enableDrag: false,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(30),
          topLeft: Radius.circular(30),
        ),
      ),
      context: context,
      builder: (BuildContext context) {
        return const RecurringPaymentDisclaimerDialog();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomBottomSheet(
      needAFit: false,
      title: OneAppString.of().textCurrentRegularlyScheduledPayment,
      icon: TFSIcons.infoBlueAsset(),
      body: Text(
        OneAppString.of().textCurrentRegularlyScheduledPaymentDescription,
        style: ThemeConfig.current()
            .textStyleUtil
            .callout1
            .withColor(ThemeConfig.current().colorUtil.tertiary05),
        textAlign: TextAlign.center,
      ),
      primaryButton: PrimaryButton(
        label: OneAppString.of().commonOK,
        onTap: () {
          Navigator.of(context).pop(true);
        },
      ),
    );
  }
}
