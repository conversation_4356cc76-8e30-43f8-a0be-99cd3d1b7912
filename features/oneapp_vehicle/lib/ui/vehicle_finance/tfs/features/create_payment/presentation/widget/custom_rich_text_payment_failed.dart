// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../../services/locators.dart';
import '../../../core/utils/urls.dart';
import '../../../core/utils/widgets/generic_bottomsheets.dart';

void showBottomDialogWhenPaymentFailed(
  BuildContext context, {
  required Widget message,
  String? title,
}) {
  GenericBottomSheets.showGenericErrorBottomSheetWithRichText(context,
      title: title,
      richText: message,
      needAFit: false,
      height: 377,
      labelPrimaryButton: OneAppString.of().textTryAgain);
}

class CustomRichWidgetTextFailedContent extends StatelessWidget {
  const CustomRichWidgetTextFailedContent(
      {Key? key,
      this.bodyText,
      this.isOnMakePaymentScreen = false,
      this.isOnEditPaymentScreen = false})
      : super(key: key);

  final bool isOnMakePaymentScreen;
  final bool isOnEditPaymentScreen;
  final String? bodyText;

  @override
  Widget build(BuildContext context) {
    final _colorUtil = ThemeConfig.current().colorUtil;
    final _textStyleUtil = ThemeConfig.current().textStyleUtil;

    final TapGestureRecognizer launchUrlPaymentOnline = TapGestureRecognizer()
      ..onTap = () {
        URLS.launchWebsiteUrl();
      };

    final TapGestureRecognizer launchPhoneDial = TapGestureRecognizer()
      ..onTap = () {
        URLS.launchPhoneCall(isBrandToyota
            ? OneAppString.of().textToyotaCallInNumber
            : OneAppString.of().textLexusCallInNumber);
      };

    final String textString = bodyText != null
        ? '$bodyText ${OneAppString.of().textPleaseVisitWebsite}'
        : '${OneAppString.of().textWeAreCurrentlyExperiencingIssues}\n' +
            (isOnMakePaymentScreen
                ? '${OneAppString.of().textPleaseTryToMakePayment}'
                : isOnEditPaymentScreen
                    ? '${OneAppString.of().textPleaseTryToEditPayment}'
                    : '');

    return RichText(
      key: Key(OneAppString.of().textWeAreCurrentlyExperiencingIssues),
      textAlign: TextAlign.center,
      text: TextSpan(
        text: textString,
        style: _textStyleUtil.callout1
            .copyWith(color: _colorUtil.tertiary05, height: 1.5),
        children: <InlineSpan>[
          textSpanWidget(
              onTap: launchUrlPaymentOnline,
              text: isBrandToyota
                  ? OneAppString.of().textWwwToyotaFinancialCom
                  : OneAppString.of().textWwwLexusFinancialCom),
          textSpanWidget(
            text: OneAppString.of().textAutomatedPhoneSystem,
            style:
                _textStyleUtil.callout1.copyWith(color: _colorUtil.tertiary05),
          ),
          textSpanWidget(
              onTap: launchPhoneDial,
              text: isBrandToyota
                  ? OneAppString.of().textToyotaPhoneDisplayNumber
                  : OneAppString.of().textLexusPhoneDisplayNumber),
        ],
      ),
    );
  }

  TextSpan textSpanWidget(
      {TextStyle? style, GestureRecognizer? onTap, required String text}) {
    final _textStyleUtil = ThemeConfig.current().textStyleUtil;
    return TextSpan(
      text: ' $text.',
      style: style ?? _textStyleUtil.callout3,
      recognizer: onTap,
    );
  }
}
