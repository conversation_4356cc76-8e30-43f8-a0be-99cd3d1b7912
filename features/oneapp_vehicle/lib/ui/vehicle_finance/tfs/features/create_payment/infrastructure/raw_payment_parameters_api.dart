// Package imports:
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/payment_parameters_request.dart';
import 'package:oa_network_impl/tfs/entity/payment_parameters_response_dto.dart';

// Project imports:
import 'payment_parameters_api.dart';

class RawPaymentParameterApi extends PaymentParameterApi {
  @override
  Future<CommonTfsResponse<PaymentParameterResponseDTO>> get({
    required String accountType,
    required double pastDueAmount,
  }) async {
    final body = PaymentParametersRequest(
        accountType: accountType, pastDueAmount: pastDueAmount);
    return await APIClients.tfsApiClient.getPaymentParameters(
      body: body,
    );
  }
}
