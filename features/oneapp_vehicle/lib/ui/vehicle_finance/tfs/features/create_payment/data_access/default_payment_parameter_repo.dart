// Package imports:
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/payment_parameters_response_dto.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../../services/firebase_service.dart';
import '../../core/domain/api_failure.dart';
import '../../core/utils/utils.dart';
import '../domain/domain.dart';
import '../infrastructure/payment_parameters_api.dart';

class DefaultPaymentParameterRepo extends PaymentParameterRepo {
  DefaultPaymentParameterRepo(this.api);

  final PaymentParameterApi api;

  @override
  Future<Result<PaymentParameter, ApiFailure>> getPaymentParameters({
    required String accountType,
    required double pastDueAmount,
  }) async {
    try {
      final CommonTfsResponse<PaymentParameterResponseDTO> parametersResponse =
          await api.get(
        accountType: accountType,
        pastDueAmount: pastDueAmount,
      );

      ResponsePaymentParameterDTO? _response =
          parametersResponse.response?.response;
      if (_response != null) {
        return Result.success(PaymentParameter.fromDomain(_response));
      }
      return Result.failure(
          ApiFailure.network(errorMessage: OneAppString.of().textServerError));
    } catch (e) {
      FirebaseService().setFireBaseNonFatalErrors(exception: e);
      return Result.failure(
          ApiFailure.network(errorMessage: ErrorMessage.handleError(e)));
    }
  }
}
