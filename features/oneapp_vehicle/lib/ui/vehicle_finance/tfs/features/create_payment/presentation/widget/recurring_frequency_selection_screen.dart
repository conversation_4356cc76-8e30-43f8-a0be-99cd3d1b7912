// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../../router/navigation_service.dart';
import '../../../../router/routes.dart';
import '../../../../services/locators.dart';
import '../../../core/utils/utils.dart';
import '../../../dashboard/domain/account.dart';
import '../../domain/payment_types.dart';
import 'custom_calendar_widget.dart';

class RecurringFrequencySelectionArgs {
  const RecurringFrequencySelectionArgs({
    required this.account,
    required this.paymentDate,
    required this.onDateSelected,
    required this.editPaymentGreyOutTodayDate,
    required this.frequency,
    this.previouslySelectedDate,
    required this.onAmountChanged,
    this.isAmountChanged,
  });
  final Account account;
  final DateTime? paymentDate;
  final Function(DateTime, PaymentFrequency) onDateSelected;
  final bool? editPaymentGreyOutTodayDate;
  final PaymentFrequency frequency;
  final DateTime? previouslySelectedDate;
  final Function(double amount)? onAmountChanged;
  final bool? isAmountChanged;
}

class RecurringFrequencySelectionScreen extends StatefulWidget {
  const RecurringFrequencySelectionScreen({Key? key, required this.args})
      : super(key: key);
  final RecurringFrequencySelectionArgs args;

  @override
  State<RecurringFrequencySelectionScreen> createState() =>
      _RecurringFrequencySelectionScreenState();
}

class _RecurringFrequencySelectionScreenState
    extends State<RecurringFrequencySelectionScreen> {
  late PaymentFrequency? frequencyChoose;
  late Color colorSelected;

  @override
  void initState() {
    super.initState();
    frequencyChoose = widget.args.frequency;
  }

  @override
  Widget build(BuildContext context) {
    final _colorUtil = ThemeConfig.current().colorUtil;
    final _textStyleUtil = ThemeConfig.current().textStyleUtil;
    colorSelected = _colorUtil.tertiary05;

    return BaseScaffold(
      label: OneAppString.of().textSelectFrequency,
      isRemoveLineTop: true,
      leading: const CustomCloseIconWidget(),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16.0),
              children: <Widget>[
                Container(
                  padding: EdgeInsetsDirectional.all(2),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    color: Colors.transparent,
                  ),
                  child: CustomTile(
                      title: OneAppString.of().textWeekly,
                      subtitle: Text(
                        OneAppString.of().textWeeklyDesc,
                        style: _textStyleUtil.body1
                            .copyWith(color: _colorUtil.tertiary05),
                      ),
                      trailing: frequencyChoose == PaymentFrequency.weekly
                          ? TFSIcons.statusIcon()
                          : SizedBox(),
                      onTap: () => {
                            FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                                VehicleAnalyticsEvent
                                    .TFS_RECURRING_PMT_FREQUENCY_WEEKLY),
                            navigateToDateSelection(PaymentFrequency.weekly)
                          }),
                ),
                const SizedBox(height: 8.0),
                Container(
                  padding: EdgeInsetsDirectional.all(2),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    color: Colors.transparent,
                  ),
                  child: CustomTile(
                      title: OneAppString.of().textBiWeekly,
                      subtitle: Text(
                        OneAppString.of().textBiWeeklyDesc,
                        style: _textStyleUtil.body1
                            .copyWith(color: _colorUtil.tertiary05),
                      ),
                      trailing: frequencyChoose == PaymentFrequency.biWeekly
                          ? TFSIcons.statusIcon()
                          : SizedBox(),
                      onTap: () => {
                            FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                                VehicleAnalyticsEvent
                                    .TFS_RECURRING_PMT_FREQUENCY_BI_WEEKLY),
                            navigateToDateSelection(PaymentFrequency.biWeekly)
                          }),
                ),
                const SizedBox(height: 8.0),
                Container(
                  padding: EdgeInsetsDirectional.all(2),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    color: Colors.transparent,
                  ),
                  child: CustomTile(
                      title: OneAppString.of().textMonthly,
                      subtitle: Text(
                        OneAppString.of().textMonthlyDesc,
                        style: _textStyleUtil.body1
                            .copyWith(color: _colorUtil.tertiary05),
                      ),
                      trailing: frequencyChoose == PaymentFrequency.monthly
                          ? TFSIcons.statusIcon()
                          : SizedBox(),
                      onTap: () => {
                            FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                                VehicleAnalyticsEvent
                                    .TFS_RECURRING_PMT_FREQUENCY_MONTHLY),
                            navigateToDateSelection(PaymentFrequency.monthly)
                          }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void navigateToDateSelection(PaymentFrequency frequency) async {
    Navigator.pop(context);
    getIt<TFSNavigationService>().pushNamed(
      Routes.customCalendarWidget,
      arguments: CustomCalendarArgs(
          account: widget.args.account,
          postedDate: widget.args.previouslySelectedDate,
          frequency: frequency,
          onDateSelected: (datePayment, frequency) {
            widget.args.onDateSelected(datePayment, frequency);
          },
          onAmountChanged: (amount) {
            if (widget.args.onAmountChanged != null) {
              widget.args.onAmountChanged!(amount);
            }
          },
          greyOutTodayDate: widget.args.editPaymentGreyOutTodayDate ?? false,
          previouslySelectedDate: widget.args.previouslySelectedDate,
          isAmountChanged: widget.args.isAmountChanged),
    );
  }
}
