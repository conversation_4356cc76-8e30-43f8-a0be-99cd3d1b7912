// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:oa_network_impl/tfs/entity/payment_parameters_response_dto.dart';

// Project imports:
import '../../core/utils/extensions/datetime_extensions.dart';
import '../../core/utils/utils.dart';

part 'payment_parameter.freezed.dart';

@freezed
class PaymentParameter with _$PaymentParameter {
  const factory PaymentParameter({
    required DateTime paymentDate,
    required List<String> bankHolidayList,
    required String maxOnetimeDate,
  }) = _PaymentParameter;

  const PaymentParameter._();

  factory PaymentParameter.fromDomain(ResponsePaymentParameterDTO response) {
    return PaymentParameter(
      paymentDate:
          DateFormatterUtil.formatStringDateWithDashesPatternInDateTime(
                  response.asapOnetimeDate) ??
              DateFormatterUtil.getNextAvailableDay(),
      maxOnetimeDate: response.maxOnetimeDate,
      bankHolidayList: response.bankHolidays ?? <String>[],
    );
  }

  List<DateTime> get holidayList {
    final List<DateTime> currentYearHolidayDateList = [];
    for (final String holiday in bankHolidayList) {
      final DateTime dateTime =
          DateFormatterUtil.paymentsApiDateFormat(holiday);
      currentYearHolidayDateList.add(dateTime.utcDate());
    }
    return currentYearHolidayDateList;
  }

  DateTime get calendarLastDay {
    final kToday = DateFormatterUtil.getTodayTimeInCST();
    final calendarLastDay =
        DateTime.utc(kToday.year, kToday.month + 4, kToday.day);
    if (maxOnetimeDate.isNotEmpty) {
      return DateFormatterUtil.formatStringDateWithDashesPatternInDateTime(
              maxOnetimeDate) ??
          calendarLastDay;
    } else {
      return calendarLastDay;
    }
  }

  DateTime nextAvailableDay({bool? includeWeekends}) {
    final DateTime kToday = DateFormatterUtil.getTodayTimeInCST();
    final List<DateTime> dates =
        _getListOfNextSevenWorkingDays(includeWeekends: includeWeekends);
    return dates.isNotEmpty
        ? dates.first
        : DateTime.utc(kToday.year, kToday.month, kToday.day + 1);
  }

  List<DateTime> _getListOfNextSevenWorkingDays({bool? includeWeekends}) {
    includeWeekends = includeWeekends ?? false;
    final DateTime kToday = DateFormatterUtil.getTodayTimeInCST();
    final List<DateTime> listOfDates = <DateTime>[];
    List<DateTime> monthEndDaysList = DateFormatterUtil.monthLast3Days();
    for (int i = 0; i < 7; i++) {
      final DateTime date =
          DateTime.utc(kToday.year, kToday.month, kToday.day + i);
      final bool isWeekDay = !(date.weekday == DateTime.sunday ||
          date.weekday == DateTime.saturday);
      final bool isDateIsMonthEnd =
          includeWeekends && monthEndDaysList.contains(date);
      if (!isDateIsMonthEnd && (isWeekDay || includeWeekends) && i > 0) {
        if (!(holidayList.contains(date))) {
          listOfDates.add(date);
        }
      }
    }
    if (listOfDates.isNotEmpty) {
      listOfDates.sort();
    }
    return listOfDates;
  }
}
