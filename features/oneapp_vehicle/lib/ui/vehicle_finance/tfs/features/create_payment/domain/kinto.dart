// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:oa_network_impl/tfs/entity/payment_kinto_id_response_dto.dart';

// Project imports:
import '../../core/utils/tfs_helper_function.dart';

part 'kinto.freezed.dart';

@freezed
class Kinto with _$Kinto {
  const factory Kinto({
    required String serviceId,
    required String ssn,
  }) = _Kinto;
  factory Kinto.getKintoResponse(String upid, List<CustomerDTO>? customer) {
    if (customer != null) {
      final CustomerDTO? cust = customer.firstWhere((e) => e.upid == upid);
      final service =
          cust?.service?.firstWhere((e) => e.serviceTyp == 'Kinto Payment');
      return Kinto(
        serviceId: service?.serviceId ?? '',
        ssn: TFSHelperFunctions.extractLast4Digits(cust?.idVal),
      );
    }
    return const Kinto(
      serviceId: '',
      ssn: '',
    );
  }
}
