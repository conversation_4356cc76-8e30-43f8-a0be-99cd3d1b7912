// Dart imports:
import 'dart:io';

// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';

// Project imports:
import '../../../../cs_self_service_payment_flutter.dart';
import '../../../core/utils/tfs_theme_constants.dart';
import '../../../core/utils/utils.dart';
import '../../../core/utils/widgets/launch_url_or_pdf_widget.dart';
import '../../../dashboard/application/account_summary_use_cases.dart';
import 'amount_entry_screen.dart';
import 'custom_amount_textfield.dart';

class RecurringPaymentAmountEntryScreen extends ConsumerStatefulWidget {
  const RecurringPaymentAmountEntryScreen({required this.args});

  final AmountEntryArgs args;

  @override
  RecurringPaymentAmountEntryScreenState createState() =>
      RecurringPaymentAmountEntryScreenState();
}

class RecurringPaymentAmountEntryScreenState
    extends ConsumerState<RecurringPaymentAmountEntryScreen> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;

  bool _isValidAmount = false;

  late AccountSummaryUseCases accountSummaryUseCases;

  final GlobalKey<FormState> _formKey = GlobalKey();
  late ColorUtil _colorUtil;
  late TextStyleUtil _textStyleUtil;
  int selectedValue = -1;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: '0.00');
    _focusNode = FocusNode();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _formKey.currentState!.validate();
    });
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _colorUtil = ThemeConfig.current().colorUtil;
    _textStyleUtil = ThemeConfig.current().textStyleUtil;
    final double maxPaymentAmount =
        double.tryParse(widget.args.account.payoffAmount) ?? 9999.99;
    return BaseScaffold(
        isRemoveLineTop: true,
        label: OneAppString.of().textPaymentOptions,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 35),
            child: Column(children: <Widget>[
              Expanded(
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    onChanged: () {
                      if (_formKey.currentState!.validate()) {
                        setState(() => _isValidAmount = true);
                      } else {
                        setState(() => _isValidAmount = false);
                      }
                    },
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Container(
                              height: 90,
                              decoration: BoxDecoration(
                                  color: _colorUtil.tile05,
                                  borderRadius: borderCircularRadius),
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: ListTile(
                                title: Text(
                                    OneAppString.of()
                                        .textCurrentRegularlyScheduledPayment,
                                    style: _textStyleUtil.body4),
                                subtitle: Text(
                                    TFSHelperFunctions.currencyFormat(
                                        widget.args.account.paymentAmount),
                                    style: _textStyleUtil.callout1),
                                leading: Radio<int>(
                                  value: 0,
                                  groupValue: selectedValue,
                                  activeColor: _colorUtil.tertiary00,
                                  // Change the active radio button color here
                                  fillColor: MaterialStateProperty.all(
                                      _colorUtil.tertiary00),
                                  // Change the fill color when selected
                                  splashRadius: 30,
                                  // Change the splash radius when clicked
                                  onChanged: (int? value) {
                                    setState(() {
                                      selectedValue = value!;
                                    });
                                  },
                                ),
                              )),
                          Visibility(
                            visible: selectedValue == 0,
                            child: RegularPaymentExpandView(),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            height: 80,
                            decoration: BoxDecoration(
                                color: _colorUtil.tile05,
                                borderRadius: borderCircularRadius),
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: ListTile(
                              title: Text(
                                  OneAppString.of().textEnterFixedAmount,
                                  style: _textStyleUtil.body4),
                              subtitle: Text(
                                  OneAppString.of().textMinimumAmount,
                                  style: _textStyleUtil.callout1),
                              leading: Radio<int>(
                                value: 1,
                                groupValue: selectedValue,
                                activeColor: _colorUtil.tertiary00,
                                // Change the active radio button color here
                                fillColor: MaterialStateProperty.all(
                                    _colorUtil.tertiary00),
                                // Change the fill color when selected
                                splashRadius: 30,
                                // Change the splash radius when clicked
                                onChanged: (int? value) {
                                  setState(() {
                                    selectedValue = value!;
                                  });
                                },
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Visibility(
                            visible: selectedValue == 1,
                            child: SizedBox(
                                height: 60,
                                child: CustomAmountTextField(
                                  controller: _controller,
                                  focusNode: _focusNode,
                                  isShowSuffixIcon:
                                      double.tryParse(_controller.text)! >=
                                              5.0 &&
                                          _isValidAmount,
                                  errorStyle: _textStyleUtil.body1
                                      .copyWith(height: 0)
                                      .copyWith(color: Colors.red),
                                  validator: (String? val) {
                                    final double parsedAmount =
                                        double.parse(val ?? '0');
                                    if (_focusNode.hasFocus &&
                                        (parsedAmount < 5.00 ||
                                            parsedAmount > maxPaymentAmount)) {
                                      return '';
                                    } else {
                                      return null;
                                    }
                                  },
                                )),
                          ),
                          if (!_isValidAmount &&
                              _focusNode.hasFocus) ...<Widget>{
                            FittedBox(
                              child: Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: Text(
                                    OneAppString.of().textCustomAmountError(
                                        TFSHelperFunctions.currencyFormat(
                                            maxPaymentAmount)),
                                    style: _textStyleUtil.callout1.copyWith(
                                        color: _colorUtil.error01,
                                        fontSize: isBrandToyota ? 14 : 14.5)),
                              ),
                            ),
                          },
                          Visibility(
                            visible: selectedValue == 1,
                            child: Padding(
                              padding:
                                  const EdgeInsets.only(top: 20.0, left: 5.0),
                              child: FittedBox(
                                  child: Row(children: [
                                Text(
                                  '${OneAppString.of().textCurrentBalance} : ${TFSHelperFunctions.currencyFormat(widget.args.account.paymentAmount)}',
                                  style: _textStyleUtil.callout1.copyWith(
                                      color: _colorUtil.tertiary05,
                                      fontSize: isBrandToyota ? 14 : 14.5),
                                ),
                                const SizedBox(width: 8),
                                InkWell(
                                  key: const Key('amount_info_breakdown'),
                                  onTap: () {
                                    if (widget.args.fromScreen ==
                                        OneAppString.of().textEditPayment) {
                                      FireBaseAnalyticsLogger
                                          .logTFSPaymentGroupEvent(
                                              VehicleAnalyticsEvent
                                                  .TFS_EDIT_PAYMENT_TOOLTIP_AMOUNT);
                                    } else if (widget.args.fromScreen ==
                                        OneAppString.of().textMakeAPayment) {
                                      FireBaseAnalyticsLogger
                                          .logTFSPaymentGroupEvent(
                                              VehicleAnalyticsEvent
                                                  .TFS_PAYMENT_TOOLTIP_AMOUNT);
                                    }
                                    GenericBottomSheets
                                        .showGenericInfoBottomSheet(
                                      context,
                                      needFit: false,
                                      height: 377,
                                      icon: TFSIcons.paymentInfoIconWidget(),
                                      infoWidget: Padding(
                                          padding:
                                              const EdgeInsets.only(top: 5),
                                          child: Column(children: [
                                            rowWithDescriptionAndAmount(
                                                description: widget.args.account
                                                            .contractType
                                                            .toUpperCase() ==
                                                        'R'
                                                    ? OneAppString.of()
                                                        .textPrincipalAndInterest
                                                    : OneAppString.of()
                                                        .textLeasePayment,
                                                amount: double.tryParse(widget
                                                    .args
                                                    .account
                                                    .monthlyPaymentAmount)),
                                            const SizedBox(
                                              height: 20,
                                            ),
                                            rowWithDescriptionAndAmount(
                                                description: OneAppString.of()
                                                    .textLateFees,
                                                amount: widget
                                                    .args.account.lateCharges),
                                            const SizedBox(
                                              height: 20,
                                            ),
                                            rowWithDescriptionAndAmount(
                                                description:
                                                    OneAppString.of().textMisc,
                                                amount: widget
                                                    .args.account.miscCharges),
                                            const SizedBox(
                                              height: 20,
                                            ),
                                            rowWithDescriptionAndAmount(
                                                description: OneAppString.of()
                                                    .textPastDue,
                                                amount: widget.args.account
                                                    .pastDueAmount),
                                          ])),
                                      title: OneAppString.of()
                                          .textPaymentBreakdown,
                                    );
                                  },
                                  child: TFSIcons.infoIconWidget(),
                                ),
                              ])),
                            ),
                          ),
                        ]),
                  ),
                ),
              ),
              Column(children: <Widget>[
                if (MediaQuery.of(context).viewInsets.bottom == 0)
                  Column(
                    children: <Widget>[
                      const SizedBox(height: 30),
                      Semantics(
                        container: true,
                        child: PrimaryButton(
                          label: OneAppString.of().textComplete,
                          isDisabled: completeButtonDisableState(),
                          onTap: () {
                            if (widget.args.fromScreen ==
                                OneAppString.of().textEditPayment) {
                              FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                                  VehicleAnalyticsEvent
                                      .TFS_EDIT_PAYMENT_ENTER_AMOUNT_CONTINUE_CTA);
                            } else if (widget.args.fromScreen ==
                                OneAppString.of().textMakeAPayment) {
                              FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                                  VehicleAnalyticsEvent
                                      .TFS_PAYMENT_ENTER_AMOUNT_CONTINUE_CTA);
                            }
                            double amount = double.parse(_controller.text);

                            if (selectedValue == 0) {
                              amount = widget.args.account.paymentAmount;
                            }
                            widget.args.onAmountChanged(amount);
                            if (widget.args.selectedDate != null) {
                              widget.args.selectedDate!(widget.args.paymentDate,
                                  widget.args.frequency);
                            }
                            Navigator.of(context).pop();
                          },
                        ),
                      ),
                    ],
                  ),
              ]),
              if (_focusNode.hasFocus && Platform.isIOS) ...<Widget>{
                const SizedBox(height: 16.0),
              }
            ]),
          ),
        ));
  }

  Widget rowWithDescriptionAndAmount(
      {required String description, double? amount}) {
    return Builder(builder: (BuildContext context) {
      return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text('$description :',
            style:
                _textStyleUtil.callout1.copyWith(color: _colorUtil.tertiary05)),
        const Spacer(),
        Text(TFSHelperFunctions.currencyFormat(amount),
            style: _textStyleUtil.callout2.copyWith(
                color: isDarkTheme()
                    ? _colorUtil.tertiary03
                    : _colorUtil.tertiary05)),
      ]);
    });
  }

  bool completeButtonDisableState() {
    bool completeButtonStatus = true;
    if (selectedValue == 0) {
      completeButtonStatus = false;
    } else if (selectedValue == 1 && _isValidAmount) {
      completeButtonStatus = false;
    }
    return completeButtonStatus;
  }

  Widget RegularPaymentExpandView() {
    final _colorUtil = ThemeConfig.current().colorUtil;
    final _textStyleUtil = ThemeConfig.current().textStyleUtil;
    return Builder(builder: (BuildContext context) {
      return RichText(
        textAlign: TextAlign.start,
        text: TextSpan(
          children: <TextSpan>[
            TextSpan(
              text: OneAppString.of()
                  .textCurrentRegularlyPaymentDetailDescription1,
              style: _textStyleUtil.callout1
                  .copyWith(color: _colorUtil.tertiary05, letterSpacing: 0.5),
            ),
            TextSpan(
              text: OneAppString.of().textWillNotVary,
              style: _textStyleUtil.textLink1.copyWith(letterSpacing: 1),
            ),
            TextSpan(
              text: OneAppString.of()
                  .textCurrentRegularlyPaymentDetailDescription3,
              style: _textStyleUtil.callout1
                  .copyWith(color: _colorUtil.tertiary05, letterSpacing: 0.5),
            ),
            TextSpan(
              recognizer: TapGestureRecognizer()
                ..onTap = () =>
                    launchUrlOrPdfWidget.onlinePoliciesAndAgreementPDF(context),
              text: OneAppString.of().textPaymentTermsAndCondition.substring(
                  0, OneAppString.of().textPaymentTermsAndCondition.length - 1),
              style: _textStyleUtil.textLink1.copyWith(letterSpacing: 1),
            ),
            TextSpan(
              text: OneAppString.of()
                  .textCurrentRegularlyPaymentDetailDescription2,
              style: _textStyleUtil.callout1
                  .copyWith(color: _colorUtil.tertiary05, letterSpacing: 0.5),
            ),
          ],
        ),
      );
    });
  }
}
