// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports:
import '../data_access/data_access.dart';
import 'create_payment_logic.dart';
import 'create_payment_state.dart';
import 'create_payment_use_cases.dart';
import 'payment_bre_logic.dart';
import 'payment_bre_state.dart';
import 'payment_bre_use_cases.dart';
import 'payment_parameter_logic.dart';
import 'payment_parameter_state.dart';
import 'payment_parameter_usecase.dart';

export 'create_payment_state.dart';

final createPaymentStateProvider =
    StateNotifierProvider<CreatePaymentUseCases, CreatePaymentState>(
  (ref) => CreatePaymentLogic(
    createPaymentRepo: ref.watch(createPaymentRepoProvider),
    kintoRepo: ref.watch(kintoRepoProvider),
    breRepo: ref.watch(breRepoProvider),
    ref: ref,
  ),
);

final paymentParameterStateProvider =
    StateNotifierProvider<PaymentParameterUsecase, PaymentParameterState>(
  (ref) => PaymentParameterLogic(
    ref.watch(paymentParameterRepoProvider),
  ),
);

final paymentBreStateProvider =
    StateNotifierProvider<PaymentBreUseCase, PaymentBreState>(
  (ref) => PaymentBreLogic(
    kintoRepo: ref.watch(kintoRepoProvider),
    breRepo: ref.watch(breRepoProvider),
  ),
);
