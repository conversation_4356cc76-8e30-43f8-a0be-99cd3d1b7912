// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_request.freezed.dart';

@freezed
class PaymentRequest with _$PaymentRequest {
  const factory PaymentRequest({
    required String upid,
    required String paymentAmount,
    required String accountNumber,
    required String accountType,
    required String paymentMethod,
    required String paymentMethodId,
    required String startDate,
    required String accountId,
    required String achAccountType,
    required String achAccountNumberMask,
    required String achAbaCode,
    required String frequency,
    required String paymentType,
    required String comments,
    required bool processPayment,
    required String kintoServiceId,
    String? creditCardExpiration,
    String? creditCardType,
    String? creditCardMaskNumber,
    String? endDate,
    required bool allowDuplicate,
  }) = _PaymentRequest;
}

const String regularPayment = 'Regular';
const String oneTimePayment = 'One-Time';
const String achChannel = 'ACH';
