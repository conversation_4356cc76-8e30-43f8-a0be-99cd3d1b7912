// Package imports:
import 'package:oneapp_common/theme/string_util.dart';

enum PaymentTypeEnum { oneTime, recurring, none }

enum PaymentFrequency { oneTime, recurring, weekly, biWeekly, monthly }

extension PaymentFrequencyStatus on PaymentFrequency {
  String get name {
    switch (this) {
      case PaymentFrequency.oneTime:
        return OneAppString.of().textOneTime;

      case PaymentFrequency.weekly:
        return OneAppString.of().textWeekly;

      case PaymentFrequency.biWeekly:
        return OneAppString.of().textBiWeekly;

      case PaymentFrequency.monthly:
        return OneAppString.of().textMonthly;

      default:
        return OneAppString.of().textOneTime;
    }
  }
}

extension FrequencyStatus on String? {
  PaymentFrequency get type {
    switch (this) {
      case 'Weekly':
        return PaymentFrequency.weekly;

      case 'Bi-Weekly':
        return PaymentFrequency.biWeekly;

      case 'BiWeekly':
        return PaymentFrequency.biWeekly;

      case 'Monthly':
        return PaymentFrequency.monthly;

      case 'One-Time':
        return PaymentFrequency.oneTime;

      default:
        return PaymentFrequency.recurring;
    }
  }
}
