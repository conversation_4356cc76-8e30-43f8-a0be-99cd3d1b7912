// Flutter imports:
// ignore_for_file: unused_result

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../../../router/navigation_service.dart';
import '../../../../router/routes.dart';
import '../../../../services/locators.dart';
import '../../../core/utils/tfs_helper_function.dart';
import '../../../core/utils/widgets/swipe_to_pay.dart';
import '../../../dashboard/domain/account.dart';
import '../../../payment_methods/domain/bank_account_info.dart';
import '../../application/application.dart';
import '../../domain/payment_types.dart';
import '../payment_functions.dart';

class BuildPayButton extends ConsumerWidget {
  const BuildPayButton({
    Key? key,
    required this.paymentDate,
    required this.paymentAmount,
    required this.accountUser,
    required this.selectedBank,
    required this.paymentFrequency,
    required this.checkToCompleteSteps,
    required this.resetRecurringSelection,
  }) : super(key: key);
  final DateTime? paymentDate;
  final double paymentAmount;
  final Account accountUser;
  final BankAccountInfo? selectedBank;
  final PaymentFrequency paymentFrequency;
  final bool checkToCompleteSteps;
  final Function? resetRecurringSelection;

  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<CreatePaymentState>(
        createPaymentStateProvider,
        PaymentFunctions(
                context: context,
                resetRecurringSelection: resetRecurringSelection,
                account: accountUser,
                paymentDate: paymentDate,
                paymentAmount: paymentAmount,
                paymentFrequency: paymentFrequency,
                selectedBank: selectedBank,
                ref: ref)
            .paymentHandler);

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Semantics(
        container: true,
        child: SwipeToPay(
          dontAllowSwipe: !checkToCompleteSteps,
          label: checkToCompleteSteps
              ? '${OneAppString.of().textSwipeToConfirm}  ${TFSHelperFunctions.currencyFormat(paymentAmount)}'
              : OneAppString.of().textCompleteStepsAbove,
          onSwipeComplete: () async {
            getIt<TFSNavigationService>().pushNamed(Routes.processingScreen);
            ref.refresh<CreatePaymentState>(createPaymentStateProvider);
            ref.read(createPaymentStateProvider.notifier).initiatePayment(
                  paymentDate: paymentDate,
                  paymentAmount: paymentAmount,
                  account: accountUser,
                  defaultBank: selectedBank,
                  frequency: paymentFrequency,
                );
          },
          amount: paymentAmount,
        ),
      ),
    );
  }
}
