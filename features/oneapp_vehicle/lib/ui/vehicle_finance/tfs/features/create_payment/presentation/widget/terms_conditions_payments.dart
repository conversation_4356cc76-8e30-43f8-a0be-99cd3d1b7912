// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../core/utils/widgets/launch_url_or_pdf_widget.dart';

class TermsAndConditionsPayments extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final _colorUtil = ThemeConfig.current().colorUtil;
    final _textStyleUtil = ThemeConfig.current().textStyleUtil;
    return Padding(
      padding: const EdgeInsets.fromLTRB(12.0, 20, 12.0, 10),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          children: <TextSpan>[
            TextSpan(
              text: OneAppString.of().textSwipeToPayConfirmationContent,
              style: _textStyleUtil.callout1
                  .copyWith(color: _colorUtil.tertiary05, letterSpacing: 0.5),
            ),
            TextSpan(
              recognizer: TapGestureRecognizer()
                ..onTap = () =>
                    launchUrlOrPdfWidget.onlinePoliciesAndAgreementPDF(context),
              text: OneAppString.of().textPaymentTermsAndCondition,
              style: _textStyleUtil.textLink1.copyWith(letterSpacing: 1),
            )
          ],
        ),
      ),
    );
  }
}
