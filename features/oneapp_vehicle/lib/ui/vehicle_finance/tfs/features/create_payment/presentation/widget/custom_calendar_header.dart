// Flutter imports:
import 'package:flutter/widgets.dart';

// Package imports:
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../core/utils/tfs_theme_constants.dart';
import '../../../core/utils/utils.dart';

class CustomCalendarHeader extends StatelessWidget {
  const CustomCalendarHeader({
    Key? key,
    required this.focusedMonth,
    required this.leftChevronIcon,
    required this.rightChevronIcon,
    required this.onTapLeftChevron,
    required this.onTapRightChevron,
    this.leftChevronVisible = true,
    this.rightChevronVisible = true,
  }) : super(key: key);
  final DateTime focusedMonth;
  final VoidCallback onTapLeftChevron;
  final VoidCallback onTapRightChevron;
  final bool leftChevronVisible;
  final bool rightChevronVisible;
  final Widget leftChevronIcon;
  final Widget rightChevronIcon;

  @override
  Widget build(BuildContext context) {
    final _colorUtil = ThemeConfig.current().colorUtil;
    final _textStyleUtil = ThemeConfig.current().textStyleUtil;
    return Container(
      decoration: BoxDecoration(
        borderRadius: borderCircularRadius,
        color: _colorUtil.tile02,
      ),
      margin: const EdgeInsets.only(left: 16.0, right: 16.0),
      padding: const EdgeInsets.only(top: 10, bottom: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Padding(
              padding: const EdgeInsets.only(left: 15),
              child: CustomTextWidget(
                DateFormatterUtil.formatStringDateToFullMonthAndYear(
                    focusedMonth),
                style: _textStyleUtil.subHeadline1,
                color: _colorUtil.tertiary03,
              )),
          Padding(
            padding: const EdgeInsets.only(right: 5, bottom: 13),
            child: Row(children: <Widget>[
              if (leftChevronVisible)
                CustomTFSIconButton(
                  icon: leftChevronIcon,
                  onTap: onTapLeftChevron,
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  padding: const EdgeInsets.all(12.0),
                ),
              SizedBox(width: MediaQuery.of(context).size.width / 25),
              if (rightChevronVisible)
                CustomTFSIconButton(
                  icon: rightChevronIcon,
                  onTap: onTapRightChevron,
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  padding: const EdgeInsets.all(12.0),
                ),
            ]),
          )
        ],
      ),
    );
  }
}
