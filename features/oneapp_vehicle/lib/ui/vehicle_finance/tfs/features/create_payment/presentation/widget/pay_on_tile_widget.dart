// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../../cs_self_service_payment_flutter.dart';
import '../../../../router/navigation_service.dart';
import '../../../../router/routes.dart';
import '../../../core/utils/extensions/extensions.dart';
import '../../../core/utils/utils.dart';
import '../../../dashboard/domain/account.dart';
import '../../application/application.dart';
import '../../domain/payment_types.dart';
import 'custom_calendar_widget.dart';
import 'payment_tile_with_details_widget.dart';
import 'recurring_frequency_selection_screen.dart';

class PayOnTileWidget extends ConsumerStatefulWidget {
  const PayOnTileWidget(
    this.account, {
    this.postDate,
    this.greyOutTodayDate = false,
    required this.selectedDate,
    this.frequency = PaymentFrequency.oneTime,
    this.paymentType = PaymentTypeEnum.oneTime,
    this.fromScreen,
    this.onAmountChanged,
    this.isAmountChanged,
    Key? key,
  }) : super(key: key);
  final Account account;
  final DateTime? postDate;
  final bool greyOutTodayDate;
  final Function(DateTime?, PaymentFrequency) selectedDate;
  final String? fromScreen;
  final PaymentFrequency frequency;
  final PaymentTypeEnum paymentType;
  final Function(double amount)? onAmountChanged;
  final bool? isAmountChanged;
  @override
  PayOnTileWidgetState createState() => PayOnTileWidgetState();
}

class PayOnTileWidgetState extends ConsumerState<PayOnTileWidget> {
  DateTime? _previouslySelectedDate;

  late PaymentFrequency frequency;
  late PaymentTypeEnum paymentType;
  DateTime? displayDate;

  @override
  void initState() {
    super.initState();
    _previouslySelectedDate = null;
    displayDate = widget.postDate;
    frequency = widget.frequency;
    paymentType = widget.paymentType;
    ref.read(paymentParameterStateProvider.notifier).getPaymentParameters(
          account: widget.account,
        );
  }

  @override
  void didUpdateWidget(PayOnTileWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    frequency = widget.frequency;
    paymentType = widget.paymentType;
    if (widget.paymentType != oldWidget.paymentType) {
      displayDate = oldWidget.paymentType == PaymentTypeEnum.none
          ? widget.postDate
          : null;
      _previouslySelectedDate = null;
      widget.selectedDate(displayDate, frequency);
    }
  }

  String getPayOnTitle() {
    return widget.paymentType == PaymentTypeEnum.recurring
        ? OneAppString.of().textSchedule
        : OneAppString.of().textDate;
  }

  @override
  Widget build(BuildContext context) {
    return CustomTile(
      title: getPayOnTitle(),
      subtitle: PaymentTileWithDetailsWidget(
        getPayOnTitle(),
        <Widget>[
          Row(
            children: <Widget>[
              buildPayOnDateWidget(),
            ],
          ),
        ],
      ),
      onTap: widget.paymentType == PaymentTypeEnum.recurring
          ? () => _navigateToFrequencyScreen()
          : () => _navigateToCalendarWidget(),
    );
  }

  void _navigateToCalendarWidget() {
    if (widget.fromScreen == OneAppString.of().textEditPayment) {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_EDIT_PAYMENT_DATE);
    } else if (widget.fromScreen == OneAppString.of().textMakeAPayment) {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_PAYMENT_DATE);
    }
    getIt<TFSNavigationService>().pushNamed(
      Routes.customCalendarWidget,
      arguments: CustomCalendarArgs(
          account: widget.account,
          greyOutTodayDate:
              isPaymentTypeIsRecurring() || widget.greyOutTodayDate,
          fromScreen: widget.fromScreen,
          frequency: isPaymentTypeIsRecurring()
              ? widget.frequency
              : PaymentFrequency.oneTime,
          previouslySelectedDate:
              displayDate == null ? null : _previouslySelectedDate,
          postedDate: displayDate,
          onDateSelected: (DateTime? date, _frequency) {
            if (date != null) {
              setState(() {
                displayDate = date;
                frequency = _frequency;
                widget.selectedDate(date, frequency);
                _previouslySelectedDate = date;
              });
            }
          },
          onAmountChanged: (amount) {
            setState(() {
              if (widget.onAmountChanged != null) {
                widget.onAmountChanged!(amount);
              }
            });
          },
          isAmountChanged: widget.isAmountChanged),
    );
  }

  void _navigateToFrequencyScreen() {
    FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
        VehicleAnalyticsEvent.TFS_PAYMENT_FREQUENCY);

    getIt<TFSNavigationService>().pushNamed(
      Routes.recurringFrequencySelectionScreen,
      arguments: RecurringFrequencySelectionArgs(
          account: widget.account,
          frequency: widget.frequency,
          editPaymentGreyOutTodayDate:
              isPaymentTypeIsRecurring() || widget.greyOutTodayDate,
          paymentDate: displayDate,
          previouslySelectedDate:
              displayDate == null ? null : _previouslySelectedDate,
          onDateSelected: (DateTime? date, _frequency) {
            if (date != null) {
              setState(() {
                displayDate = date;
                frequency = _frequency;
                _previouslySelectedDate = date;
                widget.selectedDate(date, _frequency);
              });
            }
          },
          onAmountChanged: (double amount) {
            setState(() {
              if (widget.onAmountChanged != null) {
                widget.onAmountChanged!(amount);
              }
            });
          },
          isAmountChanged: widget.isAmountChanged),
    );
  }

  Widget buildPayOnDateWidget() {
    String? _displayDate = displayDate == null
        ? ''
        : '${DateFormatterUtil.formatStringDateToFullMonthDateYear(displayDate!)} ${OneAppString.of().textSemanticAction}';
    return Semantics(
      label: _displayDate,
      excludeSemantics: true,
      child: Text(
        getPaymentDateText(widget.frequency),
        style: ThemeConfig.current()
            .textStyleUtil
            .callout1
            .withColor(ThemeConfig.current().colorUtil.tertiary05),
      ),
    );
  }

  bool isPaymentTypeIsRecurring() =>
      widget.paymentType == PaymentTypeEnum.recurring;

  String getPaymentDateText(PaymentFrequency frequency) {
    if (displayDate == null) {
      if (!isPaymentTypeIsRecurring()) {
        return OneAppString.of().textSelectDate;
      } else {
        return OneAppString.of().textSelectDateAndFrequency;
      }
    } else if (isPaymentTypeIsRecurring()) {
      final _displayDateForRecurring =
          DateFormatterUtil.formatStringDateToDayAndMonth(displayDate!);
      switch (frequency) {
        case PaymentFrequency.recurring:
          return OneAppString.of().textSelectDateAndFrequency;

        case PaymentFrequency.weekly:
          return '$_displayDateForRecurring, ${OneAppString.of().textWeekly}';

        case PaymentFrequency.biWeekly:
          return '$_displayDateForRecurring, ${OneAppString.of().textBiWeekly}';

        case PaymentFrequency.monthly:
          return '$_displayDateForRecurring, ${OneAppString.of().textMonthly}';

        default:
          return OneAppString.of().textSelectDateAndFrequency;
      }
    } else {
      return DateFormatterUtil.formatStringDateToFullMonthDateYear(
          displayDate!);
    }
  }
}
