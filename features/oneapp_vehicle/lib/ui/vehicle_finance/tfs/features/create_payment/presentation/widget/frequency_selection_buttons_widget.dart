// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../core/utils/widgets/custom_button.dart';
import '../../../core/utils/widgets/generic_bottomsheets.dart';
import '../../domain/payment_types.dart';

class FrequencySelectionButtons extends StatefulWidget {
  const FrequencySelectionButtons({
    Key? key,
    required this.onTapAction,
    this.removeOverlay,
    this.showRecurringButton,
    this.showOneTimeButton,
    this.onTapActionOnError,
  }) : super(key: key);
  final void Function(PaymentFrequency frequency) onTapAction;
  final bool? showOneTimeButton;
  final bool? showRecurringButton;
  final Function? removeOverlay;
  final Function? onTapActionOnError;

  @override
  State<FrequencySelectionButtons> createState() =>
      _FrequencySelectionButtonsState();
}

class _FrequencySelectionButtonsState extends State<FrequencySelectionButtons> {
  bool? isOneTimeSelected;

  @override
  Widget build(BuildContext context) {
    final _colorUtil = ThemeConfig.current().colorUtil;
    final _textStyleUtil = ThemeConfig.current().textStyleUtil;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        (widget.showOneTimeButton ?? false)
            ? CustomButton(
                color: isOneTimeSelected ?? false
                    ? _colorUtil.primaryButton02
                    : _colorUtil.primaryButton01,
                label: Text(
                  OneAppString.of().textOneTimePayment,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout2,
                      isOneTimeSelected ?? false
                          ? _colorUtil.primaryButton01
                          : _colorUtil.primaryButton02),
                ),
                onPressed: () => onTapAction(PaymentFrequency.oneTime),
              )
            : const SizedBox(),
        SizedBox(height: (widget.showOneTimeButton ?? false) ? 5.0 : 0),
        (widget.showRecurringButton ?? false)
            ? CustomButton(
                color: !(isOneTimeSelected ?? true)
                    ? _colorUtil.primaryButton02
                    : _colorUtil.primaryButton01,
                label: Text(
                  OneAppString.of().textRecurringPayment,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout2,
                      !(isOneTimeSelected ?? true)
                          ? _colorUtil.primaryButton01
                          : _colorUtil.primaryButton02),
                ),
                onPressed: () => onTapAction(PaymentFrequency.recurring),
              )
            : widget.showRecurringButton == null
                ? CustomButton(
                    disabled: true,
                    onPressedAllowedOnDisabledButton: true,
                    label: Text(
                      OneAppString.of().textRecurringPayment,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout2, _colorUtil.button05a),
                    ),
                    onPressed: () {
                      if (widget.removeOverlay != null) {
                        widget.removeOverlay!();
                      }
                      GenericBottomSheets.showGenericErrorBottomSheet(
                        context,
                        error: OneAppString.of().textServerError,
                      );
                      widget.onTapActionOnError?.call();
                    },
                  )
                : SizedBox(),
      ],
    );
  }

  Future<void> onTapAction(PaymentFrequency frequency) async {
    setState(() {
      if (frequency == PaymentFrequency.oneTime) {
        isOneTimeSelected = true;
      } else {
        isOneTimeSelected = false;
      }
    });
    await Future<void>.delayed(const Duration(milliseconds: 500));
    widget.onTapAction(frequency);
  }
}
