// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';

// Project imports:
import '../../payment_history/domain/payment_history.dart';
import '../../payment_history/domain/recurring_payment_plans.dart';

part 'payment_validation_status.freezed.dart';

@freezed
class PaymentValidationStatus with _$PaymentValidationStatus {
  const PaymentValidationStatus._();
  const factory PaymentValidationStatus.duplicatePayment(
      {required String kintoId}) = _DuplicatePayment;
  const factory PaymentValidationStatus.paymentNotAllowed() =
      _PaymentNotAllowed;
  const factory PaymentValidationStatus.invalidAmount() = _InvalidAmount;
  const factory PaymentValidationStatus.invalidDate() = _InvalidDate;
  const factory PaymentValidationStatus.selectBank() = _SelectBank;
  const factory PaymentValidationStatus.selectBankAndAmount() =
      _SelectBankAndAmount;
  const factory PaymentValidationStatus.activeRecurringPlan(
      {required Transaction recurringTransaction,
      required RecurringPaymentPlans? recurringPlans}) = _ActiveRecurringPlan;
  const factory PaymentValidationStatus.unKnown() = _Unknown;
}
