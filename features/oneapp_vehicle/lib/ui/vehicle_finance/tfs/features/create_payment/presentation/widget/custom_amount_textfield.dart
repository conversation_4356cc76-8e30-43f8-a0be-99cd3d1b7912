// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../core/utils/tfs_theme_constants.dart';
import '../../../core/utils/widgets/keyboard_config_wrapper.dart';
import '../../../core/utils/widgets/text_widget_with_dollar.dart';
import '../../../core/utils/widgets/tfs_icons.dart';

class CustomAmountTextField extends StatelessWidget {
  const CustomAmountTextField({
    Key? key,
    required this.controller,
    required this.focusNode,
    this.errorStyle,
    this.validator,
    this.isShowSuffixIcon = false,
  }) : super(key: key);
  final TextEditingController controller;
  final FocusNode focusNode;
  final String? Function(String?)? validator;
  final bool isShowSuffixIcon;
  final TextStyle? errorStyle;

  @override
  Widget build(BuildContext context) {
    final _colorUtil = ThemeConfig.current().colorUtil;
    final _textStyleUtil = ThemeConfig.current().textStyleUtil;
    final int inputLength = 8;
    return CustomKeyboardWidget(
      focusNode: focusNode,
      child: TextFormField(
        focusNode: focusNode,
        controller: controller,
        keyboardType: TextInputType.number,
        style: _textStyleUtil.body3.copyWith(
            color: focusNode.hasFocus || isShowSuffixIcon
                ? _colorUtil.tertiary03
                : _colorUtil.tertiary07),
        inputFormatters: <TextInputFormatter>[
          AmountInputFormatter(),
          LengthLimitingTextInputFormatter(inputLength)
        ],
        validator: validator,
        decoration: InputDecoration(
          errorStyle: errorStyle,
          errorMaxLines: 2,
          prefix: TextWidgetWithDollar(
            textString: ' ',
            textStyle: _textStyleUtil.body3.copyWith(
                color: focusNode.hasFocus || isShowSuffixIcon
                    ? _colorUtil.tertiary03
                    : _colorUtil.tertiary07),
          ),
          enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                  color: isShowSuffixIcon
                      ? _colorUtil.secondary01
                      : _colorUtil.tertiary10),
              borderRadius: commonBorderRadius),
          focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: _colorUtil.secondary01),
              borderRadius: commonBorderRadius),
          border: OutlineInputBorder(borderRadius: commonBorderRadius),
          focusedErrorBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              borderSide: BorderSide(
                color: _colorUtil.primary01,
              )),
          suffix: isShowSuffixIcon ? TFSIcons.doneIconWidget(context) : null,
        ),
      ),
    );
  }
}

class AmountInputFormatter extends TextInputFormatter {
  final RegExp numberRegExp = RegExp(r'[^0-9]');
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final String amount = newValue.text.replaceAll(numberRegExp, '');
    final int integer = int.parse(amount);
    if (integer == 0) {
      return TextEditingValue(
        text: '0.00',
        selection: TextSelection.fromPosition(
          const TextPosition(offset: 4),
        ),
      );
    } else {
      return TextEditingValue(
        text: (integer / 100).toStringAsFixed(2),
        selection: TextSelection.fromPosition(
          TextPosition(offset: (integer / 100).toStringAsFixed(2).length),
        ),
      );
    }
  }
}
