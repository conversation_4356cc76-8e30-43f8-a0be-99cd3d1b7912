// Dart imports:
import 'dart:io';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';

// Project imports:
import '../../../../cs_self_service_payment_flutter.dart';
import '../../../core/utils/tfs_theme_constants.dart';
import '../../../core/utils/utils.dart';
import '../../../dashboard/application/account_summary_use_cases.dart';
import '../../../dashboard/domain/account.dart';
import '../../domain/payment_types.dart';
import 'custom_amount_textfield.dart';

class AmountEntryArgs {
  AmountEntryArgs({
    required this.account,
    required this.onAmountChanged,
    this.fromScreen,
    this.selectedDate = null,
    this.frequency = PaymentFrequency.oneTime,
    this.paymentDate,
  });

  final Account account;
  final Function(double) onAmountChanged;
  final String? fromScreen;
  final Function(DateTime?, PaymentFrequency)? selectedDate;
  final PaymentFrequency frequency;
  final DateTime? paymentDate;
}

class AmountEntryScreen extends ConsumerStatefulWidget {
  const AmountEntryScreen({required this.args});

  final AmountEntryArgs args;

  @override
  AmountEntryScreenState createState() => AmountEntryScreenState();
}

class AmountEntryScreenState extends ConsumerState<AmountEntryScreen> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;

  bool _isValidAmount = false;

  late AccountSummaryUseCases accountSummaryUseCases;

  final GlobalKey<FormState> _formKey = GlobalKey();
  late ColorUtil _colorUtil;
  late TextStyleUtil _textStyleUtil;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: '0.00');
    _focusNode = FocusNode();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _formKey.currentState!.validate();
    });
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _colorUtil = ThemeConfig.current().colorUtil;
    _textStyleUtil = ThemeConfig.current().textStyleUtil;
    final double maxPaymentAmount =
        double.tryParse(widget.args.account.payoffAmount) ?? 9999.99;
    return BaseScaffold(
        isRemoveLineTop: true,
        label: OneAppString.of().textPayment,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 35),
            child: Column(children: <Widget>[
              Expanded(
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    onChanged: () {
                      if (_formKey.currentState!.validate()) {
                        setState(() => _isValidAmount = true);
                      } else {
                        setState(() => _isValidAmount = false);
                      }
                    },
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Container(
                            height: 60,
                            decoration: BoxDecoration(
                                color: _colorUtil.tile05,
                                borderRadius: borderCircularRadius),
                            padding: const EdgeInsets.symmetric(vertical: 18),
                            child: Row(
                              children: <Widget>[
                                const SizedBox(width: 19),
                                Container(
                                    width: 18,
                                    height: 18,
                                    decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: _colorUtil.tertiary03),
                                    child: Container(
                                        margin: const EdgeInsets.all(1.5),
                                        decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: _colorUtil.tertiary15),
                                        child: Icon(
                                            TFSIcons.dataIconAttachMoney,
                                            size: 15,
                                            color: _colorUtil.tertiary03))),
                                const SizedBox(width: 19),
                                Text(OneAppString.of().textEnterCustomAmount,
                                    style: _textStyleUtil.body4),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          SizedBox(
                              height: 60,
                              child: CustomAmountTextField(
                                controller: _controller,
                                focusNode: _focusNode,
                                isShowSuffixIcon:
                                    double.tryParse(_controller.text)! >= 5.0 &&
                                        _isValidAmount,
                                errorStyle: _textStyleUtil.body1
                                    .copyWith(height: 0)
                                    .copyWith(color: Colors.red),
                                validator: (String? val) {
                                  final double parsedAmount =
                                      double.parse(val ?? '0');
                                  if (_focusNode.hasFocus &&
                                      (parsedAmount < 5.00 ||
                                          parsedAmount > maxPaymentAmount)) {
                                    return '';
                                  } else {
                                    return null;
                                  }
                                },
                              )),
                          if (!_isValidAmount &&
                              _focusNode.hasFocus) ...<Widget>{
                            FittedBox(
                              child: Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: Text(
                                    OneAppString.of().textCustomAmountError(
                                        TFSHelperFunctions.currencyFormat(
                                            maxPaymentAmount)),
                                    style: _textStyleUtil.callout1.copyWith(
                                        color: _colorUtil.error01,
                                        fontSize: isBrandToyota ? 14 : 14.5)),
                              ),
                            ),
                          },
                          Padding(
                            padding:
                                const EdgeInsets.only(top: 20.0, left: 5.0),
                            child: FittedBox(
                                child: Row(children: [
                              Text(
                                '${OneAppString.of().textCurrentBalance} : ${TFSHelperFunctions.currencyFormat(widget.args.account.paymentAmount)}',
                                style: _textStyleUtil.callout1.copyWith(
                                    color: _colorUtil.tertiary05,
                                    fontSize: isBrandToyota ? 14 : 14.5),
                              ),
                              const SizedBox(width: 8),
                              InkWell(
                                key: const Key('amount_info_breakdown'),
                                onTap: () {
                                  if (widget.args.fromScreen ==
                                      OneAppString.of().textEditPayment) {
                                    FireBaseAnalyticsLogger
                                        .logTFSPaymentGroupEvent(
                                            VehicleAnalyticsEvent
                                                .TFS_EDIT_PAYMENT_TOOLTIP_AMOUNT);
                                  } else if (widget.args.fromScreen ==
                                      OneAppString.of().textMakeAPayment) {
                                    FireBaseAnalyticsLogger
                                        .logTFSPaymentGroupEvent(
                                            VehicleAnalyticsEvent
                                                .TFS_PAYMENT_TOOLTIP_AMOUNT);
                                  }
                                  GenericBottomSheets
                                      .showGenericInfoBottomSheet(
                                    context,
                                    needFit: false,
                                    height: 377,
                                    icon: TFSIcons.paymentInfoIconWidget(),
                                    infoWidget: Padding(
                                        padding: const EdgeInsets.only(top: 5),
                                        child: Column(children: [
                                          rowWithDescriptionAndAmount(
                                              description: widget.args.account
                                                          .contractType
                                                          .toUpperCase() ==
                                                      'R'
                                                  ? OneAppString.of()
                                                      .textPrincipalAndInterest
                                                  : OneAppString.of()
                                                      .textLeasePayment,
                                              amount: double.tryParse(widget
                                                  .args
                                                  .account
                                                  .monthlyPaymentAmount)),
                                          const SizedBox(
                                            height: 20,
                                          ),
                                          rowWithDescriptionAndAmount(
                                              description: OneAppString.of()
                                                  .textLateFees,
                                              amount: widget
                                                  .args.account.lateCharges),
                                          const SizedBox(
                                            height: 20,
                                          ),
                                          rowWithDescriptionAndAmount(
                                              description:
                                                  OneAppString.of().textMisc,
                                              amount: widget
                                                  .args.account.miscCharges),
                                          const SizedBox(
                                            height: 20,
                                          ),
                                          rowWithDescriptionAndAmount(
                                              description:
                                                  OneAppString.of().textPastDue,
                                              amount: widget
                                                  .args.account.pastDueAmount),
                                        ])),
                                    title:
                                        OneAppString.of().textPaymentBreakdown,
                                  );
                                },
                                child: TFSIcons.infoIconWidget(),
                              ),
                            ])),
                          ),
                        ]),
                  ),
                ),
              ),
              Column(children: <Widget>[
                if (MediaQuery.of(context).viewInsets.bottom == 0)
                  Column(
                    children: <Widget>[
                      MergeSemantics(
                        child: Column(
                          children: <Widget>[
                            FittedBox(
                                child: Text(
                              OneAppString.of().textPaymentAmountEntryReminder,
                              textAlign: TextAlign.center,
                              style: _textStyleUtil.callout1,
                            )),
                            FittedBox(
                                child: Text(
                              OneAppString.of().textYourPayOffPleaseObtainQuote,
                              textAlign: TextAlign.center,
                              style: _textStyleUtil.callout1,
                            )),
                            FittedBox(
                                child: Text(
                              OneAppString.of().textPleaseLoginAt,
                              textAlign: TextAlign.center,
                              style: _textStyleUtil.callout1,
                            )),
                            GestureDetector(
                              onTap: () {
                                // ignore: sdk_version_since
                                URLS.launchWebsiteLoginUrl().onError((_, __) =>
                                    CustomBottomSheet.showErrorBottomsheet(
                                        context,
                                        title: OneAppString.of()
                                            .textSomethingWentWrong,
                                        body: Center(
                                            child: Text(
                                                OneAppString.of()
                                                    .textFailedToLaunchUrl,
                                                style:
                                                    _textStyleUtil.callout1))));
                              },
                              child: FittedBox(
                                  child: Text(
                                isBrandToyota
                                    ? OneAppString.of().textToyotaFinancial
                                    : OneAppString.of().textLexusFinancial,
                                style: _textStyleUtil.body2,
                              )),
                            )
                          ],
                        ),
                      ),
                      const SizedBox(height: 30),
                      Semantics(
                        container: true,
                        child: PrimaryButton(
                          label: OneAppString.of().textComplete,
                          isDisabled: !_isValidAmount,
                          onTap: () {
                            if (widget.args.fromScreen ==
                                OneAppString.of().textEditPayment) {
                              FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                                  VehicleAnalyticsEvent
                                      .TFS_EDIT_PAYMENT_ENTER_AMOUNT_CONTINUE_CTA);
                            } else if (widget.args.fromScreen ==
                                OneAppString.of().textMakeAPayment) {
                              FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
                                  VehicleAnalyticsEvent
                                      .TFS_PAYMENT_ENTER_AMOUNT_CONTINUE_CTA);
                            }
                            final double amount =
                                double.parse(_controller.text);
                            widget.args.onAmountChanged(amount);
                            Navigator.of(context).pop();
                          },
                        ),
                      ),
                    ],
                  ),
              ]),
              if (_focusNode.hasFocus && Platform.isIOS) ...<Widget>{
                const SizedBox(height: 16.0),
              }
            ]),
          ),
        ));
  }

  Widget rowWithDescriptionAndAmount(
      {required String description, double? amount}) {
    return Builder(builder: (BuildContext context) {
      return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text('$description :',
            style:
                _textStyleUtil.callout1.copyWith(color: _colorUtil.tertiary05)),
        const Spacer(),
        Text(TFSHelperFunctions.currencyFormat(amount),
            style: _textStyleUtil.callout2.copyWith(
                color: isDarkTheme()
                    ? _colorUtil.tertiary03
                    : _colorUtil.tertiary05)),
      ]);
    });
  }
}
