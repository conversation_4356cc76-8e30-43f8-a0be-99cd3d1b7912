// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../core/utils/extensions/extensions.dart';
import '../../../core/utils/tfs_theme_constants.dart';
import '../../../core/utils/utils.dart';
import '../../../dashboard/domain/account.dart';
import '../../../payment_history/domain/payment_history.dart';

class PaymentConfirmationCard extends StatelessWidget {
  const PaymentConfirmationCard({
    required this.account,
    required this.payment,
    this.isFromTransactionHistory = false,
    this.statusPill,
  });
  final Account? account;
  final Transaction payment;
  final bool isFromTransactionHistory;
  final String? statusPill;

  @override
  Widget build(BuildContext context) {
    final _colorUtil = ThemeConfig.current().colorUtil;
    final _textStyleUtil = ThemeConfig.current().textStyleUtil;
    final bool isFailedOrCancelled = payment.isFailedOrCancelledOrError();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.only(top: 20, bottom: 16.0),
        decoration: BoxDecoration(
          color: _colorUtil.tile02,
          borderRadius: borderCircularRadius,
        ),
        child: Stack(
          alignment: AlignmentDirectional.topCenter,
          clipBehavior: Clip.none,
          children: <Widget>[
            Positioned(
              top: -70,
              child: SizedBox(
                width: 185,
                height: 100,
                child: TFSIcons.toyotaCarImage(),
              ),
            ),
            Column(
              children: <Widget>[
                const SizedBox(height: 30.0),
                Text(
                  '${account?.vehicle.year}  ${(account?.vehicle.model)}  ',
                  style: _textStyleUtil.subHeadline1,
                ),
                const SizedBox(height: 16.0),
                Text(
                  TFSHelperFunctions.currencyFormat(
                      payment.paymentAmountInDouble()),
                  style: _textStyleUtil.title2.withColor(_colorUtil.tertiary03),
                ),
                const SizedBox(height: 16.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(
                      style: ButtonStyle(
                          backgroundColor: MaterialStateProperty.all(
                              isFailedOrCancelled
                                  ? _colorUtil.primary02
                                  : _colorUtil.secondary02),
                          shape:
                              MaterialStateProperty.all<RoundedRectangleBorder>(
                                  RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0),
                            side: BorderSide(
                                color: isFailedOrCancelled
                                    ? _colorUtil.primary02
                                    : _colorUtil.secondary02),
                          ))),
                      onPressed: () {},
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 5),
                        child: Center(
                          child: Text(
                            statusPill ?? buildPaymentStatus(payment: payment),
                            style: _textStyleUtil.callout1.withColor(
                                isFailedOrCancelled
                                    ? _colorUtil.primary01
                                    : _colorUtil.secondary01),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                (payment.isPaymentOneTime() &&
                            payment.isStatusPaymentCancelled() ||
                        isFromTransactionHistory)
                    ? SizedBox.shrink()
                    : Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8.0, vertical: 8.0),
                        child: Text(
                          getPaymentProcessText(),
                          style: _textStyleUtil.callout1
                              .withColor(_colorUtil.tertiary05),
                          textAlign: TextAlign.center,
                        ),
                      ),
                if (payment.getPaymentConfirmationIDLast6Digits().isNotEmpty)
                  Column(children: <Widget>[
                    SizedBox(height: isFailedOrCancelled ? 15.0 : 6.0),
                    Text(
                      '${OneAppString.of().textConfirmation}: ${payment.getPaymentConfirmationIDLast6Digits()}',
                      style: _textStyleUtil.callout1
                          .withColor(_colorUtil.tertiary05),
                    )
                  ]),
                const SizedBox(height: 22.0),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String getPaymentProcessText() {
    if (statusPill == OneAppString.of().textPlanCancelled) {
      return OneAppString.of().textNoLongerDeductPayments;
    } else if (statusPill == OneAppString.of().textPaymentCancelled) {
      return OneAppString.of().textRecurringPaymentWillResumeOn;
    } else {
      return OneAppString.of().textPaymentTake;
    }
  }

  String buildPaymentStatus({
    required Transaction payment,
  }) {
    return payment.status.isEmpty
        ? OneAppString.of().textNa
        : payment.displayStatus();
  }
}
