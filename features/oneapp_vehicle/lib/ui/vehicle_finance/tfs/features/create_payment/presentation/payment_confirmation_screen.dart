// Dart imports:
import 'dart:typed_data';
import 'dart:ui' as ui;

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../core/mixins/mixins.dart';
import '../../core/utils/extensions/extensions.dart';
import '../../core/utils/utils.dart';
import '../../dashboard/domain/account.dart';
import '../../payment_history/application/application.dart';
import '../../payment_history/domain/payment_history.dart';
import '../domain/payment_types.dart';
import 'widget/payment_confirmation_info.dart';

class PaymentConfirmationArgs {
  const PaymentConfirmationArgs({
    required this.account,
    required this.paymentAmount,
    required this.accountNo,
    required this.paymentDate,
    required this.paymentConfirmationId,
    this.editPaymentButton = false,
    this.editPaymentIcon = false,
    this.fromScreen,
    this.frequency = PaymentFrequency.oneTime,
  });
  final Account? account;
  final double paymentAmount;
  final String accountNo;
  final DateTime paymentDate;
  final String paymentConfirmationId;
  final bool editPaymentButton;
  final bool editPaymentIcon;
  final String? fromScreen;
  final PaymentFrequency frequency;
}

class PaymentConfirmationScreen extends ConsumerStatefulWidget {
  const PaymentConfirmationScreen({required this.args, this.fromNative});
  final PaymentConfirmationArgs args;
  final bool? fromNative;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PaymentConfirmationScreenState();
}

class _PaymentConfirmationScreenState
    extends ConsumerState<PaymentConfirmationScreen>
    with NavigatorPopFirstRouteUtil {
  final GlobalKey _key = GlobalKey();
  late ColorUtil _colorUtil;
  late TextStyleUtil _textStyleUtil;
  @override
  void initState() {
    super.initState();
    if (widget.args.frequency != PaymentFrequency.oneTime &&
        widget.args.account?.fullAccountNumber != null) {
      ref.read(paymentHistoryProvider.notifier).fetchPaymentHistory(
          fullAccountNumber: widget.args.account!.fullAccountNumber);
    }
  }

  @override
  Widget build(BuildContext context) {
    _colorUtil = ThemeConfig.current().colorUtil;
    _textStyleUtil = ThemeConfig.current().textStyleUtil;
    final Transaction _tempPayment = Transaction(
      id: widget.args.paymentConfirmationId,
      paymentAmount: widget.args.paymentAmount.toString(),
      paymentCategory: '',
      confirmCode: widget.args.paymentConfirmationId,
      postDate: DateFormatterUtil.formatDateTimeToMonthDayYear(
          widget.args.paymentDate),
      status: OneAppString.of().textScheduled,
      frequency: widget.args.frequency.name,
    );
    return BaseScaffold(
      leading: CustomCloseIconWidget(onCloseIconTap: () {
        _fnCloseAction();
      }),
      action: IconButton(
        onPressed: () async {
          await onScreenShotSavePressed();
        },
        icon: Icon(
          Icons.camera_alt_outlined,
          color: _colorUtil.tertiary03,
        ),
      ),
      label: OneAppString.of().textPaymentConfirmationHeading,
      dismissibleTopLine: widget.fromNative == true ? false : true,
      showSwipeIcon: widget.fromNative == true ? false : true,
      body: Column(
        children: [
          Expanded(
            child: ListView(
              shrinkWrap: true,
              children: <Widget>[
                Column(
                  children: <Widget>[
                    RepaintBoundary(
                      key: _key,
                      child: Column(
                        children: [
                          PaymentConfirmationInfo(
                            account: widget.args.account,
                            accountEnding: widget.args.accountNo,
                            transaction: _tempPayment,
                            statusPill: OneAppString.of().textScheduled,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 80),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                Column(
                  children: [
                    Text(
                      OneAppString.of().textPaymentsMadeAfter5PMProcessing,
                      style: _textStyleUtil.callout1
                          .withColor(_colorUtil.tertiary05),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 25),
                    Semantics(
                        container: true,
                        child: PrimaryButton(
                            label: OneAppString.of().textClose,
                            onTap: () => _fnCloseAction())),
                    const SizedBox(height: 10),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildTextRow(String label, {Color? color, FontWeight? fontWeight}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Flexible(
            child: FittedBox(
              fit: BoxFit.fitWidth,
              child: Text(
                label,
                style: _textStyleUtil.body2,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget customInfoItem(String label, String content) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 10, 0, 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              label,
              style: _textStyleUtil.body4.withColor(_colorUtil.tertiary03),
            ),
            Text(
              content,
              style: _textStyleUtil.callout1.withColor(_colorUtil.tertiary05),
              textAlign: TextAlign.right,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> onScreenShotSavePressed() async {
    if (widget.args.fromScreen == OneAppString.of().textEditPayment) {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_EDIT_PAYMENT_RECEIVED_SCREENSHOT_CTA);
    } else {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_ONETIME_PAYMENT_RECEIVED_SCREENSHOT_CTA);
    }

    final RenderObject? renderObject = _key.currentContext?.findRenderObject();
    if (renderObject == null) {
      GenericBottomSheets.showGenericErrorBottomSheet(context,
          error: OneAppString.of().textFailedToSaveScreenshot);
    }
    final RenderRepaintBoundary boundary =
        renderObject! as RenderRepaintBoundary;
    final ui.Image image = await boundary.toImage(pixelRatio: 3.0);

    final ByteData? byteData =
        await image.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List? pngBytes = byteData?.buffer.asUint8List();

    if (pngBytes != null) {
      try {
        await ImageGallerySaver.saveImage(pngBytes, quality: 100);
        showSnackBar(OneAppString.of().textScreenShotSaved);
      } catch (e) {
        showErrorSnackBar(OneAppString.of().textFailedToSaveScreenshot);
      }
    } else {
      showErrorSnackBar(OneAppString.of().textFailedToSaveScreenshot);
    }
  }

  void showSnackBar(String message) {
    GenericBottomSheets.showGenericSuccessBottomSheet(context,
        successMsg: message, fromScreen: widget.args.fromScreen);
  }

  void showErrorSnackBar(String message) {
    GenericBottomSheets.showGenericErrorBottomSheet(context, error: message);
  }

  void _fnCloseAction() {
    if (widget.args.fromScreen == OneAppString.of().textEditPayment) {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_TFS_EDIT_PAYMENT_RECEIVED_CLOSE_CTA);
    } else {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_ONETIME_PAYMENT_RECEIVED_CLOSE_CTA);
    }

    if (widget.args.editPaymentButton) {
      Navigator.pop(context);
      Navigator.pop(context);
      Navigator.pop(context);
      if (widget.fromNative == true) {
        goBackToNative();
      }
    } else if (widget.args.editPaymentIcon) {
      Navigator.pop(context);
      Navigator.pop(context);
      if (widget.fromNative == true) {
        goBackToNative();
      }
    } else {
      if (widget.fromNative == true) {
        goBackToNative();
      }
    }
  }
}
