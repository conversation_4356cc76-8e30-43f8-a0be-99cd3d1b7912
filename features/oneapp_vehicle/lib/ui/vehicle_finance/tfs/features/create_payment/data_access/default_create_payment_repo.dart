// Package imports:
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/create_payment_response_dto.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../core/domain/api_failure.dart';
import '../../core/utils/error_handling.dart';
import '../domain/create_payment_success.dart';
import '../domain/domain.dart';
import '../domain/payment_request.dart';
import '../infrastructure/create_payment_api.dart';

class DefaultCreatePaymentRepo extends CreatePaymentRepo {
  DefaultCreatePaymentRepo(this.api);

  final CreatePaymentApi api;

  @override
  Future<Result<CreatePaymentSuccess, ApiFailure>>
      getScheduledPaymentRepoResponse({
    required PaymentRequest paymentRequest,
  }) async {
    try {
      final CommonTfsResponse<CreatePaymentResponseDTO> paymentResponse =
          await api.post(paymentRequest);
      if (paymentResponse.response?.confirmationId != null) {
        CreatePaymentSuccess success = CreatePaymentSuccess(
            confirmationId: paymentResponse.response!.confirmationId!);
        return Result.success(success);
      }
      return Result.failure(
          ApiFailure.network(errorMessage: OneAppString.of().textServerError));
    } catch (e) {
      return Result.failure(
          ApiFailure.network(errorMessage: ErrorMessage.handleError(e)));
    }
  }
}
