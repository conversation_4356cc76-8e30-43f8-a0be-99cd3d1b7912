// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_result/freezed_result.dart';

// Project imports:
import '../../core/domain/api_failure.dart';
import '../../core/utils/date_formatter_util.dart';
import '../../core/utils/device_and_package_info_util.dart';
import '../../dashboard/domain/account.dart';
import '../../payment_history/application/application.dart';
import '../../payment_history/domain/payment_history.dart';
import '../../payment_methods/domain/domain.dart';
import '../domain/create_payment_success.dart';
import '../domain/domain.dart';
import '../domain/payment_request.dart';
import '../domain/payment_types.dart';
import 'create_payment_state.dart';
import 'create_payment_use_cases.dart';

class CreatePaymentLogic extends CreatePaymentUseCases {
  CreatePaymentLogic({
    required this.createPaymentRepo,
    required this.kintoRepo,
    required this.breRepo,
    required this.ref,
  }) : super(const CreatePaymentState.initial());

  final CreatePaymentRepo createPaymentRepo;
  final KintoRepo kintoRepo;
  final BreRepo breRepo;
  final Ref ref;

  @override
  Future<void> initiatePayment({
    required DateTime? paymentDate,
    required double paymentAmount,
    required Account account,
    required PaymentFrequency frequency,
    BankAccountInfo? defaultBank,
  }) async {
    if (defaultBank == null || defaultBank.id.isEmpty) {
      state = const CreatePaymentState.validationStatus(
          status: PaymentValidationStatus.selectBank());
    } else if (paymentDate == null || !isValidDate(paymentDate)) {
      state = const CreatePaymentState.validationStatus(
          status: PaymentValidationStatus.invalidDate());
    } else if (paymentAmount < 5.0) {
      state = const CreatePaymentState.validationStatus(
          status: PaymentValidationStatus.invalidAmount());
    } else if (frequency != PaymentFrequency.oneTime) {
      ref
          .read(paymentHistoryProvider.notifier)
          .fetchPaymentHistory(fullAccountNumber: account.fullAccountNumber)
          .whenComplete(() {
        _readPaymentHistory(
          paymentDate: paymentDate,
          paymentAmount: paymentAmount,
          account: account,
          frequency: frequency,
          defaultBank: defaultBank,
        );
      });
    } else {
      _readPaymentHistory(
        paymentDate: paymentDate,
        paymentAmount: paymentAmount,
        account: account,
        frequency: frequency,
        defaultBank: defaultBank,
      );
    }
  }

  void _readPaymentHistory({
    required DateTime paymentDate,
    required double paymentAmount,
    required Account account,
    required PaymentFrequency frequency,
    required BankAccountInfo defaultBank,
  }) {
    ref.read(paymentHistoryProvider).maybeWhen(
          success: (paymentHistory) {
            if (paymentHistory.error != null ||
                (frequency != PaymentFrequency.oneTime &&
                    (paymentHistory.recurringPaymentPlans?.hasError ??
                        false))) {
              state = const CreatePaymentState.validationStatus(
                status: PaymentValidationStatus.unKnown(),
              );
            } else if (frequency != PaymentFrequency.oneTime &&
                paymentHistory.getRecurringPayment() != null) {
              state = CreatePaymentState.validationStatus(
                status: PaymentValidationStatus.activeRecurringPlan(
                    recurringTransaction: paymentHistory.getRecurringPayment()!,
                    recurringPlans: paymentHistory.recurringPaymentPlans),
              );
            } else {
              _fetchKintoService(
                paymentDate: paymentDate,
                paymentAmount: paymentAmount,
                account: account,
                frequency: frequency,
                defaultBank: defaultBank,
                paymentHistory: paymentHistory,
              );
            }
          },
          orElse: () => state = const CreatePaymentState.validationStatus(
            status: PaymentValidationStatus.unKnown(),
          ),
        );
  }

  bool isValidDate(DateTime paymentDate) {
    DateTime todaysDate =
        DateFormatterUtil.dateOnly(DateFormatterUtil.getTodayTimeInCST());
    DateTime _paymentDate = DateFormatterUtil.dateOnly(paymentDate);
    if (DateFormatterUtil.checkIfDateIsPastPaymentCutOff()) {
      return !(_paymentDate.isBefore(todaysDate)) &&
          !DateFormatterUtil.isSameDay(paymentDate);
    } else {
      return !(_paymentDate.isBefore(todaysDate));
    }
  }

  Future<void> _fetchKintoService({
    required DateTime paymentDate,
    required double paymentAmount,
    required Account account,
    required PaymentFrequency frequency,
    required BankAccountInfo defaultBank,
    required PaymentHistory paymentHistory,
  }) async {
    final result = await kintoRepo.getKintoData(
      upid: account.customer.ucid,
    );

    result.when(success: (Kinto kinto) {
      _fetchBreService(
        paymentDate: paymentDate,
        paymentAmount: paymentAmount,
        account: account,
        frequency: frequency,
        defaultBank: defaultBank,
        last4SSN: kinto.ssn,
        kintoServiceId: kinto.serviceId,
        paymentHistory: paymentHistory,
      );
    }, failure: (error) {
      state = const CreatePaymentState.validationStatus(
          status: PaymentValidationStatus.unKnown());
    });
  }

  Future<void> _fetchBreService({
    required DateTime paymentDate,
    required double paymentAmount,
    required Account account,
    required PaymentFrequency frequency,
    required BankAccountInfo defaultBank,
    required String last4SSN,
    required PaymentHistory paymentHistory,
    required String kintoServiceId,
  }) async {
    final result = await breRepo.getBreData(
      accountNumber: account.fullAccountNumber,
      last4SSN: last4SSN,
    );
    result.when(success: (PaymentBre breData) async {
      final bool isFrequencyOneTime = frequency == PaymentFrequency.oneTime;

      final bool isPaymentAllowedOneTime =
          (isFrequencyOneTime && (breData.paymentType.oneTime));
      final bool paymentAllowedRecurring =
          (!isFrequencyOneTime && (breData.paymentType.recurring));
      final bool paymentIsAllowed = breData.paymentEnabled &&
          (isPaymentAllowedOneTime || paymentAllowedRecurring);
      if (paymentIsAllowed) {
        if (paymentHistory.checkIfDuplicatePaymentExists(
            account.fullAccountNumber, paymentAmount.toString(), paymentDate,
            isOneTimeSubmitted: isFrequencyOneTime)) {
          state = CreatePaymentState.validationStatus(
              status: PaymentValidationStatus.duplicatePayment(
                  kintoId: kintoServiceId));
        } else {
          await submitPayment(
            paymentDate: paymentDate,
            account: account,
            defaultBank: defaultBank,
            paymentAmount: paymentAmount,
            frequency: frequency,
            kintoServiceId: kintoServiceId,
          );
        }
      } else {
        state = const CreatePaymentState.validationStatus(
            status: PaymentValidationStatus.paymentNotAllowed());
      }
    }, failure: (error) {
      state = const CreatePaymentState.validationStatus(
          status: PaymentValidationStatus.unKnown());
    });
  }

  @override
  Future<void> submitPayment({
    required DateTime paymentDate,
    required double paymentAmount,
    required Account account,
    required PaymentFrequency frequency,
    required BankAccountInfo defaultBank,
    required String kintoServiceId,
    bool? isDuplicateAccepted,
  }) async {
    final String dateString =
        DateFormatterUtil.formatPaymentDashesDate(paymentDate);
    final bool isImmediateProcessing =
        DateFormatterUtil.isSameDay(paymentDate) &&
            !DateFormatterUtil.checkIfDateIsPastPaymentCutOff();

    bool allowDuplicate = isDuplicateAccepted ?? false;
    final String comments = await getComments(
        isDuplicateAccepted: allowDuplicate,
        accountNumber: account.fullAccountNumber);
    if (frequency == PaymentFrequency.oneTime) {
      onTimePayment(
          paymentAmount: paymentAmount,
          account: account,
          defaultBank: defaultBank,
          kintoServiceId: kintoServiceId,
          paymentDate: dateString,
          isImmediateProcessing: isImmediateProcessing,
          comments: comments,
          frequencyType: frequency,
          isDuplicateAccepted: allowDuplicate);
    } else {
      recurringPayment(
          paymentAmount: paymentAmount,
          account: account,
          defaultBank: defaultBank,
          kintoServiceId: kintoServiceId,
          startDate: dateString,
          endDate: account.maturityDate.toServerFormatDate,
          isImmediateProcessing: isImmediateProcessing,
          comments: comments,
          frequencyType: frequency,
          isDuplicateAccepted: allowDuplicate);
    }
  }

  Future<String> getComments(
      {required bool isDuplicateAccepted,
      required String accountNumber}) async {
    String duplicateAccepted = '';
    if (isDuplicateAccepted) {
      duplicateAccepted = 'Customer Confirmed Duplicate (';
    }
    String paymentHubCount = '';
    ref.read(paymentHistoryProvider).maybeWhen(
          success: (paymentHistory) =>
              paymentHubCount = paymentHistory.transactions.length.toString(),
          orElse: () => paymentHubCount = '0',
        );
    final String submissionDateTime =
        DateFormatterUtil.getTodayTimeInCST().millisecondsSinceEpoch.toString();

    final String appInfo =
        '${DeviceAndPackageInfoUtil.appName}/${DeviceAndPackageInfoUtil.appVersion}';
    final String deviceInfo =
        '(${DeviceAndPackageInfoUtil.platform};${DeviceAndPackageInfoUtil.deviceReleaseVersion};${DeviceAndPackageInfoUtil.deviceManufacturer} ${DeviceAndPackageInfoUtil.deviceModel});';
    final String paymentHubInfo =
        ' paymentcount: $paymentHubCount - $submissionDateTime';
    return '$duplicateAccepted$appInfo$deviceInfo$paymentHubInfo';
  }

  Future<void> onTimePayment({
    required double paymentAmount,
    required Account account,
    required BankAccountInfo defaultBank,
    required String kintoServiceId,
    required String paymentDate,
    required bool isImmediateProcessing,
    required String comments,
    required PaymentFrequency frequencyType,
    required bool isDuplicateAccepted,
  }) async {
    final PaymentRequest paymentRequest = PaymentRequest(
        accountNumber: account.fullAccountNumber,
        upid: account.customer.ucid,
        paymentMethodId: defaultBank.id,
        paymentMethod: defaultBank.type,
        achAccountType: defaultBank.achAccountType,
        achAbaCode: defaultBank.achAbaCode,
        accountId: defaultBank.accountId,
        paymentAmount: paymentAmount.toString(),
        achAccountNumberMask: defaultBank.achAccountNumberMask,
        accountType: account.financialAccountTypeDesc,
        startDate: paymentDate,
        processPayment: isImmediateProcessing,
        frequency: frequencyType.name,
        comments: comments,
        paymentType: regularPayment,
        kintoServiceId: kintoServiceId,
        allowDuplicate: isDuplicateAccepted);

    final Result<CreatePaymentSuccess, ApiFailure> result =
        await createPaymentRepo.getScheduledPaymentRepoResponse(
            paymentRequest: paymentRequest);

    result.when(
      success: (CreatePaymentSuccess response) {
        state = CreatePaymentState.paymentCreated(
          paymentConfirmationId: response.confirmationId,
        );
      },
      failure: (ApiFailure error) {
        state = const CreatePaymentState.validationStatus(
            status: PaymentValidationStatus.unKnown());
      },
    );
  }

  Future<void> recurringPayment({
    required double paymentAmount,
    required Account account,
    required BankAccountInfo defaultBank,
    required String kintoServiceId,
    required String startDate,
    required String endDate,
    required PaymentFrequency frequencyType,
    required bool isImmediateProcessing,
    required String comments,
    required bool isDuplicateAccepted,
  }) async {
    final PaymentRequest paymentRequest = PaymentRequest(
        accountNumber: account.fullAccountNumber,
        upid: account.customer.ucid,
        paymentMethodId: defaultBank.id,
        paymentMethod: defaultBank.type,
        achAccountType: defaultBank.achAccountType,
        achAbaCode: defaultBank.achAbaCode,
        accountId: defaultBank.accountId,
        paymentAmount: paymentAmount.toString(),
        achAccountNumberMask: defaultBank.achAccountNumberMask,
        accountType: account.financialAccountTypeDesc,
        startDate: startDate,
        endDate: endDate,
        processPayment: isImmediateProcessing,
        frequency: frequencyType.name,
        comments: comments,
        paymentType: regularPayment,
        kintoServiceId: kintoServiceId,
        allowDuplicate: isDuplicateAccepted);
    final Result<CreatePaymentSuccess, ApiFailure> result =
        await createPaymentRepo.getScheduledPaymentRepoResponse(
            paymentRequest: paymentRequest);

    result.when(
      success: (CreatePaymentSuccess response) {
        state = CreatePaymentState.paymentCreated(
          paymentConfirmationId: response.confirmationId,
        );
      },
      failure: (ApiFailure error) {
        state = const CreatePaymentState.validationStatus(
            status: PaymentValidationStatus.unKnown());
      },
    );
  }
}
