// Flutter imports:
import 'package:flutter/cupertino.dart';

// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../../router/navigation_service.dart';
import '../../../router/routes.dart';
import '../../../services/locators.dart';
import '../../core/utils/widgets/generic_bottomsheets.dart';
import '../../dashboard/domain/account.dart';
import '../../payment_history/application/application.dart';
import '../../payment_history/application/payment_history_state.dart';
import '../../payment_history/domain/payment_history.dart';
import '../../payment_history/presentation/payment_history_list_screen.dart';

class PaymentHistoryHandler {
  PaymentHistoryHandler({
    required this.context,
    required this.accountUser,
  });
  final BuildContext context;
  static bool forceLoad = false;
  final Account accountUser;

  Future<void> fetchPaymentHistory({
    bool forceLoading = false,
    required WidgetRef ref,
  }) async {
    final fullAccountNumber = accountUser.fullAccountNumber;
    forceLoad = forceLoading;
    if (forceLoad) {
      getIt<TFSNavigationService>().showLoader(context: context);
    }
    await ref
        .read(paymentHistoryProvider.notifier)
        .fetchPaymentHistory(fullAccountNumber: fullAccountNumber);
  }

  void paymentHistoryStateListener(WidgetRef ref) {
    ref.listen<PaymentHistoryState>(paymentHistoryProvider,
        (PaymentHistoryState? previous, PaymentHistoryState? next) {
      if (next != null) {
        getIt<TFSNavigationService>().hideLoader(context: context);
        next.maybeWhen(success: (PaymentHistory paymentHistory) {
          if (forceLoad) {
            forceLoad = false;
            if (paymentHistory.error != null) {
              GenericBottomSheets.showGenericErrorBottomSheet(context,
                  title: OneAppString.of().textPaymentInfo,
                  error: OneAppString.of().textServerError);
            } else {
              navigatePaymentHistoryScreen(paymentHistory.transactions);
            }
          }
        }, orElse: () {
          if (forceLoad) {
            forceLoad = false;
            GenericBottomSheets.showGenericErrorBottomSheet(context,
                title: OneAppString.of().textPaymentInfo,
                error: OneAppString.of().textServerError);
          }
        });
      }
    });
  }

  void navigatePaymentHistoryScreen(List<Transaction> transactions) {
    getIt<TFSNavigationService>().pushNamed(
      Routes.viewPaymentHistoryScreen,
      arguments: PaymentHistoryArgs(
        accountUser: accountUser,
      ),
    );
  }
}
