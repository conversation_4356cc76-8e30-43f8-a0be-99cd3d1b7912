// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';

// Project imports:
import '../../../../../../../log/vehicle_analytic_event.dart';
import '../../../../router/navigation_service.dart';
import '../../../../router/routes.dart';
import '../../../../services/locators.dart';
import '../../../core/utils/extensions/extensions.dart';
import '../../../core/utils/tfs_helper_function.dart';
import '../../../core/utils/widgets/custom_tile.dart';
import '../../../dashboard/domain/account.dart';
import '../../../payment_methods/domain/bank_account_info.dart';
import '../../domain/payment_types.dart';
import 'amount_entry_screen.dart';

class PaymentAmountTile extends ConsumerWidget {
  const PaymentAmountTile(
      {required this.paymentDate,
      required this.paymentAmount,
      required this.paymentFrequency,
      required this.selectedBank,
      required this.account,
      required this.resetRecurringSelection,
      required this.onAmountChanged,
      required this.paymentTypeEnum,
      required this.isAmountChanged,
      Key? key})
      : super(key: key);

  final DateTime? paymentDate;
  final double paymentAmount;
  final PaymentFrequency paymentFrequency;
  final BankAccountInfo? selectedBank;
  final Account account;
  final Function? resetRecurringSelection;
  final Function(double amount) onAmountChanged;
  final PaymentTypeEnum paymentTypeEnum;
  final bool isAmountChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String route = Routes.amountEntryScreen;
    if (paymentTypeEnum == PaymentTypeEnum.recurring) {
      route = Routes.recurringPaymentAmountEntryScreen;
    }
    return CustomTile(
      key: const Key('keyAmountToPay'),
      title: OneAppString.of().textAmount,
      subtitle: buildPaymentAmountTile(),
      onTap: () async {
        FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
            VehicleAnalyticsEvent.TFS_PAYMENT_AMOUNT);
        getIt<TFSNavigationService>().pushNamed(
          route,
          arguments: AmountEntryArgs(
              account: account,
              fromScreen: OneAppString.of().textMakeAPayment,
              onAmountChanged: (double amount) => onAmountChanged(amount)),
        );
      },
    );
  }

  Widget buildPaymentAmountTile() {
    TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
    ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
    String paymentText = "";
    if (paymentTypeEnum == PaymentTypeEnum.recurring && !isAmountChanged) {
      paymentText = OneAppString.of().textSelectPaymentOption;
    } else {
      paymentText = TFSHelperFunctions.currencyFormat(paymentAmount);
    }
    return Semantics(
      label:
          '${TFSHelperFunctions.currencyFormat(paymentAmount)} ${OneAppString.of().textSemanticAction}',
      excludeSemantics: true,
      child: Text(
        paymentText,
        style: _textStyleUtil.callout1.withColor(_colorUtil.tertiary05),
      ),
    );
  }
}
