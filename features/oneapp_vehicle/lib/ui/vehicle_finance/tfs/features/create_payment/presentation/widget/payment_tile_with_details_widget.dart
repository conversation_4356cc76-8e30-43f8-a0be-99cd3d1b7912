// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PaymentTileWithDetailsWidget extends StatelessWidget {
  const PaymentTileWithDetailsWidget(this.label, this.listOfWidgets,
      {this.onTapAction});
  final List<Widget> listOfWidgets;
  final String label;
  final VoidCallback? onTapAction;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTapAction,
      child: Padding(
        padding: EdgeInsets.zero,
        child: Column(
          children: <Widget>[...listOfWidgets],
        ),
      ),
    );
  }
}
