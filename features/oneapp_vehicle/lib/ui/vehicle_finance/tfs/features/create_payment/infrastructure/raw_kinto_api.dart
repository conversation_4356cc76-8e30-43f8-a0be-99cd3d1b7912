// Package imports:
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/payment_kinto_id_response_dto.dart';

// Project imports:
import 'kinto_api.dart';

class RawKintoApi extends KintoApi {
  @override
  Future<CommonTfsResponse<PaymentKintoIdResponseDTO>> get({
    required String upid,
  }) async {
    final Map<String, String> input = <String, String>{'upid': upid};

    return await APIClients.tfsApiClient.getKintoData(
      body: input,
    );
  }
}
