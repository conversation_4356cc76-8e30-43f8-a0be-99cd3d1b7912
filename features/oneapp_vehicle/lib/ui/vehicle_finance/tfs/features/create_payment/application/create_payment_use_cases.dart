// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports:
import '../../dashboard/domain/account.dart';
import '../../payment_methods/domain/bank_account_info.dart';
import '../domain/payment_types.dart';
import 'create_payment_state.dart';

abstract class CreatePaymentUseCases extends StateNotifier<CreatePaymentState> {
  CreatePaymentUseCases(CreatePaymentState state) : super(state);

  Future<void> initiatePayment({
    required DateTime? paymentDate,
    required double paymentAmount,
    required Account account,
    required PaymentFrequency frequency,
    BankAccountInfo? defaultBank,
  });

  Future<void> submitPayment({
    required DateTime paymentDate,
    required double paymentAmount,
    required Account account,
    required BankAccountInfo defaultBank,
    required PaymentFrequency frequency,
    required String kintoServiceId,
    bool? isDuplicateAccepted,
  });
}
