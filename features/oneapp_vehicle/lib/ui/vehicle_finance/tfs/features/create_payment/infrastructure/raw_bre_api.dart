// Package imports:
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/payment_bre_request.dart';
import 'package:oa_network_impl/tfs/entity/payment_bre_response_dto.dart';

// Project imports:
import 'bre_api.dart';

class RawBreApi extends BreApi {
  @override
  Future<CommonTfsResponse<PaymentBreResponseDTO>> get({
    required String accountNumber,
    required String lastFourSSN,
  }) async {
    final body = PaymentBreRequest(
        fullAccountNumber: accountNumber, last4SSN: lastFourSSN);

    return await APIClients.tfsApiClient.getBreData(
      body: body,
    );
  }
}
