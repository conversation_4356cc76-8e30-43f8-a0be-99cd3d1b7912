// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';

// Project imports:
import '../domain/domain.dart';

part 'payment_bre_state.freezed.dart';

@freezed
class PaymentBreState with _$PaymentBreState {
  const factory PaymentBreState.initial() = _Initial;
  const factory PaymentBreState.loaded({
    required PaymentBre bre,
  }) = _Loaded;
  const factory PaymentBreState.error() = _Error;
}
