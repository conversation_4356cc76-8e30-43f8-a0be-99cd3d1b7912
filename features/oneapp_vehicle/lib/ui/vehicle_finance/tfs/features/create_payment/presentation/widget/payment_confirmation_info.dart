// Flutter imports:
import 'package:flutter/material.dart'; // Package imports:

// Package imports:
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../core/utils/extensions/extensions.dart';
import '../../../core/utils/utils.dart';
import '../../../dashboard/domain/account.dart';
import '../../../payment_history/domain/payment_history.dart';
import '../../../payment_history/domain/recurring_payment_plans.dart';
import 'payment_confirmation_card.dart';

class PaymentConfirmationInfo extends StatelessWidget {
  const PaymentConfirmationInfo({
    Key? key,
    this.account,
    required this.accountEnding,
    required this.transaction,
    this.recurringPlan,
    this.isFromTransactionHistory = false,
    this.isPlanCancelled = false,
    this.statusPill,
  }) : super(key: key);
  final Account? account;
  final String accountEnding;
  final Transaction transaction;
  final RecurringPlan? recurringPlan;
  final bool isFromTransactionHistory;
  final String? statusPill;
  final bool isPlanCancelled;

  @override
  Widget build(BuildContext context) {
    return Container(
        color: ThemeConfig.current().colorUtil.tertiary15,
        child: Column(
          children: <Widget>[
            const SizedBox(height: 20),
            PaymentConfirmationCard(
              account: account,
              payment: transaction,
              isFromTransactionHistory: isFromTransactionHistory,
              statusPill: getPillDisplay(),
            ),
            const SizedBox(height: 8),
            Padding(
                padding: const EdgeInsets.only(left: 10.0, right: 10),
                child: Column(children: <Widget>[
                  if (accountEnding.isNotEmpty) ...<Widget>{
                    customInfoItem(OneAppString.of().textPayFrom,
                        '${OneAppString.of().textBankEndingIn}: $accountEnding'),
                    const CustomDivider(indent: 20, endIndent: 20),
                  },
                  customInfoItem(
                    OneAppString.of().textDate,
                    getDisplayDate(),
                  ),
                  const CustomDivider(indent: 20, endIndent: 20),
                  customInfoItem(
                      OneAppString.of().textAmount,
                      TFSHelperFunctions.currencyFormat(
                          transaction.paymentAmountInDouble())),
                  const CustomDivider(indent: 20, endIndent: 20),
                  const SizedBox(height: 4.0),
                  const SizedBox(height: 10)
                ])),
          ],
        ));
  }

  String getPillDisplay() {
    if (statusPill != null) {
      return statusPill!;
    } else {
      if (isFromTransactionHistory) {
        return transaction.transactionStatus;
      } else if (transaction.isStatusPaymentCancelled()) {
        return isPlanCancelled
            ? OneAppString.of().textPlanCancelled
            : OneAppString.of().textPaymentCancelled;
      } else {
        return transaction.status.isEmpty
            ? OneAppString.of().textNa
            : transaction.displayStatus();
      }
    }
  }

  String getDisplayDate() {
    final DateTime? _date =
        DateFormatterUtil.formatStringDateWithDashesPatternInDateTime(
            transaction.postDate);
    final String? _frequencyType = recurringPlan?.frequencyType;
    return (recurringPlan?.period.isNotEmpty ?? false)
        ? DateFormatterUtil.formatStringDateToFullMonthDateYear(
                _date ?? DateTime.now()) +
            (_frequencyType != null ? ', $_frequencyType' : '')
        : transaction.isPaymentOneTime()
            ? DateFormatterUtil.formatStringDateToFullMonthDateYear(
                DateFormatterUtil.formatStringDateWithDashesPatternInDateTime(
                        transaction.postDate) ??
                    DateTime.now())
            : DateFormatterUtil.formatStringDateToFullMonthDateYear(
                    _date ?? DateTime.now()) +
                ', ${transaction.frequency}';
  }

  Widget customInfoItem(String label, String content) {
    return Builder(
      builder: (BuildContext ctx) {
        return Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 10, 0, 14),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  label,
                  style: ThemeConfig.current()
                      .textStyleUtil
                      .body4
                      .withColor(ThemeConfig.current().colorUtil.tertiary03),
                ),
                Text(
                  content,
                  style: ThemeConfig.current()
                      .textStyleUtil
                      .callout1
                      .withColor(ThemeConfig.current().colorUtil.tertiary05),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
