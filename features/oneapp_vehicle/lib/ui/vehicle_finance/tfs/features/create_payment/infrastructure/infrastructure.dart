// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports:
import 'raw_bre_api.dart';
import 'raw_create_payment_api.dart';
import 'raw_kinto_api.dart';
import 'raw_payment_parameters_api.dart';

export 'raw_create_payment_api.dart';
export 'raw_payment_parameters_api.dart';

final createPaymentApiProvider = Provider<RawCreatePaymentApi>(
  (ref) => RawCreatePaymentApi(),
);

final paymentParameterApiProvider = Provider<RawPaymentParameterApi>(
  (ref) => RawPaymentParameterApi(),
);

final kintoApiProvider = Provider<RawKintoApi>(
  (ref) => RawKintoApi(),
);

final breApiProvider = Provider<RawBreApi>(
  (ref) => RawBreApi(),
);
