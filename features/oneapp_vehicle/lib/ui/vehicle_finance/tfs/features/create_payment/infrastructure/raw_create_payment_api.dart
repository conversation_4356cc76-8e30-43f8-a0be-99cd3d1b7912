// Package imports:
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/create_payment_request.dart';
import 'package:oa_network_impl/tfs/entity/create_payment_response_dto.dart';

// Project imports:
import '../../../services/firebase_service.dart';
import '../domain/payment_request.dart';
import 'create_payment_api.dart';

class RawCreatePaymentApi extends CreatePaymentApi {
  @override
  Future<CommonTfsResponse<CreatePaymentResponseDTO>> post(
      PaymentRequest request) async {
    try {
      CreatePaymentRequest requestBody = CreatePaymentRequest(
          upid: request.upid,
          paymentType: request.paymentType,
          accountId: request.accountId,
          accountNumber: request.accountNumber,
          paymentMethodId: request.paymentMethodId,
          paymentMethod: request.paymentMethod,
          achAccountType: request.achAccountType,
          achAbaCode: request.achAbaCode,
          paymentAmount: request.paymentAmount,
          achAccountNumberMask: request.achAccountNumberMask,
          accountType: request.accountType,
          startDate: request.startDate,
          processPayment: request.processPayment,
          frequency: request.frequency,
          comments: request.comments,
          kintoServiceId: request.kintoServiceId,
          endDate: request.endDate,
          allowDuplicate: request.allowDuplicate);
      return await APIClients.tfsApiClient
          .createPayment(requestBody: requestBody);
    } catch (e) {
      FirebaseService().setFireBaseNonFatalErrors(exception: e);
      throw e;
    }
  }
}
