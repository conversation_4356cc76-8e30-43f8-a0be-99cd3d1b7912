// Package imports:
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/payment_kinto_id_response_dto.dart';

// Project imports:
import '../../../services/firebase_service.dart';
import '../../core/domain/api_failure.dart';
import '../../core/utils/utils.dart';
import '../domain/domain.dart';
import '../infrastructure/kinto_api.dart';

class DefaultKintoRepo extends KintoRepo {
  DefaultKintoRepo(this.api);

  final KintoApi api;

  @override
  Future<Result<Kinto, ApiFailure>> getKintoData({
    required String upid,
  }) async {
    try {
      final CommonTfsResponse<PaymentKintoIdResponseDTO> kintoResponse =
          await api.get(upid: upid);
      if (kintoResponse.response != null) {
        Kinto kintoData =
            Kinto.getKintoResponse(upid, kintoResponse.response!.customer);
        return Result.success(kintoData);
      } else {
        return Result.failure(
          ApiFailure.network(
            errorMessage: ErrorMessage.handleError(kintoResponse.error),
          ),
        );
      }
    } catch (e) {
      FirebaseService().setFireBaseNonFatalErrors(exception: e);
      return Result.failure(
        ApiFailure.network(
          errorMessage: ErrorMessage.handleError(e),
        ),
      );
    }
  }
}
