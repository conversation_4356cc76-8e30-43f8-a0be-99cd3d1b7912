// Package imports:

// Project imports:
import '../../dashboard/domain/account.dart';
import '../domain/domain.dart';
import 'payment_bre_state.dart';
import 'payment_bre_use_cases.dart';

class PaymentBreLogic extends PaymentBreUseCase {
  PaymentBreLogic({required this.kintoRepo, required this.breRepo})
      : super(const PaymentBreState.initial());

  final KintoRepo kintoRepo;
  final BreRepo breRepo;

  PaymentBre? breData;

  @override
  Future<void> getBreResponse(
      {required Account account, bool? loadCacheData}) async {
    final result = await kintoRepo.getKintoData(
      upid: account.customer.ucid,
    );

    result.when(success: (Kinto kinto) async {
      if (breData != null && (loadCacheData ?? false)) {
        state = PaymentBreState.loaded(bre: breData!);
      } else {
        final result = await breRepo.getBreData(
          accountNumber: account.fullAccountNumber,
          last4SSN: kinto.ssn,
        );

        result.when(success: (_breData) async {
          breData = _breData;
          state = PaymentBreState.loaded(bre: _breData);
        }, failure: (_) {
          state = const PaymentBreState.error();
        });
      }
    }, failure: (_) {
      state = const PaymentBreState.error();
    });
  }
}
