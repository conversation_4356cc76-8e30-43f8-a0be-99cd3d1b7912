// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

// Package imports:
import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:table_calendar/table_calendar.dart';

// Project imports:
import '../../../../router/navigation_service.dart';
import '../../../../router/routes.dart';
import '../../../../services/locators.dart';
import '../../../core/utils/extensions/datetime_extensions.dart';
import '../../../core/utils/tfs_theme_constants.dart';
import '../../../core/utils/utils.dart';
import '../../../dashboard/domain/account.dart';
import '../../../payment_history/application/application.dart';
import '../../../payment_history/domain/payment_history.dart';
import '../../../payment_history/domain/payment_history_failure.dart';
import '../../application/application.dart';
import '../../domain/calendar_event_model.dart';
import '../../domain/domain.dart';
import '../../domain/payment_types.dart';
import 'amount_entry_screen.dart';
import 'custom_calendar_header.dart';

class CustomCalendarArgs {
  const CustomCalendarArgs({
    required this.account,
    required this.onDateSelected,
    required this.previouslySelectedDate,
    this.greyOutTodayDate = false,
    this.postedDate,
    this.fromScreen,
    required this.frequency,
    required this.onAmountChanged,
    this.isAmountChanged,
  });
  final Account account;
  final bool greyOutTodayDate;
  final DateTime? postedDate;
  final DateTime? previouslySelectedDate;
  final String? fromScreen;
  final Function(DateTime, PaymentFrequency) onDateSelected;
  final PaymentFrequency frequency;
  final Function(double amount)? onAmountChanged;
  final bool? isAmountChanged;
}

class CustomCalendarWidget extends ConsumerStatefulWidget {
  const CustomCalendarWidget({
    required this.args,
  });
  final CustomCalendarArgs args;

  @override
  CustomCalendarWidgetState createState() => CustomCalendarWidgetState();
}

class CustomCalendarWidgetState extends ConsumerState<CustomCalendarWidget> {
  ValueNotifier<List<Event>>? _selectedEvents;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime? _selectedDay;
  DateTime? _rangeStart;
  DateTime? _rangeEnd;
  bool previewButton = false, nextButton = true;
  List<Event>? kEvents;
  DateTime? _focusedDay;
  DateTime? _previouslySelectedDate;
  late DateTime kToday;
  late DateTime kLastDay;
  int count = 0;
  PageController? _pageController;
  List<Transaction>? listOfScheduledPayments;
  List<DateTime> upcomingHolidaysList = <DateTime>[];
  late ColorUtil _colorUtil;
  late TextStyleUtil _textStyleUtil;
  late bool isRecurringPayment;

  @override
  void initState() {
    isRecurringPayment = widget.args.frequency != PaymentFrequency.oneTime;
    _previouslySelectedDate = widget.args.previouslySelectedDate;
    kToday = DateFormatterUtil.getTodayTimeInCST();
    kLastDay = DateFormatterUtil.calendarLastDay();
    _focusedDay = DateFormatterUtil.getNextAvailableDay();
    _updatePaymentParameters();
    super.initState();
    getAllPayments();
    isTodayTimePastFivePmCst(kToday);
  }

  void _updatePaymentParameters() {
    ref.read(paymentParameterStateProvider).maybeWhen(
        loaded: (PaymentParameter parameters) {
          kLastDay = parameters.calendarLastDay;
          upcomingHolidaysList = parameters.holidayList;
          if (isRecurringPayment) {
            _focusedDay = widget.args.postedDate ??
                parameters.nextAvailableDay(includeWeekends: true);
            upcomingHolidaysList.add(kToday.utcDate());
            upcomingHolidaysList.addAll(DateFormatterUtil.monthLast3Days());
          } else {
            _focusedDay = widget.args.postedDate ?? parameters.paymentDate;

            if (widget.args.greyOutTodayDate ||
                !DateFormatterUtil.isSameDateMatched(parameters.paymentDate) ||
                DateFormatterUtil.checkIfDateIsPastPaymentCutOff()) {
              upcomingHolidaysList.add(kToday.utcDate());
              if (DateFormatterUtil.isSameDay(_focusedDay!)) {
                _focusedDay =
                    parameters.nextAvailableDay(includeWeekends: false);
              }
            }
          }
        },
        orElse: () {});
  }

  void _fetchPaymentHistory() {
    final fullAccountNumber = widget.args.account.fullAccountNumber;
    ref
        .read(paymentHistoryProvider.notifier)
        .fetchPaymentHistory(fullAccountNumber: fullAccountNumber);
  }

  Future<void> getAllPayments() async {
    ref.read(paymentHistoryProvider).maybeWhen(
        success: (PaymentHistory response) {
          if (response.error != null) {
            _fetchPaymentHistory();
          }
        },
        failure: (PaymentHistoryFailure error) {},
        orElse: () {
          _fetchPaymentHistory();
        });
  }

  Future<void> isTodayTimePastFivePmCst(DateTime date) async {
    _selectedDay = _focusedDay;
    previewButton =
        _focusedDay!.isAfter(kToday) && (_focusedDay!.month != kToday.month);
    nextButton = kLastDay.month != _focusedDay!.month;
    _selectedEvents =
        ValueNotifier<List<Event>>(_getEventsForDay(_selectedDay!));
  }

  @override
  void dispose() {
    _selectedEvents?.dispose();
    super.dispose();
  }

  List<Event> _getEventsForDay(DateTime day) {
    if (kEvents != null) {
      return kEvents!
          .where((Event element) => element.dateTime == day)
          .toList();
    }
    return <Event>[];
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (upcomingHolidaysList.contains(selectedDay.utcDate())) {
      return;
    }
    if (widget.args.frequency == PaymentFrequency.oneTime &&
        (selectedDay.weekday == DateTime.sunday ||
            selectedDay.weekday == DateTime.saturday)) {
      return;
    }
    if (mounted) {
      setState(
        () {
          _focusedDay = focusedDay;
          _selectedDay = selectedDay;
          _previouslySelectedDate = selectedDay;
          _rangeStart = null;
          _rangeEnd = null;
        },
      );
      _selectedEvents ??=
          ValueNotifier<List<Event>>(_getEventsForDay(_selectedDay ?? kToday));
    }
  }

  bool _getHolidays(DateTime day) {
    return upcomingHolidaysList.contains(day.utcDate());
  }

  List<Event> buildScheduledEventsOnCalendar(List<Transaction> listOfPayments) {
    DateTime? accountPastDueDate;
    kEvents = <Event>[];
    if (listOfPayments.isNotEmpty) {
      for (final Transaction payment in listOfPayments) {
        if (payment.postDate.isNotEmpty) {
          final DateTime? date = payment.formattedDate();
          if (date != null) {
            final Event? isEventPreviouslyAddedTrue = kEvents!.firstWhereOrNull(
                (Event element) => date.utcDate() == element.dateTime);
            if (isEventPreviouslyAddedTrue == null) {
              kEvents!.add(Event(0, date.utcDate()));
            }
          }
        }
      }
    }
    if (widget.args.account.paymentdueDate.isNotEmpty) {
      accountPastDueDate = widget.args.account.dateDueDateTime();
      if (accountPastDueDate != null) {
        kEvents!.add(Event(1, accountPastDueDate.utcDate()));
      }
    }
    return kEvents ?? <Event>[];
  }

  @override
  Widget build(BuildContext context) {
    _colorUtil = ThemeConfig.current().colorUtil;
    _textStyleUtil = ThemeConfig.current().textStyleUtil;
    return ref.watch(paymentHistoryProvider).maybeWhen(
        success: (PaymentHistory history) {
      listOfScheduledPayments = history.listOfPaymentsInScheduledStatus();
      if (listOfScheduledPayments != null && kEvents == null) {
        kEvents = buildScheduledEventsOnCalendar(listOfScheduledPayments!);
      }
      if (listOfScheduledPayments != null) {
        return tableCalendarWidget();
      } else {
        return OverlaySpinner(child: tableCalendarWidget());
      }
    }, failure: (_) {
      return tableCalendarWidget();
    }, orElse: () {
      return OverlaySpinner(child: tableCalendarWidget());
    });
  }

  void _onLeftChevronTap() {
    if (previewButton) {
      _pageController?.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _onRightChevronTap() {
    if (nextButton) {
      _pageController?.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Widget tableCalendarWidget() {
    return BaseScaffold(
        label: OneAppString.of().textSelectDate,
        isRemoveLineTop: true,
        body: Column(
          children: <Widget>[
            Expanded(
                child: ListView(children: <Widget>[
              const SizedBox(height: 15),
              ValueListenableBuilder<DateTime>(
                valueListenable: ValueNotifier<DateTime>(_focusedDay!),
                builder: (BuildContext context, DateTime value, _) {
                  return calendarHeader(value);
                },
              ),
              Container(
                decoration: BoxDecoration(
                  borderRadius: borderCircularRadius,
                  color: _colorUtil.tile02,
                ),
                margin: const EdgeInsets.only(left: 16.0, right: 16.0),
                child: TableCalendar<Event>(
                  firstDay: kToday,
                  lastDay: kLastDay,
                  availableCalendarFormats: const <CalendarFormat, String>{
                    CalendarFormat.month: 'Month',
                  },
                  focusedDay: _focusedDay ?? kToday,
                  selectedDayPredicate: (DateTime day) =>
                      isSameDay(_selectedDay, day),
                  rangeStartDay: _rangeStart,
                  rangeEndDay: _rangeEnd,
                  calendarFormat: _calendarFormat,
                  eventLoader: _getEventsForDay,
                  daysOfWeekStyle: DaysOfWeekStyle(
                    weekdayStyle: _textStyleUtil.caption1
                        .copyWith(color: _colorUtil.tertiary07),
                    weekendStyle: _textStyleUtil.caption1
                        .copyWith(color: _colorUtil.tertiary07),
                  ),
                  calendarStyle: CalendarStyle(
                    defaultTextStyle: _textStyleUtil.callout1
                        .copyWith(color: _colorUtil.button02a),
                    holidayDecoration:
                        const BoxDecoration(shape: BoxShape.circle),
                    holidayTextStyle: _textStyleUtil.callout1
                        .copyWith(color: _colorUtil.tertiary07),
                    disabledTextStyle: _textStyleUtil.callout1
                        .copyWith(color: _colorUtil.tertiary07),
                    weekendTextStyle: _textStyleUtil.callout1.copyWith(
                        color: isRecurringPayment
                            ? _colorUtil.button02a
                            : _colorUtil.tertiary07),
                    todayDecoration: const BoxDecoration(),
                    todayTextStyle: _textStyleUtil.callout1,
                    markersAutoAligned: false,
                    selectedDecoration: BoxDecoration(
                      color: _colorUtil.button02a,
                      shape: BoxShape.circle,
                    ),
                    canMarkersOverflow: false,
                  ),
                  onDaySelected: _onDaySelected,
                  onFormatChanged: (CalendarFormat format) {
                    if (_calendarFormat != format) {
                      setState(() => _calendarFormat = format);
                    }
                  },
                  onHeaderTapped: (_) {},
                  onCalendarCreated: (PageController pageController) {
                    SchedulerBinding.instance.addPostFrameCallback((_) {
                      _pageController = pageController;
                    });
                  },
                  headerVisible: false,
                  onPageChanged: (DateTime focusedDay) {
                    _focusedDay = focusedDay;
                    if (kToday.month == focusedDay.month) {
                      previewButton = false;
                    } else {
                      previewButton = true;
                    }
                    if (kLastDay.month == focusedDay.month) {
                      nextButton = false;
                    } else {
                      nextButton = true;
                    }
                    setState(() {});
                  },
                  holidayPredicate: (DateTime day) => _getHolidays(day),
                  calendarBuilders: CalendarBuilders<Event>(
                    singleMarkerBuilder:
                        (BuildContext context, DateTime date, Event event) =>
                            calendarBuilder(event),
                    todayBuilder: (BuildContext context, DateTime date, _) =>
                        todayBuilder(date),
                    holidayBuilder: (BuildContext context, DateTime date, _) =>
                        holidayBuilder(date),
                    selectedBuilder: (BuildContext context, DateTime date, _) =>
                        selectedBuilder(date),
                  ),
                ),
              ),
              const SizedBox(height: 15.0),
              showMarkers(),
            ])),
            continueButton(),
            const SizedBox(height: 25),
          ],
        ));
  }

  Widget calendarHeader(DateTime value) {
    return CustomCalendarHeader(
      leftChevronIcon: SizedBox(
          key: const Key('chevron_left'),
          height: 12,
          width: 6,
          child: Icon(
            Icons.chevron_left,
            color:
                previewButton ? _colorUtil.tertiary03 : _colorUtil.tertiary07,
          )),
      rightChevronIcon: SizedBox(
          key: const Key('chevron_right'),
          height: 12,
          width: 6,
          child: Icon(
            Icons.chevron_right,
            color: nextButton ? _colorUtil.tertiary03 : _colorUtil.tertiary07,
          )),
      focusedMonth: value,
      onTapLeftChevron: _onLeftChevronTap,
      onTapRightChevron: _onRightChevronTap,
    );
  }

  Widget calendarBuilder(Event event) {
    return Container(
      key:
          Key(event.type == 0 ? 'blueCircle${count++}' : 'redCircle${count++}'),
      decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: event.type == 0 ? _colorUtil.success01 : _colorUtil.button03d),
      width: 8.0,
      height: 8.0,
      margin: const EdgeInsets.only(bottom: 4),
    );
  }

  Widget showMarkers() {
    return Container(
      margin: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Container(
                decoration: BoxDecoration(
                    shape: BoxShape.circle, color: _colorUtil.success01),
                width: 8.0,
                height: 8.0,
                margin: const EdgeInsets.symmetric(horizontal: 10),
              ),
              Text(OneAppString.of().textScheduledPaymentDate,
                  style: _textStyleUtil.caption1),
            ],
          ),
          const SizedBox(height: 10),
          Container(
            margin: const EdgeInsets.only(right: 10.0),
            child: Row(
              children: <Widget>[
                Container(
                  decoration: BoxDecoration(
                      shape: BoxShape.circle, color: _colorUtil.button03d),
                  width: 8.0,
                  height: 8.0,
                  margin: const EdgeInsets.symmetric(horizontal: 10),
                ),
                Text(OneAppString.of().textNextDueDate,
                    style: _textStyleUtil.caption1),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget continueButton() {
    return Semantics(
      container: true,
      child: PrimaryButton(
        label: OneAppString.of().textContinue,
        isDisabled: _previouslySelectedDate == null,
        onTap: () {
          _onContinueButton();
        },
      ),
    );
  }

  Widget todayBuilder(DateTime date) {
    return Center(
        child: Text('${date.day}',
            style: _getHolidays(date)
                ? _textStyleUtil.callout1.copyWith(color: _colorUtil.tertiary07)
                : _textStyleUtil.callout1
                    .copyWith(color: _colorUtil.button02a)));
  }

  Widget holidayBuilder(DateTime date) {
    return Center(
      child: Text(
        '${date.day}',
        style: _textStyleUtil.callout1.copyWith(color: _colorUtil.tertiary07),
      ),
    );
  }

  Widget selectedBuilder(DateTime date) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 250),
      margin: const EdgeInsets.all(6.0),
      padding: EdgeInsets.zero,
      decoration: date.day == kToday.day &&
              date.month == kToday.month &&
              date.year == kToday.year &&
              (_getHolidays(date))
          ? const BoxDecoration()
          : _previouslySelectedDate == null && widget.args.postedDate == null
              ? BoxDecoration(
                  color: _colorUtil.button02b,
                  border: const Border.symmetric(
                    vertical: BorderSide(
                      width: 1.25,
                    ),
                    horizontal: BorderSide(
                      width: 1.25,
                    ),
                  ),
                  shape: BoxShape.circle,
                )
              : BoxDecoration(
                  color: _colorUtil.button02a,
                  shape: BoxShape.circle,
                ),
      alignment: Alignment.center,
      child: Text(
        '${date.day}',
        style: date.day == kToday.day &&
                date.month == kToday.month &&
                date.year == kToday.year &&
                (_getHolidays(date))
            ? _textStyleUtil.callout1.copyWith(color: _colorUtil.button02a)
            : _previouslySelectedDate == null && widget.args.postedDate == null
                ? _textStyleUtil.callout1.copyWith(color: _colorUtil.button02a)
                : _textStyleUtil.callout1.copyWith(color: _colorUtil.button02b),
      ),
    );
  }

  void _onContinueButton() {
    if (widget.args.fromScreen == OneAppString.of().textEditPayment) {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_EDIT_PAYMENT_SELECT_DATE);
    } else if (widget.args.fromScreen == OneAppString.of().textMakeAPayment) {
      FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
          VehicleAnalyticsEvent.TFS_PAYMENT_SELECT_DATE);
    }
    if (_selectedDay != null) {
      Navigator.pop(context);
      if (widget.args.frequency != PaymentFrequency.oneTime &&
          widget.args.isAmountChanged == false) {
        getIt<TFSNavigationService>().pushNamed(
            Routes.recurringPaymentAmountEntryScreen,
            arguments: AmountEntryArgs(
                paymentDate: _selectedDay,
                frequency: widget.args.frequency,
                account: widget.args.account,
                fromScreen: OneAppString.of().textMakeAPayment,
                onAmountChanged: (double amount) => {
                      if (widget.args.onAmountChanged != null)
                        {widget.args.onAmountChanged!(amount)}
                    },
                selectedDate: (selectedDay, focusedDay) => {
                      if (selectedDay != null)
                        {
                          widget.args.onDateSelected(selectedDay, focusedDay),
                        }
                    }));
      } else {
        widget.args.onDateSelected(_selectedDay!, widget.args.frequency);
      }
    }
  }
}
