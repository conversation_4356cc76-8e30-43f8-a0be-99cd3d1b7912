// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:oa_network_impl/tfs/entity/payment_bre_response_dto.dart';

// Project imports:
import '../../../services/locators.dart';

part 'payment_bre.freezed.dart';

@freezed
class PaymentBre with _$PaymentBre {
  const factory PaymentBre({
    required bool paymentEnabled,
    required PaymentType paymentType,
  }) = _PaymentBre;

  factory PaymentBre.fromDomain(PaymentOptionsDTO paymentOptions) {
    bool oneTimeDisabled = getIt<AppInitialValues>().OneTimePaymentDisabled;
    bool recurringDisabled = getIt<AppInitialValues>().RecurringPaymentDisabled;
    return PaymentBre(
        paymentEnabled: oneTimeDisabled && recurringDisabled
            ? false
            : paymentOptions.paymentEnabled,
        paymentType: paymentOptions.paymentType != null
            ? PaymentType(
                oneTime: oneTimeDisabled
                    ? false
                    : (paymentOptions.paymentType!.oneTime),
                principalOnly: paymentOptions.paymentType!.principalOnly,
                recurring: recurringDisabled
                    ? false
                    : (paymentOptions.paymentType!.recurring),
                regular: paymentOptions.paymentType!.regular,
                retailPayOff: paymentOptions.paymentType!.retailPayOff,
                rpp: paymentOptions.paymentType!.rpp,
              )
            : PaymentType.empty());
  }
}

@freezed
class PaymentType with _$PaymentType {
  const factory PaymentType({
    required bool retailPayOff,
    required bool regular,
    required bool principalOnly,
    required bool rpp,
    required bool oneTime,
    required bool recurring,
  }) = _PaymentType;

  factory PaymentType.empty() {
    return PaymentType(
      oneTime: false,
      principalOnly: false,
      recurring: false,
      regular: false,
      retailPayOff: false,
      rpp: false,
    );
  }
}
