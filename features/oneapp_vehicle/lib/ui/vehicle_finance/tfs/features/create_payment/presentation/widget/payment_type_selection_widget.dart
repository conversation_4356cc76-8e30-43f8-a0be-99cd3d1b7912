// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';

// Project imports:
import '../../../../../../../log/vehicle_analytic_event.dart';
import '../../../core/utils/widgets/custom_button.dart';
import '../../../core/utils/widgets/generic_bottomsheets.dart';
import '../../domain/payment_types.dart';
import 'frequency_selection_buttons_widget.dart';

class PaymentTypeSelectionWidget {
  PaymentTypeSelectionWidget({
    required this.context,
    required this.loadPaymentServices,
    required this.onTapActionOfFrequencyButtons,
    required this.showRecurringButton,
    required this.showOneTimeButton,
    required this.paymentEnabled,
    required this.buttonLabel,
  });
  final GlobalKey _key = GlobalKey();
  OverlayEntry? _overlayEntry;
  TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  final Function loadPaymentServices;
  final Function(PaymentFrequency frequency, OverlayEntry? overlayEntry)
      onTapActionOfFrequencyButtons;
  final bool? showRecurringButton;
  final bool? showOneTimeButton;
  final BuildContext context;
  final bool? paymentEnabled;
  final String buttonLabel;

  Widget frequencyPaymentButton() {
    return CustomButton(
      key: _key,
      disabled: !(paymentEnabled ?? false),
      onPressedAllowedOnDisabledButton: !(paymentEnabled ?? false),
      label: Text(
        buttonLabel,
        style: TextStyleExtension().newStyleWithColor(
            _textStyleUtil.callout2,
            !(paymentEnabled ?? false)
                ? _colorUtil.button05a
                : _colorUtil.primaryButton01),
      ),
      onPressed: () async {
        if (paymentEnabled == null) {
          GenericBottomSheets.showGenericErrorBottomSheet(
            context,
            error: OneAppString.of().textServerError,
          );
          loadPaymentServices();
        } else if (!paymentEnabled!) {
          GenericBottomSheets.showGenericErrorBottomSheet(
            context,
            error: OneAppString.of().textPaymentNotAllowed,
          );
        } else {
          final RenderBox? renderBox =
              _key.currentContext?.findRenderObject() as RenderBox?;
          final Offset? position = renderBox?.localToGlobal(Offset.zero);
          FireBaseAnalyticsLogger.logTFSPaymentGroupEvent(
              VehicleAnalyticsEvent.TFS_SELECT_PMT_TYPE_CTA);

          showOverlay(position!);
        }
      },
    );
  }

  void showOverlay(Offset position) {
    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) {
        return GestureDetector(
          onTap: () => _overlayEntry?.remove(),
          child: Material(
            color: _colorUtil.barrierColor,
            child: Stack(
              children: <Widget>[
                SizedBox(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                ),
                Positioned(
                  top: position.dy,
                  left: position.dx,
                  child: FrequencySelectionButtons(
                    showRecurringButton: showRecurringButton,
                    showOneTimeButton: showOneTimeButton,
                    removeOverlay: removeOverlay,
                    onTapActionOnError: () => loadPaymentServices(),
                    onTapAction: (PaymentFrequency frequency) =>
                        onTapActionOfFrequencyButtons(frequency, _overlayEntry),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  void removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
