// Package imports:
import 'package:collection/collection.dart';
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network/configs/service_config.dart';

// Project imports:
import '../../../config/base_config.dart';
import '../../../services/locators.dart';
import '../../authentication/domain/access_token_repo.dart';
import '../../authentication/domain/authorize_failure.dart';
import '../../authentication/domain/authorize_response.dart';
import 'refresh_token_state.dart';
import 'refresh_token_use_case.dart';

class RefreshTokenLogic extends RefreshTokenUseCases {
  RefreshTokenLogic({
    required this.repo,
  }) : super(const RefreshTokenState.initial());

  final AccessTokenRepo repo;
  @override
  Future<void> getRefreshToken() async {
    final Result<AuthorizeResponse, AuthorizeFailure> result =
        await repo.callRefreshToken(
            clientId: getIt<BaseConfig>().properties.clientId,
            clientSecret: ServiceConfig.keys.tfsFrClientSecret);
    result.when(success: (success) {
      state = RefreshTokenState.success(authorizeResponse: success);
    }, failure: (failure) {
      if (_getTokenError(failure.authorizeResponse.error)) {
        state = const RefreshTokenState.sessionExpired();
      } else {
        state = RefreshTokenState.serverError(
            errorMessage: failure.authorizeResponse.error ?? '');
      }
    });
  }

  bool _getTokenError(String? errorMessage) {
    if ((errorMessage != null && errorMessage.isNotEmpty) &&
        (_validateInvalidGrant(errorMessage)?.isNotEmpty ?? false)) {
      return true;
    }
    return false;
  }

  static List<String> errorStrings = [
    'invalid grant',
    'invalid_grant',
    'invalid scope',
    'invalid_scope',
  ];

  String? _validateInvalidGrant(String error) => errorStrings.firstWhereOrNull(
      (element) => error.toLowerCase().contains(element.toLowerCase()));
}
