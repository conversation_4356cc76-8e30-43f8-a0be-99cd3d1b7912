// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network_impl/core/extension_helper.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../../services/locators.dart';
import '../../core/domain/api_failure.dart';
import '../../core/domain/tfs_error_code.dart';
import '../../core/utils/extensions/extensions.dart';
import '../../core/utils/utils.dart';
import '../data_access/data_access.dart';
import '../domain/account.dart';
import '../domain/account_summary.dart';
import '../domain/account_summary_failure.dart';
import '../domain/account_summary_repo.dart';
import '../domain/eboc_agreement_response.dart';
import '../domain/eboc_agreement_response_failure.dart';
import '../domain/eboc_agreement_response_repo.dart';
import '../domain/pair_accounts_failure.dart';
import '../domain/pair_accounts_response.dart';
import '../domain/server_timestamp.dart';
import '../domain/server_timestamp_repo.dart';
import '../domain/unregistered_accounts_failure.dart';
import '../domain/unregistered_accounts_list.dart';
import 'account_summary_state.dart';
import 'account_summary_use_cases.dart';
import 'application.dart';

class AccountSummaryLogic extends AccountSummaryUseCases {
  AccountSummaryLogic({
    required this.repo,
    required this.ebocRepo,
    required this.ref,
    required this.repoServerTimestamp,
  }) : super(const AccountSummaryState.initial());

  final AccountSummaryRepo repo;
  final EbocAgreementResponseRepo ebocRepo;
  final ServerTimestampRepo repoServerTimestamp;
  final Ref ref;

  @override
  Future<void> getAccountSummaryDetails() async {
    final Result<AccountSummary, AccountSummaryFailure> accountSummaryResult =
        await repo.getAccountSummary();
    accountSummaryResult.when(success: (AccountSummary accountSummary) async {
      final Account? account = accountSummary.getAccountFromVin(
          vinNumber: getIt<AppInitialValues>().vin);
      if (account == null) {
        final Account? firstAccount = accountSummary.getFirstAccountInList();
        if (firstAccount == null) {
          state = const AccountSummaryState.isAccountNotFound();
        } else {
          await getAccountsListToPair(firstAccount: firstAccount);
        }
      } else if (account.status.isOpen()) {
        state = AccountSummaryState.success(
            account: account, accountSummary: accountSummary);
      } else {
        state = const AccountSummaryState.isAccountClosed();
      }
    }, failure: (AccountSummaryFailure accountSummaryFailure) {
      if (AccountSummaryFailure.ebocError() == accountSummaryFailure) {
        state = const AccountSummaryState.isShowEBOCAgreementDialog();
      } else if (AccountSummaryFailure.inActiveVehicleAccount() ==
          accountSummaryFailure) {
        state = const AccountSummaryState.isInactiveVehicleAccount();
      } else {
        state = const AccountSummaryState.error();
      }
    });
  }

  @override
  Future<void> getAccountsListToPair({required Account firstAccount}) async {
    await ref
        .read(unregisteredAccountsStateProvider.notifier)
        .getListOfUnregisteredAccounts(
            existingAccount: firstAccount.fullAccountNumber,
            customerType: firstAccount.customer.customerType);
    ref.read(unregisteredAccountsStateProvider).maybeWhen(
        success: (UnregisteredAccountsList unregisteredAccountsList) async {
          if (unregisteredAccountsList.response.accountList.isNotEmpty) {
            await pairAccountForVin(
                fullAccountNumber: firstAccount.fullAccountNumber,
                unregisteredAccountsList: unregisteredAccountsList);
          } else {
            if ((unregisteredAccountsList.errors.isNotEmpty) &&
                unregisteredAccountsList.errors.first.code ==
                    TFSErrorCode.eef0253) {
              state = const AccountSummaryState.error();
            } else {
              state = AccountSummaryState.accountCannotBeAdded(
                  message: OneAppString.of().textAccountCannotBeAdded);
            }
          }
        },
        failure: (UnregisteredAccountsFailure failure) {
          state = const AccountSummaryState.error();
        },
        orElse: () {});
  }

  @override
  Future<void> pairAccountForVin(
      {required String fullAccountNumber,
      required UnregisteredAccountsList unregisteredAccountsList}) async {
    PairAccountsResponse? pairAccountsResponse;
    PairAccountsFailure? pairAccountsFailure;
    for (int i = 0;
        i < unregisteredAccountsList.response.accountList.length;
        i++) {
      if (pairAccountsResponse?.status.toBoolean() ?? false) {
        break;
      }
      final String vin = getIt<AppInitialValues>().vin;

      final String vinNumber = vin.lastChars(8);
      final Result<PairAccountsResponse, PairAccountsFailure> result =
          await ref.read(pairAccountsRepoProvider).postAccountToPair(
                addedAccount: fullAccountNumber,
                encryptedAccountNumber: unregisteredAccountsList
                    .response.accountList[i].encryptedAccountNumber,
                last8vin: vinNumber,
                customerType: unregisteredAccountsList
                    .response.accountList[i].customerType,
              );
      result.when(success: (PairAccountsResponse _pairAccountsResponse) {
        pairAccountsResponse = _pairAccountsResponse;
      }, failure: (PairAccountsFailure _pairAccountsFailure) {
        pairAccountsFailure = _pairAccountsFailure;
      });
    }
    if (pairAccountsResponse?.status.toBoolean() ?? false) {
      await getAccountSummaryDetails();
    } else if (pairAccountsFailure ==
        PairAccountsFailure.accountCannotBeAdded()) {
      state = AccountSummaryState.accountCannotBeAdded(
          message: OneAppString.of().textAccountCannotBeAdded);
    } else if (pairAccountsFailure ==
        PairAccountsFailure.unableToLocateAccount()) {
      state = AccountSummaryState.isAccountNotFound();
    } else if (pairAccountsFailure ==
        PairAccountsFailure.borrowerOrCoBorrowerEligibility()) {
      state = AccountSummaryState.accountCannotBeAdded(
          message: OneAppString.of().textEligibilityOfBorrowerCoBorrower);
    } else {
      state = AccountSummaryState.accountCannotBeAdded(
          message: OneAppString.of().textWeAreCurrentlyExperiencingIssues);
    }
  }

  @override
  Future<void> getTimeStampAndAcceptEboc() async {
    try {
      final Result<ServerTimestamp, ApiFailure> timestampServerResult =
          await repoServerTimestamp.getServerTimestamp();
      await timestampServerResult.when(
          success: (ServerTimestamp timestampResponse) async {
        String _date = timestampResponse.serverDateTime;
        if (_date.isEmpty) {
          state = const AccountSummaryState.error();
        } else {
          await acceptEboc(_date);
        }
      }, failure: (_) {
        state = const AccountSummaryState.error();
      });
    } catch (e) {
      state = const AccountSummaryState.error();
    }
  }

  Future<void> acceptEboc(String date) async {
    final Result<EbocAgreementResponse, EbocAgreementResponseFailure>
        ebocAgreementResponseResult =
        await ebocRepo.postEbocAgreementIsAccepted(
            ebaFlag: 'Y',
            ocFlag: 'Y',
            browserVersion: DeviceAndPackageInfoUtil.platform ?? 'Android',
            ocTimeStamp: date,
            ebaTimeStamp: date,
            browserTimeStamp: date,
            clientInfo:
                '${DeviceAndPackageInfoUtil.platform ?? 'Android'} ${DeviceAndPackageInfoUtil.appVersion ?? ''}');

    ebocAgreementResponseResult.when(
      success: (EbocAgreementResponse ebocAgreementResponse) async {
        if (ebocAgreementResponse.errors.isEmpty) {
          await getAccountSummaryDetails();
        } else {
          state = const AccountSummaryState.error();
        }
      },
      failure: (_) {
        state = const AccountSummaryState.error();
      },
    );
  }
}
