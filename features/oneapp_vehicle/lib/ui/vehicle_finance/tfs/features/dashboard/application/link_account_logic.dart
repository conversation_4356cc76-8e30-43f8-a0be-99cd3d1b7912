// Flutter imports:

// Package imports:
import 'package:freezed_result/freezed_result.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/firebase_util.dart';

// Project imports:
import '../../../../../../log/vehicle_analytic_event.dart';
import '../../../config/base_config.dart';
import '../../../services/locators.dart';
import '../../authentication/domain/auth_failure.dart';
import '../../authentication/domain/auth_success.dart';
import '../../authentication/domain/authenticate_repo.dart';
import '../../authentication/domain/authorize_failure.dart';
import '../../authentication/domain/authorize_repo.dart';
import '../../authentication/domain/authorize_response.dart';
import '../../core/handle_new_tokens.dart';
import 'link_account_state.dart';
import 'link_account_use_cases.dart';

class LinkAccountLogic extends LinkAccountUseCases {
  LinkAccountLogic({
    required this.repoAuthenticate,
    required this.repoAuthorize,
  }) : super(const LinkAccountState.initial());
  final AuthenticateRepo repoAuthenticate;
  final AuthorizeRepo repoAuthorize;

  @override
  Future<void> makeAuthenticateCall({bool isRelink = false}) async {
    final String? idToken = Global.getInstance().tfsIdToken;
    final bool? isUserUnLinked = Global.getInstance().storedUnLinkUser;
    if (idToken == null && isUserUnLinked == true) {
      state = const LinkAccountState.reLinkAccount();
    } else if (idToken != null && isRelink != true) {
      state = const LinkAccountState.successGotoAccountSummary();
    } else {
      state = const LinkAccountState.showShimmer();
      final Result<AuthSuccess, AuthFailure> result =
          await repoAuthenticate.requestAuthenticate();
      result.when(success: (AuthSuccess success) {
        success.maybeWhen(
            requestDevicePrint: () {
              if (isRelink) {
                submitDevicePrint();
              } else {
                state = const LinkAccountState.requestDevicePrint();
              }
            },
            receivedToken: (tfsSessionID) async {
              authorizeUser(tfsSessionID: tfsSessionID);
            },
            orElse: () => state = const LinkAccountState.initial());
      }, failure: (AuthFailure failure) {
        failure.maybeWhen(
            serverError: () => state = const LinkAccountState.error(),
            userUnverified: () =>
                state = const LinkAccountState.userUnverifiedFlow(),
            userNotFound: () => state =
                const LinkAccountState.userNotFoundGotoRegistrationFlow(),
            oneAppTokenExpired: () =>
                state = const LinkAccountState.oneAppTokenExpired(),
            accountLocked: () => state = const LinkAccountState.accountLocked(),
            orElse: () => state = const LinkAccountState.error());
      });
    }
  }

  @override
  Future<void> authorizeUser({
    required String tfsSessionID,
    bool? firstTimeLinkingOrRegistration,
  }) async {
    state = const LinkAccountState.showShimmer();
    final resultAuthorise = await repoAuthorize.callAuthorizeUserApi(
      clientId: getIt<BaseConfig>().properties.clientId,
      tokenId: tfsSessionID,
    );
    resultAuthorise.when(
      success: (AuthorizeResponse _authorizeResponse) {
        if (firstTimeLinkingOrRegistration ?? false) {
          FireBaseAnalyticsLogger.logTFSAuthGroupEvent(
              VehicleAnalyticsEvent.TFS_AUTH_ACCOUNT_LINKED_SUCCESSFULLY);
          if (Global.getInstance().tfsNewRegisteredUser) {
            FireBaseAnalyticsLogger.logTFSAuthGroupEvent(
                VehicleAnalyticsEvent.TFS_AUTH_ACCOUNT_REGISTERED_SSO_SUCCESS);
          } else {
            FireBaseAnalyticsLogger.logTFSAuthGroupEvent(
                VehicleAnalyticsEvent.TFS_AUTH_ACCOUNT_LINKED_SSO_SUCCESS);
          }
        }
        HandleTfsTokens.handleNewTokensTFS(tokenResponse: _authorizeResponse)
            .then((_) {
          state = const LinkAccountState.successGotoAccountSummary();
        });
      },
      failure: (AuthorizeFailure _authorizeResponse) =>
          state = const LinkAccountState.error(),
    );
  }

  @override
  Future<void> submitDevicePrint() async {
    state = const LinkAccountState.showShimmer();
    final Result<AuthSuccess, AuthFailure> result =
        await repoAuthenticate.submitDevicePrint();
    result.when(success: (AuthSuccess success) {
      success.maybeWhen(
          requestOTP: (String email) =>
              state = LinkAccountState.requestMFA(email: email),
          receivedToken: (tfsSessionID) async {
            authorizeUser(tfsSessionID: tfsSessionID);
          },
          orElse: () => state = const LinkAccountState.initial());
    }, failure: (AuthFailure failure) {
      failure.maybeWhen(
          serverError: () => state = const LinkAccountState.error(),
          oneAppTokenExpired: () =>
              state = const LinkAccountState.oneAppTokenExpired(),
          accountLocked: () => state = const LinkAccountState.accountLocked(),
          orElse: () => state = const LinkAccountState.error());
    });
  }

  @override
  Future<void> unlinkAccount() async {
    Global.getInstance().storedUnLinkUser = true;
    final vin = Global.getInstance().vin;
    if (vin != null) {
      setStoredUnLinkUser(vin, true);
    }
    await HandleTfsTokens.clearTfsTokens();
    state = const LinkAccountState.reLinkAccount();
  }

  @override
  void reLinkExpiredAccount() {
    state = const LinkAccountState.oneAppTokenExpired();
  }

  @override
  void setAccountLocked() {
    state = const LinkAccountState.accountLocked();
  }
}
