// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports:
import '../domain/account.dart';
import '../domain/unregistered_accounts_list.dart';
import 'account_summary_state.dart';

abstract class AccountSummaryUseCases
    extends StateNotifier<AccountSummaryState> {
  AccountSummaryUseCases(AccountSummaryState state) : super(state);

  Future<void> getAccountSummaryDetails();
  Future<void> getAccountsListToPair({required Account firstAccount});
  Future<void> pairAccountForVin(
      {required String fullAccountNumber,
      required UnregisteredAccountsList unregisteredAccountsList});
  Future<void> getTimeStampAndAcceptEboc();
}
