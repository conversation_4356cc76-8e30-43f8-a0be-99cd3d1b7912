// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';

part 'link_account_state.freezed.dart';

@freezed
class LinkAccountState with _$LinkAccountState {
  const LinkAccountState._();
  const factory LinkAccountState.initial() = _Initial;
  const factory LinkAccountState.showShimmer() = _ShowShimmer;
  const factory LinkAccountState.successGotoAccountSummary() =
      _SuccessGotoAccountSummary;
  const factory LinkAccountState.userNotFoundGotoRegistrationFlow() =
      _UserNotFoundGotoRegistrationFlow;
  const factory LinkAccountState.userUnverifiedFlow() = _UserUnverifiedFlow;
  const factory LinkAccountState.error() = _Error;
  const factory LinkAccountState.oneAppTokenExpired() = _OneAppTokenExpired;
  const factory LinkAccountState.accountLocked() = _AccountLocked;
  const factory LinkAccountState.reLinkAccount() = _ReLinkAccount;
  const factory LinkAccountState.requestDevicePrint() = _RequestDevicePrint;
  const factory LinkAccountState.requestMFA({required String email}) =
      _RequestMFA;
}
