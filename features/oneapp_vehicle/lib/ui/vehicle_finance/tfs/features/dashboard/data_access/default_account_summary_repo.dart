// Package imports:

// Package imports:
import 'package:dio/dio.dart';
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/account_summary_response_dto.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../core/utils/error_handling.dart';
import '../domain/account_summary.dart';
import '../domain/account_summary_failure.dart';
import '../domain/account_summary_repo.dart';
import '../infrastructure/account_summary_api.dart';

class DefaultAccountSummaryRepo extends AccountSummaryRepo {
  DefaultAccountSummaryRepo(this.api);

  AccountSummaryApi api;

  @override
  Future<Result<AccountSummary, AccountSummaryFailure>>
      getAccountSummary() async {
    try {
      final CommonTfsResponse<AccountSummaryResponseDTO>
          accountSummaryResponse = await api.get();

      final accountResponse = accountSummaryResponse.response;

      if (accountResponse?.accounts?.isNotEmpty ?? false) {
        return Result.success(AccountSummary.fromDomain(accountResponse!));
      } else {
        if (accountResponse?.errors?.isNotEmpty ?? false) {
          for (final item in accountResponse!.errors!) {
            if (isShowEBOCAgreementDialog(item.code)?.isNotEmpty ?? false) {
              return Result.failure(AccountSummaryFailure.ebocError());
            }
          }
        }
        return Result.failure(AccountSummaryFailure.network(
            message:
                accountResponse?.error ?? OneAppString.of().textServerError));
      }
    } catch (e) {
      if (e is DioException && e.response?.data != null) {
        if (isShowEBOCAgreementDialog(e.response!.data.toString())
                ?.isNotEmpty ??
            false) {
          return Result.failure(AccountSummaryFailure.ebocError());
        } else if (isShowInActiveVehicleAccount(e.response!.data.toString())
                ?.isNotEmpty ??
            false) {
          return Result.failure(AccountSummaryFailure.inActiveVehicleAccount());
        }
      }
      return Result.failure(
          AccountSummaryFailure.network(message: ErrorMessage.handleError(e)));
    }
  }
}
