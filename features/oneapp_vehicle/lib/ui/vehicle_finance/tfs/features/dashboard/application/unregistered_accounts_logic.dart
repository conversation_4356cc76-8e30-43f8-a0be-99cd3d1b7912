// Project imports:
import '../domain/unregistered_accounts_failure.dart';
import '../domain/unregistered_accounts_list.dart';
import '../domain/unregistered_accounts_list_repo.dart';
import 'unregistered_accounts_state.dart';
import 'unregistered_accounts_usecases.dart';

class UnregisteredAccountsLogic extends UnregisteredAccountsUseCases {
  UnregisteredAccountsLogic({
    required this.unregisteredAccountsRepo,
  }) : super(const UnregisteredAccountsState.initial());
  final UnregisteredAccountsRepo unregisteredAccountsRepo;

  @override
  Future<void> getListOfUnregisteredAccounts({
    required String existingAccount,
    required String customerType,
  }) async {
    final listOfUnregisteredAccounts =
        await unregisteredAccountsRepo.getUnregisteredAccounts(
            existingAccount: existingAccount, customerType: customerType);
    listOfUnregisteredAccounts.when(
        success: (UnregisteredAccountsList unregisteredAccountsList) {
      state = UnregisteredAccountsState.success(
          unregisteredAccountsList: unregisteredAccountsList);
    }, failure: (UnregisteredAccountsFailure failure) {
      state = UnregisteredAccountsState.failure(error: failure);
    });
  }
}
