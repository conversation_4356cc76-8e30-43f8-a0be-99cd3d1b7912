// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports:
import 'link_account_state.dart';

abstract class LinkAccountUseCases extends StateNotifier<LinkAccountState> {
  LinkAccountUseCases(LinkAccountState state) : super(state);

  Future<void> makeAuthenticateCall({bool isRelink = false});
  Future<void> authorizeUser({
    required String tfsSessionID,
    bool? firstTimeLinkingOrRegistration,
  });
  Future<void> submitDevicePrint();

  Future<void> unlinkAccount();

  void reLinkExpiredAccount();

  void setAccountLocked();
}
