// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:oa_network_impl/tfs/entity/vehicle_dto.dart';

part 'vehicle.freezed.dart';

@freezed
class Vehicle with _$Vehicle {
  const factory Vehicle({
    required String vin,
    String? imagePath,
    required String year,
    required String model,
    required String make,
  }) = _Vehicle;
  const Vehicle._();

  factory Vehicle.fromDomain(VehicleDTO vehicleDTO) => Vehicle(
        imagePath: vehicleDTO.imagePath,
        year: vehicleDTO.year,
        model: vehicleDTO.model,
        make: vehicleDTO.make,
        vin: vehicleDTO.vin,
      );
}
