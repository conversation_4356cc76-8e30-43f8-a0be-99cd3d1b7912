// Package imports:
import 'package:freezed_result/freezed_result.dart';

// Project imports:
import 'unregistered_accounts_failure.dart';
import 'unregistered_accounts_list.dart';

abstract class UnregisteredAccountsRepo {
  Future<Result<UnregisteredAccountsList, UnregisteredAccountsFailure>>
      getUnregisteredAccounts(
          {required String existingAccount, required String customerType});
}
