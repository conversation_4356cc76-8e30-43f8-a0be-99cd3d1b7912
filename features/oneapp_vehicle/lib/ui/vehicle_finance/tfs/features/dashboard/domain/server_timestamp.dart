// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:oa_network_impl/tfs/entity/server_timestamp_response_dto.dart';

part 'server_timestamp.freezed.dart';

@freezed
class ServerTimestamp with _$ServerTimestamp {
  const factory ServerTimestamp({
    required String serverDateTime,
  }) = _ServerTimestamps;
  const ServerTimestamp._();

  factory ServerTimestamp.fromDomain(
    ServerTimestampResponseDTO response,
  ) {
    return ServerTimestamp(
      serverDateTime: response.response,
    );
  }
}
