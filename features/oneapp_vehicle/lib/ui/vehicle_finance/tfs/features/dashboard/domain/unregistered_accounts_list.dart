// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:oa_network_impl/tfs/entity/unregistered_accounts_response_dto.dart';

// Project imports:
import 'errors_response.dart';
import 'response_unregistered_accounts_list.dart';

part 'unregistered_accounts_list.freezed.dart';

@freezed
class UnregisteredAccountsList with _$UnregisteredAccountsList {
  const factory UnregisteredAccountsList({
    required ResponseUnregisteredAccountsList response,
    required String status,
    required List<ErrorsResponse> errors,
  }) = _UnregisteredAccountsList;

  factory UnregisteredAccountsList.fromDomain(
      UnregisteredAccountsResponseDTO response) {
    return UnregisteredAccountsList(
        response:
            ResponseUnregisteredAccountsList.fromDomain(response.response),
        status: response.status,
        errors: response.errors == null
            ? <ErrorsResponse>[]
            : response.errors!
                .map((e) => ErrorsResponse(code: e.code, message: e.message))
                .toList());
  }

  const UnregisteredAccountsList._();
}
