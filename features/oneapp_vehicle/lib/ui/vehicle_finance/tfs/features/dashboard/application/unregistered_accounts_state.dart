// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';

// Project imports:
import '../domain/unregistered_accounts_failure.dart';
import '../domain/unregistered_accounts_list.dart';

part 'unregistered_accounts_state.freezed.dart';

@freezed
class UnregisteredAccountsState with _$UnregisteredAccountsState {
  const factory UnregisteredAccountsState.initial() = _Initial;
  const factory UnregisteredAccountsState.success({
    required UnregisteredAccountsList unregisteredAccountsList,
  }) = _Success;
  const factory UnregisteredAccountsState.failure({
    required UnregisteredAccountsFailure error,
  }) = _Failure;
}
