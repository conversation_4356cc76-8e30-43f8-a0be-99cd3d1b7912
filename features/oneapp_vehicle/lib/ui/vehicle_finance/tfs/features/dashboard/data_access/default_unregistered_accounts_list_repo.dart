// Package imports:
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/unregistered_accounts_response_dto.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../../services/firebase_service.dart';
import '../../core/utils/error_handling.dart';
import '../domain/unregistered_accounts_failure.dart';
import '../domain/unregistered_accounts_list.dart';
import '../domain/unregistered_accounts_list_repo.dart';
import '../infrastructure/unregistered_accounts_api.dart';

class DefaultUnregisteredAccountsRepo extends UnregisteredAccountsRepo {
  DefaultUnregisteredAccountsRepo(this.api);
  UnregisteredAccountsApi api;

  @override
  Future<Result<UnregisteredAccountsList, UnregisteredAccountsFailure>>
      getUnregisteredAccounts(
          {required String existingAccount,
          required String customerType}) async {
    try {
      final CommonTfsResponse<UnregisteredAccountsResponseDTO>
          unregisteredAccountsResponse = await api.post(
              existingAccount: existingAccount, customerType: customerType);
      if (unregisteredAccountsResponse.response != null) {
        return Result.success(UnregisteredAccountsList.fromDomain(
            unregisteredAccountsResponse.response!));
      } else {
        return Result.failure(UnregisteredAccountsFailure.network(
            message: OneAppString.of().textServerError));
      }
    } catch (e) {
      FirebaseService().setFireBaseNonFatalErrors(exception: e);
      return Result.failure(UnregisteredAccountsFailure.network(
          message: ErrorMessage.handleError(e)));
    }
  }
}
