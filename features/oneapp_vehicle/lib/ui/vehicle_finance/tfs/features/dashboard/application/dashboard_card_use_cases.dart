// Package imports:
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports:
import '../../../services/locators.dart';
import 'dashboard_card_state.dart';

abstract class DashboardCardUseCases extends StateNotifier<DashboardCardState> {
  DashboardCardUseCases(DashboardCardState state) : super(state);

  Future<void> initialize({
    required AppInitialValues appInitialValues,
    required FirebaseCrashlytics firebaseCrashlytics,
  });
}
