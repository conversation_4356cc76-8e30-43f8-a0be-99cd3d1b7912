// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports:
import '../../authentication/data_access/data_access.dart';
import '../data_access/data_access.dart';
import 'account_summary_logic.dart';
import 'account_summary_state.dart';
import 'account_summary_use_cases.dart';
import 'dashboard_card_logic.dart';
import 'dashboard_card_state.dart';
import 'dashboard_card_use_cases.dart';
import 'link_account_logic.dart';
import 'link_account_state.dart';
import 'link_account_use_cases.dart';
import 'refresh_token_logic.dart';
import 'refresh_token_state.dart';
import 'refresh_token_use_case.dart';
import 'unregistered_accounts_logic.dart';
import 'unregistered_accounts_state.dart';
import 'unregistered_accounts_usecases.dart';

final unregisteredAccountsStateProvider = StateNotifierProvider<
    UnregisteredAccountsUseCases, UnregisteredAccountsState>(
  (ref) => UnregisteredAccountsLogic(
    unregisteredAccountsRepo: ref.watch(unregisteredAccountsListRepoProvider),
  ),
);

final accountSummaryStateProvider =
    StateNotifierProvider<AccountSummaryUseCases, AccountSummaryState>((ref) =>
        AccountSummaryLogic(
            repo: ref.watch(accountSummaryRepoProvider),
            ebocRepo: ref.watch(ebocAgreementRepoProvider),
            repoServerTimestamp: ref.watch(serverTimestampRepoProvider),
            ref: ref));

final StateNotifierProvider<LinkAccountUseCases, LinkAccountState>
    linkAccountProviderState =
    StateNotifierProvider<LinkAccountUseCases, LinkAccountState>((ref) =>
        LinkAccountLogic(
            repoAuthenticate: ref.watch(authenticateUserRepoProvider),
            repoAuthorize: ref.watch(authorizeRepoProvider)));

final refreshTokenStateProvider = StateNotifierProvider<RefreshTokenUseCases,
        RefreshTokenState>(
    (StateNotifierProviderRef<RefreshTokenUseCases, RefreshTokenState> ref) =>
        RefreshTokenLogic(repo: ref.watch(accessTokenRepoProvider)));

final StateNotifierProvider<DashboardCardUseCases, DashboardCardState>
    dashboardCardState =
    StateNotifierProvider<DashboardCardUseCases, DashboardCardState>(
  (_) => DashboardCardLogic(),
);
