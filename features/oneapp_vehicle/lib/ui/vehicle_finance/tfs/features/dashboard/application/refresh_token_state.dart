// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';

// Project imports:
import '../../authentication/domain/authorize_response.dart';

part 'refresh_token_state.freezed.dart';

@freezed
class RefreshTokenState with _$RefreshTokenState {
  const RefreshTokenState._();
  const factory RefreshTokenState.initial() = _Initial;
  const factory RefreshTokenState.success(
      {required AuthorizeResponse authorizeResponse}) = _Success;
  const factory RefreshTokenState.sessionExpired() = _SessionExpired;
  const factory RefreshTokenState.serverError({required String errorMessage}) =
      _ServerError;
}
