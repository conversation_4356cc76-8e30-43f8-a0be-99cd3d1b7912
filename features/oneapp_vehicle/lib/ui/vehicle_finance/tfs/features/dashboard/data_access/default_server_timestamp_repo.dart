// Package imports:
import 'package:freezed_result/src/result.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/server_timestamp_response_dto.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../../services/firebase_service.dart';
import '../../core/domain/api_failure.dart';
import '../../core/utils/error_handling.dart';
import '../domain/server_timestamp.dart';
import '../domain/server_timestamp_repo.dart';
import '../infrastructure/server_timestamp_api.dart';

class DefaultServerTimeStampRepo extends ServerTimestampRepo {
  final ServerTimestampApi api;

  DefaultServerTimeStampRepo(this.api);

  @override
  Future<Result<ServerTimestamp, ApiFailure>> getServerTimestamp() async {
    try {
      final CommonTfsResponse<ServerTimestampResponseDTO>
          serverTimestampResponseDTO = await api.get();
      if (serverTimestampResponseDTO.response != null) {
        return Result.success(
            ServerTimestamp.fromDomain(serverTimestampResponseDTO.response!));
      } else {
        return Result.failure(ApiFailure.network(
            errorMessage: OneAppString.of().textServerError));
      }
    } catch (e) {
      FirebaseService().setFireBaseNonFatalErrors(exception: e);
      return Result.failure(
          ApiFailure.network(errorMessage: ErrorMessage.handleError(e)));
    }
  }
}
