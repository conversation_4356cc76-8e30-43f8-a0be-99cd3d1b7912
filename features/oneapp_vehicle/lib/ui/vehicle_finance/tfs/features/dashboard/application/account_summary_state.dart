// Package imports:
import 'package:freezed_annotation/freezed_annotation.dart';

// Project imports:
import '../domain/account.dart';
import '../domain/account_summary.dart';

part 'account_summary_state.freezed.dart';

@freezed
class AccountSummaryState with _$AccountSummaryState {
  const AccountSummaryState._();
  const factory AccountSummaryState.initial() = _Initial;
  const factory AccountSummaryState.success(
      {required AccountSummary accountSummary,
      required Account account}) = _Success;
  const factory AccountSummaryState.error({String? message}) = _Error;
  const factory AccountSummaryState.isInactiveVehicleAccount() =
      _IsInactiveVehiculeAccount;
  const factory AccountSummaryState.accountCannotBeAdded(
      {required String message}) = _AccountCannotBeAdded;
  const factory AccountSummaryState.isShowEBOCAgreementDialog() =
      _IsShowEBOCAgreementDialog;
  const factory AccountSummaryState.isAccountNotFound() = _IsAccountNotFound;
  const factory AccountSummaryState.isAccountClosed() = _IsAccountClosed;

  bool get isSuccess => mapOrNull(success: (_) => true) ?? false;
}
