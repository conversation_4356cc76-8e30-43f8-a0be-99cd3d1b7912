// Package imports:
import 'package:dio/dio.dart';
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/pair_accounts_response_dto.dart';
import 'package:oneapp_common/theme/string_util.dart';

// Project imports:
import '../../../services/firebase_service.dart';
import '../../core/utils/device_and_package_info_util.dart';
import '../../core/utils/error_handling.dart';
import '../domain/pair_accounts_failure.dart';
import '../domain/pair_accounts_repo.dart';
import '../domain/pair_accounts_response.dart';
import '../infrastructure/pair_accounts_api.dart';

class DefaultPairAccountsRepo extends PairAccountsRepo {
  DefaultPairAccountsRepo(this.api);

  PairAccountsApi api;

  final String _clientInfo = 'ABCD1234';

  @override
  Future<Result<PairAccountsResponse, PairAccountsFailure>> postAccountToPair(
      {required String addedAccount,
      required String encryptedAccountNumber,
      required String last8vin,
      required String customerType}) async {
    try {
      final CommonTfsResponse<PairAccountsResponseDTO>
          pairAccountsResponseData = await api.post(
        addedAccount: addedAccount,
        encryptedAccountNumber: encryptedAccountNumber,
        last8vin: last8vin,
        customerType: customerType,
        clientInfo: _clientInfo,
        browserVersion: DeviceAndPackageInfoUtil.platform,
      );
      if (pairAccountsResponseData.response != null) {
        PairAccountsResponse _pairAccountsResponse =
            PairAccountsResponse.fromDomain(pairAccountsResponseData.response!);
        if (_pairAccountsResponse.errors.isNotEmpty) {
          return Result.failure(PairAccountsFailure.withError(
              _pairAccountsResponse.errors.first.toString()));
        } else {
          return Result.success(_pairAccountsResponse);
        }
      }
      return Result.failure(PairAccountsFailure.network(
          message: OneAppString.of().textServerError));
    } catch (e) {
      FirebaseService().setFireBaseNonFatalErrors(exception: e);

      if (e is DioException && e.response?.data != null) {
        return Result.failure(
            PairAccountsFailure.withError(e.response!.data.toString()));
      }
      return Result.failure(
          PairAccountsFailure.network(message: ErrorMessage.handleError(e)));
    }
  }
}
