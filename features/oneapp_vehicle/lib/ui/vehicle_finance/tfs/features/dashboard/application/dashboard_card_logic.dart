// Package imports:
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

// Project imports:
import '../../../services/locators.dart';
import 'dashboard_card_state.dart';
import 'dashboard_card_use_cases.dart';

class DashboardCardLogic extends DashboardCardUseCases {
  DashboardCardLogic() : super(const DashboardCardState.initial());

  @override
  Future<void> initialize({
    required AppInitialValues appInitialValues,
    required FirebaseCrashlytics firebaseCrashlytics,
  }) async {
    await setupLocators(
      firebaseAppCrashlytics: firebaseCrashlytics,
      appInitialValues: appInitialValues,
      onFinished: () {
        state = const DashboardCardState.loaded();
      },
    );
  }
}
