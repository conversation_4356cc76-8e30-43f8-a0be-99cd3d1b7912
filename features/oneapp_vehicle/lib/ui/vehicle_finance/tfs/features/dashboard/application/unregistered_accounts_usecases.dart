// Package imports:
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports:
import 'unregistered_accounts_state.dart';

abstract class UnregisteredAccountsUseCases
    extends StateNotifier<UnregisteredAccountsState> {
  UnregisteredAccountsUseCases(UnregisteredAccountsState state) : super(state);
  Future<void> getListOfUnregisteredAccounts({
    required String existingAccount,
    required String customerType,
  });
}
