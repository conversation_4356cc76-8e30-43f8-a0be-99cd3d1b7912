// Package imports:
import 'package:freezed_result/freezed_result.dart';
import 'package:oa_network_impl/entity/common_tfs_response.dart';
import 'package:oa_network_impl/tfs/entity/eboc_response_dto.dart';

// Project imports:
import '../../../services/firebase_service.dart';
import '../../core/utils/error_handling.dart';
import '../domain/eboc_agreement_response.dart';
import '../domain/eboc_agreement_response_failure.dart';
import '../domain/eboc_agreement_response_repo.dart';
import '../infrastructure/eboc_agreement_response_api.dart';

class DefaultEbocAgreementResponseRepo extends EbocAgreementResponseRepo {
  DefaultEbocAgreementResponseRepo(this.api);
  EbocAgreementResponseApi api;

  @override
  Future<Result<EbocAgreementResponse, EbocAgreementResponseFailure>>
      postEbocAgreementIsAccepted({
    required String ebaFlag,
    required String ocFlag,
    required String browserVersion,
    required String ocTimeStamp,
    required String ebaTimeStamp,
    required String browserTimeStamp,
    required String clientInfo,
  }) async {
    try {
      final CommonTfsResponse<EbocResponseDTO> ebocResponse = await api.get(
        ebaFlag: ebaFlag,
        ocFlag: ocFlag,
        browserVersion: browserVersion,
        ocTimeStamp: ocTimeStamp,
        ebaTimeStamp: ebaTimeStamp,
        browserTimeStamp: browserTimeStamp,
        clientInfo: browserTimeStamp,
      );
      if (ebocResponse.response != null) {
        return Result.success(
            EbocAgreementResponse.fromDomain(ebocResponse.response!));
      }
      return Result.failure(EbocAgreementResponseFailure.network(
          message: ErrorMessage.handleError(ebocResponse.error)));
    } catch (e) {
      FirebaseService().setFireBaseNonFatalErrors(exception: e);
      return Result.failure(
        EbocAgreementResponseFailure.network(
            message: ErrorMessage.handleError(e)),
      );
    }
  }
}
