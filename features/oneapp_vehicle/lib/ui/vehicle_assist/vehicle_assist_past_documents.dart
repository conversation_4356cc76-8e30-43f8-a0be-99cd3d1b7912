//Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_health_helper.dart';

// Project imports:
import 'vehicle_assist_bloc.dart';
import 'vehicle_collision_assitance/vehicle_collision_assistance_bloc.dart';
import 'vehicle_collision_assitance/vehicle_collision_assistance_start_page.dart';
import 'vehicle_collision_assitance/vehicle_collision_document_progress_page.dart';
import 'vehicle_collision_assitance/vehicle_collision_document_ready_page.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/document_collision_entity.dart'
    as documentCollision;

class VehicleAssistPastDocumentsPage extends StatefulWidget {
  @override
  _VehicleAssistPastDocumentsPageState createState() =>
      _VehicleAssistPastDocumentsPageState();
}

class _VehicleAssistPastDocumentsPageState
    extends State<VehicleAssistPastDocumentsPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  VehicleAssistBloc _assistBloc = VehicleAssistBloc();
  VehicleCollisionAssistanceBloc _bloc = VehicleCollisionAssistanceBloc();

  @override
  void initState() {
    _assistBloc.init();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _bloc.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: true,
      child: BlocProvider(
        bloc: _bloc,
        child: OneAppScaffold(
          body: Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SwipeBarIcon(),
                bottomSheetCustomAppBar(
                    formatTextForLexusAndToyota(
                        OneAppString.of().pastDocuments), onBackPressed: () {
                  Navigator.of(context).pop();
                }, elevation: 0),
                SizedBox(
                  height: 38.h,
                ),
                Expanded(
                  child: Column(
                    children: [
                      StreamBuilder(
                          stream: _assistBloc.vehicleName,
                          builder: (context, snapshot) {
                            return Text(snapshot.data ?? " - ",
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyleExtension().newStyleWithColor(
                                    ThemeConfig.current()
                                        .textStyleUtil
                                        .callout2,
                                    ThemeConfig.current()
                                        .colorUtil
                                        .tertiary03));
                          }),
                      SizedBox(
                        height: 34.h,
                      ),
                      Text(
                        OneAppString.of().collisionAssistPastDocumentNote,
                        style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1,
                          _colorUtil.error01,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(
                        height: 34.h,
                      ),
                      Expanded(child: _pastDocumentsLayout()),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _pastDocumentsLayout() {
    return StreamBuilder<List<VehicleHealthHelper>>(
        stream: _assistBloc.vehicleCollisionPastDocumentsCardList,
        builder: (context, snapshot) {
          return snapshot.hasData
              ? ListView.builder(
                  itemBuilder: (BuildContext context, int index) {
                    return GestureDetector(
                      onTap: () {},
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          decoration: BoxDecoration(
                            color: _colorUtil.tertiary15,
                            boxShadow: [
                              BoxShadow(
                                color: Color.fromRGBO(0, 0, 0, 0.07),
                                offset: Offset(0, 0.8),
                                blurRadius: 6.0.r,
                              ),
                            ],
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.r)),
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16.w, vertical: 32.h),
                            child: Column(
                              children: [
                                snapshot.data![index].description == null ||
                                        (snapshot.data![index].description !=
                                                null &&
                                            snapshot.data![index].description!
                                                .isEmpty)
                                    ? SizedBox()
                                    : Text(
                                        "${snapshot.data![index].description}",
                                        textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyleExtension()
                                            .newStyleWithColor(
                                                ThemeConfig.current()
                                                    .textStyleUtil
                                                    .caption1,
                                                ThemeConfig.current()
                                                    .colorUtil
                                                    .tertiary05)),
                                SizedBox(
                                  height: 4.h,
                                ),
                                snapshot.data![index].subTitle == null ||
                                        (snapshot.data![index].subTitle !=
                                                null &&
                                            snapshot
                                                .data![index].subTitle!.isEmpty)
                                    ? SizedBox()
                                    : Text("${snapshot.data![index].subTitle}",
                                        textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyleExtension()
                                            .newStyleWithColor(
                                                ThemeConfig.current()
                                                    .textStyleUtil
                                                    .callout2,
                                                ThemeConfig.current()
                                                    .colorUtil
                                                    .tertiary03)),
                                SizedBox(
                                  height: 21.h,
                                ),
                                Padding(
                                  padding: EdgeInsets.all(8.h),
                                  child: Container(
                                    child: Align(
                                      alignment: Alignment.bottomCenter,
                                      child: CustomDefaultButton(
                                        text: formatTextForLexusAndToyota(
                                            snapshot.data![index].buttonText!),
                                        borderColor: _colorUtil.button01b,
                                        buttonTextColor: _colorUtil.button01a,
                                        backgroundColor: _colorUtil.button01b,
                                        press: () {
                                          documentCollision.CollisionData
                                              collisionData = _assistBloc
                                                  .documentCollisionPayload!
                                                  .collisionData!
                                                  .firstWhere((element) =>
                                                      element.collisionId ==
                                                      snapshot.data![index]
                                                          .collisionId);
                                          documentCollision.ScreenDetails?
                                              screenDetails = collisionData
                                                  .displayDetails!
                                                  .screenDetails;
                                          if (screenDetails != null &&
                                              screenDetails.cta != null) {
                                            _collisionButtonClick(
                                                screenDetails);
                                          } else {
                                            urlLauncher(collisionData.deepLink!,
                                                forceWebView: false,
                                                enableJavaScript: true);
                                          }
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                  shrinkWrap: true,
                  itemCount: snapshot.data!.length,
                )
              : _shimmerLayout();
        });
  }

  _collisionButtonClick(documentCollision.ScreenDetails screenDetails) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => _chooseCollisionPage(screenDetails),
    ).then((value) {
      _assistBloc.fetchVehicleInfo();
    });
  }

  Widget _chooseCollisionPage(documentCollision.ScreenDetails screenDetails) {
    String? screenNo = screenDetails.screenNo;
    if (screenNo == null) screenNo = "1";
    if (screenNo == "1" || screenNo == '2') {
      //If screenNo == 1 or screenNo == 2 No collision docs.
      return VehicleCollisionDocumentStartPage(screenDetails: screenDetails);
    } else if (screenNo == '3') {
      //If screenNo == 3 Doc collision is requested already and status is file preparation is in progress.
      return VehicleCollisionDocumentProgressPage(screenDetails: screenDetails);
    } else if (screenNo == '4') {
      //If screenNo == 4 Your file is ready to document your collision.
      return VehicleCollisionDocumentReadyPage(screenDetails: screenDetails);
    } else {
      return Container(
        color: Colors.white,
        child: Center(
          child: Text(
            formatTextForLexusAndToyota(OneAppString.of().inProgress),
            style: _textStyleUtil.title1,
          ),
        ),
      );
    }
  }

  Widget _shimmerLayout() {
    return ListView.builder(
        itemCount: 10,
        itemBuilder: (BuildContext context, int index) {
          return Padding(
            padding: EdgeInsets.all(8.0..w),
            child: shimmerRectangle(170.h, double.maxFinite, 8.r),
          );
        });
  }
}
