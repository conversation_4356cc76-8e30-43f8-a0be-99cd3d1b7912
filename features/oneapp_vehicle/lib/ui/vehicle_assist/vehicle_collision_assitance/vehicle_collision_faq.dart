// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/list_tile/custom_expanded_tile.dart';

// Project imports:
import 'vehicle_collision_faq_bloc.dart';

class VehicleCollisionFAQPage extends StatefulWidget {
  const VehicleCollisionFAQPage({Key? key}) : super(key: key);

  @override
  State<VehicleCollisionFAQPage> createState() =>
      _VehicleCollisionFAQPageState();
}

class _VehicleCollisionFAQPageState extends State<VehicleCollisionFAQPage> {
  VehicleCollisionFAQBloc _bloc = VehicleCollisionFAQBloc();

  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  Widget build(BuildContext context) {
    return OneAppScaffold(
      backgroundColor: _colorUtil.tertiary15,
      body: SafeArea(
        top: true,
        child: SingleChildScrollView(
          child: Column(
            children: [
              SwipeBarIcon(),
              _bottomSheetHeader(OneAppString.of().collisionAssistFAQCta,
                  onBackPressed: _popBack),
              _commonExpandableLayout(
                  OneAppString.of().collisionFAQServiceRetiredTitle,
                  OneAppString.of().collisionFAQServiceRetiredBody,
                  _bloc.isServiceRetiredLayoutExpanded,
                  1),
              _commonExpandableLayout(
                  OneAppString.of().collisionFAQPassedDocumentTitle,
                  OneAppString.of().collisionFAQPassedDocumentBody,
                  _bloc.isPassedDocumentLayoutExpanded,
                  2),
              _commonExpandableLayout(
                  OneAppString.of().collisionFAQAccidentReportTitle,
                  OneAppString.of().collisionFAQAccidentReportBody,
                  _bloc.isAccidentReportLayoutExpanded,
                  3),
              SizedBox(height: 150.h),
              _exitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _exitButton() {
    return Padding(
      padding: EdgeInsets.only(bottom: 32.0.h),
      child: CustomDefaultButton(
        backgroundColor: _colorUtil.button01b,
        buttonTextColor: _colorUtil.button01a,
        press: () => _popBack(),
        text: OneAppString.of().disclaimerExit,
        borderColor: _colorUtil.button01b,
        horizontalPadding: 80.w,
        verticalPadding: 16.h,
      ),
    );
  }

  Widget _commonExpandableLayout(String title, String messageToDisplay,
      Stream<bool> currentStream, int index) {
    return Container(
      padding: EdgeInsets.only(left: 16.0.w, right: 16.0.w, bottom: 8.h),
      child: CustomExpansionTile(
        collapsedBackgroundColor: _colorUtil.tile05,
        title: SizedBox(
          width: 271.w,
          height: 84.h,
          child: Center(
            child: Text(title,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body4, _colorUtil.tertiary03)),
          ),
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(messageToDisplay,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body3, _colorUtil.tertiary05)),
          ),
        ],
        trailing: SizedBox(
          width: 10.w,
          child: StreamBuilder<bool>(
              stream: currentStream,
              builder: (context, snapshot) {
                return Container(
                  height: 48.h,
                  child: SvgPicture.asset(
                    snapshot.hasData && snapshot.data! ? minusIcon : plusIcon,
                    colorFilter: ColorFilter.mode(
                      _colorUtil.tertiary05,
                      BlendMode.srcIn,
                    ),
                    height: 12.w,
                    width: 12.w,
                    allowDrawingOutsideViewBox: true,
                    semanticsLabel: snapshot.hasData && snapshot.data!
                        ? MINIMIZE_ICON
                        : PLUS_ICON,
                  ),
                );
              }),
        ),
        onExpansionChanged: (isExpanded) {
          _bloc.featuresTrailingIcon(isExpanded, index);
        },
      ),
    );
  }

  Widget _bottomSheetHeader(String title, {VoidCallback? onBackPressed}) {
    return Container(
      margin: EdgeInsets.only(left: 16.w, bottom: 8.h),
      child: Column(
        children: [
          Container(
            height: kToolbarHeight,
            child: Stack(
              fit: StackFit.loose,
              children: [
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(formatTextForLexusAndToyota(title),
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.subHeadline3,
                              _colorUtil.tertiary03)),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: InkWell(
                      onTap: onBackPressed,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _colorUtil.tertiary12,
                          shape: BoxShape.circle,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(8.w),
                          child: Icon(
                            Icons.chevron_left,
                            color: _colorUtil.tertiary00,
                            semanticLabel: BACK_BUTTON,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _popBack() {
    Navigator.of(context).pop();
  }
}
