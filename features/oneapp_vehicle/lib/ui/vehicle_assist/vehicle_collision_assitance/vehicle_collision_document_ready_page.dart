//Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';

// Project imports:
import '/log/vehicle_analytic_event.dart';
import 'vehicle_collision_assistance_bloc.dart';

// Project imports:

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/document_collision_entity.dart'
    as documentCollision;

class VehicleCollisionDocumentReadyPage extends StatefulWidget {
  final documentCollision.ScreenDetails? screenDetails;

  VehicleCollisionDocumentReadyPage({this.screenDetails});

  @override
  _VehicleCollisionDocumentReadyPageState createState() =>
      _VehicleCollisionDocumentReadyPageState();
}

class _VehicleCollisionDocumentReadyPageState
    extends State<VehicleCollisionDocumentReadyPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  VehicleCollisionAssistanceBloc _bloc = VehicleCollisionAssistanceBloc();

  @override
  void initState() {
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.COLLISION_DOCUMENT_READY_PAGE);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _bloc.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: true,
      child: BlocProvider(
        bloc: _bloc,
        child: OneAppScaffold(
          body: Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SwipeBarIcon(),
                bottomSheetCustomAppBar(
                    formatTextForLexusAndToyota(
                        OneAppString.of().collisionAssistance),
                    onBackPressed: () {
                  Navigator.of(context).pop();
                }, elevation: 0),
                Expanded(
                    child: _collisionDocumentReadyLayout(widget.screenDetails!))
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _collisionDocumentReadyLayout(
      documentCollision.ScreenDetails screenDetails) {
    return Column(
      children: [
        Expanded(
            child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(left: 12.w, top: 12.h),
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: _colorUtil.button03b,
                shape: BoxShape.circle,
              ),
              child: Padding(
                padding: EdgeInsets.all(12.w),
                child: SvgPicture.asset(
                  collisionAssistanceIcon,
                  height: 94.w,
                  width: 94.w,
                  semanticsLabel: COLLISION_ASSISTANCE_ICON,
                  alignment: Alignment.center,
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 26.h, bottom: 16.h),
              child: Text(OneAppString.of().documentationReadyFor,
                  textAlign: TextAlign.center,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1, _colorUtil.tertiary05)),
            ),
            Container(
              margin: EdgeInsets.only(bottom: 16.h),
              child: Text(formatTextForLexusAndToyota(screenDetails.header!),
                  textAlign: TextAlign.center,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.title2, _colorUtil.tertiary03)),
            ),
            Text(screenDetails.body!,
                textAlign: TextAlign.center,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body3, _colorUtil.tertiary05)),
            _shareDocumentLinkButton(),
          ],
        )),
        _cancelButton(),
        _startDocumentButton()
      ],
    );
  }

  Widget _shareDocumentLinkButton() {
    return Container(
      margin: EdgeInsets.only(top: 16.h),
      child: InkWell(
          onTap: () {
            _onShare();
          },
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: SvgPicture.asset(
              shareIcon,
              height: 18.w,
              width: 18.w,
              colorFilter: ColorFilter.mode(
                _colorUtil.tertiary03,
                BlendMode.srcIn,
              ),
              allowDrawingOutsideViewBox: true,
              semanticsLabel: SHARE_ICON,
            ),
          )),
    );
  }

  Widget _cancelButton() {
    return Container(
        margin: EdgeInsets.only(bottom: 16.h, top: 16.h),
        alignment: Alignment.center,
        child: InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Text(
              formatTextForLexusAndToyota(OneAppString.of().commonCancel),
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.buttonLink1, _colorUtil.tertiary03),
            )));
  }

  Widget _startDocumentButton() {
    return Container(
        margin: EdgeInsets.only(bottom: 22.h),
        alignment: Alignment.center,
        child: CustomDefaultButton(
          press: () {
            urlLauncher(widget.screenDetails!.url!,
                forceWebView: false, enableJavaScript: true);
          },
          backgroundColor: _colorUtil.button01b,
          buttonTextColor: _colorUtil.button01a,
          text: formatTextForLexusAndToyota(
              widget.screenDetails!.positiveButtonText!),
          borderColor: _colorUtil.button01b,
          horizontalPadding: 12.w,
          verticalPadding: 4.h,
        ));
  }

  void _onShare() {
    openNativeSharing(widget.screenDetails!.url!);
  }
}
