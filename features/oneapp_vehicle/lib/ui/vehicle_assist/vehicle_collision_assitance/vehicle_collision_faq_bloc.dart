// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:rxdart/rxdart.dart';

class VehicleCollisionFAQBloc extends BlocBase {
  final _isServiceRetiredLayoutExpanded = BehaviorSubject<bool>();

  Stream<bool> get isServiceRetiredLayoutExpanded =>
      _isServiceRetiredLayoutExpanded.stream;
  final _isPassedDocumentLayoutExpanded = BehaviorSubject<bool>();

  Stream<bool> get isPassedDocumentLayoutExpanded =>
      _isPassedDocumentLayoutExpanded.stream;
  final _isAccidentReportLayoutExpanded = BehaviorSubject<bool>();

  Stream<bool> get isAccidentReportLayoutExpanded =>
      _isAccidentReportLayoutExpanded.stream;

  void featuresTrailingIcon(bool isExpanded, int currentTile) {
    switch (currentTile) {
      case 1:
        return _isServiceRetiredLayoutExpanded.sink.add(isExpanded);

      case 2:
        return _isPassedDocumentLayoutExpanded.sink.add(isExpanded);

      case 3:
        return _isAccidentReportLayoutExpanded.sink.add(isExpanded);

      default:
        {
          return _isServiceRetiredLayoutExpanded.sink.add(isExpanded);
        }
    }
  }

  @override
  void dispose() {
    _isServiceRetiredLayoutExpanded.close();
    _isPassedDocumentLayoutExpanded.close();
    _isAccidentReportLayoutExpanded.close();
  }
}
