//Package imports:

// Package imports:
import 'package:location/location.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/collision_assistance_claim_entity.dart'
    as claimAssistanceEntity;
// Project imports:

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/document_collision_entity.dart'
    as documentCollision;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleCollisionAssistanceBloc extends BlocBase {
  OneAppClient api = APIClientConfig.oneAppClient;
  late Function(bool) progressHandlerCallback;
  documentCollision.DocumentCollisionPayload? documentCollisionPayload;
  claimAssistanceEntity.CollisionAssistanceClaimPayload?
      collisionAssistanceClaimPayload;
  Location location = Location();
  vehicleInfo.Payload? vehicleItem;

  Stream<documentCollision.ScreenDetails?> get startDocumentData =>
      _startDocumentData.stream;
  final _startDocumentData =
      BehaviorSubject<documentCollision.ScreenDetails?>();

  Stream<bool> get isClaimInProgress => _isClaimInProgress.stream;
  final _isClaimInProgress = BehaviorSubject<bool>();

  Stream<bool> get appLocationDenied => _appLocationDenied.stream;
  final _appLocationDenied = BehaviorSubject<bool>();

  void init(Function(bool) progressHandler) {
    progressHandlerCallback = progressHandler;
    _isClaimInProgress.sink.add(
        false); // Setting it to false to enable collision document default page.
    fetchVehicleInfo();
  }

  Future<void> fetchVehicleInfo() async {
    String? vin = Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);
  }

  void parseDataFromPayload() {
    if (documentCollisionPayload != null) {
      documentCollision.CollisionData displayDetails =
          documentCollisionPayload!.collisionData!.firstWhere((element) =>
              element.displayDetails!.screenDetails!.screenNo == '2');
      _startDocumentData.sink.add(displayDetails.displayDetails!.screenDetails);
    }
  }

  // Start an new collision assistant document
  void requestToCreateCollisionDocument(Position location) async {
    progressHandlerCallback(true);
    CommonResponse<claimAssistanceEntity.CollisionAssistanceClaimEntity>
        commonResponse = await api.applyForCollisionDocument(
            Global.getInstance().collisionVin!,
            location.lat.toDouble(),
            location.lng.toDouble(),
            Global.getInstance().mileage,
            vehicleItem!.brand ?? Global.getInstance().appBrand);
    collisionAssistanceClaimPayload = commonResponse.response?.payload;
    if (collisionAssistanceClaimPayload != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.COLLISION_ASSISTANCE_SUCCESS,
          category: LogCategory.FL_VEHI);
      parseDataFromCollisionRequestPayload();
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.COLLISION_ASSISTANCE_FAILURE,
          category: LogCategory.FL_VEHI);
    }

    progressHandlerCallback(false);
  }

  // We need to pass current location to start collision document
  Future<void> getCurrentLocationAndStartDocument() async {
    progressHandlerCallback(true);
    if (await Permission.location.request().isGranted) {
      late Position _currentLocation;
      try {
        _currentLocation = await MapUtil.getCurrentLocation();
      } catch (e) {
        FireBaseAnalyticsLogger.logError(e.toString(),
            category: LogCategory.FL_VEHI);
      }
      requestToCreateCollisionDocument(_currentLocation);
    } else {
      _appLocationDenied.sink.add(true);
    }
  }

  void parseDataFromCollisionRequestPayload() {
    if (collisionAssistanceClaimPayload != null) {
      _isClaimInProgress.sink
          .add(true); //Setting it to true to display the in-progress layout.
    }
  }

  String? getInProgressDescription() {
    String? inProgressDescription = '';
    if (collisionAssistanceClaimPayload != null &&
        collisionAssistanceClaimPayload!
                .collisionData!.first.displayDetails?.screenDetails?.body !=
            null) {
      inProgressDescription = collisionAssistanceClaimPayload!
          .collisionData!.first.displayDetails?.screenDetails?.body;
    }
    return inProgressDescription;
  }

  String getInProgressHeading() {
    String? inProgressHeading = '';
    if (collisionAssistanceClaimPayload != null &&
        collisionAssistanceClaimPayload!
                .collisionData!.first.displayDetails?.screenDetails?.header !=
            null) {
      inProgressHeading = collisionAssistanceClaimPayload!
          .collisionData!.first.displayDetails?.screenDetails?.header;
    }
    return formatTextForLexusAndToyota(inProgressHeading!);
  }

  @override
  void dispose() {
    _startDocumentData.close();
    _isClaimInProgress.close();
    _appLocationDenied.close();
  }
}
