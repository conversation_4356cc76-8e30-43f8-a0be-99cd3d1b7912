//Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:app_settings/app_settings.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';

// Project imports:
import '../../../log/vehicle_marketing_event.dart';
import '../vehicle_assist_bloc.dart';
import '../vehicle_assist_past_documents.dart';
import '/log/vehicle_analytic_event.dart';
import 'vehicle_collision_assistance_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/document_collision_entity.dart'
    as documentCollision;

class VehicleCollisionDocumentStartPage extends StatefulWidget {
  final documentCollision.ScreenDetails? screenDetails;

  VehicleCollisionDocumentStartPage({this.screenDetails});

  @override
  _VehicleCollisionDocumentStartPageState createState() =>
      _VehicleCollisionDocumentStartPageState();
}

class _VehicleCollisionDocumentStartPageState
    extends State<VehicleCollisionDocumentStartPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  VehicleCollisionAssistanceBloc _bloc = VehicleCollisionAssistanceBloc();
  VehicleAssistBloc _vehicleAssistBloc = VehicleAssistBloc();

  @override
  void initState() {
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.COLLISION_DOCUMENT_START_PAGE);
    _bloc.init(_progressHandlerCallback);
    super.initState();
    _bloc.appLocationDenied.listen((event) {
      if (event == true) {
        BottomConfirmationDialog().showBottomDialog(
            context,
            OneAppString.of().locationPermissionHeading,
            OneAppString.of().locationPermissionHeading,
            mapLocationPinIcon,
            OneAppString.of().commonOK,
            OneAppString.of().commonCancel,
            _appPermissionConfirmClick);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _bloc.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: true,
      child: BlocProvider(
        bloc: _bloc,
        child: OneAppScaffold(
          body: Padding(
            padding: const EdgeInsets.only(left: 16.0, right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SwipeBarIcon(),
                bottomSheetCustomAppBar(
                    formatTextForLexusAndToyota(
                        OneAppString.of().collisionAssistance),
                    onBackPressed: () {
                  Navigator.of(context).pop();
                }, elevation: 0),
                Expanded(
                  child: StreamBuilder<bool>(
                      stream: _bloc.isClaimInProgress,
                      builder: (context, snapshot) {
                        if (snapshot.hasData && snapshot.data!) {
                          return _bloc.collisionAssistanceClaimPayload != null
                              ? _collisionDocumentInProgressLayout()
                              : Container();
                        } else if (snapshot.hasData && !snapshot.data!) {
                          return _collisionAssistanceLayout(
                              widget.screenDetails!);
                        } else {
                          return Container();
                        }
                      }),
                )
                //_collisionAssistanceLayout(widget.screenDetails)
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Start collision layout
  Widget _collisionAssistanceLayout(
      documentCollision.ScreenDetails screenDetails) {
    return Column(
      children: [
        Expanded(
            child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(left: 12.h, top: 12.h),
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: _colorUtil.button03b,
                shape: BoxShape.circle,
              ),
              child: Padding(
                padding: EdgeInsets.all(12.w),
                child: SvgPicture.asset(
                  collisionAssistanceIcon,
                  height: 94.w,
                  width: 94.w,
                  semanticsLabel: COLLISION_ASSISTANCE_ICON,
                  alignment: Alignment.center,
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 26.h, bottom: 16.h),
              child: Text(formatTextForLexusAndToyota(screenDetails.header!),
                  textAlign: TextAlign.start,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.title2, _colorUtil.tertiary03)),
            ),
            Text(screenDetails.body!,
                textAlign: TextAlign.start,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body3, _colorUtil.tertiary05)),
          ],
        )),
        _notNowButton(),
        _continueButton(),
      ],
    );
  }

  Widget _continueButton() {
    return Container(
        margin: EdgeInsets.only(bottom: 22.h),
        alignment: Alignment.center,
        child: CustomDefaultButton(
          press: () {
            FireBaseAnalyticsLogger.logMarketingEvent(
                VehicleMarketingEvent.USERS_AUTO_COLLISION_ACKOWLEDGE_CTA);
            _bloc.getCurrentLocationAndStartDocument();
          },
          backgroundColor: _colorUtil.button01b,
          buttonTextColor: _colorUtil.button01a,
          text: formatTextForLexusAndToyota(OneAppString.of().commonContinue),
          borderColor: _colorUtil.button01b,
          horizontalPadding: 12,
          verticalPadding: 4,
        ));
  }

  Widget _notNowButton() {
    return Container(
        margin: EdgeInsets.only(bottom: 16.h, top: 16.h),
        alignment: Alignment.center,
        child: InkWell(
            onTap: () {
              FireBaseAnalyticsLogger.logMarketingEvent(
                  VehicleMarketingEvent.Users_AUTO_COLLISION_DENIED_CTA);
              Navigator.pop(context);
            },
            child: Text(
              formatTextForLexusAndToyota(OneAppString.of().notNow),
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.buttonLink1, _colorUtil.tertiary03),
            )));
  }

  // Once user click start document, we show progress page in same layout
  Widget _collisionDocumentInProgressLayout() {
    return Column(
      children: [
        Expanded(
            child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(left: 12.w, top: 12.h),
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: _colorUtil.button03b,
                shape: BoxShape.circle,
              ),
              child: Padding(
                padding: EdgeInsets.all(12.w),
                child: SvgPicture.asset(
                  collisionAssistanceIcon,
                  height: 94.w,
                  width: 94.w,
                  semanticsLabel: COLLISION_ASSISTANCE_ICON,
                  alignment: Alignment.center,
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 26.h, bottom: 16.h),
              child: Text(OneAppString.of().preparingFileFor,
                  textAlign: TextAlign.center,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1, _colorUtil.tertiary05)),
            ),
            Container(
              margin: EdgeInsets.only(bottom: 16.h),
              child: Text(_bloc.getInProgressHeading(),
                  textAlign: TextAlign.center,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.title2, _colorUtil.tertiary03)),
            ),
            Text(_bloc.getInProgressDescription()!,
                textAlign: TextAlign.center,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body3, _colorUtil.tertiary05)),
          ],
        )),
        _doneButton()
      ],
    );
  }

  Widget _doneButton() {
    return Container(
        margin: EdgeInsets.only(bottom: 22.h, top: 16.h),
        alignment: Alignment.center,
        child: CustomDefaultButton(
          press: () {
            _doneButtonClick();
          },
          backgroundColor: _colorUtil.button01b,
          buttonTextColor: _colorUtil.button01a,
          text: formatTextForLexusAndToyota(OneAppString.of().commonDone),
          borderColor: _colorUtil.button01b,
          horizontalPadding: 12,
          verticalPadding: 4,
        ));
  }

  _doneButtonClick() {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => VehicleAssistPastDocumentsPage(),
    ).then((value) => {
          Navigator.of(context).pop(),
          _vehicleAssistBloc.fetchVehicleInfo(),
        });
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  void _appPermissionConfirmClick() {
    AppSettings.openAppSettings();
  }
}
