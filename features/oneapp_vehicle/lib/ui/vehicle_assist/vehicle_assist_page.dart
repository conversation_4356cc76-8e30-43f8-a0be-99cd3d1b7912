// Dart imports:
import 'dart:ui';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/no_data_widget.dart';
import 'package:oneapp_common/widget/toast/toast_message_border.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_health_helper.dart';

// Project imports:
import '../../log/vehicle_marketing_event.dart';
import '/ui/vehicle_assist/vehicle_assist_bloc.dart';
import 'vehicle_assist_past_documents.dart';
import 'vehicle_collision_assitance/vehicle_collision_assistance_start_page.dart';
import 'vehicle_collision_assitance/vehicle_collision_faq.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/document_collision_entity.dart'
    as documentCollision;

class VehicleAssistPage extends StatefulWidget {
  @override
  _VehicleAssistPageState createState() => _VehicleAssistPageState();
}

class _VehicleAssistPageState extends State<VehicleAssistPage> {
  VehicleAssistBloc _bloc = VehicleAssistBloc();
  ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;

  @override
  void initState() {
    super.initState();
    _bloc.init();
    _bloc.phonePermissionDenied.listen((event) {
      if (event == true) {
        showPhonePermissionPopup(context);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _bloc.dispose();
  }

  @override
  void didUpdateWidget(covariant VehicleAssistPage oldWidget) {
    _colorUtil = ThemeConfig.current().colorUtil;
    _textStyleUtil = ThemeConfig.current().textStyleUtil;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        child: OneAppScaffold(
          body: _checkVinList(),
        ),
        bloc: _bloc);
  }

  Widget _checkVinList() {
    return StreamBuilder<bool>(
        stream: _bloc.isVinListEmpty,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data!) {
            return _blurWholePage();
          } else {
            return _buildBody(false);
          }
        });
  }

  Widget _buildBody(bool isVinListEmpty) {
    return SafeArea(
      child: StreamBuilder<bool>(
          stream: _bloc.showNoFeaturesAvailable,
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data!) {
              return NoDataAvailable(
                noDataMessage: OneAppString.of().someFeaturesUnavailable,
              );
            } else {
              return ListView(
                padding: EdgeInsets.only(
                    top: isVinListEmpty ? kToolbarHeight : 0,
                    left: 16.w,
                    right: 16.w),
                children: [
                  _roadSideAssistLayout(),
                  _collisionAssistLayout(),
                  SizedBox(
                    height: 21.h,
                  ),
                ],
              );
            }
          }),
    );
  }

  //Add Vehicle hint shown when vehicle list is empty
  Widget _addVehicleHintLayout(String title, String description) {
    return Wrap(
      children: [
        Container(
          width: ScreenUtil().screenWidth / 1.5,
          decoration: ShapeDecoration(
            color: _colorUtil.tertiary03,
            shape: ToastMessageBorder(usePadding: true),
          ),
          alignment: Alignment.center,
          padding:
              EdgeInsets.only(left: 8.w, top: 16.h, bottom: 16.h, right: 8.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.subHeadline3, _colorUtil.tertiary15),
              ),
              Text(
                description,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body3, _colorUtil.tertiary15),
              )
            ],
          ),
        ),
      ],
    );
  }

  //If vin list is empty then assist page should be displayed with a blur effect.
  Widget _blurWholePage() {
    return Stack(
      children: [
        _buildBody(true),
        Positioned.fill(
          child: Container(
            child: ClipRRect(
              child: BackdropFilter(
                // To blur the parent widget i.e stack
                filter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
                child: Container(
                  width: double.maxFinite,
                  color: _colorUtil.tertiary00.withOpacity(0.3), // Blur colour.
                ),
              ),
            ),
          ),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: _addVehicleHintLayout(
            OneAppString.of().addAVehicle,
            OneAppString.of().addVehicleDescription,
          ),
        ),
      ],
    );
  }

  // Road side assistance layout
  Widget _roadSideAssistLayout() {
    return StreamBuilder<bool>(
      stream: _bloc.isRoadSideAssistFeatureEnabled,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data!) {
          return Card(
            color: _colorUtil.tile01,
            elevation: 15.h,
            shadowColor: Color.fromRGBO(0, 0, 0, 0.07),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Padding(
              padding: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 16.h),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _headerLayout(
                      formatTextForLexusAndToyota(
                          OneAppString.of().roadsideAssistanceHeading),
                      roadSideAssistanceIcon,
                      _colorUtil.button03c,
                      _colorUtil.button01a),
                  SizedBox(
                    height: 8.h,
                  ),
                  _roadSideAssistanceBodyLayout(),
                ],
              ),
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }

  Widget _collisionAssistLayout() {
    return StreamBuilder<bool>(
      stream: _bloc.isCollisionAssistFeatureEnabled,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data!) {
          return StreamBuilder<bool>(
              stream: _bloc.isCollisionAssistDisclaimerText,
              builder: (context, snapshotData) {
                if (snapshotData.hasData && snapshotData.data!) {
                  return StreamBuilder<List<VehicleHealthHelper>>(
                    stream: _bloc.vehicleCollisionCarouselCardList,
                    builder: (context, carouselCardSnapshot) {
                      if (carouselCardSnapshot.hasData) {
                        return Card(
                          color: _colorUtil.tile01,
                          elevation: 15.h,
                          shadowColor: Color.fromRGBO(0, 0, 0, 0.07),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Padding(
                            padding:
                                EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 16.h),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _headerLayout(
                                    formatTextForLexusAndToyota(
                                        OneAppString.of().collisionAssistance),
                                    collisionAssistanceIcon,
                                    _colorUtil.button03b,
                                    _colorUtil.button01a),
                                SizedBox(
                                  height: 12.h,
                                ),
                                _collisionAssistBodyLayout()
                              ],
                            ),
                          ),
                        );
                      } else {
                        return Container();
                      }
                    },
                  );
                } else {
                  return Container();
                }
              });
        } else {
          return Container();
        }
      },
    );
  }

  Widget _headerLayout(String headerText, String headerIcon,
      Color headerIconBackgroundColor, Color headerIconColor) {
    return Row(
      children: [
        Container(
          width: 48.w,
          height: 48.w,
          decoration: BoxDecoration(
            color: headerIconBackgroundColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: SvgPicture.asset(
              headerIcon,
              colorFilter: ColorFilter.mode(
                headerIconColor,
                BlendMode.srcIn,
              ),
              width: 18.w,
              height: 18.w,
              semanticsLabel: PARTS_ACCESSORIES_ICON,
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Text(
            formatTextForLexusAndToyota(headerText),
            style: TextStyleExtension()
                .newStyleWithColor(_textStyleUtil.body4, _colorUtil.tertiary03),
            softWrap: true,
          ),
        ),
      ],
    );
  }

  Widget _roadSideAssistanceBodyLayout() {
    return Container(
      decoration: BoxDecoration(
        color: _colorUtil.tile02,
        borderRadius: BorderRadius.all(Radius.circular(6.r)),
      ),
      padding: EdgeInsets.fromLTRB(10.w, 16.h, 10.w, 16.h),
      child: Container(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              formatTextForLexusAndToyota(OneAppString.of().getHelpOnTheRoad),
              style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.subHeadline2,
                _colorUtil.tertiary03,
              ),
            ),
            SizedBox(height: 8.h),
            StreamBuilder<String>(
                stream: _bloc.roadSideAssistanceSubHeading,
                initialData: OneAppString.of()
                    .roadsideAssistanceSubHeading(OneAppString.of().toyotaName),
                builder: (context, snapshot) {
                  return Text(
                    snapshot.data!,
                    textAlign: TextAlign.center,
                    style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1,
                      _colorUtil.tertiary05,
                    ),
                  );
                }),
            GestureDetector(
              onTap: () {
                _bloc.openFaq();
              },
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 5.h),
                child: Text(
                  formatTextForLexusAndToyota(OneAppString.of().assistSeeFaq),
                  style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.callout2,
                    _colorUtil.tertiary03,
                  ),
                ),
              ),
            ),
            SizedBox(height: 10.h),
            StreamBuilder<String>(
                stream: _bloc.roadSideAssistanceButtonText,
                initialData: formatTextForLexusAndToyota(OneAppString.of()
                    .callToyotaRoadside(OneAppString.of().toyotaName)),
                builder: (context, snapshot) {
                  return Align(
                    alignment: Alignment.bottomCenter,
                    child: CustomDefaultButton(
                      text: snapshot.data,
                      borderColor: _colorUtil.primaryButton02,
                      buttonTextColor: _colorUtil.primaryButton01,
                      backgroundColor: _colorUtil.primaryButton02,
                      press: _roadSideAssistanceClickListener,
                      horizontalPadding: 26.w,
                      verticalPadding: 16.h,
                      textAlign: TextAlign.center,
                    ),
                  );
                }),
          ],
        ),
      ),
    );
  }

  Widget _collisionAssistBodyLayout() {
    return Container(
      height: 212.h,
      decoration: BoxDecoration(
        color: _colorUtil.tile02,
        borderRadius: BorderRadius.all(Radius.circular(8.r)),
      ),
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 8.h),
      child: Column(
        children: [
          StreamBuilder<String?>(
              stream: _bloc.disclaimerText,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return Text(
                    formatTextForLexusAndToyota(snapshot.data!),
                    textAlign: TextAlign.center,
                    style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1,
                      _colorUtil.error01,
                    ),
                  );
                } else {
                  return Container();
                }
              }),
          SizedBox(
            height: 36.h,
          ),
          InkWell(
            onTap: () => _openCollisionFAQPage(),
            child: Text(
              formatTextForLexusAndToyota(
                  OneAppString.of().collisionAssistFAQCta),
              textAlign: TextAlign.center,
              style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.callout2,
                _colorUtil.button02a,
              ),
            ),
          ),
          SizedBox(height: 20.h),
          Align(
            alignment: Alignment.bottomCenter,
            child: CustomDefaultButton(
              text: formatTextForLexusAndToyota(OneAppString.of().viewDocument),
              borderColor: _colorUtil.primaryButton02,
              buttonTextColor: _colorUtil.primaryButton01,
              backgroundColor: _colorUtil.primaryButton02,
              press: () => _collisionButtonClick(null, "1"),
              horizontalPadding: 16.w,
              verticalPadding: 16.h,
            ),
          ),
        ],
      ),
    );
  }

  _openCollisionFAQPage() {
    showMaterialModalBottomSheet(
        expand: true,
        isDismissible: true,
        context: context,
        useRootNavigator: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(CARD_RADIUS),
          ),
        ),
        builder: (context) => VehicleCollisionFAQPage());
  }

  // Click listener for Button in road side assistance card
  void _roadSideAssistanceClickListener() {
    _registerFirebaseEvent(VehicleMarketingEvent.DASHBOARD_ROADSIDE_ASSISTANCE);
    _bloc.callRoadsideAssistance();
  }

  // Bottom Navigation Sheet for collision assistance
  _collisionButtonClick(
      documentCollision.ScreenDetails? screenDetails, String screenNo) {
    _registerFirebaseEvent(
        VehicleMarketingEvent.DASHBOARD_COLLISION_ASSIST_DISPLAYED);
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => _documentCollisionPage(screenDetails, screenNo),
    ).then((value) {
      _bloc.fetchVehicleInfo();
    });
  }

  Widget _documentCollisionPage(
      documentCollision.ScreenDetails? screenDetails, String screenNo) {
    if (screenNo == "1") {
      //if screenNo == 1 move to passDocuments page
      return VehicleAssistPastDocumentsPage();
    } else if (screenNo == '2') {
      //If screenNo == 2 Doc collision is requested already and status is file preparation is in progress.
      return VehicleCollisionDocumentStartPage(
        screenDetails: screenDetails,
      );
    } else {
      return Container(
        color: Colors.white,
        child: Center(
          child: Text(
            formatTextForLexusAndToyota(OneAppString.of().inProgress),
            style: _textStyleUtil.title1,
          ),
        ),
      );
    }
  }

  void _registerFirebaseEvent(String childEvent) {
    FireBaseAnalyticsLogger.logMarketingGroupEvent(
        VehicleMarketingEvent.DASHBOARD_CARD,
        childEventName: childEvent);
  }
}
