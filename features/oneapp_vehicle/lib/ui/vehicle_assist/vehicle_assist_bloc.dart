// Dart imports:
import 'dart:async';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_health_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '/local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/document_collision_entity.dart'
    as documentCollision;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleAssistBloc extends BlocBase {
  vehicleInfo.Payload? vehicleItem;
  GetIt locator = GetIt.instance;
  documentCollision.DocumentCollisionPayload? documentCollisionPayload;
  bool isLMEX = false;

  Stream<bool> get isRoadSideAssistEnabled => _isRoadSideAssistEnabled.stream;
  final _isRoadSideAssistEnabled = BehaviorSubject<bool>();

  Stream<List<VehicleHealthHelper>> get vehicleCollisionCarouselCardList =>
      _vehicleCollisionCarouselCardList.stream;
  final _vehicleCollisionCarouselCardList =
      BehaviorSubject<List<VehicleHealthHelper>>();

  Stream<bool> get isVinListEmpty => _isVinListEmpty.stream;
  final _isVinListEmpty = BehaviorSubject<bool>();

  Stream<bool> get phonePermissionDenied => _phonePermissionDenied.stream;
  final _phonePermissionDenied = BehaviorSubject<bool>();

  Stream<String?> get vehicleName => _vehicleName.stream;
  final _vehicleName = BehaviorSubject<String?>();

  Stream<List<VehicleHealthHelper>> get vehicleCollisionPastDocumentsCardList =>
      _vehicleCollisionPastDocumentsCardList.stream;
  final _vehicleCollisionPastDocumentsCardList =
      BehaviorSubject<List<VehicleHealthHelper>>();

  Stream<bool> get isRoadSideAssistFeatureEnabled =>
      _isRoadSideAssistFeatureEnabled.stream;
  final _isRoadSideAssistFeatureEnabled = BehaviorSubject<bool>();

  Stream<bool> get isCollisionAssistFeatureEnabled =>
      _isCollisionAssistFeatureEnabled.stream;
  final _isCollisionAssistFeatureEnabled = BehaviorSubject<bool>();

  Stream<bool> get isCollisionAssistDisclaimerText =>
      _isCollisionAssistDisclaimerText.stream;
  final _isCollisionAssistDisclaimerText = BehaviorSubject<bool>();

  Stream<String> get roadSideAssistanceButtonText =>
      _roadSideAssistanceButtonText.stream;
  final _roadSideAssistanceButtonText = BehaviorSubject<String>();

  Stream<String> get roadSideAssistanceSubHeading =>
      _roadSideAssistanceSubHeading.stream;
  final _roadSideAssistanceSubHeading = BehaviorSubject<String>();

  Stream<bool> get showNoFeaturesAvailable => _showNoFeaturesAvailable.stream;
  final _showNoFeaturesAvailable = BehaviorSubject<bool>();

  Stream<String?> get disclaimerText => _disclaimerText.stream;
  final _disclaimerText = BehaviorSubject<String?>();

  void init() {
    fetchVehicleInfo();
  }

  // Fetch vehicle info to get conditions
  Future<void> fetchVehicleInfo() async {
    String? vin = Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);

    locator.isReady<List<vehicleInfo.Payload>>().then((value) async {
      List<vehicleInfo.Payload> vehicleListPayload = [];
      try {
        vehicleListPayload = locator<List<vehicleInfo.Payload>>();
        _isVinListEmpty.sink.add(false);
      } catch (e) {}

      if (vehicleListPayload.isEmpty) {
        _isVinListEmpty.sink.add(true);
        fetchCollisionAssistanceCards();
      } else {
        if (vehicleItem != null) {
          bool isNonCvtVehicle = vehicleItem!.nonCvtVehicle == true;
          updateRoadSideAssistButtonText();
          if (vehicleItem!.generation != null && !isNonCvtVehicle) {
            if (isRemoteSubscriptionNotAvailable(vehicleItem!)) {
              _isRoadSideAssistEnabled.sink.add(true);
            }
          }
          VehicleRepo()
              .storeVehicleCollisionDocument(vehicleItem)
              .then((value) => fetchCollisionAssistanceCards());
          _vehicleName.sink.add(vehicleItem!.modelName);
        }
      }
    });
    if (vehicleItem != null) {
      checkRoadSideAndCollisionAssistFeatureEnabledOrNot();
    }
  }

  void checkRoadSideAndCollisionAssistFeatureEnabledOrNot() {
    if (isFeatureEnabled(ROAD_SIDE_ASSISTANCE, vehicleItem!.features)) {
      _isRoadSideAssistFeatureEnabled.sink.add(true);
    } else {
      _isRoadSideAssistFeatureEnabled.sink.add(false);
    }

    if (isFeatureEnabled(COLLISION_ASSISTANCE, vehicleItem!.features)) {
      _isCollisionAssistFeatureEnabled.sink.add(true);
    } else {
      _isCollisionAssistFeatureEnabled.sink.add(false);
    }

    if (!isFeatureEnabled(COLLISION_ASSISTANCE, vehicleItem!.features) &&
        !isFeatureEnabled(ROAD_SIDE_ASSISTANCE, vehicleItem!.features)) {
      _showNoFeaturesAvailable.add(true);
    } else {
      _showNoFeaturesAvailable.add(false);
    }
  }

  // Fetch collision assistance datas
  void fetchCollisionAssistanceCards() {
    if (vehicleItem != null) {
      documentCollisionPayload = null;
      try {
        documentCollisionPayload =
            locator<documentCollision.DocumentCollisionPayload>();
        if (documentCollisionPayload != null &&
            documentCollisionPayload!.disclaimerText != null) {
          Global.getInstance().setCollisionVin(Global.getInstance().vin);
          formCollisionCarouselCard();
          _disclaimerText.sink.add(documentCollisionPayload!.disclaimerText);
          _isCollisionAssistDisclaimerText.sink.add(true);
        } else {
          _isCollisionAssistDisclaimerText.sink.add(false);
        }
      } catch (e) {
        FireBaseAnalyticsLogger.logError(e.toString(),
            category: LogCategory.FL_VEHI);
      }
    }
  }

  Future<bool> checkLMEX() async {
    String? vin = Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);
    isLMEX = isLmexAvailable(vehicleItem!);
    return isLMEX;
  }

  // Convert the data into new model to show them in an carousel card
  void formCollisionCarouselCard() {
    List<VehicleHealthHelper> cardsList = <VehicleHealthHelper>[];
    List<VehicleHealthHelper> pastDocuments = <VehicleHealthHelper>[];
    if (documentCollisionPayload!.collisionData != null) {
      for (final documentCollision.CollisionData data
          in documentCollisionPayload!.collisionData!) {
        if (data.displayDetails != null &&
            data.displayDetails!.cardDetails != null) {
          cardsList.add(VehicleHealthHelper(
              iconPath: data.displayDetails?.cardDetails?.image,
              title: data.displayDetails?.cardDetails?.title,
              subTitle: data.displayDetails?.cardDetails?.messageTitle,
              description: data.displayDetails?.cardDetails?.message,
              buttonText: getButtonText(data.displayDetails!),
              isNetworkImage: true,
              cta: data.displayDetails?.cardDetails?.cta,
              collisionId: data.collisionId));
          if (data.displayDetails?.cardDetails?.cta == "3" ||
              data.displayDetails?.cardDetails?.cta == "4") {
            pastDocuments.add(VehicleHealthHelper(
                iconPath: data.displayDetails?.cardDetails?.image,
                title: data.displayDetails?.cardDetails?.title,
                subTitle: data.displayDetails?.cardDetails?.messageTitle,
                description: data.displayDetails?.cardDetails?.message,
                buttonText: getButtonText(data.displayDetails!),
                isNetworkImage: true,
                cta: data.displayDetails?.cardDetails?.cta,
                collisionId: data.collisionId));
          }
        }
      }
    }
    _vehicleCollisionCarouselCardList.sink.add(cardsList);
    _vehicleCollisionPastDocumentsCardList.sink.add(pastDocuments);
  }

  String? getButtonText(documentCollision.DisplayDetails displayDetails) {
    String? buttonText = displayDetails.cardDetails!.buttonText;
    if (displayDetails.cardDetails!.cta == "3") {
      buttonText = OneAppString.of().checkStatus;
    } else if (displayDetails.cardDetails!.cta == "4") {
      buttonText = OneAppString.of().commonContinue;
    }
    return buttonText;
  }

  // Call roadside assistance
  void callRoadsideAssistance() async {
    bool isToyota = vehicleItem != null &&
            vehicleItem!.brand != null &&
            vehicleItem!.brand!.toLowerCase() == "t"
        ? true
        : false;

    bool isNG86 = vehicleItem != null && vehicleItem!.generation != null
        ? isNG86TypeVehicle(vehicleItem!.generation!.toLowerCase())
        : false;

    dialerLauncherPermissionDialog(getRoadsideAssistanceContactNumber(
            vehicleItem != null && vehicleItem!.region != null
                ? vehicleItem!.region!
                : "US",
            isToyota,
            isNG86))
        .then((value) {
      if (value == false) _phonePermissionDenied.sink.add(true);
    });
  }

  void updateRoadSideAssistButtonText() {
    if (vehicleItem!.brand != null) {
      if (vehicleItem!.brand!.toLowerCase() == "t") {
        _roadSideAssistanceButtonText.sink.add(formatTextForLexusAndToyota(
            OneAppString.of()
                .callToyotaRoadside(OneAppString.of().toyotaName)));
        _roadSideAssistanceSubHeading.sink.add(OneAppString.of()
            .roadsideAssistanceSubHeading(OneAppString.of().toyotaName));
      } else {
        _roadSideAssistanceButtonText.sink.add(formatTextForLexusAndToyota(
            OneAppString.of().callToyotaRoadside(OneAppString.of().lexusName)));
        checkLMEX().then((value) {
          if (value) {
            _roadSideAssistanceSubHeading.sink
                .add(OneAppString.of().lexusExperience);
          } else {
            _roadSideAssistanceSubHeading.sink.add(OneAppString.of()
                .roadsideAssistanceSubHeading(OneAppString.of().lexusName));
          }
        });
      }
    } else {
      _roadSideAssistanceButtonText.sink.add(formatTextForLexusAndToyota(
          OneAppString.of().callToyotaRoadside(OneAppString.of().toyotaName)));
      _roadSideAssistanceSubHeading.sink.add(OneAppString.of()
          .roadsideAssistanceSubHeading(OneAppString.of().toyotaName));
    }
  }

  void updateAppClickListener() {
    appUpdateRequired();
  }

  Future<bool> checkAppVersion() async {
    bool status = false;
    int currentVersion = await getExtendedVersionNumber(
        Global.getInstance().appVersion.split("-").first);
    int expectedVersion = await getExtendedVersionNumber(JANUS_VERSION);
    if (currentVersion < expectedVersion) {
      status = true;
    } else {
      status = false;
    }
    return status;
  }

  // Open FAQ Url in browser with respect to vehicle
  void openFaq() {
    urlLauncher(vehicleItem!.faqUrl!,
        forceWebView: false, enableJavaScript: true);
  }

  @override
  void dispose() {
    _isRoadSideAssistEnabled.close();
    _vehicleCollisionCarouselCardList.close();
    _isVinListEmpty.close();
    _phonePermissionDenied.close();
    _vehicleName.close();
    _vehicleCollisionPastDocumentsCardList.close();
    _isRoadSideAssistFeatureEnabled.close();
    _isCollisionAssistFeatureEnabled.close();
    _roadSideAssistanceButtonText.close();
    _roadSideAssistanceSubHeading.close();
    _showNoFeaturesAvailable.close();
    _isCollisionAssistDisclaimerText.close();
    _disclaimerText.close();
  }
}
