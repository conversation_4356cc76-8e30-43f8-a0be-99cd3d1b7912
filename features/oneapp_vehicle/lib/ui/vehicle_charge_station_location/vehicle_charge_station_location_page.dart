// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart' hide SearchBar;

// Package imports:
import 'package:app_settings/app_settings.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' hide Visibility;
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/debouncer.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/custom_divider.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/form/search_bar.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_station_location_entity.dart';

// Project imports:
import '/log/vehicle_analytic_event.dart';
import 'vehcle_charge_station_location_list_card.dart';
import 'vehicle_charge_station_location_bloc.dart';
import 'vehicle_charge_station_location_map.dart';

final _stationsDebounce = Debouncer(milliseconds: 1000);

class VehicleChargeStationLocationPage extends StatefulWidget {
  final bool? isFromNative;

  VehicleChargeStationLocationPage({this.isFromNative});

  @override
  _VehicleChargeStationLocationPageState createState() =>
      _VehicleChargeStationLocationPageState();
}

class _VehicleChargeStationLocationPageState
    extends State<VehicleChargeStationLocationPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  TextEditingController _searchController = TextEditingController();
  VehicleChargeStationLocationBloc _bloc = VehicleChargeStationLocationBloc();
  final GlobalKey<VehicleChargeStationLocationMapState> _mapKey = GlobalKey();
  final _debouncer = Debouncer(milliseconds: 3000);
  BuildContext? _draggableSheetContext;
  bool isVehicleLocationEnabled = true;
  String evChargeLevelOneAndTwo = '1,2';
  String evChargeLevelTwo = '2';
  String defaultChargeLevel = "0";

  double _initialSheetChildSize = BOTTOM_SHEET_PERCENT_50;
  double _dragScrollSheetExtent = 0;

  double _widgetHeight = 0;
  double _fabPosition = 0;
  double _mobileHeight = 0;
  double _mapBottomMargin = 0;

  bool isSheetPresented = false;

  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _stationsDebounce.run(() {
      FireBaseAnalyticsLogger.logScreenVisit(
          VehicleAnalyticsEvent.VEHICLE_PREFERRED_SERVICE_PAGE);
      _bloc.init(_progressHandlerCallback, _dialogHandlerCallback);
      _searchController.addListener(_latestSearchValue);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _fabPosition = _initialSheetChildSize * context.size!.height;
        _bloc.changeFabPosition(_fabPosition);
      });
      _bloc.appLocationDenied.listen((event) {
        if (event == true && isSheetPresented == false) {
          isSheetPresented = true;
          BottomConfirmationDialog().showBottomDialog(
              context,
              OneAppString.of().locationPermissionHeading,
              OneAppString.of().locationPermissionHeading,
              mapLocationPinIcon,
              OneAppString.of().commonOK,
              OneAppString.of().commonCancel,
              _appPermissionConfirmClick,
              cancelCallback: _cancelClick);
        }
      });
      _bloc.phonePermissionDenied.listen((event) {
        if (event == true) {
          showPhonePermissionPopup(context);
        }
      });
    });
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _mobileHeight = MediaQuery.of(context).size.height;
    _mapBottomMargin =
        (_mobileHeight <= 720) ? _mobileHeight / 2.2 : _mobileHeight / 2.5;
    return BlocProvider(
      bloc: _bloc,
      child: OneAppScaffold(
        backgroundColor: Colors.transparent,
        resizeToAvoidBottomInset: true,
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return SafeArea(
      top: true,
      child: Stack(
        children: [
          PopScope(
            onPopInvoked: _willPopCallback,
            child: Stack(
              children: [
                StreamBuilder<int>(
                    stream: _bloc.showPreferredVehicleSheet,
                    builder: (context, snapshot) {
                      return _buildMap((snapshot.hasData && snapshot.data == 0)
                          ? _mapBottomMargin
                          : 0);
                    }),
              ],
            ),
          ),
          if (isLoading)
            Container(
              alignment: Alignment.center,
              color: Colors.white.withOpacity(0.3),
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }

// Search box to search locations
  Widget _searchLayout() {
    return StreamBuilder<bool>(
        stream: _bloc.showPrefixIcon,
        builder: (context, snapshot) {
          return SearchBar(
            suffixIconColor: _colorUtil.tertiary07,
            prefixIconColor: _colorUtil.tertiary03,
            prefixSvgPath: searchIcon,
            suffixSvgPath: removeIcon,
            textEditingController: _searchController,
            hintText: OneAppString.of().searchMapHint,
            suffixVisibility:
                (snapshot.hasData && snapshot.data!) ? true : false,
            suffixIconClickCallBack: _removeIconClick,
            submitCallBackListener: _submitButtonClick,
            textColor: _colorUtil.tertiary03,
            backGroundColor: _colorUtil.tile05,
            elevation: 0,
            cursorColor: _colorUtil.tertiary05,
          );
        });
  }

  Widget _buildMap(double mapBottomMargin) {
    return StreamBuilder<List<FuelStations>>(
        stream: _bloc.chargeStationList,
        builder: (context, snapshot) {
          return _serviceDealerMap(
              context, snapshot.hasData ? snapshot.data : [], mapBottomMargin);
        });
  }

  // Map containing service dealers locations
  Widget _serviceDealerMap(BuildContext context,
      List<FuelStations>? chargeStationLocationInfo, double mapBottomMargin) {
    return Stack(
      children: <Widget>[
        StreamBuilder<Position?>(
            stream: _bloc.preferredLatLng,
            builder: (context, snapshot) {
              return AnimatedContainer(
                duration: Duration(milliseconds: 250),
                margin:
                    EdgeInsets.only(bottom: (ScreenUtil().screenHeight * 0.30)),
                child: VehicleChargeStationLocationMap(
                    key: _mapKey,
                    chargeStationInfoList: chargeStationLocationInfo,
                    preferredLoc: (snapshot.hasData && snapshot.data != null)
                        ? snapshot.data!
                        : null,
                    vehiclePosition: isVehicleLocationEnabled
                        ? _bloc.vehicleLocation
                        : _bloc.currentLocation,
                    pinClickListener: pinClickCallback,
                    isVehicleIconClicked: isVehicleLocationEnabled,
                    isMiraiTypeVehicle: _bloc.isMiraiTypeVehicle),
              );
            }),
        StreamBuilder<bool>(
            stream: _bloc.showIcons,
            builder: (context, snapshot) {
              return Positioned(
                top: 60.h,
                left: 0,
                right: 0,
                child: Align(
                  alignment: Alignment.bottomRight,
                  child: Container(
                    margin:
                        EdgeInsets.only(top: ScreenUtil().screenHeight * 0.15),
                    child: Column(
                      children: [
                        Visibility(
                          visible: snapshot.hasData && snapshot.data!,
                          child: InkWell(
                            onTap: () async {
                              isVehicleLocationEnabled = true;
                              final vehicleLocation =
                                  await _bloc.getVehicleLocation();
                              _animateMap(vehicleLocation);
                            },
                            child: Container(
                              margin: EdgeInsets.only(right: 14.w),
                              padding: EdgeInsets.all(12.w),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: _colorUtil.tile01,
                              ),
                              child: SvgPicture.asset(
                                vehicleIcon,
                                height: 20.h,
                                width: 30.w,
                                colorFilter: ColorFilter.mode(
                                  _colorUtil.button02a,
                                  BlendMode.srcIn,
                                ),
                                allowDrawingOutsideViewBox: true,
                                semanticsLabel: VEHICLE_ICON,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 16.h),
                        InkWell(
                          onTap: () async {
                            isVehicleLocationEnabled = false;
                            final location = await _bloc.showDeviceLocation();
                            _animateMap(location);
                          },
                          child: Container(
                            margin: EdgeInsets.only(right: 14.w),
                            padding: EdgeInsets.all(12.w),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _colorUtil.tile01,
                            ),
                            child: SvgPicture.asset(
                              navigationIcon,
                              height: 20.h,
                              width: 30.w,
                              colorFilter: ColorFilter.mode(
                                _colorUtil.button02a,
                                BlendMode.srcIn,
                              ),
                              allowDrawingOutsideViewBox: true,
                              semanticsLabel: NAVIGATION_ICON,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
        Align(
          alignment: AlignmentDirectional.bottomEnd,
          child: Container(
            child: StreamBuilder<int>(
                stream: _bloc.showPreferredVehicleSheet,
                builder: (context, snapshot) {
                  int? visibleItem = 1;
                  visibleItem =
                      (snapshot.hasData) ? snapshot.data! : visibleItem;
                  if (visibleItem == 0) {
                    return StreamBuilder<FuelStations>(
                        stream: _bloc.preferredChargeStation,
                        builder: (context, snapshot) {
                          return (snapshot.hasData)
                              ? _selectedDealerBottomSheet(snapshot.data)
                              : Container(child: _emptyBottomSheet());
                        });
                  } else {
                    return NotificationListener<
                            DraggableScrollableNotification>(
                        onNotification:
                            (DraggableScrollableNotification notification) {
                          _widgetHeight = context.size!.height;
                          _dragScrollSheetExtent = notification.extent;
                          // Calculate FAB position based on parent widget height and DraggableScrollable position
                          _fabPosition = _dragScrollSheetExtent * _widgetHeight;
                          _bloc.changeFabPosition(_fabPosition);
                          return true;
                        },
                        child: StreamBuilder<List<FuelStations>>(
                            stream: _bloc.chargeStationSearchList,
                            builder: (context,
                                AsyncSnapshot<List<FuelStations>> snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.waiting) {
                                return AnimatedContainer(
                                    duration: Duration(milliseconds: 250),
                                    child: _emptyBottomSheet());
                              } else {
                                return _dealerListBottomSheet(snapshot.data);
                              }
                            }));
                  }
                }),
          ),
        ),
      ],
    );
  }

  Widget _emptyBottomSheet() {
    return DraggableScrollableSheet(
      initialChildSize: BOTTOM_SHEET_PERCENT_60,
      maxChildSize: BOTTOM_SHEET_PERCENT_60,
      minChildSize: BOTTOM_SHEET_PERCENT_60,
      builder: (BuildContext context, ScrollController scrollController) {
        return Container(
          padding: EdgeInsets.only(left: 16.w, right: 16.w),
          decoration: _colorUtil.bottomSheetDecorator(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _bottomSheetHeader(OneAppString.of().nearbyStation,
                  onBackPressed: _popBack),
              Expanded(
                child: Container(),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _bottomSheetHeader(String title, {VoidCallback? onBackPressed}) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 250),
      child: Column(
        children: [
          SwipeBarIcon(),
          Container(
            height: kToolbarHeight,
            child: Stack(
              fit: StackFit.loose,
              children: [
                Positioned.fill(
                  left: 45.w,
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(formatTextForLexusAndToyota(title),
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.subHeadline3,
                              _colorUtil.tertiary03)),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: InkWell(
                      onTap: onBackPressed,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _colorUtil.tertiary12,
                          shape: BoxShape.circle,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(8.w),
                          child: Icon(
                            Icons.chevron_left,
                            color: _colorUtil.tertiary00,
                            semanticLabel: BACK_BUTTON,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _popBack() {
    if (widget.isFromNative ?? true) {
      goBackToNative();
    } else {
      Navigator.of(context).pop(true);
    }
  }

  // Bottom sheet with list of dealers
  _dealerListBottomSheet(List<FuelStations>? chargeStationLocationInfo) {
    return DraggableScrollableActuator(
        child: DraggableScrollableSheet(
      expand: false,
      key: Key(_initialSheetChildSize.toString()),
      initialChildSize: BOTTOM_SHEET_PERCENT_60,
      maxChildSize: BOTTOM_SHEET_PERCENT_90,
      minChildSize: BOTTOM_SHEET_PERCENT_35,
      builder: (BuildContext context, ScrollController scrollController) {
        _draggableSheetContext = context;
        return Padding(
          //This padding is to avoid keyboard hiding the search field when it is focused.
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            decoration: _colorUtil.bottomSheetDecorator(),
            child: chargeStationLocationInfo != null &&
                    chargeStationLocationInfo.isNotEmpty
                ? _dealerListLayout(scrollController, chargeStationLocationInfo)
                : _dealerListLayout(
                    scrollController, chargeStationLocationInfo),
          ),
        );
      },
    ));
  }

  Widget _dealerListLayout(ScrollController scrollController,
      List<FuelStations>? chargeStationLocationInfo) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 250),
      child: SingleChildScrollView(
        controller: scrollController,
        physics: NeverScrollableScrollPhysics(),
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        child: SizedBox(
          height: MediaQuery.of(context).size.height,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NotificationListener<OverscrollIndicatorNotification>(
                onNotification: (overScroll) {
                  overScroll.disallowIndicator();
                  return true;
                },
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: SizedBox(
                    child: Column(
                      children: [
                        _bottomSheetHeader(OneAppString.of().nearbyStation,
                            onBackPressed: _popBack),
                        _searchLayout(),
                        Container(
                          margin: EdgeInsets.only(top: 16.h, bottom: 16.h),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _bloc.isMiraiTypeVehicle
                                    ? OneAppString.of().fuelStations
                                    : OneAppString.of().chargeStations,
                                style: TextStyleExtension().newStyleWithColor(
                                    _textStyleUtil.body4,
                                    _colorUtil.tertiary03),
                              ),
                              Text(
                                OneAppString.of().countNearBy(
                                    (chargeStationLocationInfo != null)
                                        ? chargeStationLocationInfo.length
                                            .toString()
                                        : "-"),
                                style: TextStyleExtension().newStyleWithColor(
                                    _textStyleUtil.callout1,
                                    _colorUtil.tertiary05),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  margin: EdgeInsets.only(bottom: 150.h),
                  child: (chargeStationLocationInfo != null &&
                          chargeStationLocationInfo.isNotEmpty)
                      ? _chargeStationListView(
                          scrollController, chargeStationLocationInfo)
                      : _emptyPlaceHolderView(),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

// Empty View For Charge Station Non-Availability
  Widget _emptyPlaceHolderView() {
    return Visibility(
      visible: !_bloc.isApiLoading,
      child: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.all(8.w),
              child: Text(
                OneAppString.of().noChargeStationHintOne,
                textAlign: TextAlign.center,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.callout1, _colorUtil.tertiary05),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(8.w),
              child: Text(
                OneAppString.of().noChargeStationHintTwo,
                textAlign: TextAlign.center,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.callout1, _colorUtil.tertiary05),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Charge Station list item
  Widget _chargeStationListView(ScrollController scrollController,
      List<FuelStations> chargeStationLocationInfo) {
    Position? currentPosition = _bloc.currentLocation;
    return ListView.separated(
      separatorBuilder: (context, index) {
        return CustomDivider(
          lineColor: _colorUtil.tertiary10,
        );
      },
      shrinkWrap: true,
      controller: scrollController,
      itemCount: chargeStationLocationInfo.length,
      itemBuilder: (BuildContext context, int index) {
        return InkWell(
          onTap: () {
            if (chargeStationLocationInfo[index].latitude != null &&
                chargeStationLocationInfo[index].longitude != null) {
              FocusScope.of(context).unfocus();
              _searchController.clear();
              _bloc.toggleSearchView(0);
              _toggleDraggableScrollableSheet();
              _bloc.selectedDealer(chargeStationLocationInfo[index]);
              Future.delayed(const Duration(milliseconds: 400), () {
                _animateMapCameraZoom(
                    chargeStationLocationInfo[index].latitude!,
                    chargeStationLocationInfo[index].longitude!,
                    16);
              });
            }
          },
          child: VehicleChargeStationLocationCard(
            locationName: chargeStationLocationInfo[index].stationName,
            locationAddress:
                chargeStationLocationInfo[index].streetAddress! + ",",
            locationLongitude: chargeStationLocationInfo[index].longitude,
            locationLatitude: chargeStationLocationInfo[index].latitude,
            currentLocation: currentPosition,
            locationStateWithZip: chargeStationLocationInfo[index].city! +
                ", " +
                chargeStationLocationInfo[index].state! +
                " " +
                chargeStationLocationInfo[index].zip!,
            availability: chargeStationLocationInfo[index].availability,
            distance: chargeStationLocationInfo[index].distance != null
                ? chargeStationLocationInfo[index].distance
                : '-',
            level: (chargeStationLocationInfo[index].evChargeLevel2Count !=
                        null &&
                    chargeStationLocationInfo[index].evChargeLevel1Count !=
                        null)
                ? evChargeLevelOneAndTwo
                : chargeStationLocationInfo[index].evChargeLevel2Count != null
                    ? evChargeLevelTwo
                    : '-',
            chargers: (int.parse(
                        chargeStationLocationInfo[index].evChargeLevel2Count ??
                            defaultChargeLevel) +
                    int.parse(
                        chargeStationLocationInfo[index].evChargeLevel1Count ??
                            defaultChargeLevel))
                .toString(),
            isMiraiTypeVehicle: _bloc.isMiraiTypeVehicle,
            pressurePoints: chargeStationLocationInfo[index].pressures,
          ),
        );
      },
    );
  }

  // Bottom sheet with details about selected dealer
  Widget _selectedDealerBottomSheet(FuelStations? chargeStationLocationInfo) {
    return DraggableScrollableSheet(
      initialChildSize: BOTTOM_SHEET_PERCENT_60,
      maxChildSize: BOTTOM_SHEET_PERCENT_90,
      minChildSize: BOTTOM_SHEET_PERCENT_35,
      builder: (BuildContext context, ScrollController scrollController) {
        return AnimatedContainer(
          duration: Duration(milliseconds: 250),
          padding: EdgeInsets.only(left: 16.w, right: 16.w),
          decoration: _colorUtil.bottomSheetDecorator(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _bottomSheetHeader(OneAppString.of().chargeStationDetails,
                  onBackPressed: () {
                _bloc.updateNearByStationsVisibility();
              }),
              Expanded(
                child: _selectedDealerView(chargeStationLocationInfo!),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _selectedDealerView(FuelStations chargeStationLocationInfo) {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, top: 16.h, right: 16.w),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      chargeStationLocationInfo.stationName!,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body4, _colorUtil.tertiary03),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 8.h),
                      child: Text(
                        chargeStationLocationInfo.streetAddress! + ",",
                        maxLines: 1,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1, _colorUtil.tertiary05),
                        textAlign: TextAlign.start,
                      ),
                    ),
                    Text(
                      chargeStationLocationInfo.city! +
                          ", " +
                          chargeStationLocationInfo.state! +
                          " " +
                          chargeStationLocationInfo.zip!,
                      maxLines: 1,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1, _colorUtil.tertiary05),
                      textAlign: TextAlign.start,
                    ),
                    (chargeStationLocationInfo.availability == OPEN_24_HOURS ||
                            chargeStationLocationInfo.availability ==
                                WEEKLY_OPEN_24)
                        ? Text(
                            OneAppString.of().open24Hours,
                            maxLines: 1,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.callout1,
                                _colorUtil.secondary01),
                            textAlign: TextAlign.start,
                          )
                        : Container()
                  ],
                )),
                Container(
                  margin: EdgeInsets.only(top: 8.h),
                  child: Text(
                    (chargeStationLocationInfo.distance != null)
                        ? chargeStationLocationInfo.distance!
                        : _bloc.checkRegion(),
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05),
                  ),
                )
              ],
            ),
            Padding(
              padding: EdgeInsets.only(top: 16.h),
              child: CustomDivider(lineColor: _colorUtil.tertiary10),
            ),
            InkWell(
              onTap: () {
                _bloc.openMapPage(chargeStationLocationInfo.stationName);
              },
              child: Padding(
                padding: EdgeInsets.only(top: 22.h, bottom: 22.h),
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(right: 16.w),
                      child: SvgPicture.asset(
                        //Adhoc Image
                        mapLocationPinIcon,
                        height: 14.w,
                        width: 14.w,
                        colorFilter: ColorFilter.mode(
                          _colorUtil.tertiary03,
                          BlendMode.srcIn,
                        ),
                        allowDrawingOutsideViewBox: true,
                        semanticsLabel: MAP_LOCATION_PIN_ICON,
                      ),
                    ),
                    Text(
                      OneAppString.of().directions,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body3, _colorUtil.tertiary03),
                    ),
                  ],
                ),
              ),
            ),
            CustomDivider(lineColor: _colorUtil.tertiary10),
            InkWell(
              onTap: () {
                _bloc.openWebPage();
              },
              child: Padding(
                padding: EdgeInsets.only(top: 22.h, bottom: 22.h),
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(right: 16.w),
                      child: SvgPicture.asset(
                        webIcon,
                        height: 14.w,
                        width: 14.w,
                        colorFilter: ColorFilter.mode(
                          _colorUtil.tertiary03,
                          BlendMode.srcIn,
                        ),
                        allowDrawingOutsideViewBox: true,
                        semanticsLabel: WEB_ICON,
                      ),
                    ),
                    Text(
                      OneAppString.of().website,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body3, _colorUtil.tertiary03),
                    ),
                  ],
                ),
              ),
            ),
            CustomDivider(lineColor: _colorUtil.tertiary10),
            InkWell(
              onTap: () {
                _bloc.openPhoneDialer();
              },
              child: Padding(
                padding: EdgeInsets.only(top: 22.h, bottom: 22.h),
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(right: 16.w),
                      child: SvgPicture.asset(
                        mobileIcon,
                        height: 14.w,
                        width: 14.w,
                        colorFilter: ColorFilter.mode(
                          _colorUtil.tertiary03,
                          BlendMode.srcIn,
                        ),
                        allowDrawingOutsideViewBox: true,
                        semanticsLabel: MOBILE_ICON,
                      ),
                    ),
                    Text(
                      OneAppString.of().callChargeStation,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body3, _colorUtil.tertiary03),
                    ),
                  ],
                ),
              ),
            ),
            CustomDivider(lineColor: _colorUtil.tertiary10),
          ],
        ),
      ),
    );
  }

// Capture change in text from Textformfield widget
  void _latestSearchValue() {
    _debouncer.run(() {
      _bloc.searchValueChanged(_searchController.text);
    });
  }

  void _progressHandlerCallback(bool show) {
    if (!mounted) return;
    setState(() {
      isLoading = show;
    });
  }

  void _dialogHandlerCallback(String dialogText) {
    showBaseDialog(context, message: dialogText);
  }

  // Clear button in search field
  void _removeIconClick() {
    _searchController.clear();
  }

  // Submit button click handler in search field
  void _submitButtonClick(String searchText) {
    _bloc.handleDoneButton();
  }

  void _toggleDraggableScrollableSheet() {
    if (_draggableSheetContext != null) {
      _initialSheetChildSize = BOTTOM_SHEET_PERCENT_50;
      _fabPosition = _initialSheetChildSize * context.size!.height;
      _bloc.changeFabPosition(_fabPosition);
      DraggableScrollableActuator.reset(_draggableSheetContext!);
    }
  }

  void _animateMap(Position? position) {
    if (position != null) {
      _mapKey.currentState!.animateCamera(position);
    }
  }

  void _animateMapCameraZoom(
      double latitude, double longitude, double zoomLevel) {
    _mapKey.currentState!.animateCameraWithZoom(latitude, longitude, zoomLevel);
  }

  // void _backPressCallback() {
  //   Navigator.pop(context, true);
  // }

  void pinClickCallback(FuelStations chargeStationLocationPayloadFuelStation) {
    if (chargeStationLocationPayloadFuelStation.latitude != null &&
        chargeStationLocationPayloadFuelStation.longitude != null) {
      Future.delayed(const Duration(milliseconds: 400), () {
        _animateMapCameraZoom(chargeStationLocationPayloadFuelStation.latitude!,
            chargeStationLocationPayloadFuelStation.longitude!, 15);
      });
      _bloc.selectedDealer(chargeStationLocationPayloadFuelStation);
      _searchController.clear();
      _bloc.toggleSearchView(0);
      _toggleDraggableScrollableSheet();
    }
  }

  void _appPermissionConfirmClick() {
    isSheetPresented = false;
    AppSettings.openAppSettings();
  }

  void _cancelClick() {
    isSheetPresented = false;
  }

  Future<void> _willPopCallback(bool didPop) async {
    if (didPop) return;
    int? page = _bloc.getNearByStationsVisibilityStatus();
    if (page == null) {
      _popBack();
    }
    if (page == 0) {
      _bloc.updateNearByStationsVisibility();
    } else {
      _popBack();
    }
  }
}
