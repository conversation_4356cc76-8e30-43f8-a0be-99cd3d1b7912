// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_station_location_entity.dart';

class VehicleChargeStationLocationMap extends StatefulWidget {
  final List<FuelStations>? chargeStationInfoList;
  final Position? preferredLoc;
  final Position? vehiclePosition;
  final Function? pinClickListener;
  final Position? devicePosition;
  final bool isVehicleIconClicked;
  final bool isMiraiTypeVehicle;

  const VehicleChargeStationLocationMap({
    Key? key,
    this.chargeStationInfoList,
    this.preferredLoc,
    this.vehiclePosition,
    this.devicePosition,
    this.pinClickListener,
    this.isVehicleIconClicked = false,
    this.isMiraiTypeVehicle = false,
  }) : super(key: key);

  @override
  State createState() => VehicleChargeStationLocationMapState();
}

class VehicleChargeStationLocationMapState
    extends State<VehicleChargeStationLocationMap> {
  late MapboxMap mapboxMap;

  final defaultPosition = Position(-96.84320, 33.08118);

  @override
  Widget build(BuildContext context) {
    if (widget.chargeStationInfoList == null ||
        widget.chargeStationInfoList!.isEmpty) {
      return shimmerRectangle(
        double.maxFinite,
        double.maxFinite,
        CARD_RADIUS_SMALL,
      );
    } else {
      return MapWidget(
        androidHostingMode: AndroidPlatformViewHostingMode.TLHC_HC,
        styleUri:
            isDarkTheme() == true ? MapboxStyles.DARK : MapboxStyles.LIGHT,
        onMapCreated: _onMapCreated,
        cameraOptions: CameraOptions(
          zoom: 10,
          center: Point(
            coordinates: widget.vehiclePosition ?? defaultPosition,
          ),
        ),
      );
    }
  }

  void animateCameraWithZoom(double lat, double lon, double zoomLevel) async {
    await mapboxMap.setCamera(
      CameraOptions(
        zoom: zoomLevel,
        center: Point(
          coordinates: Position(lon, lat),
        ),
      ),
    );
  }

  void animateCamera(Position position) async {
    await mapboxMap
        .setCamera(CameraOptions(center: Point(coordinates: position)));
  }

  Future<void> _onMapCreated(MapboxMap mapboxMap) async {
    this.mapboxMap = mapboxMap;
    await mapboxMap.scaleBar.updateSettings(ScaleBarSettings(enabled: false));
    await _setMapBounds();
    await _drawMarkers();
  }

  Future<void> _setMapBounds() async {
    final bounds = _calculateMapBounds(widget.chargeStationInfoList!);
    final camera = await mapboxMap.cameraForCoordinateBounds(
      bounds,
      MbxEdgeInsets(top: 40, left: 60, bottom: 40, right: 60),
      0,
      0,
      null,
      null,
    );
    mapboxMap.setCamera(camera);
  }

  Future<void> _drawMarkers() async {
    if (widget.chargeStationInfoList != null) {
      final assetName = widget.isMiraiTypeVehicle
          ? hydrogenMapPinImage
          : mapLocationChargePinIcon;
      final chargingStations = widget.chargeStationInfoList!;
      final manager =
          await mapboxMap.annotations.createPointAnnotationManager();
      final annotationsMap = <String, FuelStations>{};
      for (final chargingStation in chargingStations) {
        if (chargingStation.latitude != null &&
            chargingStation.longitude != null) {
          final image = await _getIconAsset(assetName);
          final point = PointAnnotationOptions(
            geometry: Point(
              coordinates: Position(
                chargingStation.longitude!,
                chargingStation.latitude!,
              ),
            ),
            image: image,
            iconSize: 0.5,
          );
          final annotation = await manager.create(point);
          annotationsMap[annotation.id] = chargingStation;
        }
      }
      final vehicleIcon = await _getIconAsset(mapCarLocationPin);
      final carPoint = PointAnnotationOptions(
        geometry: Point(
          coordinates: widget.vehiclePosition ?? defaultPosition,
        ),
        image: vehicleIcon,
        iconSize: 0.5,
      );
      await manager.create(carPoint);
      manager.addOnPointAnnotationClickListener(
        MarkerClickListener(widget.pinClickListener, annotationsMap),
      );
    }
  }

  Future<Uint8List> _getIconAsset(String assetName) async {
    final bytes = await rootBundle.load(assetName);
    return bytes.buffer.asUint8List();
  }

  CoordinateBounds _calculateMapBounds(List<FuelStations> chargingStations) {
    double? northEastLat, northEastLong, southWestLat, southWestLong;
    final locations = chargingStations
        .where((e) => e.latitude != null && e.longitude != null)
        .map((e) => LatLng(e.latitude!, e.longitude!))
        .toList();
    for (final item in locations) {
      if (northEastLat == null ||
          northEastLong == null ||
          southWestLat == null ||
          southWestLong == null) {
        northEastLat = southWestLat = item.latitude;
        northEastLong = southWestLong = item.longitude;
      }
      if (item.latitude > northEastLat) {
        northEastLat = item.latitude;
      }
      if (item.latitude < southWestLat) {
        southWestLat = item.latitude;
      }
      if (item.longitude > northEastLong) {
        northEastLong = item.longitude;
      }
      if (item.longitude < southWestLong) {
        southWestLong = item.longitude;
      }
    }
    final northEast =
        Point(coordinates: Position(northEastLong!, northEastLat!));
    final southWest =
        Point(coordinates: Position(southWestLong!, southWestLat!));
    return CoordinateBounds(
      southwest: southWest,
      northeast: northEast,
      infiniteBounds: true,
    );
  }
}

class LatLng {
  const LatLng(this.latitude, this.longitude);

  factory LatLng.fromPosition(Position position) =>
      LatLng(position.lat.toDouble(), position.lng.toDouble());

  final double latitude;
  final double longitude;

  Position toPosition() => Position(longitude, latitude);

  Point toPoint() => Point(coordinates: toPosition());
}

class MarkerClickListener extends OnPointAnnotationClickListener {
  final Function? pinClickListener;
  final Map<String, FuelStations> annotationsMap;

  MarkerClickListener(this.pinClickListener, this.annotationsMap);

  @override
  void onPointAnnotationClick(PointAnnotation annotation) {
    if (pinClickListener != null && annotationsMap.containsKey(annotation.id)) {
      pinClickListener?.call(annotationsMap[annotation.id]);
    }
  }
}
