// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:intl/intl.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/climate_setting_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/charge_timer_body_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../log/vehicle_marketing_event.dart';
import '../../electric_vehicle_management/vehicle_search_station_charging_management/ev_vehicle_info_repository.dart';
import '/local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/climate_setting_detail_entity.dart'
    as climateSettingDetail;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleClimateSettingBloc extends BlocBase {
  OneAppClient api = APIClientConfig.oneAppClient;
  vehicleInfo.Payload? vehicleInfoEntity;
  String temperatureUnit = "\u2109";
  bool isCanadaRegion = false;
  late Function(bool) progressHandlerCallback;
  Function(bool, String?)? savedSuccessfullyBottomSheetCallBack;
  int timeOut = 150;
  bool frontSeatHeatingVisibilityStatus = false;
  bool rearSeatHeatingVisibilityStatus = false;
  bool frontLeftSeatHeatingVisibilityStatus = false;
  bool frontRightSeatHeatingVisibilityStatus = false;
  bool rearLeftSeatHeatingVisibilityStatus = false;
  bool rearRightSeatHeatingVisibilityStatus = false;
  bool frontLeftSeatCoolingVisibilityStatus = false;
  bool frontRightSeatCoolingVisibilityStatus = false;
  bool? frontLeftSeatHeatingStatus = false;
  bool? frontRightSeatHeatingStatus = false;
  bool? frontLeftSeatCoolingStatus = false;
  bool? frontRightSeatCoolingStatus = false;

  bool rearLeftSeatHeatingCoolingClicked = false;
  bool rearRightSeatHeatingCoolingClicked = false;
  bool frontLeftSeatHeatingCoolingClicked = false;
  bool frontRightSeatHeatingCoolingClicked = false;

  bool steeringHeatingVisibilityStatus = false;
  bool heatedSeatVisibilityStatus = false;
  bool frontDefoggerVisibilityStatus = false;
  bool rearDefoggerVisibilityStatus = false;
  bool defoggerVisibilityStatus = false;
  bool frontVentilationVisibilityStatus = false;
  bool rearVentilationVisibilityStatus = false;
  bool airCirculationVisibilityStatus = false;
  bool blowerVisibilityStatus = false;
  bool fanSpeedVisibilityStatus = false;
  bool isPre17CY = false;
  int? fanSpeed = 0;
  int? minFanSpeed = 0;
  int? maxFanSpeed = 0;
  double minTemp = MIN_TEMP_F;
  double maxTemp = MAX_TEMP_F;
  bool isNG86 = false;
  bool is21MMGeneration = false;
  String ePlatformCode = "";
  bool isSaveClimateOption = false;
  String? toastMessage;
  Timer? timeOutTimer;

  climateSettingDetail.ClimateSettingDetailPayload? climateSettingPayload;
  ClimatePageDecisionStatus uiState =
      ClimatePageDecisionStatus.INITIAL_LOAD_STATE;
  bool showClimateSettingsScreen = true;
  bool isBackgroundPolling = false;
  double? intervalValue = 1;
  int? temperatureIntervalValue;
  StreamSubscription? _vehicleInfoSubscription;
  VehicleInfo? vehicleInfoPayload;
  DateTime? _previousOccurrenceDate;
  late EVVehicleInfoRepository evRepository;

  Stream<String> get temperatureLevel => _temperatureLevel.stream;
  final _temperatureLevel = BehaviorSubject<String>();

  Stream<double> get sliderTemperatureLevel => _sliderTemperatureLevel.stream;
  final _sliderTemperatureLevel = BehaviorSubject<double>();

  Stream<bool> get enableTemperatureSlider => _enableTemperatureSlider.stream;
  final _enableTemperatureSlider = BehaviorSubject<bool>();

  Stream<int> get blowerSetting => _blowerSetting.stream;
  final _blowerSetting = BehaviorSubject<int>();

  Stream<VehicleSeatState> get frontLeftHeaterState =>
      _frontLeftHeaterState.stream;
  final _frontLeftHeaterState = BehaviorSubject<VehicleSeatState>();

  Stream<VehicleSeatState> get frontRightHeaterState =>
      _frontRightHeaterState.stream;
  final _frontRightHeaterState = BehaviorSubject<VehicleSeatState>();

  Stream<VehicleSeatState> get rearLeftHeaterState =>
      _rearLeftHeaterState.stream;
  final _rearLeftHeaterState = BehaviorSubject<VehicleSeatState>();

  Stream<VehicleSeatState> get rearRightHeaterState =>
      _rearRightHeaterState.stream;
  final _rearRightHeaterState = BehaviorSubject<VehicleSeatState>();

  Stream<bool?> get frontDefoggerState => _frontDefoggerState.stream;
  final _frontDefoggerState = BehaviorSubject<bool?>();

  Stream<bool?> get rearDefoggerState => _rearDefoggerState.stream;
  final _rearDefoggerState = BehaviorSubject<bool?>();

  Stream<bool> get insideAirCirculationState =>
      _insideAirCirculationState.stream;
  final _insideAirCirculationState = BehaviorSubject<bool>();

  Stream<bool?> get steeringHeatingState => _steeringHeatingState.stream;
  final _steeringHeatingState = BehaviorSubject<bool?>();

  Stream<bool> get defoggerVisibility => _defoggerVisibility.stream;
  final _defoggerVisibility = BehaviorSubject<bool>();

  Stream<bool> get airCirculationVisibility => _airCirculationVisibility.stream;
  final _airCirculationVisibility = BehaviorSubject<bool>();

  Stream<bool> get frontDefoggerVisibility => _frontDefoggerVisibility.stream;
  final _frontDefoggerVisibility = BehaviorSubject<bool>();

  Stream<bool> get rearDefoggerVisibility => _rearDefoggerVisibility.stream;
  final _rearDefoggerVisibility = BehaviorSubject<bool>();

  Stream<bool> get disableShimmerLoading => _disableShimmerLoading.stream;
  final _disableShimmerLoading = BehaviorSubject<bool>();

  Stream<bool?> get evRemoteChecked => _evRemoteChecked.stream;
  final _evRemoteChecked = BehaviorSubject<bool?>();

  Stream<bool?> get evPre17Checked => _evPre17Checked.stream;
  final _evPre17Checked = BehaviorSubject<bool?>();

  Stream<bool> get frontRightSeatHeatingVisibility =>
      _frontRightSeatHeatingVisibility.stream;
  final _frontRightSeatHeatingVisibility = BehaviorSubject<bool>();

  Stream<bool> get frontLeftSeatHeatingVisibility =>
      _frontLeftSeatHeatingVisibility.stream;
  final _frontLeftSeatHeatingVisibility = BehaviorSubject<bool>();

  Stream<bool> get rearRightSeatHeatingVisibility =>
      _rearRightSeatHeatingVisibility.stream;
  final _rearRightSeatHeatingVisibility = BehaviorSubject<bool>();

  Stream<bool> get rearLeftSeatHeatingVisibility =>
      _rearLeftSeatHeatingVisibility.stream;
  final _rearLeftSeatHeatingVisibility = BehaviorSubject<bool>();

  Stream<bool> get steeringHeatingVisibility =>
      _steeringHeatingVisibility.stream;
  final _steeringHeatingVisibility = BehaviorSubject<bool>();

  Stream<bool> get heatedSeatVisibility => _heatedSeatVisibility.stream;
  final _heatedSeatVisibility = BehaviorSubject<bool>();

  Stream<bool> get blowerVisibility => _blowerVisibility.stream;
  final _blowerVisibility = BehaviorSubject<bool>();

  Stream<bool> get fanSpeedVisibility => _fanSpeedVisibility.stream;
  final _fanSpeedVisibility = BehaviorSubject<bool>();

  Stream<String> get fanSpeedState => _fanSpeedState.stream;
  final _fanSpeedState = BehaviorSubject<String>();

  Stream<String> get saveButton => _saveButton.stream;
  final _saveButton = BehaviorSubject<String>();

  Stream<int> get timeRemaining => _timeRemaining.stream;
  final _timeRemaining = BehaviorSubject<int>();

  Stream<bool> get timeRemainingVisibility => _timeRemainingVisibility.stream;
  final _timeRemainingVisibility = BehaviorSubject<bool>();

  Stream<bool> get saveButtonVisibility => _saveButtonVisibility.stream;
  final _saveButtonVisibility = BehaviorSubject<bool>();

  Stream<bool> get climateSettingsVisibility =>
      _climateSettingsVisibility.stream;
  final _climateSettingsVisibility = BehaviorSubject<bool>();

  Stream<bool> get loadingLayoutVisibility => _loadingLayoutVisibility.stream;
  final _loadingLayoutVisibility = BehaviorSubject<bool>();

  Stream<RangeValues> get tempRangeValues => _tempRangeValues.stream;
  final _tempRangeValues = BehaviorSubject<RangeValues>();

  Stream<bool> get mainTemperatureToggleVisibility21MMandNG86 =>
      _mainTemperatureToggleVisibility21MMandNG86.stream;
  final _mainTemperatureToggleVisibility21MMandNG86 = BehaviorSubject<bool>();

  Stream<bool> get mainTemperatureToggleValue21MMandNG86 =>
      _mainTemperatureToggleValue21MMandNG86.stream;
  final _mainTemperatureToggleValue21MMandNG86 = BehaviorSubject<bool>();

  Stream<bool> get enableLayout => _enableLayout.stream;
  final _enableLayout = BehaviorSubject<bool>();

  Stream<bool> get showVehicleDataNotAvailable =>
      _showVehicleDataNotAvailable.stream;
  final _showVehicleDataNotAvailable = BehaviorSubject<bool>();

  Stream<bool> get showCoolingSeatOption => _showCoolingSeatOption.stream;
  final _showCoolingSeatOption = BehaviorSubject<bool>();

  Stream<bool> get showSeatOptions => _showSeatOptions.stream;
  final _showSeatOptions = BehaviorSubject<bool>();

  void init(Function(bool) progressHandler,
      Function(bool, String?) savedSuccessfullyBottomSheet) {
    progressHandlerCallback = progressHandler;
    savedSuccessfullyBottomSheetCallBack = savedSuccessfullyBottomSheet;
    _frontLeftHeaterState.sink.add(VehicleSeatState.OFF);
    _frontRightHeaterState.sink.add(VehicleSeatState.OFF);
    _rearLeftHeaterState.sink.add(VehicleSeatState.OFF);
    _rearRightHeaterState.sink.add(VehicleSeatState.OFF);
    _rearDefoggerState.sink.add(false);
    _frontDefoggerState.sink.add(false);
    _blowerSetting.sink.add(1);
    _steeringHeatingState.sink.add(false);
    _sliderTemperatureLevel.sink.add(MIN_TEMP_F);
    _temperatureLevel.sink.add("0");
    bool isChecked = Global.getInstance().evRemoteChecked;
    bool isCheckedPre17CY =
        Global.getInstance().isShowAgainAlertRemoteClimatePre17CYModel;
    _evRemoteChecked.sink.add(isChecked);
    _evPre17Checked.sink.add(isCheckedPre17CY);
    fetchClimateSettings();
  }

  Future<void> fetchClimateSettings() async {
    String? vin = Global.getInstance().vin;
    vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
    isNG86 = isNG86TypeVehicle(vehicleInfoEntity?.generation?.toLowerCase());
    is21MMGeneration = is21MMVehicle(vehicleInfoEntity!.generation);
    isPre17CY = isPRECY17Vehicle(vehicleInfoEntity!.generation!);
    ePlatformCode = vehicleInfoEntity?.electricalPlatformCode ?? "";
    isSaveClimateOption =
        (isNG86 || is21mmNonElectricVehicle(vehicleInfoEntity!));
    if (isSaveClimateOption) {
      _mainTemperatureToggleVisibility21MMandNG86.sink.add(true);
      _saveButtonVisibility.sink.add(true);
      _showVehicleDataNotAvailable.sink.add(false);
      _saveButton.sink.add(OneAppString.of().saveSettings);
      if (is21MMGeneration) {
        fetchClimateSettingsDetail21MM();
      } else if (isNG86) {
        fetchClimateSettingsDetailNG86();
      }
    } else {
      _showVehicleDataNotAvailable.sink.add(true);
      _mainTemperatureToggleVisibility21MMandNG86.sink.add(false);
      _saveButton.sink.add(OneAppString.of().startClimate);
      await subscribeToChargeManagementDetail();
    }
  }

  Future<void> fetchClimateSettingsDetail21MM() async {
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.fetchClimateSettingDetail21MM(
        vin, vehicleInfoEntity!.brand ?? Global.getInstance().appBrand);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      _disableShimmerLoading.sink.add(true);
      process21MMandNG86Payload(payLoad);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_CLIMATE_SETTING_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      _disableShimmerLoading.sink.add(true);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_CLIMATE_SETTING_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  Future<void> fetchClimateSettingsDetailNG86() async {
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.fetchClimateSettingDetailNG86(
        vin, vehicleInfoEntity!.brand ?? Global.getInstance().appBrand);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      _disableShimmerLoading.sink.add(true);
      process21MMandNG86Payload(payLoad);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_CLIMATE_SETTING_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      _disableShimmerLoading.sink.add(true);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_CLIMATE_SETTING_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  void process21MMandNG86Payload(
      climateSettingDetail.ClimateSettingDetailPayload payload) {
    climateSettingPayload = payload;
    _mainTemperatureToggleValue21MMandNG86.sink
        .add(climateSettingPayload!.settingsOn ?? true);
    _enableLayout.sink.add(climateSettingPayload!.settingsOn ?? true);
    intervalValue = climateSettingPayload!.tempInterval;
    if (payload.temperatureUnit?.toLowerCase() == "c") {
      temperatureUnit = "\u2103";
      minTemp = payload.minTemp ?? MIN_TEMP_C;
      maxTemp = payload.maxTemp ?? MAX_TEMP_C;
      _sliderTemperatureLevel.sink.add(payload.temperature ?? MIN_TEMP_C);
      _temperatureLevel.sink.add((payload.temperature ?? MIN_TEMP_C)
              .toStringAsFixed(intervalValue == 0.5 ? 1 : 0) +
          temperatureUnit);
    } else {
      temperatureUnit = "\u2109";
      minTemp = payload.minTemp ?? MIN_TEMP_F;
      maxTemp = payload.maxTemp ?? MAX_TEMP_F;
      _sliderTemperatureLevel.sink.add(payload.temperature ?? MIN_TEMP_F);
      _temperatureLevel.sink.add(
          (payload.temperature ?? MIN_TEMP_F).toStringAsFixed(0) +
              temperatureUnit);
    }
    _enableTemperatureSlider.sink.add(true);
    if (payload.airFlowVolume == null) {
      fanSpeedVisibilityStatus = false;
    } else {
      // Show fan speed
      fanSpeedVisibilityStatus = true;
      fanSpeed = payload.airFlowVolume;
      _fanSpeedVisibility.sink.add(true);
      _fanSpeedState.sink.add(fanSpeed.toString());
      minFanSpeed = payload.minAirFlow;
      maxFanSpeed = payload.maxAirFlow;
    }
    if (payload.acOperations != null) {
      payload.acOperations!.forEach((element) {
        if (element.categoryName == CATEGORY_SEAT_HEAT ||
            element.categoryName == CATEGORY_SEAT_VENT) {
          processVehicleSeatOption(element.categoryName, element);
        } else {
          processACOperation(element.categoryName, element);
        }
      });
    }
  }

  void processVehicleSeatOption(
      String? category, climateSettingDetail.AcOperations acOperations) {
    if (category == CATEGORY_SEAT_HEAT) {
      if (shouldEnableOperation(acOperations)) {
        heatedSeatVisibilityStatus = true;
        acOperations.acParameters?.forEach((element) {
          if (element.name == AC_PARAM_FRONT_DR) {
            if (isNG86 || element.available!) {
              frontLeftSeatHeatingVisibilityStatus = true;
              frontLeftSeatHeatingStatus = element.enabled;
            } else {
              frontLeftSeatHeatingVisibilityStatus = false;
            }
          }
          if (element.name == AC_PARAM_FRONT_PASS) {
            if (isNG86 || element.available!) {
              frontRightSeatHeatingVisibilityStatus = true;
              frontRightSeatHeatingStatus = element.enabled;
            } else {
              frontRightSeatHeatingVisibilityStatus = false;
            }
          }
          if (element.name == AC_PARAM_REAR_DR) {
            if (isNG86 || element.available!) {
              rearLeftSeatHeatingVisibilityStatus = true;
              _rearLeftHeaterState.sink.add(element.enabled!
                  ? VehicleSeatState.HEATED
                  : VehicleSeatState.OFF);
            } else {
              rearLeftSeatHeatingVisibilityStatus = false;
            }
          }
          if (element.name == AC_PARAM_REAR_PASS) {
            if (isNG86 || element.available!) {
              rearRightSeatHeatingVisibilityStatus = true;
              _rearRightHeaterState.sink.add(element.enabled!
                  ? VehicleSeatState.HEATED
                  : VehicleSeatState.OFF);
            } else {
              rearRightSeatHeatingVisibilityStatus = false;
            }
          }
        });
      }
    } else if (category == CATEGORY_SEAT_VENT) {
      if (shouldEnableOperation(acOperations)) {
        heatedSeatVisibilityStatus = true;
        acOperations.acParameters?.forEach((element) {
          if (element.name == AC_PARAM_VENT_FRONT_DR) {
            if (isNG86 || element.available!) {
              frontLeftSeatCoolingVisibilityStatus = true;
              frontLeftSeatCoolingStatus = element.enabled;
            } else {
              frontLeftSeatCoolingVisibilityStatus = false;
            }
          }
          if (element.name == AC_PARAM_VENT_FRONT_PASS) {
            if (isNG86 || element.available!) {
              frontRightSeatCoolingVisibilityStatus = true;
              frontRightSeatCoolingStatus = element.enabled;
            } else {
              frontRightSeatCoolingVisibilityStatus = false;
            }
          }
        });
      }
    }
    if (frontRightSeatHeatingVisibilityStatus ||
        frontLeftSeatHeatingVisibilityStatus ||
        frontRightSeatCoolingVisibilityStatus ||
        frontLeftSeatCoolingVisibilityStatus) {
      frontSeatHeatingVisibilityStatus = true;
    }
    if (rearRightSeatHeatingVisibilityStatus ||
        rearLeftSeatHeatingVisibilityStatus) {
      rearSeatHeatingVisibilityStatus = true;
    }

    if (frontLeftSeatHeatingVisibilityStatus ||
        frontLeftSeatCoolingVisibilityStatus) {
      _frontLeftSeatHeatingVisibility.sink.add(true);
      if (frontLeftSeatHeatingStatus! || frontLeftSeatCoolingStatus!) {
        if (frontLeftSeatHeatingStatus!) {
          _frontLeftHeaterState.sink.add(VehicleSeatState.HEATED);
        } else {
          _frontLeftHeaterState.sink.add(VehicleSeatState.VENTILATED);
        }
      } else {
        _frontLeftHeaterState.sink.add(VehicleSeatState.OFF);
      }
    } else {
      _frontLeftSeatHeatingVisibility.sink.add(false);
    }

    if (frontRightSeatCoolingVisibilityStatus ||
        frontRightSeatHeatingVisibilityStatus) {
      _frontRightSeatHeatingVisibility.sink.add(true);
      if (frontRightSeatHeatingStatus! || frontRightSeatCoolingStatus!) {
        if (frontRightSeatHeatingStatus!) {
          _frontRightHeaterState.sink.add(VehicleSeatState.HEATED);
        } else {
          _frontRightHeaterState.sink.add(VehicleSeatState.VENTILATED);
        }
      } else {
        _frontRightHeaterState.sink.add(VehicleSeatState.OFF);
      }
    } else {
      _frontRightSeatHeatingVisibility.sink.add(false);
    }

    _rearLeftSeatHeatingVisibility.sink
        .add(rearLeftSeatHeatingVisibilityStatus);
    _rearRightSeatHeatingVisibility.sink
        .add(rearRightSeatHeatingVisibilityStatus);
    _heatedSeatVisibility.sink.add(heatedSeatVisibilityStatus);
  }

  void processACOperation(
      String? category, climateSettingDetail.AcOperations acOperations) async {
    switch (category) {
      case CATEGORY_DEFROST:
        {
          if (isNG86 || shouldEnableOperation(acOperations)) {
            defoggerVisibilityStatus = true;
            acOperations.acParameters?.forEach((element) {
              if (element.name == AC_PARAM_FRONT_DEFROST) {
                if (isNG86 || element.available!) {
                  frontDefoggerVisibilityStatus = true;
                  _frontDefoggerState.sink.add(element.enabled);
                } else {
                  frontDefoggerVisibilityStatus = false;
                }
              }
              if (element.name == AC_PARAM_REAR_DEFROST) {
                if (isNG86 || element.available!) {
                  rearDefoggerVisibilityStatus = true;
                  _rearDefoggerState.sink.add(element.enabled);
                } else {
                  rearDefoggerVisibilityStatus = false;
                }
              }
            });
            _defoggerVisibility.sink.add(defoggerVisibilityStatus);
            _rearDefoggerVisibility.sink.add(rearDefoggerVisibilityStatus);
            _frontDefoggerVisibility.sink.add(frontDefoggerVisibilityStatus);
          } else {
            defoggerVisibilityStatus = false;
            _defoggerVisibility.sink.add(defoggerVisibilityStatus);
          }
        }
        break;
      case CATEGORY_STEERING_HEAT_CAT:
        {
          if (shouldEnableOperation(acOperations)) {
            steeringHeatingVisibilityStatus = true;
            acOperations.acParameters?.forEach((element) {
              if (element.name == AC_PARAM_STEERING) {
                if (isNG86 || element.available!) {
                  steeringHeatingVisibilityStatus = true;
                  _steeringHeatingState.sink.add(element.enabled);
                } else {
                  steeringHeatingVisibilityStatus = false;
                }
              }
            });
          } else {
            steeringHeatingVisibilityStatus = false;
          }
          _steeringHeatingVisibility.sink.add(steeringHeatingVisibilityStatus);
        }
        break;
      case CATEGORY_PARAM_AIR_CIRCULATE:
        {
          if (shouldEnableOperation(acOperations)) {
            airCirculationVisibilityStatus = true;
            acOperations.acParameters?.forEach((element) {
              if (element.name == AC_PARAM_AIR_CIRCULATE_ON) {
                if (element.enabled!) {
                  _insideAirCirculationState.sink.add(true);
                } else {
                  _insideAirCirculationState.sink.add(false);
                }
              }
            });
          } else {
            airCirculationVisibilityStatus = false;
          }
          _airCirculationVisibility.sink.add(airCirculationVisibilityStatus);
        }
        break;
      case CATEGORY_AIR_FLOW:
        {
          if (shouldEnableOperation(acOperations)) {
            blowerVisibilityStatus = true;
            acOperations.acParameters?.forEach((element) {
              if (element.name == AC_PARAM_UPPER_BODY &&
                  element.enabled! &&
                  element.available!) {
                _blowerSetting.sink.add(1);
              } else if (element.name == AC_PARAM_FEET &&
                  element.enabled! &&
                  element.available!) {
                _blowerSetting.sink.add(2);
              } else if (element.name == AC_PARAM_UPPER_BODY_FEET &&
                  element.enabled! &&
                  element.available!) {
                _blowerSetting.sink.add(3);
              } else if (element.name == AC_PARAM_FRONT_DEFROST_FEET &&
                  element.enabled! &&
                  element.available!) {
                _blowerSetting.sink.add(4);
              }
            });
          } else {
            blowerVisibilityStatus = false;
          }
          _blowerVisibility.sink.add(blowerVisibilityStatus);
        }
        break;
    }
  }

  bool shouldEnableOperation(AcOperations acOperations) {
    bool? paramAvailable = false;
    paramAvailable =
        acOperations.acParameters?.any((element) => element.available == true);
    return acOperations.available == true && paramAvailable!;
  }

  void changeClimateSettings(
      String category, String setting, bool switchOn) async {
    climateSettingPayload!.acOperations!.forEach((element) {
      switch (element.categoryName) {
        case CATEGORY_DEFROST:
        case CATEGORY_SEAT_HEAT:
        case CATEGORY_SEAT_VENT:
        case CATEGORY_STEERING_HEAT_CAT:
          {
            if (element.categoryName == category) {
              element.acParameters?.forEach((element) {
                if (element.name == setting) {
                  element.enabled = switchOn;
                }
              });
            }
          }
          break;
        case CATEGORY_PARAM_AIR_CIRCULATE:
        case CATEGORY_AIR_FLOW:
          {
            if (element.categoryName == category) {
              element.acParameters?.forEach((element) {
                if (element.name == setting) {
                  element.enabled = switchOn;
                } else {
                  element.enabled = !switchOn;
                }
              });
            }
          }
          break;
      }
    });
  }

  Future<void> subscribeToChargeManagementDetail() async {
    String? vin = Global.getInstance().vin;
    evRepository = await VehicleRepo().getEVVehicleInfoRepository(vin);
    _vehicleInfoSubscription = evRepository.evVehicleInfo.listen((vehicleInfo) {
      vehicleInfoPayload = vehicleInfo;
      if (vehicleInfoPayload != null) {
        updateUIWithVehicleInfo();
      } else {
        _saveButtonVisibility.sink.add(false);
        _disableShimmerLoading.sink.add(true);
        _showVehicleDataNotAvailable.sink.add(true);
      }
    });
    vehicleInfoPayload = evRepository.lastReceived;
    if (vehicleInfoPayload != null) {
      updateUIWithVehicleInfo();
    }
  }

  Future<void> updateUIWithVehicleInfo() async {
    if (vehicleInfoPayload != null) {
      _showVehicleDataNotAvailable.sink.add(false);
      {
        _saveButtonVisibility.sink.add(true);
        RemoteHvacInfo? remoteHvacInfo = vehicleInfoPayload!.remoteHvacInfo;
        if (remoteHvacInfo != null) {
          _disableShimmerLoading.sink.add(true);
          showClimateSettingsScreen = determineInitialScreen(remoteHvacInfo);
          if (uiState == ClimatePageDecisionStatus.SETTINGS_ON_STATE) {
            if (!showClimateSettingsScreen) {
              _loadingLayoutVisibility.sink.add(false);
              uiState = ClimatePageDecisionStatus.INITIAL_LOAD_STATE;
              if (timeOutTimer != null && timeOutTimer!.isActive) {
                timeOutTimer!.cancel();
              }
            }
          } else if (uiState == ClimatePageDecisionStatus.SETTINGS_OFF_STATE) {
            if (showClimateSettingsScreen) {
              _loadingLayoutVisibility.sink.add(false);
              uiState = ClimatePageDecisionStatus.INITIAL_LOAD_STATE;
              if (timeOutTimer != null && timeOutTimer!.isActive) {
                timeOutTimer!.cancel();
              }
            }
          }
          bool isCY17 = isCY17OnlyVehicle(vehicleInfoEntity!.generation!);
          // Celcius for canada
          if (isVehicleCanadaRegion(vehicleInfoEntity!.region!)) {
            isCanadaRegion = true;
            temperatureUnit = "\u2103";
            minTemp = MIN_TEMP_C;
            maxTemp = MAX_TEMP_C;
            if (isCY17) {
              temperatureIntervalValue = 2;
            } else {
              // 1 - CY17PLUS excluding RAV4 and MIRAI
              if (!isRav4orMiraiClimateVehicle(vehicleInfoEntity!)) {
                temperatureIntervalValue = 1;
              }
            }
          } else {
            if (isCY17) {
              temperatureIntervalValue = 2;
            } else {
              // 1 - CY17PLUS excluding RAV4 and MIRAI
              if (!isRav4orMiraiClimateVehicle(vehicleInfoEntity!)) {
                temperatureIntervalValue = 1;
              }
            }
          }
          if (remoteHvacInfo.settingTemperature != null &&
              remoteHvacInfo.settingTemperature != -1 &&
              !isPre17CY) {
            _enableTemperatureSlider.sink.add(true);
            double temperatureLevel = remoteHvacInfo.settingTemperature!;
            temperatureLevel = getVehicleTemperature(
                is21mmNonElectricVehicle(vehicleInfoEntity!),
                temperatureLevel,
                isCanadaRegion);
            _sliderTemperatureLevel.sink.add(temperatureLevel);
            _temperatureLevel.sink
                .add(temperatureLevel.toStringAsFixed(0) + temperatureUnit);
          } else {
            _enableTemperatureSlider.sink.add(false);
          }

          int blowerStatus = remoteHvacInfo.blowerStatus ?? 1;
          _blowerSetting.sink.add(blowerStatus);
          _climateSettingsVisibility.sink.add(showClimateSettingsScreen);
          if (showClimateSettingsScreen) {
            _saveButton.sink.add(OneAppString.of().startClimate);
            formUIForSettingsPage(remoteHvacInfo);
            if (uiState == ClimatePageDecisionStatus.INITIAL_LOAD_STATE) {
              _vehicleInfoSubscription?.pause();
            }
          } else {
            _saveButton.sink.add(OneAppString.of().stopClimate);
            formUIForClimateOnPage(remoteHvacInfo);
          }
        } else {
          _disableShimmerLoading.sink.add(true);
        }
      }
    } else {
      _saveButtonVisibility.sink.add(false);
      _disableShimmerLoading.sink.add(true);
      _showVehicleDataNotAvailable.sink.add(true);
    }
  }

  void formUIForSettingsPage(RemoteHvacInfo remoteHvacInfo) {
    int? frontDefogger = remoteHvacInfo.frontDefoggerStatus;
    if (frontDefogger != null && frontDefogger == 1) {
      _frontDefoggerState.sink.add(true);
    } else {
      _frontDefoggerState.sink.add(false);
    }
    int? rearDefogger = remoteHvacInfo.rearDefoggerStatus;
    if (rearDefogger != null && rearDefogger == 1) {
      _rearDefoggerState.sink.add(true);
    } else {
      _rearDefoggerState.sink.add(false);
    }

    int? steeringHeatingState = remoteHvacInfo.steeringHeaterStatus;
    if (steeringHeatingState == null || (steeringHeatingState == 0)) {
      _steeringHeatingState.sink.add(false);
      steeringHeatingVisibilityStatus = false;
    } else {
      _steeringHeatingState.sink.add(true);
      steeringHeatingVisibilityStatus = true;
    }

    int? frontSeatHeatingState = remoteHvacInfo.frontSeatHeaterStatus;
    int? rearSeatHeatingState = remoteHvacInfo.rearSeatHeaterStatus;
    if (frontSeatHeatingState == null || (frontSeatHeatingState == 0)) {
      frontSeatHeatingVisibilityStatus = false;
      frontLeftSeatHeatingVisibilityStatus = false;
      frontRightSeatHeatingVisibilityStatus = false;
    } else {
      frontSeatHeatingVisibilityStatus = true;
      frontLeftSeatHeatingVisibilityStatus = true;
      frontRightSeatHeatingVisibilityStatus = true;
    }

    if (rearSeatHeatingState == null || (rearSeatHeatingState == 0)) {
      rearSeatHeatingVisibilityStatus = false;
      rearLeftSeatHeatingVisibilityStatus = false;
      rearRightSeatHeatingVisibilityStatus = false;
    } else {
      rearSeatHeatingVisibilityStatus = true;
      rearLeftSeatHeatingVisibilityStatus = true;
      rearRightSeatHeatingVisibilityStatus = true;
    }

    if (frontSeatHeatingVisibilityStatus || rearSeatHeatingVisibilityStatus) {
      heatedSeatVisibilityStatus = true;
    } else {
      heatedSeatVisibilityStatus = false;
    }

    frontDefoggerVisibilityStatus = true;
    rearDefoggerVisibilityStatus = true;
    if ((frontDefoggerVisibilityStatus || rearDefoggerVisibilityStatus) &&
        !isPre17CY) {
      defoggerVisibilityStatus = true;
    } else {
      defoggerVisibilityStatus = false;
    }

    _steeringHeatingVisibility.sink.add(steeringHeatingVisibilityStatus);
    _frontLeftSeatHeatingVisibility.sink.add(frontSeatHeatingVisibilityStatus);
    _frontRightSeatHeatingVisibility.sink.add(frontSeatHeatingVisibilityStatus);
    _rearLeftSeatHeatingVisibility.sink.add(rearSeatHeatingVisibilityStatus);
    _rearRightSeatHeatingVisibility.sink.add(rearSeatHeatingVisibilityStatus);
    _heatedSeatVisibility.sink.add(heatedSeatVisibilityStatus);
    _defoggerVisibility.sink.add(defoggerVisibilityStatus);
    _rearDefoggerVisibility.sink.add(rearDefoggerVisibilityStatus);
    _frontDefoggerVisibility.sink.add(frontDefoggerVisibilityStatus);
    _frontLeftHeaterState.sink.add(VehicleSeatState.OFF);
    _frontRightHeaterState.sink.add(VehicleSeatState.OFF);
    _rearLeftHeaterState.sink.add(VehicleSeatState.OFF);
    _rearRightHeaterState.sink.add(VehicleSeatState.OFF);
    _timeRemainingVisibility.sink.add(false);
  }

  void formUIForClimateOnPage(RemoteHvacInfo remoteHvacInfo) {
    int? frontDefogger = remoteHvacInfo.frontDefoggerStatus;
    if (frontDefogger != null && frontDefogger == 1) {
      _frontDefoggerState.sink.add(true);
      frontDefoggerVisibilityStatus = true;
    } else {
      _frontDefoggerState.sink.add(false);
      frontDefoggerVisibilityStatus = false;
    }

    int? rearDefogger = remoteHvacInfo.rearDefoggerStatus;
    if (rearDefogger != null && rearDefogger == 1) {
      _rearDefoggerState.sink.add(true);
      rearDefoggerVisibilityStatus = true;
    } else {
      _rearDefoggerState.sink.add(false);
      rearDefoggerVisibilityStatus = false;
    }

    int? steeringHeatingState = remoteHvacInfo.steeringHeaterStatus;
    if (steeringHeatingState == null || (steeringHeatingState == 0)) {
      steeringHeatingVisibilityStatus = false;
    } else {
      steeringHeatingVisibilityStatus = true;
    }

    int? frontSeatHeatingState = remoteHvacInfo.frontSeatHeaterStatus;
    int? rearSeatHeatingState = remoteHvacInfo.rearSeatHeaterStatus;
    if (frontSeatHeatingState == null || (frontSeatHeatingState == 0)) {
      frontSeatHeatingVisibilityStatus = false;
      frontLeftSeatHeatingVisibilityStatus = false;
      frontRightSeatHeatingVisibilityStatus = false;
    } else {
      frontSeatHeatingVisibilityStatus = true;
      frontLeftSeatHeatingVisibilityStatus = true;
      frontRightSeatHeatingVisibilityStatus = true;
    }

    if (rearSeatHeatingState == null || (rearSeatHeatingState == 0)) {
      rearSeatHeatingVisibilityStatus = false;
      rearLeftSeatHeatingVisibilityStatus = false;
      rearRightSeatHeatingVisibilityStatus = false;
    } else {
      rearSeatHeatingVisibilityStatus = true;
      rearLeftSeatHeatingVisibilityStatus = true;
      rearRightSeatHeatingVisibilityStatus = true;
    }

    if (frontSeatHeatingVisibilityStatus || rearSeatHeatingVisibilityStatus) {
      heatedSeatVisibilityStatus = true;
    } else {
      heatedSeatVisibilityStatus = false;
    }

    if ((frontDefoggerVisibilityStatus || rearDefoggerVisibilityStatus) &&
        !isPre17CY) {
      defoggerVisibilityStatus = true;
    } else {
      defoggerVisibilityStatus = false;
    }
    _steeringHeatingVisibility.sink.add(steeringHeatingVisibilityStatus);
    _steeringHeatingState.sink.add(steeringHeatingVisibilityStatus);
    _frontLeftSeatHeatingVisibility.sink.add(frontSeatHeatingVisibilityStatus);
    _frontRightSeatHeatingVisibility.sink.add(frontSeatHeatingVisibilityStatus);
    _rearLeftSeatHeatingVisibility.sink.add(rearSeatHeatingVisibilityStatus);
    _rearRightSeatHeatingVisibility.sink.add(rearSeatHeatingVisibilityStatus);
    _heatedSeatVisibility.sink.add(heatedSeatVisibilityStatus);
    _defoggerVisibility.sink.add(defoggerVisibilityStatus);
    _rearDefoggerVisibility.sink.add(rearDefoggerVisibilityStatus);
    _frontDefoggerVisibility.sink.add(frontDefoggerVisibilityStatus);
    _frontLeftHeaterState.sink.add(VehicleSeatState.OFF);
    _frontRightHeaterState.sink.add(VehicleSeatState.OFF);
    _rearLeftHeaterState.sink.add(VehicleSeatState.OFF);
    _rearRightHeaterState.sink.add(VehicleSeatState.OFF);
    int timeRemaining = getTimeDifference(remoteHvacInfo.latestAcStartTime);
    _timeRemainingVisibility.sink.add(true);
    _timeRemaining.sink.add(timeRemaining);
    int temperatureLevel = remoteHvacInfo.temperaturelevel ?? 0;
    double currentTemperature = getCurrentTemperature(
        temperatureLevel.toDouble(),
        remoteHvacInfo.blowerStatus,
        remoteHvacInfo.latestAcStartTime);
    if (currentTemperature >= -10 && currentTemperature <= 100) {
      if (remoteHvacInfo.settingTemperature != null) {
        if (remoteHvacInfo.settingTemperature! < currentTemperature) {
          _tempRangeValues.sink.add(RangeValues(
              remoteHvacInfo.settingTemperature!, currentTemperature));
        } else {
          _tempRangeValues.sink.add(RangeValues(
              currentTemperature, remoteHvacInfo.settingTemperature!));
        }
      }
    } else {
      _tempRangeValues.sink.add(RangeValues(remoteHvacInfo.settingTemperature!,
          remoteHvacInfo.settingTemperature!));
    }
  }

  Future<void> saveSettingsChangesFor21MMandNG86() async {
    progressHandlerCallback(true);
    String? vin = Global.getInstance().vin;
    final commonResponse = isNG86
        ? await api.updateClimateSettingDetailNG86(
            vin!,
            vehicleInfoEntity!.generation!,
            climateSettingPayload!,
            vehicleInfoEntity!.brand ?? Global.getInstance().appBrand)
        : await api.updateClimateSettingDetail21MM(
            vin!, vehicleInfoEntity!.brand!, climateSettingPayload!);

    progressHandlerCallback(false);
    if (commonResponse.error != null) {
      errorCallBack(savedSuccessfullyBottomSheetCallBack);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_CLIMATE_SETTING_FAILURE,
          category: LogCategory.FL_VEHI);
    } else {
      successCallBack(savedSuccessfullyBottomSheetCallBack);
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_CLIMATE_SETTING_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
  }

  void pressOkButtonPre17ModalAlert() {
    Global.getInstance().isShowAgainAlertRemoteClimatePre17CYModel =
        !_evPre17Checked.value!;
  }

  void turnOnClimate() {
    Global.getInstance().evRemoteChecked = _evRemoteChecked.value!;

    if (isPre17CY) {
      ChargeTimerBodyHelper request = ChargeTimerBodyHelper(
          command: AC_ON, remoteHvac: null, reservationCharge: null);
      saveClimateSettingChanges(
          request, ClimatePageDecisionStatus.SETTINGS_ON_STATE);
    } else {
      String tempSetting = SETTING_TYPE_CUSTOM;
      if (_sliderTemperatureLevel.value == MIN_TEMP_F ||
          _sliderTemperatureLevel.value == MIN_TEMP_C) {
        tempSetting = SETTING_TYPE_COLD;
      }
      if (_sliderTemperatureLevel.value == MAX_TEMP_F ||
          _sliderTemperatureLevel.value == MAX_TEMP_C) {
        tempSetting = SETTING_TYPE_HOT;
      }
      if (_sliderTemperatureLevel.value == MID_TEMP_F ||
          _sliderTemperatureLevel.value == MID_TEMP_C) {
        tempSetting = SETTING_TYPE_NO_CHANGE;
      }

      Option option = Option(
          frontDefogger: boolToString(_frontDefoggerState.value!),
          rearDefogger: boolToString(_rearDefoggerState.value!),
          mirrorHeater: "off",
          frontDriverSeatHeater: stateToString(_frontLeftHeaterState.value),
          frontPassengerSeatHeater: stateToString(_frontRightHeaterState.value),
          rearDriverSeatHeater: stateToString(_rearLeftHeaterState.value),
          rearPassengerSeatHeater: stateToString(_rearRightHeaterState.value),
          steeringHeater: boolToString(_steeringHeatingState.value!));

      String unit = "f";
      if (isCanadaRegion) {
        unit = "c";
      }

      RemoteHvac remoteHvac = RemoteHvac(
          settingType: tempSetting,
          temperatureUnit: unit,
          temperature: _sliderTemperatureLevel.value.toInt(),
          option: option);
      ChargeTimerBodyHelper request = ChargeTimerBodyHelper(
          command: AC_ON, remoteHvac: remoteHvac, reservationCharge: null);
      saveClimateSettingChanges(
          request, ClimatePageDecisionStatus.SETTINGS_ON_STATE);
    }
  }

  void turnOffClimate() {
    ChargeTimerBodyHelper request = ChargeTimerBodyHelper(
        command: AC_OFF, remoteHvac: null, reservationCharge: null);
    saveClimateSettingChanges(
        request, ClimatePageDecisionStatus.SETTINGS_OFF_STATE);
  }

  Future<void> saveClimateSettingChanges(
      ChargeTimerBodyHelper chargeTimerBodyHelper,
      ClimatePageDecisionStatus nextStatus) async {
    _loadingLayoutVisibility.sink.add(true);
    int startTime = DateTime.now().millisecondsSinceEpoch;
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.postVehicleChargeTimerRequest(
        vehicleInfoEntity!.brand!,
        vin,
        vehicleInfoEntity!.generation!,
        Global.getInstance().fcmDeviceId!,
        chargeTimerBodyHelper);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      if (payLoad.returnCode == "ONE-RES-10000" && isPre17CY) {
        bool isAcOff = chargeTimerBodyHelper.command == "ac-off";
        fetchVehicleClimateStatusPreCY17(startTime, nextStatus, isAcOff);
      } else {
        fetchVehicleClimateStatus(startTime, payLoad.appRequestNo, nextStatus);
      }
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_CLIMATE_SETTING_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      _loadingLayoutVisibility.sink.add(false);
      errorCallBack(savedSuccessfullyBottomSheetCallBack);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_CLIMATE_SETTING_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  // Check climate status after changing the settings
  Future<void> fetchVehicleClimateStatusPreCY17(
      int startTime, ClimatePageDecisionStatus nextStatus, bool isAcOff) async {
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.fetchChargeManagementTimerStatus(
        vehicleInfoEntity!.generation!,
        vin,
        vehicleInfoEntity!.brand ?? Global.getInstance().appBrand);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_PRE_CY17_STATUS_SUCCESS,
          category: LogCategory.FL_VEHI);
      if (payLoad.returnCode != null &&
          (payLoad.returnCode == ELECTRIC_STATUS_RETURN_CODE_SUCCESS ||
              payLoad.returnCode!.isEmpty) &&
          payLoad.vehicleStatusResult != null &&
          payLoad.vehicleStatusResult!.occurrenceDate != null) {
        DateTime occurrenceDate = DateFormat(CTP_DATE_FORMAT)
            .parse(payLoad.vehicleStatusResult!.occurrenceDate!, true)
            .toLocal();

        if (_previousOccurrenceDate == null) {
          _previousOccurrenceDate = occurrenceDate;
        }
        if (!occurrenceDate.isAfter(_previousOccurrenceDate!)) {
          int commandTimeout = isAcOff
              ? (timeOut + 200)
              : timeOut; //Extra time added for timeout due to long response times from AC-OFF from Pre17 vehicles
          int now = DateTime.now().millisecondsSinceEpoch;
          double timeElapsed = (now - startTime) / 1000;
          if (timeElapsed >= commandTimeout) {
            _loadingLayoutVisibility.sink.add(false);
            errorCallBack(savedSuccessfullyBottomSheetCallBack);
          } else {
            Future.delayed(const Duration(milliseconds: 3000), () {
              fetchVehicleClimateStatusPreCY17(startTime, nextStatus, isAcOff);
            });
          }
        } else {
          _previousOccurrenceDate = null; //Resetting for next operation
          uiState = nextStatus;
          _vehicleInfoSubscription?.resume();
          VehicleRepo()
              .getEVVehicleInfoRepository(vin)
              .then((repository) async {
            await repository.triggerVehicleToUpdateTSC();
          });
          timeOutTimer = Timer(Duration(seconds: 150), () {
            _loadingLayoutVisibility.sink.add(false);
            errorCallBack(savedSuccessfullyBottomSheetCallBack);
          });
        }
      } else {
        _loadingLayoutVisibility.sink.add(false);
        errorCallBack(savedSuccessfullyBottomSheetCallBack);
      }
    } else {
      _loadingLayoutVisibility.sink.add(false);
      errorCallBack(savedSuccessfullyBottomSheetCallBack);
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        fetchVehicleClimateStatusPreCY17(startTime, nextStatus, isAcOff);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_PRE_CY17_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      } else {
        _loadingLayoutVisibility.sink.add(false);
        errorCallBack(savedSuccessfullyBottomSheetCallBack);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_PRE_CY17_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  // Check climate status after changing the settings
  Future<void> fetchVehicleClimateStatus(int startTime, String? requestNo,
      ClimatePageDecisionStatus nextStatus) async {
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.fetchChargeManagementTimerStatus(
        vehicleInfoEntity!.generation!,
        vin,
        vehicleInfoEntity!.brand ?? Global.getInstance().appBrand,
        requestNo: requestNo);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_STATUS_SUCCESS,
          category: LogCategory.FL_VEHI);
      if (payLoad.returnCode != null &&
          (payLoad.returnCode == ELECTRIC_STATUS_RETURN_CODE_SUCCESS ||
              payLoad.returnCode!.isEmpty)) {
        int? status = payLoad.remoteControlResult?.status;
        if (status != null) {
          if (status == 9) {
            int now = DateTime.now().millisecondsSinceEpoch;
            double timeElapsed = (now - startTime) / 1000;
            if (timeElapsed >= timeOut) {
              _loadingLayoutVisibility.sink.add(false);
              errorCallBack(savedSuccessfullyBottomSheetCallBack);
            } else {
              fetchVehicleClimateStatus(startTime, requestNo, nextStatus);
            }
          } else {
            uiState = nextStatus;
            if (status == 0) {
              _vehicleInfoSubscription?.resume();
              VehicleRepo()
                  .getEVVehicleInfoRepository(vin)
                  .then((repository) async {
                await repository.triggerVehicleToUpdateTSC();
              });
              timeOutTimer = Timer(Duration(minutes: 3), () {
                _loadingLayoutVisibility.sink.add(false);
                errorCallBack(savedSuccessfullyBottomSheetCallBack);
              });
            } else {
              if (status == 1) {
                savedSuccessfullyBottomSheetCallBack!(
                    false, OneAppString.of().error_system_turn_on_error);
              }
              if (status == 2) {
                savedSuccessfullyBottomSheetCallBack!(
                    false, OneAppString.of().error_system_error);
              }
              if (status == 3) {
                savedSuccessfullyBottomSheetCallBack!(
                    false, OneAppString.of().error_request_cannot_be_sent);
              }
            }
          }
        } else {
          _loadingLayoutVisibility.sink.add(false);
        }
      } else {
        _loadingLayoutVisibility.sink.add(false);
        errorCallBack(savedSuccessfullyBottomSheetCallBack);
      }
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        fetchVehicleClimateStatus(startTime, requestNo, nextStatus);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      } else {
        _loadingLayoutVisibility.sink.add(false);
        errorCallBack(savedSuccessfullyBottomSheetCallBack);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  bool determineInitialScreen(RemoteHvacInfo hvac) {
    if (hvac.remoteHvacMode == 1) {
      if (hvac.remoteHvacProhibitionSignal == 0) {
        return false;
      }
      return true;
    } else {
      return true;
    }
  }

  int getTimeDifference(String? date) {
    if (date != null) {
      Duration difference = DateTime.now().difference(DateTime.parse(date));
      int min = difference.inMinutes;
      return (20 - min).abs();
    } else {
      return 20;
    }
  }

  double getCurrentTemperature(
      double temp, int? blowerStatus, String? latestAcStartTime) {
    var plusRange = 160.0;
    var minusRange = 15.0;
    double ratio = 8.0 / 15.0;
    int timeElapsed = 20 - getTimeDifference(latestAcStartTime);
    var temp2 = 0.0;
    temp2 = temp;
    if (isCY17Vehicle(vehicleInfoEntity!.generation)) {
      if (vehicleInfoEntity!.generation!.toLowerCase() == "us") {
        return celciusToFarenheit(temp2);
      }
      return temp2;
    }
    if (blowerStatus == 1) {
      minusRange = 5.0;
      plusRange = blowerOneTemperature(timeElapsed);
    } else if (blowerStatus == 3) {
      minusRange = blowerThreeTemperature(timeElapsed);
    }

    double first = (temp - minusRange) * ratio;
    double second = (temp + plusRange) * ratio;
    return first + second / 2.0;
  }

  double blowerOneTemperature(int timeElapsed) {
    switch (timeElapsed) {
      case 0:
        return 165.0;

      case 1:
        return 160.0;

      case 2:
        return 155.0;

      case 3:
        return 152.5;

      case 4:
      case 5:
        return 150.0;

      case 6:
      case 7:
        return 149.0;

      case 8:
      case 9:
      case 10:
        return 148.0;

      default:
        return 174;
    }
  }

  double blowerThreeTemperature(int timeElapsed) {
    switch (timeElapsed) {
      case 0:
      case 1:
        return 25.0;

      case 2:
      case 3:
        return 20.0;

      case 4:
      case 5:
        return 15.0;

      case 6:
      case 7:
        return 10.0;

      case 8:
      case 9:
      case 10:
        return 5.0;

      default:
        return 0;
    }
  }

  void sliderTemperatureChange(double temperatureValue) {
    _sliderTemperatureLevel.sink.add(temperatureValue);
    if (temperatureIntervalValue != null &&
        temperatureIntervalValue == 2 &&
        temperatureValue != MIN_TEMP_F &&
        temperatureValue != MAX_TEMP_F &&
        temperatureValue != MIN_TEMP_C &&
        temperatureValue != MAX_TEMP_C) {
      _temperatureLevel.sink.add(OneAppString.of().lastInCarSetting);
    } else {
      _temperatureLevel.sink
          .add(temperatureValue.toStringAsFixed(0) + temperatureUnit);
    }

    if (isSaveClimateOption) {
      if (intervalValue == 0.5 &&
          climateSettingPayload?.temperatureUnit?.toLowerCase() == "c") {
        climateSettingPayload!.temperature =
            getDecimalRounded(temperatureValue);
        _temperatureLevel.sink.add(
            climateSettingPayload!.temperature.toString() + temperatureUnit);
      } else {
        climateSettingPayload!.temperature =
            double.parse(temperatureValue.toStringAsFixed(0));
        _temperatureLevel.sink
            .add(temperatureValue.toStringAsFixed(0) + temperatureUnit);
      }
    }
  }

  double getDecimalRounded(double temperature) {
    if (temperature == minTemp || temperature == maxTemp) {
      double finalValue = double.parse(temperature.toStringAsFixed(0));
      return finalValue;
    } else {
      double initValue = double.parse(temperature.toStringAsFixed(1));
      double finalValue = double.parse(temperature.toStringAsFixed(0));
      int afterDecimal = int.tryParse(initValue.toString().split('.')[1])!;
      int? beforeDecimal = int.tryParse(initValue.toString().split('.')[0]);
      if (afterDecimal > 5) {
        beforeDecimal = beforeDecimal! + 1;
        finalValue = double.parse(beforeDecimal.toStringAsFixed(0));
      } else {
        finalValue = beforeDecimal! + 0.5;
      }
      return finalValue;
    }
  }

  void frontDefoggerChange(bool value) {
    logFirebaseEvent(value, VehicleMarketingEvent.FRONT_DEFROST);
    _frontDefoggerState.sink.add(value);
    if (isSaveClimateOption) {
      changeClimateSettings(CATEGORY_DEFROST, AC_PARAM_FRONT_DEFROST, value);
    }
  }

  void rearDefoggerChange(bool value) {
    logFirebaseEvent(value, VehicleMarketingEvent.BACK_DEFROST);
    _rearDefoggerState.sink.add(value);
    if (isSaveClimateOption) {
      changeClimateSettings(CATEGORY_DEFROST, AC_PARAM_REAR_DEFROST, value);
    }
  }

  void insideAirCirculationChange(bool value) {
    if (value) {
      logFirebaseEvent(true, VehicleMarketingEvent.REMOTE_AIR_CIRC_INSIDE);
    } else {
      logFirebaseEvent(true, VehicleMarketingEvent.REMOTE_AIR_CIRC_OUTSIDE);
    }
    _insideAirCirculationState.sink.add(value);
    if (isSaveClimateOption) {
      changeClimateSettings(
          CATEGORY_PARAM_AIR_CIRCULATE, AC_PARAM_AIR_CIRCULATE_ON, value);
    }
  }

  void steeringWheelTemperatureChange(bool value) {
    logFirebaseEvent(value, VehicleMarketingEvent.STEERING_WHEEL);
    _steeringHeatingState.sink.add(value);
    if (isSaveClimateOption) {
      changeClimateSettings(
          CATEGORY_STEERING_HEAT_CAT, AC_PARAM_STEERING, value);
    }
  }

  void heatedSeatRearLeftClick() {
    rearLeftSeatHeatingCoolingClicked = true;
    _showSeatOptions.sink.add(true);
  }

  void heatedSeatRearLeftChange(VehicleSeatState status) {
    logFirebaseEvent(!stateToBool(_rearLeftHeaterState.value),
        VehicleMarketingEvent.REAR_SEAT);
    _rearLeftHeaterState.sink.add(status);
    if (isSaveClimateOption) {
      if (status == VehicleSeatState.VENTILATED) {
        changeClimateSettings(CATEGORY_SEAT_VENT, AC_PARAM_VENT_REAR_DR, true);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_REAR_DR, false);
      } else if (status == VehicleSeatState.HEATED) {
        changeClimateSettings(CATEGORY_SEAT_VENT, AC_PARAM_VENT_REAR_DR, false);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_REAR_DR, true);
      } else {
        changeClimateSettings(CATEGORY_SEAT_VENT, AC_PARAM_VENT_REAR_DR, false);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_REAR_DR, false);
      }
    }
  }

  void heatedSeatRearRightClick() {
    rearRightSeatHeatingCoolingClicked = true;
    _showSeatOptions.sink.add(true);
  }

  void heatedSeatRearRightChange(VehicleSeatState status) {
    logFirebaseEvent(!stateToBool(_rearRightHeaterState.value),
        VehicleMarketingEvent.REAR_PASSENGER);
    _rearRightHeaterState.sink.add(status);
    if (isSaveClimateOption) {
      if (status == VehicleSeatState.VENTILATED) {
        changeClimateSettings(
            CATEGORY_SEAT_VENT, AC_PARAM_VENT_REAR_PASS, true);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_REAR_PASS, false);
      } else if (status == VehicleSeatState.HEATED) {
        changeClimateSettings(
            CATEGORY_SEAT_VENT, AC_PARAM_VENT_REAR_PASS, false);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_REAR_PASS, true);
      } else {
        changeClimateSettings(
            CATEGORY_SEAT_VENT, AC_PARAM_VENT_REAR_PASS, false);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_REAR_PASS, false);
      }
    }
  }

  void heatedSeatFrontLeftClick() {
    frontLeftSeatHeatingCoolingClicked = true;
    _showSeatOptions.sink.add(true);
  }

  void heatedSeatFrontLeftChange(VehicleSeatState status) {
    logFirebaseEvent(!stateToBool(_frontLeftHeaterState.value),
        VehicleMarketingEvent.FRONT_SEAT);
    _frontLeftHeaterState.sink.add(status);
    if (isSaveClimateOption) {
      if (status == VehicleSeatState.VENTILATED) {
        changeClimateSettings(CATEGORY_SEAT_VENT, AC_PARAM_VENT_FRONT_DR, true);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_FRONT_DR, false);
      } else if (status == VehicleSeatState.HEATED) {
        changeClimateSettings(
            CATEGORY_SEAT_VENT, AC_PARAM_VENT_FRONT_DR, false);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_FRONT_DR, true);
      } else {
        changeClimateSettings(
            CATEGORY_SEAT_VENT, AC_PARAM_VENT_FRONT_DR, false);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_FRONT_DR, false);
      }
    }
  }

  void heatedSeatFrontRightClick() {
    frontRightSeatHeatingCoolingClicked = true;
    _showSeatOptions.sink.add(true);
  }

  void heatedSeatFrontRightChange(VehicleSeatState status) {
    logFirebaseEvent(!stateToBool(_frontRightHeaterState.value),
        VehicleMarketingEvent.PASSENGER_SEAT);
    _frontRightHeaterState.sink.add(status);
    if (isSaveClimateOption) {
      if (status == VehicleSeatState.VENTILATED) {
        changeClimateSettings(
            CATEGORY_SEAT_VENT, AC_PARAM_VENT_FRONT_PASS, true);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_FRONT_PASS, false);
      } else if (status == VehicleSeatState.HEATED) {
        changeClimateSettings(
            CATEGORY_SEAT_VENT, AC_PARAM_VENT_FRONT_PASS, false);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_FRONT_PASS, true);
      } else {
        changeClimateSettings(
            CATEGORY_SEAT_VENT, AC_PARAM_VENT_FRONT_PASS, false);
        changeClimateSettings(CATEGORY_SEAT_HEAT, AC_PARAM_FRONT_PASS, false);
      }
    }
  }

  void heatedCoolingSeatStatusOffClicked() {
    if (rearLeftSeatHeatingCoolingClicked) {
      heatedSeatRearLeftChange(VehicleSeatState.OFF);
    } else if (rearRightSeatHeatingCoolingClicked) {
      heatedSeatRearRightChange(VehicleSeatState.OFF);
    } else if (frontLeftSeatHeatingCoolingClicked) {
      heatedSeatFrontLeftChange(VehicleSeatState.OFF);
    } else if (frontRightSeatHeatingCoolingClicked) {
      heatedSeatFrontRightChange(VehicleSeatState.OFF);
    }
    _showSeatOptions.sink.add(false);
    rearLeftSeatHeatingCoolingClicked = false;
    rearRightSeatHeatingCoolingClicked = false;
    frontLeftSeatHeatingCoolingClicked = false;
    frontRightSeatHeatingCoolingClicked = false;
  }

  void heatedCoolingSeatStatusHeatedClicked() {
    if (rearLeftSeatHeatingCoolingClicked) {
      heatedSeatRearLeftChange(VehicleSeatState.HEATED);
    } else if (rearRightSeatHeatingCoolingClicked) {
      heatedSeatRearRightChange(VehicleSeatState.HEATED);
    } else if (frontLeftSeatHeatingCoolingClicked) {
      heatedSeatFrontLeftChange(VehicleSeatState.HEATED);
    } else if (frontRightSeatHeatingCoolingClicked) {
      heatedSeatFrontRightChange(VehicleSeatState.HEATED);
    }
    _showSeatOptions.sink.add(false);
    rearLeftSeatHeatingCoolingClicked = false;
    rearRightSeatHeatingCoolingClicked = false;
    frontLeftSeatHeatingCoolingClicked = false;
    frontRightSeatHeatingCoolingClicked = false;
  }

  void heatedCoolingSeatStatusCoolClicked() {
    if (rearLeftSeatHeatingCoolingClicked) {
      heatedSeatRearLeftChange(VehicleSeatState.VENTILATED);
    } else if (rearRightSeatHeatingCoolingClicked) {
      heatedSeatRearRightChange(VehicleSeatState.VENTILATED);
    } else if (frontLeftSeatHeatingCoolingClicked) {
      heatedSeatFrontLeftChange(VehicleSeatState.VENTILATED);
    } else if (frontRightSeatHeatingCoolingClicked) {
      heatedSeatFrontRightChange(VehicleSeatState.VENTILATED);
    }
    _showSeatOptions.sink.add(false);
    rearLeftSeatHeatingCoolingClicked = false;
    rearRightSeatHeatingCoolingClicked = false;
    frontLeftSeatHeatingCoolingClicked = false;
    frontRightSeatHeatingCoolingClicked = false;
  }

  void blowerSettingChange(int status) {
    _blowerSetting.sink.add(status);
    if (isSaveClimateOption) {
      if (status == 1) {
        changeClimateSettings(CATEGORY_AIR_FLOW, AC_PARAM_UPPER_BODY, true);
      } else if (status == 2) {
        changeClimateSettings(CATEGORY_AIR_FLOW, AC_PARAM_FEET, true);
      } else if (status == 3) {
        changeClimateSettings(
            CATEGORY_AIR_FLOW, AC_PARAM_UPPER_BODY_FEET, true);
      } else if (status == 4) {
        changeClimateSettings(
            CATEGORY_AIR_FLOW, AC_PARAM_FRONT_DEFROST_FEET, true);
      }
    }
  }

  void isPre17CYStatusAlertChecked(bool? status) {
    _evPre17Checked.sink.add(status);
  }

  void evRemoteCheckStatus(bool? status) {
    _evRemoteChecked.sink.add(status);
  }

  void onFanSpeedReduced() {
    int parsedInt = fanSpeed! - 1;
    if (parsedInt >= minFanSpeed!) {
      fanSpeed = parsedInt;
      _fanSpeedState.sink.add(fanSpeed.toString());
      climateSettingPayload!.airFlowVolume = fanSpeed;
    }
  }

  void onFanSpeedIncreased() {
    int parsedInt = fanSpeed! + 1;
    if (parsedInt <= maxFanSpeed!) {
      fanSpeed = parsedInt;
      _fanSpeedState.sink.add(fanSpeed.toString());
      climateSettingPayload!.airFlowVolume = fanSpeed;
    }
  }

  void mainTemperatureToggleChange(bool value) {
    if (isSaveClimateOption) {
      climateSettingPayload!.settingsOn = value;
      _mainTemperatureToggleValue21MMandNG86.sink.add(value);
      _enableLayout.sink.add(value);
    }
  }

  void errorCallBack(Function(bool, String?)? savedSuccessfullyBottomSheet) {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (savedSuccessfullyBottomSheet != null) {
        savedSuccessfullyBottomSheet(false, toastMessage);
      }
    });
  }

  void successCallBack(Function(bool, String?)? savedSuccessfullyBottomSheet) {
    Future.delayed(const Duration(milliseconds: 500), () {
      savedSuccessfullyBottomSheet!(true, toastMessage);
    });
  }

  void logFirebaseEvent(bool value, String chileEventName) {
    if (value) {
      FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleMarketingEvent.REMOTE_SETTINGS_TOGGLE_ON,
          childEventName: chileEventName);
    } else {
      FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleMarketingEvent.REMOTE_SETTINGS_TOGGLE_OFF,
          childEventName: chileEventName);
    }
  }

  String stateToString(VehicleSeatState value) {
    if (value == VehicleSeatState.OFF) {
      return "Off";
    } else {
      return "On";
    }
  }

  bool stateToBool(VehicleSeatState value) {
    if (value == VehicleSeatState.OFF) {
      return false;
    } else {
      return true;
    }
  }

  @override
  void dispose() {
    _temperatureLevel.close();
    _enableTemperatureSlider.close();
    _blowerSetting.close();
    _frontLeftHeaterState.close();
    _frontRightHeaterState.close();
    _rearLeftHeaterState.close();
    _rearRightHeaterState.close();
    _rearDefoggerState.close();
    _frontDefoggerState.close();
    _sliderTemperatureLevel.close();
    _steeringHeatingState.close();
    _disableShimmerLoading.close();
    _evRemoteChecked.close();
    _evPre17Checked.close();
    _frontRightSeatHeatingVisibility.close();
    _frontLeftSeatHeatingVisibility.close();
    _rearLeftSeatHeatingVisibility.close();
    _rearRightSeatHeatingVisibility.close();
    _steeringHeatingVisibility.close();
    _heatedSeatVisibility.close();
    _defoggerVisibility.close();
    _rearDefoggerVisibility.close();
    _frontDefoggerVisibility.close();
    _airCirculationVisibility.close();
    _insideAirCirculationState.close();
    _blowerVisibility.close();
    _fanSpeedState.close();
    _fanSpeedVisibility.close();
    _saveButton.close();
    _saveButtonVisibility.close();
    _climateSettingsVisibility.close();
    _timeRemaining.close();
    _timeRemainingVisibility.close();
    _loadingLayoutVisibility.close();
    _tempRangeValues.close();
    _mainTemperatureToggleVisibility21MMandNG86.close();
    _mainTemperatureToggleValue21MMandNG86.close();
    _enableLayout.close();
    _showVehicleDataNotAvailable.close();
    _showSeatOptions.close();
    _showCoolingSeatOption.close();
    _vehicleInfoSubscription?.cancel();
  }
}
