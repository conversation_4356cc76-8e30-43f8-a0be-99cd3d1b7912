// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';

// Project imports:
import '../../vehicle_subscriptions/vehicle_subscriptions_page.dart';
import '/log/vehicle_analytic_event.dart';
import '/ui/vehicle_account/vehicle_announcement_detail/vehicle_announcement_detail_bloc.dart';

// Project imports:
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/announcement_center_entity.dart'
    as announcementCenterEntity;

class VehicleAnnouncementDetailPage extends StatefulWidget {
  final announcementCenterEntity.Messages? announcementDetailPayload;
  final String? vin;

  VehicleAnnouncementDetailPage({this.announcementDetailPayload, this.vin});

  @override
  _VehicleAnnouncementDetailPageState createState() =>
      _VehicleAnnouncementDetailPageState();
}

class _VehicleAnnouncementDetailPageState
    extends State<VehicleAnnouncementDetailPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  VehicleAnnouncementDetailBloc _bloc = VehicleAnnouncementDetailBloc();

  @override
  void initState() {
    super.initState();
    _bloc.init(widget.announcementDetailPayload, widget.vin);
    _bloc.progressHandlerCallback = (bool value) {
      if (value) {
        showProgress(context);
      } else {
        dismissProgress(context);
      }
    };
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_ACCOUNCEMENT_DETAIL_PAGE);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        top: true,
        child: BlocProvider(
            bloc: _bloc,
            child: OneAppScaffold(
              body: Padding(
                  padding: EdgeInsets.only(left: 16.w, right: 16.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SwipeBarIcon(),
                      bottomSheetAlertCustomAppBar(_bloc.getTitle()!,
                          onBackPressed: () {
                        Navigator.of(context).pop();
                      }, elevation: 0),
                      Expanded(child: _announcementDetailLayout())
                    ],
                  )),
            )));
  }

  Widget _announcementDetailLayout() {
    return StreamBuilder<bool>(
        stream: _bloc.alertDetail,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return _announcementDetail();
          } else {
            return Container();
          }
        });
  }

  //Detailed view of announcements
  Widget _announcementDetail() {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Container(
            margin: EdgeInsets.only(top: 25.h),
            child: ListView(
              children: [
                widget.announcementDetailPayload?.image != null
                    ? Image.network(widget.announcementDetailPayload?.image)
                    : SvgPicture.asset(
                        _bloc.checkForSubscriptionFlowType()
                            ? warningIcon
                            : _bloc.checkForFlowType()
                                ? removeIcon
                                : engineIcon,
                        height:
                            _bloc.checkForSubscriptionFlowType() ? 48.h : 120.h,
                        width:
                            _bloc.checkForSubscriptionFlowType() ? 48.h : 100.h,
                        colorFilter: _bloc.checkForSubscriptionFlowType()
                            ? null
                            : ColorFilter.mode(
                                _bloc.checkForFlowType()
                                    ? _colorUtil.tertiary05
                                    : _colorUtil.gradientThreeStart,
                                BlendMode.srcIn,
                              ),
                        allowDrawingOutsideViewBox: true,
                        semanticsLabel: _bloc.checkForSubscriptionFlowType()
                            ? WARNING_ICON
                            : _bloc.checkForFlowType()
                                ? REMOVE_ICON
                                : ENGINE_ICON,
                      ),
                Container(
                    margin: EdgeInsets.only(top: 8.h, bottom: 16.h),
                    child: Center(
                        child: Text(_bloc.getHeader()!,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.subHeadline1,
                                _colorUtil.tertiary03)))),
                Text(_bloc.getMessage()!,
                    textAlign: TextAlign.justify,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05)),
              ],
            ),
          ),
        ),
        Container(
          child: Column(
            children: [
              _dismissTextButton(),
              _doneButton(),
              _bloc.findFlow() == 'failure' ? _tryAgainButton() : Container(),
            ],
          ),
        ),
      ],
    );
  }

  //check cta condition and redirect to dashboard
  void _checkCtaAndRedirectToDashboard() {
    String flow = _bloc.findFlow();
    if (flow == 'success' || flow == 'failure') {
      _redirectToDashboard();
    } else {
      _mainFlow();
    }
  }

  //conditions for actual flow
  void _mainFlow() {
    if (widget.announcementDetailPayload!.cta == CTA_DEALER) {
      loadScheduleServiceNativePage();
    } else if (widget.announcementDetailPayload!.cta == CTA_PHONE) {
      _bloc.openPhoneDialer();
    } else if (widget.announcementDetailPayload!.cta == CTA_ADD_SUBSCRIPTION) {
      showMaterialModalBottomSheet(
        expand: true,
        isDismissible: true,
        context: context,
        useRootNavigator: true,
        backgroundColor: _colorUtil.tertiary15,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(CARD_RADIUS),
          ),
        ),
        builder: (context) => SafeArea(
          top: true,
          child: Column(
            children: [
              SwipeBarIcon(),
              Expanded(child: VehicleSubscriptionsPage()),
            ],
          ),
        ),
      );
    } else if (widget.announcementDetailPayload!.cta == CTA_OTA) {
      _tryAgainListener();
    }
  }

  //redirect to dashboard
  void _redirectToDashboard() {
    goBackToDashboardNative();
  }

  void _refreshUI(
      announcementCenterEntity.Messages announcementDetailPayload, String vin) {
    _bloc.init(announcementDetailPayload, vin);
  }

  //try again listener for try again button
  void _tryAgainListener() {
    _bloc.updateOTA(_refreshUI);
  }

  //Done button
  Widget _doneButton() {
    return Container(
      margin: EdgeInsets.only(top: 16.h, bottom: 16..h),
      child: CustomDefaultButton(
        backgroundColor: _colorUtil.button01b,
        buttonTextColor: _colorUtil.button01a,
        text: _bloc.getPrimaryBtnText(),
        press: _checkCtaAndRedirectToDashboard,
        borderColor: _colorUtil.button01b,
        horizontalPadding: 32.w,
        verticalPadding: 4.h,
      ),
    );
  }

  //Dismiss Text Button
  Widget _dismissTextButton() {
    return Visibility(
      visible: !_bloc.checkForFlowType(),
      child: Padding(
          padding: EdgeInsets.only(left: 8.w, right: 8.w),
          child: TextButton(
            onPressed: () {
              _showAnnouncementConfirmationPopup();
            },
            child: Text(
              _bloc.announcementDetailMessage!.dismissText ?? "",
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.buttonLink1, _colorUtil.button02a),
            ),
          )),
    );
  }

  //try again button
  Widget _tryAgainButton() {
    return Visibility(
      visible: _bloc.checkForFlowType(),
      child: Container(
        margin: EdgeInsets.only(top: 16.h, bottom: 32.h),
        child: CustomDefaultButton(
          backgroundColor: _colorUtil.button01b,
          buttonTextColor: _colorUtil.button01a,
          text: _bloc.getSecondaryBtnText(),
          press: _tryAgainListener,
          borderColor: _colorUtil.button01b,
          horizontalPadding: 32.w,
          verticalPadding: 4.h,
        ),
      ),
    );
  }

  // Confirmation to Dismiss the Announcement
  void _showAnnouncementConfirmationPopup() {
    BottomConfirmationDialog().showBottomDialog(
        context,
        "",
        widget.announcementDetailPayload!.alertMessage!,
        carIcon,
        widget.announcementDetailPayload!.alertPositiveButtonText!,
        widget.announcementDetailPayload!.alertNegativeButtonText!,
        _confirmDismissAnnouncement);
  }

  void _confirmDismissAnnouncement() {
    _bloc.dismissAnnouncement(_callCallBack);
  }

  void _callCallBack(String? msg) {
    if (msg != null) {
      showBaseDialog(context, message: msg);
      return;
    }
    _redirectToDashboard();
  }
}
