// Flutter imports:

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '/local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/announcement_center_entity.dart'
    as announcementCenterEntity;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleAnnouncementDetailBloc extends BlocBase {
  announcementCenterEntity.Messages? announcementDetailMessage;
  String? vin;
  OneAppClient api = APIClientConfig.oneAppClient;

  late Function(bool) progressHandlerCallback;
  Function(String)? dialogHandlerCallback;
  vehicleInfo.Payload? vehicleItem;
  GetIt locator = GetIt.instance;

  final _announcementDetailPayload = BehaviorSubject<bool>();
  Stream<bool> get alertDetail => _announcementDetailPayload.stream;

  void init(announcementCenterEntity.Messages? detailPayload,
      String? vehicleIdentificationNumber) async {
    announcementDetailMessage = detailPayload;
    vin = vehicleIdentificationNumber;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);

    _announcementDetailPayload.sink.add(true);
  }

  //check for success or failure flow
  bool checkForFlowType() {
    bool isFailureFlow = false;
    if (announcementDetailMessage!.cta == CTA_OTA &&
        announcementDetailMessage!.status == STATUS_FAILED) {
      // Failure flow.
      isFailureFlow = true;
    } else {
      // Success flow or any other flow.
      isFailureFlow = false;
    }
    return isFailureFlow;
  }

  //check for subscription expiring Flow type
  bool checkForSubscriptionFlowType() {
    bool isSubscriptionFlow = false;
    if (announcementDetailMessage!.cta == CTA_ADD_SUBSCRIPTION) {
      isSubscriptionFlow = true;
    } else {
      isSubscriptionFlow = false;
    }
    return isSubscriptionFlow;
  }

  //check for flow to continue
  String findFlow() {
    String whichFlow = '';
    if (announcementDetailMessage!.cta == CTA_OTA &&
        (announcementDetailMessage!.status == STATUS_COMPLETED ||
            announcementDetailMessage!.status == STATUS_SCHEDULED)) {
      whichFlow = 'success';
    } else if (announcementDetailMessage!.cta == CTA_OTA &&
        announcementDetailMessage!.status == STATUS_FAILED) {
      whichFlow = 'failure';
    } else {
      whichFlow = 'main';
    }

    return whichFlow;
  }

  String? getTitle() {
    String? titleText = '';
    if (announcementDetailMessage != null &&
        announcementDetailMessage!.title != null) {
      titleText = announcementDetailMessage!.title;
    }
    return titleText;
  }

  String? getHeader() {
    String? headerText = '';
    if (announcementDetailMessage != null &&
        announcementDetailMessage!.header != null) {
      headerText = announcementDetailMessage!.header;
    }
    return headerText;
  }

  String? getMessage() {
    String? messageText = '';
    if (announcementDetailMessage != null &&
        announcementDetailMessage!.body != null) {
      messageText = announcementDetailMessage!.body;
    }
    return messageText;
  }

  String? getPrimaryBtnText() {
    String? primaryBtnText = '';
    if (announcementDetailMessage != null &&
        announcementDetailMessage!.ctaText != null) {
      primaryBtnText = announcementDetailMessage!.ctaText;
    }
    return primaryBtnText;
  }

  String? getSecondaryBtnText() {
    String? secondaryBtnText = '';
    if (announcementDetailMessage != null &&
        announcementDetailMessage!.secondaryCtaText != null) {
      secondaryBtnText = announcementDetailMessage!.secondaryCtaText;
    }
    return secondaryBtnText;
  }

  //open phone dialer
  void openPhoneDialer() async {
    dialerLauncher(announcementDetailMessage!.phone!);
  }

  //update softwares
  void updateOTA(Function callBackFunction) async {
    progressHandlerCallback(true);

    final commonResponse = await api.updateOTA(vin!, vehicleItem!.brand!);

    if (commonResponse.response != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_ALERT_OTA_UPDATE_SUCCESS,
          category: LogCategory.FL_VEHI);

      await VehicleRepo().storeAnnouncementCenter(vehicleItem);
      locator
          .isReady<announcementCenterEntity.AnnouncementCenterPayload>()
          .then((value) async {
        announcementCenterEntity.AnnouncementCenterPayload?
            announcementCenterEntityPayload;
        try {
          announcementCenterEntityPayload =
              locator<announcementCenterEntity.AnnouncementCenterPayload>();
        } catch (e) {}
        if (announcementCenterEntityPayload != null) {
          FireBaseAnalyticsLogger.logSuccessAPI(
              VehicleAnalyticsEvent
                  .VEHICLE_FETCH_VEHICLE_ALERTS_FROM_LOCALDB_SUCCESS,
              category: LogCategory.FL_VEHI);
          if (announcementCenterEntityPayload.messages!.isNotEmpty) {
            callBackFunction(announcementCenterEntityPayload.messages!.first,
                announcementCenterEntityPayload.vin);
          }
        } else {
          FireBaseAnalyticsLogger.logErrorAPI(
              VehicleAnalyticsEvent
                  .VEHICLE_FETCH_VEHICLE_ALERTS_FROM_LOCALDB_FAILURE,
              category: LogCategory.FL_VEHI);
        }
      });
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_ALERT_OTA_UPDATE_FAILURE,
          category: LogCategory.FL_VEHI);
    }
    progressHandlerCallback(false);

    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.ADD_SERVICE_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  //dismiss Announcement
  void dismissAnnouncement(Function callBackFunction) async {
    progressHandlerCallback(true);

    final commonResponse = await api.dismissAnnouncement(
        vin!,
        announcementDetailMessage!.messageCategory!,
        announcementDetailMessage!.messageId!,
        Global.getInstance().correlationId);

    progressHandlerCallback(false);

    if (commonResponse.response != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_ALERT_DISMISS_UPDATE_SUCCESS,
          category: LogCategory.FL_VEHI);
      callBackFunction(null);
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_ALERT_DISMISS_UPDATE_FAILURE,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      callBackFunction(commonResponse.error?.errorMessage);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_ALERT_DISMISS_UPDATE_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  @override
  void dispose() {
    _announcementDetailPayload.close();
  }
}
