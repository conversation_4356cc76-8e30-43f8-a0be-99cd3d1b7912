// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/widget/transperent_image.dart';

class VehicleMarketingBannerCarouselCard extends StatefulWidget {
  VehicleMarketingBannerCarouselCard(
      {Key? key, this.imagePath, this.clickCallBack})
      : super(key: key);

  final String? imagePath;
  final Function? clickCallBack;

  @override
  _VehicleMarketingBannerCarouselCardState createState() =>
      _VehicleMarketingBannerCarouselCardState();
}

class _VehicleMarketingBannerCarouselCardState
    extends State<VehicleMarketingBannerCarouselCard> {
  final _colorUtil = ThemeConfig.current().colorUtil;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(CARD_RADIUS_SMALL),
          color: _colorUtil.tile03,
          boxShadow: [
            BoxShadow(
              spreadRadius: 0.5,
              color: Color.fromRGBO(0, 0, 0, 0.08),
              offset: Offset(0, 2),
              blurRadius: 0.5,
            )
          ]),
      child: InkWell(
        onTap: widget.clickCallBack as void Function()?,
        child: Container(
          child: ClipRRect(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(CARD_RADIUS_SMALL),
              topLeft: Radius.circular(CARD_RADIUS_SMALL),
            ),
            child: FadeInImage.memoryNetwork(
              fit: BoxFit.fitWidth,
              placeholder: kTransparentImage,
              image: widget.imagePath!,
              height: double.maxFinite,
              width: double.maxFinite,
            ),
          ),
        ),
      ),
    );
  }

  dynamic dummyCallBackListener() {}
}
