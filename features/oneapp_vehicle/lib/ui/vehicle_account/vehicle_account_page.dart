// Dart imports:
import 'dart:convert';

// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/login_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_outlined_button.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_marketing_consent_entity.dart';

// Project imports:
import '../../log/vehicle_analytic_event.dart';
import '../../log/vehicle_marketing_event.dart';
import '../../ui/vehicle_account/vehicle_account_bloc.dart';
import '../../ui/vehicle_account/vehicle_announcement_list/vehicle_announcements_list_page.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/announcement_center_entity.dart'
    as announcementCenterEntity;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

// Used to view and change the vehicle account
class VehicleAccountPage extends StatefulWidget {
  @override
  _VehicleAccountPageState createState() => _VehicleAccountPageState();
}

class _VehicleAccountPageState extends State<VehicleAccountPage>
    with WidgetsBindingObserver {
  VehicleAccountBloc _bloc = VehicleAccountBloc();
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  late BuildContext _builderContext;
  late String _imagePath;
  late String _brandText;
  static const platform = const MethodChannel(callNativeMethod);

  bool _isLargeText = false;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    _bloc.init(_progressHandlerCallback);
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_ACCOUNT_PAGE);
    if (_bloc.brand == "T") {
      _imagePath = marketingConsentToyotaIcon;
      _brandText = "Toyota";
    } else {
      _imagePath = marketingConsentLexusIcon;
      _brandText = "Lexus";
    }
    // Method channel for native calls
    platform.setMethodCallHandler(_handleConsent);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _bloc.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _bloc.init(_progressHandlerCallback);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        child: Navigator(
            onGenerateRoute: (_) =>
                MaterialPageRoute(builder: (materialContext) {
                  return Builder(builder: (builderContext) {
                    _builderContext = builderContext;
                    return _scaffoldLayout();
                  });
                })));
  }

  Widget _scaffoldLayout() {
    return BlocProvider(
      bloc: _bloc,
      child: OneAppScaffold(
        backgroundColor: _colorUtil.tertiary15,
        body: SingleChildScrollView(
          controller: ModalScrollController.of(_builderContext),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _signOutButton(
                  OneAppString.of().logoutUser, _showSignOutConfirmationPopup),
              _accountInfoLayout(),
              _inboxText(),
              _inboxLayout(),
              _takeATourLayout(),
              _announcementCarouselCardLayout(),
              _displayMarketingConsent(),
              _accessibilityText(),
              _accessibilityLayout()
            ],
          ),
        ),
      ),
    );
  }

  //SignOut Button
  Widget _signOutButton(String text, VoidCallback onPressed) {
    return Container(
      alignment: Alignment.topRight,
      padding: EdgeInsets.only(right: 16.w),
      child: TextButton(
        onPressed: onPressed,
        child: Text(
          formatTextForLexusAndToyota(text),
          style: TextStyleExtension().newStyleWithColor(
              _textStyleUtil.callout1, _colorUtil.tertiary05),
        ),
      ),
    );
  }

  // Confirmation to Sign Out the user
  void _showSignOutConfirmationPopup() {
    BottomConfirmationDialog().showBottomDialog(
        context,
        OneAppString.of().signOutConfirmationHeading,
        OneAppString.of().signOutConfirmationSubHeading,
        userIcon,
        OneAppString.of().logoutUser,
        OneAppString.of().commonCancel,
        _confirmSignOutClick);
  }

  // logout and redirect to signin on confirm click
  void _confirmSignOutClick() {
    Global.getInstance().keepMeLogin = false;
    Global.getInstance().setFinanceAccountInfo(null);
    LoginUtil.forceToLogin();
  }

  Widget _accountInfoLayout() {
    return Container(
      child: Column(
        children: [
          Container(
            width: 120.w,
            height: 120.h,
            child: CircleAvatar(
              backgroundColor: Colors.transparent,
              child: ClipOval(
                child: StreamBuilder<String?>(
                    stream: _bloc.profilePicBase64,
                    builder: (context, snapshot) {
                      if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                        return Image.memory(
                          base64Decode(snapshot.data!),
                          semanticLabel: PROFILE_IMAGE,
                        );
                      } else {
                        return SvgPicture.asset(
                          profilePicDefaultIcon,
                        );
                      }
                    }),
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 20.h, left: 30.w, right: 30.w),
            child: StreamBuilder<String?>(
              stream: _bloc.userProfileName,
              builder: (context, snapshot) {
                return (snapshot.hasData && snapshot.data!.isNotEmpty)
                    ? Text(
                        snapshot.data!,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.subHeadline2, _colorUtil.button02a),
                      )
                    : Container();
              },
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 10.h, left: 80.w, right: 80.w),
            child: Text(
              OneAppString.of().accountOptions,
              textAlign: TextAlign.center,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary00),
            ),
          ),
          Container(
              margin: EdgeInsets.only(top: 10.h, bottom: 20.h),
              child: CustomOutlinedButton(
                buttonWidth: 0,
                buttonText: OneAppString.of().account,
                borderColor: _colorUtil.button02c,
                textColor: _colorUtil.button02a,
                backgroundColor: _colorUtil.tertiary15,
                clickCallBack: () {
                  loadAccountNativePage();
                },
              )),
        ],
      ),
    );
  }

  Widget _inboxText() {
    return Container(
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.only(top: 10.h, bottom: 10.h, left: 20.w),
      child: Text(
        OneAppString.of().inbox,
        style: TextStyleExtension().newStyleWithColor(
            _textStyleUtil.subHeadline3, _colorUtil.tertiary00),
      ),
    );
  }

  //layout for inbox and its description
  Widget _inboxLayout() {
    return InkWell(
        onTap: () {
          _bloc.goNative();
          FireBaseAnalyticsLogger.logMarketingGroupEvent(
              VehicleMarketingEvent.DASHBOARD_CARD,
              childEventName: VehicleMarketingEvent.DASHBOARD_NHISTORY);
          loadNotificationNativePage();
        },
        child: Container(
          padding:
              EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 16.h),
          margin:
              EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 6.h),
          decoration: _colorUtil.dayElevationPaneBottom02Mid(),
          child: Stack(
            children: [
              Column(
                children: [
                  Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: 15.w),
                        alignment: Alignment.center,
                        child: SvgPicture.asset(
                          notificationIcon,
                          height: 48.h,
                          width: 48.w,
                          allowDrawingOutsideViewBox: false,
                          semanticsLabel: NOTIFICATION_ICON,
                        ),
                      ),
                      Text(
                        OneAppString.of().notifications,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.body4, _colorUtil.tertiary03),
                      ),
                    ],
                  ),
                  Container(
                    padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      OneAppString.of().notificationContent,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1, _colorUtil.tertiary05),
                    ),
                  ),
                ],
              ),
              StreamBuilder<bool?>(
                  stream: _bloc.accountBadgeStatus,
                  builder: (context, snapshot) {
                    if (snapshot.hasData && snapshot.data == true) {
                      return dotCircle();
                    } else {
                      return Container();
                    }
                  }),
            ],
          ),
        ));
  }

  //layout for Take a Tour
  Widget _takeATourLayout() {
    return InkWell(
        child: Container(
      padding:
          EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 16.h),
      margin: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 6.h),
      decoration: _colorUtil.dayElevationPaneBottom02Mid(),
      child: Stack(
        children: [
          Column(
            children: [
              Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(right: 15.w),
                    alignment: Alignment.center,
                    child: SvgPicture.asset(
                      announcementIcon,
                      height: 48.h,
                      width: 48.w,
                      allowDrawingOutsideViewBox: false,
                      semanticsLabel: ANNOUNCEMENT_ICON,
                    ),
                  ),
                  Text(
                    OneAppString.of().takeATour,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.body4, _colorUtil.tertiary03),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
                alignment: Alignment.centerLeft,
                child: Text(
                  OneAppString.of().takeATourContent,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1, _colorUtil.tertiary05),
                ),
              ),
            ],
          ),
          dotCircle()
        ],
      ),
    ));
  }

  Widget dotCircle() {
    return Positioned(
      top: 0.0,
      right: 0.0,
      child: Container(
        width: 5.w,
        height: 5.h,
        decoration: BoxDecoration(
          color: _colorUtil.primary01,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  // Horizontal swiping cards or carousel cards for announcements.
  Widget _announcementCarouselCardLayout() {
    return StreamBuilder<
            List<announcementCenterEntity.AnnouncementCenterPayload>>(
        stream: _bloc.announcementListPayLoad,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            if (snapshot.hasData && snapshot.data!.isNotEmpty) {
              return Container(
                child: _announcementCard(snapshot.data!),
              );
            } else {
              return Container();
            }
          } else {
            return _shimmerLayout();
          }
        });
  }

  Widget _shimmerLayout() {
    return Container(
        margin:
            EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 16.h),
        decoration: _colorUtil.dayElevationPaneBottom02Mid(),
        child: shimmerTileCard());
  }

  Widget _announcementCard(
      List<announcementCenterEntity.AnnouncementCenterPayload>
          announcementData) {
    return Stack(
      children: [
        Container(
          padding:
              EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 16.h),
          margin:
              EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 16.h),
          decoration: _colorUtil.dayElevationPaneBottom02Mid(),
          child: InkWell(
            onTap: () {
              _navigateToDetailPage(announcementData);
            },
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      margin: EdgeInsets.only(right: 15.w),
                      alignment: Alignment.center,
                      child: SvgPicture.asset(
                        announcementIcon,
                        height: 48.h,
                        width: 48.w,
                        allowDrawingOutsideViewBox: false,
                        semanticsLabel: ANNOUNCEMENT_ICON,
                      ),
                    ),
                    Text(
                      OneAppString.of().announcements,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body4, _colorUtil.tertiary03),
                    ),
                  ],
                ),
                Container(
                  padding: EdgeInsets.all(16.w),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    announcementData.isNotEmpty
                        ? OneAppString.of().announcementText(
                            announcementData.length.toString())
                        : OneAppString.of().noAnnouncementText,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _displayMarketingConsent() {
    return StreamBuilder<EligibleConsents>(
        stream: _bloc.marketingConsent,
        builder: (context, snapshot) {
          return (snapshot.hasData)
              ? Container(
                  height: 370,
                  padding: EdgeInsets.only(
                      top: 16.h, left: 16.w, right: 16.w, bottom: 16.h),
                  margin: EdgeInsets.only(
                      top: 16.h, left: 16.w, right: 16.w, bottom: 16.h),
                  decoration: _colorUtil.dayElevationPaneBottom02Mid(),
                  child: SizedBox(
                    width: double.maxFinite,
                    child: Column(
                      children: [
                        Container(
                          height: 122.h,
                          child: ClipRRect(
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(CARD_RADIUS_SMALL),
                              topLeft: Radius.circular(CARD_RADIUS_SMALL),
                            ),
                            child: Image.asset(
                              _imagePath,
                              height: double.maxFinite,
                              width: double.maxFinite,
                              fit: BoxFit.contain,
                              alignment: Alignment.center,
                            ),
                          ),
                        ),
                        Container(
                          height: 28.h,
                        ),
                        Container(
                          margin: EdgeInsets.only(left: 16.w, right: 16.w),
                          child: Text(
                              OneAppString.of()
                                  .inAppMarketingCardDesc(_brandText),
                              textAlign: TextAlign.center,
                              maxLines: 4,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.body1, _colorUtil.tertiary05)),
                        ),
                        Container(
                          height: 28.h,
                        ),
                        CustomDefaultButton(
                          press: () {
                            _marketingConsentClickListener(snapshot.data);
                          },
                          backgroundColor: _colorUtil.button01b,
                          buttonTextColor: _colorUtil.button01a,
                          text: OneAppString.of().inAppMarketingCardBtn,
                          borderColor: _colorUtil.button01b,
                          horizontalPadding: 12.w,
                          verticalPadding: 4.h,
                        ),
                      ],
                    ),
                  ),
                )
              : Container();
        });
  }

  Widget _accessibilityText() {
    if (_bloc.checkBiggerFontEnabled()) {
      return Container(
        alignment: Alignment.centerLeft,
        margin: EdgeInsets.only(top: 10.h, bottom: 10.h, left: 20.w),
        child: Text(
          OneAppString.of().accessibility,
          style: TextStyleExtension().newStyleWithColor(
              _textStyleUtil.subHeadline3, _colorUtil.tertiary00),
        ),
      );
    } else {
      return Container();
    }
  }

  //layout for inbox and its description
  Widget _accessibilityLayout() {
    if (_bloc.checkBiggerFontEnabled()) {
      return InkWell(
          onTap: () {
            // loadNotificationNativePage();
          },
          child: Container(
            padding: EdgeInsets.only(
                top: 16.h, left: 16.w, right: 16.w, bottom: 16.h),
            margin: EdgeInsets.only(
                top: 16.h, left: 16.w, right: 16.w, bottom: 6.h),
            decoration: _colorUtil.dayElevationPaneBottom02Mid(),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      height: 48.w,
                      width: 48.w,
                      margin: EdgeInsets.only(right: 15.w),
                      decoration: BoxDecoration(
                        color: _colorUtil.button02b,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: SvgPicture.asset(fontZoomIcon,
                          height: 24.w,
                          width: 24.w,
                          colorFilter: ColorFilter.mode(
                            _colorUtil.success01,
                            BlendMode.srcIn,
                          ),
                          allowDrawingOutsideViewBox: true,
                          semanticsLabel: FONTZOOM_ICON),
                    ),
                    Text(
                      OneAppString.of().accessibilityTitle,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body4, _colorUtil.tertiary03),
                    ),
                    Expanded(
                        child: StreamBuilder<bool>(
                      stream: _bloc.isBiggerFont,
                      builder: (context, snapshot) {
                        return (snapshot.hasData)
                            ? Container(
                                alignment: Alignment.centerRight,
                                child: CupertinoSwitch(
                                    value: snapshot.data!,
                                    activeColor: _colorUtil.secondary01,
                                    onChanged: (value) {
                                      _isLargeText = value;
                                      appGlobalKey.currentState!
                                          .setState(() {});
                                      _bloc.setBiggerFont(_isLargeText);
                                    }),
                              )
                            : Container();
                      },
                    ))
                  ],
                ),
                Container(
                  padding: EdgeInsets.all(16.w),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    OneAppString.of().accessibilitySubTitle,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05),
                  ),
                ),
              ],
            ),
          ));
    } else {
      return Container();
    }
  }

  //Method to generate  list of dot indicators widgets.
  List<T?> map<T>(List list, Function handler) {
    List<T?> result = [];
    for (var i = 0; i < list.length; i++) {
      result.add(handler(i, list[i]));
    }
    return result;
  }

  //showing bottomsheet for detail view of announcements
  _navigateToDetailPage(
      List<announcementCenterEntity.AnnouncementCenterPayload>
          announcementData) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) =>
          VehicleAnnouncementsPage(announcementPayloadObj: announcementData),
    );
  }

  _marketingConsentClickListener(EligibleConsents? dataConsent) {
    loadMarketingConsentNativePage(jsonEncode(dataConsent));
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  // Method used to pull data from native

  Future<dynamic> _handleConsent(MethodCall call) async {
    switch (call.method.toLowerCase()) {
      case "vehiclelist":
        {
          GetIt locator = GetIt.instance;
          List<vehicleInfo.Payload> vehicleInfoListEntity = [];
          try {
            vehicleInfoListEntity = locator<List<vehicleInfo.Payload>>();
          } catch (e) {}
          return Future.value(jsonEncode(vehicleInfoListEntity));
        }
      case "selectedvin":
        {
          return Future.value(Global.getInstance().vin);
        }
      case "marketingConsentStatus":
        {
          if (call.arguments != null) {
            _bloc.fetchMarketingConsent();
          }
        }
        break;
      case "accountbadgestatus":
        {
          if (call.arguments != null && call.arguments['badgeStatus'] != null) {
            _bloc.updateBadgeStatus(call.arguments['badgeStatus']);
          }
        }
        break;
    }
  }
}
