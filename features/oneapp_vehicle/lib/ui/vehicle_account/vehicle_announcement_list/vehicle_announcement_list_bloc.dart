// Dart imports:
import 'dart:convert';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '/local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/announcement_center_entity.dart'
    as announcementCenterEntity;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleAnnouncementsBloc extends BlocBase {
  announcementCenterEntity.Messages? announcementDetailMessage;
  OneAppClient api = APIClientConfig.oneAppClient;

  Function(bool)? progressHandlerCallback;
  Function(String)? dialogHandlerCallback;
  vehicleInfo.Payload? vehicleItem;
  GetIt locator = GetIt.instance;

  final _announcementDetailPayload = BehaviorSubject<bool>();

  List<announcementCenterEntity.AnnouncementCenterPayload>?
      announcementPayloadData;

  Stream<bool> get alertDetail => _announcementDetailPayload.stream;

  final _vehicleSelectedIndex = BehaviorSubject<int>();

  Stream<int> get vehicleSelectedIndex => _vehicleSelectedIndex.stream;

  final _vehicleListPayLoad = BehaviorSubject<List<vehicleInfo.Payload>>();

  Stream<List<vehicleInfo.Payload>> get vehicleListPayLoad =>
      _vehicleListPayLoad.stream;

  final _announcementListPayLoad = BehaviorSubject<
      List<announcementCenterEntity.AnnouncementCenterPayload>?>();

  Stream<List<announcementCenterEntity.AnnouncementCenterPayload>?>
      get announcementListPayLoad => _announcementListPayLoad.stream;

  List<announcementCenterEntity.AnnouncementCenterPayload>? mapTo(
      Object? payloadObj) {
    if (payloadObj != null) {
      if (payloadObj is Map) {
        List<announcementCenterEntity.AnnouncementCenterPayload>
            announcementList = [];
        List<dynamic> dynamicList =
            jsonDecode(payloadObj["announcementsList"].toString());
        for (final dynamic item in dynamicList) {
          announcementCenterEntity.AnnouncementCenterPayload
              announcementPayload =
              announcementCenterEntity.AnnouncementCenterPayload.fromJson(item);
          announcementList.add(announcementPayload);
        }
        return announcementList;
      }
    }
    return null;
  }

  void init(
      Function(bool) progressHandler,
      List<announcementCenterEntity.AnnouncementCenterPayload>?
          announcementPayload) async {
    progressHandlerCallback = progressHandler;
    announcementPayloadData = announcementPayload;
    String? vin = Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);
    _announcementDetailPayload.sink.add(true);
    getVehicleModelList();

    _announcementListPayLoad.sink.add(announcementPayloadData);
    //To get Currently selected vehicle announcement list
    changeSelectedVehicle(Global.getInstance().vin, 0);
  }

  void getVehicleModelList() {
    locator.isReady<List<vehicleInfo.Payload>>().then((value) async {
      List<vehicleInfo.Payload> vehicleListPayload = [];
      try {
        vehicleListPayload = locator<List<vehicleInfo.Payload>>();
        if (vehicleListPayload.isNotEmpty) {
          vehicleInfo.Payload selectedVinPayload = vehicleListPayload
              .firstWhere((element) => element.vin == Global.getInstance().vin);
          vehicleListPayload.remove(selectedVinPayload);
          vehicleListPayload.insert(0, selectedVinPayload);
          _vehicleListPayLoad.sink.add(vehicleListPayload);
        }
      } catch (e) {}
    });
  }

  @override
  void dispose() {
    _announcementDetailPayload.close();
    _vehicleSelectedIndex.close();
    _vehicleListPayLoad.close();
    _announcementListPayLoad.close();
  }

  void changeSelectedVehicle(String? selectedVin, int index) {
    List<announcementCenterEntity.AnnouncementCenterPayload> _newVehicleList =
        announcementPayloadData!.where((i) => i.vin == selectedVin).toList();
    _announcementListPayLoad.sink.add(_newVehicleList);
    _vehicleSelectedIndex.sink.add(index);
  }
}
