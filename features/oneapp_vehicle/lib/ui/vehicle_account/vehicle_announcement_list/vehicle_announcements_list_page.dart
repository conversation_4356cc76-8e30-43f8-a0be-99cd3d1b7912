// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';

// Project imports:
import '../../vehicle_account/vehicle_announcement_detail/vehicle_announcements_detail_page.dart';
import '../../vehicle_account/vehicle_announcement_list/vehicle_announcement_list_bloc.dart';
import '/log/vehicle_analytic_event.dart';

// Project imports:
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/announcement_center_entity.dart'
    as announcementCenterEntity;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/announcement_center_entity.dart'
    as vehicleAlerts;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleAnnouncementsPage extends StatefulWidget {
  final Object? announcementPayloadObj;

  VehicleAnnouncementsPage({this.announcementPayloadObj});

  @override
  _VehicleAnnouncementsPageState createState() =>
      _VehicleAnnouncementsPageState();
}

class _VehicleAnnouncementsPageState extends State<VehicleAnnouncementsPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final PageController pageController = PageController();
  ScrollController _scrollController = ScrollController();
  List<announcementCenterEntity.AnnouncementCenterPayload>?
      announcementPayloadData;
  int currentPageIndex = 0;

  VehicleAnnouncementsBloc _bloc = VehicleAnnouncementsBloc();

  @override
  void initState() {
    super.initState();
    announcementPayloadData = _bloc.mapTo(widget.announcementPayloadObj);
    _bloc.init(_progressHandlerCallback, announcementPayloadData);
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_ACCOUNCEMENT_DETAIL_PAGE);
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        child: SafeArea(
            top: true,
            child: BlocProvider(
                bloc: _bloc,
                child: OneAppScaffold(
                  body: Padding(
                      padding: EdgeInsets.only(left: 16.w, right: 16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SwipeBarIcon(),
                          bottomSheetCustomAppBar(
                              OneAppString.of().announcements,
                              onBackPressed: () {
                            goBackToNative();
                          }, elevation: 0),
                          Flexible(
                            child: StreamBuilder<List<vehicleInfo.Payload>>(
                                stream: _bloc.vehicleListPayLoad,
                                builder: (context, vehicleListSnapshot) {
                                  if (vehicleListSnapshot.hasData &&
                                      vehicleListSnapshot.data!.isNotEmpty) {
                                    return Column(children: [
                                      Container(
                                          margin: EdgeInsets.all(15.w),
                                          height: 50.h,
                                          child: _vehicleList(
                                              vehicleListSnapshot.data!)),
                                      Expanded(
                                          child: _announcementList(
                                              vehicleListSnapshot.data!))
                                    ]);
                                  } else {
                                    return Container();
                                  }
                                }),
                          ),
                        ],
                      )),
                ))),
        onPopInvoked: (didPop) async {
          if (didPop) return;
          goBackToNative();
        });
  }

  Widget _vehicleList(List<vehicleInfo.Payload> vehicleListPayLoad) {
    return ListView.builder(
        shrinkWrap: true,
        controller: _scrollController,
        itemCount: vehicleListPayLoad.length,
        scrollDirection: Axis.horizontal,
        itemBuilder: (BuildContext context, int index) {
          return StreamBuilder<int>(
              stream: _bloc.vehicleSelectedIndex,
              initialData: 0,
              builder: (context, snapshot) {
                return Container(
                    margin: EdgeInsets.only(right: 15.w),
                    child: TextButton(
                      onPressed: () {
                        pageController.jumpToPage(index);
                        _bloc.changeSelectedVehicle(
                            vehicleListPayLoad[index].vin, index);
                      },
                      style: TextButton.styleFrom(
                          backgroundColor: (index == snapshot.data)
                              ? _colorUtil.button02a
                              : _colorUtil.button05b,
                          shape: StadiumBorder(),
                          minimumSize: Size(120.w, 40.h),
                          padding: EdgeInsets.symmetric(
                              horizontal: 15.w, vertical: 4.h)),
                      child: Text(
                        formatTextForLexusAndToyota(
                            vehicleListPayLoad[index].modelName!),
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout2,
                            (index == snapshot.data)
                                ? _colorUtil.button05b
                                : _colorUtil.button02a),
                      ),
                    ));
              });
        });
  }

  Widget _announcementList(List<vehicleInfo.Payload> vehicleListPayLoad) {
    return PageView.builder(
      controller: pageController,
      onPageChanged: (index) {
        _scrollController.animateTo(ScreenUtil().screenWidth / 2.5 * index,
            duration: Duration(seconds: 1), curve: Curves.fastOutSlowIn);
        _bloc.changeSelectedVehicle(vehicleListPayLoad[index].vin, index);
      },
      itemCount: vehicleListPayLoad.length,
      itemBuilder: (context, position) {
        return StreamBuilder<List<vehicleAlerts.AnnouncementCenterPayload>?>(
            stream: _bloc.announcementListPayLoad,
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                return ListView.builder(
                    shrinkWrap: true,
                    itemCount: snapshot.data!.length,
                    scrollDirection: Axis.vertical,
                    itemBuilder: (BuildContext context, int index) {
                      return Container(
                        padding: EdgeInsets.only(
                            top: 10.h, left: 10.w, right: 10.w, bottom: 10.h),
                        margin: EdgeInsets.only(
                            top: 10.h, left: 10.w, right: 10.w, bottom: 10.h),
                        decoration: _colorUtil.dayElevationPaneBottom02Mid(),
                        child: InkWell(
                          onTap: () {
                            _navigateToDetailPage(
                                snapshot.data![index].messages![index],
                                snapshot.data![index].vin);
                          },
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(right: 15.w),
                                    alignment: Alignment.center,
                                    child: SvgPicture.asset(
                                      announcementIcon,
                                      height: 48.h,
                                      width: 48.w,
                                      allowDrawingOutsideViewBox: false,
                                      semanticsLabel: ANNOUNCEMENT_ICON,
                                    ),
                                  ),
                                  //as per confirmation only one announcement will be present so getting 0th element of message
                                  Text(
                                    snapshot
                                        .data![index].messages![0].cardTitle!,
                                    style: TextStyleExtension()
                                        .newStyleWithColor(_textStyleUtil.body4,
                                            _colorUtil.tertiary03),
                                  ),
                                ],
                              ),
                              Container(
                                padding: EdgeInsets.all(16.w),
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  snapshot
                                      .data![index].messages![0].cardMessage!,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyleExtension().newStyleWithColor(
                                      _textStyleUtil.callout1,
                                      _colorUtil.tertiary05),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    });
              } else {
                return Container(
                  padding: EdgeInsets.all(16.w),
                  alignment: Alignment.center,
                  child: Text(
                    OneAppString.of().noAnnouncementText,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05),
                  ),
                );
              }
            });
      },
    );
  }

  //showing bottomsheet for detail view of announcements
  _navigateToDetailPage(
      announcementCenterEntity.Messages message, String? vin) {
    return showMaterialModalBottomSheet(
      backgroundColor: _colorUtil.tertiary15,
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => VehicleAnnouncementDetailPage(
          announcementDetailPayload: message, vin: vin),
    );
  }
}
