// Dart imports:
import 'dart:core';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/insure_connect_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_marketing_consent_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_account_grid_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_announcement_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_insure_connect_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '/local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/announcement_center_entity.dart'
    as announcementCenterEntity;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_marketing_consent_entity.dart'
    as marketingConsentEntity;

class VehicleAccountBloc extends BlocBase {
  List<VehicleAccountGridHelper> vehicleAccountGridHelperList = [];
  VehicleAccountGridHelper? accountGridItem, appSuiteGridItem;

  String? brand;
  List<OfferDetails>? offerDetails;
  bool _isNativeView = false;

  Stream<List<VehicleAccountGridHelper>> get vehicleAccountGridList =>
      _vehicleAccountGridList.stream;
  final BehaviorSubject<List<VehicleAccountGridHelper>>
      _vehicleAccountGridList =
      BehaviorSubject<List<VehicleAccountGridHelper>>();
  vehicleInfo.Payload? vehicleInfoEntity;
  Function(bool)? progressHandlerCallback;
  GetIt locator = GetIt.instance;
  late announcementCenterEntity.AnnouncementCenterPayload
      announcementCenterPayload;
  OneAppClient api = APIClientConfig.oneAppClient;
  List<vehicleInfo.Payload> vehicleListPayload = [];

  Stream<List<VehicleAnnouncementsHelper>>
      get vehicleAnnouncementCarouselCardList =>
          _vehicleAnnouncementCarouselCardList.stream;
  final _vehicleAnnouncementCarouselCardList =
      BehaviorSubject<List<VehicleAnnouncementsHelper>>();

  Stream<List<VehicleInsureConnectHelper>>
      get vehicleInsureConnectCarouselCardList =>
          _vehicleInsureConnectCarouselCardList.stream;
  final _vehicleInsureConnectCarouselCardList =
      BehaviorSubject<List<VehicleInsureConnectHelper>>();

  final _marketingConsent = BehaviorSubject<EligibleConsents>();

  Stream<EligibleConsents> get marketingConsent => _marketingConsent.stream;

  marketingConsentEntity.Payload? marketingConsentPayload;
  List<announcementCenterEntity.AnnouncementCenterPayload>
      announcementPayloadList = [];

  final _announcementListPayLoad = BehaviorSubject<
      List<announcementCenterEntity.AnnouncementCenterPayload>>();

  Stream<List<announcementCenterEntity.AnnouncementCenterPayload>>
      get announcementListPayLoad => _announcementListPayLoad.stream;

  Stream<String?> get profilePicBase64 => _profilePicBase64.stream;
  final _profilePicBase64 = BehaviorSubject<String?>();

  Stream<String?> get userProfileName => _userProfileName.stream;
  final _userProfileName = BehaviorSubject<String?>();

  Stream<bool> get isBiggerFont => _isBiggerFont.stream;
  final _isBiggerFont = BehaviorSubject<bool>();

  Stream<bool?> get accountBadgeStatus => _accountBadgeStatus.stream;
  final _accountBadgeStatus = BehaviorSubject<bool?>();

  Future<void> init(Function(bool) progressHandler) async {
    progressHandlerCallback = progressHandler;
    String? vin = Global.getInstance().vin;

    getProfilePicture().then((value) async {
      if (!_profilePicBase64.isClosed) {
        _profilePicBase64.sink.add(value);
      }
    });
    getProfileName().then((value) async {
      if (!_userProfileName.isClosed) {
        _userProfileName.sink.add(value);
      }
    });

    try {
      _isBiggerFont.sink.add(Global.getInstance().isBiggerFont);
      vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
      accountGridItem = VehicleAccountGridHelper(0, OneAppString.of().account,
          OneAppString.of().accountSubTitle, accountGridItemIcon);
      appSuiteGridItem = VehicleAccountGridHelper(1, OneAppString.of().appSuite,
          OneAppString.of().appSuiteSubTitle, placeHolderImage);
      vehicleAccountGridHelperList.clear();
      if (accountGridItem != null) {
        vehicleAccountGridHelperList.add(accountGridItem!);
      }
      if (vehicleInfoEntity != null) {
        bool isXcappEnabled =
            isFeatureEnabled(XCAPP, vehicleInfoEntity!.features);
        if (isXcappEnabled &&
            (Global.getInstance().appBrand == vehicleInfoEntity!.brand) &&
            appSuiteGridItem != null) {
          vehicleAccountGridHelperList.add(appSuiteGridItem!);
        }
      }
      _vehicleAccountGridList.sink.add(vehicleAccountGridHelperList);

      if (vehicleInfoEntity != null) brand = vehicleInfoEntity!.brand;
      if (!_isNativeView) {
        fetchAnnouncementsCards();
        fetchMarketingBannersAndConsent();
      }
      updateBadgeStatus(Global.getInstance().newNotification);
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_VEHI);
    }
  }

  void setBiggerFont(bool isBiggerFont) {
    Global.getInstance().isBiggerFont = isBiggerFont;
    _isBiggerFont.sink.add(isBiggerFont);
  }

  bool checkBiggerFontEnabled() {
    bool isBiggerFontEnabled = Global.getInstance().isBiggerFontEnabled;
    return isBiggerFontEnabled;
  }

  void fetchMarketingBannersAndConsent() {
    //to show marketing banners
    if (vehicleInfoEntity != null) {
      bool isMarketingConsentEnabled =
          isFeatureEnabled(MARKETING_CONSENT, vehicleInfoEntity!.features);
      if (isMarketingConsentEnabled) {
        fetchMarketingConsent();
      }
    }
  }

  void fetchMarketingConsent() {
    locator.isReady<marketingConsentEntity.Payload>().then((value) {
      marketingConsentPayload = null;
      try {
        marketingConsentPayload = locator<marketingConsentEntity.Payload>();
      } catch (e) {}
      if (marketingConsentPayload != null) {
        if (marketingConsentPayload!.eligibleConsents != null) {
          if (marketingConsentPayload!
                      .eligibleConsents![0].alreadyAcknowledged !=
                  true &&
              marketingConsentPayload!.eligibleConsents![0].consentType ==
                  "marketing") {
            _marketingConsent.sink
                .add(marketingConsentPayload!.eligibleConsents![0]);
          }
        }
      }
    });
  }

  // Fetch announcements data
  Future<void> fetchAnnouncementsCards() async {
    vehicleListPayload = [];
    try {
      vehicleListPayload = locator<List<vehicleInfo.Payload>>();
    } catch (e) {}
    if (vehicleListPayload.isNotEmpty) {
      for (var i = 0; i < vehicleListPayload.length; i++) {
        announcementCenterEntity.AnnouncementCenterPayload announcementPayload =
            await VehicleRepo().fetchVehicleAlerts(vehicleListPayload[i]);
        if (announcementPayload.messages != null &&
            announcementPayload.messages!.isNotEmpty) {
          announcementPayloadList.add(announcementPayload);
        }
      }
    }
    if (!_announcementListPayLoad.isClosed) {
      _announcementListPayLoad.sink.add(announcementPayloadList);
    }
  }

  void goNative() {
    _isNativeView = true;
  }

  // Convert the data into new model to show them in an carousel card
  void formAnnouncementsCarouselCard() {
    List<VehicleAnnouncementsHelper> cardsList = <VehicleAnnouncementsHelper>[];
    if (announcementCenterPayload.messages != null) {
      for (final announcementCenterEntity.Messages data
          in announcementCenterPayload.messages!) {
        cardsList.add(VehicleAnnouncementsHelper(
            iconPath: data.image,
            title: data.title,
            subTitle: data.cardTitle,
            description: data.cardMessage,
            buttonText: data.moreInfoText,
            isNetworkImage: true,
            cta: data.cta));
      }
    }
    _vehicleAnnouncementCarouselCardList.sink.add(cardsList);
  }

  void updateBadgeStatus(bool? status) {
    if (!_accountBadgeStatus.isClosed) {
      _accountBadgeStatus.add(status);
    }
  }

  @override
  void dispose() {
    _vehicleAnnouncementCarouselCardList.close();
    _vehicleAccountGridList.close();
    _marketingConsent.close();
    _vehicleInsureConnectCarouselCardList.close();
    _announcementListPayLoad.close();
    _profilePicBase64.close();
    _userProfileName.close();
    _isBiggerFont.close();
  }
}
