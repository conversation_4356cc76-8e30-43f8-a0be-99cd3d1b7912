// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_common/widget/dialog/bottom_dynamic_confirmation_dialog.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_evs_connector_Entity.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';

// Project imports:
import '../../../log/vehicle_analytic_event.dart';
import 'vehicle_search_charge_station_location_bloc.dart';

class VehicleSearchChargeStationLocationConnectorsCard extends StatefulWidget {
  final Station selectedChargeStation;
  final VehicleSearchChargeStationLocationBloc? bloc;
  final int? selectedIndex;
  final bool? isWalletFeatureEnabled;

  const VehicleSearchChargeStationLocationConnectorsCard(
      {Key? key,
      required this.selectedChargeStation,
      required this.bloc,
      required this.selectedIndex,
      required this.isWalletFeatureEnabled})
      : super(key: key);

  @override
  _VehicleSearchChargeStationLocationConnectorsCardState createState() =>
      _VehicleSearchChargeStationLocationConnectorsCardState();
}

class _VehicleSearchChargeStationLocationConnectorsCardState
    extends State<VehicleSearchChargeStationLocationConnectorsCard> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  List<EvEvsConnectorEntity>? _connectors;
  final dialog = BottomDynamicConfirmationDialog();

  @override
  void initState() {
    super.initState();
    _connectors = widget.selectedChargeStation.evEvsConnectorsDetail;
  }

  @override
  Widget build(BuildContext context) {
    final coordinates = widget.selectedChargeStation.geometry.coordinates;
    final hasLocation = coordinates.length > 1;
    final hasPlaceId = widget.selectedChargeStation.placeId != null;
    final isFavourite = widget.selectedChargeStation.isFavourite;
    Icon favouriteIcon = isFavourite
        ? Icon(
            Icons.favorite,
            color: _colorUtil.secondary01,
          )
        : Icon(
            Icons.favorite_border_outlined,
            color: Colors.grey,
          );

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        widget.selectedChargeStation.isPartner(widget.isWalletFeatureEnabled!)
            ? Flexible(
                child: Container(
                  padding: EdgeInsets.only(top: 10.h),
                  height: 50.h,
                  child: ListView.builder(
                    itemCount: _connectors!.length,
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) => Padding(
                      padding: EdgeInsets.only(right: 8.w),
                      child: Chip(
                        backgroundColor: _colorUtil.success02,
                        label: RichText(
                          text: TextSpan(
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.callout1, _colorUtil.button02a),
                            children: [
                              TextSpan(
                                  text:
                                      '${_connectors![index].active}/${_connectors![index].total}',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold)),
                              TextSpan(
                                  text: ' ${_connectors![index].friendlyName}'),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              )
            : Flexible(
                child: Container(
                    padding: EdgeInsets.only(top: 10.h),
                    height: 50.h,
                    child: Padding(
                        padding: EdgeInsets.only(top: 5.h),
                        child: Text(OneAppString.of().nonPartnerStationText,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.callout1,
                                _colorUtil.tertiary07))))),
        StreamBuilder<bool>(
          stream: widget.bloc!.isMyDestinationEnabled,
          builder: (context, snapshot) {
            final isDestinationsEnabled = (snapshot.hasData && snapshot.data!);
            return Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: (isDestinationsEnabled)
                  ? MainAxisAlignment.spaceEvenly
                  : MainAxisAlignment.end,
              children: [
                if (isDestinationsEnabled)
                  IconButton(
                    onPressed: () async {
                      _showSendToCarInProgress();
                      widget.selectedChargeStation.usePlaceId = false;
                      final result = await sendToCar(
                        widget.selectedChargeStation.poiText,
                      );
                      if (result != null && result.outcome == 'success') {
                        FireBaseAnalyticsLogger.logMarketingGroupEvent(
                          VehicleAnalyticsEvent.VEHICLE_EV_PUB_NAV_GROUP,
                          childEventName: VehicleAnalyticsEvent
                              .VEHICLE_EV_PUB_SENDTOCAR_LIST_SUCCESS,
                        );
                        _showSendToCarSuccess();
                      } else {
                        FireBaseAnalyticsLogger.logMarketingGroupEvent(
                          VehicleAnalyticsEvent.VEHICLE_EV_PUB_NAV_GROUP,
                          childEventName: VehicleAnalyticsEvent
                              .VEHICLE_EV_PUB_SENDTOCAR_LIST_FAILURE,
                        );
                        _showSendToCarError();
                      }
                    },
                    icon: SvgPicture.asset(lexusCarIcon,
                        colorFilter: ColorFilter.mode(
                          _colorUtil.tertiary00,
                          BlendMode.srcIn,
                        ),
                        width: 20.w,
                        height: 20.h),
                    tooltip: OneAppString.of().sendToCar,
                  ),
                IconButton(
                  onPressed: (!hasLocation)
                      ? null
                      : () {
                          final longitude = coordinates[0];
                          final latitude = coordinates[1];
                          final displayName = widget.selectedChargeStation.name;
                          FireBaseAnalyticsLogger.logMarketingGroupEvent(
                            VehicleAnalyticsEvent.VEHICLE_EV_PUB_NAV_GROUP,
                            childEventName: VehicleAnalyticsEvent
                                .VEHICLE_EV_PUB_DIRECTIONS_LIST,
                          );

                          openMap(longitude, latitude, address: displayName);
                        },
                  icon: SvgPicture.asset(
                    directionsIcon,
                    colorFilter: ColorFilter.mode(
                      _colorUtil.tertiary00,
                      BlendMode.srcIn,
                    ),
                    width: 25.h,
                    height: 25.h,
                  ),
                  color: _colorUtil.tertiary00,
                  tooltip: OneAppString.of().directions,
                ),
                if (isDestinationsEnabled)
                  (hasPlaceId)
                      ? IconButton(
                          onPressed: () {
                            widget.bloc!.toggleFavouriteStation(
                                selectedStation: widget.selectedChargeStation,
                                bottomSheetId: 1);
                          },
                          icon: favouriteIcon,
                          tooltip: OneAppString.of().add_to_favorite,
                        )
                      : SizedBox(
                          width: 50.w,
                          height: 50.h,
                        ),
              ],
            );
          },
        ),
      ],
    );
  }

  void _showSendToCarInProgress() {
    dialog.updateContent(
      headingText: OneAppString.of().sendToCarTitle,
      subHeadingText: OneAppString.of().sendToCarMessage,
      cancelText: OneAppString.of().cancel,
      showSpinner: true,
    );
    dialog.showDialog(context, () {}, () {});
  }

  void _showSendToCarSuccess() {
    dialog.updateContent(
      headingText: OneAppString.of().directionsSentTitle,
      subHeadingText: OneAppString.of().directionsSentMessage,
      confirmationText: OneAppString.of().commonOK,
    );
  }

  void _showSendToCarError() {
    dialog.updateContent(
      iconPath: chargeAlertIcon,
      headingText: OneAppString.of().error,
      subHeadingText: OneAppString.of().directionsSentError,
      confirmationText: OneAppString.of().commonOK,
    );
  }
}
