// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';

// Project imports:
import '../../ev_common/evgo_complimentary_alert_util.dart';

class EVGOChargePointTermsAndConditionPage extends StatelessWidget {
  final Function(bool result) callBackFunction;

  EVGOChargePointTermsAndConditionPage({required this.callBackFunction});

  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SwipeBarIcon(),
        bottomSheetAppBar(
          OneAppString.of().chargePoint,
          elevation: 0,
          backgroundColor: Colors.transparent,
          onBackPressed: () {},
          leadingWidget: IconButton(
              icon: CircleAvatar(
                backgroundColor: _colorUtil.button05b,
                radius: 48.r,
                child: Icon(
                  Icons.chevron_left,
                  color: ThemeConfig.current().colorUtil.button02a,
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              }),
        ),
        Container(
            child: Padding(
          padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 20.h),
          child: Column(children: [
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: EVGOComplimentaryUtil.evgoCPTermsContent,
                    style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1,
                      _colorUtil.tertiary05,
                    ),
                  ),
                  TextSpan(
                    text: EVGOComplimentaryUtil.evgoPrivacyTitle,
                    style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout2,
                      _colorUtil.tertiary03,
                    ),
                    recognizer: TapGestureRecognizer()..onTap = () {},
                  ),
                  TextSpan(
                    text: EVGOComplimentaryUtil.evgoMidContent,
                    style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1,
                      _colorUtil.tertiary05,
                    ),
                  ),
                  TextSpan(
                    text: EVGOComplimentaryUtil.evgoTermsTitle,
                    style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout2,
                      _colorUtil.tertiary03,
                    ),
                    recognizer: TapGestureRecognizer()..onTap = () {},
                  ),
                  TextSpan(
                    text: EVGOComplimentaryUtil.evgoEndContent,
                    style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1,
                      _colorUtil.tertiary05,
                    ),
                  ),
                ],
              ),
            ),
            _buttonLayout(context)
          ]),
        ))
      ],
    );
  }

  Widget _buttonLayout(BuildContext context) {
    return Container(
      //// issue fix agree button overlapping
      padding: EdgeInsets.only(top: 40.h, bottom: 10.h),
      child: Column(
        children: [
          CustomDefaultButton(
            press: () {
              Navigator.pop(context);
              callBackFunction(false);
            },
            text: OneAppString.of().decline,
            buttonTextColor: _colorUtil.tertiary00,
            backgroundColor: Colors.transparent,
          ),
          Padding(
            //// issue fix agree button overlapping
            padding: const EdgeInsets.only(top: 10),
            child: CustomDefaultButton(
                text: OneAppString.of().agree,
                press: () {
                  Navigator.pop(context);
                  callBackFunction(true);
                }),
          )
        ],
      ),
    );
  }
}
