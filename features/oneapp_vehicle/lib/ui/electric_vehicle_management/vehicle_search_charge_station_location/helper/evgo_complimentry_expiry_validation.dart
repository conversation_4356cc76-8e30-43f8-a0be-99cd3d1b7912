// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ev_module/charge_info/blocs/partner_details_Info_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';

// Project imports:
import '../../../../log/vehicle_analytic_event.dart';
import '../../../vehicle_finance/vehicle_finance_page.dart';
import '../../ev_common/ev_alert_popup.dart';
import '../../ev_common/ev_enum_const_util.dart';
import '../../ev_common/evgo_complimentary_alert_util.dart';
import '../ev_driver_account_enrollment/ev_driver_account_enrollment_page.dart';
import 'evgo_charge_point_terms_and_condition.dart';

class EVGOComplimentaryExpiryValidation extends StatefulWidget {
  final Station? selectedChargeStation;
  final bool? evgoExpired;
  final Function? acceptTermsCallBackFunction;
  final Function? callBackFunction;

  EVGOComplimentaryExpiryValidation(
      {Key? key,
      this.selectedChargeStation,
      this.acceptTermsCallBackFunction,
      this.callBackFunction,
      this.evgoExpired})
      : super(key: key);

  @override
  State<EVGOComplimentaryExpiryValidation> createState() =>
      _EVGOComplimentaryExpiryValidationState();
}

class _EVGOComplimentaryExpiryValidationState
    extends State<EVGOComplimentaryExpiryValidation> {
  PartnerDetailsInfoBloc _bloc = PartnerDetailsInfoBloc();
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () async {
      await _bloc.fetchPartnerInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<PartnerDetails?>(
        stream: _bloc.partnerDetails,
        builder: (context, snapshot) {
          if (snapshot.hasData &&
              snapshot.connectionState == ConnectionState.active) {
            return _checkIfEVgoComplimentaryExpired(snapshot.data);
          } else {
            return Container();
          }
        });
  }

  Widget _checkIfEVgoComplimentaryExpired(PartnerDetails? response) {
    try {
      if (response != null) {
        ///Get wallet setup status
        bool isWalletSetupNotDone = !response.isWalletSetupDone;
        bool isChargePointRegistered = response.chargePoint.isRegistered;
        bool evgoExpired = response.evgoExpired;
        bool evgoExpiringSoon = response.evgoExpiredSoon;

        if (evgoExpiringSoon || evgoExpired) {
          /// Scenario 1.4: No Wallet Setup & Not Registered with ChargePoint
          if (isWalletSetupNotDone == true &&
              isChargePointRegistered == false &&
              Global.getInstance().showEVGoComplementaryPopUp1Point4 == true) {
            Global.getInstance().showEVGoComplementaryPopUp1Point4 = false;
            Future.delayed(Duration.zero, () {
              showChargingAlert(
                  title: evgoExpiringSoon
                      ? EVGOComplimentaryUtil
                          .evgoNoWalletNoChargeEndingSoonTitle
                      : EVGOComplimentaryUtil.evgoNoWalletNoChargeEndedTitle,
                  description: evgoExpiringSoon
                      ? EVGOComplimentaryUtil
                          .evgoNoWalletNoChargeEndingSoonContent
                      : EVGOComplimentaryUtil.evgoNoWalletNoChargeEndedContent,
                  status: false,
                  primaryButtonText: EVGOComplimentaryUtil.evgoSetupWalletCta,
                  additionalButtonText: evgoExpiringSoon
                      ? EVGOComplimentaryUtil.evgoSkipCta
                      : EVGOComplimentaryUtil.evgoEmptyString,
                  additionalButtonPressed: () {
                    Navigator.pop(context);
                  },
                  primaryButtonPressed: () {
                    _showSetUpWalletScreen(navigateToChargePointFlow: true);
                    FireBaseAnalyticsLogger.logMarketingGroupEvent(
                      VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                      childEventName:
                          VehicleAnalyticsEvent.VEHICLE_EV_WALLET_SET_UP,
                    );
                  });
            });
          }

          /// Scenario 1.1: Wallet Setup & Not Registered with ChargePoint
          else if (isWalletSetupNotDone == false &&
              isChargePointRegistered == false &&
              Global.getInstance().showEVGoComplementaryPopUp1Point1 == true) {
            Global.getInstance().showEVGoComplementaryPopUp1Point1 = false;
            Future.delayed(Duration.zero, () {
              showChargingAlert(
                  title: evgoExpiringSoon
                      ? EVGOComplimentaryUtil.evgoWalletNoChargeEndingSoonTitle
                      : EVGOComplimentaryUtil.evgoWalletNoChargeEndedTitle,
                  description: evgoExpiringSoon
                      ? EVGOComplimentaryUtil
                          .evgoWalletNoChargeEndingSoonContent
                      : EVGOComplimentaryUtil.evgoWalletNoChargeEndedContent,
                  status: false,
                  primaryButtonText:
                      EVGOComplimentaryUtil.evgoRegisterWithChargePointCta,
                  additionalButtonText: evgoExpiringSoon
                      ? EVGOComplimentaryUtil.evgoSkipCta
                      : EVGOComplimentaryUtil.evgoGoBackCta,
                  primaryButtonPressed: () {
                    Navigator.pop(context);
                    _showAcceptTermsAndConditionScreen();
                  },
                  additionalButtonPressed: () {
                    Navigator.pop(context);
                  });
            });
          }

          /// Scenario 1.3: No Wallet Setup & Registered with ChargePoint
          else if (isWalletSetupNotDone == true &&
              isChargePointRegistered == true &&
              Global.getInstance().showEVGoComplementaryPopUp1Point3 == true) {
            Global.getInstance().showEVGoComplementaryPopUp1Point3 = false;
            Future.delayed(Duration.zero, () {
              showChargingAlert(
                  title: evgoExpiringSoon
                      ? EVGOComplimentaryUtil.evgoNoWalletChargeEndingSoonTitle
                      : EVGOComplimentaryUtil.evgoNoWalletChargeEndedTitle,
                  description: evgoExpiringSoon
                      ? EVGOComplimentaryUtil
                          .evgoNoWalletChargeEndingSoonContent
                      : EVGOComplimentaryUtil.evgoNoWalletChargeEndedContent,
                  status: false,
                  primaryButtonText: EVGOComplimentaryUtil.evgoSetupWalletCta,
                  additionalButtonText: evgoExpiringSoon
                      ? EVGOComplimentaryUtil.evgoSkipCta
                      : EVGOComplimentaryUtil.evgoGoBackCta,
                  primaryButtonPressed: () {
                    FireBaseAnalyticsLogger.logMarketingGroupEvent(
                      VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                      childEventName:
                          VehicleAnalyticsEvent.VEHICLE_EV_WALLET_SET_UP,
                    );
                    _showSetUpWalletScreen(navigateToChargePointFlow: false);
                  },
                  additionalButtonPressed: () {
                    Navigator.pop(context);
                  });
            });
          }

          /// Scenario 1.2: Wallet Setup & Registered with ChargePoint
          else if (Global.getInstance().showEVGoComplementaryPopUp1Point2 ==
                  true &&
              isWalletSetupNotDone == false &&
              isChargePointRegistered == true) {
            Global.getInstance().showEVGoComplementaryPopUp1Point2 = false;
            Future.delayed(Duration.zero, () {
              showChargingAlert(
                  title: evgoExpiringSoon
                      ? EVGOComplimentaryUtil.evgoWalletChargeEndingSoonTitle
                      : EVGOComplimentaryUtil.evgoWalletChargeEndedTitle,
                  description: evgoExpiringSoon
                      ? EVGOComplimentaryUtil.evgoWalletChargeEndingSoonContent
                      : EVGOComplimentaryUtil.evgoWalletChargeEndedContent,
                  status: true,
                  primaryButtonText: EVGOComplimentaryUtil.evgoOkayCta,
                  primaryButtonPressed: () {
                    Navigator.pop(context);
                  });
            });
          }
        }
      }
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString());
    }
    return Container();
  }

  Future<dynamic> showChargingAlert(
      {required String title,
      required String description,
      required bool status,
      required String primaryButtonText,
      String? additionalButtonText,
      required Null Function() primaryButtonPressed,
      Null Function()? additionalButtonPressed}) {
    return showMaterialModalBottomSheet(
      expand: false,
      context: context,
      isDismissible: false,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
      barrierColor: ThemeConfig.current().colorUtil.barrierColor,
      builder: (context) {
        return Container(
          height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_65,
          child: EVAlertPopUpState(
            success: status,
            title: title,
            description: description,
            primaryButtonText: primaryButtonText,
            additionalButtonText: additionalButtonText,
            additionalButtonPressed: additionalButtonPressed,
            primaryButtonPressed: primaryButtonPressed,
          ),
        );
      },
    );
  }

  _showSetUpWalletScreen<bool>({bool? navigateToChargePointFlow}) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(
                child: VehicleFinancePage(
              fromChargeStation: true,
              showPassDueNotification: (showNotification) {},
            )),
          ],
        ),
      ),
    ).then((value) => {
          if (value == true && navigateToChargePointFlow == true)
            {Navigator.of(context).pop(), _showAcceptTermsAndConditionScreen()}
        });
  }

  _showAcceptTermsAndConditionScreen<bool>() {
    return showMaterialModalBottomSheet(
      context: context,
      expand: false,
      isDismissible: false,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => Container(
        height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_65,
        child: SingleChildScrollView(
            child: EVGOChargePointTermsAndConditionPage(
                callBackFunction: acceptTermsCallBackFunction)),
      ),
    );
  }

  void acceptTermsCallBackFunction(bool result) {
    if (result == true) {
      _findActions(EV_SCREENS.ENROLLMENT_SCREEN,
          chargeStationLocationInfo: widget.selectedChargeStation);
    }
  }

  void _findActions(EV_SCREENS ev_screens,
      {Station? chargeStationLocationInfo,
      String? errorMessage = EVGOComplimentaryUtil.evgoEmptyString}) {
    switch (ev_screens) {
      case EV_SCREENS.ENROLLMENT_SCREEN:
        _showEnrollmentScreen(partnerName: OneAppString.of().chargePoint);
        break;
      case EV_SCREENS.SETUP_WALLET:
        _showSetUpWalletScreen().then((value) => _bloc.fetchPartnerInfo());
        break;
      case EV_SCREENS.TERMS_AND_CONDITION:
        _showAcceptTermsAndConditionScreen();
        break;
      default:
        break;
    }
  }

  _showEnrollmentScreen({String? partnerName}) {
    return showModalBottomSheet(
        context: context,
        isDismissible: true,
        useRootNavigator: true,
        backgroundColor: _colorUtil.tertiary12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(CARD_RADIUS),
          ),
        ),
        builder: (context) => SafeArea(
              child: Container(
                height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_65,
                child: OneAppScaffold(
                  resizeToAvoidBottomInset: true,
                  body: Padding(
                    padding: EdgeInsets.only(
                      top: 10.h,
                    ),
                    child: EVDriverAccountEnrollmentPage(
                        evgoExpired: widget.evgoExpired,
                        continueCallback: _enrollCallBackFunction,
                        screenAction: _screenAction,
                        partnerName: partnerName),
                  ),
                ),
              ),
            ));
  }

  _enrollCallBackFunction() {
    _bloc.fetchPartnerInfo();
  }

  void _screenAction(EV_SCREENS val, {String? errorMessage}) {
    _findActions(val,
        chargeStationLocationInfo: widget.selectedChargeStation!,
        errorMessage: errorMessage);
  }
}
