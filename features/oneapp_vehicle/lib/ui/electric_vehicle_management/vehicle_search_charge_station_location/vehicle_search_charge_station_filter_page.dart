// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/entity/filterModel.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/widget/MultiSelelct/multi_select_page.dart';

// Project imports:
import '../../common/chip_card.dart';
import 'vehicle_search_charge_station_location_bloc.dart';
import 'vehicle_search_charge_station_location_util.dart';

class VehicleSearchChargeStationFilterPage extends StatefulWidget {
  const VehicleSearchChargeStationFilterPage({Key? key, required this.bloc})
      : super(key: key);
  final VehicleSearchChargeStationLocationBloc bloc;

  @override
  _VehicleSearchChargeStationFilterPageState createState() =>
      _VehicleSearchChargeStationFilterPageState();
}

class _VehicleSearchChargeStationFilterPageState
    extends State<VehicleSearchChargeStationFilterPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  ScrollController _listcontroller = ScrollController();

  void scrollToBottom() {
    final bottomOffset = _listcontroller.position.maxScrollExtent;
    _listcontroller.animateTo(
      bottomOffset,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeIn,
    );
  }

  void scrollToTop() {
    final bottomOffset = _listcontroller.position.minScrollExtent;
    _listcontroller.animateTo(
      bottomOffset,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeIn,
    );
  }

  void dispose() {
    super.dispose();
    _listcontroller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 50.h,
        child: ListView(
            shrinkWrap: true,
            controller: _listcontroller,
            physics: BouncingScrollPhysics(),
            scrollDirection: Axis.horizontal,
            children: [
              StreamBuilder<FilterHelperClass?>(
                  stream: widget.bloc.filteredPartnerItems,
                  builder: (context, snapshot) {
                    return snapshot.hasData
                        ? InkWell(
                            onTap: () {
                              scrollToBottom();
                              _showPartnerMultiSelect();
                            },
                            child: Padding(
                                padding: EdgeInsets.only(left: 10.w),
                                child: ChipCard(
                                  label: snapshot.data!.name,
                                  labelStyle: TextStyleExtension()
                                      .newStyleWithColor(
                                          _textStyleUtil.callout2,
                                          snapshot.data!.color),
                                  backgroundColor:
                                      snapshot.data!.backgroundColor,
                                  isFilterAvailable: true,
                                  iconColor: snapshot.data!.iconColor,
                                  selectedFilter: snapshot.data?.length ?? 0,
                                )),
                          )
                        : Container();
                  }),
              StreamBuilder<FilterHelperClass?>(
                  stream: widget.bloc.filteredConnectorNames,
                  builder: (context, snapshot) {
                    return snapshot.hasData
                        ? InkWell(
                            onTap: () {
                              _showConnectorMultiSelect();
                            },
                            child: ChipCard(
                              label: snapshot.data!.name,
                              labelStyle: TextStyleExtension()
                                  .newStyleWithColor(_textStyleUtil.callout2,
                                      snapshot.data!.color),
                              backgroundColor: snapshot.data!.backgroundColor,
                              isFilterAvailable: true,
                              iconColor: snapshot.data!.iconColor,
                              selectedFilter: snapshot.data?.length ?? 0,
                            ))
                        : Container();
                  }),
              StreamBuilder<bool>(
                  stream: widget.bloc.isFavouriteStationFilterDisabled,
                  builder: (context, snapshot) {
                    return InkWell(
                      onTap: () {
                        scrollToTop();
                        widget.bloc.favouriteFilter();
                      },
                      child: ChipCard(
                          label: OneAppString.of().FavouriteFilter,
                          labelStyle: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.callout2,
                              (snapshot.data != null && snapshot.data!)
                                  ? _colorUtil.tertiary15
                                  : _colorUtil.tertiary05),
                          backgroundColor: (snapshot.hasData && snapshot.data!)
                              ? _colorUtil.tertiary00
                              : _colorUtil.button05b),
                    );
                  }),
              InkWell(
                  onTap: () {
                    widget.bloc.resetFilters();
                  },
                  child: ChipCard(
                      label: OneAppString.of().clearAll,
                      labelStyle: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout2, _colorUtil.tertiary05),
                      backgroundColor: _colorUtil.tile01,
                      isBorderRequired: true)),
            ]));
  }

  void _showPartnerMultiSelect() {
    showMaterialModalBottomSheet(
      expand: false,
      context: context,
      backgroundColor: _colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      isDismissible: false,
      useRootNavigator: false,
      enableDrag: false,
      builder: (context) => Container(
          height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_50,
          child: MultiSelectPage(
            filterList: widget.bloc.partnerlist,
            selectedList: selectedPartnerList,
          )),
    );
  }

  void _showConnectorMultiSelect() {
    showMaterialModalBottomSheet(
      expand: false,
      context: context,
      backgroundColor: _colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      isDismissible: false,
      useRootNavigator: false,
      enableDrag: false,
      builder: (context) => Container(
          height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_35,
          child: MultiSelectPage(
            filterList: widget.bloc.connectorList,
            selectedList: selectedConnectorsList,
          )),
    );
  }

  void selectedPartnerList(List<FilterContentModel> selectedPartnerList) {
    List<String> val = [];
    if (selectedPartnerList.isNotEmpty) {
      widget.bloc.partnerlist = selectedPartnerList;
      selectedPartnerList.forEach((element) {
        if (element.isSelected) {
          debugPrint(element.itemName);
          val.add(element.itemName);
        }
      });
      widget.bloc.onFilterPartnerItemsChange(val);
    }
  }

  void selectedConnectorsList(List<FilterContentModel> selectedConnectorList) {
    List<String> val = [];
    List<String> names = [];
    if (selectedConnectorList.isNotEmpty) {
      widget.bloc.connectorList = selectedConnectorList;
      selectedConnectorList.forEach((element) {
        if (element.isSelected) {
          val.add(element.itemName);
          names.add(element.friendlyName);
        }
      });
      debugPrint(val.toString());
      debugPrint(names.toString());
    }
    widget.bloc.onFilterConnectorItemsChange(val, names);
  }
}
