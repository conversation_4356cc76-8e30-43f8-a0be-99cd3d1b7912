// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';

// Project imports:
import 'vehicle_search_charge_station_location_bloc.dart';
import 'vehicle_search_charge_station_location_connectors_card.dart';
import 'vehicle_search_charge_station_location_util.dart';

class VehicleSearchChargeStationLocationCard extends StatefulWidget {
  VehicleSearchChargeStationLocationCard({
    Key? key,
    required this.selectedHit,
    this.bloc,
    this.selectedIndex,
    this.isWalletFeatureEnabled,
  }) : super(key: key);
  final Station selectedHit;
  final int? selectedIndex;
  final VehicleSearchChargeStationLocationBloc? bloc;
  final bool? isWalletFeatureEnabled;

  @override
  _VehicleSearchChargeStationLocationCardState createState() =>
      _VehicleSearchChargeStationLocationCardState();
}

class _VehicleSearchChargeStationLocationCardState
    extends State<VehicleSearchChargeStationLocationCard> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  late Station _selectedChargeStation;
  RegExp reg = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
  String Function(Match) mathFunc = (Match match) => '${match[1]},';

  @override
  void initState() {
    super.initState();
    this._selectedChargeStation = widget.selectedHit;
  }

  @override
  Widget build(BuildContext context) {
    final String partnerName =
        VehicleSearchChargeStationLocationUtil.getPartnerTypeText(
            _selectedChargeStation);

    return Padding(
      padding: EdgeInsets.only(top: 8.h, bottom: 12.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// Station Details in List
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: partnerName.isNotEmpty,
                      child: Padding(
                        padding: EdgeInsets.only(top: 8.h, bottom: 5.h),
                        child: Row(
                          children: [
                            Text(partnerName,
                                style: TextStyleExtension().newStyleWithColor(
                                    _textStyleUtil.body2,
                                    _colorUtil.tertiary03))
                          ],
                        ),
                      ),
                    ),
                    Visibility(
                      visible: _selectedChargeStation.name.isNotEmpty,
                      child: Padding(
                        padding: EdgeInsets.only(top: 8.h, bottom: 2.h),
                        child: Wrap(
                          children: [
                            Text(_selectedChargeStation.name,
                                style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout1,
                                  _colorUtil.tertiary03,
                                ))
                          ],
                        ),
                      ),
                    ),
                    Text(
                      '${_selectedChargeStation.address}, ${_selectedChargeStation.city}',
                      maxLines: 1,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1, _colorUtil.tertiary05),
                    ),
                    Visibility(
                      visible:
                          _selectedChargeStation.openingTimes.timing != null &&
                              _selectedChargeStation
                                  .isPartner(widget.isWalletFeatureEnabled!),
                      child: Text(
                        _selectedChargeStation.openingTimes.timing != null
                            ? _selectedChargeStation.openingTimes.timing!
                            : "",
                        maxLines: 3,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1, _colorUtil.secondary01),
                        textAlign: TextAlign.start,
                      ),
                    ),
                  ],
                ),
              ),

              /// Distance In List
              Padding(
                padding: EdgeInsets.only(top: 8.h),
                child: Text(
                  (_selectedChargeStation.distance != null)
                      ? _selectedChargeStation.distance
                              .toString()
                              .replaceAllMapped(reg, mathFunc) +
                          " ${OneAppString.of().short_miles}"
                      : " ${OneAppString.of().short_miles}",
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1, _colorUtil.tertiary05),
                ),
              ),
            ],
          ),

          /// Connectors Detail Row Class
          VehicleSearchChargeStationLocationConnectorsCard(
            selectedChargeStation: _selectedChargeStation,
            bloc: widget.bloc,
            selectedIndex: widget.selectedIndex,
            isWalletFeatureEnabled: widget.isWalletFeatureEnabled,
          ),
        ],
      ),
    );
  }
}
