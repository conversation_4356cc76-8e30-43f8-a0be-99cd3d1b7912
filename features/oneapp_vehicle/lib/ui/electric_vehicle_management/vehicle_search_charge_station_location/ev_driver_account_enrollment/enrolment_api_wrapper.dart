// Flutter imports:

// Package imports:
import 'package:collection/collection.dart' show IterableExtension;
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_driver_existence_validation.dart';
import 'package:oneapp_network_implementation/ev/entity_helper/ev_enrollment_request_body.dart';
import 'package:oneapp_network_implementation/ev/ev_api_client.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/profile_detail_entity.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';

// Project imports:

class EvEnrolmentAPI {
  static _ENROLMENT_RESPONSE ENROLMENT = const _ENROLMENT_RESPONSE();
  static _WALLET_RESPONSE WALLET = const _WALLET_RESPONSE();

  static OneAppClient? get api => APIClientConfig.oneAppClient;

  static EvEnrollmentRequestBody _buildEnrollmentBody(
      Addresses? address,
      Emails email,
      String? evNetwork,
      ProfilePhoneNumber? phoneNumber,
      Customer customer,
      {String? evNetPassword = ""}) {
    return EvEnrollmentRequestBody(
      address1: address?.address,
      address2: address?.address,
      city: address?.city,
      country: address?.country,
      stateCode: address?.state,
      zipCode: address?.zipCode,
      timeZone: currentDateTimeZone(),
      emailAddress: email.emailAddress,
      evNetwork: evNetwork,
      evNetPassword: evNetPassword,
      mobilePhone: phoneNumber?.phoneNumber.toString(),
      firstName: customer.firstName,
      lastName: customer.lastName,
      guid: Global.getInstance().guid,
    );
  }

  static Addresses? getOCPRAddress({required Customer customer}) {
    if (customer.addresses != null && customer.addresses!.length > 1) {
      return customer.addresses!.firstWhereOrNull(
          (element) => element.addressType!.toUpperCase() == "WORK");
    }
    return customer.addresses?.first ?? null;
  }

  static ProfilePhoneNumber? getOCPRPhoneNumber({required Customer customer}) {
    if (customer.phoneNumbers != null && customer.phoneNumbers!.length > 1) {
      ProfilePhoneNumber? profilePhoneNumber = customer.phoneNumbers!
          .firstWhereOrNull(
              (element) => element.phoneType!.toUpperCase() == "MOBILE");
      return profilePhoneNumber;
    }
    return customer.phoneNumbers?.first ?? null;
  }

  static Emails? getOCPREmail({required Customer customer}) {
    if (customer.emails!.length > 1) {
      Emails? email = customer.emails!.firstWhereOrNull(
          (element) => element.emailType!.toUpperCase() == "FORGEROCK");
      return email;
    }
    return customer.emails?.first ?? null;
  }

  static Future<CommonResponse<EvDriverExistenceValidation>>
      callDriverEnrollment({
    required String? evNetwork,
    String? email = '',
    String? password,
  }) async {
    EvEnrollmentRequestBody evEnrollmentRequestBody =
        await EvEnrolmentAPI.getOCPRProfileDetails(evNetwork,
            password: password);
    EVApiClient evApi = APIClientConfig.evApiClient;
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();
    return evApi.driverEnrollment(
      enrollmentRequestBody: evEnrollmentRequestBody,
      make: vehicleInfo?.make ?? '',
      vin: vehicleInfo?.vin ?? '',
    );
  }

  static Future<EvEnrollmentRequestBody> getOCPRProfileDetails(
      String? evNetwork,
      {String? email,
      String? password}) async {
    final commonResponse = await api!.fetchProfileDetails("application/json",
        Global.getInstance().correlationId, Global.getInstance().appBrand);
    late EvEnrollmentRequestBody enrollmentRequestBody;
    final payload = commonResponse.response!.payload!;
    if (payload.customer != null) {
      final customer = payload.customer!;
      final address = getOCPRAddress(customer: customer);
      final phoneNumber = getOCPRPhoneNumber(customer: customer);
      final email = getOCPREmail(customer: customer)!;
      enrollmentRequestBody = _buildEnrollmentBody(
          address, email, evNetwork, phoneNumber, customer,
          evNetPassword: password);
    }
    return enrollmentRequestBody;
  }
}

class _ENROLMENT_RESPONSE {
  const _ENROLMENT_RESPONSE();

  String get DRIVER_ACCOUNT_FOUND => 'found';

  String get DRIVER_ACCOUNT_NOT_FOUND => 'not found';

  String get DRIVER_NOT_FOUND_UPDATE_PROFILE => 'update Profile';

  String get DRIVER_ENROLMENT_ERROR => 'error';

  String getEnrolmentState(
      CommonResponse<EvDriverExistenceValidation> enrollmentResponse) {
    int SUCCESS_CODE = 200;
    int UPDATE_PROFILE_CODE = 406;
    bool successResponseCode =
        enrollmentResponse.response!.messages!.responseCode == SUCCESS_CODE;
    bool failedResponseCode =
        enrollmentResponse.response!.messages!.responseCode ==
            UPDATE_PROFILE_CODE;
    bool successDescription =
        enrollmentResponse.response!.messages!.description!.toUpperCase() ==
            "SUCCESS";
    bool failedDescription =
        enrollmentResponse.response!.messages!.description!.toUpperCase() ==
            "FAILED";
    bool successDriverFoundMessage = (enrollmentResponse
            .response!.messages!.detailedDescription!
            .toLowerCase() ==
        'call was successful and driver found');

    if (successResponseCode &&
        successDescription &&
        successDriverFoundMessage) {
      return DRIVER_ACCOUNT_FOUND;
    } else if (successResponseCode &&
        successDescription &&
        !successDriverFoundMessage) {
      return DRIVER_ACCOUNT_NOT_FOUND;
    } else if (failedResponseCode && failedDescription) {
      return DRIVER_NOT_FOUND_UPDATE_PROFILE;
    } else {
      return DRIVER_ENROLMENT_ERROR;
    }
  }
}

class _WALLET_RESPONSE {
  const _WALLET_RESPONSE();

  String get FOUND => 'found';

  String get NOT_FOUND => 'not found';
}
