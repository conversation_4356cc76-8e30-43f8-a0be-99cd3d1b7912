// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/bottom_error_conformation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_subscription_offer_snippet_helper.dart';

// Project imports:
import '../../../vehicle_account/vehicle_account_page.dart';
import '../../../vehicle_overview/vehicle_announcement/vehicle_snippet_card.dart';
import '../../Evgo_Charge_Station_Screens/evgo_account_Linked_page.dart';
import '../../Evgo_Charge_Station_Screens/evgo_account_created_page.dart';
import '../../Evgo_Charge_Station_Screens/evgo_forget_password_page.dart';
import '../../ev_common/ev_enum_const_util.dart';
import 'ev_driver_account_enrolment_bloc.dart';

class EVDriverAccountEnrollmentPage extends StatefulWidget {
  final ScrollController? scrollController;
  final Station? chargeStation;
  final bool? evgoExpired;
  final Function() continueCallback;
  final String? partnerName;
  final Function(EV_SCREENS val, {String? errorMessage})? screenAction;

  EVDriverAccountEnrollmentPage({
    Key? key,
    this.scrollController,
    this.chargeStation,
    required this.continueCallback,
    this.partnerName,
    this.evgoExpired,
    this.screenAction,
  }) : super(key: key);

  @override
  _EVDriverAccountEnrollmentPageState createState() =>
      _EVDriverAccountEnrollmentPageState();
}

class _EVDriverAccountEnrollmentPageState
    extends State<EVDriverAccountEnrollmentPage> {
  EVDriverAccountEnrollmentBloc _bloc = EVDriverAccountEnrollmentBloc();

  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  /// Login Form Key For Sign-in From Validation
  final _signinformKey = GlobalKey<FormState>();
  bool _passwordVisible = true;
  TextEditingController _signInPasswordController = TextEditingController();
  String _signInUserPassword = "";
  String? _signInUserMail = Global.getInstance().userEmail;

  /// Registration Form Key For Sign-in From Validation
  final _registerformKey = GlobalKey<FormState>();
  bool _registrationPasswordVisible = true;
  bool _registrationConfrimpasswordVisible = true;

  TextEditingController _registrationPasswordController =
      TextEditingController();
  TextEditingController _registrationConfrimPasswordController =
      TextEditingController();

  String _registrationUserPassword = "";

  void initState() {
    _passwordVisible = false;
    _registrationPasswordVisible = false;
    _registrationConfrimpasswordVisible = false;
    _bloc.init(
        progressHandler: _progressHandlerCallback,
        screenAction: screenActionCallBack,
        selectedChargeStation: widget.chargeStation,
        partnerName: widget.partnerName);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        child: Navigator(
            onGenerateRoute: (_) =>
                MaterialPageRoute(builder: (materialContext) {
                  return Builder(builder: (builderContext) {
                    return StreamBuilder<String?>(
                        stream: _bloc.enrollmentPartnerName,
                        builder: (context, snapshot) {
                          if (snapshot.data == EV_PARTNERS.CHARGE_POINT) {
                            return _chargePointLayout();
                          }
                          if (snapshot.data == EV_PARTNERS.EVGO) {
                            return widget.evgoExpired == true
                                ? _chargePointLayout()
                                : _evgoLayouts();
                          } else {
                            return Container();
                          }
                        });
                  });
                })));
  }

  Widget _chargePointLayout() {
    return Container(
        color: _colorUtil.tertiary15,
        child: SingleChildScrollView(
            controller: widget.scrollController,
            child: StreamBuilder<EnrollmentAccountState>(
                stream: _bloc.accountState,
                builder: (context, snapshot) {
                  switch (snapshot.data) {
                    case EnrollmentAccountState.ACCOUNT_NOT_FOUND:
                      return _chargePointAccountNotFound();

                    case EnrollmentAccountState.ACCOUNT_FOUND:
                      return _chargePointAccountFound();

                    case EnrollmentAccountState.CHARGEPOINT_ACCOUNT_LINKED:
                      return _chargePointAccountLinked();

                    case EnrollmentAccountState.ERROR:
                      return _retryLayout();

                    default:
                      return Container();
                  }
                })));
  }

  Widget _evgoLayouts() {
    return Container(
      color: _colorUtil.tertiary15,
      child: SingleChildScrollView(
        controller: widget.scrollController,
        child: StreamBuilder<EnrollmentAccountState>(
            stream: _bloc.accountState,
            builder: (context, snapshot) {
              switch (snapshot.data) {
                case EnrollmentAccountState.ACCOUNT_FOUND:
                  return _evGoAccountFound();

                case EnrollmentAccountState.LOGIN:
                  return _evGoLogin();

                case EnrollmentAccountState.REGISTER:
                  return _evGoRegister();

                default:
                  return Container();
              }
            }),
      ),
    );
  }

//// Acount Found Changed As per Figma V6
  Widget _chargePointAccountNotFound() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 25.h),
          child: CircleAvatar(
            backgroundColor: _colorUtil.secondary02,
            radius: 25.r,
            child: Icon(
              Icons.check,
              size: 30.h,
              color: ThemeConfig.current().colorUtil.secondary01,
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 30.h),
          child: Text(OneAppString.of().chargePoint,
              style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.subHeadline3,
                _colorUtil.tertiary05,
              )),
        ),
        Padding(
          padding: EdgeInsets.only(top: 40.h, left: 10.w, right: 10.w),
          child: Align(
            alignment: Alignment.center,
            child: Text(
              OneAppString.of().accountNotFoundText,
              style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.callout1,
                _colorUtil.tertiary05,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        SizedBox(height: 20.h),
        Container(
            child: Padding(
          padding: EdgeInsets.only(top: 80.h),
          child: CustomDefaultButton(
            backgroundColor: _colorUtil.button01b,
            buttonTextColor: _colorUtil.button01a,
            text: OneAppString.of().continueText,
            press: () {
              screenActionCallBack(ENROLLMENT_SCREENS.CONTINUE_CALL_BACK);
            },
            borderColor: _colorUtil.button01b,
            horizontalPadding: 22.w,
            verticalPadding: 6.h,
          ),
        ))
      ],
    );
  }

  Widget _chargePointAccountFound() {
    return Padding(
      padding: EdgeInsets.only(top: 15.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 25.h),
            child: CircleAvatar(
              backgroundColor: _colorUtil.secondary02,
              radius: 25.r,
              child: Icon(
                Icons.check,
                size: 30.h,
                color: ThemeConfig.current().colorUtil.secondary01,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 30.h),
            child: Text(OneAppString.of().chargePoint,
                style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.subHeadline3,
                  _colorUtil.tertiary05,
                )),
          ),
          Padding(
            padding: EdgeInsets.only(top: 40.h, left: 10.w, right: 10.w),
            child: Align(
              alignment: Alignment.center,
              child: Text(
                OneAppString.of().chargePointAccountLinkedText,
                style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1,
                  _colorUtil.tertiary05,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Container(
              padding: EdgeInsets.only(top: 80.h),
              child: CustomDefaultButton(
                backgroundColor: _colorUtil.button01b,
                buttonTextColor: _colorUtil.button01a,
                text: OneAppString.of().continueText,
                press: () {
                  /// charge point redirectional url  for login
                  _bloc.chargePointLogin();
                },
                borderColor: _colorUtil.button01b,
                horizontalPadding: 22.w,
                verticalPadding: 6.h,
              ))
        ],
      ),
    );
  }

  Widget _chargePointAccountLinked() {
    return Padding(
      padding: EdgeInsets.only(top: 15.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 25.h),
            child: CircleAvatar(
              backgroundColor: _colorUtil.secondary02,
              radius: 25.r,
              child: Icon(
                Icons.check,
                size: 30.h,
                color: ThemeConfig.current().colorUtil.secondary01,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 30.h),
            child: Text(OneAppString.of().chargePoint,
                style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.subHeadline3,
                  _colorUtil.tertiary05,
                )),
          ),
          Padding(
            padding: EdgeInsets.only(top: 40.h, left: 10.w, right: 10.w),
            child: Align(
              alignment: Alignment.center,
              child: Text(
                OneAppString.of().accountFoundText,
                style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1,
                  _colorUtil.tertiary05,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Container(
              padding: EdgeInsets.only(top: 50.h),
              child: CustomDefaultButton(
                backgroundColor: _colorUtil.button01b,
                buttonTextColor: _colorUtil.button01a,
                text: OneAppString.of().continueText,
                press: () {
                  screenActionCallBack(ENROLLMENT_SCREENS.CONTINUE_CALL_BACK);
                },
                borderColor: _colorUtil.button01b,
                horizontalPadding: 22.w,
                verticalPadding: 6.h,
              ))
        ],
      ),
    );
  }

  Widget _evGoAccountFound() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        bottomSheetAppBar(
          OneAppString.of().evgoHeadingText,
          elevation: 0,
          backgroundColor: Colors.transparent,
          onBackPressed: () {},
          leadingWidget: IconButton(
              icon: Icon(
                Icons.close,
                color: ThemeConfig.current().colorUtil.button02a,
              ),
              onPressed: () {
                Navigator.pop(context);
              }),
        ),
        Padding(
          padding: EdgeInsets.only(top: 30.h, left: 10.w, right: 10.w),
          child: Align(
            alignment: Alignment.center,
            child: Text(
              /// Email Is Shown Using Global Instance
              _signInUserMail!,
              style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.body4,
                _colorUtil.tertiary00,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 30.h, left: 10.w, right: 10.w),
          child: Align(
            alignment: Alignment.center,
            child: Text(
              OneAppString.of().evGoAccountFoundText,
              style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.callout1,
                _colorUtil.tertiary05,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        SizedBox(
          height: 60.h,
        ),
        Padding(
          padding: EdgeInsets.only(top: 40.h, bottom: 16.h),
          child: InkWell(
            onTap: () {
              _bloc.screenRedirectToEvlogin();
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 75.w),
              decoration: BoxDecoration(
                  border: Border.all(
                    color: _colorUtil.tertiary10,
                  ),
                  color: _colorUtil.tertiary15,
                  borderRadius: BorderRadius.all(Radius.circular(25.r))),
              child: Text(
                OneAppString.of().loginSignin,
                style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body4,
                  _colorUtil.tertiary03,
                ),
              ),
            ),
          ),
        ),
        Padding(
            padding: EdgeInsets.only(top: 8.h, bottom: 16.h),
            child: CustomDefaultButton(
              backgroundColor: _colorUtil.tertiary00,
              buttonTextColor: _colorUtil.tertiary15,
              text: OneAppString.of().loginRegister,
              press: () {
                _bloc.screenRedirectToEvRegistration();
              },
              horizontalPadding: 22.w,
              verticalPadding: 6.h,
            )),
      ],
    );
  }

//// Evgo Login Layout
  Widget _evGoLogin() {
    return Column(
      children: [
        bottomSheetAppBar(
          OneAppString.of().loginSignin,
          elevation: 0,
          backgroundColor: Colors.transparent,
          leadingWidget: CircleAvatar(
            backgroundColor: _colorUtil.tertiary15,
            radius: 10.r,
            child: CircleAvatar(
              radius: 22.r,
              backgroundColor: _colorUtil.button02b,
              child: IconButton(
                  icon: Icon(
                    Icons.chevron_left,
                    color: ThemeConfig.current().colorUtil.tertiary00,
                  ),
                  onPressed: () {
                    _bloc.backPressedHandler();
                    _signInPasswordController.clear();
                  }),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 30.h, bottom: 20.h),
          child: Text(
            /// Email Is Shown Using Global Instance
            _signInUserMail!,
            textAlign: TextAlign.center,
            style: TextStyleExtension()
                .newStyleWithColor(_textStyleUtil.body4, _colorUtil.tertiary00),
          ),
        ),
        _signInLayout()
      ],
    );
  }

//// Sign In Loayput
  Widget _signInLayout() {
    return Column(
      children: [
        Form(
          key: _signinformKey,
          child: Container(
            child: Column(
              children: [
                //// Sign in Password Layout
                Padding(
                  padding: EdgeInsets.all(25.h),
                  child: Container(
                    child: TextFormField(
                      obscureText: !_passwordVisible,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body3, _colorUtil.tertiary00),
                      decoration: InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 10.h,
                            horizontal: 10.w,
                          ),

                          /// Eye Icon to toggle Password Visibility
                          suffixIcon: IconButton(
                            icon: Icon(
                                _passwordVisible
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: _colorUtil.tertiary10),
                            onPressed: () {
                              setState(() {
                                _passwordVisible = !_passwordVisible;
                              });
                            },
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.r)),
                            borderSide:
                                BorderSide(color: _colorUtil.tertiary10),
                          ),
                          disabledBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.r)),
                            borderSide:
                                BorderSide(color: _colorUtil.tertiary10),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: _colorUtil.tertiary10),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.r)),
                            borderSide:
                                BorderSide(color: _colorUtil.gradientOneStart),
                          ),
                          border: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.r)),
                            borderSide:
                                BorderSide(color: _colorUtil.tertiary10),
                          ),
                          hintStyle: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.body3, _colorUtil.tertiary03),
                          filled: true,
                          fillColor: _colorUtil.tertiary15),
                      controller: _signInPasswordController,
                      autovalidateMode: AutovalidateMode.onUserInteraction,

                      /// validation Check Function
                      validator: _bloc.signinValidatePasswordCheck,
                    ),
                  ),
                ),
                StreamBuilder<bool>(
                    stream: _bloc.errorMessageVisible,
                    builder: (context, snapshot) {
                      return snapshot.hasData
                          ? Visibility(
                              visible: snapshot.data!,
                              child: Container(
                                padding: EdgeInsets.all(8),
                                child: VehicleSnippetCard(
                                  snippetItem:
                                      VehicleSubscriptionOfferSnippetHelper(
                                          id: 0,
                                          headingText:
                                              _bloc.enrollErrorMessageText,
                                          iconPath: announcementsIcon,
                                          cardColor: ThemeConfig.current()
                                              .colorUtil
                                              .primary01,
                                          iconColor: ThemeConfig.current()
                                              .colorUtil
                                              .primary01),
                                  closeSnippet: _closeSnippet,
                                ),
                              ),
                            )
                          : Container();
                    }),
                Padding(
                  padding: EdgeInsets.only(top: 50.h, bottom: 10.h),
                  child: InkWell(
                    onTap: () => {forgetPasswordScreen()},
                    child: Container(
                        child: Text(
                      OneAppString.of().forgetPasswordText,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.buttonLink1, _colorUtil.tertiary00),
                    )),
                  ),
                ),

                Padding(
                  padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
                  child: CustomDefaultButton(
                    backgroundColor: _colorUtil.tertiary03,
                    text: OneAppString.of().loginSignin,
                    press: signinOnClickListener,
                    buttonTextColor: _colorUtil.tertiary15,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

//// Sign in button click Listener
  void signinOnClickListener() {
    if (_signinformKey.currentState!.validate()) {
      debugPrint("Password Validated");
      {
        _signInUserPassword = _signInPasswordController.text;
        _bloc.enrollEVGoUser(password: _signInUserPassword);
      }

      debugPrint("UserPassword: $_signInUserPassword");
    } else {
      debugPrint("Not validated Pass");
    }
  }

//// Evgo Register Layout
  Widget _evGoRegister() {
    return Container(
      child: SingleChildScrollView(
        child: Column(
          children: [
            bottomSheetAppBar(
              OneAppString.of().loginRegister,
              elevation: 0,
              backgroundColor: Colors.transparent,
              leadingWidget: CircleAvatar(
                backgroundColor: _colorUtil.tertiary15,
                radius: 10.r,
                child: CircleAvatar(
                  radius: 22.r,
                  backgroundColor: _colorUtil.button02d,
                  child: IconButton(
                      icon: Icon(
                        Icons.chevron_left,
                        color: ThemeConfig.current().colorUtil.tertiary00,
                      ),
                      onPressed: () {
                        _bloc.backPressedHandler();

                        /// clear text from field
                        _registrationConfrimPasswordController.clear();
                        _registrationPasswordController.clear();
                      }),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 30.h, bottom: 20.h),
              child: Text(
                /// Email Is Shown Using Global Instance
                _signInUserMail!,
                textAlign: TextAlign.center,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body4, _colorUtil.tertiary00),
              ),
            ),
            _registerLayout(),
          ],
        ),
      ),
    );
  }

//// Register Layout
  Widget _registerLayout() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Form(
              key: _registerformKey,
              child: Container(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: 5.h),
                      child: Container(
                        child: Padding(
                          padding: EdgeInsets.only(
                              top: 10.h, bottom: 5.h, right: 20.w, left: 20.w),
                          child: TextFormField(
                            obscureText: !_registrationPasswordVisible,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body3, _colorUtil.tertiary00),
                            decoration: InputDecoration(
                                errorMaxLines: 2,
                                contentPadding: EdgeInsets.symmetric(
                                  vertical: 10.h,
                                  horizontal: 10.w,
                                ),

                                /// Eye Icon to toggle Password Visibility
                                suffixIcon: IconButton(
                                  icon: Icon(
                                      _registrationPasswordVisible
                                          ? Icons.visibility
                                          : Icons.visibility_off,
                                      color: _colorUtil.tertiary10),
                                  onPressed: () {
                                    setState(() {
                                      _registrationPasswordVisible =
                                          !_registrationPasswordVisible;
                                    });
                                  },
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.r)),
                                  borderSide:
                                      BorderSide(color: _colorUtil.tertiary10),
                                ),
                                disabledBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.r)),
                                  borderSide:
                                      BorderSide(color: _colorUtil.tertiary10),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: _colorUtil.tertiary10),
                                ),
                                errorBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.r)),
                                  borderSide: BorderSide(
                                      color: _colorUtil.gradientOneStart),
                                ),
                                border: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.r)),
                                  borderSide:
                                      BorderSide(color: _colorUtil.tertiary10),
                                ),
                                hintText: OneAppString.of().passwordHintText,
                                hintStyle: TextStyleExtension()
                                    .newStyleWithColor(_textStyleUtil.body3,
                                        _colorUtil.tertiary00),
                                errorStyle:
                                    TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.body3,
                                  _colorUtil.error01,
                                ),
                                filled: true,
                                fillColor: _colorUtil.tertiary15),
                            controller: _registrationPasswordController,
                            autovalidateMode:
                                AutovalidateMode.onUserInteraction,

                            /// Validation Function
                            validator:
                                _bloc.registrationPasswordValidationCheck,
                          ),
                        ),
                      ),
                    ),
                    //// Confrim Password Field Layout
                    Padding(
                      padding: EdgeInsets.only(top: 5.h),
                      child: Container(
                        child: Padding(
                          padding: EdgeInsets.only(
                              top: 10.h, bottom: 5.h, right: 20.w, left: 20.w),
                          child: TextFormField(
                            obscureText: !_registrationConfrimpasswordVisible,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body3, _colorUtil.tertiary00),
                            decoration: InputDecoration(
                                errorMaxLines: 2,
                                contentPadding: EdgeInsets.symmetric(
                                  vertical: 10.h,
                                  horizontal: 10.w,
                                ),

                                /// Eye Icon to toggle Password Visibility
                                suffixIcon: IconButton(
                                  icon: Icon(
                                      _registrationConfrimpasswordVisible
                                          ? Icons.visibility
                                          : Icons.visibility_off,
                                      color: _colorUtil.tertiary10),
                                  onPressed: () {
                                    setState(() {
                                      _registrationConfrimpasswordVisible =
                                          !_registrationConfrimpasswordVisible;
                                    });
                                  },
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.r)),
                                  borderSide:
                                      BorderSide(color: _colorUtil.tertiary10),
                                ),
                                disabledBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.r)),
                                  borderSide:
                                      BorderSide(color: _colorUtil.tertiary10),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: _colorUtil.tertiary10),
                                ),
                                errorBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.r)),
                                  borderSide: BorderSide(
                                      color: _colorUtil.gradientOneStart),
                                ),
                                border: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.r)),
                                  borderSide:
                                      BorderSide(color: _colorUtil.tertiary10),
                                ),
                                hintText:
                                    OneAppString.of().confirmPasswordHintText,
                                hintStyle: TextStyleExtension()
                                    .newStyleWithColor(_textStyleUtil.body3,
                                        _colorUtil.tertiary00),
                                errorStyle:
                                    TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.body3,
                                  _colorUtil.error01,
                                ),
                                filled: true,
                                fillColor: _colorUtil.tertiary15),
                            controller: _registrationConfrimPasswordController,
                            autovalidateMode:
                                AutovalidateMode.onUserInteraction,

                            /// Validation Function
                            validator: _confirmPasswordValidation,
                          ),
                        ),
                      ),
                    ),
                    StreamBuilder<bool>(
                        stream: _bloc.errorMessageVisible,
                        builder: (context, snapshot) {
                          return snapshot.hasData
                              ? Visibility(
                                  visible: snapshot.data!,
                                  child: Container(
                                      padding: EdgeInsets.all(8),
                                      child: VehicleSnippetCard(
                                          snippetItem:
                                              VehicleSubscriptionOfferSnippetHelper(
                                                  id: 0,
                                                  headingText: _bloc
                                                      .enrollErrorMessageText,
                                                  iconPath: announcementsIcon,
                                                  cardColor:
                                                      ThemeConfig.current()
                                                          .colorUtil
                                                          .primary01,
                                                  iconColor:
                                                      ThemeConfig.current()
                                                          .colorUtil
                                                          .primary01),
                                          closeSnippet: _closeSnippet)),
                                )
                              : Container();
                        }),
                    //// Button  Layout

                    Padding(
                      padding: EdgeInsets.only(
                        top: 30.h,
                      ),
                      child: CustomDefaultButton(
                        backgroundColor: _colorUtil.tertiary03,
                        text: OneAppString.of().loginRegister,
                        press: _registerOnClickListener,
                        buttonTextColor: _colorUtil.tertiary15,
                      ),
                    )
                  ],
                ),
              ))
        ],
      ),
    );
  }

//// On-click Listener Function for Register
  _registerOnClickListener() {
    if (_registerformKey.currentState!.validate()) {
      debugPrint("Validated success");
      _registrationUserPassword = _registrationPasswordController.text;
      _bloc.enrollEVGoUser(password: _registrationUserPassword);
    } else {
      debugPrint("validated Failed");
    }
  }

  void updateProfileScreen(String errorMessage) {
    BottomErrorConfirmationDialog().showBottomDialog(
      context,
      chargeAlertIcon,
      OneAppString.of().updateProfileText,
      OneAppString.of().updateProfileErrorMessage(errorMessage),
      OneAppString.of().updateProfileButtonText,
      OneAppString.of().cancel,
      _updateProfileClickListener,
    );
  }

  void evgoErrorMessage() {
    BottomErrorConfirmationDialog().showBottomDialog(
      context,
      chargeAlertIcon,
      OneAppString.of().evgoLinkingFailedHeading,
      OneAppString.of().evgo400FailedError,
      OneAppString.of().openEVgo,
      OneAppString.of().contactSupportText,
      _bloc.evgoUrlLauncher,
      cancelCallback: _bloc.callCustomerSupport,
      barrierDismissible: true,
    );
  }

  void screenActionCallBack(ENROLLMENT_SCREENS val, {String? errorMessage}) {
    switch (val) {
      case ENROLLMENT_SCREENS.CHARGEPOINT_UPDATE_PROFILE:
        Navigator.of(context).pop();
        widget.screenAction!(EV_SCREENS.UPDATE_PROFILE,
            errorMessage: errorMessage);
        break;
      case ENROLLMENT_SCREENS.EVGO_UPDATE_PROFILE:
        updateProfileScreen(errorMessage!);
        break;
      case ENROLLMENT_SCREENS.ACCOUNT_LINKED:
        accountLinKedScreen();
        break;
      case ENROLLMENT_SCREENS.ACCOUNT_CREATED:
        accountCreatedScreen();
        break;
      case ENROLLMENT_SCREENS.CONTINUE_CALL_BACK:
        Navigator.of(context).pop();
        widget.continueCallback();
        break;
      case ENROLLMENT_SCREENS.EVGO_ERROR:
        evgoErrorMessage();
        break;
      default:
        break;
    }
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  _updateProfileClickListener() {
    _showVehicleAccountPage();
  }

  _showVehicleAccountPage<bool>() {
    return showMaterialModalBottomSheet(
      expand: true,
      context: context,
      isDismissible: true,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(child: VehicleAccountPage()),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _bloc.dispose();
    _signInPasswordController.clear();
    _registrationConfrimPasswordController.clear();
    _registrationPasswordController.clear();
  }

  /// Account Linked
  Future accountLinKedScreen() {
    return showMaterialModalBottomSheet(
      expand: false,
      context: context,
      elevation: 10,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      barrierColor: _colorUtil.tertiary15,
      backgroundColor: _colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
          child: EvGoAccountLinkedPage(
        findActionContinueCallback: _findActionOnClickListener,
      )),
    );
  }

  /// Account Created
  Future accountCreatedScreen() {
    return showMaterialModalBottomSheet(
      expand: false,
      context: context,
      elevation: 10,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      barrierColor: _colorUtil.tertiary15,
      backgroundColor: _colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => Container(
        height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_65,
        child: EvGoAccountCreatedPage(
          findActionCallback: _findActionOnClickListener,
        ),
      ),
    );
  }

  Future forgetPasswordScreen() {
    return showModalBottomSheet(
      context: context,
      elevation: 10,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      barrierColor: Colors.transparent,
      backgroundColor: _colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => Container(
          height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_65,
          child: SafeArea(child: EvgoForgetPasswordPage())),
    );
  }

  _findActionOnClickListener() {
    screenActionCallBack(ENROLLMENT_SCREENS.CONTINUE_CALL_BACK);
  }

//// Confrim Password Validation
  String? _confirmPasswordValidation(_userConfirmPass) {
    if (_userConfirmPass.isEmpty) {
      return OneAppString.of().required;
    } else if (_registrationConfrimPasswordController.text !=
        _registrationPasswordController.text) {
      return OneAppString.of().confirmPasswordErrorText;
    } else {
      return null;
    }
  }

  _closeSnippet(int? p1) {
    _bloc.closeErrorMessage();
  }

  Widget _retryLayout() {
    return Container(
        height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_50,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              OneAppString.of().errorMessage,
              textAlign: TextAlign.center,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary05),
            ),
            SizedBox(height: 50.h),
            Container(
                child: Padding(
              padding: EdgeInsets.only(top: 80.h),
              child: CustomDefaultButton(
                backgroundColor: _colorUtil.button01b,
                buttonTextColor: _colorUtil.button01a,
                text: OneAppString.of().retry,
                press: () {
                  Navigator.of(context).pop();
                },
                borderColor: _colorUtil.button01b,
                horizontalPadding: 22.w,
                verticalPadding: 6.h,
              ),
            ))
          ],
        ));
  }
}
