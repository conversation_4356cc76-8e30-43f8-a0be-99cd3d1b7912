// Package imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_web_auth/flutter_web_auth.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network/api_config.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';
import '../../../../log/vehicle_analytic_event.dart';
import '../../ev_common/ev_enum_const_util.dart';
import '../vehicle_search_charge_station_location_util.dart';
import 'enrolment_api_wrapper.dart';

class EVDriverAccountEnrollmentBloc extends BlocBase {
  bool launchWebView = true;
  OneAppClient api = APIClientConfig.oneAppClient;
  String? partnerDriverIdString;
  Function(bool)? progressHandlerCallback;
  Function(ENROLLMENT_SCREENS, {String? errorMessage})? screenActionCallBack;

  Stream<EnrollmentAccountState> get accountState => _accountState.stream;
  final _accountState = BehaviorSubject<EnrollmentAccountState>();

  final _enrollmentPartnerName = BehaviorSubject<String>();
  Stream<String> get enrollmentPartnerName => _enrollmentPartnerName.stream;

  final _errorMessageVisible = BehaviorSubject<bool>();

  Stream<bool> get errorMessageVisible => _errorMessageVisible.stream;
  final _enrollErrorMessage = BehaviorSubject<String>();

  Stream<String> get enrollErrorMessage => _enrollErrorMessage.stream;

  String get enrollErrorMessageText => _enrollErrorMessage.value;

  void init(
      {Function(bool)? progressHandler,
      Function(ENROLLMENT_SCREENS, {String? errorMessage})? screenAction,
      Station? selectedChargeStation,
      String? partnerName}) {
    screenActionCallBack = screenAction;
    progressHandlerCallback = progressHandler;
    _errorMessageVisible.sink.add(false);
    _enrollErrorMessage.sink.add('');
    enrollmentRedirect(
        selectedChargeStation: selectedChargeStation, partnerName: partnerName);
  }

  /// charge point login redirection
  Future<void> chargePointLogin() {
    final url =
        '${APIConfig.cpWebAuthBaseUrl}?code=$partnerDriverIdString&forcesignin=true';
    final callbackUrlScheme = 'com.toyota.oneapp';
    return FlutterWebAuth.authenticate(
            url: url, callbackUrlScheme: callbackUrlScheme)
        .then((result) {
      if (result == APIConfig.cpWebAuthCallBackUrl) {
        ///Driver existence check and then Redirection to Start charge screen
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_REG_GROUP,
          childEventName: VehicleAnalyticsEvent.VEHICLE_EV_PUB_CP_REG_LINKED,
        );
        _accountState.sink
            .add(EnrollmentAccountState.CHARGEPOINT_ACCOUNT_LINKED);
      }
    }).catchError((error) {
      /// Driver existence check redirect to register screen
      debugPrint(error.toString());
    });
  }

  //// enrolment check
  Future<void> enrollCPUser() async {
    progressHandlerCallback!(true);
    final commonResponse = await EvEnrolmentAPI.callDriverEnrollment(
      evNetwork: _enrollmentPartnerName.value,
    );
    final responseMessage = commonResponse.response?.messages;

    String enrollmentState =
        EvEnrolmentAPI.ENROLMENT.getEnrolmentState(commonResponse);

    if (enrollmentState == EvEnrolmentAPI.ENROLMENT.DRIVER_ACCOUNT_FOUND) {
      partnerDriverIdString =
          commonResponse.response!.payload!.accounts?.first.partnerDriverId;
      _accountState.sink.add(EnrollmentAccountState.ACCOUNT_FOUND);
      FireBaseAnalyticsLogger.logMarketingGroupEvent(
        VehicleAnalyticsEvent.VEHICLE_EV_PUB_REG_GROUP,
        childEventName: VehicleAnalyticsEvent.VEHICLE_EV_PUB_CP_REG_CREATED,
      );
    } else if (enrollmentState ==
        EvEnrolmentAPI.ENROLMENT.DRIVER_ACCOUNT_NOT_FOUND) {
      /// REGISTER Condition NOT FOUND
      partnerDriverIdString =
          commonResponse.response!.payload!.accounts?.first.partnerDriverId;
      _accountState.sink.add(EnrollmentAccountState.ACCOUNT_NOT_FOUND);
    } else if (enrollmentState ==
        EvEnrolmentAPI.ENROLMENT.DRIVER_NOT_FOUND_UPDATE_PROFILE) {
      progressHandlerCallback!(false);
      Future.delayed(Duration(milliseconds: 100), () {
        screenActionCallBack!(ENROLLMENT_SCREENS.CHARGEPOINT_UPDATE_PROFILE,
            errorMessage: responseMessage!.detailedDescription);
      });
    } else {
      progressHandlerCallback!(false);
      _accountState.sink.add(EnrollmentAccountState.ERROR);
    }
    if (commonResponse.error != null) {
      progressHandlerCallback!(false);
      _accountState.sink.add(EnrollmentAccountState.ERROR);
      FireBaseAnalyticsLogger.logMarketingGroupEvent(
        VehicleAnalyticsEvent.VEHICLE_EV_PUB_REG_GROUP,
        childEventName: VehicleAnalyticsEvent.VEHICLE_EV_PUB_CP_REG_FAILED,
      );
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_EV_ENROLMENT_FAILURE);
    }
    progressHandlerCallback!(false);
  }

  void enrollmentRedirect(
      {Station? selectedChargeStation, String? partnerName}) {
    late String evNetwork;

    if (partnerName != null && partnerName.toString().isNotEmpty) {
      evNetwork = partnerName.toString().toLowerCase();
    } else {
      evNetwork = (!selectedChargeStation!.isRoamingPartner
          ? VehicleSearchChargeStationLocationUtil.getPartnerTypeText(
              selectedChargeStation,
              toLowerCase: true)
          : EV_PARTNERS.CHARGE_POINT);
    }

    if (evNetwork == EV_PARTNERS.CHARGE_POINT) {
      _enrollmentPartnerName.sink.add(evNetwork);
      enrollCPUser();
      return;
    } else if (evNetwork == EV_PARTNERS.EVGO) {
      _enrollmentPartnerName.sink.add(evNetwork);
      _accountState.sink.add(EnrollmentAccountState.ACCOUNT_FOUND);
      return;
    } else {
      return;
    }
  }

  void backPressedHandler() {
    _errorMessageVisible.sink.add(false);
    _accountState.sink.add(EnrollmentAccountState.ACCOUNT_FOUND);
  }

  void screenRedirectToEvlogin() {
    _accountState.sink.add(EnrollmentAccountState.LOGIN);
  }

  void screenRedirectToEvRegistration() {
    _accountState.sink.add(EnrollmentAccountState.REGISTER);
  }

  /// Password Validation Check
  String? signinValidatePasswordCheck(String? signInPass) {
    _errorMessageVisible.sink.add(false);
    if (signInPass == null || signInPass.trim().isEmpty) {
      return "Required";
    }
    return null;
  }

  /// valid eVGO password structure
  bool validateStructure(String value) {
    String pattern =
        r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{8,}$';

    RegExp regExp = RegExp(pattern);

    return regExp.hasMatch(value);
  }

  /// Valid Password Validation check as per EVGO
  String? registrationPasswordValidationCheck(String? _userPass) {
    _errorMessageVisible.sink.add(false);
    if (_userPass == null || _userPass.trim().isEmpty) {
      return OneAppString.of().required;
    } else if (_userPass.trim().length < 8) {
      return OneAppString.of().passwordLengthRule;
    } else if (!validateStructure(_userPass.trim())) {
      return OneAppString.of().passwordCharactersRule;
    } else {
      return null;
    }
  }

  Future<void> evgoUrlLauncher() async {
    await urlLauncher(APIConfig.evgoWebUrl);
  }

  void callCustomerSupport() async {
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    bool isToyota = Global.getInstance().appBrand == "T" ? true : false;
    dialerLauncher(getCustomerSupportNumber(vehicleItem!.region!, isToyota));
  }

  Future<void> enrollEVGoUser({required String password}) async {
    progressHandlerCallback!(true);
    final commonResponse = await EvEnrolmentAPI.callDriverEnrollment(
      evNetwork: _enrollmentPartnerName.value,
      password: password,
    );
    final responseMessage = commonResponse.response?.messages;
    if (commonResponse.error != null) {
      _stopProgressShowError(commonResponse.error?.errorMessage);
      FireBaseAnalyticsLogger.logMarketingGroupEvent(
        VehicleAnalyticsEvent.VEHICLE_EV_PUB_REG_GROUP,
        childEventName: VehicleAnalyticsEvent.VEHICLE_EV_PUB_EVGO_REG_FAILED,
      );
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_EV_ENROLMENT_FAILURE);
      return;
    }
    if (responseMessage != null && responseMessage.description != null) {
      if (responseMessage.responseCode == 200 &&
          responseMessage.description!.toUpperCase() == "SUCCESS") {
        if (responseMessage.detailedDescription?.toLowerCase() ==
            "call was successful and driver found") {
          progressHandlerCallback!(false);
          FireBaseAnalyticsLogger.logMarketingGroupEvent(
            VehicleAnalyticsEvent.VEHICLE_EV_PUB_REG_GROUP,
            childEventName:
                VehicleAnalyticsEvent.VEHICLE_EV_PUB_EVGO_REG_LINKED,
          );
          Future.delayed(Duration(milliseconds: 100), () {
            screenActionCallBack!(ENROLLMENT_SCREENS.ACCOUNT_LINKED);
          });
        } else {
          progressHandlerCallback!(false);
          FireBaseAnalyticsLogger.logMarketingGroupEvent(
            VehicleAnalyticsEvent.VEHICLE_EV_PUB_REG_GROUP,
            childEventName:
                VehicleAnalyticsEvent.VEHICLE_EV_PUB_EVGO_REG_CREATED,
          );
          Future.delayed(Duration(milliseconds: 100), () {
            screenActionCallBack!(ENROLLMENT_SCREENS.ACCOUNT_CREATED);
          });
        }
      } else if (responseMessage.responseCode == 406 &&
          responseMessage.description?.toUpperCase() == "FAILED") {
        progressHandlerCallback!(false);
        Future.delayed(Duration(milliseconds: 100), () {
          screenActionCallBack!(ENROLLMENT_SCREENS.EVGO_UPDATE_PROFILE,
              errorMessage: responseMessage.detailedDescription);
        });
      } else if (responseMessage.responseCode == 400 &&
          responseMessage.description?.toUpperCase() == "FAILED") {
        progressHandlerCallback!(false);
        Future.delayed(Duration(milliseconds: 100), () {
          screenActionCallBack!(ENROLLMENT_SCREENS.EVGO_ERROR);
        });
      } else {
        _stopProgressShowError(responseMessage.detailedDescription);
      }
    } else {
      _stopProgressShowError(responseMessage?.detailedDescription);
    }
    progressHandlerCallback!(false);
  }

  void _stopProgressShowError(String? message) {
    progressHandlerCallback!(false);
    _errorMessageVisible.sink.add(true);
    _enrollErrorMessage.sink.add(message ?? 'Unknown Error occurred');
  }

  @override
  void dispose() {
    _accountState.close();
    _enrollmentPartnerName.close();
    _enrollErrorMessage.close();
    _errorMessageVisible.close();
  }

  void closeErrorMessage() {
    _errorMessageVisible.sink.add(false);
  }
}
