// Dart imports:
import 'dart:convert';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:collection/collection.dart' show IterableExtension;
import 'package:ev_module/log/ev_analytics_events.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/config/oneapp_ui_config.dart';
import 'package:oneapp_common/entity/filterModel.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_evs_connector_Entity.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_wallet_and_enrollment_validation.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/map_box_prediction_entity.dart';

// Project imports:
import '../../../log/vehicle_analytic_event.dart';
import '../ev_common/ev_enum_const_util.dart';
import 'ev_driver_account_enrollment/enrolment_api_wrapper.dart';

class VehicleSearchChargeStationLocationUtil {
  static Position get dallasLatLng => Position(-96.8891417, 32.9080553);

  ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  static const typeOfConnectors = ["j1772", "ccs1"];

  static void setFavouriteStation(String text, bool isFav) {
    if (isFav) {
      /// Add the station into Favourite list if isFav is true
      FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
          childEventName: EVAnalyticsEvent.FAVORITE_STATION);
      addFavourite(text);
    } else {
      /// Remove the station form Favourite list if isFav is false
      removeFavourite(text);
    }
  }

  static List<Station> getFavStations(List<Station> stationsList) {
    return stationsList.where((element) => element.isFavourite).toList();
  }

  FilterHelperClass? getPartnerFilterDetails(List<String> data) {
    FilterHelperClass? filterHelper;
    if (data.length == 1) {
      if (data.contains('ChargePoint')) {
        filterHelper = FilterHelperClass(
          isOpenFilterEnabled: true,
          name: data[0],
          color: _colorUtil.tertiary15,
          backgroundColor: _colorUtil.tertiary00,
          iconColor: _colorUtil.button05b,
        );
      } else {
        filterHelper = FilterHelperClass(
          isOpenFilterEnabled: true,
          name: data[0],
          color: _colorUtil.tertiary15,
          backgroundColor: _colorUtil.tertiary00,
          iconColor: _colorUtil.button05b,
        );
      }
    } else if (data.length > 1) {
      if (data.contains('ChargePoint')) {
        filterHelper = FilterHelperClass(
            isOpenFilterEnabled: true,
            name: OneAppString.of().partners,
            length: data.length,
            color: _colorUtil.tertiary15,
            backgroundColor: _colorUtil.tertiary00,
            iconColor: _colorUtil.button05b);
      } else {
        filterHelper = FilterHelperClass(
            isOpenFilterEnabled: true,
            name: OneAppString.of().partners,
            length: data.length,
            color: _colorUtil.tertiary15,
            backgroundColor: _colorUtil.tertiary00,
            iconColor: _colorUtil.button05b);
      }
    }
    return filterHelper;
  }

  FilterHelperClass getConnectorFilterDetails(List<String> data) {
    FilterHelperClass filterHelper;
    if (data.length == 1) {
      filterHelper = FilterHelperClass(
        isOpenFilterEnabled: true,
        name: data[0],
        color: _colorUtil.tertiary15,
        backgroundColor: _colorUtil.tertiary00,
        iconColor: _colorUtil.tertiary15,
      );
    } else {
      filterHelper = FilterHelperClass(
        isOpenFilterEnabled: true,
        name: OneAppString.of().plugTypes,
        length: data.length,
        color: _colorUtil.tertiary15,
        backgroundColor: _colorUtil.tertiary00,
        iconColor: _colorUtil.tertiary15,
      );
    }
    return filterHelper;
  }

  static List<FilterContentModel> selectAllPartnerFilter(
      List<FilterContentModel> partnerlists, bool isOpen) {
    partnerlists.forEach((element) {
      isOpen ? element.isSelected = true : element.isSelected = false;
    });
    return partnerlists.toList();
  }

  static List<FilterContentModel> selectAllPlugTypeFilter(
      List<FilterContentModel> plugLists, bool isSelected) {
    plugLists.forEach((element) {
      isSelected ? element.isSelected = true : element.isSelected = false;
    });
    return plugLists.toList();
  }

  static String partnerEnrollmentStatus(
      {PartnerStatus? partners, String? wallet}) {
    if (wallet?.toUpperCase() == "FOUND") {
      return partners?.status.toUpperCase() == 'FOUND'
          ? OneAppString.of().enrolled
          : OneAppString.of().loginRegister;
    } else {
      return OneAppString.of().loginRegister;
    }
  }

  static List<String> selectedPatener(List<FilterContentModel> partnerlists) {
    List<String> partners = [];
    partnerlists.forEach((element) {
      if (element.isSelected) partners.add(element.itemName.toLowerCase());
    });
    return partners;
  }

  static String getRegistrationStatusText({int status = 1}) {
    switch (status) {
      case 1:
        return OneAppString.of().additionalDataRequiredForRegistration;
      default:
        return '';
    }
  }

  static String get evCarOnMapPinIcon {
    return evParkPin;
  }

  static String get mapPinBlueIcon {
    return mapPinBlueImage;
  }

  static String get evFavouritePinIcon {
    return evFavouritePin;
  }

  static String get evFavouriteBlackPinIcon {
    return evFavBlackPin;
  }

  static String get evPreferredDealerPinIcon {
    return evBlueChargeStationPin;
  }

  static String get evNonPreferredDealerPinIcon {
    return evBlackChargeStationPin;
  }

  static String getPartnerTypeText(Station station,
      {bool toLowerCase = false}) {
    PartnerInfo? partnerInfo = getNonNrelPartnerInfo(station);
    if (partnerInfo != null) {
      return toLowerCase ? partnerInfo.name.toLowerCase() : partnerInfo.name;
    }
    return toLowerCase
        ? OneAppString.of().evNetwork.toLowerCase()
        : OneAppString.of().evNetwork;
  }

  static String getStationName(Station station) {
    return station.name;
  }

  static bool isPartnerStation({
    required Station selectedStation,
    required bool isWalletEnabled,
    bool checkIfChargePoint = false,
  }) {
    final isStationPartner = selectedStation.isPartner(isWalletEnabled);
    if (checkIfChargePoint) {
      return isStationPartner && getChargePointInfo(selectedStation) != null;
    }
    return isStationPartner;
  }

  static bool _isChargePoint(PartnerInfo partnerInfoData) {
    return (partnerInfoData.name.toUpperCase() ==
            Station.partnerNameChargePoint ||
        partnerInfoData.name.toUpperCase() ==
            Station.partnerNameChargePointNetwork);
  }

  static bool _isNotNrel(PartnerInfo partnerInfoData) {
    return partnerInfoData.name.toUpperCase() != Station.partnerNameNrel;
  }

  static PartnerInfo? getChargePointInfo(Station selectedHit) {
    if (!selectedHit.isRoamingPartner) {
      return selectedHit.partnerInfo.firstWhereOrNull(_isChargePoint);
    } else {
      return selectedHit.partnerInfo.first;
    }
  }

  static PartnerInfo? getNonNrelPartnerInfo(Station selectedHit) {
    return selectedHit.partnerInfo.firstWhereOrNull(_isNotNrel);
  }

  static Map<String, List> preparePrizeInfo(Station station) {
    List energyPrizeList = [];
    List parkingPrizeList = [];
    PartnerInfo? partnerInfo = getChargePointInfo(station);
    if (partnerInfo != null) {
      TariffInfo? tariffInfo = getTariffInfo(
          station: station,

          /// Todo Instead of first standard, We have to pass Selected Standard
          standard: station.evEvsConnectorsDetail.first.standard);

      tariffInfo?.elements.forEach((element) {
        element.priceComponents?.forEach((PriceComponent priceComponent) {
          if (isParkingTariff(priceComponent)) {
            bool isMinRestrictionFound = false;
            bool isMaxRestrictionFound = false;
            String stepValueText = priceComponent.stepSize! > 1
                ? OneAppString.of().perHours(priceComponent.stepSize!)
                : OneAppString.of().perHour;
            if (element.restrictions?.maxDuration != null) {
              isMaxRestrictionFound = true;
              parkingPrizeList.add({
                'label': OneAppString.of().forFirstMaxDuration(
                    convertSecondsToHours(
                        element.restrictions!.maxDuration.toString())),
                'value': OneAppString.of()
                    .parkingPriceValue(priceComponent.price!, stepValueText)
              });
            }
            if (element.restrictions?.minDuration != null) {
              isMinRestrictionFound = true;
              parkingPrizeList.add({
                'label': OneAppString.of().forAfterMinDuration(
                    convertSecondsToHours(
                        element.restrictions!.minDuration.toString())),
                'value': OneAppString.of()
                    .parkingPriceValue(priceComponent.price!, stepValueText)
              });
            }
            if ((!isMinRestrictionFound && !isMaxRestrictionFound) &&
                element.restrictions?.startTime != null &&
                element.restrictions?.endTime != null) {
              parkingPrizeList.add({
                'label':
                    '${changeDateTimeFormat(element.restrictions!.startTime!, "HH:mm", "h:mma")} - ${changeDateTimeFormat(element.restrictions!.endTime!, "HH:mm", "h:mma")}',
                'value': OneAppString.of()
                    .parkingPriceValue(priceComponent.price!, stepValueText)
              });
            }
          } else if (isFlatTariff(priceComponent)) {
            energyPrizeList.add({
              'label': OneAppString.of().flatFee,
              'value': OneAppString.of().energyPriceValue(
                priceComponent.price!,
              )
            });
          }
        });
      });
    }
    return {
      'energy': energyPrizeList,
      'parking': parkingPrizeList,
    };
  }

  static bool isParkingTariff(PriceComponent priceComponent) {
    return ["SESSION_TIME", "PARKING_TIME"]
        .contains(priceComponent.type!.toUpperCase());
  }

  static bool isFlatTariff(PriceComponent priceComponent) {
    return ["FLAT", "ENERGY"].contains(priceComponent.type!.toUpperCase());
  }

  static List<EvEvsConnectorEntity> getTotalConnectors(Station station) {
    return station.evEvsConnectorsDetail
        .where((element) => element.total > 0)
        .toList();
  }

  static List<EvEvsConnectorEntity> getActiveConnectors(Station station) {
    return station.evEvsConnectorsDetail
        .where((element) => element.active! > 0)
        .toList();
  }

  static void updateCounterOnStation(Station station) {
    List<EvEvsConnectorEntity> _connectorSet = [];
    station.connectorsum.forEach((standard, value) {
      if (station.connectorsum[standard]!.total != null &&
          station.connectorsum[standard]!.total! > 0) {
        _connectorSet.add(EvEvsConnectorEntity(
          standard: standard,
          total: value.total!,
          active: value.active,
          connectorInfo:
              getAnyConnectorByStandard(standard: standard, station: station),
          tariffInfo:
              getAnyTariffByStandard(standard: standard, station: station),
        ));
      }
    });
    station.evEvsConnectorsDetail = _connectorSet;
  }

  static Connector? getAnyConnectorByStandard(
      {required String standard, required Station station}) {
    PartnerInfo? partnerInfo = getChargePointInfo(station);
    if (partnerInfo != null) {
      for (final Evse eachEvs in partnerInfo.evses!) {
        for (final Connector connector in eachEvs.connectors!) {
          if (connector.standard == standard) {
            return connector;
          }
        }
      }
    }
    return null;
  }

  static TariffInfo? getAnyTariffByStandard(
      {required String standard, required Station station}) {
    PartnerInfo? partnerInfo = getChargePointInfo(station);
    if (partnerInfo != null) {
      for (final Evse eachEvs in partnerInfo.evses!) {
        for (final Connector connector in eachEvs.connectors!) {
          if (connector.standard == standard) {
            return partnerInfo.tariffInfo?.firstWhereOrNull(
                (TariffInfo tariffInfo) => tariffInfo.id == connector.tariffId);
          }
        }
      }
    }
    return null;
  }

  static TariffInfo? getTariffInfo(
      {required String? standard, required Station station}) {
    EvEvsConnectorEntity? evEvsConnectorsDetail = station.evEvsConnectorsDetail
        .firstWhereOrNull((element) => element.standard == standard);
    return evEvsConnectorsDetail?.tariffInfo;
  }

  static Future<MapBoxAddressPredictionEntity> getMapboxAddressPredictionList(
      {required String searchText, String? latLng}) async {
    var predictionsResponse = await APIClientConfig.oneAppClient
        .getAddressListBySearchText(
            searchText: searchText,
            accessToken: MAP_BOX_ACCESS_TOKEN,
            latLng: latLng);
    MapBoxAddressPredictionEntity predictions;
    Map<String, dynamic>? decodedJSON;
    try {
      decodedJSON =
          json.decode(predictionsResponse.response!) as Map<String, dynamic>?;
      predictions = MapBoxAddressPredictionEntity.fromJson(decodedJSON!);
    } catch (e) {
      debugPrint(e.toString());
      predictions = MapBoxAddressPredictionEntity(
        features: [],
        attribution: '',
        query: [],
        type: '',
      );
    }
    return predictions;
  }

  //Return open Filter Text Color
  Color getFilterOpenTextColor(
      FilterHelperClass getFilterPartnerOptions, bool data) {
    if (data && getFilterPartnerOptions.isOpenFilterEnabled) {
      return _colorUtil.tertiary15;
    } else {
      return _colorUtil.tertiary05;
    }
  }

  /// Change the background color of the open filter based on the partner Filter Options.
  Color getOpenFilterBackground(
      FilterHelperClass getFilterPartnerOptions, bool data) {
    if (data && getFilterPartnerOptions.isOpenFilterEnabled) {
      return _colorUtil.tertiary00;
    } else if (getFilterPartnerOptions.isOpenFilterEnabled) {
      return _colorUtil.button05b;
    } else {
      return _colorUtil.tertiary10;
    }
  }

  static EvValidationHelper walletAndDriverCheck(
      PartnerEnrollment? partnerEnrollment,
      String? partnerName,
      Station chargeStationLocationInfo,
      bool isLexus) {
    EvValidationHelper? evValidationHelper;
    if (checkWalletFoundOrNot(partnerEnrollment, partnerName, isLexus) ||
        (partnerEnrollment?.wallet.toLowerCase() ==
                EvEnrolmentAPI.WALLET.NOT_FOUND &&
            partnerEnrollment?.partnerStatus
                    .firstWhere((element) =>
                        element.partnerName == EV_PARTNERS.CHARGE_POINT)
                    .status
                    .toLowerCase() ==
                EvEnrolmentAPI.ENROLMENT.DRIVER_ACCOUNT_NOT_FOUND &&
            partnerEnrollment?.partnerStatus
                    .firstWhere(
                        (element) => element.partnerName == EV_PARTNERS.EVGO)
                    .status
                    .toLowerCase() ==
                EvEnrolmentAPI.ENROLMENT.DRIVER_ACCOUNT_NOT_FOUND)) {
      evValidationHelper = !chargeStationLocationInfo.isRoamingPartner
          ? _checkPartnerEnrollment(
              partnerEnrollment ??
                  PartnerEnrollment(guid: '', wallet: '', partnerStatus: []),
              partnerName,
              evValidationHelper,
              chargeStationLocationInfo,
              isLexus)
          : _checkPartnerEnrollment(
              partnerEnrollment ??
                  PartnerEnrollment(guid: '', wallet: '', partnerStatus: []),
              EV_PARTNERS.CHARGE_POINT,
              evValidationHelper,
              chargeStationLocationInfo,
              isLexus);
    } else {
      if (partnerName == EV_PARTNERS.CHARGE_POINT) {
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
          childEventName: VehicleAnalyticsEvent.VEHICLE_EV_WALLET_SET_UP,
        );
        evValidationHelper = EvValidationHelper(
            buttonName: OneAppString.of().setupWallet,
            ev_screens: EV_SCREENS.SETUP_WALLET);
      } else {
        evValidationHelper = EvValidationHelper(
            buttonName: OneAppString.of().loginRegister,
            ev_screens: EV_SCREENS.TERMS_AND_CONDITION);
      }
    }
    return evValidationHelper!;
  }

  static bool checkWalletFoundOrNot(
      PartnerEnrollment? partnerEnrollment, String? partnerName, bool isLexus) {
    if (!isLexus) {
      return partnerEnrollment?.wallet.toLowerCase() ==
              EvEnrolmentAPI.WALLET.FOUND ||
          (partnerName == EV_PARTNERS.EVGO &&
              checkEvgoSubscriptionNotEnded(partnerEnrollment));
    } else {
      return partnerEnrollment?.wallet.toLowerCase() ==
          EvEnrolmentAPI.WALLET.FOUND;
    }
  }

  static bool checkEvgoSubscription() {
    if (Global.getInstance().evgoFreeExpireDate != null) {
      return DateTime.parse(Global.getInstance().evgoFreeExpireDate!) !=
          DateTime.now();
    }
    return true;
  }

  static bool EVgoExpired(PartnerEnrollment? partnerEnrollment) {
    if (partnerEnrollment != null) {
      var evgoInfo = partnerEnrollment.partnerStatus
          .firstWhere((partner) => partner.partnerName == "evgo");
      if (evgoInfo.expiryDate != null &&
          evgoInfo.expiryDate?.isNotEmpty == true) {
        var evgoExpiryDate = evgoInfo.expiryDate;
        try {
          var expiry = DateTime.parse(evgoExpiryDate!);
          final expiryDays = expiry.difference(DateTime.now()).inDays;
          return expiryDays <= 0;
        } catch (e) {
          return false;
        }
      }
    }
    return false;
  }

  static bool checkEvgoSubscriptionNotEnded(
      PartnerEnrollment? partnerEnrollment) {
    if (partnerEnrollment != null) {
      var evgoInfo = partnerEnrollment.partnerStatus
          .firstWhere((partner) => partner.partnerName == "evgo");
      if (evgoInfo.expiryDate != null &&
          evgoInfo.expiryDate?.isNotEmpty == true) {
        var evgoExpiryDate = evgoInfo.expiryDate;
        try {
          var expiry = DateTime.parse(evgoExpiryDate!);
          final expiryDays = expiry.difference(DateTime.now()).inDays;
          return expiryDays >= 0;
        } catch (e) {
          return false;
        }
      }
    }
    return false;
  }

  static EvValidationHelper? _checkPartnerEnrollment(
      PartnerEnrollment partnerEnrollment,
      String? partnerName,
      EvValidationHelper? evValidationHelper,
      Station chargeStationLocationInfo,
      bool isLexus) {
    partnerEnrollment.partnerStatus.forEach((element) {
      bool evgoCheck = element.partnerName == "evgo" &&
          checkEvgoSubscriptionNotEnded(partnerEnrollment) &&
          element.status.toLowerCase() ==
              EvEnrolmentAPI.ENROLMENT.DRIVER_ACCOUNT_FOUND;
      bool evgoExpiredCheck = element.partnerName == "evgo" &&
          !checkEvgoSubscriptionNotEnded(partnerEnrollment) &&
          element.status.toLowerCase() ==
              EvEnrolmentAPI.ENROLMENT.DRIVER_ACCOUNT_FOUND &&
          partnerEnrollment.partnerStatus
                  .firstWhere((element) =>
                      element.partnerName == EV_PARTNERS.CHARGE_POINT)
                  .status
                  .toLowerCase() ==
              EvEnrolmentAPI.ENROLMENT.DRIVER_ACCOUNT_FOUND;
      bool chargePointCheck = element.partnerName == "chargepoint" &&
          partnerEnrollment.partnerStatus
                  .firstWhere((element) =>
                      element.partnerName == EV_PARTNERS.CHARGE_POINT)
                  .status
                  .toLowerCase() ==
              EvEnrolmentAPI.ENROLMENT.DRIVER_ACCOUNT_FOUND;
      bool walletNotFound = partnerEnrollment.wallet.toLowerCase() ==
          EvEnrolmentAPI.WALLET.NOT_FOUND;
      if (element.partnerName == partnerName) {
        if (evgoCheck || evgoExpiredCheck || chargePointCheck) {
          evValidationHelper = EvValidationHelper(
              buttonName: partnerName == EV_PARTNERS.CHARGE_POINT &&
                      !chargeStationLocationInfo.isRoamingEVgo()
                  ? "Unlock Station"
                  : "Start Charging",
              ev_screens: EV_SCREENS.CHARGING_SCREEN);
        } else if (element.partnerName == "evgo" &&
            element.status.toLowerCase() ==
                EvEnrolmentAPI.ENROLMENT.DRIVER_ACCOUNT_FOUND &&
            !checkEvgoSubscriptionNotEnded(partnerEnrollment)) {
          evValidationHelper = EvValidationHelper(
              buttonName: OneAppString.of().RegisterWithChargePoint,
              ev_screens: EV_SCREENS.TERMS_AND_CONDITION);
        } else if ((walletNotFound &&
                partnerName?.toLowerCase() == EV_PARTNERS.CHARGE_POINT) ||
            (walletNotFound &&
                partnerName?.toLowerCase() == EV_PARTNERS.EVGO &&
                EVgoExpired(partnerEnrollment))) {
          FireBaseAnalyticsLogger.logMarketingGroupEvent(
            VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
            childEventName: VehicleAnalyticsEvent.VEHICLE_EV_WALLET_SET_UP,
          );
          evValidationHelper = EvValidationHelper(
              buttonName: OneAppString.of().setupWallet,
              ev_screens: EV_SCREENS.SETUP_WALLET);
        } else {
          evValidationHelper = EvValidationHelper(
              buttonName: OneAppString.of().loginRegister,
              ev_screens: EV_SCREENS.TERMS_AND_CONDITION);
        }
      }
    });
    return evValidationHelper;
  }

  static List<String> getEVSEIdList(Station selectedDealerStation,
      {bool withPhysical = false}) {
    List<String> evseIdList = [];
    final List<Evse>? evses = getEvseList(selectedDealerStation);
    if (evses != null) {
      evses.forEach((Evse element) {
        evseIdList.addAll(prepareEvseConnectorId(element, withPhysical));
      });
    }
    return evseIdList;
  }

  static List<Evse>? getEvseList(Station selectedStation) {
    final PartnerInfo partnerInfo =
        VehicleSearchChargeStationLocationUtil.getNonNrelPartnerInfo(
            selectedStation)!;
    return partnerInfo.evses;
  }

  static List<String> prepareEvseConnectorId(Evse evse, bool withPhysical) {
    List<String> val = [];
    evse.connectors?.forEach((element) {
      String elementLabel = (withPhysical
              ? '${evse.physicalReference} — ${element.friendlyName}'
              : element.id) ??
          "";
      val.add(elementLabel);
    });
    return val;
  }

  static void setEVgoFreeExpireDate(PartnerEnrollment partnerEnrollment) {
    final evgoPartnerStatus = partnerEnrollment.partnerStatus.firstWhereOrNull(
        (partner) =>
            partner.partnerName == EV_PARTNERS.EVGO &&
            partner.status.toUpperCase() == "FOUND");
    final enrolledDate = evgoPartnerStatus?.enrollmentDate;
    if (enrolledDate != null) {
      var parsedDate = DateTime.parse(enrolledDate);
      final evgoFreeChargeDate = parsedDate.add(const Duration(days: 365));
      Global.getInstance().evgoFreeExpireDate =
          evgoFreeChargeDate.toLocal().toString();
    } else {
      Global.getInstance().evgoFreeExpireDate = "";
    }
  }
}

///Filter Helper Class
class FilterHelperClass {
  FilterHelperClass({
    required this.isOpenFilterEnabled,
    required this.name,
    required this.color,
    required this.backgroundColor,
    required this.iconColor,
    this.length,
  });

  bool isOpenFilterEnabled;
  String name;
  Color color;
  int? length;
  Color backgroundColor;
  Color iconColor;
}

/// Setup Wallet button Helper
class EvValidationHelper {
  String buttonName;
  EV_SCREENS ev_screens;

  EvValidationHelper({required this.buttonName, required this.ev_screens});
}

//// Charging Button Helpper Class
class EvChargingButtonHelper {
  String buttonName;
  CHARGING_EV_SCREEN charging_ev_screens;
  Color buttonColor;
  String iconPath;
  Color textColor;
  Color iconColor;
  bool isChargingVisible;

  EvChargingButtonHelper(
      {required this.buttonName,
      required this.charging_ev_screens,
      required this.buttonColor,
      required this.iconPath,
      required this.textColor,
      required this.iconColor,
      required this.isChargingVisible});
}
