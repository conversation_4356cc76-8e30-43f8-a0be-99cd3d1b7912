// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';

class ConnectorIdBottomSelection extends StatefulWidget {
  ConnectorIdBottomSelection({
    Key? key,
    required this.availableConnectors,
    required this.availableConnectorsWithPhyRef,
    required this.selectedConnectors,
  }) : super(key: key);
  final List<String> availableConnectors;
  final List<String> availableConnectorsWithPhyRef;
  final Function(String) selectedConnectors;

  @override
  _ConnectorIdBottomSelectionState createState() =>
      _ConnectorIdBottomSelectionState();
}

class _ConnectorIdBottomSelectionState
    extends State<ConnectorIdBottomSelection> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  int selectedIndex = 0;

  final List<String> selectedConnectors = [];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SwipeBarIcon(),
        bottomSheetAppBar(
          OneAppString.of().plugs,
          elevation: 0,
          backgroundColor: Colors.transparent,
          leadingWidget: Padding(
            padding: EdgeInsets.only(left: 10.w),
            child: CircleAvatar(
              backgroundColor: _colorUtil.button02d,
              radius: 30.r,
              child: IconButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                icon: Icon(Icons.chevron_left),
                color: _colorUtil.tertiary00,
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 10.h),
          child: Text(
            OneAppString.of().selectPlugMessage,
            textAlign: TextAlign.center,
            style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.callout1, _colorUtil.tertiary05),
          ),
        ),
        widget.availableConnectors.isNotEmpty ? _mainContent() : Container(),
        widget.availableConnectors.isNotEmpty
            ? Column(
                children: [
                  Center(
                      child: Text(OneAppString.of().vehiclePlugingMessage,
                          textAlign: TextAlign.center,
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.callout1, _colorUtil.tertiary05))),
                  Padding(
                    padding: EdgeInsets.only(top: 10.h, bottom: 20.h),
                    child: CustomDefaultButton(
                      text: OneAppString.of().continueText,
                      press: () {
                        if (selectedConnectors.isNotEmpty) {
                          widget.selectedConnectors(selectedConnectors[0]);
                        } else {
                          widget.selectedConnectors(
                              widget.availableConnectors[0]);
                        }
                      },
                    ),
                  ),
                ],
              )
            : Container(),
      ],
    );
  }

  Widget _mainContent() {
    return Expanded(
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
          child: ListView.builder(
              physics: NeverScrollableScrollPhysics(),
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemCount: widget.availableConnectors.length,
              itemBuilder: (context, index) {
                return Padding(
                    padding: EdgeInsets.only(
                        top: 10.h, bottom: 10.h, right: 10.w, left: 10.w),
                    child: ListTile(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10.0)),
                      title: Text(
                        widget.availableConnectorsWithPhyRef[index].toString(),
                        textAlign: TextAlign.start,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.body4, _colorUtil.tertiary05),
                      ),
                      tileColor: _colorUtil.tile02,
                      trailing: Icon(
                        Icons.check,
                        color: selectedIndex == index
                            ? _colorUtil.secondary01
                            : _colorUtil.tile02,
                      ),
                      onTap: () {
                        setState(() {
                          selectedIndex = index;
                          if (selectedConnectors.contains(
                              widget.availableConnectors[index].toString())) {
                            selectedConnectors.remove(
                                widget.availableConnectors[index].toString());
                          } else {
                            selectedConnectors.clear();
                            selectedConnectors.add(
                                widget.availableConnectors[index].toString());
                          }

                          debugPrint("selected value:$selectedConnectors");
                        });
                      },
                    ));
              }),
        ),
      ),
    );
  }
}
