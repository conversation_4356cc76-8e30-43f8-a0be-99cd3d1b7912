// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ev_module/charge_info/blocs/partner_details_Info_bloc.dart';
import 'package:ev_module/core/constants.dart';
import 'package:ev_module/log/ev_analytics_events.dart';
import 'package:location/location.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/entity/filterModel.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_wallet_and_enrollment_validation.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/map_box_prediction_entity.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';
import 'package:uuid/uuid.dart';

// Project imports:
import '../../../local_repo/vehicle_repo.dart';
import '../../../log/vehicle_analytic_event.dart';
import '../../../ui/electric_vehicle_management/vehicle_search_charge_station_location/vehicle_search_charge_station_location_util.dart';
import '../ev_common/ev_enum_const_util.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleSearchChargeStationLocationBloc extends BlocBase {
  late Function(bool) progressHandlerCallback;
  bool isDarkMode = false;
  bool isApiLoading = false;
  bool isSingleStationBackPressed = false;
  Function(String)? dialogHandlerCallback;
  Function(EV_SCREENS val, {String errorMessage})? screenActionCallBack;
  String? email;
  String? region = "US";
  String? fuelType = "I";
  String? radius;
  String _initialSearchWord = "";
  bool isPlaceApiLoading = true;
  List<Station> chargeStationsList = [];
  Location _location = Location();
  Position? currentLocation;
  Position? selectedCurrentLocation;
  Position? _selectedCurrentLocation;
  bool isPartnersLocationSelected = false;
  bool isFavouriteLocationSelected = false;
  int totalNumberOfNearByEvStations = 0;
  double initialSizeBottomSheet = 0.63;
  TextEditingController searchController = TextEditingController();
  ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  String partners = "";
  String connectors = "";
  String? placeName = "";
  String currentLatLng = "";
  PositionInfo? _positionInfo;
  vehicleInfo.Payload? vehicleItem;
  bool isLexus = false;

  PartnerDetailsInfoBloc _partnerDetailsInfoBloc;

  VehicleSearchChargeStationLocationBloc()
      : _partnerDetailsInfoBloc = PartnerDetailsInfoBloc();

  PartnerDetailsInfoBloc get partnerDetailsInfoBloc => _partnerDetailsInfoBloc;

  VehicleSearchChargeStationLocationUtil stationLocationUtil =
      VehicleSearchChargeStationLocationUtil();

  static const double KM_TO_MILES_FACTOR = 0.00062137;

  List<FilterContentModel> partnerlist = [];

  List<FilterContentModel> connectorList = [
    FilterContentModel(
        itemName: "J1772",
        friendlyName: "Level 2",
        isSelected: false,
        imagePath: chargerLevel2Icon,
        imagePackage: 'oneapp_common'),
    FilterContentModel(
        itemName: "CCS1",
        friendlyName: "DCFast",
        isSelected: false,
        imagePath: chargerDcFastIcon,
        imagePackage: 'oneapp_common'),
  ];

  Stream<bool> get isPublicChargingControlAllowed =>
      _isPublicChargingControlAllowed.stream;
  final _isPublicChargingControlAllowed = BehaviorSubject<bool>();

  Stream<bool> get isWalletFeatureEnabled => _isWalletFeatureEnabled.stream;
  final _isWalletFeatureEnabled = BehaviorSubject<bool>();

  Stream<bool> get isMyDestinationEnabled => _isMyDestinationEnabled.stream;
  final _isMyDestinationEnabled = BehaviorSubject<bool>();

  Stream<FilterHelperClass?> get filteredPartnerItems =>
      _filteredPartnerItems.stream;
  final _filteredPartnerItems = BehaviorSubject<FilterHelperClass?>();

  Stream<FilterHelperClass?> get filteredConnectorItems =>
      _filteredConnectorItems.stream;
  final _filteredConnectorItems = BehaviorSubject<FilterHelperClass?>();

  Stream<FilterHelperClass?> get filteredConnectorNames =>
      _filteredConnectorNames.stream;
  final _filteredConnectorNames = BehaviorSubject<FilterHelperClass?>();

  final _availableConnectorsWithPhyRef = BehaviorSubject<List<String>>();

  Stream<List<String>> get availableConnectorsWithPhyRef =>
      _availableConnectorsWithPhyRef.stream;

  List<String> get availableConnectorsWithPhyRefAsList =>
      _availableConnectorsWithPhyRef.value;

  final _availableConnectors = BehaviorSubject<List<String>>();

  Stream<List<String>> get availableConnectors => _availableConnectors.stream;

  final _refreshController = StreamController<bool>.broadcast();

  Stream<bool> get refreshApp => _refreshController.stream;

  Stream<bool> get isNightTheme => _isNightTheme.stream;
  final _isNightTheme = BehaviorSubject<bool>();

  Stream<bool> get isEVStationOpen => _isEVStationOpen.stream;
  final _isEVStationOpen = BehaviorSubject<bool>();

  BehaviorSubject<bool> _isWalletExist = BehaviorSubject<bool>();

  Stream<bool> get isWalletExistStream => _isWalletExist.stream;

  Stream<String?> get evConnectorType => _evConnectorType.stream;
  final _evConnectorType = BehaviorSubject<String?>();

  Stream<int> get evConnectorTypeIndex => _evConnectorTypeIndex.stream;
  final _evConnectorTypeIndex = BehaviorSubject<int>();

  Stream<bool> get isFavouriteStationFilterDisabled =>
      _isFavouriteStationFilterDisabled.stream;
  final _isFavouriteStationFilterDisabled = BehaviorSubject<bool>();

  bool get isFavouriteStationSelectedValue =>
      _isFavouriteStationFilterDisabled.value;

  final _showPrefixIcon = BehaviorSubject<bool>();

  Stream<bool> get showPrefixIcon => _showPrefixIcon.stream;

  final _isEvChargeStationLoading = BehaviorSubject<bool>();

  Stream<bool> get isEvStationLoading => _isEvChargeStationLoading.stream;

  bool get isEvStationLoadingAsBool => _isEvChargeStationLoading.hasValue
      ? _isEvChargeStationLoading.value
      : false;

  final _chargeStationList = BehaviorSubject<List<Station>?>();

  Stream<List<Station>?> get chargeStationList => _chargeStationList.stream;

  final _fabPosition = BehaviorSubject<double>();

  Stream<double> get fabPosition => _fabPosition.stream;

  final _showListView = BehaviorSubject<double>();

  Stream<double> get showListView => _showListView.stream;

  //0 - Show preferred dealer, 1 - Show vehicle list, 3 - Show info list
  final _showPreferredVehicleSheet = BehaviorSubject<int>();

  Stream<int> get showPreferredVehicleSheet =>
      _showPreferredVehicleSheet.stream;

  final _selectedChargeStation = BehaviorSubject<Station>();

  Stream<Station> get selectedChargeStation => _selectedChargeStation.stream;

  Station? get selectedChargeStationInfo =>
      _selectedChargeStation.hasValue ? _selectedChargeStation.value : null;

  final _selectedLatLng = BehaviorSubject<Position>();

  Stream<Position> get selectedLatLng => _selectedLatLng.stream;

  final _appLocationDenied = BehaviorSubject<bool>();

  Stream<bool> get appLocationDenied => _appLocationDenied.stream;

  final _placeAutoSearch = BehaviorSubject<MapBoxAddressPredictionEntity?>();

  Stream<MapBoxAddressPredictionEntity?> get placeAutoSearch =>
      _placeAutoSearch.stream;

  final _walletAndEnrollmentValidation = BehaviorSubject<PartnerEnrollment?>();

  Stream<PartnerEnrollment?> get walletAndEnrollmentValidation =>
      _walletAndEnrollmentValidation.stream;

  //0 for accountNotFound , 1 for account Found 2 for New Driver Registration.
  final _accountState = BehaviorSubject<EnrollmentAccountState>();

  Stream<EnrollmentAccountState> get accountState => _accountState.stream;

  final _enrollmentPartnerName = BehaviorSubject<String>();

  Stream<String> get enrollmentPartnerName => _enrollmentPartnerName.stream;

  Future<void> getPlaces(String input) async {
    _placeAutoSearch.sink.add(null);

    ///issue  fix  for  moving current location  after clearing  search result
    ///issue  fix  for keyboard  hopping
    if ((_showListView.hasValue && _showListView.value == 0) && input.isEmpty) {
      return _getCurrentLocation();
    }
    if (input.length > 3) {
      isPlaceApiLoading = true;
      if (currentLocation != null) {
        currentLatLng = '${currentLocation!.lng},${currentLocation!.lat}';
      }
      MapBoxAddressPredictionEntity predictions =
          await VehicleSearchChargeStationLocationUtil
              .getMapboxAddressPredictionList(
                  searchText: input, latLng: currentLatLng);
      if (predictions.features.isNotEmpty) {
        _placeAutoSearch.sink.add(predictions);
      } else {
        _placeAutoSearch.sink.add(null);
        isPlaceApiLoading = false;
      }
    } else {
      _placeAutoSearch.sink.add(null);

      ///issue  fix  no place found error msg unwanted Display
      isPlaceApiLoading = true;
    }
  }

  // Initialize the page by getting current location
  void init(
      Function(bool) progressHandler,
      Function(EV_SCREENS, {String? errorMessage}) screenAction,
      Function(String dialogText) dialogHandler) async {
    isDarkMode = isDarkTheme();
    isApiLoading = true;
    _isNightTheme.sink.add(isDarkMode);
    await refreshAppAction();
    email = Global.getInstance().userEmail;
    progressHandlerCallback = progressHandler;
    screenActionCallBack = screenAction;
    dialogHandlerCallback = dialogHandler;
    _initalExecutionMethod();
  }

  void _initalExecutionMethod() async {
    _filteredPartnerItems.sink.add(FilterHelperClass(
        isOpenFilterEnabled: true,
        name: OneAppString.of().partners,
        color: _colorUtil.tertiary05,
        backgroundColor: _colorUtil.button05b,
        iconColor: _colorUtil.tertiary05));
    _filteredConnectorItems.sink.add(FilterHelperClass(
        isOpenFilterEnabled: true,
        name: OneAppString.of().plugTypes,
        color: _colorUtil.tertiary05,
        backgroundColor: _colorUtil.button05b,
        iconColor: _colorUtil.tertiary05));
    _filteredConnectorNames.sink.add(FilterHelperClass(
        isOpenFilterEnabled: true,
        name: OneAppString.of().plugTypes,
        color: _colorUtil.tertiary05,
        backgroundColor: _colorUtil.button05b,
        iconColor: _colorUtil.tertiary05));
    _isEVStationOpen.sink.add(false);
    _isFavouriteStationFilterDisabled.sink.add(false);
    await fetchVehicleInfo();
    await fetchPartnerFilterData();
    await _getCurrentLocation();
    await checkWalletAndDriverExistence();
    await loadFavourites();
  }

  void updateWalletStatus(bool isFound) {
    _isWalletExist.value = isFound;
  }

  Future<void> fetchVehicleInfo() async {
    String? vin = Global.getInstance().vin;

    vehicleItem = await (VehicleRepo().getLocalPayloadFromVin(vin)) ??
        vehicleInfo.Payload();
    isLexus = vehicleItem?.make.toLowerCase() == makeLexus;
    region = vehicleItem?.region;
    fuelType = vehicleItem?.fuelType;
    radius = (fuelType == FUELTYPE_HYDROGENFUELCELL) ? FCV_RADIUS : PHEV_RADIUS;
    _isPublicChargingControlAllowed.sink
        .add(isEvPublicChargingControlEnabled(vehicleItem?.features!));

    _isWalletFeatureEnabled.sink
        .add(isFeatureEnabled(WALLET, vehicleItem?.features));
    _isMyDestinationEnabled.sink
        .add(isFeatureEnabled(MY_DESTINATION, vehicleItem?.features));
    final evRepository = await VehicleRepo().getEVVehicleInfoRepository(vin);
    final detail = evRepository.lastChargeManagementDetailReceived;
    _positionInfo = detail?.payload?.positionInfo;
  }

  // Notify change in search value to bloc
  void searchValueChanged(String searchValue) {
    try {
      if (searchValue.isNotEmpty) {
        if (!_showPrefixIcon.isClosed) _showPrefixIcon.sink.add(true);
      } else {
        if (!_showPrefixIcon.isClosed) _showPrefixIcon.sink.add(false);
      }

      if (searchValue.trim().isNotEmpty && placeName != searchController.text) {
        if (_initialSearchWord != searchValue.trim()) {
          _initialSearchWord = searchValue.trim();
          toggleSearchView(1);
        }
      }
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString());
    }
  }

  Future<void> _getCurrentLocation() async {
    if (await Permission.location.request().isGranted) {
      bool _serviceEnabled = false;
      _serviceEnabled = await _location.serviceEnabled();
      if (!_serviceEnabled) {
        await _location.requestService();
      }
      Position _currentLocation;
      if (_positionInfo == null ||
          _positionInfo?.latitude == 0 ||
          _positionInfo?.latitude == 0) {
        _currentLocation = await MapUtil.getCurrentLocation();
      } else {
        _currentLocation = Position(
          _positionInfo!.longitude,
          _positionInfo!.latitude,
        );
      }
      currentLocation = _currentLocation;
      selectedCurrentLocation = null;
      fetchChargeLocationListForCurrentLocation();
    } else {
      _appLocationDenied.sink.add(true);
    }
  }

  void fetchChargeLocationListForCurrentLocation() {
    double latitude;
    double longitude;

    if (currentLocation != null && selectedCurrentLocation == null) {
      latitude = currentLocation!.lat.toDouble();
      longitude = currentLocation!.lng.toDouble();
      _selectedLatLng.sink
          .add(VehicleSearchChargeStationLocationUtil.dallasLatLng);
      isPartnersLocationSelected = false;
      isFavouriteLocationSelected = false;
    } else {
      latitude = selectedCurrentLocation!.lat.toDouble();
      longitude = selectedCurrentLocation!.lng.toDouble();
      _selectedLatLng.sink
          .add(VehicleSearchChargeStationLocationUtil.dallasLatLng);
      isPartnersLocationSelected = false;
      isFavouriteLocationSelected = false;
    }
    fetchEvChargingStationList(
      latitude,
      longitude,
      connectors: connectors,
      partners: partners,
      isOpen: _isEVStationOpen.value,
    );
  }

  //0 - Show preferred dealer, 1 - Show vehicle list, 3 - Show info list
  // Hide/ Show list view and map view
  void toggleSearchView(double toggleValue) {
    _showListView.sink.add(toggleValue);
  }

  Future<void> checkWalletAndDriverExistence() async {
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();

    final evApi = APIClientConfig.evApiClient;
    String email = Global.getInstance().userEmail ?? '';
    String guid = Global.getInstance().guid ?? '';
    final commonResponse = await evApi.checkWalletAndDriverExistence(
      email: email,
      guid: guid,
      make: vehicleInfo?.make ?? '',
    );
    final response = commonResponse.response;
    final partnerEnrollment = response?.payload.partnerEnrollment;
    if (partnerEnrollment != null) {
      _walletAndEnrollmentValidation.sink.add(partnerEnrollment);
      VehicleSearchChargeStationLocationUtil.setEVgoFreeExpireDate(
          partnerEnrollment);
    }
  }

  /// Fetch Dealers list using lat long
  Future<void> fetchEvChargingStationList(
    double latitude,
    double longitude, {
    int offset = 0,
    String connectors = "",
    String partners = "",
    bool isOpen = false,
  }) async {
    if (offset == 0) {
      _chargeStationList.sink.add([]);
      chargeStationsList = [];
    }
    final evApi = APIClientConfig.evApiClient;
    CommonResponse? commonResponse;
    String logRequestId = Uuid().v4();
    logRequestId = logRequestId.substring(logRequestId.length - 5);

    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();

    /// Api is about to Call
    try {
      isApiLoading = true;
      commonResponse = await evApi.fetchEVChargeStationLocation(
        latitude: latitude,
        longitude: longitude,
        offset: offset,
        connectors: connectors,
        partners: partners,
        isOpen: isOpen,
        limit: 40,
        radius: 10,
        make: vehicleInfo!.make,
      );
      isApiLoading = false;
    } on Exception catch (e) {
      isApiLoading = false;
      progressHandlerCallback(false);
      debugPrint(e.toString());
    }

    final response = commonResponse!.response;

    FireBaseAnalyticsLogger.logInfo(
        VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_STATION_LIST_SUCCESS);
    if (response?.payload != null) {
      List<Station>? dealerList = response.payload.stations;
      totalNumberOfNearByEvStations = response.payload.totalNoOfRecords ?? 20;
      if (dealerList != null && dealerList.isNotEmpty) {
        FireBaseAnalyticsLogger.logInfo(
            "at=fetchEvChargingStationList status=has-stations logId=$logRequestId count=$totalNumberOfNearByEvStations");
        if (currentLocation != null) {
          await Future.forEach(dealerList, (Station item) async {
            addDistanceToCurrentLocation(item);
          });
          FireBaseAnalyticsLogger.logInfo(
              "at=fetchEvChargingStationList status=distance-calculation logId=$logRequestId lat=${currentLocation!.lat} lng=${currentLocation!.lng}");
        }

        // dealerList.sort((a, b) => a.distance.compareTo(b.distance));
        final placeIdFavorites = await _mapPlaceIdsToFavourites(dealerList);
        for (final Station station in dealerList) {
          station.isFavourite = placeIdFavorites[station.placeId] ?? false;
          VehicleSearchChargeStationLocationUtil.updateCounterOnStation(
              station);
        }

        chargeStationsList.addAll(dealerList);
        FireBaseAnalyticsLogger.logInfo(
            "at=fetchEvChargingStationList status=total-list logId=$logRequestId count=${chargeStationsList.length}");
        if (_isFavouriteStationFilterDisabled.value) {
          _chargeStationList.sink.add(
              VehicleSearchChargeStationLocationUtil.getFavStations(
                  dealerList));
        } else {
          _chargeStationList.sink.add(chargeStationsList);
        }
      } else {
        FireBaseAnalyticsLogger.logInfo(
            "at=fetchEvChargingStationList status=no-stations logId=$logRequestId");
        progressHandlerCallback(false);
        _chargeStationList.sink.add([]);
      }
    } else {
      FireBaseAnalyticsLogger.logInfo(
          "at=fetchEvChargingStationList status=payload-empty logId=$logRequestId");
      progressHandlerCallback(false);
      _chargeStationList.sink.add([]);
    }

    progressHandlerCallback(false);
    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logInfo(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_STATION_LIST_FAILURE);
      _chargeStationList.sink.add([]);
    }
  }

  Future<Map<String, bool>> _mapPlaceIdsToFavourites(
      List<Station> dealerList) async {
    final placeIds = dealerList.map((e) => e.placeId).toList();
    placeIds.retainWhere((element) => element != null);
    final placeIdFavorites =
        await isFavourite(placeIds.whereType<String>().toList());
    return placeIdFavorites;
  }

  void addDistanceToCurrentLocation(Station item) async {
    if (item.geometry.coordinates.length > 1) {
      double latitude;
      double longitude;
      if (currentLocation != null && selectedCurrentLocation == null) {
        latitude = currentLocation!.lat.toDouble();
        longitude = currentLocation!.lng.toDouble();
      } else {
        latitude = selectedCurrentLocation!.lat.toDouble();
        longitude = selectedCurrentLocation!.lng.toDouble();
      }

      double distance = (await MapUtil.distanceBetween(
            item.geometry.coordinates[1],
            item.geometry.coordinates[0],
            latitude,
            longitude,
          ) *
          KM_TO_MILES_FACTOR);
      double stationDistance = double.parse(distance.toStringAsFixed(2));
      item.distance = stationDistance;
    }
  }

  // Change fab position according to bottom sheet
  void changeFabPosition(double fabPosition) {
    _fabPosition.sink.add(fabPosition);
  }

  /// Logic to search dealer
  void searchDealer(Features searchItem) {
    try {
      _chargeStationList.sink.add([]);
      if (placeName != searchController.text) {
        /// Go To Search Dealer Page
        toggleSearchView(0);
        placeName = searchItem.placeName;
        searchController.text = searchItem.placeName;
        selectedCurrentLocation = Position(
          searchItem.geometry.coordinates[0],
          searchItem.geometry.coordinates[1],
        );
        fetchEvChargingStationList(
          searchItem.geometry.coordinates[1], // Mapbox uses lng, lat
          searchItem.geometry.coordinates[0],
          connectors: connectors,
          partners: partners,
          isOpen: _isEVStationOpen.value,
        );
      } else {
        toggleSearchView(0);
      }
    } catch (e) {
      isApiLoading = false;
      FireBaseAnalyticsLogger.logError(e.toString());
    }
  }

  // Present selected dealer in bottom sheet
  void selectedDealer({
    required Station selectedDealerStation,
    int bottomSheetId = 0,
  }) async {
    final evApi = APIClientConfig.evApiClient;
    _showPreferredVehicleSheet.sink.add(bottomSheetId);
    Position vehicleLoc = Position(
        selectedDealerStation.geometry.coordinates[0],
        selectedDealerStation.geometry.coordinates[1]);
    _selectedLatLng.sink.add(vehicleLoc);
    _selectedCurrentLocation = selectedCurrentLocation;
    selectedCurrentLocation = null;
    isPartnersLocationSelected =
        selectedDealerStation.isPartner(_isWalletFeatureEnabled.value);
    isFavouriteLocationSelected = selectedDealerStation.isFavourite;
    selectedDealerStation.usePlaceId = true;
    List<Station> chargeStationList = List.from(chargeStationsList);
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();

    _chargeStationList.sink.add(chargeStationList);
    _selectedChargeStation.sink.add(selectedDealerStation);
    try {
      final commonResponse = await evApi.getStationById(
        stationId: selectedDealerStation.id,
        make: vehicleInfo!.make,
      );
      final response = commonResponse.response!;
      final payload = response.payload;
      final stations = payload.stations;
      if (stations.isNotEmpty) {
        final placeIdFavorites = await _mapPlaceIdsToFavourites(stations);
        final updatedDealerStation = stations.first;
        updatedDealerStation.isFavourite =
            placeIdFavorites[updatedDealerStation.placeId] ?? false;
        VehicleSearchChargeStationLocationUtil.updateCounterOnStation(
            updatedDealerStation);
        addDistanceToCurrentLocation(updatedDealerStation);
        _selectedChargeStation.sink.add(updatedDealerStation);
      }
      VehicleSearchChargeStationLocationUtil.setFavouriteStation(
          selectedDealerStation.poiText, isFavouriteLocationSelected);
    } on Exception catch (e) {
      isApiLoading = false;
      debugPrint(e.toString());
    }
  }

  /// This function is kept for Future implementation
  void openWebPage(String url) async {
    // urlLauncher(url);
  }

  void favouriteFilter() {
    bool isFavouriteStations = !_isFavouriteStationFilterDisabled.value;
    _isFavouriteStationFilterDisabled.sink.add(isFavouriteStations);
    fetchChargeLocationListForCurrentLocation();
  }

  void resetFilters() {
    _filteredPartnerItems.sink.add(getPartnerFilter());
    partners = "";
    partnerlist = VehicleSearchChargeStationLocationUtil.selectAllPartnerFilter(
        partnerlist, false);
    _filteredConnectorItems.sink.add(getPlugTypeFilter());
    _filteredConnectorNames.sink.add(getPlugTypeFilter());
    connectors = "";
    connectorList =
        VehicleSearchChargeStationLocationUtil.selectAllPlugTypeFilter(
            connectorList, false);
    _isFavouriteStationFilterDisabled.sink.add(false);
    fetchChargeLocationListForCurrentLocation();
  }

  FilterHelperClass getPlugTypeFilter() {
    return FilterHelperClass(
        isOpenFilterEnabled: false,
        name: OneAppString.of().plugTypes,
        color: _colorUtil.tertiary05,
        backgroundColor: _colorUtil.button05b,
        iconColor: _colorUtil.tertiary05);
  }

  FilterHelperClass getPartnerFilter() {
    return FilterHelperClass(
        isOpenFilterEnabled: false,
        name: OneAppString.of().partners,
        color: _colorUtil.tertiary05,
        backgroundColor: _colorUtil.button05b,
        iconColor: _colorUtil.tertiary05);
  }

  void toggleFavouriteStation({
    required Station selectedStation,
    required int bottomSheetId,
  }) async {
    selectedStation.usePlaceId = true;
    if (bottomSheetId == 1 && chargeStationsList.contains(selectedStation)) {
      final chargeStationToUpdate =
          chargeStationsList[chargeStationsList.indexWhere((element) {
        return element.id == selectedStation.id;
      })];
      chargeStationToUpdate.isFavourite = !selectedStation.isFavourite;
      _chargeStationList.sink.add(chargeStationsList);
      VehicleSearchChargeStationLocationUtil.setFavouriteStation(
          chargeStationToUpdate.poiText, chargeStationToUpdate.isFavourite);
      return;
    }
    selectedStation.isFavourite = !selectedStation.isFavourite;
    selectedDealer(selectedDealerStation: selectedStation);
    return;
  }

  Future refreshAppAction() {
    return Future(() {
      _refreshController.add(isDarkMode);
    });
  }

  //Todo: Need to implement Dialer. Commented because no contact number from API
  // void openPhoneDialer() async {
  //   dialerLauncher(_selectedChargeStation
  //       .value.source.partnerData.phone); // Need To be changed as Phone
  // }

  // Todo: Need to implement Open Map.
  // void openMapPage() async {
  //   String googleMapLocationUrl =
  //       "https://www.google.com/maps/search/?api=1&query=${_selectedChargeStation.value.source.geometry.coordinates[1]},${_selectedChargeStation.value.source.geometry.coordinates[0]}";
  //   urlLauncher(googleMapLocationUrl);
  // }

  // Handle back press only when selected dealer bottom sheet is open
  Future<bool> backPressHandler(Function backPressCallback) async {
    bool shouldPop = false;
    isSingleStationBackPressed = true;
    _selectedLatLng.sink
        .add(VehicleSearchChargeStationLocationUtil.dallasLatLng);
    selectedCurrentLocation = null;
    if (_showListView.valueOrNull == 1) {
      toggleSearchView(0);
    } else if (_showPreferredVehicleSheet.valueOrNull == 0) {
      _showPreferredVehicleSheet.sink.add(1);
      _chargeStationList.sink.add(null);
      selectedCurrentLocation = _selectedCurrentLocation;
      _chargeStationList.sink.add(chargeStationsList);
    } else if (_showPreferredVehicleSheet.valueOrNull == 2) {
      _showPreferredVehicleSheet.sink.add(1);
    } else {
      shouldPop = true;
      backPressCallback();
    }
    return shouldPop;
  }

  String prepareLine2Address(
      {String city = "", String province = "", String postalCode = ""}) {
    return "$city, $province $postalCode";
  }

  void loadNewStationList() async {
    _isEvChargeStationLoading.value = true;
    double latitude;
    double longitude;
    if (currentLocation != null && selectedCurrentLocation == null) {
      latitude = currentLocation!.lat.toDouble();
      longitude = currentLocation!.lng.toDouble();
    } else {
      latitude = selectedCurrentLocation!.lat.toDouble();
      longitude = selectedCurrentLocation!.lng.toDouble();
    }
    await fetchEvChargingStationList(
      latitude,
      longitude,
      offset: chargeStationsList.length,
      connectors: connectors,
      partners: partners,
      isOpen: _isEVStationOpen.value,
    );
    _isEvChargeStationLoading.value = false;
  }

  void onFilterConnectorItemsChange(
    List<String> selectedValues,
    List<String> selectedNames,
  ) {
    _filteredConnectorItems.sink.add(null);
    _filteredConnectorNames.sink.add(null);
    connectors = "";
    if (selectedValues.isNotEmpty) {
      FilterHelperClass filterHelper =
          stationLocationUtil.getConnectorFilterDetails(selectedValues);
      _filteredConnectorItems.sink.add(filterHelper);
      connectors = selectedValues.join(',').toString();
      FilterHelperClass namesHelper =
          stationLocationUtil.getConnectorFilterDetails(selectedNames);
      _filteredConnectorNames.sink.add(namesHelper);
    } else {
      _filteredConnectorItems.sink.add(FilterHelperClass(
          isOpenFilterEnabled: false,
          name: OneAppString.of().plugTypes,
          color: _colorUtil.tertiary05,
          backgroundColor: _colorUtil.button05b,
          iconColor: _colorUtil.tertiary05));
      _filteredConnectorNames.sink.add(FilterHelperClass(
          isOpenFilterEnabled: false,
          name: OneAppString.of().plugTypes,
          color: _colorUtil.tertiary05,
          backgroundColor: _colorUtil.button05b,
          iconColor: _colorUtil.tertiary05));
    }
    fetchChargeLocationListForCurrentLocation();
  }

  void onFilterPartnerItemsChange(List<String> selectedValues) {
    _filteredPartnerItems.sink.add(null);
    partners = "";
    if (selectedValues.isNotEmpty) {
      FilterHelperClass? filterHelper =
          stationLocationUtil.getPartnerFilterDetails(selectedValues);
      _filteredPartnerItems.sink.add(filterHelper);
      //if lexus
      List<String> roamingPartnerValues = [];
      if (vehicleItem?.make.toLowerCase() == makeLexus) {
        selectedValues.forEach((element) {
          if (element.toLowerCase() == chargePointLowerCase) {
            roamingPartnerValues.add(element);
          } else {
            roamingPartnerValues.add("$chargePointLowerCase@$element");
          }
        });
        partners = roamingPartnerValues.join(',').toString();
      } else {
        partners = selectedValues.join(',').toString();
      }
      FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
          childEventName: EVAnalyticsEvent.FILTER_PARTNER_TYPE);
    } else {
      _filteredPartnerItems.sink.add(FilterHelperClass(
        isOpenFilterEnabled: true,
        name: OneAppString.of().partners,
        color: _colorUtil.tertiary05,
        backgroundColor: _colorUtil.button05b,
        iconColor: _colorUtil.tertiary05,
      ));
    }
    fetchChargeLocationListForCurrentLocation();
  }

  void isOpen() {
    partners = "";
    _isEVStationOpen.value = !_isEVStationOpen.value;
    _isEVStationOpen.sink.add(_isEVStationOpen.value);
    partnerlist = VehicleSearchChargeStationLocationUtil.selectAllPartnerFilter(
        partnerlist, _isEVStationOpen.value);
    List<String> val =
        VehicleSearchChargeStationLocationUtil.selectedPatener(partnerlist);
    if (val.isNotEmpty) {
      FilterHelperClass? filterHelper =
          stationLocationUtil.getPartnerFilterDetails(val);
      partners = val.join(',').toString();
      _filteredPartnerItems.sink.add(filterHelper);
    } else {
      _filteredPartnerItems.sink.add(FilterHelperClass(
          isOpenFilterEnabled: true,
          name: OneAppString.of().partners,
          color: _colorUtil.tertiary05,
          backgroundColor: _colorUtil.button05b,
          iconColor: _colorUtil.tertiary05));
    }

    fetchChargeLocationListForCurrentLocation();
  }

  void getConnectosName(String ct) async {
    _evConnectorType.sink.add(null);
    _evConnectorType.sink.add(ct);
  }

  void getTariffData(int index, String? standard) {
    _evConnectorTypeIndex.sink.add(index);
    _evConnectorType.sink.add(standard);
  }

  void updateEvogoConnectorDetails() {
    final String partnerName =
        VehicleSearchChargeStationLocationUtil.getPartnerTypeText(
            _selectedChargeStation.value);
    if (partnerName.toLowerCase() == EV_PARTNERS.EVGO) {
      List<String> availableConnectors =
          VehicleSearchChargeStationLocationUtil.getEVSEIdList(
              _selectedChargeStation.value,
              withPhysical: false);
      List<String> availablePlugs =
          VehicleSearchChargeStationLocationUtil.getEVSEIdList(
              _selectedChargeStation.value,
              withPhysical: true);
      _availableConnectorsWithPhyRef.sink.add(availablePlugs);
      _availableConnectors.sink.add(availableConnectors);
    }
  }

  Future<void> fetchPartnerFilterData() async {
    partnerlist = [
      //Needs to be dynamic from the filter API
      FilterContentModel(
          itemName: OneAppString.of().chargePoint, isSelected: false),
      FilterContentModel(itemName: partnerFilterEVConnect, isSelected: false),
      FilterContentModel(itemName: partnerFilterEVgo, isSelected: false),
      FilterContentModel(itemName: partnerFilterFLONetwork, isSelected: false),
      FilterContentModel(itemName: partnerFilterGreenlots, isSelected: false),
      FilterContentModel(
          itemName: partnerFilterShellRecharge, isSelected: false),
    ];
  }

  @override
  void dispose() {
    _refreshController.close();
    _isNightTheme.close();
    _showPrefixIcon.close();
    _chargeStationList.close();
    _fabPosition.close();
    _showListView.close();
    _showPreferredVehicleSheet.close();
    _selectedChargeStation.close();
    _selectedLatLng.close();
    _appLocationDenied.close();
    _isEvChargeStationLoading.close();
    _isWalletExist.close();
    _isEVStationOpen.close();
    _filteredPartnerItems.close();
    _filteredConnectorItems.close();
    _placeAutoSearch.close();
    _isFavouriteStationFilterDisabled.close();
    _evConnectorType.close();
    _evConnectorTypeIndex.close();
    _walletAndEnrollmentValidation.close();
    _accountState.close();
    _availableConnectors.close();
    _availableConnectorsWithPhyRef.close();
    _enrollmentPartnerName.close();
    _filteredConnectorNames.close();
    _isPublicChargingControlAllowed.close();
    _isWalletFeatureEnabled.close();
    _isMyDestinationEnabled.close();
  }

  void searchOnSubmit(String value) async {
    debugPrint(value);
    final entity = await _placeAutoSearch.first;
    final features = entity?.features;
    if (entity != null &&
        features != null &&
        features.isNotEmpty &&
        features.first.placeType.first == "postcode") {
      if (_showListView.value == 1) {
        toggleSearchView(0);
      }
      selectedCurrentLocation = Position(
        features.first.geometry.coordinates[0],
        features.first.geometry.coordinates[1],
      );
      fetchChargeLocationListForCurrentLocation();
    }
  }
}
