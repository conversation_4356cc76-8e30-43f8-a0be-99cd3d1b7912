// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';

// Project imports:
import '../../../log/vehicle_analytic_event.dart';

class VehicleSearchChargeStationLocationMap extends StatefulWidget {
  const VehicleSearchChargeStationLocationMap({
    Key? key,
    this.chargeStationInfoList,
    this.selectedLocation,
    this.currentLocation,
    this.pinClickListener,
    this.favouriteLocation,
    this.selectedCurrentLocation,
  }) : super(key: key);

  final List<Station>? chargeStationInfoList;
  final Position? selectedLocation;
  final Position? currentLocation;
  final Function? pinClickListener;
  final bool? favouriteLocation;
  final Position? selectedCurrentLocation;

  @override
  VehicleSearchChargeStationLocationMapState createState() =>
      VehicleSearchChargeStationLocationMapState();
}

class VehicleSearchChargeStationLocationMapState
    extends State<VehicleSearchChargeStationLocationMap> {
  late MapboxMap mapboxMap;

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logInfo(
      VehicleAnalyticsEvent.VEHICLE_PREFERRED_SERVICE_MAP_PAGE,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.chargeStationInfoList == null ||
        widget.chargeStationInfoList!.isEmpty) {
      return shimmerRectangle(
          double.maxFinite, double.maxFinite, CARD_RADIUS_SMALL);
    } else {
      return MapWidget(
        styleUri: isDarkTheme() ? MapboxStyles.DARK : MapboxStyles.LIGHT,
        androidHostingMode: AndroidPlatformViewHostingMode.TLHC_HC,
        onMapCreated: _onMapCreated,
        cameraOptions: CameraOptions(
          zoom: 12,
          center: Point(
            coordinates: widget.currentLocation ?? TOYOTA_HEADQUARTERS_POSITION,
          ),
        ),
      );
    }
  }

  Future<void> _onMapCreated(MapboxMap mapboxMap) async {
    this.mapboxMap = mapboxMap;
    await mapboxMap.scaleBar.updateSettings(ScaleBarSettings(enabled: false));
    await _setMapBounds();
    await _addMarkers();
  }

  Future<void> _addMarkers() async {
    if (widget.chargeStationInfoList != null) {
      final stations = widget.chargeStationInfoList!;
      final manager =
          await mapboxMap.annotations.createPointAnnotationManager();
      if (widget.currentLocation != null) {
        final currentLocation = widget.currentLocation!;
        final currentLocationMarkerIcon = await _getIconAsset(evParkPin);
        final currentLocationPoint = PointAnnotationOptions(
          geometry: Point(coordinates: currentLocation),
          image: currentLocationMarkerIcon,
        );
        await manager.create(currentLocationPoint);
      }
      final annotationsMap = <String, Station>{};
      for (final station in stations) {
        final markerIcon = await _getIconAsset(
          station.isFavourite ? evFavouritePin : evBlueChargeStationPin,
        );
        final coordinates = station.geometry.coordinates.toPosition;
        final point = PointAnnotationOptions(
          geometry: Point(coordinates: coordinates),
          image: markerIcon,
        );
        final annotation = await manager.create(point);
        annotationsMap[annotation.id] = station;
      }
      manager.addOnPointAnnotationClickListener(
        MarkerClickListener(widget.pinClickListener, annotationsMap),
      );
    }
  }

  Future<Uint8List> _getIconAsset(String assetName) async {
    final bytes = await rootBundle.load(assetName);
    return bytes.buffer.asUint8List();
  }

  Future<void> _setMapBounds() async {
    final bounds = _calculateMapBounds(widget.chargeStationInfoList!);
    final camera = await mapboxMap.cameraForCoordinateBounds(
      bounds,
      MbxEdgeInsets(top: 40, left: 60, bottom: 40, right: 60),
      0,
      0,
      null,
      null,
    );
    mapboxMap.setCamera(camera);
  }

  CoordinateBounds _calculateMapBounds(List<Station> chargingStations) {
    double? northEastLat, northEastLong, southWestLat, southWestLong;
    final locations = chargingStations
        .map(
          (e) => e.geometry.coordinates.toLatLng,
        )
        .toList();
    for (final item in locations) {
      if (northEastLat == null ||
          northEastLong == null ||
          southWestLat == null ||
          southWestLong == null) {
        northEastLat = southWestLat = item.latitude;
        northEastLong = southWestLong = item.longitude;
      }
      if (item.latitude > northEastLat) {
        northEastLat = item.latitude;
      }
      if (item.latitude < southWestLat) {
        southWestLat = item.latitude;
      }
      if (item.longitude > northEastLong) {
        northEastLong = item.longitude;
      }
      if (item.longitude < southWestLong) {
        southWestLong = item.longitude;
      }
    }
    final northEast =
        Point(coordinates: Position(northEastLong!, northEastLat!));
    final southWest =
        Point(coordinates: Position(southWestLong!, southWestLat!));
    return CoordinateBounds(
      southwest: southWest,
      northeast: northEast,
      infiniteBounds: true,
    );
  }
}

extension on List<double> {
  Position get toPosition => Position(this[0], this[1]);
  LatLng get toLatLng => LatLng(this[1], this[0]);
}

class LatLng {
  const LatLng(this.latitude, this.longitude);

  factory LatLng.fromPosition(Position position) =>
      LatLng(position.lat.toDouble(), position.lng.toDouble());

  final double latitude;
  final double longitude;

  Position toPosition() => Position(longitude, latitude);

  Point toPoint() => Point(coordinates: toPosition());

  @override
  String toString() => 'LatLng($latitude, $longitude)';
}

class MarkerClickListener extends OnPointAnnotationClickListener {
  final Function? pinClickListener;
  final Map<String, Station> annotationsMap;

  MarkerClickListener(this.pinClickListener, this.annotationsMap);

  @override
  void onPointAnnotationClick(PointAnnotation annotation) {
    if (pinClickListener != null && annotationsMap.containsKey(annotation.id)) {
      pinClickListener?.call(annotationsMap[annotation.id]);
    }
  }
}
