// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';

// Project imports:
import '../ev_common/ev_enum_const_util.dart';
import 'vehicle_search_charge_station_location_bloc.dart';
import 'vehicle_search_charge_station_location_util.dart';

class VehicleSearchChargeTariffScreen extends StatefulWidget {
  final Station selectedChargeStation;
  final ScrollController? scrollController;
  final VehicleSearchChargeStationLocationBloc bloc;

  const VehicleSearchChargeTariffScreen(
      {Key? key,
      required this.selectedChargeStation,
      required this.scrollController,
      required this.bloc})
      : super(key: key);

  @override
  _VehicleSearchChargeTariffScreenState createState() =>
      _VehicleSearchChargeTariffScreenState();
}

class _VehicleSearchChargeTariffScreenState
    extends State<VehicleSearchChargeTariffScreen> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: _colorUtil.bottomSheetDecorator(
            backgroundColor: _colorUtil.tertiary15),
        child: Column(children: [
          Expanded(
              child: SingleChildScrollView(
            child: Column(
              children: [
                bottomSheetAppBar(
                  OneAppString.of().pricingHeading,
                  elevation: 0,
                  backgroundColor: Colors.transparent,
                  onBackPressed: () {},
                  leadingWidget: IconButton(
                      icon: Icon(
                        Icons.chevron_left,
                        color: _colorUtil.button02a,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      }),
                ),
                SizedBox(
                  height: 20.h,
                ),

                /// Displaying the Connectors Details
                Visibility(
                  visible: widget
                      .selectedChargeStation.evEvsConnectorsDetail.isNotEmpty,
                  child: StreamBuilder<int>(
                      stream: widget.bloc.evConnectorTypeIndex,
                      builder: (context, snapshot) {
                        int? indexId = (snapshot.hasData &&
                                snapshot.data != null &&
                                widget.selectedChargeStation
                                    .evEvsConnectorsDetail.isNotEmpty)
                            ? snapshot.data
                            : 0;
                        return Container(
                          height: 40.h,
                          child: ListView.builder(
                              itemCount: widget.selectedChargeStation
                                  .evEvsConnectorsDetail.length,
                              scrollDirection: Axis.horizontal,
                              itemBuilder: (context, index) {
                                final int id = index;
                                return Padding(
                                  padding:
                                      EdgeInsets.only(left: 22.w, right: 22.w),
                                  child: InkWell(
                                    child: Container(
                                      height: 40.h,
                                      width: 150.w,
                                      decoration: BoxDecoration(
                                          color: (id == indexId)
                                              ? _colorUtil.tertiary00
                                              : _colorUtil.button05b,
                                          borderRadius:
                                              BorderRadius.circular(40)),
                                      child: Center(
                                        child: Text(
                                            widget
                                                .selectedChargeStation
                                                .evEvsConnectorsDetail[index]
                                                .friendlyName!,
                                            style: TextStyleExtension()
                                                .newStyleWithColor(
                                                    _textStyleUtil.callout1,
                                                    (id == indexId)
                                                        ? _colorUtil.tertiary15
                                                        : _colorUtil
                                                            .tertiary00)),
                                      ),
                                    ),
                                    onTap: () {
                                      widget.bloc.getTariffData(
                                          index,
                                          widget
                                              .selectedChargeStation
                                              .evEvsConnectorsDetail[index]
                                              .standard);
                                    },
                                  ),
                                );
                              }),
                        );
                      }),
                ),
                _buildEnergyAndPricing(widget.selectedChargeStation)
              ],
            ),
          ))
        ]));
  }

  String getEVgoFreeDisplayDate() {
    final DateFormat formatter = DateFormat('MMM dd, yyyy');
    final freeChargeExpireDate =
        DateTime.parse(Global.getInstance().evgoFreeExpireDate!);
    final String freeChargeExpireDisplayDate =
        formatter.format(freeChargeExpireDate).toString();
    return freeChargeExpireDisplayDate;
  }

  ///Tariff Pricing and Energy Details
  _buildEnergyAndPricing(Station selectedChargeStation) {
    final prizeInfo = VehicleSearchChargeStationLocationUtil.preparePrizeInfo(
        selectedChargeStation);
    final partnerName =
        VehicleSearchChargeStationLocationUtil.getPartnerTypeText(
            selectedChargeStation,
            toLowerCase: true);
    return Container(
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(left: 16.h, right: 16.h, top: 5.h),
          child: Column(
            children: [
              Visibility(
                visible: prizeInfo['energy']!.isEmpty &&
                    prizeInfo['parking']!.isEmpty,
                child: Padding(
                    padding: EdgeInsets.only(top: 40.h),
                    child: Center(
                      child: Text(
                          selectedChargeStation.isRoamingEVgo()
                              ? OneAppString.of().noTariffText
                              : (partnerName == EV_PARTNERS.EVGO &&
                                      (Global.getInstance()
                                              .evgoFreeExpireDate !=
                                          null))
                                  ? OneAppString.of()
                                      .evgoTariffMessageWithEnrollmentDate(
                                          getEVgoFreeDisplayDate())
                                  : (partnerName == EV_PARTNERS.EVGO)
                                      ? OneAppString.of().evgoTariffMessage
                                      : OneAppString.of().noTariffText,
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.callout1, _colorUtil.tertiary05),
                          textAlign: TextAlign.center),
                    )),
              ),
              Visibility(
                visible: prizeInfo['energy']!.isNotEmpty,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                        top: 20.h,
                      ),
                      child: Align(
                        alignment: Alignment.topLeft,
                        child: SizedBox(
                          height: 50.h,
                          width: 340.w,
                          child: Container(
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                                color: _colorUtil.tile02,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(10))),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                OneAppString.of().energy,
                                maxLines: 1,
                                style: TextStyleExtension().newStyleWithColor(
                                    _textStyleUtil.body4,
                                    _colorUtil.tertiary03),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    ListView.builder(
                        shrinkWrap: true,
                        itemCount: prizeInfo['energy']!.length,
                        scrollDirection: Axis.vertical,
                        itemBuilder: (context, index) {
                          return ListTile(
                            leading: Text(
                              prizeInfo['energy']![index]['label'],
                              maxLines: 1,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout2,
                                  _colorUtil.tertiary03),
                              textAlign: TextAlign.start,
                            ),
                            trailing: Text(
                              prizeInfo['energy']![index]['value'],
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout1,
                                  _colorUtil.tertiary05),
                            ),
                          );
                        }),
                  ],
                ),
              ),
              SizedBox(
                height: 10.h,
              ),
              Visibility(
                visible: prizeInfo['parking']!.isNotEmpty,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                        top: 5.h,
                      ),
                      child: Align(
                        alignment: Alignment.topLeft,
                        child: SizedBox(
                          height: 50.h,
                          width: 340.w,
                          child: Container(
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                                color: _colorUtil.tile02,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(10))),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                OneAppString.of().parking,
                                maxLines: 1,
                                style: TextStyleExtension().newStyleWithColor(
                                    _textStyleUtil.body4,
                                    _colorUtil.tertiary03),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    ListView.builder(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: prizeInfo['parking']!.length,
                        scrollDirection: Axis.vertical,
                        itemBuilder: (context, index) {
                          return ListTile(
                            leading: Text(
                              prizeInfo['parking']![index]['label'],
                              maxLines: 1,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout2,
                                  _colorUtil.tertiary03),
                              textAlign: TextAlign.start,
                            ),
                            trailing: Text(
                              prizeInfo['parking']![index]['value'],
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout1,
                                  _colorUtil.tertiary05),
                            ),
                          );
                        }),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
