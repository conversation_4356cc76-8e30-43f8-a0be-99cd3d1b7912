// Flutter imports:

// Dart imports:
import 'dart:convert';
import 'dart:math';

// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' hide SearchBar;

// Package imports:
import 'package:app_settings/app_settings.dart';
import 'package:ev_module/charge_info/blocs/partner_details_Info_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' hide Visibility;
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/debouncer.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/custom_divider.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_common/widget/dialog/bottom_dynamic_confirmation_dialog.dart';
import 'package:oneapp_common/widget/dialog/bottom_error_conformation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/form/search_bar.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_wallet_and_enrollment_validation.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/map_box_prediction_entity.dart';

// Project imports:
import '../../../log/vehicle_analytic_event.dart';
import '../../../ui/electric_vehicle_management/vehicle_search_charge_station_location/vehicle_search_charge_station_location_bloc.dart';
import '../../../ui/electric_vehicle_management/vehicle_search_charge_station_location/vehicle_search_charge_station_location_list_card.dart';
import '../../../ui/electric_vehicle_management/vehicle_search_charge_station_location/vehicle_search_charge_station_location_map.dart';
import '../../vehicle_account/vehicle_account_page.dart';
import '../../vehicle_finance/vehicle_finance_page.dart';
import '../ev_common/ev_enum_const_util.dart';
import '../vehicle_search_station_charging_management/ev_charging_management_page.dart';
import 'LegalTermsAndCondition/ev_Legal_Terms_and_Condition_page.dart';
import 'connectors_Id_selection/connectors_id_bottom_selection.dart';
import 'ev_driver_account_enrollment/ev_driver_account_enrollment_page.dart';
import 'helper/evgo_complimentry_expiry_validation.dart';
import 'vehicle_search_charge_station_filter_page.dart';
import 'vehicle_search_charge_station_location_util.dart';
import 'vehicle_search_charge_station_tariff_screen.dart';

final _StationViewdebouncer = Debouncer(milliseconds: 1000);

class VehicleSearchChargeStationLocationPage extends StatefulWidget {
  final bool? isFromNative;

  VehicleSearchChargeStationLocationPage({this.isFromNative});

  @override
  _VehicleSearchChargeStationLocationPageState createState() =>
      _VehicleSearchChargeStationLocationPageState();
}

class _VehicleSearchChargeStationLocationPageState
    extends State<VehicleSearchChargeStationLocationPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  VehicleSearchChargeStationLocationBloc _bloc =
      VehicleSearchChargeStationLocationBloc();
  final GlobalKey<VehicleSearchChargeStationLocationMapState> _mapKey =
      GlobalKey();
  final _debouncer = Debouncer(milliseconds: 500);
  BuildContext? _draggableSheetContext;
  ScrollController? _scrollController;
  double _initialSheetChildSize = 0.5;
  double _dragScrollSheetExtent = 0;
  ScrollController _listcontroller = ScrollController();
  Station? selectedChargeStation;
  final dialog = BottomDynamicConfirmationDialog();

  double _widgetHeight = 0;
  double _fabPosition = 0;
  double _mobileHeight = 0;
  double _mapBottomMargin = 0;
  double _mapHeight = 0;
  RegExp reg = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
  String Function(Match) mathFunc = (Match match) => '${match[1]},';
  bool? _isWalletFeatureEnabled;

  @override
  void initState() {
    super.initState();
    _StationViewdebouncer.run(() {
      FireBaseAnalyticsLogger.logInfo(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_FIND_STATIONS_PAGE);
      _bloc.init(
          _progressHandlerCallback, _screenAction, _dialogHandlerCallback);
      _bloc.searchController.addListener(_latestSearchValue);
      _bloc.partnerDetailsInfoBloc.fetchPartnerInfo();
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _fabPosition = _initialSheetChildSize * context.size!.height;
        _bloc.changeFabPosition(_fabPosition);
      });
      _bloc.appLocationDenied.listen((event) {
        if (event == true) {
          BottomConfirmationDialog().showBottomDialog(
              context,
              OneAppString.of().locationPermissionHeading,
              OneAppString.of().locationPermissionHeading,
              mapLocationPinIcon,
              OneAppString.of().commonOK,
              OneAppString.of().commonCancel,
              _appPermissionConfirmClick);
        }
      });
      _bloc.isWalletFeatureEnabled.listen((event) {
        _isWalletFeatureEnabled = event;
      });
    });
  }

  void scrollToBottom() {
    final bottomOffset = _listcontroller.position.maxScrollExtent;
    _listcontroller.animateTo(
      bottomOffset,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeIn,
    );
  }

  void scrollToTop() {
    final bottomOffset = _listcontroller.position.minScrollExtent;
    _listcontroller.animateTo(
      bottomOffset,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeIn,
    );
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
    _listcontroller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _mobileHeight = MediaQuery.of(context).size.height;
    _initialSheetChildSize = (_mobileHeight <= 720) ? 0.55 : 0.63;
    final sheetHeight = _initialSheetChildSize * _mobileHeight;
    _mapHeight = _mobileHeight - sheetHeight + CARD_RADIUS;
    _mapBottomMargin = _mobileHeight / 2;
    return PopScope(
        child: StreamBuilder(
          stream: _bloc.refreshApp,
          builder: (ctx, data) => BlocProvider(
            bloc: _bloc,
            child: OneAppScaffold(
                resizeToAvoidBottomInset: true, body: _buildBody()),
          ),
        ),
        onPopInvoked: (didPop) async {
          if (didPop) return;
          await _bloc.backPressHandler(_backPressCallback);
        });
  }

  Widget _buildBody() {
    return Container(
      color: _colorUtil.tertiary15,
      child: SafeArea(
        top: true,
        bottom: false,
        child: Stack(
          children: [
            StreamBuilder<int>(
                stream: _bloc.showPreferredVehicleSheet,
                builder: (context, snapshot) {
                  return _buildMap((snapshot.hasData && snapshot.data == 0)
                      ? _mapBottomMargin
                      : 0);
                }),
            StreamBuilder<double>(
                stream: _bloc.showListView,
                builder: (context, snapshot) {
                  return (snapshot.hasData && snapshot.data == 1)
                      ? AnimatedOpacity(
                          opacity: (snapshot.hasData) ? snapshot.data! : 0,
                          curve: Curves.easeInOut,
                          duration: Duration(milliseconds: 500),
                          child: _dealerList())
                      : Container();
                }),
          ],
        ),
      ),
    );
  }

  _showVehicleAccountPage<bool>() {
    return showMaterialModalBottomSheet(
      expand: true,
      context: context,
      isDismissible: true,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(child: VehicleAccountPage()),
          ],
        ),
      ),
    );
  }

  StreamBuilder<int> getSearchLayoutComponent() {
    return StreamBuilder<int>(
        stream: _bloc.showPreferredVehicleSheet,
        builder: (context, snapshot) {
          return ((snapshot.hasData && snapshot.data == 0) ||
                  (snapshot.hasData && snapshot.data == 2))
              ? AnimatedContainer(
                  duration: Duration(milliseconds: 250),
                )
              : _searchLayout();
        });
  }

  StreamBuilder<int> getFilterLayout() {
    return StreamBuilder<int>(
        stream: _bloc.showPreferredVehicleSheet,
        builder: (context, snapshot) {
          return ((snapshot.hasData && snapshot.data == 0) ||
                  (snapshot.hasData && snapshot.data == 2))
              ? AnimatedContainer(
                  duration: Duration(milliseconds: 500),
                )
              : Visibility(
                  visible: !_bloc.isApiLoading,
                  child: VehicleSearchChargeStationFilterPage(
                    bloc: _bloc,
                  ),
                );
        });
  }

// Search box to search locations
  Widget _searchLayout() {
    return StreamBuilder<bool>(
        stream: _bloc.showPrefixIcon,
        builder: (context, snapshot) {
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: SearchBar(
              backGroundColor: _colorUtil.tile05,
              allowSpecialCharacters: true,
              textLimit: 150,
              suffixIconColor: _colorUtil.tertiary07,
              prefixIconColor: _colorUtil.tertiary03,
              prefixSvgPath: searchIcon,
              suffixSvgPath: removeIcon,
              cursorColor: _colorUtil.tertiary00,
              textEditingController: _bloc.searchController,
              hintText: OneAppString.of().currentLocationHint,
              suffixVisibility:
                  (snapshot.hasData && snapshot.data!) ? true : false,
              suffixIconClickCallBack: _removeIconClick,
              onChangeCallBackListener: _bloc.getPlaces,
              submitCallBackListener: _bloc.searchOnSubmit,
              textColor: _colorUtil.tertiary03,
              elevation: 0,
              hintStyle: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body3, _colorUtil.tertiary03),
            ),
          );
        });
  }

  Widget _buildMap(double mapBottomMargin) {
    return StreamBuilder<List<Station>?>(
        stream: _bloc.chargeStationList,
        builder: (context, snapshot) {
          return _serviceDealerMap(
              context, snapshot.hasData ? snapshot.data : [], mapBottomMargin);
        });
  }

  // Map containing service dealers locations
  Widget _serviceDealerMap(
    BuildContext context,
    List<Station>? chargeStationLocationInfo,
    double mapBottomMargin,
  ) {
    return Stack(
      children: <Widget>[
        StreamBuilder<Position?>(
            stream: _bloc.selectedLatLng,
            builder: (context, snapshot) {
              return chargeStationLocationInfo?.length != null &&
                      chargeStationLocationInfo!.isNotEmpty
                  ? Container(
                      height: _mapHeight,
                      margin: EdgeInsets.only(bottom: mapBottomMargin),
                      child: VehicleSearchChargeStationLocationMap(
                        key: _mapKey,
                        chargeStationInfoList: chargeStationLocationInfo,
                        currentLocation: _bloc.currentLocation,
                        selectedCurrentLocation: _bloc.selectedCurrentLocation,
                        favouriteLocation: _bloc.isFavouriteLocationSelected,
                        selectedLocation:
                            (snapshot.hasData && snapshot.data != null)
                                ? snapshot.data!
                                : null,
                        pinClickListener: pinClickCallback,
                      ),
                    )
                  : Center(child: CircularProgressIndicator());
            }),
        Align(
          alignment: AlignmentDirectional.topStart,
          child: Container(
            width: 80.w,
            height: 64.h,
            child: Center(
              child: Container(
                width: 48.w,
                height: 48.h,
                decoration: BoxDecoration(
                  color: _colorUtil.button05b,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      offset: Offset(0.w, 5.h),
                      blurRadius: 15.w,
                    ),
                  ],
                  borderRadius: BorderRadius.circular(24.0.r),
                ),
                child: Center(
                  child: IconButton(
                      icon: Icon(
                        Icons.close,
                        color: ThemeConfig.current().colorUtil.tertiary00,
                      ),
                      onPressed: () async {
                        _backPressCallback();
                      }),
                ),
              ),
            ),
          ),
        ),

        /// Profile and Theme Icons are removed from the MAP as per new CR
        StreamBuilder<int>(
            stream: _bloc.showPreferredVehicleSheet,
            builder: (context, snapshot) {
              int? visibleItem = 1;
              visibleItem = (snapshot.hasData) ? snapshot.data! : visibleItem;
              if (visibleItem == 0) {
                return StreamBuilder<Station>(
                    stream: _bloc.selectedChargeStation,
                    builder: (context, snapshot) {
                      selectedChargeStation = snapshot.data;
                      return (snapshot.hasData)
                          ? _selectedDealerBottomSheet(selectedChargeStation)
                          : Container();
                    });
              } else {
                return NotificationListener<DraggableScrollableNotification>(
                    onNotification:
                        (DraggableScrollableNotification notification) {
                      _widgetHeight = context.size!.height;
                      _dragScrollSheetExtent = notification.extent;
                      // Calculate FAB position based on parent widget height and DraggableScrollable position
                      _fabPosition = _dragScrollSheetExtent * _widgetHeight;
                      _bloc.changeFabPosition(_fabPosition);
                      return true;
                    },
                    child: StreamBuilder<List<Station>?>(
                        stream: _bloc.chargeStationList,
                        builder: (context, snapshot) {
                          return _dealerListBottomSheet(snapshot.data);
                        }));
              }
            }),
      ],
    );
  }

  // Dealer list once user presses done button after searching
  // Auto Searching mapbox api
  Widget _dealerList() {
    return Container(
      height: double.maxFinite,
      width: double.maxFinite,
      color: _colorUtil.tertiary15,
      child: Column(
        children: [
          bottomSheetAppBar(
            OneAppString.of().nearByStations,
            elevation: 0,
            backgroundColor: Colors.transparent,
            onBackPressed: () {},
            leadingWidget: IconButton(
                icon: Icon(
                  Icons.chevron_left,
                  color: ThemeConfig.current().colorUtil.button02a,
                ),
                onPressed: () {
                  _bloc.backPressHandler(_backPressCallback);
                  _toggleDraggableScrollableSheet();
                }),
          ),
          _searchLayout(),
          StreamBuilder<MapBoxAddressPredictionEntity?>(
              stream: _bloc.placeAutoSearch,
              builder: (context, snapshot) {
                final features = snapshot.data?.features;
                return (features != null && features.isNotEmpty)
                    ? Expanded(
                        child: ListView.separated(
                          separatorBuilder: (context, index) {
                            return CustomDivider(
                              lineColor: _colorUtil.tertiary10,
                            );
                          },
                          padding: EdgeInsets.symmetric(horizontal: 15.w),
                          itemCount: features.length,
                          itemBuilder: (ctx, i) {
                            Features _singlePlace = features[i];
                            return ListTile(
                                title: Text(_singlePlace.placeName,
                                    style: TextStyleExtension()
                                        .newStyleWithColor(_textStyleUtil.body4,
                                            _colorUtil.tertiary05)),
                                onTap: () {
                                  _bloc.searchDealer(_singlePlace);
                                  FocusScope.of(context)
                                      .requestFocus(FocusNode());
                                });
                          },
                        ),
                      )
                    : (!_bloc.isPlaceApiLoading)
                        ? AnimatedContainer(
                            duration: Duration(milliseconds: 250),
                            padding: EdgeInsets.only(top: 10.h),
                            child: Text(
                              OneAppString.of().SearchErrorMsg,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.body4, _colorUtil.tertiary03),
                            ))
                        : Container();
              }),
        ],
      ),
    );
  }

  // Bottom sheet with list of dealers
  Widget _dealerListBottomSheet(List<Station>? chargeStationLocationInfo) {
    return DraggableScrollableActuator(
      child: DraggableScrollableSheet(
        key: Key(_initialSheetChildSize.toString()),
        initialChildSize: _initialSheetChildSize,
        maxChildSize: 1,
        minChildSize: _initialSheetChildSize,
        builder: (BuildContext context, ScrollController scrollController) {
          _draggableSheetContext = context;
          return Container(
            decoration: _colorUtil.bottomSheetDecorator(),
            child: SingleChildScrollView(
              controller: scrollController,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SwipeBarIcon(),
                  bottomSheetAppBar(
                    OneAppString.of().nearByStations,
                    elevation: 0,
                    backgroundColor: Colors.transparent,
                    onBackPressed: () {},
                    leadingWidget: Container(),
                  ),
                  getSearchLayoutComponent(),
                  getFilterLayout(),
                  _bloc.isApiLoading
                      ? Center(
                          child: Container(
                            height: 300.h,
                            color: Colors.transparent,
                            child: Center(child: CircularProgressIndicator()),
                          ),
                        )
                      : (chargeStationLocationInfo != null &&
                              chargeStationLocationInfo.isNotEmpty)
                          ? Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              child: _chargeStationListView(
                                  scrollController, chargeStationLocationInfo),
                            )
                          : _emptyPlaceHolderView()
                ],
              ),
            ),
          );
        },
      ),
    );
  }

// Empty View For Charge Station Non-Availability
  Widget _emptyPlaceHolderView() {
    ///  fav error msg not displaying  cause  because  stream builder is added
    /// old code without streambuilder
    return Visibility(
      visible: !_bloc.isApiLoading && !_bloc.isSingleStationBackPressed,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 40.h),
            child: StreamBuilder<bool>(
                stream: _bloc.isFavouriteStationFilterDisabled,
                builder: (context, snapshot) {
                  return Text(
                    snapshot.hasData && snapshot.data!
                        ? OneAppString.of().noFavoriteChargeStationHint
                        : OneAppString.of().noChargeStationHintOne,
                    textAlign: TextAlign.center,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout2, _colorUtil.tertiary00),
                  );
                }),
          ),
        ],
      ),
    );
  }

  // Charge Station list item
  Widget _chargeStationListView(ScrollController scrollController,
      List<Station> chargeStationLocationInfo) {
    scrollController.addListener(() {
      double maxScroll = scrollController.position.maxScrollExtent;
      double currentScroll = scrollController.position.pixels;
      double delta = 0; // or something else..
      if (maxScroll - currentScroll == delta) {
        if (!_bloc.isEvStationLoadingAsBool &&
            (_bloc.chargeStationsList.length <
                _bloc.totalNumberOfNearByEvStations) &&
            !_bloc.isFavouriteStationSelectedValue) {
          _bloc.loadNewStationList();
        }
      }
    });
    return ListView.separated(
        separatorBuilder: (context, index) {
          return CustomDivider(
            lineColor: _colorUtil.tertiary10,
          );
        },
        physics: BouncingScrollPhysics(),
        shrinkWrap: true,
        itemCount: chargeStationLocationInfo.length + 1,
        itemBuilder: (BuildContext context, int index) {
          if (index == chargeStationLocationInfo.length) {
            return StreamBuilder(
              stream: _bloc.isEvStationLoading,
              builder: (context, snapshot) => Visibility(
                visible: (snapshot.hasData && snapshot.data == true) &&
                        !_bloc.isFavouriteStationSelectedValue
                    ? true
                    : false,
                child: Container(
                    height: 20.h, child: CupertinoActivityIndicator()),
              ),
            );
          }
          return InkWell(
            onTap: () async {
              FocusScope.of(context).unfocus();
              // _bloc.searchController.clear();
              _bloc.toggleSearchView(0);
              _toggleDraggableScrollableSheet();
              _bloc.selectedDealer(
                  selectedDealerStation: chargeStationLocationInfo[index]);
            },
            child: VehicleSearchChargeStationLocationCard(
              selectedHit: chargeStationLocationInfo[index],
              selectedIndex: index,
              bloc: _bloc,
              isWalletFeatureEnabled: _isWalletFeatureEnabled,
            ),
          );
        });
  }

  // Bottom sheet with details about selected dealer
  Widget _selectedDealerBottomSheet(Station? chargeStationLocationInfo) {
    return DraggableScrollableSheet(
      initialChildSize: _initialSheetChildSize,
      maxChildSize: max(_initialSheetChildSize, BOTTOM_SHEET_PERCENT_65),
      minChildSize: _initialSheetChildSize,
      builder: (BuildContext context, ScrollController scrollController) {
        _scrollController = scrollController;
        return Container(
          decoration: _colorUtil.bottomSheetDecorator(
              backgroundColor: _colorUtil.tertiary15),
          child: Column(
            children: [
              SwipeBarIcon(),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: StreamBuilder<bool>(
                      stream: _bloc.isWalletFeatureEnabled,
                      initialData: false,
                      builder: (context, snapshot) {
                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            bottomSheetAppBar(
                              (chargeStationLocationInfo!
                                      .isPartner(snapshot.data!))
                                  ? OneAppString.of().partnerStationHeading
                                  : OneAppString.of().nonPartnerStationHeading,
                              backgroundColor: _colorUtil.tertiary15,
                              leadingWidget: IconButton(
                                  icon: Icon(
                                    Icons.chevron_left,
                                    color: ThemeConfig.current()
                                        .colorUtil
                                        .tertiary00,
                                  ),
                                  onPressed: () {
                                    _bloc.backPressHandler(_backPressCallback);
                                    // _bloc
                                    //     .fetchChargeLocationListForCurrentLocation();
                                    FocusScope.of(context)
                                        .requestFocus(FocusNode());
                                  }),
                              elevation: 0,
                            ),
                            _selectedDealerView(
                                selectedChargeStation!, snapshot.data!),
                          ],
                        );
                      }),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _selectedDealerView(
      Station chargeStationLocationInfo, bool isWalletEnabled) {
    String partnerName =
        VehicleSearchChargeStationLocationUtil.getPartnerTypeText(
            chargeStationLocationInfo);

    final coordinates = chargeStationLocationInfo.geometry.coordinates;
    final hasLocation = coordinates.length > 1;
    final hasPlaceId = chargeStationLocationInfo.placeId != null;

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 16.w,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          StreamBuilder<PartnerDetails?>(
              stream: _bloc.partnerDetailsInfoBloc.partnerDetails,
              builder: (context, snapshot) {
                if (partnerName.toLowerCase() == EV_PARTNERS.EVGO) {
                  return EVGOComplimentaryExpiryValidation();
                } else {
                  return Container();
                }
              }),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                  child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Visibility(
                    visible: partnerName.isNotEmpty,
                    child: Row(
                      children: [
                        // boltIcon,
                        Padding(
                          padding: EdgeInsets.only(top: 20.h),
                          child: Text(
                            partnerName,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body2, _colorUtil.tertiary03),
                          ),
                        )
                      ],
                    ),
                  ),
                  Visibility(
                    visible: chargeStationLocationInfo.name.isNotEmpty,
                    child: Padding(
                      padding: EdgeInsets.only(top: 20.h),
                      child: Text(
                        chargeStationLocationInfo.name,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.caption1, _colorUtil.tertiary03),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  Text(
                    '${chargeStationLocationInfo.address}, ${chargeStationLocationInfo.city}',
                    maxLines: 1,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05),
                    textAlign: TextAlign.start,
                  ),
                  Visibility(
                    visible:
                        chargeStationLocationInfo.openingTimes.timing != null &&
                            chargeStationLocationInfo
                                .isPartner(_isWalletFeatureEnabled!),
                    child: Text(
                      chargeStationLocationInfo.openingTimes.timing != null
                          ? chargeStationLocationInfo.openingTimes.timing!
                          : "",
                      maxLines: 3,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1, _colorUtil.secondary01),
                      textAlign: TextAlign.start,
                    ),
                  ),
                ],
              )),
              Container(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: 20.h),
                      child: Text(
                        (chargeStationLocationInfo.distance != null)
                            ? "${chargeStationLocationInfo.distance.toString().replaceAllMapped(reg, mathFunc)} ${OneAppString.of().short_miles}"
                            : "${OneAppString.of().short_miles}",
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1, _colorUtil.tertiary05),
                      ),
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        StreamBuilder<bool>(
                          stream: _bloc.isMyDestinationEnabled,
                          builder: (context, snapshot) {
                            final canFavorite =
                                (snapshot.hasData && snapshot.data!);
                            return (canFavorite && hasPlaceId)
                                ? IconButton(
                                    onPressed: () {
                                      _bloc.toggleFavouriteStation(
                                          selectedStation:
                                              chargeStationLocationInfo,
                                          bottomSheetId: 0);
                                    },
                                    icon:
                                        (chargeStationLocationInfo.isFavourite)
                                            ? Icon(
                                                Icons.favorite,
                                                color: _colorUtil.secondary01,
                                              )
                                            : Icon(
                                                Icons.favorite_border_outlined,
                                                color: Colors.grey,
                                              ),
                                    tooltip: OneAppString.of().add_to_favorite,
                                  )
                                : SizedBox(
                                    width: 50.w,
                                    height: 50.h,
                                  );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
          SizedBox(height: 10.h),
          Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: StreamBuilder<Object>(
                stream: _bloc.isMyDestinationEnabled,
                builder: (context, snapshot) {
                  final isPricingAvailable =
                      chargeStationLocationInfo.isPartner(isWalletEnabled) &&
                          chargeStationLocationInfo
                              .evEvsConnectorsDetail.isNotEmpty;
                  final isDestinationsEnabled =
                      (snapshot.hasData && snapshot.data as bool);
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (isPricingAvailable)
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            InkWell(
                              onTap: () async {
                                if (chargeStationLocationInfo
                                        .isPartner(isWalletEnabled) &&
                                    chargeStationLocationInfo
                                        .evEvsConnectorsDetail.isNotEmpty) {
                                  FireBaseAnalyticsLogger.logScreenVisit(
                                      VehicleAnalyticsEvent
                                          .VEHICLE_EV_PUB_PRICING_PAGE);
                                  _findActions(EV_SCREENS.TARIFF_SCREEN,
                                      chargeStationLocationInfo);
                                }
                              },
                              child: CircleAvatar(
                                radius: 25,
                                backgroundColor: _colorUtil.button02b,
                                child: SvgPicture.asset(pricingIcon,
                                    colorFilter: ColorFilter.mode(
                                      _colorUtil.tertiary00,
                                      BlendMode.srcIn,
                                    ),
                                    width: 20.w,
                                    height: 20.h),
                              ),
                            ),
                            SizedBox(height: 10.h),
                            Text(
                              OneAppString.of().pricing,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout1,
                                  _colorUtil.button02a),
                            )
                          ],
                        ),
                      if (isDestinationsEnabled)
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            InkWell(
                              onTap: () async {
                                _showSendToCarInProgress();
                                chargeStationLocationInfo.usePlaceId = false;
                                final result = await sendToCar(
                                  chargeStationLocationInfo.poiText,
                                );
                                if (result != null &&
                                    result.outcome == 'success') {
                                  FireBaseAnalyticsLogger
                                      .logMarketingGroupEvent(
                                    VehicleAnalyticsEvent
                                        .VEHICLE_EV_PUB_NAV_GROUP,
                                    childEventName: VehicleAnalyticsEvent
                                        .VEHICLE_EV_PUB_SENDTOCAR_DETAIL_SUCCESS,
                                  );

                                  _showSendToCarSuccess();
                                } else {
                                  FireBaseAnalyticsLogger
                                      .logMarketingGroupEvent(
                                    VehicleAnalyticsEvent
                                        .VEHICLE_EV_PUB_NAV_GROUP,
                                    childEventName: VehicleAnalyticsEvent
                                        .VEHICLE_EV_PUB_SENDTOCAR_DETAIL_FAILURE,
                                  );

                                  _showSendToCarError();
                                }
                              },
                              child: CircleAvatar(
                                radius: 25,
                                backgroundColor: _colorUtil.button02b,
                                child: SvgPicture.asset(lexusCarIcon,
                                    colorFilter: ColorFilter.mode(
                                      _colorUtil.tertiary00,
                                      BlendMode.srcIn,
                                    ),
                                    width: 20.w,
                                    height: 20.h),
                              ),
                            ),
                            SizedBox(height: 10.h),
                            Text(
                              OneAppString.of().sendToCar,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout1,
                                  _colorUtil.button02a),
                            )
                          ],
                        ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          InkWell(
                            onTap: (!hasLocation)
                                ? null
                                : () {
                                    final longitude = coordinates[0];
                                    final latitude = coordinates[1];
                                    final displayName =
                                        chargeStationLocationInfo.name;
                                    FireBaseAnalyticsLogger
                                        .logMarketingGroupEvent(
                                      VehicleAnalyticsEvent
                                          .VEHICLE_EV_PUB_NAV_GROUP,
                                      childEventName: VehicleAnalyticsEvent
                                          .VEHICLE_EV_PUB_DIRECTIONS_DETAIL,
                                    );

                                    openMap(longitude, latitude,
                                        address: displayName);
                                  },
                            child: CircleAvatar(
                              radius: 25,
                              backgroundColor: _colorUtil.button02b,
                              child: SvgPicture.asset(directionsIcon,
                                  colorFilter: ColorFilter.mode(
                                    _colorUtil.tertiary00,
                                    BlendMode.srcIn,
                                  ),
                                  width: 25.w,
                                  height: 25.h),
                            ),
                          ),
                          SizedBox(height: 10.h),
                          Text(
                            //latest change as per figma
                            OneAppString.of().directionText,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.callout1, _colorUtil.button02a),
                          )
                        ],
                      ),
                    ],
                  );
                }),
          ),
          SizedBox(
            height: 30.h,
          ),
          InkWell(
              onTap: () {},
              child: (chargeStationLocationInfo.isPartner(isWalletEnabled) &&
                      chargeStationLocationInfo
                          .evEvsConnectorsDetail.isNotEmpty)
                  ? AnimatedContainer(
                      duration: Duration(milliseconds: 250),
                      decoration: BoxDecoration(
                          //change as per latest figma  in color change
                          color: _colorUtil.tile02,
                          borderRadius: BorderRadius.circular(10)),
                      child: Row(
                        children: [
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Padding(
                                padding: EdgeInsets.only(left: 25.w),
                                child: Text(
                                    OneAppString.of().availablePlugsText,
                                    style: TextStyleExtension()
                                        .newStyleWithColor(_textStyleUtil.body4,
                                            _colorUtil.tertiary03),
                                    textAlign: TextAlign.center),
                              ),
                            ],
                          ),
                          SizedBox(
                            width: chargeStationLocationInfo
                                    .evEvsConnectorsDetail.isNotEmpty
                                ? chargeStationLocationInfo
                                            .evEvsConnectorsDetail.length >
                                        1
                                    ? 40.h
                                    : 70.h
                                : 70.h,
                          ),
                          chargeStationLocationInfo
                                  .evEvsConnectorsDetail.isNotEmpty
                              ? Flexible(
                                  flex: 1,
                                  child: Container(
                                    height: 50.h,
                                    child: ListView.builder(
                                      itemCount: chargeStationLocationInfo
                                          .evEvsConnectorsDetail.length,
                                      scrollDirection: Axis.horizontal,
                                      itemBuilder: (context, index) => Padding(
                                        padding: EdgeInsets.only(right: 8.w),
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Chip(
                                            backgroundColor:
                                                _colorUtil.success02,
                                            label: RichText(
                                              text: TextSpan(
                                                style: TextStyleExtension()
                                                    .newStyleWithColor(
                                                        _textStyleUtil.callout1,
                                                        _colorUtil.button02a),
                                                children: [
                                                  TextSpan(
                                                      text:
                                                          '${chargeStationLocationInfo.evEvsConnectorsDetail[index].active}/${chargeStationLocationInfo.evEvsConnectorsDetail[index].total}',
                                                      style: TextStyle(
                                                          fontWeight:
                                                              FontWeight.bold)),
                                                  TextSpan(
                                                      text:
                                                          ' ${chargeStationLocationInfo.evEvsConnectorsDetail[index].friendlyName}'),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              : (chargeStationLocationInfo
                                      .evConnectorTypes.isNotEmpty)
                                  ? Flexible(
                                      child: Container(
                                        height: 50.h,
                                        child: ListView.builder(
                                          itemCount:
                                              VehicleSearchChargeStationLocationUtil
                                                  .typeOfConnectors.length,
                                          scrollDirection: Axis.horizontal,
                                          itemBuilder: (context, index) =>
                                              Padding(
                                            padding:
                                                EdgeInsets.only(left: 25.w),
                                            child: Chip(
                                              backgroundColor:
                                                  _colorUtil.success02,
                                              label: Text(
                                                  VehicleSearchChargeStationLocationUtil
                                                      .typeOfConnectors[index]
                                                      .toUpperCase()),
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                  : Spacer(),
                        ],
                      ))
                  : Container()),
          _pageFooter(chargeStationLocationInfo),
        ],
      ),
    );
  }

  _showConnectorsAvailability() {
    return showMaterialModalBottomSheet(
      context: context,
      expand: true,
      isDismissible: true,
      // isScrollControlled: true,
      backgroundColor: _colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
          child: StreamBuilder<List<String>>(
              stream: _bloc.availableConnectors,
              builder: (context, snapshot) {
                return ConnectorIdBottomSelection(
                  availableConnectors: snapshot.data ?? [],
                  availableConnectorsWithPhyRef:
                      _bloc.availableConnectorsWithPhyRefAsList,
                  selectedConnectors: selectedConnector,
                );
              })),
    );
  }

  _showAcceptTermsAndConditionScreen<bool>(String? partnerName) {
    return showModalBottomSheet(
      context: context,
      isDismissible: true,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Container(
          height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_50,
          child: Column(
            children: [
              SwipeBarIcon(),
              Padding(
                padding: EdgeInsets.only(top: 15.h, left: 20.w),
                child: bottomSheetAppBar(
                  OneAppString.of().termsAndConditionText,
                  elevation: 0,
                  backgroundColor: Colors.transparent,
                  onBackPressed: () {},
                  leadingWidget: IconButton(
                      icon: CircleAvatar(
                        backgroundColor: _colorUtil.button05b,
                        radius: 30,
                        child: Icon(
                          Icons.chevron_left,
                          color: ThemeConfig.current().colorUtil.button02a,
                        ),
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      }),
                ),
              ),
              Expanded(
                  child: SingleChildScrollView(
                child: StreamBuilder<PartnerDetails?>(
                    stream: _bloc.partnerDetailsInfoBloc.partnerDetails,
                    builder: (BuildContext context, snapshot) {
                      if (snapshot.hasData && snapshot.data != null) {
                        return EvNewTermsAndConditionPage(
                          acceptTermsCallBackFunction:
                              acceptTermsCallBackFunction,
                          partnerName: partnerName,
                          evgoExpired: snapshot.data?.evgoExpired ?? false,
                        );
                      } else {
                        return EvNewTermsAndConditionPage(
                          acceptTermsCallBackFunction:
                              acceptTermsCallBackFunction,
                          partnerName: partnerName,
                        );
                      }
                    }),
              ))
            ],
          ),
        ),
      ),
    );
  }

  _showEnrollmentScreen({Station? chargeStation, String? partnerName}) {
    return showModalBottomSheet(
        context: context,
        isDismissible: true,
        useRootNavigator: true,
        backgroundColor: _colorUtil.tertiary12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(CARD_RADIUS),
          ),
        ),
        builder: (context) => SafeArea(
                child: Container(
              height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_65,
              child: OneAppScaffold(
                resizeToAvoidBottomInset: true,
                body: Padding(
                  padding: EdgeInsets.only(
                    top: 10.h,
                  ),
                  child: StreamBuilder<PartnerDetails?>(
                    stream: _bloc.partnerDetailsInfoBloc.partnerDetails,
                    builder: (context, snapshot) {
                      if (snapshot.hasData && snapshot.data != null) {
                        return EVDriverAccountEnrollmentPage(
                            chargeStation: chargeStation,
                            continueCallback: _enrollCallBackFunction,
                            screenAction: _screenAction,
                            evgoExpired: snapshot.data?.evgoExpired ?? false);
                      } else {
                        return EVDriverAccountEnrollmentPage(
                          chargeStation: chargeStation,
                          continueCallback: _enrollCallBackFunction,
                          screenAction: _screenAction,
                        );
                      }
                    },
                  ),
                ),
              ),
            )));
  }

  _showTariffScreen(Station chargeStationLocationInfo) {
    return showModalBottomSheet(
        context: context,
        isDismissible: true,
        useRootNavigator: true,
        backgroundColor: _colorUtil.tertiary12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(CARD_RADIUS),
          ),
        ),
        builder: (context) => SafeArea(
              child: Container(
                height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_65,
                child: OneAppScaffold(
                  resizeToAvoidBottomInset: true,
                  body: Padding(
                      padding: EdgeInsets.only(
                        top: 10.h,
                      ),
                      child: VehicleSearchChargeTariffScreen(
                        scrollController: _scrollController,
                        selectedChargeStation: chargeStationLocationInfo,
                        bloc: _bloc,
                      )),
                ),
              ),
            ));
  }

  Widget _pageFooter(Station chargeStationLocationInfo) {
    var selectedChargeStationText =
        Global.getInstance().selectedChargeStationText;
    dynamic chargeStation = null;
    if (selectedChargeStationText != null) {
      final jsonData = jsonDecode(selectedChargeStationText);
      chargeStation = Station.fromJson(jsonData);
    }
    return StreamBuilder<bool>(
        initialData: false,
        stream: _bloc.isPublicChargingControlAllowed,
        builder: (context, snapshot) {
          if (!snapshot.data!) {
            // when public charging is disabled everywhere, we hide both buttons and explanations
            return Container();
          } else {
            if (Global.getInstance().chargingId != null) {
              return Container(
                child: Padding(
                  padding: EdgeInsets.only(top: 20.h),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Center(
                        child: Text(
                          OneAppString.of().chargingInProgress,
                          style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1,
                            _colorUtil.tertiary05,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Visibility(
                        visible: chargeStation != null
                            ? selectedChargeStation?.id == chargeStation?.id
                            : false,
                        child: Padding(
                          padding: EdgeInsets.only(top: 20.h),
                          child: CustomDefaultButton(
                            backgroundColor: _colorUtil.button01b,
                            buttonTextColor: _colorUtil.button01a,
                            text: OneAppString.of().continueText,
                            press: () {
                              _showStartChargingScreen();
                            },
                            borderColor: _colorUtil.button01b,
                            horizontalPadding: 22.w,
                            verticalPadding: 6.h,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              );
            } else {
              return StreamBuilder<bool>(
                  stream: _bloc.isWalletFeatureEnabled,
                  initialData: false,
                  builder: (context, isWalletEnabled) {
                    if (chargeStationLocationInfo
                        .isPartner(isWalletEnabled.data!)) {
                      return StreamBuilder<PartnerEnrollment?>(
                          stream: _bloc.walletAndEnrollmentValidation,
                          builder: (context, snapshot) {
                            EvValidationHelper? evValidationHelper;
                            if (snapshot.hasData) {
                              evValidationHelper =
                                  VehicleSearchChargeStationLocationUtil
                                      .walletAndDriverCheck(
                                          snapshot.data,
                                          VehicleSearchChargeStationLocationUtil
                                              .getPartnerTypeText(
                                                  chargeStationLocationInfo,
                                                  toLowerCase: true),
                                          chargeStationLocationInfo,
                                          _bloc.vehicleItem?.make
                                                  .toLowerCase() ==
                                              makeLexus);
                            }
                            return Container(
                                margin:
                                    EdgeInsets.only(top: 30.h, bottom: 16.h),
                                child: CustomDefaultButton(
                                  backgroundColor: _colorUtil.button01b,
                                  buttonTextColor: _colorUtil.button01a,
                                  text: (evValidationHelper != null)
                                      ? evValidationHelper.buttonName
                                      : "",
                                  press: () {
                                    final EV_SCREENS nextScreen =
                                        (evValidationHelper != null)
                                            ? evValidationHelper.ev_screens
                                            : "" as EV_SCREENS;
                                    _findActions(
                                      nextScreen,
                                      chargeStationLocationInfo,
                                    );
                                  },
                                  borderColor: _colorUtil.button01b,
                                  horizontalPadding: 22.w,
                                  verticalPadding: 6.h,
                                ));
                          });
                    }

                    //here

                    else {
                      return Container(
                        child: Padding(
                          padding: EdgeInsets.only(top: 20.h),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Center(
                                child: Text(
                                  OneAppString.of().ChargingNotAvailable,
                                  style: TextStyleExtension().newStyleWithColor(
                                    _textStyleUtil.body4,
                                    _colorUtil.tertiary00,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                  });
            }
          }
        });
  }

  //Call back Function For Accepting
  void acceptTermsCallBackFunction({String? partnerName}) {
    // _bloc.enrollmentRedirect(selectedChargeStation: selectedChargeStation);
    _findActions(EV_SCREENS.ENROLLMENT_SCREEN, selectedChargeStation!);
  }

// Capture change in text from Textformfield widget
  void _latestSearchValue() {
    _debouncer.run(() {
      _bloc.searchValueChanged(_bloc.searchController.text);
    });
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  void _screenAction(EV_SCREENS val, {String? errorMessage}) {
    _findActions(val, selectedChargeStation!, errorMessage: errorMessage);
  }

  void _dialogHandlerCallback(
    String dialogText,
  ) {
    showBaseDialog(context, message: dialogText);
  }

  // Clear button in search field
  void _removeIconClick() {
    _bloc.getPlaces('');
    FocusScope.of(context).unfocus();
    _bloc.searchController.clear();
  }

  // _getTermsAndCondition(String partnerName) {
  //   _bloc.fetchTermsAndConditionContent(partnerName);
  // }

  void updateProfileScreen(String errorMessage) {
    _progressHandlerCallback(false);
    BottomErrorConfirmationDialog().showBottomDialog(
        context,
        chargeAlertIcon,
        OneAppString.of().updateProfileText,
        OneAppString.of().updateProfileErrorMessage(errorMessage),
        OneAppString.of().updateProfileButtonText,
        OneAppString.of().cancel,
        _updateProfileClickListener);
  }

  // redirect to contact support
  void _updateProfileClickListener() {
    _showVehicleAccountPage();
  }

  //This method To change the screen Function
  void _findActions(EV_SCREENS ev_screens, Station chargeStationLocationInfo,
      {String? errorMessage = ""}) {
    var partnerName = VehicleSearchChargeStationLocationUtil.getPartnerTypeText(
        chargeStationLocationInfo);

    switch (ev_screens) {
      case EV_SCREENS.ENROLLMENT_SCREEN:
        _showEnrollmentScreen(
            chargeStation: chargeStationLocationInfo,
            partnerName:
                _bloc.isLexus ? OneAppString.of().chargePoint : partnerName);
        break;
      case EV_SCREENS.TARIFF_SCREEN:
        _showTariffScreen(chargeStationLocationInfo);
        break;
      case EV_SCREENS.CHARGING_SCREEN:
        if (partnerName.toLowerCase() == EV_PARTNERS.CHARGE_POINT ||
            chargeStationLocationInfo.isRoamingPartner) {
          _showStartChargingScreen();
        } else {
          _bloc.updateEvogoConnectorDetails();
          _showConnectorsAvailability();
        }
        break;
      case EV_SCREENS.SETUP_WALLET:
        _showSetUpWalletScreen()
            .then((value) => _bloc.checkWalletAndDriverExistence());
        break;
      case EV_SCREENS.TERMS_AND_CONDITION:
        _showAcceptTermsAndConditionScreen(partnerName);
        break;
      case EV_SCREENS.UPDATE_PROFILE:
        updateProfileScreen(errorMessage!);
        break;
    }
  }

  _showStartChargingScreen<bool>({String? connectorId}) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        bottom: false,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(
                child: EvChargingManagementPage(
              isFromNative: false,
              selectedChargeStation: selectedChargeStation,
              connectorId: connectorId,
            )),
          ],
        ),
      ),
    );
  }

  _showSetUpWalletScreen<bool>() {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(
                child: VehicleFinancePage(
              fromChargeStation: true,
              showPassDueNotification: (showNotification) {},
            )),
          ],
        ),
      ),
    );
  }

  void _toggleDraggableScrollableSheet() {
    if (_draggableSheetContext != null) {
      _fabPosition = _initialSheetChildSize * context.size!.height;
      _bloc.changeFabPosition(_fabPosition);
      DraggableScrollableActuator.reset(_draggableSheetContext!);
      FocusScope.of(context).requestFocus(FocusNode());
    }
  }

  void _backPressCallback() async {
    if (widget.isFromNative ?? true) {
      goBackToNative();
    } else {
      Navigator.of(context).pop(true);
    }
  }

  void pinClickCallback(Station chargeStationLocationPayloadFuelStation) async {
    _bloc.selectedDealer(
        selectedDealerStation: chargeStationLocationPayloadFuelStation);
    _bloc.searchController.clear();
    _bloc.toggleSearchView(0);
    _toggleDraggableScrollableSheet();
  }

  void _appPermissionConfirmClick() {
    AppSettings.openAppSettings();
  }

  _enrollCallBackFunction() {
    _bloc.checkWalletAndDriverExistence();
  }

  void selectedConnector(String val) {
    Navigator.of(context).pop();
    _showStartChargingScreen(connectorId: val);
  }

  void _showSendToCarInProgress() {
    dialog.updateContent(
      headingText: OneAppString.of().sendToCarTitle,
      subHeadingText: OneAppString.of().sendToCarMessage,
      cancelText: OneAppString.of().cancel,
      showSpinner: true,
    );
    dialog.showDialog(context, () {}, () {});
  }

  void _showSendToCarSuccess() {
    dialog.updateContent(
      headingText: OneAppString.of().directionsSentTitle,
      subHeadingText: OneAppString.of().directionsSentMessage,
      confirmationText: OneAppString.of().commonOK,
    );
  }

  void _showSendToCarError() {
    dialog.updateContent(
      iconPath: chargeAlertIcon,
      headingText: OneAppString.of().error,
      subHeadingText: OneAppString.of().directionsSentError,
      confirmationText: OneAppString.of().commonOK,
    );
  }
}
