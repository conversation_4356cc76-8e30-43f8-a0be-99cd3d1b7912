// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/cupertino.dart';

// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/ev_api_client.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class EvNewTermsAndConditionBloc extends BlocBase {
  vehicleInfo.Payload? vehicleItem;

  final _acceptTermsAndCondition = BehaviorSubject<AsyncSnapshot<String?>>();

  ValueStream<AsyncSnapshot<String?>> get acceptTermsAndCondition =>
      _acceptTermsAndCondition.stream;

  void init(String? partnerName) {
    fetchTermsAndConditionContent(partnerName);
  }

  @override
  void dispose() {
    _acceptTermsAndCondition.close();
  }

  Future<void> fetchTermsAndConditionContent(String? partnerName) async {
    _acceptTermsAndCondition.add(AsyncSnapshot<String?>.waiting());

    try {
      EVApiClient evApi = APIClientConfig.evApiClient;
      String language = Global.getInstance().locale.toLanguageTag();

      vehicleItem = await VehicleRepo().cachedGlobalVehicleInfo();

      final commonResponse = await evApi.fetchEvPartnerTermsAndCondition(
        partner: (vehicleItem?.make.toLowerCase() == makeLexus)
            ? OneAppString.of().chargePoint
            : partnerName,
        language: language.toString(),
        make: vehicleItem?.make ?? '',
      );
      final response = commonResponse.response;
      final payload = response?.payload;
      if (payload != null) {
        _acceptTermsAndCondition.add(AsyncSnapshot<String?>.withData(
            ConnectionState.done, payload.legalDetails.content));
      } else {
        _acceptTermsAndCondition
            .add(AsyncSnapshot<String?>.withData(ConnectionState.done, null));
      }
    } catch (error) {
      _acceptTermsAndCondition
          .add(AsyncSnapshot<String?>.withError(ConnectionState.done, error));
    }
  }

//// decline call
  Future<void> fetchDeclineTermsAndCondition(String partnerName) async {
    EVApiClient evApi = APIClientConfig.evApiClient;
    String email = Global.getInstance().userEmail!;
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();
    await evApi.fetchDeclinePartnerTermsAndCondition(
      partnerName: partnerName,
      email: email,
      make: vehicleInfo!.make,
    );
  }
}
