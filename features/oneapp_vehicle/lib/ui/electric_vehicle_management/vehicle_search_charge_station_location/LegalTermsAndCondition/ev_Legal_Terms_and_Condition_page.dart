// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';

// Project imports:
import 'CustomHTMLDisplay.dart';
import 'ev_Legal_Terms_and_Condition_bloc.dart';

class EvNewTermsAndConditionPage extends StatefulWidget {
  final Function({String? partnerName})? acceptTermsCallBackFunction;
  final String? partnerName;
  final bool? evgoExpired;

  const EvNewTermsAndConditionPage({
    Key? key,
    this.acceptTermsCallBackFunction,
    required this.partnerName,
    this.evgoExpired,
  }) : super(key: key);

  @override
  _EvNewTermsAndConditionPageState createState() =>
      _EvNewTermsAndConditionPageState();
}

class _EvNewTermsAndConditionPageState
    extends State<EvNewTermsAndConditionPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  late EvNewTermsAndConditionBloc bloc;
  Function? declineFunction;
  Function? acceptFunction;
  String? partnerName;

  @override
  void initState() {
    super.initState();
    partnerName = widget.partnerName ?? "";
    bloc = EvNewTermsAndConditionBloc();
    bloc.init(partnerName);
    acceptFunction = widget.acceptTermsCallBackFunction ?? _callBackFunction;
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<AsyncSnapshot<String?>>(
      stream: bloc.acceptTermsAndCondition,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting ||
            snapshot.data?.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        } else if (snapshot.hasData && snapshot.data != null) {
          // Here we check if snapshot.data is not null before accessing it
          return _ShowLegalTermsAndCondition(
            snapshot.data?.data ?? '<p>No content available</p>',
          );
        } else {
          return Center(child: Text('No data available'));
        }
      },
    );
  }

  Widget _ShowLegalTermsAndCondition(String data) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _headerLayout(),
        Padding(
          padding: EdgeInsets.all(16.0),
          child: CustomHTMLDisplay(
            htmlContent: data,
            textStyle: TextStyle(
              fontSize: 14.0,
              color: _colorUtil.tertiary05,
            ),
            linkColor: _colorUtil.tertiary05,
          ),
        ),
        _buttonLayout(),
      ],
    );
  }

  Widget _buttonLayout() {
    return Container(
      //// issue fix agree button overlapping
      padding: EdgeInsets.only(top: 30.h, bottom: 10.h),
      child: Column(
        children: [
          CustomDefaultButton(
            press: () {
              Navigator.of(context).pop();
              bloc.fetchDeclineTermsAndCondition(
                  bloc.vehicleItem?.make.toLowerCase() == makeLexus ||
                          widget.evgoExpired == true
                      ? OneAppString.of().chargePoint
                      : widget.partnerName ?? '');
            },
            text: OneAppString.of().decline,
            buttonTextColor: _colorUtil.tertiary00,
            backgroundColor: Colors.transparent,
          ),
          Padding(
            //// issue fix agree button overlapping
            padding: const EdgeInsets.only(top: 10),
            child: CustomDefaultButton(
                text: OneAppString.of().agree,
                press: () {
                  Navigator.of(context).pop();
                  widget.acceptTermsCallBackFunction!(
                      partnerName:
                          bloc.vehicleItem?.make.toLowerCase() == makeLexus ||
                                  widget.evgoExpired == true
                              ? OneAppString.of().chargePoint
                              : widget.partnerName);
                }),
          )
        ],
      ),
    );
  }

  _callBackFunction() {}

  Widget _headerLayout() {
    return SafeArea(
        child: bottomSheetCustomAppBar(
            (partnerName?.toUpperCase() == Station.partnerNameEVgo &&
                        bloc.vehicleItem?.make.toLowerCase() == makeLexus ||
                    widget.evgoExpired == true)
                ? OneAppString.of().chargePoint
                : partnerName ?? '', onBackPressed: () {
      Navigator.of(context).pop();
    }, elevation: 0));
  }
}
