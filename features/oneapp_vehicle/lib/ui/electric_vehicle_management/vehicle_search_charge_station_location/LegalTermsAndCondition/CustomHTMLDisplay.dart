// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:html/dom.dart' as dom;
import 'package:html/parser.dart' as htmlparser;
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:url_launcher/url_launcher.dart';

class CustomHTMLDisplay extends StatelessWidget {
  final String htmlContent;
  final TextStyle textStyle;
  final Color linkColor;

  CustomHTMLDisplay({
    required this.htmlContent,
    required this.textStyle,
    required this.linkColor,
  });
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  @override
  Widget build(BuildContext context) {
    List<Widget> paragraphs = _parseHTML(htmlContent);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: paragraphs,
    );
  }

  List<Widget> _parseHTML(String html) {
    List<Widget> paragraphs = [];
    var document = htmlparser.parse(html);

    for (final element in document.body?.children ?? []) {
      if (element.localName == 'p') {
        List<InlineSpan> spans = _parseInlineElements(element);
        paragraphs.add(Padding(
          padding: EdgeInsets.only(bottom: 16),
          child: RichText(
            text: TextSpan(
              children: spans,
              style: textStyle,
            ),
          ),
        ));
      }
    }

    print("Number of paragraphs created: ${paragraphs.length}");
    return paragraphs;
  }

  List<InlineSpan> _parseInlineElements(dom.Element element) {
    List<InlineSpan> spans = [];

    for (final node in element.nodes) {
      if (node is dom.Element) {
        if (node.localName == 'a') {
          spans.add(_createLinkSpan(node));
        } else if (node.localName == 'em' || node.localName == 'i') {
          spans.add(TextSpan(
            children: _parseInlineElements(node),
            style: textStyle.copyWith(fontStyle: FontStyle.italic),
          ));
        } else if (node.localName == 'strong' || node.localName == 'b') {
          spans.add(TextSpan(
            children: _parseInlineElements(node),
            style: textStyle.copyWith(fontWeight: FontWeight.bold),
          ));
        } else {
          spans.addAll(_parseInlineElements(node));
        }
      } else if (node is dom.Text) {
        print(
            "Adding text node: ${node.text.substring(0, node.text.length > 20 ? 20 : node.text.length)}...");
        spans.add(TextSpan(text: node.text));
      }
    }
    if (element.localName == 'a' && spans.isEmpty) {
      spans.add(_createLinkSpan(element));
    }
    return spans;
  }

  TextSpan _createLinkSpan(dom.Element linkElement) {
    String hrefAttribute = linkElement.attributes['href'] ?? '';
    String urlString = hrefAttribute.replaceAll("'", "").trim();
    Uri? url = Uri.tryParse(urlString);

    if (url == null) {
      print("Invalid URL: $urlString");
      return TextSpan(text: linkElement.text);
    }

    String linkText =
        linkElement.text.trim().isNotEmpty ? linkElement.text : urlString;

    return TextSpan(
      text: linkText,
      style: textStyle.copyWith(
        //Accessing the color directly as it seems this doesn't show intermittently when passed
        color: _colorUtil.tertiary05,
        fontWeight: FontWeight.bold,
        decoration: TextDecoration.underline,
      ),
      recognizer: TapGestureRecognizer()
        ..onTap = () async {
          if (await canLaunchUrl(url)) {
            await launchUrl(url, mode: LaunchMode.externalApplication);
          } else {
            print('Could not launch $url');
          }
        },
    );
  }
}
