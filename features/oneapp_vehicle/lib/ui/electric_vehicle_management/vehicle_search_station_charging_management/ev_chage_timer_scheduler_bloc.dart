// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

class EvChargeTimer {
  static final EvChargeTimer _evChargeTimerInstance = EvChargeTimer._internal();
  static Timer? _evStatusTimer;
  static Timer? _evSessionTimer;
  static Timer? _evCdrSessionTimer;
  static late Duration _evStatusDuration;
  static late Duration _evSessionDuration;
  static late Duration _evCdrSessionDuration;

  late int _vehicleStatusApiInterval;
  late Function _vehicleStatusApiCallback;
  late int _sessionDetailsApiInterval;
  late Function _sessionDetailsCallback;
  late int _cdrSessionDetailsApiInterval;
  late Function _cdrSessionDetailsCallback;

  EvChargeTimer._internal();

  static EvChargeTimer getEVSchedulerInstance({
    int? vehicleStatusApiInterval,
    Function? vehicleStatusApiCallback,
    int? sessionDetailsInterval,
    Function? sessionDetailsCallback,
    int? cdrSessionDetailsInterval,
    Function? cdrSessionDetailsCallback,
  }) {
    /// vehicleStatusApiInterval --> Units in seconds
    _evChargeTimerInstance._vehicleStatusApiInterval =
        vehicleStatusApiInterval ?? 30;

    _evChargeTimerInstance._vehicleStatusApiCallback =
        vehicleStatusApiCallback ?? () {};

    _evChargeTimerInstance._sessionDetailsApiInterval =
        sessionDetailsInterval ?? 15;

    _evChargeTimerInstance._sessionDetailsCallback =
        sessionDetailsCallback ?? () {};

    _evChargeTimerInstance._cdrSessionDetailsApiInterval =
        cdrSessionDetailsInterval ?? 5;

    _evChargeTimerInstance._cdrSessionDetailsCallback =
        cdrSessionDetailsCallback ?? () {};

    return _evChargeTimerInstance;
  }

  void init() {
    debugPrint('init Called');
    _evStatusDuration = Duration(seconds: _vehicleStatusApiInterval);
    _evSessionDuration =
        Duration(seconds: _evChargeTimerInstance._sessionDetailsApiInterval);
    _evCdrSessionDuration =
        Duration(seconds: _evChargeTimerInstance._cdrSessionDetailsApiInterval);

    _evChargeTimerInstance.invokeCallbacks();
    _evChargeTimerInstance._activateTimer();
  }

  /**
   * There are mainly 4 states for it:
   * resumed: The application is visible and responding to user input.
   * inactive: The application is in an inactive state and is not receiving user input.
   * paused: The application is not currently visible to the user, not responding user input, and running in the background.
   * detached: The application is still hosted on a flutter engine but is detached from any host views.
   */

  void didChangeAppLifecycleState(AppLifecycleState state) {
    debugPrint('From Timer: ${state.toString()}');
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        _deactivateTimer();
        break;
      case AppLifecycleState.resumed:
        init();
        break;
    }
  }

  void _activateTimer() {
    _deactivateTimer();
    debugPrint('_activateTimer called');
    _evStatusTimer = Timer.periodic(
        _evStatusDuration, (Timer t) => _vehicleStatusApiCallback());
    _evSessionTimer = Timer.periodic(
        _evSessionDuration, (Timer t) => _sessionDetailsCallback());
  }

  void invokeCDRCallback() {
    _cdrSessionDetailsCallback();
  }

  void activateCDRTimer() {
    _deactivateTimer(keepEvStatusAlive: true);
    _evCdrSessionTimer = Timer.periodic(
        _evCdrSessionDuration, (Timer t) => _cdrSessionDetailsCallback());
  }

  void _deactivateTimer({keepEvStatusAlive = false}) {
    debugPrint('_deactivateTimer called');
    if (_evSessionTimer != null && _evSessionTimer!.isActive) {
      _evSessionTimer!.cancel();
    }
    if (!keepEvStatusAlive &&
        _evStatusTimer != null &&
        _evStatusTimer!.isActive) {
      _evStatusTimer!.cancel();
    }
  }

  void cancel({bool keepEvStatusAlive = false}) {
    _deactivateTimer(keepEvStatusAlive: keepEvStatusAlive);
    debugPrint('Timer Cancelled');
  }

  void invokeCallbacks() {
    _vehicleStatusApiCallback();
    _sessionDetailsCallback();
  }

  void cancelCDR() {
    if (_evCdrSessionTimer != null && _evCdrSessionTimer!.isActive) {
      _evCdrSessionTimer?.cancel();
    }
    debugPrint('CDR Timer Cancelled');
  }

  void dispose() {
    cancel();
    debugPrint('Timer Disposed');
    _evStatusTimer = null;
    _evSessionTimer = null;
  }
}
