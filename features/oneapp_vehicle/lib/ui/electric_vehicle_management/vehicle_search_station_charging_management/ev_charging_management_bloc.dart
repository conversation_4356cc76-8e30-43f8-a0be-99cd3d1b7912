// Package imports:

// Dart imports:
import 'dart:async';
import 'dart:convert';

// Package imports:
import 'package:intl/intl.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_cdr_session_details.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_charging_session_details.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:oneapp_network_implementation/ev/entity_helper/ev_start_charging.dart';
import 'package:oneapp_network_implementation/ev/entity_helper/ev_stop_charging.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/subjects.dart';

// Project imports:
import '../../../local_repo/vehicle_repo.dart';
import '../../../log/vehicle_analytic_event.dart';
import '../ev_common/ev_enum_const_util.dart';
import '../vehicle_search_charge_station_location/vehicle_search_charge_station_location_util.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

enum ChargingStatus {
  CHARGING_INITIATED,
  CHARGING_FAILED,
  CHARGING_COMPLETED,
  CHARGING_COMPLETED_UNPLUG
}

enum ScreenChangeAction {
  INTRUPTED,
  RETRY,
  COMPLETED,
  START_CDR_TIMER,
  STOP_AT_STATION,
  STOP_CDR_TIMER,
  PAYMENT_INFO,
  PAYMENT_PENDING,
  UNPLUG_VEHICLE,
  UPDATED_ENERGY_MESSAGE
}

class EvChargingManagementBloc extends BlocBase {
  late Function(ScreenChangeAction data, {CdrDetails? cdrData, int? cdrFlag})
      screenActionCallBack;
  vehicleInfo.Payload? vehicleInfoEntity;
  final api = APIClientConfig.oneAppClient;
  ChargeInfo? chargeInfo;
  String? acquisitionDatetime;
  VehicleInfo? vehicleInfoPayload;
  Station? chargeStation;
  String? guid;
  String? email;
  String chargingId = "";
  String partnerName = "";
  String? evseConnectorId;
  bool? isMultidayChargingEnabled;

  Stream<bool> get showSwitcherPopup => _showSwitcherPopup.stream;
  final _showSwitcherPopup = BehaviorSubject<bool>();

  Stream<String> get lastUpdatedTime => _lastUpdatedTime.stream;
  final _lastUpdatedTime = BehaviorSubject<String>();

  Stream<bool> get isCV17Vehicle => _isCV17Vehicle.stream;
  final _isCV17Vehicle = BehaviorSubject<bool>();

  Stream<bool> get isRefreshIsCalled => _isRefreshIsCalled.stream;
  final _isRefreshIsCalled = BehaviorSubject<bool>();

  Stream<int> get chargeRemaining => _chargeRemaining.stream;
  final _chargeRemaining = BehaviorSubject<int>();

  Stream<String> get distanceWithoutAC => _distanceWithoutAC.stream;
  final _distanceWithoutAC = BehaviorSubject<String>();

  Stream<String?> get distanceUnit => _distanceUnit.stream;
  final _distanceUnit = BehaviorSubject<String?>();

  Stream<ChargingStatus?> get chargingStatus => _chargingStatus.stream;
  final _chargingStatus = BehaviorSubject<ChargingStatus?>();

  Stream<String> get chargeDescription => _chargeDescription.stream;
  final _chargeDescription = BehaviorSubject<String>();

  Stream<String> get chargeStatusBadgeText => _chargeStatusBadgeText.stream;
  final _chargeStatusBadgeText = BehaviorSubject<String>();

  Stream<List<String>> get tabTitleList => _tabTitleList.stream;
  final _tabTitleList = BehaviorSubject<List<String>>();

  Stream<bool> get isBatteryLow => _isBatteryLow.stream;
  final _isBatteryLow = BehaviorSubject<bool>();

  Stream<bool> get isCharging => _isCharging.stream;
  final _isCharging = BehaviorSubject<bool>();

  Stream<bool> get isChargingOrWaiting => _isChargingOrWaiting.stream;
  final _isChargingOrWaiting = BehaviorSubject<bool>();

  Stream<bool> get isUnlockingStation => _isUnlockingStation.stream;
  final _isUnlockingStation = BehaviorSubject<bool>();

  Stream<bool> get isEmptyPage => _isEmptyPage.stream;
  final _isEmptyPage = BehaviorSubject<bool>();

  Stream<bool> get isCdrDataNull => _isCdrDataNull.stream;
  final _isCdrDataNull = BehaviorSubject<bool>();

  Stream<SessionDetails> get chargingSessionDetails =>
      _chargingSessionDetails.stream;
  final _chargingSessionDetails = BehaviorSubject<SessionDetails>();

  Stream<String> get chargingSessionStatus => _chargingSessionStatus.stream;
  final _chargingSessionStatus = BehaviorSubject<String>();

  Stream<CdrDetails?> get cdrSessionDetails => _cdrSessionDetails.stream;
  final _cdrSessionDetails = BehaviorSubject<CdrDetails?>();

  Stream<LocationCardHelper> get locationCardDetails =>
      _locationCardDetails.stream;
  final _locationCardDetails = BehaviorSubject<LocationCardHelper>();

  Stream<bool?> get isMultidayChargingAllowed =>
      _isMultidayChargingAllowed.stream;
  final _isMultidayChargingAllowed = BehaviorSubject<bool?>();

  CommonResponse<EvCdrSessionDetails>? CDRResponsePayload = null;

  StreamSubscription? _vehicleInfoSubscription;

  void init(
      Station? selectedChargeStation,
      Function(ScreenChangeAction data, {CdrDetails? cdrData, int? cdrFlag})
          screenAction,
      {String? connectorId,
      String? partnerNameValue}) {
    chargeStation = selectedChargeStation ?? null;
    partnerName = partnerNameValue ?? "";
    screenActionCallBack = screenAction;
    evseConnectorId = connectorId ?? Global.getInstance().evgoConnectorId;
    _isCdrDataNull.sink.add(false);
    _isRefreshIsCalled.sink.add(false);
    _initAsync().whenComplete(() => FireBaseAnalyticsLogger.logInfo(
        "at=EVChargeManagementBloc status=init-async-complete"));
  }

  Future<void> _initAsync() async {
    final vin = Global.getInstance().vin;
    vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
    bool isCY17 = isCY17Vehicle(vehicleInfoEntity!.generation);
    _isCV17Vehicle.sink.add(isCY17);

    isMultidayChargingEnabled =
        isMultidayChargingFeatureEnabled(vehicleInfoEntity!.features!);
    _isMultidayChargingAllowed.sink.add(isMultidayChargingEnabled);

    final evRepository = await VehicleRepo().getEVVehicleInfoRepository(vin);
    _vehicleInfoSubscription = evRepository.evVehicleInfo.listen((vehicleInfo) {
      vehicleInfoPayload = vehicleInfo;
      if (vehicleInfoPayload != null) {
        _updateUIWithVehicleInfo();
      }
    });
    vehicleInfoPayload = evRepository.lastReceived;
    _updateUIWithVehicleInfo();
  }

  Future<void> initiateStartCharging(
      Station? chargeStation, String? guid) async {
    final evApi = APIClientConfig.evApiClient;

    if (chargingId.isEmpty) {
      if (chargeStation != null && guid!.isNotEmpty) {
        _isUnlockingStation.sink.add(true);
        String vin = Global.getInstance().vin!;
        String vinLast4 = vin.substring(vin.length - 4);
        final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();

        EvStartChargingRequest startChargingBody = EvStartChargingRequest(
          email: email,
          guid: guid,
          partnerName: chargeStation.isRoamingEVgo()
              ? chargeStation.evSource.toLowerCase()
              : partnerName.toLowerCase(),
          vin: vin,
        );
        CommonResponse commonResponse;
        String? chargePointStationId = null;
        if (partnerName == EV_PARTNERS.CHARGE_POINT ||
            chargeStation.isRoamingEVgo()) {
          PartnerInfo partnerInfo =
              VehicleSearchChargeStationLocationUtil.getChargePointInfo(
                  chargeStation)!;
          chargePointStationId = partnerInfo.id;
          FireBaseAnalyticsLogger.logInfo(
              "at=initiateStartCharging step=start-chargepoint partnerName=$partnerName stationId=$chargePointStationId vin=_$vinLast4");
          commonResponse = await evApi.initiateStartCharging(
            evStartChargingBody: startChargingBody,
            placeId: chargePointStationId,
            make: vehicleInfo!.make,
          );
        } else {
          FireBaseAnalyticsLogger.logInfo(
              "at=initiateStartCharging step=start-evgo partnerName=$partnerName connectorId=$evseConnectorId vin=_$vinLast4");
          commonResponse = await evApi.initiateStartCharging(
            evStartChargingBody: startChargingBody,
            placeId: evseConnectorId!,
            make: vehicleInfo!.make,
          );
        }
        _isUnlockingStation.sink.add(false);

        final payLoad = commonResponse.response?.payload;
        if (payLoad != null) {
          chargingId = payLoad.startChargeResponse?.first?.chargingId;
          if (chargingId.isNotEmpty) {
            Global.getInstance().chargingId = chargingId;
            Global.getInstance().chargingPartnerName = partnerName;
            Global.getInstance().chargePointStationId = chargePointStationId;
            Global.getInstance().evgoConnectorId = evseConnectorId;
            Global.getInstance().selectedChargeStationText =
                jsonEncode(chargeStation.toJson());

            FireBaseAnalyticsLogger.logInfo(
                "at=initiateStartCharging chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
            FireBaseAnalyticsLogger.logMarketingGroupEvent(
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
              childEventName:
                  VehicleAnalyticsEvent.VEHICLE_EV_PUB_START_CHARGING_SUCCESS,
            );
          }
          fetchChargingSessionDetails(partnerName);
        }

        if (commonResponse.error != null) {
          FireBaseAnalyticsLogger.logMarketingGroupEvent(
            VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
            childEventName:
                VehicleAnalyticsEvent.VEHICLE_EV_PUB_START_CHARGING_FAILURE,
          );
          FireBaseAnalyticsLogger.logErrorAPI(
              VehicleAnalyticsEvent.VEHICLE_EV_CHARGING_SESSIONS_FAILURE);
        }
      } else {
        _isUnlockingStation.sink.add(false);
      }
    } else {
      _isUnlockingStation.sink.add(false);
    }
  }

  void _updateUIWithVehicleInfo() {
    if (vehicleInfoPayload == null) {
      return;
    }
    acquisitionDatetime = vehicleInfoPayload!.acquisitionDatetime;
    chargeInfo = vehicleInfoPayload!.chargeInfo;
    _setLastUpdatedDate();
    if (chargeInfo != null) {
      _isCharging.sink.add(false);
      _addLocationDetails();
      int chargeRemaining = chargeInfo!.chargeRemainingAmount == null
          ? 0
          : chargeInfo!.chargeRemainingAmount!;
      _chargeRemaining.sink.add(chargeRemaining);
      _distanceWithoutAC.sink
          .add(checkForZeroWithTrimmedDecimal(chargeInfo!.evDistance!, 1));
      _distanceUnit.sink.add(chargeInfo!.evDistanceUnit);
      _isBatteryLow.sink.add(chargeRemaining <= 30);

      String timeRemaining =
          getTimeFormattedDuration(chargeInfo!.remainingChargeTime);
      String distanceRemaining =
          checkForZeroWithTrimmedDecimal(chargeInfo!.evDistance!, 1) +
              " " +
              chargeInfo!.evDistanceUnit!;

      _isCharging.sink.add(chargeInfo!.isCharging);
      _isChargingOrWaiting.sink.add(chargeInfo!.isChargingOrWaiting);

      if (chargeInfo!.isCharging) {
        _chargeDescription.sink.add(timeRemaining.isEmpty
            ? ""
            : OneAppString.of().timeUntilFullyCharged(timeRemaining));
        _chargeStatusBadgeText.sink.add(OneAppString.of().charging);
      } else if (chargeInfo!.isPlugWaitingForTimerToCharge) {
        _chargeDescription.sink
            .add(OneAppString.of().distanceRemaining(distanceRemaining));
        _chargeStatusBadgeText.sink.add(OneAppString.of().pluggedIn);
        // } else if (chargeInfo.isChargingComplete) {
        //   _chargeDescription.sink
        //       .add(OneAppString.of().distanceRemaining(distanceRemaining));
        //   _chargeStatusBadgeText.sink
        //       .add(OneAppString.of().chargingCompleted);
      } else {
        _distanceWithoutAC.sink
            .add(checkForZeroWithTrimmedDecimal(chargeInfo!.evDistance!, 1));
        final hideACDistance =
            isMultidayChargingFeatureEnabled(vehicleInfoEntity!.features!);
        if (hideACDistance) {
          _chargeDescription.sink.add(OneAppString.of().withoutAC);
        } else {
          _chargeDescription.sink.add(OneAppString.of().orDistanceWithACon(
              checkForZeroWithTrimmedDecimal(chargeInfo!.evDistanceAC!, 1) +
                  " " +
                  chargeInfo!.evDistanceUnit!));
        }
      }
    } else {
      _isEmptyPage.sink.add(true);
    }
  }

  void _addLocationDetails() {
    LocationCardHelper lc = LocationCardHelper(
        locationName:
            "${chargeStation?.address ?? ""}, ${chargeStation?.city ?? ""}",
        partnerName: VehicleSearchChargeStationLocationUtil.getPartnerTypeText(
            chargeStation!),
        stationName: chargeStation!.name);
    _locationCardDetails.sink.add(lc);
  }

  ///This method will fetch session details
  ///This method will also check if any intruption happens
  Future<void> fetchChargingSessionDetails(String partnerName) async {
    guid = Global.getInstance().guid;
    email = Global.getInstance().userEmail;
    final evApi = APIClientConfig.evApiClient;
    if (chargingId.isEmpty) {
      chargingId = Global.getInstance().chargingId ?? "";
    }
    String vin = Global.getInstance().vin!;
    String vinLast4 = vin.substring(vin.length - 4);
    String? chargePointStationId = Global.getInstance().chargePointStationId;

    String partnerNameVal = partnerName;
    if (chargeStation != null) {
      partnerNameVal = chargeStation!.isRoamingEVgo()
          ? chargeStation!.evSource
          : partnerName;
    }

    if (chargingId.isNotEmpty) {
      final commonResponse =
          await evApi.fetchEvChargingSessionDetails(chargingId, partnerNameVal);

      //If response code is positive we are displaing Charging Screen For Bottom Layout
      final payLoad = commonResponse.response?.payload;
      final commonMessage = commonResponse.response?.messages;
      final responseCode = commonMessage?.responseCode?.toString();
      final description = commonMessage?.description?.toLowerCase();
      if (responseCode == "200" && description == "success") {
        if (payLoad != null) {
          _chargingStatus.sink.add(ChargingStatus.CHARGING_INITIATED);
          //Adding Session Details
          SessionDetails? sessionDetails = payLoad.sessionDetails;
          sessionDetails?.data!.kwh =
              double.parse((sessionDetails.data!.kwh)!.toStringAsFixed(2));
          if (_isRefreshIsCalled.value &&
              _chargingSessionDetails.value.data?.kwh ==
                  sessionDetails?.data?.kwh) {
            noUpdatedEnergyMessage();
            screenActionCallBack(ScreenChangeAction.UPDATED_ENERGY_MESSAGE);
          }
          if (sessionDetails != null) {
            _chargingSessionDetails.sink.add(sessionDetails);
          }

          if (Global.getInstance().evIsNotRealTimeMessage == null) {
            _showSwitcherPopup.sink.add(true);
            Global.getInstance().evIsNotRealTimeMessage = true;
          }

          String sessionStatus = sessionDetails?.data!.status ?? "UNKNOWN";
          if (sessionStatus == "STOPPED") {
            FireBaseAnalyticsLogger.logMarketingGroupEvent(
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
              childEventName:
                  VehicleAnalyticsEvent.VEHICLE_EV_PUB_SESSION_STOPPED_SUCCESS,
            );
            FireBaseAnalyticsLogger.logInfo(
                "at=fetchChargingSessionDetails status=stopped chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");

            // don't clear the local chargingId yet because that changes the screen to "start"
            // clear the Global chargingId for use cases when user has walked away and come back
            Global.getInstance().clearChargingInfo();
          } else if (sessionStatus == "ERROR" || sessionStatus == "FAILED") {
            FireBaseAnalyticsLogger.logMarketingGroupEvent(
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
              childEventName:
                  VehicleAnalyticsEvent.VEHICLE_EV_PUB_SESSION_STOPPED_FAILURE,
            );
            FireBaseAnalyticsLogger.logInfo(
                "at=fetchChargingSessionDetails status=error sessionStatus=$sessionStatus chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");

            // don't clear the local chargingId yet because that changes the screen to "start"
            // clear the Global chargingId for use cases when user has walked away and come back
            Global.getInstance().clearChargingInfo();
          }
          _chargingSessionStatus.sink.add(sessionStatus);
        }
      } else if (responseCode == "400" && description == "failed") {
        //Display Error Message if Any intruption
        FireBaseAnalyticsLogger.logInfo(
            "at=fetchChargingSessionDetails status=api-400-failed chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
        chargingId = "";
        Global.getInstance().clearChargingInfo();
        _chargingStatus.sink.add(ChargingStatus.CHARGING_FAILED);
        screenActionCallBack(ScreenChangeAction.RETRY);
      }

      if (commonResponse.error != null) {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_EV_CHARGING_SESSIONS_FAILURE);
        FireBaseAnalyticsLogger.logInfo(
            "at=fetchChargingSessionDetails status=api-unknown-error chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
      }
    } else {
      Future.delayed(Duration(seconds: 3), () {
        if (!_chargingStatus.hasValue ||
            _chargingStatus.value != ChargingStatus.CHARGING_COMPLETED) {
          initiateStartCharging(chargeStation, guid);
        }
      });
    }
  }

  Future<void> stopCharging() async {
    screenActionCallBack(ScreenChangeAction.START_CDR_TIMER);
    final evApi = APIClientConfig.evApiClient;
    EvStopChargingRequest stopChargingRequestBody =
        EvStopChargingRequest(chargingId: chargingId);
    String vin = Global.getInstance().vin!;
    String vinLast4 = vin.substring(vin.length - 4);
    String? chargePointStationId = Global.getInstance().chargePointStationId;
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();

    if (partnerName == EV_PARTNERS.CHARGE_POINT) {
      final commonResponse = await evApi.stopCharging(
        evStopChargingBody: stopChargingRequestBody,
        make: vehicleInfo!.make,
      );
      final responseMessage = commonResponse.response?.messages;
      final responseCode = responseMessage?.responseCode.toString();
      final description = responseMessage?.description.toLowerCase();
      if (responseCode == "200" && description == "success") {
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
          childEventName:
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_STOP_CHARGING_SUCCESS,
        );
        FireBaseAnalyticsLogger.logInfo(
            "at=stopCharging status=api-success chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");

        _changeMessageScreen();
      } else if (responseCode == "400" && description == "failed") {
        ///Session Stopped
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
          childEventName:
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_STOP_CHARGING_FAILURE,
        );
        FireBaseAnalyticsLogger.logInfo(
            "at=stopCharging status=api-400-failed chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
        Global.getInstance().clearChargingInfo();
        chargingId = "";
        screenActionCallBack(ScreenChangeAction.PAYMENT_INFO, cdrFlag: 1);
      } else if (responseCode == "500") {
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
          childEventName:
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_STOP_CHARGING_FAILURE,
        );
        _changeMessageScreen();
      } else {
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
          childEventName:
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_STOP_CHARGING_CANCELED,
        );
        FireBaseAnalyticsLogger.logInfo(
            "at=stopCharging status=api-unknown-status statusCode=$responseCode chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
        Global.getInstance().clearChargingInfo();
        chargingId = "";
        screenActionCallBack(ScreenChangeAction.PAYMENT_INFO, cdrFlag: 1);
      }

      if (commonResponse.error != null) {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_EV_CHARGING_SESSIONS_FAILURE);
        FireBaseAnalyticsLogger.logInfo(
            "at=stopCharging status=api-error chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4 error=${commonResponse.error.toString()}");
      }
    } else if (partnerName == EV_PARTNERS.EVGO) {
      _changeMessageScreen();
    }
  }

  void _changeMessageScreen() {
    fetchCdrSessionDetails();
  }

  Future<void> fetchCdrSessionDetails() async {
    _isCdrDataNull.sink.add(false);
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();

    final evApi = APIClientConfig.evApiClient;
    final commonResponse = await evApi.fetchEvcdrSessionDetails(
      chargingId: chargingId,
      partnerName: partnerName,
      make: vehicleInfo!.make,
    );
    final payload = commonResponse.response!.payload;
    final commonMessage = commonResponse.response!.messages;
    String vin = Global.getInstance().vin!;
    String vinLast4 = vin.substring(vin.length - 4);
    String? chargePointStationId = Global.getInstance().chargePointStationId;

    if (commonMessage.responseCode == 200 &&
        commonMessage.description.toUpperCase() == "SUCCESS") {
      final cdrDetailsData = payload.cdrDetails?.data;
      if (cdrDetailsData != null) {
        _chargeStatusBadgeText.sink.add(OneAppString.of().chargingCompleted);
        _chargingStatus.sink.add(ChargingStatus.CHARGING_COMPLETED);
        screenActionCallBack(ScreenChangeAction.STOP_CDR_TIMER);
        cdrDetailsData.averagePower =
            double.parse(cdrDetailsData.averagePower?.toStringAsFixed(2) ?? "");

        cdrDetailsData.duration = calculateTimeDifferenceBetweenWithDuration(
            cdrDetailsData.startDateTime ?? DateTime.now(),
            cdrDetailsData.stopDateTime ?? DateTime.now());
        payload.cdrDetails?.data = cdrDetailsData;
        _cdrSessionDetails.sink.add(payload.cdrDetails);
        FireBaseAnalyticsLogger.logInfo(
            "at=fetchCdrSessionDetails status=cdr-received chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
        Global.getInstance().clearChargingInfo();
        chargingId = "";
      } else {
        _isCdrDataNull.sink.add(true);
        FireBaseAnalyticsLogger.logInfo(
            "at=fetchCdrSessionDetails status=cdr-empty chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
        Global.getInstance().clearChargingInfo();
        chargingId = "";
        screenActionCallBack(ScreenChangeAction.PAYMENT_PENDING);
      }
    } else if (commonMessage.responseCode == 400 &&
        commonMessage.description.toUpperCase() == "FAILED") {
      if (partnerName == EV_PARTNERS.CHARGE_POINT) {
        FireBaseAnalyticsLogger.logInfo(
            "at=fetchCdrSessionDetails status=api-400-failed chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
        Global.getInstance().clearChargingInfo();
        chargingId = "";
        screenActionCallBack(ScreenChangeAction.PAYMENT_INFO, cdrFlag: 0);
      } else if (partnerName == EV_PARTNERS.EVGO) {
        FireBaseAnalyticsLogger.logInfo(
            "at=fetchCdrSessionDetails status=api-400-failed chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
        Global.getInstance().clearChargingInfo();
        chargingId = "";
        screenActionCallBack(ScreenChangeAction.PAYMENT_INFO, cdrFlag: 1);
      }
    } else if (commonMessage.responseCode == 406 &&
        commonMessage.description.toUpperCase() == "FAILED") {
      screenActionCallBack(ScreenChangeAction.STOP_AT_STATION);
    } else {
      FireBaseAnalyticsLogger.logInfo(
          "at=stopCharging status=api-unknown-status statusCode=${commonMessage.responseCode.toString()} chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4");
      Global.getInstance().clearChargingInfo();
      chargingId = "";
      screenActionCallBack(ScreenChangeAction.PAYMENT_INFO, cdrFlag: 1);
    }

    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logInfo(
          "at=stopCharging status=api-error chargingId=$chargingId partnerName=$partnerName stationId=$chargePointStationId connectorId=$evseConnectorId vin=_$vinLast4 error=${commonResponse.error.toString()}");
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_EV_CHARGING_SESSIONS_FAILURE);
    }
  }

  void _setLastUpdatedDate() {
    DateTime now = DateTime.now().toLocal();
    String updatedTime = DateFormat("MMM dd @ h:mma").format(now);
    updatedTime =
        updatedTime.replaceAll('@', OneAppString.of().at).capitalize();

    _lastUpdatedTime.sink.add(OneAppString.of().updated(updatedTime));
  }

  void noUpdatedEnergyMessage() {
    bool val = !_isRefreshIsCalled.value;
    _isRefreshIsCalled.sink.add(val);
  }

  void callCustomerSupport() async {
    final vehicleItem =
        await (VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin)
            as FutureOr<vehicleInfo.Payload>);
    bool isToyota = Global.getInstance().appBrand == "T" ? true : false;
    dialerLauncher(getCustomerSupportNumber(vehicleItem.region!, isToyota));
  }

  void dispose() {
    _isCV17Vehicle.close();
    _chargeRemaining.close();
    _distanceWithoutAC.close();
    _distanceUnit.close();
    _chargeDescription.close();
    _chargeStatusBadgeText.close();
    _tabTitleList.close();
    _isBatteryLow.close();
    _isCharging.close();
    _isChargingOrWaiting.close();
    _isEmptyPage.close();
    _lastUpdatedTime.close();
    _chargingStatus.close();
    _chargingSessionDetails.close();
    _cdrSessionDetails.close();
    _locationCardDetails.close();
    _isCdrDataNull.close();
    _chargingSessionStatus.close();
    _isUnlockingStation.close();
    _isRefreshIsCalled.close();
    _vehicleInfoSubscription?.cancel();
    _showSwitcherPopup.close();
    _isMultidayChargingAllowed.close();
  }
}

class LocationCardHelper {
  String? locationName;
  String? partnerName;
  String? stationName;

  LocationCardHelper({this.locationName, this.partnerName, this.stationName});
}
