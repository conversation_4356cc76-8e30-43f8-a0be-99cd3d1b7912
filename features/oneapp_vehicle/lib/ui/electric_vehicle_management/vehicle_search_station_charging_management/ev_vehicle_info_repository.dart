// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/debouncer.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../log/vehicle_analytic_event.dart';

final _realtimeDebouncer = Debouncer(milliseconds: 500);
final _refreshDebouncer = Debouncer(milliseconds: 500);

/// Implements the policy for repeatedly requesting vehicle information. The policy uses a combination
/// of cached information and realtime commands telling the vehicle to update the cache (in the TSC).
///
/// Encapsulates 2 private strategies for finding [VehicleInfo] objects. One uses the cached values from
/// the TSC (2-3 sec). The other contacts the vehicle directly and polls for a realtime response (~45 sec).
class EVVehicleInfoRepository
    with WidgetsBindingObserver
    implements Disposable {
  static const realtimeRefreshInterval = Duration(minutes: 3);

  final String? vin;
  final String? generation;
  final String? brand;

  final _realtimeTimeout = Duration(minutes: 3);
  final _startTime = DateTime.now();
  String? _appRequestNo = '';
  int _realTimeAttempt = 1;
  DateTime _lastForcedUpdate = DateTime.fromMillisecondsSinceEpoch(0);
  DateTime _lastResultSavedAt = DateTime.fromMillisecondsSinceEpoch(0);
  DateTime _lastRequestStartedAt = DateTime.fromMillisecondsSinceEpoch(0);
  Timer? _chargeInfoTimer;

  // Must be dynamic, because it is replaced at minimum on
  // token refresh.
  OneAppClient? get _api => APIClientConfig.oneAppClient;

  Stream<VehicleInfo?> get evVehicleInfo => _evVehicleInfoBroadcast;
  final _evVehicleInfo = BehaviorSubject<VehicleInfo?>();
  late Stream<VehicleInfo?> _evVehicleInfoBroadcast;
  VehicleInfo? lastReceived;

  Stream<ChargeManagementDetailEntity>? get evChargeManagementDetail =>
      _evChargeManagementDetailBroadcast;
  final _evChargeManagementDetail =
      BehaviorSubject<ChargeManagementDetailEntity>();
  Stream<ChargeManagementDetailEntity>? _evChargeManagementDetailBroadcast;
  ChargeManagementDetailEntity? lastChargeManagementDetailReceived;

  EVVehicleInfoRepository({
    this.vin,
    this.generation,
    this.brand,
  }) {
    _evVehicleInfoBroadcast = _evVehicleInfo.stream.asBroadcastStream();
    _evChargeManagementDetailBroadcast =
        _evChargeManagementDetail.stream.asBroadcastStream();
  }

  Future<void> init() async {
    WidgetsBinding.instance.addObserver(this);
    resume();
  }

  void onDispose() {
    _evVehicleInfo.close();
    _evChargeManagementDetail.close();
    _chargeInfoTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        pause();
        break;
      case AppLifecycleState.resumed:
        resume();
        break;
    }
  }

  void pause() {
    if (_chargeInfoTimer?.isActive == true) {
      FireBaseAnalyticsLogger.logInfo(
          "at=evVehicleInfoRepository action=pause");
      _chargeInfoTimer?.cancel();
    }
  }

  void resume() {
    _refreshDebouncer.run(() {
      FireBaseAnalyticsLogger.logInfo(
          "at=evVehicleInfoRepository action=resume");
      _refreshChargeManagementDetail().then(_saveChargeManagementDetail);
    });
    _chargeInfoTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      if (_okToRefresh()) {
        _refreshDebouncer.run(() {
          _lastRequestStartedAt = DateTime.now();
          _refreshChargeManagementDetail().then(_saveChargeManagementDetail);
        });
      }
    });
  }

  void forceRefreshChargeManagementDetail() {
    _refreshDebouncer.run(() {
      _refreshChargeManagementDetail()
          .then((value) => _saveChargeManagementDetail(value));
    });
  }

  bool _okToRefresh() {
    //todo: tim . check this default value
    if (_evChargeManagementDetail.isClosed) {
      FireBaseAnalyticsLogger.logError(
          "at=evVehicleInfoRepository step=refresh-timer error=stream-closed");
      return false;
    }
    if (_lastRequestStartedAt.isAfter(_lastResultSavedAt)) {
      final duration = _durationSince(_lastRequestStartedAt);
      FireBaseAnalyticsLogger.logError(
          "at=evVehicleInfoRepository step=refresh-timer error=request-in-progress time-since-request=${duration.inSeconds}s");
      return false;
    }

    final duration = _durationSince(_lastResultSavedAt);
    if (duration < Duration(seconds: 25)) {
      FireBaseAnalyticsLogger.logError(
          "at=evVehicleInfoRepository step=refresh-timer error=too-soon time-since-save=${duration.inSeconds}s");
      // Realign timer in reaction to significant delay
      final bestFiringTime = _lastResultSavedAt.add(Duration(seconds: 30));
      Future.delayed(_durationUntil(bestFiringTime)).then((_) {
        pause();
        resume();
      });
      return false;
    }
    return true;
  }

  Duration _durationSince(DateTime mark) {
    final ms =
        DateTime.now().millisecondsSinceEpoch - mark.millisecondsSinceEpoch;
    return Duration(milliseconds: ms);
  }

  Duration _durationUntil(DateTime mark) {
    final ms =
        mark.millisecondsSinceEpoch - DateTime.now().millisecondsSinceEpoch;
    return Duration(milliseconds: ms);
  }

  /// Option 3: temporarily allow other code to donate any
  /// detail they retrieved. This is most useful for code
  /// which issues and follows remote commands.
  void receivedExtraChargeManagementDetail(ChargeManagementDetailEntity value) {
    _saveChargeManagementDetail(value);
  }

  void _saveChargeManagementDetail(ChargeManagementDetailEntity? value) {
    if (value != null) {
      _lastResultSavedAt = DateTime.now();
      if (!_evChargeManagementDetail.isClosed) {
        lastChargeManagementDetailReceived = value;
        _evChargeManagementDetail.sink.add(value);
      }
      final vehicleInfo = value.payload?.vehicleInfo;
      if (vehicleInfo != null && !_evVehicleInfo.isClosed) {
        lastReceived = vehicleInfo;
        _evVehicleInfo.sink.add(lastReceived);
      }
      final chargeInfo = vehicleInfo?.chargeInfo;
      if (chargeInfo != null) {
        Global.getInstance().chargeRemainingAmount =
            chargeInfo.chargeRemainingAmount ?? 0;
      }
    } else {
      FireBaseAnalyticsLogger.logError(
          "evVehicleInfoRepository step=_saveVehicleInfo value=null");
    }
  }

  Future<ChargeManagementDetailEntity?> _refreshChargeManagementDetail() async {
    final chargeManagementDetail = await _fetchChargeManagementDetailFromTSC();
    bool shouldForceUpdate = _shouldForceRealTimeUpdate();
    if (shouldForceUpdate) {
      await triggerVehicleToUpdateTSC();
      _lastForcedUpdate = DateTime.now();
    }
    return chargeManagementDetail;
  }

  // Policy: every 3 mins refresh we must force the vehicle to update the TSC cache
  bool _shouldForceRealTimeUpdate() {
    final _lastForcedUpdateDuration =
        DateTime.now().difference(_lastForcedUpdate);
    return _lastForcedUpdateDuration.compareTo(realtimeRefreshInterval) > 0;
  }

  /// Strategy 1: get the cached info from the TSC
  Future<ChargeManagementDetailEntity?>
      _fetchChargeManagementDetailFromTSC() async {
    final commonResponse =
        await _api!.fetchChargeManagementDetail(generation!, vin!, brand!);
    final chargeManagementDetail = commonResponse.response;
    final payload = chargeManagementDetail?.payload;
    if (payload != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(VehicleAnalyticsEvent
          .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS);
      return chargeManagementDetail;
    }
    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(VehicleAnalyticsEvent
          .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE);
    } else {
      FireBaseAnalyticsLogger.logError(
          "at=evVehicleInfoRepository step=_fetchVehicleInfoFromTSC step=no-response-error response=${commonResponse.toString()}");
    }
    return null;
  }

  /// Strategy 2a: some other process triggered a realtime
  /// command or request. We need to watch for the response
  /// and deliver it to all subscribers.
  Future<void> watchForRealTimeResponse(String? requestNo) async {
    _appRequestNo = requestNo;
    final chargeManagementDetail = await _pollForVehicleRealTimeResponse();
    _saveChargeManagementDetail(chargeManagementDetail);
  }

  /// Tell the vehicle to update the TSC with its latest information, but do not wait for a reply.
  Future<void> triggerVehicleToUpdateTSC() async {
    _realtimeDebouncer.run(() async {
      await _submitRealTimeRequestToVehicle();
    });
  }

  Future<String?> _submitRealTimeRequestToVehicle() async {
    String deviceId = Global.getInstance().fcmDeviceId!;
    final postResponse = await _api!
        .postRealTimeStatusRequest(vin!, brand!, generation!, deviceId);
    final payLoad = postResponse.response?.payload;
    // progressHandlerCallback(false);
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_SUCCESS,
          category: LogCategory.FL_VEHI);
      return payLoad.appRequestNo ?? '';
    }
    if (postResponse.error != null) {
      FireBaseAnalyticsLogger.logError(
          VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
          category: LogCategory.FL_VEHI);
    } else {
      FireBaseAnalyticsLogger.logError(
          "at=evVehicleInfoRepository step=_submitRealTimeRequestToVehicle step=no-response-error response=${postResponse.toString()}");
    }
    return null;
  }

  Future<ChargeManagementDetailEntity?>
      _pollForVehicleRealTimeResponse() async {
    final commonResponse = await _api!.fetchClimateRealTimeStatus(
        generation!, vin!, _appRequestNo!, brand!, _realTimeAttempt);
    final chargeManagementDetail = commonResponse.response;
    final payLoad = chargeManagementDetail?.payload;
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);

      int? status = payLoad.realtimeStatusResult?.status;
      if (status != null && status == 0) {
        int? result = payLoad.realtimeStatusResult?.result;
        if (result != null && result == 0) {
          return chargeManagementDetail;
        } else {
          FireBaseAnalyticsLogger.logError(
              "at=_pollForVehicleRealTimeResponse status=$status result=$result response=${commonResponse.toString()}");
          return null;
        }
      } else {
        final currentTime = DateTime.now();
        final duration = currentTime.difference(_startTime);
        if (duration.compareTo(_realtimeTimeout) <= 0) {
          _realTimeAttempt++;
          return await Future.delayed(
            Duration(seconds: 3),
            () => _pollForVehicleRealTimeResponse(),
          );
        } else {
          FireBaseAnalyticsLogger.logError(
              "at=_pollForVehicleRealTimeResponse status=$status response=${commonResponse.toString()}");
          return null;
        }
      }
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
        _realTimeAttempt++;
        return await Future.delayed(
          Duration(seconds: 3),
          () => _pollForVehicleRealTimeResponse(),
        );
      } else {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
            category: LogCategory.FL_VEHI);
        FireBaseAnalyticsLogger.logError(
            "at=_pollForVehicleRealTimeResponse step=error-but-not-500 response=${commonResponse.toString()}");
        return null;
      }
    } else {
      FireBaseAnalyticsLogger.logError(
          "at=_pollForVehicleRealTimeResponse step=no-response-error response=${commonResponse.toString()}");
    }

    return null;
  }
}
