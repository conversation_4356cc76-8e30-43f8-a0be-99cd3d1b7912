// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';

// Project imports:
import '../../../log/vehicle_analytic_event.dart';
import '../ev_common/ev_enum_const_util.dart';

class EvChargeConfirmationPage extends StatefulWidget {
  final String? chargingAmount;
  final String? accountNumber;
  final int? cdrFlagValue;
  final String? partnerName;

  EvChargeConfirmationPage(
      {Key? key,
      required this.chargingAmount,
      required this.accountNumber,
      this.cdrFlagValue,
      required this.partnerName})
      : super(key: key);

  @override
  _EvChargeConfirmationPageState createState() =>
      _EvChargeConfirmationPageState();
}

class _EvChargeConfirmationPageState extends State<EvChargeConfirmationPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return OneAppScaffold(
        backgroundColor: _colorUtil.tile01, body: _buildBody());
  }

  Widget _buildBody() {
    return Column(
      children: [
        Expanded(
            child: Center(
                child: Padding(
                    padding: EdgeInsets.only(left: 8.h, right: 8.0),
                    child: topContent()))),
        Align(
            alignment: Alignment.bottomCenter,
            child: Container(
                padding: EdgeInsets.only(bottom: 40.h),
                child: CustomDefaultButton(
                  backgroundColor: _colorUtil.button01b,
                  buttonTextColor: _colorUtil.button01a,
                  text: OneAppString.of().commonOK,
                  press: () {
                    if (widget.cdrFlagValue == 0) {
                      goBackToNative();
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  borderColor: _colorUtil.button01b,
                  horizontalPadding: 22.w,
                  verticalPadding: 6.h,
                )))
      ],
    );
  }

  Widget topContent() {
    ///cdr flag used to display the content in the payment Screen
    ///0 -> payment Pending
    ///1 -> Session Stoped
    ///default -> payment successfull
    if (widget.cdrFlagValue == 0) {
      FireBaseAnalyticsLogger.logScreenVisit(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHARGE_PENDING_PAGE);
      return _paymentPendingContent();
    }
    if (widget.cdrFlagValue == 1) {
      FireBaseAnalyticsLogger.logScreenVisit(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHARGE_CANCELED_PAGE);
      return _sessionStoppedContent();
    }
    // if (widget.chargingAmount.toLowerCase().contains("free")) {
    //   return _paymentFreeContent();
    // }
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHARGE_CONFIRMED_PAGE);
    return _paymentSuccessfullContent();
  }

  Column _paymentSuccessfullContent() {
    if (widget.partnerName == EV_PARTNERS.CHARGE_POINT &&
        (widget.chargingAmount?.toLowerCase().contains("free") ?? false)) {
      return _paymentFreeContent();
    } else {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text("${widget.chargingAmount ?? ""}",
              maxLines: 1,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: TextStyleExtension().newStyleWithColor(
                  ThemeConfig.current().textStyleUtil.subHeadline4,
                  ThemeConfig.current().colorUtil.tertiary03)),
          SizedBox(
            height: 20.h,
          ),
          widget.partnerName != EV_PARTNERS.EVGO
              ? Text(
                  widget.accountNumber != null
                      ? OneAppString.of()
                          .paymentChargedWithCardDetails(widget.accountNumber!)
                      : OneAppString.of().paymentChargedWithoutCardDetails,
                  textAlign: TextAlign.center,
                  style: TextStyleExtension().newStyleWithColor(
                      ThemeConfig.current().textStyleUtil.body3,
                      ThemeConfig.current().colorUtil.tertiary05),
                )
              : Text(
                  OneAppString.of().evgoFreeChargingMessage,
                  textAlign: TextAlign.center,
                  style: TextStyleExtension().newStyleWithColor(
                      ThemeConfig.current().textStyleUtil.body3,
                      ThemeConfig.current().colorUtil.tertiary05),
                ),
          SizedBox(
            height: 20.h,
          ),
          CircleAvatar(
              backgroundColor: _colorUtil.secondary02,
              child: Icon(
                Icons.check,
                color: _colorUtil.secondary01,
              )),
        ],
      );
    }
  }

  Column _paymentPendingContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(OneAppString.of().paymentPendingTitle,
            textAlign: TextAlign.center,
            style: TextStyleExtension().newStyleWithColor(
                ThemeConfig.current().textStyleUtil.subHeadline4,
                ThemeConfig.current().colorUtil.tertiary03)),
        SizedBox(
          height: 20.h,
        ),
        Text(
          OneAppString.of().paymentPendingMessage,
          textAlign: TextAlign.center,
          style: TextStyleExtension().newStyleWithColor(
              ThemeConfig.current().textStyleUtil.body3,
              ThemeConfig.current().colorUtil.tertiary00),
        ),
        SizedBox(
          height: 20.h,
        ),
        CircleAvatar(
            backgroundColor: _colorUtil.secondary02,
            child: Icon(
              Icons.check,
              color: _colorUtil.secondary01,
            )),
      ],
    );
  }

  Column _sessionStoppedContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(OneAppString.of().sessionStoppedHeading,
            textAlign: TextAlign.center,
            style: TextStyleExtension().newStyleWithColor(
                ThemeConfig.current().textStyleUtil.subHeadline4,
                ThemeConfig.current().colorUtil.tertiary03)),
        SizedBox(
          height: 20.h,
        ),
        Text(
          OneAppString.of().sessionStoppedSubHeading,
          textAlign: TextAlign.center,
          style: TextStyleExtension().newStyleWithColor(
              ThemeConfig.current().textStyleUtil.body3,
              ThemeConfig.current().colorUtil.tertiary05),
        ),
        SizedBox(
          height: 20.h,
        ),
      ],
    );
  }

  Column _paymentFreeContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CircleAvatar(
            backgroundColor: _colorUtil.secondary02,
            child: Icon(
              Icons.check,
              color: _colorUtil.secondary01,
            )),
        SizedBox(
          height: 20.h,
        ),
        Text(OneAppString.of().evPaymentFreeContentTitle,
            textAlign: TextAlign.center,
            style: TextStyleExtension().newStyleWithColor(
                ThemeConfig.current().textStyleUtil.headline1,
                ThemeConfig.current().colorUtil.tertiary03)),
        SizedBox(
          height: 20.h,
        ),
        Text(
          OneAppString.of().evPaymentFreeContentSubTitle,
          textAlign: TextAlign.center,
          style: TextStyleExtension().newStyleWithColor(
              ThemeConfig.current().textStyleUtil.body3,
              ThemeConfig.current().colorUtil.tertiary05),
        ),
      ],
    );
  }
}
