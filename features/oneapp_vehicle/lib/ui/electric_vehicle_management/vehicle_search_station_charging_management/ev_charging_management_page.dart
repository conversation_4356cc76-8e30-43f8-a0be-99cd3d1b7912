// Dart imports:
import 'dart:convert';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:bot_toast/bot_toast.dart';
import 'package:ev_module/charge_info/blocs/partner_details_Info_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/bottom_dynamic_confirmation_dialog.dart';
import 'package:oneapp_common/widget/dialog/bottom_error_conformation_dialog.dart';
import 'package:oneapp_common/widget/graphics/fuel_electric_graphics.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:oneapp_common/widget/toast/toast_message_border.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_cdr_session_details.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_charging_session_details.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:super_tooltip/super_tooltip.dart';

// Project imports:
import '../../../log/vehicle_analytic_event.dart';
import '../ev_common/default_button_with_timer.dart';
import '../ev_common/ev_enum_const_util.dart';
import '../vehicle_search_charge_station_location/helper/evgo_complimentry_expiry_validation.dart';
import '../vehicle_search_charge_station_location/vehicle_search_charge_station_location_util.dart';
import 'ev_chage_timer_scheduler_bloc.dart';
import 'ev_charge_confirmation.dart';
import 'ev_charging_management_bloc.dart';

class EvChargingManagementPage extends StatefulWidget
    with WidgetsBindingObserver {
  final ScrollController? scrollController;
  final Station? selectedChargeStation;
  final String? connectorId;
  final bool? isFromNative;

  const EvChargingManagementPage({
    this.scrollController,
    this.selectedChargeStation,
    this.connectorId,
    this.isFromNative,
  }) : super();

  @override
  _EvChargingManagementPageState createState() =>
      _EvChargingManagementPageState();
}

class _EvChargingManagementPageState extends State<EvChargingManagementPage>
    with
        AutomaticKeepAliveClientMixin<EvChargingManagementPage>,
        TickerProviderStateMixin,
        WidgetsBindingObserver {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final EvChargingManagementBloc _bloc = EvChargingManagementBloc();

  final PartnerDetailsInfoBloc _partnerDetailsInfoBloc =
      PartnerDetailsInfoBloc();
  EvChargeTimer? _evChargeTimer;
  late AnimationController _animationController;
  Station? chargeStation;
  String partnerName = "";
  final dialog = BottomDynamicConfirmationDialog();
  int lowBatteryValue = 30;
  late BuildContext _pageContext;
  SuperTooltip? tooltip;
  BuildContext? chargeDescWidgetContext;

  @override
  void initState() {
    chargeStation = widget.selectedChargeStation ?? null;
    if (chargeStation == null) {
      // TOOD: if null?
      final jsonData =
          jsonDecode(Global.getInstance().selectedChargeStationText!);
      chargeStation = Station.fromJson(jsonData);
    }
    if (chargeStation != null) {
      bool isRoamingPartner = chargeStation?.isRoamingPartner ?? false;
      partnerName = (isRoamingPartner
              ? VehicleSearchChargeStationLocationUtil.getPartnerTypeText(
                  chargeStation!,
                  toLowerCase: true)
              : (chargeStation?.evSource)) ??
          "";
    }

    _evChargeTimer = EvChargeTimer.getEVSchedulerInstance(
      sessionDetailsCallback: () =>
          _bloc.fetchChargingSessionDetails(partnerName),
      cdrSessionDetailsCallback: () => _bloc.fetchCdrSessionDetails(),
    );
    _bloc.init(chargeStation, _findActions,
        connectorId: widget.connectorId, partnerNameValue: partnerName);
    WidgetsBinding.instance.addObserver(this);
    _evChargeTimer?.init();

    super.initState();
    _animationController =
        AnimationController(vsync: this, duration: Duration(seconds: 1));
    _animationController.repeat(reverse: true);
    FireBaseAnalyticsLogger.logInfo(
        VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHARGE_SESSION_PAGE);

    _bloc.showSwitcherPopup.listen((event) {
      if (event == true) {
        Future.delayed(const Duration(seconds: 1), () {
          _displayDirectionSwitcherHintToast(
              OneAppString.of().notRealTimeHeading,
              OneAppString.of().notRealTimeSubHeading);
        });
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    debugPrint('From Widget Page: ${state.toString()}');
    _evChargeTimer?.didChangeAppLifecycleState(state);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    _pageContext = context;
    return SafeArea(
        child: Material(
            child: Navigator(
                onGenerateRoute: (_) =>
                    MaterialPageRoute(builder: (materialContext) {
                      return Builder(builder: (builderContext) {
                        return BlocProvider(
                            child: OneAppScaffold(
                                backgroundColor: _colorUtil.tile01,
                                body: Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: StreamBuilder<bool>(
                                      stream: _bloc.isEmptyPage,
                                      builder: (context, snapshot) {
                                        return (snapshot.hasData &&
                                                snapshot.data!)
                                            ? _noDataAvailableLayout()
                                            : _dataAvailableLayout();
                                      }),
                                )),
                            bloc: _bloc);
                      });
                    }))));
  }

  _dataAvailableLayout() {
    return SingleChildScrollView(
        controller: widget.scrollController,
        child: Container(
            // padding: EdgeInsets.only(top: 10.h, left: 20.h, right: 20.h),
            child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            bottomSheetCustomAppBar(OneAppString.of().chargeInfo,
                onBackPressed: () {
              if (widget.isFromNative ?? true) {
                goBackToNative();
              } else {
                Navigator.of(context).pop(true);
              }
            }, elevation: 0),
            _topLayout(),
            StreamBuilder<ChargingStatus?>(
                stream: _bloc.chargingStatus,
                builder: (context, snapshot) {
                  return snapshot.hasData
                      ? _bottomLayout(snapshot.data)
                      : _shimmerLayout();
                }),
          ],
        )));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _evChargeTimer?.dispose();
    _bloc.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  ///Need to change the stream
  Widget _bottomLayout(ChargingStatus? data) {
    ChargingStatus? chargingStatus = data;
    if (chargingStatus == ChargingStatus.CHARGING_INITIATED) {
      return _onChargingLayout();
    } else if (chargingStatus == ChargingStatus.CHARGING_COMPLETED) {
      return _chargingStoppedLayout();
    } else {
      return _shimmerLayout();
    }
  }

  _displayDirectionSwitcherHintToast(String title, String description) {
    try {
      _showAttachedToast(
          target: Offset(
              ScreenUtil().screenWidth / 2, ScreenUtil().screenHeight * 0.90),
          childWidget: _directionHintLayout(title, description),
          duration: 3);
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_VEHI);
    }
  }

//This is Popup Hit Layout
  Widget _directionHintLayout(String title, String description) {
    return Wrap(
      children: [
        Container(
            width: ScreenUtil().screenWidth / 1.1,
            decoration: ShapeDecoration(
              color: _colorUtil.tile03,
              shape: ToastMessageTopBorder(),
              shadows: [
                BoxShadow(
                  color: _colorUtil.tertiary03,
                  blurRadius: 0.5,
                ),
              ],
            ),
            alignment: Alignment.center,
            padding:
                EdgeInsets.only(left: 8.w, top: 16.h, bottom: 16.h, right: 8.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  child: Padding(
                    padding: EdgeInsets.all(10.w),
                    child: SvgPicture.asset(
                      chargeStationIcon,
                      height: 20.h,
                      width: 20.w,
                      colorFilter: ColorFilter.mode(
                        _colorUtil.primary01,
                        BlendMode.srcIn,
                      ),
                      allowDrawingOutsideViewBox: true,
                    ),
                  ),
                  decoration: BoxDecoration(
                    color: _colorUtil.primary02,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                Text(
                  title,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
                ),
                SizedBox(
                  height: 10.h,
                ),
                Text(
                  description,
                  textAlign: TextAlign.center,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1, _colorUtil.tertiary05),
                )
              ],
            )),
      ],
    );
  }

  CancelFunc _showAttachedToast(
      {Offset? target, Widget? childWidget, required int duration}) {
    return BotToast.showAttachedWidget(
        target: target,
        animationDuration: Duration(milliseconds: 500),
        duration: Duration(seconds: duration),
        attachedBuilder: (cancel) => childWidget!);
  }

  Widget _onChargingLayout() {
    return StreamBuilder<String>(
        stream: _bloc.chargingSessionStatus,
        builder: (context, snapshot) {
          final sessionStatus = snapshot.hasData ? snapshot.data : "UNKNOWN";
          final showStopAtStationMessage =
              (partnerName == EV_PARTNERS.EVGO && sessionStatus == "ACTIVE");
          final showStopChargingText = partnerName != EV_PARTNERS.EVGO ||
              (sessionStatus != "ACTIVE" && sessionStatus != "STOPPED");
          final disableStopChargingButton = showStopAtStationMessage;
          final showErrorText = sessionStatus == "ERROR";
          final showFailedText = sessionStatus == "FAILED";
          final showErrorOrFailed = showErrorText || showFailedText;

          String stopButtonText = OneAppString.of().commonContinue;
          if (showErrorOrFailed) {
            stopButtonText = OneAppString.of().commonOK;
          } else if (showStopChargingText) {
            stopButtonText = OneAppString.of().stopCharging;
          }

          return Column(
            children: [
              StreamBuilder<PartnerDetails?>(
                  stream: _partnerDetailsInfoBloc.partnerDetails,
                  builder: (context, snapshot) {
                    if (snapshot.hasData &&
                        snapshot.data != null &&
                        partnerName == EV_PARTNERS.EVGO) {
                      return EVGOComplimentaryExpiryValidation();
                    } else {
                      return Container();
                    }
                  }),
              _chargingDetils(),
              SizedBox(
                height: 60.0,
              ),
              if (!showErrorOrFailed) _refreshButton(),
              if (!showErrorOrFailed) _lastUpdatedTime(),
              if (showErrorOrFailed)
                Align(
                  alignment: Alignment.center,
                  child: Center(
                    child: RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body3,
                          _colorUtil.error01,
                        ),
                        children: [
                          TextSpan(
                              text: showErrorText
                                  ? OneAppString.of().chargeSessionError
                                  : OneAppString.of().chargeSessionFailure),
                        ],
                      ),
                    ),
                  ),
                ),
              if (showStopAtStationMessage) SizedBox(height: 10.h),
              if (showStopAtStationMessage)
                Align(
                  alignment: Alignment.center,
                  child: Center(
                    child: RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body3,
                          _colorUtil.button02a,
                        ),
                        children: [
                          TextSpan(text: OneAppString.of().useStationToStop),
                        ],
                      ),
                    ),
                  ),
                ),
              Container(
                margin: EdgeInsets.only(top: 30.h, bottom: 16.h),
                child: CustomDefaultButton(
                  backgroundColor: _colorUtil.primaryButton02,
                  buttonTextColor: _colorUtil.primaryButton01,
                  disabledBackgroundColor: _colorUtil.button02d,
                  disabledButtonTextColor: _colorUtil.button05a,
                  primaryButtonState: disableStopChargingButton
                      ? PrimaryButtonState.INACTIVE
                      : PrimaryButtonState.ACTIVE,
                  text: stopButtonText,
                  press: disableStopChargingButton
                      ? null
                      : () async {
                          if (showErrorOrFailed) {
                            if (widget.isFromNative ?? true) {
                              goBackToNative();
                            } else {
                              Navigator.of(_pageContext).pop();
                            }
                          } else {
                            stopCharging();
                          }
                        },
                  borderColor: disableStopChargingButton
                      ? _colorUtil.button01b
                      : _colorUtil.button02d,
                  horizontalPadding: 22.w,
                  verticalPadding: 6.h,
                ),
              ),
            ],
          );
        });
  }

  Widget _chargingStoppedLayout() {
    return StreamBuilder<CdrDetails?>(
        stream: _bloc.cdrSessionDetails,
        builder: (context, snapshot) {
          final hasCdr = snapshot.hasData;
          if (hasCdr) {
            FireBaseAnalyticsLogger.logScreenVisit(
                VehicleAnalyticsEvent.VEHICLE_EV_PUB_CDR_PAGE);
          }
          return hasCdr
              ? Column(
                  children: [
                    _chargedDetils(snapshot.data!),
                    _paymentEndCardText(snapshot.data!.paymentInfo),
                    Container(
                        margin: EdgeInsets.only(top: 20.h, bottom: 16.h),
                        child: CustomDefaultButton(
                          backgroundColor: _colorUtil.button01b,
                          buttonTextColor: _colorUtil.button01a,
                          text: OneAppString.of().commonSmallOk,
                          press: () {
                            _findActions(ScreenChangeAction.PAYMENT_INFO,
                                cdrData: snapshot.data);
                          },
                          borderColor: _colorUtil.button01b,
                          horizontalPadding: 22.w,
                          verticalPadding: 6.h,
                        ))
                  ],
                )
              : _ChargedShimmerLayout();
        });
  }

  // Description - Common for both - Time remaining to complete charge or distance left with ac on
  Widget _chargeDescriptionLayout(bool showTooltipButton) {
    return StreamBuilder<String>(
      stream: _bloc.chargeDescription,
      builder: (context, snapshot) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              snapshot.hasData ? snapshot.data! : "",
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary05),
            ),
            showTooltipButton
                ? InkWell(
                    onTap: () {
                      showChargeDescToolTip(chargeDescWidgetContext);
                    },
                    child: SizedBox(
                      width: 24.w,
                      height: 24.h,
                      child: Image.asset(infoIcon, fit: BoxFit.cover),
                    ),
                  )
                : Container()
          ],
        );
      },
    );
  }

  Widget _topLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 15.h, bottom: 6.h),
          child: _chargePercentCommonLayout(),
        ),
        _chargeMeterCommonLayout(),
        Padding(
          padding: EdgeInsets.only(top: 10.h),
          child: Container(
            height: 76.h,
            width: 311.w,
            alignment: Alignment.center,
            child: StreamBuilder<bool>(
              stream: _bloc.isChargingOrWaiting,
              builder: (context, snapshot) {
                return (snapshot.hasData && snapshot.data!)
                    ? _chargeStatusPlugInLayout()
                    : _distanceToEmptyLayout();
              },
            ),
          ),
        ),
        StreamBuilder<bool?>(
          stream: _bloc.isMultidayChargingAllowed,
          builder: (context, snapshot) {
            return (snapshot.hasData && snapshot.data!)
                ? Builder(
                    builder: (BuildContext widgetContext) {
                      chargeDescWidgetContext = widgetContext;
                      if (Global.getInstance().chargeDescTooltipShown == null) {
                        triggerChargeDescTooltip();
                        Global.getInstance().chargeDescTooltipShown = true;
                      }
                      return _chargeDescriptionLayout(true);
                    },
                  )
                : _chargeDescriptionLayout(false);
          },
        ),
        Padding(
          padding:
              EdgeInsets.only(top: 20.h, bottom: 20.h, left: 20.w, right: 20.w),
          child: _locationCard(),
        ),
      ],
    );
  }

  // Common layout for percentage
  Widget _chargePercentCommonLayout() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        StreamBuilder<bool>(
            stream: _bloc.isCharging,
            builder: (context, snapshot) {
              return (snapshot.hasData && snapshot.data!)
                  ? _electricIconWithAnimation()
                  : _electricIconWithoutAnimation();
            }),
        Padding(
          padding: EdgeInsets.only(left: 6.w),
          child: StreamBuilder<int>(
              stream: _bloc.chargeRemaining,
              builder: (context, snapshot) {
                return RichText(
                    text: TextSpan(children: <TextSpan>[
                  TextSpan(
                      text: snapshot.hasData ? snapshot.data.toString() : "--",
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.title2, _colorUtil.tertiary00)),
                  TextSpan(
                      text: snapshot.hasData ? "%" : "",
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.subHeadline4, _colorUtil.tertiary00)),
                ]));
              }),
        )
      ],
    );
  }

  // Animate electric icon
  Widget _electricIconWithAnimation() {
    return FadeTransition(
        opacity: _animationController,
        child: SvgPicture.asset(
          electricStationIcon,
          height: 32.w,
          width: 55.w,
          colorFilter: ColorFilter.mode(
            _colorUtil.tertiary00,
            BlendMode.srcIn,
          ),
          allowDrawingOutsideViewBox: false,
        ));
  }

  //  Electric icon without animation
  Widget _electricIconWithoutAnimation() {
    return StreamBuilder<int>(
        stream: _bloc.chargeRemaining,
        builder: (context, snapshot) {
          int chargeRemaining = snapshot.hasData ? snapshot.data! : 0;
          return SvgPicture.asset(
            electricStationIcon,
            height: 25.33.h,
            width: 43.43.w,
            colorFilter: ColorFilter.mode(
              chargeRemaining <= lowBatteryValue
                  ? chargeRemaining == 0
                      ? _colorUtil.tertiary00
                      : _colorUtil.primary01
                  : _colorUtil.secondary01,
              BlendMode.srcIn,
            ),
            allowDrawingOutsideViewBox: false,
          );
        });
  }

  //Common charging meter layout (plugin/plugout)
  Widget _chargeMeterCommonLayout() {
    return Container(
      width: 168.w,
      child: StreamBuilder<int>(
          stream: _bloc.chargeRemaining,
          builder: (context, snapshot) {
            int chargeRemaining = snapshot.hasData ? snapshot.data! : 0;
            return FuelElectricOverviewGraphics(
                animatedDuration: Duration(seconds: 1),
                currentValueInPercent: chargeRemaining,
                size: 8.h,
                borderRadius: 3.h,
                backgroundColor: _colorUtil.tertiary10,
                borderColor: _colorUtil.tertiary10,
                isGradient: false,
                progressColor: chargeRemaining <= lowBatteryValue
                    ? _colorUtil.primary01
                    : _colorUtil.secondary01);
          }),
    );
  }

  _locationCard() {
    return StreamBuilder<LocationCardHelper>(
        stream: _bloc.locationCardDetails,
        builder: (context, snapshot) {
          return Container(
            decoration: BoxDecoration(
              color: _colorUtil.tertiary15,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: _colorUtil.tertiary00.withOpacity(0.1),
                  spreadRadius: 5,
                  blurRadius: 5,
                  offset: Offset(0, 3), // changes position of shadow
                ),
              ],
            ),
            child: Card(
              color: _colorUtil.tertiary15,
              elevation: 0.0,
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: _colorUtil.success02,
                  radius: 20,
                  child: Icon(
                    Icons.location_on_outlined,
                    color: _colorUtil.success01,
                  ),
                ),
                title: Text(
                  snapshot.hasData ? snapshot.data!.partnerName! : "",
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.body4, _colorUtil.tertiary00),
                ),
                subtitle: Text(
                  "${snapshot.hasData ? snapshot.data!.stationName : ""} \n${snapshot.hasData ? snapshot.data!.locationName : ""}",
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1, _colorUtil.tertiary03),
                  textAlign: TextAlign.start,
                ),
              ),
            ),
          );
        });
  }

// Empty layout if data is null
  Widget _noDataAvailableLayout() {
    return Container(
      width: double.maxFinite,
      margin: EdgeInsets.only(top: 34.h),
      child: Center(
        child: Text(
          OneAppString.of().dataNotAvailableFor(OneAppString.of().chargeInfo),
          textAlign: TextAlign.center,
          style: TextStyleExtension().newStyleWithColor(
              _textStyleUtil.callout1, _colorUtil.tertiary07),
        ),
      ),
    );
  }

  _lastUpdatedTime() {
    return StreamBuilder<String>(
        stream: _bloc.lastUpdatedTime,
        builder: (context, snapshot) {
          return Text(
            snapshot.hasData ? snapshot.data! : "",
            style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.caption1, _colorUtil.tertiary05),
          );
        });
  }

  _refreshButton() {
    return Padding(
      padding: EdgeInsets.only(bottom: 6.h, top: 6.h),
      child: CircleAvatar(
          backgroundColor: _colorUtil.button02b,
          child: IconButton(
            icon: Icon(Icons.refresh_outlined),
            color: _colorUtil.tertiary00,
            onPressed: () {
              refreshUI();
            },
          )),
    );
  }

  _chargingDetils() {
    return StreamBuilder<SessionDetails>(
        stream: _bloc.chargingSessionDetails,
        builder: (context, snapshot) {
          return (snapshot.data != null && snapshot.hasData)
              ? Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [
                      ListTile(
                          leading: Text(
                            "Start Time",
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body1, _colorUtil.tertiary03),
                          ),
                          trailing: Text(
                            "${DateFormat.jm().format(snapshot.data!.data!.startDatetime!.toLocal()).toLowerCase().replaceAll(RegExp(r"\s+"), "")}",
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body1, _colorUtil.tertiary03),
                          )),
                      Divider(
                        height: 1,
                        color: _colorUtil.tertiary03,
                      ),
                      Visibility(
                        visible: !(partnerName == EV_PARTNERS.EVGO &&
                            snapshot.data!.data!.kwh == 0.0),
                        child: ListTile(
                            leading: Text(
                              OneAppString.of().energy,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.body1, _colorUtil.tertiary03),
                            ),
                            trailing: Text(
                              "${snapshot.data!.data!.kwh == 0.0 ? "--" : snapshot.data!.data!.kwh.toString() + " " + "kWh"}",
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.body1, _colorUtil.tertiary03),
                            )),
                      ),
                      Divider(
                        height: 1,
                        color: _colorUtil.tertiary03,
                      ),
                    ],
                  ),
                )
              : _shimmerLayout();
        });
  }

  _paymentEndCardText(String? paymentInfo) {
    return Visibility(
      visible: partnerName != EV_PARTNERS.EVGO,
      child: Padding(
        padding: EdgeInsets.only(top: 60.h),
        child: Text(
          paymentInfo != null
              ? OneAppString.of().paymentChargedWithoutCardDetails
              : "",
          style: TextStyleExtension().newStyleWithColor(
              _textStyleUtil.caption1, _colorUtil.tertiary05),
        ),
      ),
    );
  }

  _chargedDetils(CdrDetails data) {
    return Column(
      children: [
        ListTile(
            leading: Text(
              "Duration",
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body1, _colorUtil.tertiary03),
            ),
            trailing: Text(
              "${data.data!.duration!.toLowerCase()}",
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body1, _colorUtil.tertiary03),
            )),
        Divider(
          height: 1,
          color: _colorUtil.tertiary03,
        ),
        Visibility(
          visible: !(partnerName == EV_PARTNERS.EVGO &&
              data.data!.totalEnergy == 0.0),
          child: ListTile(
              leading: Text(
                OneAppString.of().energy,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body1, _colorUtil.tertiary03),
              ),
              trailing: Text(
                "${data.data!.totalEnergy == 0.0 ? "--" : data.data!.totalEnergy.toString() + " " + "kWh"}",
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body1, _colorUtil.tertiary03),
              )),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 10),
          child: Container(
            color: _colorUtil.tile05,
            child: ListTile(
                leading: Text(
                  "Total",
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline1, _colorUtil.tertiary03),
                ),
                trailing: Text(
                  "${data.data!.formattedTotalCost}",
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline1, _colorUtil.tertiary03),
                )),
          ),
        ),
      ],
    );
  }

  // Charge stats - Charging - Plugged
  Widget _chargeStatusPlugInLayout() {
    return StreamBuilder<bool>(
        stream: _bloc.isBatteryLow,
        builder: (context, snapshot) {
          final isBatteryLow = snapshot.data ?? false;
          return Container(
              height: 34.h,
              alignment: Alignment.center,
              child: Chip(
                backgroundColor: isBatteryLow
                    ? _colorUtil.primary02
                    : _colorUtil.secondary02,
                label: StreamBuilder<String>(
                    stream: _bloc.chargeStatusBadgeText,
                    builder: (context, snapshot) {
                      return Text(
                        snapshot.data ?? "",
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1,
                            isBatteryLow
                                ? _colorUtil.primary01
                                : _colorUtil.button03b),
                      );
                    }),
              ));
        });
  }

  // Distance to empty - Only for Unplugged
  Widget _distanceToEmptyLayout() {
    return Container(
      height: 76.h,
      width: 311.w,
      child: Center(
        child: StreamBuilder<String>(
            stream: _bloc.distanceWithoutAC,
            builder: (context, snapshot) {
              return StreamBuilder<String?>(
                  stream: _bloc.distanceUnit,
                  builder: (context, distanceSnapShot) {
                    return RichText(
                        text: TextSpan(children: <TextSpan>[
                      TextSpan(
                          text: snapshot.hasData ? snapshot.data : "--",
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.title3, _colorUtil.tertiary00)),
                      TextSpan(
                          text: distanceSnapShot.hasData
                              ? distanceSnapShot.data
                              : snapshot.hasData
                                  ? "mi"
                                  : "",
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.title2, _colorUtil.tertiary00)),
                    ]));
                  });
            }),
      ),
    );
  }

//For Loading Design
  Widget _ChargedShimmerLayout() {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 10.h),
      child: _unplugMessage(),
    );
  }

  Widget _unplugMessage() {
    return Container(
      padding: EdgeInsets.only(top: 100.h, bottom: 10.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Align(
            alignment: Alignment.center,
            child: Center(
              //// second msg  after checking plug ststus
              // Vehicle is not unplugged, please try again.
              child: Text(
                //unplug error message
                OneAppString.of().unplugErrorMessage,
                textAlign: TextAlign.center,
                style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body3,
                  _colorUtil.button02a,
                ),
              ),
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          StreamBuilder<bool>(
              stream: _bloc.isCdrDataNull,
              builder: (context, snapshot) {
                return (snapshot.data != null && snapshot.data!)
                    ? Align(
                        alignment: Alignment.center,
                        child: Center(
                          //// second msg  after checking plug ststus
                          // Vehicle is not unplugged, please try again.
                          child: Text(
                            OneAppString.of().vehicleNotUnpluggedMessage,
                            textAlign: TextAlign.center,
                            style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.body3,
                              _colorUtil.button02a,
                            ),
                          ),
                        ),
                      )
                    : Container();
              }),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: EdgeInsets.only(top: 100.h),
              child: DefaultButtonWithTimer(
                disableOnClick: true,
                showProgressOnDisabled: true,
                enableButtonAfter: Duration(seconds: 30),
                backgroundColor: _colorUtil.button01b,
                buttonTextColor: _colorUtil.button01a,
                press: () {
                  _evChargeTimer?.invokeCDRCallback();
                },
                text: OneAppString.of().continueText,
              ),
            ),
          )
        ],
      ),
    );
  }

  //This method To change the screen Function
  void _findActions(ScreenChangeAction data,
      {CdrDetails? cdrData, int? cdrFlag}) {
    ///cdr flag used to display the content in the payment Screen
    ///0 -> payment Pending
    ///1 -> Session Stoped
    ///default -> payment successfull
    switch (data) {
      case ScreenChangeAction.INTRUPTED:
        errorMessage();
        _evChargeTimer?.cancel();
        break;
      case ScreenChangeAction.RETRY:
        _evChargeTimer?.cancel();
        handlingErrorMessage();
        break;
      case ScreenChangeAction.START_CDR_TIMER:
        // _evChargeTimer?.activateCDRTimer();
        _evChargeTimer?.cancel(keepEvStatusAlive: true);
        break;
      case ScreenChangeAction.STOP_CDR_TIMER:
        _evChargeTimer?.cancelCDR();
        break;
      case ScreenChangeAction.UNPLUG_VEHICLE:
        _showUnplugVehicle();
        break;
      case ScreenChangeAction.STOP_AT_STATION:
        _showStopAtStation();
        break;
      case ScreenChangeAction.PAYMENT_PENDING:
        _showChargeSessionFinished();
        break;
      case ScreenChangeAction.PAYMENT_INFO:
        Navigator.of(context).pop();
        Navigator.of(context).push(MaterialPageRoute(
            builder: (context) => EvChargeConfirmationPage(
                chargingAmount: cdrData?.data?.formattedTotalCost,
                accountNumber: cdrData?.paymentInfo,
                cdrFlagValue: cdrFlag,
                partnerName: partnerName)));
        break;
      case ScreenChangeAction.UPDATED_ENERGY_MESSAGE:
        _noUpdatedEnergyMessage();
        break;
      default:
        _evChargeTimer?.dispose();
        break;
    }
  }

  void _showUnplugVehicle() {
    dialog.updateContent(
      headingText: OneAppString.of().unplug,
      subHeadingText: OneAppString.of().unplugErrorMessage,
      cancelText: OneAppString.of().commonDone,
      showSpinner: true,
    );
    dialog.showDialog(context, () {
      goBackToNative();
    }, () {
      _showChargeSessionFinished();
    });
  }

  void _showStopAtStation() {
    dialog.updateContent(
      headingText: OneAppString.of().waitDontGoYet,
      subHeadingText: OneAppString.of().useStationToStop,
      cancelText: OneAppString.of().commonContinue,
      showSpinner: true,
    );
    dialog.showDialog(context, () {
      // ignore
    }, () {
      stopCharging();
    });
  }

  void _showChargeSessionFinished() {
    dialog.updateContent(
      headingText: OneAppString.of().paymentPendingTitle,
      subHeadingText: OneAppString.of().paymentPendingMessage,
      confirmationText: OneAppString.of().goToDashboard,
    );
  }

  /// charging interpted error popup
  void errorMessage() {
    BottomErrorConfirmationDialog().showBottomDialog(
        context,
        chargeAlertIcon,
        OneAppString.of().chargingInterrupted,
        OneAppString.of().chargingErrorConfirmationText,
        OneAppString.of().contactSupportText,
        OneAppString.of().reportStationText,
        _contactSupportClickListener,
        cancelCallback: _bloc.callCustomerSupport);
  }

  /// charging interpted error popup
  void handlingErrorMessage() {
    BottomErrorConfirmationDialog().showBottomDialog(
        context,
        chargeAlertIcon,
        OneAppString.of().chargingInterrupted,
        OneAppString.of().chargingErrorConfirmationText,
        "Retry",
        "",
        retryCallBack);
  }

  // redirect to contact support
  void _contactSupportClickListener() {
    _evChargeTimer?.cancel();
  }

  //It Will Refresh When UI Need
  void refreshUI() {
    _bloc.noUpdatedEnergyMessage();
    Future.delayed(Duration(milliseconds: 400), () {
      _evChargeTimer?.invokeCallbacks();
    });
  }

  _noUpdatedEnergyMessage() {
    dialog.updateContent(
        iconPath: chargeAlertIcon,
        headingText: OneAppString.of().energyUpdateErrorTitle,
        subHeadingText: OneAppString.of().energyUpdateError,
        confirmationText: OneAppString.of().commonSmallOk);
    dialog.showDialog(context, () {}, () {});
  }

  void triggerChargeDescTooltip() {
    Future.delayed(Duration(seconds: 1), () {
      if (chargeDescWidgetContext != null) {
        showChargeDescToolTip(chargeDescWidgetContext);
      }
    });
  }

  void showChargeDescToolTip(BuildContext? widgetContext) {
    if (tooltip != null && tooltip!.isOpen) {
      tooltip!.close();
      return;
    }
    tooltip = SuperTooltip(
      popupDirection: TooltipDirection.down,
      borderRadius: 30,
      hasShadow: false,
      arrowBaseWidth: 40.0,
      arrowLength: 15.0,
      borderWidth: 0,
      containsBackgroundOverlay: true,
      outsideBackgroundColor: Color.fromRGBO(0, 0, 0, 0.5),
      dismissOnTapOutside: true,
      content: Material(
          child: Padding(
        padding: EdgeInsets.only(top: 20.h),
        child: Container(
          height: 190.h,
          width: 343.w,
          child: Column(
            children: [
              SvgPicture.asset(
                chargeTooltipIcon,
                height: 48.w,
                width: 48.w,
              ),
              Padding(
                padding: EdgeInsets.only(top: 16.h),
                child: Text(
                  OneAppString.of().chargeDescToolTipTitle,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 8.h, left: 20.w, right: 20.w),
                child: Container(
                  // width: 200.w,
                  child: Text(
                    OneAppString.of().chargeDescToolTipSubTitle,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      )),
    );

    tooltip!.show(widgetContext!);
  }

  @override
  bool get wantKeepAlive => true;

  void stopCharging() async {
    _bloc.stopCharging();
  }

  Widget _shimmerLayout() {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 10.h),
      child: Column(
        children: [
          StreamBuilder<Object>(
              stream: _bloc.isUnlockingStation,
              builder: (context, snapshot) {
                if (snapshot.hasData && snapshot.data == true) {
                  return Text(OneAppString.of().unlockStationMessage,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1, _colorUtil.tertiary05));
                } else {
                  return SizedBox.shrink();
                }
              }),
          SizedBox(
            height: 32.h,
          ),
          shimmerRectangle(100.h, double.maxFinite, CARD_RADIUS),
          SizedBox(
            height: 32.h,
          ),
          shimmerRectangle(100.h, double.maxFinite, CARD_RADIUS),
          SizedBox(
            height: 32.h,
          ),
          //   shimmerRectangle(100.h, double.maxFinite, CARD_RADIUS)
        ],
      ),
    );
  }

  void retryCallBack() {
    Navigator.of(context).pop();
  }
}
