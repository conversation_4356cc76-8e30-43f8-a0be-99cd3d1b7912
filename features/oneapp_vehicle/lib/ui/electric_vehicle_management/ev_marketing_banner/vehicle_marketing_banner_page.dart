// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';

class VehicleMarketingBannerPage extends StatefulWidget {
  const VehicleMarketingBannerPage(
      {Key? key, required this.isWalletEnable, required this.onRegister})
      : super(key: key);

  final bool isWalletEnable;
  final Function onRegister;

  @override
  State<VehicleMarketingBannerPage> createState() =>
      _VehicleMarketingBannerPageState();
}

TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

class _VehicleMarketingBannerPageState
    extends State<VehicleMarketingBannerPage> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(children: [
        Padding(
          padding: EdgeInsets.only(top: 20.h),
          child: Text(
            OneAppString.of().findStation,
            style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
          ),
        ),
        Column(
          children: [
            Container(
              height: 246.h,
              margin: EdgeInsets.all(16.h),
              decoration: BoxDecoration(
                  color: _colorUtil.button05b,
                  borderRadius: BorderRadius.circular(8.r),
                  image: DecorationImage(
                      image: ExactAssetImage(evMarketingBannercar),
                      fit: BoxFit.cover)),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                        top: 24.h, left: 16.w, right: 16.w, bottom: 8.h),
                    child: Text(
                      OneAppString.of().evmcNoScheduleTitleText,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout2, Colors.white),
                    ),
                  ),
                  Text(
                    OneAppString.of().evmcChargeYourVehicleText,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.subHeadline3, Colors.white),
                    textAlign: TextAlign.center,
                  ),
                  Row()
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
              child: CustomDefaultButton(
                text: OneAppString.of().loginRegister,
                textStyle: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.buttonLink1, _colorUtil.tertiary15),
                backgroundColor: _colorUtil.tertiary00,
                buttonTextColor: _colorUtil.tertiary15,
                press: () {
                  Navigator.pop(context);
                  widget.onRegister();
                },
              ),
            )
          ],
        ),
      ]),
    );
  }
}
