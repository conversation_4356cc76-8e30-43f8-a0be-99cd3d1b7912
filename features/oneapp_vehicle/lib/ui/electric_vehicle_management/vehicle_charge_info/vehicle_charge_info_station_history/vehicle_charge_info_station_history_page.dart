// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/src/intl/date_format.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/custom_page_route.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';

// Project imports:
import '../custom_calender_view.dart';
import 'vehicle_charge_info_station_history_bloc.dart';
import 'vehicle_charge_station_invoices_screen.dart';

import 'package:oneapp_network_implementation/ev/entity/ev_list_of_cdr_session.dart'
    as listOfCdr;

class VehicleChargeInfoStationHistoryPage extends StatefulWidget {
  VehicleChargeInfoStationHistoryPage({Key? key}) : super(key: key);

  @override
  _VehicleChargeInfoStationHistoryPageState createState() =>
      _VehicleChargeInfoStationHistoryPageState();
}

class _VehicleChargeInfoStationHistoryPageState
    extends State<VehicleChargeInfoStationHistoryPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final VehicleChargeInfoStationHistoryBloc _historyBloc =
      VehicleChargeInfoStationHistoryBloc();
  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    _historyBloc.init();
    super.initState();
    _scrollController.addListener(() {
      double maxScroll = _scrollController.position.maxScrollExtent;
      double currentScroll = _scrollController.position.pixels;
      double delta = 0; // or something else..
      if (maxScroll - currentScroll == delta) {
        _historyBloc.loadNewCDRList();
      }
    });
  }

  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          color: _colorUtil.tile01,
          padding: EdgeInsets.only(bottom: 6.h),
          child: Column(
            children: [
              bottomSheetCustomAppBar(OneAppString.of().history,
                  onBackPressed: () => Navigator.of(context).pop(true),
                  elevation: 0),
            ],
          ),
        ),
        Expanded(child: _stationHistoryLayout()),
      ],
    );
  }

  Widget _stationHistoryLayout() {
    return StreamBuilder<List<listOfCdr.ListResponse>?>(
        stream: _historyBloc.listOfCdr,
        builder: (context, snapshot) {
          return snapshot.hasData &&
                  snapshot.data != null &&
                  snapshot.data!.isNotEmpty
              ? Scrollbar(
                  controller: _scrollController,
                  child: Container(
                    color: _colorUtil.tile01,
                    child: ListView.builder(
                      controller: _scrollController,
                      itemBuilder: (context, index) {
                        if (index == snapshot.data!.length) {
                          return StreamBuilder(
                            stream: _historyBloc.isListCdrLoading,
                            builder: (context, snapshot) => Visibility(
                              visible:
                                  snapshot.hasData && snapshot.data == true,
                              child: Container(
                                  height: 20.h,
                                  child: CupertinoActivityIndicator()),
                            ),
                          );
                        }
                        return _historylistview(snapshot.data![index]);
                      },
                      itemCount: snapshot.data!.length + 1,
                      shrinkWrap: true,
                    ),
                  ),
                )
              : (snapshot.data != null && snapshot.data!.isEmpty)
                  ? Center(child: Text(OneAppString.of().noCdrHistoryMessage))
                  : _shimmerLayout();
        });
  }

  Padding _shimmerLayout() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h),
      child: Column(
        children: [
          shimmerRectangle(80.h, double.maxFinite, CARD_RADIUS_SMALL),
          Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: shimmerRectangle(80.h, double.maxFinite, CARD_RADIUS_SMALL),
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: shimmerRectangle(80.h, double.maxFinite, CARD_RADIUS_SMALL),
          ),
        ],
      ),
    );
  }

  Widget _historylistview(listOfCdr.ListResponse data) {
    String month =
        DateFormat("MMM").format(data.data!.startDateTime!.toLocal());
    String date = DateFormat("d").format(data.data!.startDateTime!.toLocal());
    return Padding(
      padding: EdgeInsets.only(top: 3.h, left: 16.h, right: 16.h),
      child: Card(
        color: _colorUtil.tertiary15,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15.0),
        ),
        child: Container(
          child: ListTile(
            enabled: true,
            tileColor: _colorUtil.tertiary15,
            onTap: () => {_showVehicleInvoicePage(data)},
            leading: CustomCalenderView(monthName: month, date: date),
            title: Text(
              data.data!.location!.name ?? "",
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body4, _colorUtil.tertiary00),
            ),
            subtitle: RichText(
              text: TextSpan(
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1, _colorUtil.tertiary00),
                  children: [
                    TextSpan(
                      text: _historyBloc.partnerNameValues
                              .map[data.partnerName?.toLowerCase()] ??
                          _historyBloc.partnerNameValues.map[data
                              .data?.location?.locationOperator?.name
                              ?.toLowerCase()] ??
                          "",
                    ),
                    TextSpan(
                        text: ' • ',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(text: data.formattedTotalCost),
                    if (data.isPending)
                      TextSpan(
                          text: ' • ',
                          style: TextStyle(
                              color: _colorUtil.tertiary05,
                              fontWeight: FontWeight.bold)),
                    if (data.isPending)
                      TextSpan(
                          text: OneAppString.of().pending,
                          style: TextStyle(color: _colorUtil.tertiary05)),
                  ]),
            ),
          ),
        ),
        elevation: 0.5,
        shadowColor: _colorUtil.tertiary00,
      ),
    );
  }

  _showVehicleInvoicePage<bool>(data) {
    Navigator.of(context).push(CustomPageRoute(
        page: VehicleStartChargingInvoicesScreen(cdrdetails: data)));
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
  }
}
