// Package imports:

// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_list_of_cdr_session.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';

class VehicleChargeInfoStationHistoryBloc extends BlocBase {
  Stream<List<ListResponse>?> get listOfCdr => _listOfCdr.stream;
  final _listOfCdr = BehaviorSubject<List<ListResponse>?>();
  final _isListCdrLoading = BehaviorSubject<bool>();

  Stream<bool> get isListCdrLoading => _isListCdrLoading.stream;

  int totalNumberOfCrdList = 0;
  List<ListResponse> cdrList = [];
  bool isApiLoading = true;
  final partnerNameValues =
      EnumValues({"chargepoint": "ChargePoint", "evgo": "EVgo"});

  void init() {
    fetchListOfCdrDetails();
  }

  Future<void> fetchListOfCdrDetails({int limit = 10, int offset = 0}) async {
    final evApi = APIClientConfig.evApiClient;
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();
    final commonResponse = await evApi.fetchEvListOfCdrDetails(
        limit: limit, offset: offset, make: vehicleInfo!.make);
    final payload = commonResponse.response?.payload;
    final listCdr = payload?.listCdr;
    final List<ListResponse> listResponse = listCdr?.listResponse ?? [];

    if (offset == 0) {
      cdrList = [];
      _listOfCdr.sink.add(cdrList);
    }
    totalNumberOfCrdList = listCdr?.noOfCdr ?? 0;
    if (listResponse.isNotEmpty) {
      cdrList.addAll(listResponse);
      _listOfCdr.sink.add(cdrList);
    } else {
      isApiLoading = false;
      _listOfCdr.sink.add([]);
    }
  }

  void loadNewCDRList() async {
    if (_listOfCdr.value!.length < totalNumberOfCrdList) {
      _isListCdrLoading.value = true;
      await fetchListOfCdrDetails(limit: 10, offset: cdrList.length);
      _isListCdrLoading.value = false;
    }
  }

  @override
  void dispose() {
    _listOfCdr.close();
    _isListCdrLoading.close();
  }
}
