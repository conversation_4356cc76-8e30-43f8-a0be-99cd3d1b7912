// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/src/intl/date_format.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/widget/common_widget.dart';

// Project imports:
import '../../../../log/vehicle_analytic_event.dart';
import '../../ev_common/ev_enum_const_util.dart';
import 'vehicle_charge_info_station_history_bloc.dart';

import 'package:oneapp_network_implementation/ev/entity/ev_list_of_cdr_session.dart'
    as listCdr;

class VehicleStartChargingInvoicesScreen extends StatefulWidget {
  final listCdr.ListResponse? cdrdetails;

  final double width;
  final double height;

  const VehicleStartChargingInvoicesScreen(
      {Key? key, this.cdrdetails, this.width = 60, this.height = 60})
      : super(key: key);

  @override
  _VehicleStartChargingInvoicesScreen createState() =>
      _VehicleStartChargingInvoicesScreen();
}

class _VehicleStartChargingInvoicesScreen
    extends State<VehicleStartChargingInvoicesScreen> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final VehicleChargeInfoStationHistoryBloc _historyBloc =
      VehicleChargeInfoStationHistoryBloc();

  @override
  void initState() {
    _historyBloc.init();
    super.initState();
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_EV_PUB_INVOICES_PAGE);
  }

  Widget build(BuildContext context) {
    return Material(
        child: Navigator(
            onGenerateRoute: (_) =>
                MaterialPageRoute(builder: (materialContext) {
                  return Builder(builder: (builderContext) {
                    return Container(
                      decoration: _colorUtil.bottomSheetDecorator(
                          backgroundColor: _colorUtil.tertiary15),
                      child: Column(
                        children: [
                          Expanded(
                              child: SingleChildScrollView(
                            child: Column(children: [
                              bottomSheetCustomAppBar(OneAppString.of().invoice,
                                  onBackPressed: () =>
                                      Navigator.of(context).pop(true),
                                  elevation: 0),
                              SizedBox(
                                height: 10.h,
                              ),
                              _calenderview(),
                              _invoiceview(),
                            ]),
                          ))
                        ],
                      ),
                    );
                  });
                })));
  }

  Widget _calenderview() {
    return Column(
      children: [
        Container(
          height: 60.h,
          width: 60.w,
          decoration: BoxDecoration(
            color: _colorUtil.secondary01,
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(color: _colorUtil.secondary01, width: 1.w),
          ),
          child: Column(
            children: [
              Padding(
                  padding: EdgeInsets.all(8.h),
                  child: Text(
                    DateFormat("MMM").format(
                        widget.cdrdetails!.data!.startDateTime!.toLocal()),
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.tabLabel01, _colorUtil.tertiary15),
                  )),
              Expanded(
                  child: Container(
                height: 30.h,
                width: 60.w,
                decoration: BoxDecoration(
                    color: _colorUtil.tertiary15,
                    border: Border.all(color: _colorUtil.secondary01, width: 1),
                    borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(10.r),
                        bottomLeft: Radius.circular(10.r))),
                child: Padding(
                  padding: EdgeInsets.only(top: 5.h),
                  child: Text(
                    DateFormat("d").format(
                        widget.cdrdetails!.data!.startDateTime!.toLocal()),
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary00),
                    textAlign: TextAlign.center,
                  ),
                ),
              ))
            ],
          ),
        )
      ],
    );
  }

  Widget _invoiceview() {
    return Padding(
      padding: EdgeInsets.all(8.r),
      child: Column(
        children: [
          Container(
            child: Padding(
              padding: EdgeInsets.only(top: 15.h, bottom: 20.h),
              child: Text(
                DateFormat("MMM dd, yyyy")
                    .format(widget.cdrdetails!.data!.startDateTime!.toLocal()),
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body4, _colorUtil.tertiary00),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Container(
            child: Padding(
              padding: EdgeInsets.all(8.h),
              child: Card(
                color: _colorUtil.tertiary15,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15.r),
                ),
                child: Container(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _colorUtil.secondary02,
                      radius: 20,
                      child: Icon(
                        Icons.location_on_outlined,
                        color: _colorUtil.secondary01,
                      ),
                    ),
                    title: Text(
                      widget.cdrdetails!.data!.location!.name ?? "",
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body4, _colorUtil.tertiary00),
                    ),
                    subtitle: Text(
                      _historyBloc.partnerNameValues.map[
                              widget.cdrdetails?.partnerName?.toLowerCase()] ??
                          _historyBloc.partnerNameValues.map[widget.cdrdetails
                              ?.data?.location?.locationOperator?.name
                              ?.toLowerCase()] ??
                          "",
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1, _colorUtil.tertiary00),
                    ),
                  ),
                ),
                elevation: 1,
                shadowColor: _colorUtil.tertiary00,
              ),
            ),
          ),
          Container(
            child: Padding(
              padding: EdgeInsets.only(top: 15.h),
              child: Card(
                elevation: 0,
                color: _colorUtil.tertiary15,
                child: ListTile(
                  title: Text(
                    OneAppString.of().durationText,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.body3, _colorUtil.tertiary00),
                  ),
                  trailing: Text(
                    widget.cdrdetails!.data!.duration =
                        calculateTimeDifferenceBetweenWithDuration(
                                widget.cdrdetails!.data!.startDateTime!,
                                widget.cdrdetails!.data!.stopDateTime!)
                            .capitalize(),
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary00),
                  ),
                ),
              ),
            ),
          ),
          Divider(
            color: _colorUtil.tertiary10,
            height: 10.h,
            thickness: 1.w,
            indent: 10.w,
            endIndent: 10.w,
          ),
          Container(
            child: Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: Card(
                elevation: 0,
                color: _colorUtil.tertiary15,
                child: Visibility(
                  visible: !((widget.cdrdetails?.data?.location
                                  ?.locationOperator?.name ==
                              EV_PARTNERS.EVGO ||
                          widget.cdrdetails?.partnerName == EV_PARTNERS.EVGO) &&
                      widget.cdrdetails!.data!.totalEnergy == 0.0),
                  child: ListTile(
                    title: Text(
                      OneAppString.of().energy,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body3, _colorUtil.tertiary00),
                    ),
                    trailing: Text(
                      widget.cdrdetails!.data!.totalEnergy == 0.0
                          ? "--"
                          : widget.cdrdetails!.data!.totalEnergy!
                                  .toStringAsFixed(2) +
                              " " +
                              "kWh",
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1, _colorUtil.tertiary00),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Container(
            child: Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: Card(
                color: _colorUtil.tertiary12,
                elevation: 0,
                child: ListTile(
                  title: Text(
                    "Total",
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.subHeadline1, _colorUtil.tertiary00),
                  ),
                  trailing: Text(
                    "${widget.cdrdetails!.formattedTotalCost}",
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.subHeadline1, _colorUtil.tertiary00),
                  ),
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
                padding: EdgeInsets.only(top: 60.h, right: 10.w, left: 10.w),
                child: Visibility(
                    visible: _cardDetailsIsVisble(),
                    child: Text(
                      widget.cdrdetails!.isPending
                          ? OneAppString.of().paymentChargedWithoutCardDetails
                          : OneAppString.of().paymentChargedWithCardDetails(
                              widget.cdrdetails!.paymentInfo!),
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.caption2, _colorUtil.tertiary00),
                    ))),
          )
        ],
      ),
    );
  }

  bool _cardDetailsIsVisble() {
    if (widget.cdrdetails?.partnerName?.toLowerCase() ==
        EV_PARTNERS.CHARGE_POINT) {
      return widget.cdrdetails?.formattedTotalCost.toLowerCase() == "free"
          ? false
          : true;
    } else if (widget.cdrdetails?.partnerName?.toLowerCase() ==
        EV_PARTNERS.EVGO) {
      return false;
    }
    return true;
  }
}
