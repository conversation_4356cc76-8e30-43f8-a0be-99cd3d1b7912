// Flutter imports:
import 'package:flutter/painting.dart';

// Package imports:
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';

enum EVStationsButtonType {
  walletPresence,
  setupWallet,
  register,
  view,
}

extension EVStationsButtonTypeDetails on EVStationsButtonType? {
  String getCardText(String? cardNumber) {
    return "••••$cardNumber";
  }

  String get buttonText {
    switch (this) {
      case EVStationsButtonType.setupWallet:
        return OneAppString.of().setup;
      case EVStationsButtonType.register:
        return OneAppString.of().loginRegister;
      case EVStationsButtonType.view:
        return OneAppString.of().view;
      default:
        return "";
    }
  }

  Color backgroundColor(ColorUtil colorUtil) {
    switch (this) {
      case EVStationsButtonType.walletPresence:
        return colorUtil.success02;
      case EVStationsButtonType.setupWallet:
        return colorUtil.button05b;
      case EVStationsButtonType.register:
        return colorUtil.button05b;
      case EVStationsButtonType.view:
        return colorUtil.success02;
      default:
        return colorUtil.button05b;
    }
  }

  Color iconColor(ColorUtil colorUtil) {
    switch (this) {
      case EVStationsButtonType.walletPresence:
        return colorUtil.secondary01;
      case EVStationsButtonType.setupWallet:
        return colorUtil.button02a;
      case EVStationsButtonType.register:
        return colorUtil.button02a;
      case EVStationsButtonType.view:
        return colorUtil.secondary01;
      default:
        return colorUtil.secondary01;
    }
  }

  bool get navToregister {
    switch (this) {
      case EVStationsButtonType.register:
        return true;
      default:
        return false;
    }
  }

  bool get navToInvoice {
    switch (this) {
      case EVStationsButtonType.view:
        return true;
      default:
        return false;
    }
  }

  bool get navToWalletScreen {
    switch (this) {
      case EVStationsButtonType.walletPresence:
        return true;
      case EVStationsButtonType.setupWallet:
        return true;
      default:
        return false;
    }
  }
}
