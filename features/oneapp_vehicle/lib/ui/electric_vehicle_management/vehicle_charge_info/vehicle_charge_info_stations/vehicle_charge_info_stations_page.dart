// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/custom_page_route.dart';
import 'package:oneapp_common/widget/dialog/bottom_error_conformation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:oneapp_network_implementation/oneapp/entity/wallet/card_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/wallet/payment_method_entity.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';
import '../../../../log/vehicle_analytic_event.dart';
import '../../../vehicle_account/vehicle_account_page.dart';
import '../../../vehicle_finance/wallet/add_wallet/add_wallet_page.dart';
import '../../../vehicle_finance/wallet/wallet_home/wallet_home_page.dart';
import '../../ev_common/ev_enum_const_util.dart';
import '../../vehicle_search_charge_station_location/LegalTermsAndCondition/ev_Legal_Terms_and_Condition_page.dart';
import '../../vehicle_search_charge_station_location/ev_driver_account_enrollment/ev_driver_account_enrollment_page.dart';
import '../vehicle_charge_info_station_history/vehicle_charge_info_station_history_page.dart';
import 'ev_stations_button_type.dart';
import 'vehicle_charge_info_stations_bloc.dart';
import 'vehicle_charge_info_stations_partners_list_card.dart';

class VehicleChargeInfoStationsPage extends StatefulWidget {
  const VehicleChargeInfoStationsPage({
    Key? key,
    required this.isWalletFeatureEnabled,
  }) : super(key: key);
  final bool? isWalletFeatureEnabled;

  @override
  _VehicleChargeInfoStationsPageState createState() =>
      _VehicleChargeInfoStationsPageState();
}

class _VehicleChargeInfoStationsPageState
    extends State<VehicleChargeInfoStationsPage> {
  final _bloc = VehicleChargeInfoStationsBloc();

  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  void initState() {
    _bloc.init(widget.isWalletFeatureEnabled);
    _bloc.listOfPartners.listen((event) {});
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
          child: Column(
        children: [_walletMethods(), _partnerMethods(), _findStationButton()],
      )),
    );
  }

  Widget stationsWidget(bool isWallet, [PaymentMethod? data]) {
    return Column(
      children: [
        isWallet
            ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Card(
                  color: _colorUtil.tertiary15,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: _colorUtil.tile01,
                        boxShadow: [
                          BoxShadow(
                              color: Color.fromRGBO(0, 0, 0, 0.07),
                              blurRadius: 15.w,
                              spreadRadius: 0.0,
                              offset: Offset(0.0, 5.w))
                        ]),
                    height: 72,
                    alignment: Alignment.center,
                    child: ListTile(
                      onTap: () => _showSetUpWalletScreen(isWallet),
                      enabled: true,
                      tileColor: _colorUtil.tertiary15,
                      title: Row(
                        children: [
                          SvgPicture.asset(
                            walletIcon,
                            height: 24.w,
                            width: 24.w,
                            colorFilter: ColorFilter.mode(
                              _colorUtil.tertiary03,
                              BlendMode.srcIn,
                            ),
                            allowDrawingOutsideViewBox: true,
                          ),
                          SizedBox(width: 16.w),
                          Flexible(
                            child: Text(
                              OneAppString.of().wallet,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.subHeadline1,
                                  _colorUtil.tertiary03),
                            ),
                          ),
                        ],
                      ),
                      trailing: SizedBox(
                          height: 34.0,
                          width: 84.0,
                          child: evStationsButton(
                              buttonType: data?.card?.last4 != null
                                  ? EVStationsButtonType.walletPresence
                                  : EVStationsButtonType.setupWallet,
                              card: data?.card,
                              isWallet: isWallet)),
                    ),
                  ),
                  elevation: 0.5,
                  shadowColor: _colorUtil.tertiary00,
                ),
              )
            : Padding(
                padding: EdgeInsets.only(top: 8.h, left: 16.w, right: 16.w),
                child:
                    shimmerRectangle(72.h, double.maxFinite, CARD_RADIUS_SMALL),
              ),
        SizedBox(
          height: 10.h,
        ),
      ],
    );
  }

  _chargeHistorySheet() {
    Navigator.of(context)
        .push(
          CustomPageRoute(
            page: Container(
              color: _colorUtil.tile01,
              child: VehicleChargeInfoStationHistoryPage(),
            ),
          ),
        )
        .then(
          (_) => _bloc.initializePartners(),
        );
  }

  Widget evStationsButton(
      {EVStationsButtonType? buttonType, WalletCard? card, bool? isWallet}) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      onTap: () => {
        if (buttonType.navToWalletScreen)
          {_showSetUpWalletScreen(isWallet!)}
        else if (buttonType.navToInvoice)
          {_chargeHistorySheet()}
      },
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
            border: Border.all(
              color: buttonType.backgroundColor(_colorUtil),
            ),
            color: buttonType.backgroundColor(_colorUtil),
            borderRadius: BorderRadius.all(Radius.circular(20.r))),
        child: Center(
          child: Text(
            card?.last4 != null
                ? buttonType.getCardText(card!.last4)
                : buttonType.buttonText,
            style: TextStyleExtension().newStyleWithColor(
              _textStyleUtil.callout1,
              buttonType.iconColor(_colorUtil),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _walletMethods() {
    return StreamBuilder<bool>(
        stream: _bloc.walletPresence,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data == true) {
            return StreamBuilder<PaymentMethod>(
                stream: _bloc.walletDetail,
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    if (snapshot.data!.paymentMethodDefault == true) {
                      return stationsWidget(true, snapshot.data);
                    } else {
                      return stationsWidget(true);
                    }
                  } else {
                    return stationsWidget(false);
                  }
                });
          } else {
            return SizedBox.shrink();
          }
        });
  }

  Widget partnersWidget(List? listOfPartners) {
    bool isInvoiceVisible =
        listOfPartners?.any((element) => element?.isEnrolled == true) ?? false;
    List enrolledPartners = listOfPartners
            ?.where((element) => element?.isEnrolled == true)
            .toList() ??
        [];
    List nonEnrolledPartners = listOfPartners
            ?.where((element) => element?.isEnrolled != true)
            .toList() ??
        [];
    return Column(
      children: [
        isInvoiceVisible
            ? InkWell(
                onTap: () => _chargeHistorySheet(),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.0),
                      color: _colorUtil.tile02,
                    ),
                    child: Column(
                      children: [
                        _invoiceTile(isInvoiceVisible),
                        SizedBox(height: 4.h),
                        _partnersListTile(enrolledPartners)
                      ],
                    ),
                  ),
                ),
              )
            : _invoiceTile(isInvoiceVisible),
        nonEnrolledPartners.isNotEmpty
            ? _partnersListTile(nonEnrolledPartners)
            : Container()
      ],
    );
  }

  Widget _partnersListTile(List listOfPartners) {
    return Container(
        child: Column(
      children: [
        ListView.builder(
          physics: ScrollPhysics(parent: NeverScrollableScrollPhysics()),
          shrinkWrap: true,
          itemCount: listOfPartners.length,
          itemBuilder: (BuildContext context, int index) =>
              StationPartnersListCard(
            partnerEnrolledData: listOfPartners[index],
            onRegisterCallBackFunction: _showAcceptTermsAndConditionScreen,
            bloc: _bloc,
          ),
        ),
      ],
    ));
  }

  Widget _invoiceTile(bool isInvoiceVisible) {
    return Visibility(
      visible: isInvoiceVisible,
      child: Padding(
        padding: EdgeInsets.only(left: 4.w),
        child: Container(
          height: 80.h,
          alignment: Alignment.center,
          child: ListTile(
            onTap: () => _chargeHistorySheet(),
            enabled: true,
            tileColor: _colorUtil.tile02,
            title: Row(
              children: [
                SvgPicture.asset(
                  bookIcon,
                  height: 24.h,
                  width: 24.w,
                  colorFilter: ColorFilter.mode(
                    _colorUtil.tertiary03,
                    BlendMode.srcIn,
                  ),
                  allowDrawingOutsideViewBox: true,
                ),
                SizedBox(width: 16),
                Flexible(
                  child: Text(
                    OneAppString.of().invoices,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.subHeadline1, _colorUtil.tertiary03),
                  ),
                ),
              ],
            ),
            trailing: SizedBox(
                height: 34.h,
                width: 84.w,
                child: evStationsButton(buttonType: EVStationsButtonType.view)),
          ),
        ),
      ),
    );
  }

  Widget _shimmerWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 8.h),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: shimmerRectangle(72.h, double.maxFinite, CARD_RADIUS_SMALL),
          ),
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: shimmerRectangle(72.h, double.maxFinite, CARD_RADIUS_SMALL),
          ),
        ],
      ),
    );
  }

  Widget _partnerMethods() {
    return StreamBuilder(
        stream: _bloc.listOfPartners,
        builder: (ctx, AsyncSnapshot snapshot) {
          if (snapshot.hasData) {
            return partnersWidget(snapshot.data);
          } else {
            return _shimmerWidget();
          }
        });
  }

  _showSetUpWalletScreen(bool isPresent) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(
                child: isPresent
                    ? WalletHomePage(isFromChargeInfo: true)
                    : AddWallet()),
          ],
        ),
      ),
    ).then((_) => _bloc.initializeWallet());
  }

  _showAcceptTermsAndConditionScreen<bool>(String partnerName) {
    Navigator.of(context)
        .push(CustomPageRoute(
            page: Container(
          color: _colorUtil.tile01,
          child: EvNewTermsAndConditionPage(
            acceptTermsCallBackFunction: acceptTermsCallBackFunction,
            partnerName: partnerName,
          ),
        )))
        .then((_) => _bloc.initializePartners());
  }

  Future onRegisterFunction(String? partnerName) {
    return showModalBottomSheet(
      context: context,
      isDismissible: true,
      barrierColor: Colors.transparent,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
          child: Container(
        height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_60,
        child: OneAppScaffold(
          resizeToAvoidBottomInset: true,
          body: Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: EVDriverAccountEnrollmentPage(
              continueCallback: continueCallBack,
              partnerName: partnerName,
              screenAction: screenActionCallBack,
            ),
          ),
        ),
      )),
    );
  }

  void acceptTermsCallBackFunction({String? partnerName}) {
    Future.delayed(Duration(milliseconds: 100), () {
      onRegisterFunction(partnerName).then((_) => _bloc.initializePartners());
    });
  }

  void continueCallBack() {
    VehicleRepo().fetchIsEvPublicChargingEnabled().then(
          (isPublicChargingEnabled) => NavigateService.pushNamedRoute(
            RoutePath.VEHICLE_SEARCH_CHARGE_STATION_LOCATION,
            arguments: isPublicChargingEnabled,
          ).then(
            (_) => _bloc.initializePartners(),
          ),
        );
  }

  void screenActionCallBack(EV_SCREENS val, {String? errorMessage}) {
    if (val == EV_SCREENS.UPDATE_PROFILE) {
      BottomErrorConfirmationDialog().showBottomDialog(
          context,
          chargeAlertIcon,
          OneAppString.of().updateProfileText,
          OneAppString.of().updateProfileErrorMessage(errorMessage!),
          OneAppString.of().updateProfileButtonText,
          OneAppString.of().cancel,
          _updateProfileClickListener);
    }
  }

  // redirect to contact support
  void _updateProfileClickListener() {
    _showVehicleAccountPage();
  }

  _showVehicleAccountPage<bool>() {
    return showMaterialModalBottomSheet(
      expand: true,
      context: context,
      isDismissible: true,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(child: VehicleAccountPage()),
          ],
        ),
      ),
    );
  }

  Widget _findStationButton() {
    return Container(
      padding: EdgeInsets.only(top: 10.h),
      child: Center(
        child: CustomDefaultButton(
          backgroundColor: _colorUtil.primaryButton02,
          buttonTextColor: _colorUtil.primaryButton01,
          text: OneAppString.of().findStations,
          press: () {
            FireBaseAnalyticsLogger.logScreenVisit(
                VehicleAnalyticsEvent.VEHICLE_EV_PUB_FIND_FROM_CHARGEINFO_LINK);
            VehicleRepo()
                .fetchIsEvPublicChargingEnabled()
                .then((isPublicChargingEnabled) {
              if (isPublicChargingEnabled) {
                NavigateService.pushNamedRoute(
                  RoutePath.VEHICLE_SEARCH_CHARGE_STATION_LOCATION,
                  arguments: {
                    "publicCharging": isPublicChargingEnabled,
                    "isFromNative": false
                  },
                );
              } else {
                NavigateService.pushNamedRoute(
                    RoutePath.VEHICLE_SEARCH_CHARGE_STATION_LOCATION,
                    arguments: {"isFromNative": false});
              }
            });
          },
          borderColor: _colorUtil.primaryButton02,
          horizontalPadding: 22.w,
          verticalPadding: 1.h,
        ),
      ),
    );
  }
}
