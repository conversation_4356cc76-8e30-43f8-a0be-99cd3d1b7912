// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_wallet_and_enrollment_validation.dart';

// Project imports:
import '../../../../log/vehicle_analytic_event.dart';
import '../../../vehicle_finance/vehicle_finance_page.dart';
import '../../ev_common/ev_enum_const_util.dart';
import 'ev_stations_button_type.dart';
import 'vehicle_charge_info_stations_bloc.dart';

class StationPartnersListCard extends StatelessWidget {
  StationPartnersListCard(
      {this.partnerEnrolledData,
      Key? key,
      this.onRegisterCallBackFunction,
      this.bloc})
      : super(key: key);
  final Function? onRegisterCallBackFunction;
  final PartnerStatus? partnerEnrolledData;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final VehicleChargeInfoStationsBloc? bloc;

  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;

  _showSetUpWalletScreen<bool>(context) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(
                child: VehicleFinancePage(
              fromChargeStation: true,
              showPassDueNotification: (showNotification) {},
            )),
          ],
        ),
      ),
    ).then((_) => bloc!.initializePartners());
  }

  String getEVgoFreeDisplayDate() {
    final DateFormat formatter = DateFormat('MMM dd, yyyy');
    if (Global.getInstance().evgoFreeExpireDate != null) {
      final freeChargeExpireDate =
          DateTime.parse(Global.getInstance().evgoFreeExpireDate!).toLocal();

      final todayDate =
          DateFormat('yyyy-MM-dd').parse(DateTime.now().toLocal().toString());

      if (todayDate.isBefore(freeChargeExpireDate) ||
          todayDate.isAtSameMomentAs(freeChargeExpireDate)) {
        final freeChargeExpireDisplayDate =
            formatter.format(freeChargeExpireDate).toString();
        return OneAppString.of()
            .evgoFreeWithExpireDate(freeChargeExpireDisplayDate);
      } else if (todayDate.isAfter(freeChargeExpireDate)) {
        return "";
      }
    }
    return OneAppString.of().evgoFreeOneYear;
  }

  @override
  Widget build(BuildContext buildContext) {
    return partnerEnrolledData?.isEnrolled == true
        ? Container(
            height: 72.0,
            alignment: Alignment.center,
            child: Column(
              children: [partnerTile(buildContext)],
            ),
          )
        : Container(
            child: Padding(
              padding: EdgeInsets.only(top: 3.h, left: 12.h, right: 12.h),
              child: Card(
                elevation: 0.0,
                shadowColor: Colors.transparent,
                color: _colorUtil.tile02,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Container(
                    height: 72.h,
                    alignment: Alignment.center,
                    child: partnerTile(buildContext)),
              ),
            ),
          );
  }

  Widget partnerTile(BuildContext buildContext) {
    bool isPartnerEnrolled = partnerEnrolledData?.isEnrolled ?? false;
    return AbsorbPointer(
      absorbing: isPartnerEnrolled,
      child: ListTile(
        onTap: () {
          if (partnerEnrolledData?.status == OneAppString.of().setupWallet) {
            _showSetUpWalletScreen(buildContext);
          } else {
            onRegisterCallBackFunction!(partnerEnrolledData?.partnerName);
          }
        },
        title: Row(
          children: [
            isPartnerEnrolled
                ? CircleAvatar(
                    radius: 15.r,
                    backgroundColor: _colorUtil.tile01,
                    child: SvgPicture.asset(
                      enrolledStationIcon,
                      colorFilter: ColorFilter.mode(
                        _colorUtil.button03b,
                        BlendMode.srcIn,
                      ),
                      width: 15.w,
                      height: 15.h,
                    ),
                  )
                : SvgPicture.asset(
                    chargeStationIcon,
                    height: 24.w,
                    width: 24.w,
                    colorFilter: ColorFilter.mode(
                      _colorUtil.tertiary03,
                      BlendMode.srcIn,
                    ),
                    allowDrawingOutsideViewBox: true,
                  ),
            SizedBox(width: isPartnerEnrolled ? 8.w : 16.w),
            Text(partnerEnrolledData?.partnerName ?? "",
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body4, _colorUtil.tertiary00)),
            SizedBox(width: 8.w),
            Flexible(
              child: Text(
                partnerEnrolledData?.partnerName.toLowerCase() ==
                            EV_PARTNERS.EVGO &&
                        isPartnerEnrolled
                    ? getEVgoFreeDisplayDate()
                    : partnerEnrolledData?.partnerName.toLowerCase() ==
                            EV_PARTNERS.EVGO
                        ? OneAppString.of().evgoFreeOneYear
                        : "",
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.caption1, _colorUtil.tertiary05),
                maxLines: 2,
              ),
            )
          ],
        ),
        trailing: !isPartnerEnrolled
            ? AbsorbPointer(
                absorbing: isPartnerEnrolled,
                child: GestureDetector(
                  onTap: () {
                    if (partnerEnrolledData?.status ==
                        OneAppString.of().setupWallet) {
                      FireBaseAnalyticsLogger.logMarketingGroupEvent(
                        VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                        childEventName:
                            VehicleAnalyticsEvent.VEHICLE_EV_WALLET_SET_UP,
                      );

                      _showSetUpWalletScreen(buildContext);
                    } else {
                      onRegisterCallBackFunction!(
                          partnerEnrolledData?.partnerName);
                    }
                  },
                  child: SizedBox(
                    height: 34.h,
                    width: 84.w,
                    child: Container(
                      padding: EdgeInsets.all(4.w),
                      decoration: BoxDecoration(
                        color: _colorUtil.button05b,
                        borderRadius: BorderRadius.circular(20.w),
                      ),
                      child: Row(
                        children: [
                          Padding(padding: EdgeInsets.only(left: 10.w)),
                          Text(
                            partnerEnrolledData!.status,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.callout1,
                                EVStationsButtonType.register
                                    .iconColor(_colorUtil)),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            : null,
      ),
    );
  }
}
