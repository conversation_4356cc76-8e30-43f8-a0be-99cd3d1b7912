//Package imports:

// Dart imports:

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_wallet_and_enrollment_validation.dart';
import 'package:oneapp_network_implementation/oneapp/entity/wallet/payment_method_entity.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../vehicle_search_charge_station_location/vehicle_search_charge_station_location_util.dart';
import '/local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleChargeInfoStationsBloc extends BlocBase {
  final evApi = APIClientConfig.evApiClient;
  final api = APIClientConfig.oneAppClient;

  GetIt locator = GetIt.instance;
  vehicleInfo.Payload? vehicleItem;
  String? currentVin = '';

  Stream<PaymentMethod> get walletDetail => _walletDetail.stream;
  final _walletDetail = BehaviorSubject<PaymentMethod>();

  Stream<bool> get walletPresence => _walletPresence.stream;
  final _walletPresence = BehaviorSubject<bool>();

  Stream<List<PartnerStatus>> get listOfPartners => _listOfPartners.stream;
  final _listOfPartners = BehaviorSubject<List<PartnerStatus>>();

  Stream<bool?> get isWalletFeatureEnabled => _isWalletFeatureEnabled.stream;
  final _isWalletFeatureEnabled = BehaviorSubject<bool?>();

  final partnerNameValues =
      EnumValues({"chargepoint": "ChargePoint", "evgo": "EVgo"});

  void init(bool? isWalletFeatureEnabled) {
    initializeWallet();
    initializePartners();
    _isWalletFeatureEnabled.sink.add(isWalletFeatureEnabled);
  }

  Future<void> initializeWallet() async {
    currentVin = Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(currentVin);

    locator.registerSingletonAsync(
        () async => await VehicleRepo().fetchWalletImages());

    if (vehicleItem != null) {
      if (isFeatureEnabled(WALLET, vehicleItem!.features)) {
        _walletPresence.add(true);
        final commonResponse = await api.fetchWallet();
        final response = commonResponse.response;
        if (response != null &&
            response.status!.messages != null &&
            response.status!.messages!.first.responseCode == 'SUCCESS' &&
            response.payload != null &&
            response.payload!.paymentMethods!.isNotEmpty) {
          PaymentMethod detail = response.payload!.paymentMethods!
              .firstWhere((element) => element.paymentMethodDefault!);
          _walletDetail.sink.add(detail);
        } else {
          _walletDetail.sink.add(PaymentMethod());
        }
      } else {
        _walletPresence.add(false);
      }
    }
  }

  Future<void> initializePartners() async {
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();

    final CommonResponse<EvWalletAndEnrollmentValidation> commonResponse =
        await evApi.checkWalletAndDriverExistence(
      email: Global.getInstance().userEmail ?? '',
      guid: Global.getInstance().guid ?? '',
      make: vehicleInfo?.make ?? '',
    );
    PartnerEnrollment partnerEnrollment =
        commonResponse.response!.payload.partnerEnrollment;
    Global.getInstance().evgoFreeExpireDate =
        partnerEnrollment.evgoFreeExpirationDate();

    VehicleSearchChargeStationLocationUtil.setEVgoFreeExpireDate(
        partnerEnrollment);
    var partnersListData = partnerEnrollment.partnerStatus.map((eachPartner) {
      eachPartner.isEnrolled = eachPartner.status.toUpperCase() == 'FOUND';
      eachPartner.status =
          VehicleSearchChargeStationLocationUtil.partnerEnrollmentStatus(
              partners: eachPartner, wallet: partnerEnrollment.wallet);
      eachPartner.partnerName =
          partnerNameValues.map[eachPartner.partnerName] ?? '';
      return eachPartner;
    }).toList();
    if (!isFeatureEnabled(EV_PARTNER_EVGO, vehicleInfo?.features)) {
      partnersListData.removeWhere(
          (element) => element.partnerName.toLowerCase() == "evgo");
    }
    _listOfPartners.sink.add(partnersListData);
    _listOfPartners.value = partnersListData;
  }

  @override
  void dispose() {
    _walletDetail.close();
    _walletPresence.close();
    _listOfPartners.close();
    _isWalletFeatureEnabled.close();
  }
}
