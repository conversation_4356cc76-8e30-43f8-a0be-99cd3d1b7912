// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:charts_flutter/flutter.dart' as charts;
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

class CleanAssistGraphData {
  final String month;
  final double? value;

  CleanAssistGraphData({required this.month, required this.value});
}

class CleanAssistChart extends StatelessWidget {
  final List<CleanAssistGraphData> data;

  CleanAssistChart({required this.data});
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  Widget build(BuildContext context) {
    List<charts.Series<CleanAssistGraphData, String>> series = [
      charts.Series(
          id: "ca-graph",
          data: data,
          domainFn: (CleanAssistGraphData series, _) => series.month,
          measureFn: (CleanAssistGraphData series, _) => series.value,
          colorFn: (CleanAssistGraphData series, _) =>
              charts.ColorUtil.fromDartColor(_colorUtil.tertiary00)),
    ];

    return charts.BarChart(
      series,
      animate: true,
      primaryMeasureAxis: charts.NumericAxisSpec(
          renderSpec: charts.SmallTickRendererSpec(), showAxisLine: false),
      domainAxis: charts.OrdinalAxisSpec(
        showAxisLine: false,
      ),
      defaultRenderer:
          charts.BarRendererConfig(maxBarWidthPx: 5, minBarLengthPx: 10),
      behaviors: [charts.SelectNearest()],
    );
  }
}
