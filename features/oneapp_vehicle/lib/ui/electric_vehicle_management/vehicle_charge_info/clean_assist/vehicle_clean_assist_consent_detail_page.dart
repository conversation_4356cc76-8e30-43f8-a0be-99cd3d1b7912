// Dart imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ev_module/charge_info/widgets/statics_card/_accordion.dart';
import 'package:ev_module/log/ev_analytics_events.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_network/api_config.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ca/entity/ca_dataconsent_entity.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_marketing_consent_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_marketing_consent_helper.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';
import '../../../../log/vehicle_analytic_event.dart';
import '../../../../log/vehicle_marketing_event.dart';
import '../../vehicle_search_charge_station_location/LegalTermsAndCondition/CustomHTMLDisplay.dart';

class VehicleCleanAssistConsentDetailPage extends StatefulWidget {
  const VehicleCleanAssistConsentDetailPage({Key? key}) : super(key: key);

  @override
  _VehicleCleanAssistConsentDetailPageState createState() =>
      _VehicleCleanAssistConsentDetailPageState();
}

class _VehicleCleanAssistConsentDetailPageState
    extends State<VehicleCleanAssistConsentDetailPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  OneAppClient api = APIClientConfig.oneAppClient;
  Payload? payload;
  late AcknowledgedConsents howCAWorkDetail;
  EligibleConsents? eligibleConsent;
  String whatIsCA = "";
  bool showLoader = true;
  String vehicleMake = "";

  @override
  void initState() {
    super.initState();
    getDataconsentDetail();
  }

  void enrollCA() async {
    CustomerConsentAcceptBodyEntity body = CustomerConsentAcceptBodyEntity(
        eventType: "UpdateSubscription",
        vin: Global.getInstance().vin,
        consents: [
          Consents(consentId: "A5", status: "Accepted", versionId: "v1")
        ]);
    setState(() {
      showLoader = true;
    });
    CommonResponse<CAEligibilityEntity> response =
        await api.customerConsentAccept(APIConfig.xApiKey,
            Global.getInstance().appBrand, Global.getInstance().guid!, body);
    if (response.response!.status!.messages!.isNotEmpty &&
        response.response!.status!.messages!.first.responseCode != null &&
        response.response!.status!.messages!.first.responseCode == "CC-0000") {
      FireBaseAnalyticsLogger.logMarketingEvent(
        VehicleMarketingEvent.LCFS_DESCRIPTION_CONSENT_ACCEPTED,
      );
      // Success
      Global.getInstance().announcementCAEnroll = true;
      goBackToNative();
    } else {
      // Failure
      setState(() {
        showLoader = false;
      });
    }
  }

  void getDataconsentDetail() async {
    VehicleMarketingConsentHelper helper = VehicleMarketingConsentHelper(
        products: List.empty(), productCodes: List.empty(), flowType: "Banner");
    showLoader = true;

    final vehicleItem =
        await (VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin));
    vehicleMake = vehicleItem?.make ?? "";

    CommonResponse<VehicleMarketingConsentEntity> commonResponse =
        await api.fetchMarketingConsent(
            Global.getInstance().vin!,
            vehicleItem?.brand ?? Global.getInstance().appBrand,
            vehicleItem!.generation!,
            vehicleItem.region!,
            vehicleItem.region!,
            "lcfs",
            Global.getInstance().correlationId,
            helper);
    if (commonResponse.response != null) {
      final payload = commonResponse.response?.payload;
      if (payload != null) {
        // Success
        this.payload = payload;
        payload.eligibleConsents?.forEach((element) {
          if (element.name == OneAppString.of().cleanAssist) {
            eligibleConsent = element;
            if (eligibleConsent != null &&
                eligibleConsent?.description != null &&
                eligibleConsent?.description!.dialogs != null) {
              List<Dialogs>? dialogs = eligibleConsent?.description!.dialogs!;
              if (dialogs != null && dialogs.isNotEmpty) {
                whatIsCA = dialogs.first.body ??
                    OneAppString.of().featureUnderMaintenance;
              }
            }
          }
        });
        payload.acknowledgedConsents?.forEach((element) {
          if (element.name == OneAppString.of().cleanAssist) {
            howCAWorkDetail = element;
          }
        });
      }
      setState(() {
        showLoader = false;
      });
    } else {
      // Failure
      setState(() {
        showLoader = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    FireBaseAnalyticsLogger.logMarketingGroupEvent(
      VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
      childEventName: VehicleAnalyticsEvent.VEHICLE_EV_WHAT_IS_CLEAN_ASSIST,
    );
    return OneAppScaffold(
      backgroundColor: _colorUtil.tertiary15,
      body: SafeArea(
        child: showLoader
            ? Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Column(
                  children: [
                    bottomSheetCustomAppBar(
                      OneAppString.of().cleanAssist,
                      onBackPressed: () {
                        Navigator.of(context).pop(true);
                        goNativeCaEnrollStatus(
                            Global.getInstance().announcementCAEnroll);
                      },
                      elevation: 0,
                    ),
                    SizedBox(height: 20.h),
                    Accordion(
                      title: OneAppString.of().whatIsCleanAssist,
                      textStyle: TextStyleExtension().newStyleWithColor(
                        ThemeConfig.current().textStyleUtil.callout1,
                        ThemeConfig.current().colorUtil.tertiary05,
                      ),
                      showContent: false,
                      content: (vehicleMake.toLowerCase() == makeLexus
                              ? OneAppString.of().caConsentPrefaceTextLexus
                              : OneAppString.of().caConsentPrefaceTextToyota) +
                          whatIsCA,
                    ),
                    SizedBox(height: 16.h),
                    Accordion(
                      title: OneAppString.of().termsAndPrivacy,
                      isHtmlContent: true,
                      htmlContent: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: CustomHTMLDisplay(
                          htmlContent: eligibleConsent?.description?.body ?? "",
                          textStyle: TextStyleExtension().newStyleWithColor(
                            ThemeConfig.current().textStyleUtil.callout1,
                            ThemeConfig.current().colorUtil.tertiary05,
                          ),
                          linkColor:
                              isDarkTheme() ? Colors.black : Colors.white,
                        ),
                      ),
                      content: '',
                    ),
                    SizedBox(height: 16.h),
                    ConsentDeclineButton(),
                    caAcceptButton(OneAppString.of().accept),
                  ],
                ),
              ),
      ),
    );
  }

  Widget caAcceptButton(String title) {
    return InkWell(
      onTap: () {
        enrollCA();
      },
      child: Container(
        width: 192.w,
        height: 52.h,
        margin: EdgeInsets.only(bottom: 32.h),
        decoration: BoxDecoration(
            color: ThemeConfig.current().colorUtil.button01b,
            borderRadius: BorderRadius.circular(100.r)),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class ConsentDeclineButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
            VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
            childEventName: EVAnalyticsEvent.CLEAN_ASSIST_DECLINE);
        Navigator.pop(context);
      },
      child: Container(
        width: 192.w,
        height: 52.h,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(100)),
        child: Center(
          child: Text(
            OneAppString.of().decline,
            style: ThemeConfig.current().textStyleUtil.body3,
          ),
        ),
      ),
    );
  }
}
