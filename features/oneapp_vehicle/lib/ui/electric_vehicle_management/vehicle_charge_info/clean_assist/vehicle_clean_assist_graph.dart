// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ca/entity/ca_dataconsent_entity.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';
import 'vehicle_clean_assist_graph_data.dart';

class VehicleCleanAssistGraph extends StatefulWidget {
  const VehicleCleanAssistGraph({Key? key}) : super(key: key);

  @override
  _VehicleCleanAssistGraphState createState() =>
      _VehicleCleanAssistGraphState();
}

class _VehicleCleanAssistGraphState extends State<VehicleCleanAssistGraph> {
  OneAppClient api = APIClientConfig.oneAppClient;
  bool showLoader = true;

  List<CleanAssistGraphData> cleanElectricityChargedInMonthData = [];
  List<CleanAssistGraphData> co2eAvoidedInMonthData = [];

  @override
  void initState() {
    getLCFSDashboard();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return showLoader
        ? Center(child: CircularProgressIndicator())
        : Column(
            children: [
              bottomSheetCustomAppBar(OneAppString.of().cleanAssist,
                  onBackPressed: () => Navigator.of(context).pop(),
                  elevation: 0),
              Expanded(child: parentContainer())
            ],
          );
  }

  Widget parentContainer() {
    return SingleChildScrollView(
      child: Column(
        children: [
          (cleanElectricityChargedInMonthData.isNotEmpty)
              ? caCommonGraph(OneAppString.of().cleanEnergyGraphTitle,
                  cleanElectricityChargedInMonthData)
              : Container(),
          (co2eAvoidedInMonthData.isNotEmpty)
              ? caCommonGraph(OneAppString.of().co2EmissionsGraphTitle,
                  co2eAvoidedInMonthData)
              : Container(),
        ],
      ),
    );
  }

  Container caCommonGraph(String title, List<CleanAssistGraphData> data) {
    return Container(
      decoration:
          BoxDecoration(color: ThemeConfig.current().colorUtil.tertiary12),
      child: Center(
        child: Container(
          margin: EdgeInsets.all(16),
          height: 352.h,
          decoration: BoxDecoration(
              color: ThemeConfig.current().colorUtil.tertiary12,
              borderRadius: BorderRadius.circular(8.r)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 16.h, left: 16.h),
                child: Text(
                  title,
                  style: ThemeConfig.current().textStyleUtil.subHeadline2,
                ),
              ),
              Expanded(
                  child: Center(
                child: CleanAssistChart(
                  data: data,
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  void getLCFSDashboard() async {
    showLoader = true;
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    CommonResponse<LCFSDashboardEntity> response = await api.fetchLCFSDashboard(
        vehicleItem!.brand!, Global.getInstance().vin!);
    cleanElectricityChargedInMonthData.clear();
    co2eAvoidedInMonthData.clear();
    setState(() {
      if (response.response != null) {
        if (response.response!.payload != null) {
          response.response!.payload!.cleanChargingHistory!
              .asMap()
              .forEach((index, element) {
            cleanElectricityChargedInMonthData.add(CleanAssistGraphData(
                month: getMonth(element.monthAndYear!.split("/")[0].toString()),
                value: element.cleanElectricityChargedInMonth));
            co2eAvoidedInMonthData.add(CleanAssistGraphData(
                month: getMonth(element.monthAndYear!.split("/")[0].toString()),
                value: element.co2eAvoidedInMonth));
          });
        }
      }
      showLoader = false;
    });
  }
}
