// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_network/api_config.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ca/entity/ca_dataconsent_entity.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';
import '../../../../log/vehicle_marketing_event.dart';
import 'vehicle_clean_assist_consent_detail_page.dart';
import 'vehicle_clean_assist_graph.dart';

class VehicleCleanAssistPage extends StatefulWidget {
  const VehicleCleanAssistPage({Key? key, required this.dashboardEnroll})
      : super(key: key);
  final bool dashboardEnroll;

  @override
  _VehicleCleanAssistPageState createState() => _VehicleCleanAssistPageState();
}

class _VehicleCleanAssistPageState extends State<VehicleCleanAssistPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  OneAppClient api = APIClientConfig.oneAppClient;
  bool showGraph = false;
  bool showLoader = false;

  @override
  void initState() {
    if (!widget.dashboardEnroll) checkCleanAssistEligibility();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return (showLoader)
        ? Center(
            child: CircularProgressIndicator(),
          )
        : SingleChildScrollView(child: _parentView(context));
  }

  Widget _parentView(BuildContext context) {
    return (showGraph)
        ? VehicleCleanAssistGraph()
        : Container(
            height: 470.h,
            child: Column(
              children: [
                Center(
                  child: Text(
                    OneAppString.of().cleanAssist,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
                  ),
                ),
                caEnroll(),
                Center(
                  child: InkWell(
                    onTap: () {
                      showMaterialModalBottomSheet(
                        expand: false,
                        context: context,
                        clipBehavior: Clip.antiAliasWithSaveLayer,
                        backgroundColor: _colorUtil.tertiary15,
                        builder: (context) =>
                            VehicleCleanAssistConsentDetailPage(),
                      ).then((value) => setState(() {
                            if (!widget.dashboardEnroll) {
                              checkCleanAssistEligibility();
                            }
                          }));
                      FireBaseAnalyticsLogger.logMarketingEvent(
                        VehicleMarketingEvent.LCFS_DASHBOPARD_TAP_CARD,
                      );
                    },
                    child: Container(
                      width: 192.w,
                      height: 52.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100),
                        color: _colorUtil.button01b,
                      ),
                      child: Center(
                        child: Text(
                          OneAppString.of().enrollNow,
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.body3, _colorUtil.button01a),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
  }

  Widget caEnroll() {
    return Container(
      height: 296.h,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
          color: _colorUtil.button05b,
          borderRadius: BorderRadius.circular(8),
          image: DecorationImage(
              image: ExactAssetImage(cleanAssistEnrollBGIcon),
              fit: BoxFit.cover)),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(
                top: 24.h, left: 16.w, right: 16.w, bottom: 8.h),
            child: Text(
              OneAppString.of().renewableEnergyCredit,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.subHeadline3, _colorUtil.tertiary15),
              textAlign: TextAlign.center,
            ),
          ),
          Row()
        ],
      ),
    );
  }

  void checkCleanAssistEligibility() async {
    setState(() {
      showLoader = true;
    });
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();
    CommonResponse<CAEligibilityEntity> response =
        await api.checkCleanAssistEligibility(
            Global.getInstance().vin!,
            APIConfig.xApiKey,
            vehicleInfo!.brand ?? Global.getInstance().appBrand,
            Global.getInstance().guid!);
    final payload = response.response?.payload;
    if (payload != null) {
      // Success
      if (payload.lcfsOptIn != null && payload.lcfsOptIn == 'In') {
        showGraph = true;
      } else {
        showGraph = false;
      }
      setState(() {
        showLoader = false;
      });
    } else {
      // Failure
    }
  }
}
