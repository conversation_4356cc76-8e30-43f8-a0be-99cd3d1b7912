// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network_implementation/oneapp/entity/wallet/payment_method_entity.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';
import '../../../vehicle_finance/vehicle_finance_bloc.dart';
import '../../../vehicle_finance/wallet/add_wallet/add_wallet_page.dart';
import '../../../vehicle_finance/wallet/wallet_helper/card_helper.dart';
import '../../../vehicle_finance/wallet/wallet_home/wallet_home_page.dart';

class VehicleChargeInfoStationWalletPage extends StatefulWidget {
  const VehicleChargeInfoStationWalletPage({Key? key}) : super(key: key);

  @override
  _VehicleChargeInfoStationWalletPageState createState() =>
      _VehicleChargeInfoStationWalletPageState();
}

class _VehicleChargeInfoStationWalletPageState
    extends State<VehicleChargeInfoStationWalletPage> {
  VehicleFinanceBloc _bloc = VehicleFinanceBloc();
  ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  void initState() {
    _bloc.init(_progressHandlerCallback);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(child: _walletMethods());
  }

  Widget walletWidget(bool isWallet, String walletImage,
      [PaymentMethod? data]) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: InkWell(
            onTap: () {
              _showSetUpWalletScreen(isWallet);
            },
            child: Container(
              margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
              child: Column(
                children: <Widget>[
                  Container(
                    decoration: BoxDecoration(
                        color: _colorUtil.tile02,
                        borderRadius: BorderRadius.all(
                            Radius.circular(CARD_RADIUS_SMALL))),
                    child: Container(
                      margin:
                          EdgeInsets.symmetric(vertical: 1.h, horizontal: 1.w),
                      child: isWallet
                          ? cardHolder(data!.card!.brand!, data.card!.last4)
                          : Image.asset(
                              walletImage,
                            ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      top: 44.h,
                    ),
                    child: Center(
                      child: CustomDefaultButton(
                        backgroundColor: _colorUtil.button01b,
                        buttonTextColor: _colorUtil.button01a,
                        text: OneAppString.of().findStation,
                        press: () {
                          VehicleRepo().fetchIsEvPublicChargingEnabled().then(
                                (isPublicChargingEnabled) =>
                                    NavigateService.pushNamedRoute(
                                  RoutePath
                                      .VEHICLE_SEARCH_CHARGE_STATION_LOCATION,
                                  arguments: isPublicChargingEnabled,
                                ),
                              );
                        },
                        borderColor: _colorUtil.button01b,
                        horizontalPadding: 22.w,
                        verticalPadding: 1.h,
                      ),
                    ),
                  ),
                ],
              ),
            )),
      ),
    );
  }

  Widget _walletMethods() {
    return StreamBuilder<bool>(
        stream: _bloc.walletPresence,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data == true) {
            return StreamBuilder<PaymentMethod>(
                stream: _bloc.walletDetail,
                builder: (context, snapshot) {
                  if (snapshot.hasData &&
                      snapshot.data!.paymentMethodDefault == true) {
                    return walletWidget(true, walletSetupCard, snapshot.data);
                  } else {
                    return walletWidget(false, walletSetupCard);
                  }
                });
          } else {
            return SizedBox.shrink();
          }
        });
  }

  Widget cardHolder(String brand, String? last4) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 40.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(20.0)),
            gradient: RadialGradient(
              center: Alignment(0.7, -0.6), // near the top right
              radius: 0.9,
              colors: <Color>[
                Color(0xff4a4f55),
                Color(0xff181920),
              ],
            )),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    formatFirstLetterCaps(brand),
                    style: TextStyleExtension().newStyleWithColor(
                        ThemeConfig.current().textStyleUtil.body3,
                        ThemeConfig.current().colorUtil.tertiary15),
                  ),
                  Text(
                    "•••• $last4",
                    style: TextStyleExtension().newStyleWithColor(
                        ThemeConfig.current().textStyleUtil.body3,
                        ThemeConfig.current().colorUtil.tertiary15),
                  ),
                ],
              ),
              SizedBox(
                height: 52.h,
              ),
              FutureBuilder<String?>(
                future: CardHelper().getCardImage(brand),
                builder:
                    (BuildContext context, AsyncSnapshot<String?> snapshot) {
                  switch (snapshot.connectionState) {
                    case ConnectionState.waiting:
                      return Container();
                    default:
                      return CachedNetworkImage(
                        height: 34.h,
                        imageUrl: snapshot.data!,
                        errorWidget: (context, url, error) => Image.asset(
                          imageNotFoundImage,
                        ),
                      );
                  }
                },
              ),
              SizedBox(
                height: 22.h,
              ),
            ],
          ),
        ));
  }

  _showSetUpWalletScreen(bool isPresent) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(
                child: isPresent
                    ? WalletHomePage(isFromChargeInfo: true)
                    : AddWallet()),
          ],
        ),
      ),
    );
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }
}
