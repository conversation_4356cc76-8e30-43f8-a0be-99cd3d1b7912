// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';

// Project imports:
import '../../../log/vehicle_analytic_event.dart';
import 'clean_assist/vehicle_clean_assist_page.dart';
import 'managedcharging/schedule_listing_page.dart';
import 'vehicle_charge_info_bloc.dart';
import 'vehicle_charge_info_station_history/vehicle_charge_info_station_history_page.dart';

class VehicleChargeInfoPage extends StatefulWidget {
  const VehicleChargeInfoPage({Key? key}) : super(key: key);

  @override
  _VehicleChargeInfoPageState createState() => _VehicleChargeInfoPageState();
}

class _VehicleChargeInfoPageState extends State<VehicleChargeInfoPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  VehicleChargeInfoBloc _bloc = VehicleChargeInfoBloc();
  ScrollController? _scrollController;

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logInfo(
        VehicleAnalyticsEvent.VEHICLE_CHARGE_STATION_PAGE);
    _bloc.init();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        child: Navigator(
            onGenerateRoute: (_) =>
                MaterialPageRoute(builder: (materialContext) {
                  return Builder(builder: (builderContext) {
                    return _chargingScreenWidget();
                  });
                })));
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  Widget _chargingScreenWidget() {
    return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
      SingleChildScrollView(
        controller: _scrollController,
        child: Container(
          color: _colorUtil.button01a,
          padding: EdgeInsets.only(top: 10.h, left: 5.h, right: 5.h),
          child: Column(
            children: [
              SwipeBarIcon(),
              Padding(
                padding: const EdgeInsets.only(top: 20),
                child: Text(OneAppString.of().chargeInfo,
                    maxLines: 1,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.subHeadline3, _colorUtil.tertiary03)),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 0.h,
                    ),
                    _fuelIcon(),
                    SizedBox(
                      width: 10.w,
                    ),
                    Text(
                      '75%',
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.title2, _colorUtil.tertiary03),
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 15),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    height: 10,
                    width: 180,
                    child: const LinearProgressIndicator(
                        value: 0.75,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                        backgroundColor: Colors.grey),
                  ),
                ),
              ),
              SizedBox(
                height: 20.h,
              ),
              Text(
                "Left on Charge",
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.caption1, _colorUtil.tertiary05),
              ),
              SizedBox(
                height: 10.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "115 ",
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.title3, _colorUtil.tertiary03),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 14),
                    child: Text(
                      "mi",
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.title2, _colorUtil.tertiary03),
                    ),
                  ),
                ],
              ),
              Text(
                "or 75 mi with AC on",
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body3, _colorUtil.tertiary00),
              ),
              SizedBox(
                height: 10.h,
              ),
              // Expanded(child: _stationInfo()),
              Container(
                height: MediaQuery.of(context).size.height - 315,
                child: _stationInfo(),
              )
            ],
          ),
        ),
      )
    ]);
  }

  DefaultTabController _stationInfo() {
    return DefaultTabController(
        length: 3,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            TabBar(
              labelColor: Colors.black,
              indicatorColor: Colors.black,
              indicatorSize: TabBarIndicatorSize.label,
              labelStyle: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body4, _colorUtil.tertiary00),
              unselectedLabelStyle: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body3, _colorUtil.tertiary00),
              tabs: [
                Tab(text: "Schedule"),
                Tab(text: "History"),
                Tab(text: "Clean Assist"),
              ],
            ),
            Flexible(
              child: TabBarView(
                children: [
                  ScheduleListing(),
                  VehicleChargeInfoStationHistoryPage(),
                  VehicleCleanAssistPage(
                    dashboardEnroll: false,
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  Widget _fuelIcon() {
    return SvgPicture.asset(
      evChargingIcon,
      height: 20.w,
      width: 20.w,
      colorFilter: ColorFilter.mode(
        _colorUtil.tertiary03,
        BlendMode.srcIn,
      ),
      allowDrawingOutsideViewBox: false,
    );
  }
}
