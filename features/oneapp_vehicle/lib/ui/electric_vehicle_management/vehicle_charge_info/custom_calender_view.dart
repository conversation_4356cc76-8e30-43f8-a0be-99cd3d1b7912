// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

class CustomCalenderView extends StatefulWidget {
  final String monthName;
  final String date;
  final Color? boxColor;
  final double width;
  final double height;

  CustomCalenderView(
      {Key? key,
      this.monthName = "",
      this.date = "",
      this.boxColor,
      this.width = 60,
      this.height = 60})
      : super(key: key);

  @override
  _CustomCalenderViewState createState() => _CustomCalenderViewState();
}

class _CustomCalenderViewState extends State<CustomCalenderView> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
            child: Container(
                height: widget.height.h,
                width: widget.width.w,
                decoration: BoxDecoration(
                    color: _colorUtil.secondary01,
                    border: Border.all(color: _colorUtil.secondary01, width: 1),
                    borderRadius: BorderRadius.circular(10)),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        widget.monthName,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.tabLabel01, _colorUtil.tertiary15),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      child: Container(
                          height: 30.h,
                          width: 60.w,
                          decoration: BoxDecoration(
                              color: _colorUtil.tertiary15,
                              border: Border.all(
                                  color: _colorUtil.secondary01, width: 1.w),
                              borderRadius: BorderRadius.only(
                                  bottomRight: Radius.circular(10),
                                  bottomLeft: Radius.circular(10))),
                          child: Text(
                            "${widget.date}",
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.callout1, _colorUtil.tertiary00),
                            textAlign: TextAlign.center,
                          )),
                    )
                  ],
                )))
      ],
    );
  }
}
