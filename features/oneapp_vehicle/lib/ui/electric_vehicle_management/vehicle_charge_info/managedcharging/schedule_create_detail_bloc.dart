// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/mc/entity/create_schedule_req_entity.dart';
import 'package:oneapp_network_implementation/mc/entity/schedule_listing_entity.dart';
import 'package:oneapp_network_implementation/mc/entity/update_schedule_req_entity.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '/local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/mc/entity/create_schedule_req_entity.dart'
    as c;
import 'package:oneapp_network_implementation/mc/entity/update_schedule_req_entity.dart'
    as u;

class MultidayScheduleCreateDetailBloc extends BlocBase {
  OneAppClient api = APIClientConfig.oneAppClient;
  late Function(bool) progressHandlerCallback;

  String? _requestNo;

  Stream<bool> get createScheduleSuccess => _createScheduleSuccess.stream;
  final _createScheduleSuccess = BehaviorSubject<bool>();

  Stream<bool> get updateScheduleSuccess => _updateScheduleSuccess.stream;
  final _updateScheduleSuccess = BehaviorSubject<bool>();

  Stream<bool> get deleteScheduleSuccess => _deleteScheduleSuccess.stream;
  final _deleteScheduleSuccess = BehaviorSubject<bool>();

  void init(Function(bool) progressHandler) {
    _requestNo = '';
    progressHandlerCallback = progressHandler;
  }

  String? getRequestNo() {
    return _requestNo;
  }

  Future<void> createScheduleAPI(
      bool startTimeAMSelected,
      bool endTimeAMSelected,
      bool departureTimeSwitch,
      TextEditingController _startHourcontroller,
      TextEditingController _endHourcontroller,
      TextEditingController _startMincontroller,
      TextEditingController _endMincontroller,
      List<String> daysOfWeek,
      Function errorCallBack) async {
    CreateScheduleRequestEntity reqBodyObj;
    var startHour, endHour = 0;

    if (startTimeAMSelected == endTimeAMSelected &&
        _startHourcontroller.text == _endHourcontroller.text &&
        _startMincontroller.text == _endMincontroller.text) {
      errorCallBack(OneAppString.of().sameStartEndTime);
      return;
    }

    if (startTimeAMSelected) {
      if (int.parse(_startHourcontroller.text) == 12) {
        startHour = 00;
      } else {
        startHour = int.parse(_startHourcontroller.text);
      }
    } else {
      if (int.parse(_startHourcontroller.text) == 12) {
        startHour = 12;
      } else {
        startHour = int.parse(_startHourcontroller.text) + 12;
      }
    }
    if (endTimeAMSelected) {
      if (int.parse(_endHourcontroller.text) == 12) {
        endHour = 00;
      } else {
        endHour = int.parse(_endHourcontroller.text);
      }
    } else {
      if (int.parse(_endHourcontroller.text) == 12) {
        endHour = 12;
      } else {
        endHour = int.parse(_endHourcontroller.text) + 12;
      }
    }
    if (departureTimeSwitch) {
      reqBodyObj = CreateScheduleRequestEntity(
        enabled: true,
        startTime: c.StartTime(
          hour: startHour,
          minute: int.parse(_startMincontroller.text),
        ),
        endTime: c.StartTime(
          hour: endHour,
          minute: int.parse(_endMincontroller.text),
        ),
        daysOfTheWeek: daysOfWeek,
      );
    } else {
      reqBodyObj = CreateScheduleRequestEntity(
        enabled: true,
        startTime: c.StartTime(
          hour: startHour,
          minute: int.parse(_startMincontroller.text),
        ),
        daysOfTheWeek: daysOfWeek,
      );
    }
    progressHandlerCallback(true);

    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    final brand = vehicleItem?.brand;
    final generation = vehicleItem?.generation;
    final fcmDeviceId = Global.getInstance().fcmDeviceId;
    final vin = Global.getInstance().vin;
    if (brand == null ||
        generation == null ||
        fcmDeviceId == null ||
        vin == null) {
      progressHandlerCallback(false);
      return;
    }
    final commonResponse = await api.createMultidayChargeSchedule(
        reqBodyObj, brand, vin, 'application/json', generation, fcmDeviceId);

    if (commonResponse.response != null) {
      _requestNo = commonResponse.response!.payload!.appRequestNo;
      progressHandlerCallback(false);
      _createScheduleSuccess.sink.add(true);
    } else if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(commonResponse.error!.errorMessage);
    }
  }

  Future<void> updateScheduleAPI(
      TimerChargeInfo? scheduleData,
      bool startTimeAMSelected,
      bool endTimeAMSelected,
      bool departureTimeSwitch,
      TextEditingController _startHourcontroller,
      TextEditingController _endHourcontroller,
      TextEditingController _startMincontroller,
      TextEditingController _endMincontroller,
      List<String> daysOfWeek,
      Function errorCallBack) async {
    UpdateScheduleRequestEntity reqBodyObj;
    var startHour, endHour = 0;

    if (startTimeAMSelected == endTimeAMSelected &&
        _startHourcontroller.text == _endHourcontroller.text &&
        _startMincontroller.text == _endMincontroller.text) {
      errorCallBack(OneAppString.of().sameStartEndTime);
      return;
    }

    if (startTimeAMSelected) {
      if (int.parse(_startHourcontroller.text) == 12) {
        startHour = 00;
      } else {
        startHour = int.parse(_startHourcontroller.text);
      }
    } else {
      if (int.parse(_startHourcontroller.text) == 12) {
        startHour = 12;
      } else {
        startHour = int.parse(_startHourcontroller.text) + 12;
      }
    }
    if (endTimeAMSelected) {
      if (int.parse(_endHourcontroller.text) == 12) {
        endHour = 00;
      } else {
        endHour = int.parse(_endHourcontroller.text);
      }
    } else {
      if (int.parse(_endHourcontroller.text) == 12) {
        endHour = 12;
      } else {
        endHour = int.parse(_endHourcontroller.text) + 12;
      }
    }
    if (departureTimeSwitch) {
      reqBodyObj = u.UpdateScheduleRequestEntity(
        settingId: scheduleData!.settingId as int?,
        enabled: scheduleData.enabled,
        startTime: u.StartTime(
          hour: startHour,
          minute: int.parse(_startMincontroller.text),
        ),
        endTime: u.StartTime(
          hour: endHour,
          minute: int.parse(_endMincontroller.text),
        ),
        daysOfTheWeek: daysOfWeek,
      );
    } else {
      reqBodyObj = u.UpdateScheduleRequestEntity(
        settingId: scheduleData!.settingId as int?,
        enabled: scheduleData.enabled,
        startTime: u.StartTime(
          hour: startHour,
          minute: int.parse(_startMincontroller.text),
        ),
        daysOfTheWeek: daysOfWeek,
      );
    }
    progressHandlerCallback(true);

    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);

    final commonResponse = await api.updateMultidayChargeSchedule(
        reqBodyObj,
        vehicleItem!.brand!,
        Global.getInstance().vin!,
        'application/json',
        vehicleItem.generation ?? "",
        Global.getInstance().fcmDeviceId!);

    if (commonResponse.response != null) {
      _requestNo = commonResponse.response!.payload!.appRequestNo;
      progressHandlerCallback(false);
      _updateScheduleSuccess.sink.add(true);
    } else if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(commonResponse.error!.errorMessage);
    }
  }

  Future<void> deleteScheduleAPI(
      String settingId, Function errorCallBack) async {
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);

    progressHandlerCallback(true);

    final commonResponse = await api.deleteMultidayChargeSchedule(
        vehicleItem!.brand!,
        settingId,
        Global.getInstance().vin!,
        'application/json',
        vehicleItem.generation ?? "",
        Global.getInstance().fcmDeviceId!);
    if (commonResponse.response != null) {
      _requestNo = commonResponse.response!.payload!.appRequestNo;
      progressHandlerCallback(false);
      _deleteScheduleSuccess.sink.add(true);
    } else if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(commonResponse.error!.errorMessage);
    }
  }

  @override
  void dispose() {
    _createScheduleSuccess.close();
    _updateScheduleSuccess.close();
    _deleteScheduleSuccess.close();
  }
}
