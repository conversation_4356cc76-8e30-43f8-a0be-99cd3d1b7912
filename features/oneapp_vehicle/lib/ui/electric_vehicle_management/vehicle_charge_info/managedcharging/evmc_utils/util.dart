// Package imports:
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_network_implementation/mc/entity/schedule_listing_entity.dart';

const String Sunday = 'Sunday';
const String Monday = 'Monday';
const String Tuesday = 'Tuesday';
const String Wednesday = 'Wednesday';
const String Thursday = 'Thursday';
const String Friday = 'Friday';
const String Saturday = "Saturday";

class ScheduleBoxedReturns {
  final String? requestNo;
  final String toastMsg;

  ScheduleBoxedReturns(this.requestNo, this.toastMsg);
}

String getScheduleDaysString(List<String> daysSelected) {
  if (daysSelected.contains(Sunday) &&
      daysSelected.contains(Monday) &&
      daysSelected.contains(Tuesday) &&
      daysSelected.contains(Wednesday) &&
      daysSelected.contains(Thursday) &&
      daysSelected.contains(Friday) &&
      daysSelected.contains(Saturday)) {
    return OneAppString.of().everyday;
  } else if (daysSelected.contains(Monday) &&
      daysSelected.contains(Tuesday) &&
      daysSelected.contains(Wednesday) &&
      daysSelected.contains(Thursday) &&
      daysSelected.contains(Friday)) {
    return OneAppString.of().weekdays;
  } else if (daysSelected.length == 2 &&
      (daysSelected.contains(Saturday) && daysSelected.contains(Sunday))) {
    return OneAppString.of().weekend;
  } else {
    List<String> daysToDisplay = [];
    for (final element in daysSelected) {
      daysToDisplay.add(_getTranslatedValue(element).substring(0, 3));
    }
    String daysToDisplayStr = daysToDisplay.join(', ');
    return daysToDisplayStr;
  }
}

dynamic _getTranslatedValue(String name) {
  switch (name) {
    case 'Sunday':
      return OneAppString.of().sunday;
    case 'Monday':
      return OneAppString.of().monday;
    case 'Tuesday':
      return OneAppString.of().tuesday;
    case 'Wednesday':
      return OneAppString.of().wednesday;
    case 'Thursday':
      return OneAppString.of().thursday;
    case 'Friday':
      return OneAppString.of().friday;
    case 'Saturday':
      return OneAppString.of().saturday;
    default:
      return name;
  }
}

int? getOffPeakIndex(List<TimerChargeInfo> listingDataArray) {
  for (var i = 0; i < listingDataArray.length; i++) {
    if (isOffPeakSchedule(listingDataArray[i])) return i;
  }
  return null;
}

bool isOffPeakSchedule(TimerChargeInfo scheduleData) {
  int? startHour, startMin, endHour, endMin = 0;
  startHour = int.parse(scheduleData.startTime!.split(':').first);
  startMin = int.parse(scheduleData.startTime!.split(':').last);
  if (scheduleData.endTime != null) {
    endHour = int.parse(scheduleData.endTime!.split(':').first);
    endMin = int.parse(scheduleData.endTime!.split(':').last);
  }

  bool hasOffPeakDays = false;
  if (scheduleData.daysOfTheWeek!.contains(Sunday) &&
      scheduleData.daysOfTheWeek!.contains(Monday) &&
      scheduleData.daysOfTheWeek!.contains(Tuesday) &&
      scheduleData.daysOfTheWeek!.contains(Wednesday) &&
      scheduleData.daysOfTheWeek!.contains(Thursday) &&
      scheduleData.daysOfTheWeek!.contains(Friday) &&
      scheduleData.daysOfTheWeek!.contains(Saturday)) hasOffPeakDays = true;

  if (startHour == 23 &&
      startMin == 0 &&
      endHour == 4 &&
      endMin == 0 &&
      hasOffPeakDays) {
    return true;
  } else {
    return false;
  }
}
