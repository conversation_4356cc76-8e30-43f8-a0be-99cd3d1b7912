// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/custom_page_route.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/mc/entity/schedule_listing_entity.dart';
import 'package:super_tooltip/super_tooltip.dart';

// Project imports:
import '../../../../log/vehicle_analytic_event.dart';
import '../../../vehicle_charge_management/vehicle_charge_management_bloc.dart';
import 'evmc_utils/util.dart';
import 'schedule_create_detail_page.dart';
import 'schedule_listing_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart'
    as cm;

class ScheduleListing extends StatefulWidget {
  final cm.ChargeInfo? chargeInfo;
  final String? acquisitionDatetime;
  final ScrollController? scrollController;
  final BuildContext? materialContext;
  final Function(bool)? reloadChargeManagementPage;
  final VehicleChargeManagementBloc? vehicleBloc;

  ScheduleListing(
      {Key? key,
      this.chargeInfo,
      this.acquisitionDatetime,
      this.scrollController,
      this.materialContext,
      this.reloadChargeManagementPage,
      this.vehicleBloc})
      : super(key: key);

  @override
  State<ScheduleListing> createState() => _ScheduleListingState();
}

class _ScheduleListingState extends State<ScheduleListing> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  ScrollController listScrollcontroller = ScrollController();

  bool noSchedule = false;
  bool scheduleLimitReached = false;
  bool showLoader = true;

  String requestNo = '';
  MultidayScheduleListingBloc _bloc = MultidayScheduleListingBloc();
  List<TimerChargeInfo>? _listingData = [];
  SuperTooltip? tooltip;
  BuildContext? offPeakWidgetContext;

  @override
  void initState() {
    super.initState();
    _bloc.init(widget.chargeInfo, widget.acquisitionDatetime,
        _progressHandlerCallback);
    widget.vehicleBloc!.reloadMultidaySchedules.listen((requestNo) {
      _bloc.refreshUI(requestNo);
    });
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_EV_MULTIDAY_SCHEDULE_PAGE);
  }

  void triggerOffPeakTooltip() {
    Future.delayed(Duration(seconds: 1), () {
      if (offPeakWidgetContext != null) {
        showOffPeakTooltip(offPeakWidgetContext);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: true,
      child: StreamBuilder<List<TimerChargeInfo>?>(
        stream: _bloc.scheduleList,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            _listingData = snapshot.data;
            scheduleLimitReached =
                (_listingData!.length == _bloc.getMaxSchedulesLimit());
            widget.vehicleBloc!.updateFloatingUI(_listingData!);
            noSchedule = _listingData!.isEmpty;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                Container(
                  child: noSchedule
                      ? schedulesInfoImg()
                      : scheduleListing(_listingData),
                ),
                SizedBox(height: 10.h),
              ],
            );
          } else {
            return _shimmerLoadingLayout();
          }
        },
      ),
      // ),
    );
  }

  Widget scheduleListing(List<TimerChargeInfo>? listingData) {
    List<TimerChargeInfo>? listingDataArray = [];
    listingDataArray = listingData;
    int? offPeakIndex =
        getOffPeakIndex(listingDataArray!); //Saving the first off-peak index

    if (listingDataArray.length > 1 && offPeakIndex != null) {
      TimerChargeInfo offPeakObject = listingDataArray[offPeakIndex];
      listingDataArray
          .removeAt(offPeakIndex); //Remove off-peak schedule from its position
      listingDataArray.insert(
          0, offPeakObject); //Insert off-peak schedule in the 1st position
      offPeakIndex =
          0; //Off-peak schedule will be at the first index after the above operation
    }

    if (listingDataArray.isEmpty) return schedulesInfoImg();

    return Container(
      height: (listingDataArray.length + 2) * 72.h,
      child: MediaQuery.removePadding(
        removeTop: true,
        context: context,
        child: ListView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: listingDataArray.length + 1,
          itemBuilder: (BuildContext context, int index) {
            if (listingDataArray!.length != index) {
              bool isOffPeakSchedule = (index == offPeakIndex);
              bool hasEndTime = false;
              if (listingDataArray[index].endTime != null) hasEndTime = true;

              String startTimeToDisplay = convertScheduleTimeTo12Hour(
                  listingDataArray[index].startTime!);
              String endTimeToDisplay = hasEndTime
                  ? '${convertScheduleTimeTo12Hour(listingDataArray[index].endTime!)}'
                  : '';

              String lineOneText = isOffPeakSchedule
                  ? OneAppString.of().offpeakSchedule
                  : hasEndTime
                      ? '${OneAppString.of().schedule} • $startTimeToDisplay - $endTimeToDisplay'
                      : '${OneAppString.of().schedule} • $startTimeToDisplay';

              String lineTwoText = isOffPeakSchedule
                  ? '$startTimeToDisplay - $endTimeToDisplay • ${getScheduleDaysString(listingDataArray[index].daysOfTheWeek!)}'
                  : getScheduleDaysString(
                      listingDataArray[index].daysOfTheWeek!);

              if (isOffPeakSchedule &&
                  Global.getInstance().offPeakListTooltipShown == null) {
                return Builder(
                  builder: (BuildContext widgetContext) {
                    offPeakWidgetContext = widgetContext;
                    triggerOffPeakTooltip();
                    Global.getInstance().offPeakListTooltipShown = true;
                    return getScheduleListCardView(index, listingDataArray!,
                        lineOneText, lineTwoText, isOffPeakSchedule);
                  },
                );
              } else {
                return getScheduleListCardView(index, listingDataArray,
                    lineOneText, lineTwoText, isOffPeakSchedule);
              }
            } else {
              return StreamBuilder<bool>(
                stream: widget.vehicleBloc!.isChargingOrWaiting,
                builder: (context, snapshot) {
                  return (snapshot.hasData && snapshot.data == false)
                      ? Container(
                          child: Center(
                            child: _refreshIconLayout(),
                          ),
                        )
                      : Container();
                },
              );
            }
          },
        ),
      ),
    );
  }

  Widget getScheduleListCardView(
      int index,
      List<TimerChargeInfo> listingDataArray,
      String lineOneText,
      String lineTwoText,
      bool isOffPeakSchedule) {
    return Container(
      height: 72.h,
      child: Card(
        color: _colorUtil.tile02,
        child: ListTile(
          minVerticalPadding: 5.h,
          title: Text(
            lineOneText,
            style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
          ),
          subtitle: Text(
            '$lineTwoText',
            style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.callout1, _colorUtil.tertiary05),
          ),
          trailing: SizedBox(
            height: 34.0.h,
            width: 60.0.w,
            child: FlutterSwitch(
              width: 60.0.w,
              height: 34.0.h,
              activeColor: Colors.white,
              inactiveColor: Colors.white,
              inactiveToggleColor: _colorUtil.tertiary05,
              activeToggleColor: _colorUtil.button03b,
              switchBorder: Border.all(color: _colorUtil.tertiary10),
              toggleSize: 20.0.w,
              value: listingDataArray[index].enabled!,
              borderRadius: 30.0.w,
              showOnOff: false,
              onToggle: (val) {
                _bloc.updateScheduleAPI(
                    val, listingDataArray[index], _showErrorToastMessage);
              },
            ),
          ),
          onTap: () {
            Navigator.of(context)
                .push(CustomPageRoute(
                    page: ScheduleDetailPage(
              scheduleData: listingDataArray[index],
              isOffPeakSchedule: isOffPeakSchedule,
            )))
                .then((value) {
              if (value != null) {
                ScheduleBoxedReturns returnVal = value;
                requestNo = returnVal.requestNo ?? '';
                showCustomToast(_toastCustomWidget(returnVal.toastMsg), 2);
                _bloc.refreshUI(requestNo);
              }
            });
          },
        ),
      ),
    );
  }

  Widget schedulesInfoImg() {
    Color gradientStart = Colors.black;
    Color gradientEnd = Colors.transparent;

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: ShaderMask(
              shaderCallback: (rect) {
                return LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [gradientStart, gradientEnd],
                ).createShader(
                    Rect.fromLTRB(0, 0, rect.width - 40.w, rect.height - 49.h));
              },
              blendMode: BlendMode.darken,
              child: Container(
                height: 296.h,
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: ExactAssetImage(
                          _bloc.vehicleItem?.make.toLowerCase() == makeLexus
                              ? mcBGImgLexus
                              : mcBGImg),
                      fit: BoxFit.cover),
                ),
              ),
            ),
          ),
          Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 30.h, left: 16.w, right: 16.w),
                child: Text(
                  OneAppString.of().evmcNoScheduleTitleText,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout2, _colorUtil.tertiary15),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 24.h, left: 24.w, right: 24.w),
                child: Text(
                  OneAppString.of().evmcNoScheduleSubtitleText,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline3, _colorUtil.tertiary15),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget createScheduleButton(String title) {
    return InkWell(
      onTap: () {
        if (!scheduleLimitReached) {
          Navigator.of(context)
              .push(CustomPageRoute(page: ScheduleDetailPage()))
              .then((value) {
            if (value != null) {
              ScheduleBoxedReturns returnVal = value;
              requestNo = returnVal.requestNo ?? '';
              showCustomToast(_toastCustomWidget(returnVal.toastMsg), 2);
              _bloc.refreshUI(requestNo);
            }
          });
        }
      },
      child: Container(
        width: 192.w,
        height: 52.h,
        decoration: BoxDecoration(
            color: ThemeConfig.current().colorUtil.button01b,
            borderRadius: BorderRadius.circular(100.r)),
        child: Center(
          child: Text(
            title,
            style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.callout2,
                scheduleLimitReached
                    ? _colorUtil.tertiary07
                    : _colorUtil.button01a),
          ),
        ),
      ),
    );
  }

  Widget _refreshIconLayout() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        children: [
          InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              // _bloc.refreshUI('');
              _bloc.refreshScheduleData(
                  _savedResponseCallback, _reloadChargeManagementPage);
            },
            child: CircleAvatar(
              radius: 24.r,
              backgroundColor: _colorUtil.button02d,
              child: SvgPicture.asset(
                refreshIcon,
                colorFilter: ColorFilter.mode(
                  _colorUtil.button02a,
                  BlendMode.srcIn,
                ),
                semanticsLabel: REFRESH_BUTTON,
              ),
            ),
          ),
          StreamBuilder<String>(
              stream: _bloc.lastUpdatedTime,
              builder: (context, snapshot) {
                return Container(
                  margin: EdgeInsets.only(top: 8.h),
                  child: Text(
                    snapshot.hasData ? snapshot.data! : " - ",
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.caption1, _colorUtil.tertiary05),
                  ),
                );
              }),
        ],
      ),
    );
  }

  void _reloadChargeManagementPage(bool reload) {
    _progressHandlerCallback(false);
    // widget.reloadChargeManagementPage(reload);
  }

  void _savedResponseCallback() {
    showCustomToast(
        _responseToastCustomWidget(OneAppString.of().failure, false), 3);
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  Widget _shimmerLoadingLayout() {
    return Padding(
      padding: EdgeInsets.only(left: 12.w, right: 12.w),
      child: Column(
        children: [
          Container(height: 8.h),
          shimmerRectangle(80.h, double.maxFinite, CARD_RADIUS_SMALL),
          Container(height: 8.h),
          shimmerRectangle(80.h, double.maxFinite, CARD_RADIUS_SMALL),
          Container(height: 8.h),
          shimmerRectangle(80.h, double.maxFinite, CARD_RADIUS_SMALL),
          Container(height: 8.h),
        ],
      ),
    );
  }

  void _showErrorToastMessage(String message) {
    showCustomToast(_toastCustomWidget(message), 3);
  }

  void showOffPeakTooltip(BuildContext? widgetContext) {
    if (tooltip != null && tooltip!.isOpen) {
      tooltip!.close();
      return;
    }
    tooltip = SuperTooltip(
      popupDirection: TooltipDirection.down,
      borderRadius: 30.r,
      hasShadow: false,
      arrowBaseWidth: 40.w,
      arrowLength: 15.h,
      borderWidth: 0,
      containsBackgroundOverlay: true,
      outsideBackgroundColor: Color.fromRGBO(0, 0, 0, 0.5),
      dismissOnTapOutside: true,
      content: Material(
          child: Padding(
        padding: EdgeInsets.only(top: 20.h),
        child: Container(
          height: 190.h,
          width: 343.w,
          child: Column(
            children: [
              Container(
                height: 60.h,
                width: 50.w,
                child: SvgPicture.asset(
                  offPeakTooltipIcon,
                  height: 48.w,
                  width: 48.w,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 16.h),
                child: Text(
                  OneAppString.of().evmcOffPeakButtonText,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 8.h),
                child: Container(
                  width: 200.w,
                  child: Text(
                    OneAppString.of().evmcOffPeakTooltipText,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      )),
    );

    tooltip!.show(widgetContext!);
  }

  Widget _toastCustomWidget(String toastMessage) {
    return CommonToast(
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
      showIcon: false,
    );
  }

  Widget _responseToastCustomWidget(String toastMessage, bool isSuccessful) {
    return CommonToast(
      iconColor: _colorUtil.tertiary15,
      iconPath: isSuccessful ? checkIcon : closeIcon,
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
    );
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }
}
