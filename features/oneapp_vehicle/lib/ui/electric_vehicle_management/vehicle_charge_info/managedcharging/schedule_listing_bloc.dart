// Package imports:
import 'package:intl/intl.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/mc/entity/schedule_listing_entity.dart';
import 'package:oneapp_network_implementation/mc/entity/update_schedule_req_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/charge_timer_body_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../../log/vehicle_analytic_event.dart';
import '/local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/mc/entity/update_schedule_req_entity.dart'
    as u;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart'
    as cm;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class MultidayScheduleListingBloc extends BlocBase {
  final api = APIClientConfig.oneAppClient;
  List<TimerChargeInfo>? listingData = [];
  int? _maxScheduleLimit;
  cm.ChargeInfo? chargeInfo;
  String? acquisitionDatetime;
  int timeOut = 150;
  late Function(bool) progressHandlerCallback;

  vehicleInfo.Payload? vehicleItem;

  Stream<List<TimerChargeInfo>?> get scheduleList => _scheduleList.stream;
  final _scheduleList = BehaviorSubject<List<TimerChargeInfo>?>();

  Stream<bool> get createScheduleSuccess => _createScheduleSuccess.stream;
  final _createScheduleSuccess = BehaviorSubject<bool>();

  Stream<String> get lastUpdatedTime => _lastUpdatedTime.stream;
  final _lastUpdatedTime = BehaviorSubject<String>();

  Future<void> init(cm.ChargeInfo? chargeInfoData,
      String? acquisitionDatetimeData, Function(bool) progressHandler) async {
    vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    int startTime = DateTime.now().millisecondsSinceEpoch;
    fetchMultidayScheduleRemoteControlStatusAPI(startTime, '', null, null);

    chargeInfo = chargeInfoData;
    acquisitionDatetime = acquisitionDatetimeData;
    progressHandlerCallback = progressHandler;

    if (acquisitionDatetime != null) {
      _setLastUpdatedDate(acquisitionDatetime);
    }
  }

  int? getMaxSchedulesLimit() {
    return _maxScheduleLimit;
  }

  void _setLastUpdatedDate(String? lastUpdatedDate) {
    if (lastUpdatedDate != null && lastUpdatedDate.isNotEmpty) {
      String lastUpdated = lastUpdatedDate;
      DateTime dateTime =
          DateFormat("yyyy-MM-ddTHH:mm:ssZ").parse(lastUpdated, true).toLocal();
      String updatedTime = "";
      String? todayOrYesterday = checkDateIsTodayOrYesterday(dateTime);
      if (todayOrYesterday != null) {
        updatedTime = todayOrYesterday +
            convertDateTimeFormat(
                lastUpdated, "yyyy-MM-ddTHH:mm:ssZ", " @ h:mma");
      } else {
        updatedTime = convertDateTimeFormat(
            lastUpdated, "yyyy-MM-ddTHH:mm:ssZ", "MMM dd @ h:mma");
      }
      updatedTime = updatedTime
          .replaceAll('@', OneAppString.of().at)
          .replaceAll("AM", "am")
          .replaceAll("PM", "pm");
      _lastUpdatedTime.sink.add(OneAppString.of().updated(updatedTime));
    }
  }

  Future<void> fetchMultidayScheduleRemoteControlStatusAPI(
      int startTime,
      String requestNo,
      Function? savedSuccessfullyBottomSheet,
      Function? reloadPage) async {
    final commonResponse = await api.fetchMultidayScheduleRemoteControlStatus(
        vehicleItem!.brand!,
        vehicleItem?.generation ?? "",
        Global.getInstance().vin!,
        requestNo);

    ScheduleListingEntity? scheduleListResponse = commonResponse.response;

    if (scheduleListResponse?.payload != null) {
      if (scheduleListResponse!.payload!.remoteControlResult != null) {
        if (scheduleListResponse.payload!.remoteControlResult!.result == 0 &&
            scheduleListResponse.payload!.remoteControlResult!.status == 0) {
          if (reloadPage != null) {
            progressHandlerCallback(false);
            reloadPage(true);
          } else {
            if (scheduleListResponse.payload!.vehicleInfo!.timerChargeInfo !=
                null) {
              listingData =
                  scheduleListResponse.payload!.vehicleInfo!.timerChargeInfo;
              _maxScheduleLimit = scheduleListResponse
                  .payload!.vehicleInfo!.maxNoOfChargeSchedules as int?;
              _scheduleList.sink.add(listingData);
            } else {
              _scheduleList.sink.add([]);
            }
          }
        } else {
          int currentTime = DateTime.now().millisecondsSinceEpoch;
          double timeElapsed = (currentTime - startTime) / 1000;
          if (timeElapsed >= timeOut) {
            _scheduleList.sink.add([]);
          } else {
            Future.delayed(Duration(seconds: 3), () async {
              if (reloadPage != null) {
                fetchMultidayScheduleRemoteControlStatusAPI(startTime,
                    requestNo, savedSuccessfullyBottomSheet, reloadPage);
              } else {
                fetchMultidayScheduleRemoteControlStatusAPI(
                    startTime, requestNo, null, null);
              }
            });
          }
        }
      } else if (scheduleListResponse.payload!.vehicleInfo!.timerChargeInfo !=
          null) {
        listingData =
            scheduleListResponse.payload!.vehicleInfo!.timerChargeInfo;
        _maxScheduleLimit = scheduleListResponse
            .payload!.vehicleInfo!.maxNoOfChargeSchedules as int?;
        _scheduleList.sink.add(listingData);
      } else {
        _scheduleList.sink.add([]);
      }
    } else {
      _scheduleList.sink.add([]);
    }
  }

  // Confirm new time change
  Future<void> startImmediateChargingAPI(
      Function savedSuccessfullyBottomSheet, Function reloadPage) async {
    String vin = Global.getInstance().vin ?? "";
    ChargeTimerBodyHelper chargeTimerBodyHelper =
        constructImmediateChargeBody();
    progressHandlerCallback(true);

    final commonResponse = await api.postVehicleChargeTimerRequest(
        vehicleItem!.brand!,
        vin,
        vehicleItem?.generation ?? "",
        Global.getInstance().fcmDeviceId!,
        chargeTimerBodyHelper);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      String requestNo = commonResponse.response!.payload!.appRequestNo!;
      int startTime = DateTime.now().millisecondsSinceEpoch;
      fetchMultidayScheduleRemoteControlStatusAPI(
          startTime, requestNo, savedSuccessfullyBottomSheet, reloadPage);
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_CHARGE_TIMER_SETTING_SUCCESS,
          category: LogCategory.FL_VEHI);
    }

    if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(savedSuccessfullyBottomSheet);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_CHARGE_TIMER_SETTING_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  Future<void> refreshScheduleData(
      Function savedSuccessfullyBottomSheet, Function reloadPage) async {
    //Refreshing the schedule data using Realtime Status API

    String vin = (Global.getInstance().vin != null &&
            Global.getInstance().vin!.isNotEmpty)
        ? Global.getInstance().vin!
        : "";

    String? deviceId = Global.getInstance().fcmDeviceId;
    _scheduleList.sink.add(null);
    if (vehicleItem != null) {
      int startTime = DateTime.now().millisecondsSinceEpoch;
      final postResponse = await api.postRealTimeStatusRequest(vin,
          vehicleItem?.brand ?? "", vehicleItem?.generation ?? "", deviceId!);
      final payLoad = postResponse.response?.payload;
      if (payLoad != null) {
        if (payLoad.returnCode == "ONE-RES-10000") {
          fetchFromRealtimeStatusAPI(
            startTime,
            "",
            savedSuccessfullyBottomSheet,
            reloadPage,
          );
        } else {
          fetchFromRealtimeStatusAPI(startTime, payLoad.appRequestNo!,
              savedSuccessfullyBottomSheet, reloadPage);
        }
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_SUCCESS,
            category: LogCategory.FL_VEHI);
      }

      if (postResponse.error != null) {
        progressHandlerCallback(false);
        FireBaseAnalyticsLogger.logError(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  Future<void> fetchFromRealtimeStatusAPI(int startTime, String appRequestNo,
      Function savedSuccessfullyBottomSheet, Function reloadPage) async {
    String vin = Global.getInstance().vin ?? "";
    final commonResponse = await api.fetchClimateRealTimeStatus(
        vehicleItem!.generation ?? "",
        vin,
        appRequestNo,
        vehicleItem?.brand ?? Global.getInstance().appBrand);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      int? status = payLoad.realtimeStatusResult?.status;
      int? result = payLoad.realtimeStatusResult?.result;
      if (status != null && status == 0) {
        if (result != null) {
          if (result == 0) {
            if (payLoad.vehicleInfo!.chargeInfo != null) {
              listingData = payLoad.vehicleInfo!.timerChargeInfo;
              _scheduleList.sink.add(listingData);
            } else {
              _scheduleList.sink.add([]);
            }
            acquisitionDatetime = payLoad.vehicleInfo!.acquisitionDatetime;
            _setLastUpdatedDate(acquisitionDatetime);
            // reloadPage(true);
          } else if (result == 1) {
            _scheduleList.sink.add([]);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 2) {
            _scheduleList.sink.add([]);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 3) {
            _scheduleList.sink.add([]);
            errorCallBack(savedSuccessfullyBottomSheet);
          }
        } else {
          errorCallBack(savedSuccessfullyBottomSheet);

          _scheduleList.sink.add([]);
        }
      } else {
        int currentTime = DateTime.now().millisecondsSinceEpoch;
        double timeElapsed = (currentTime - startTime) / 1000;
        if (timeElapsed >= timeOut) {
          _scheduleList.sink.add([]);
          errorCallBack(savedSuccessfullyBottomSheet);
        } else {
          fetchFromRealtimeStatusAPI(startTime, appRequestNo,
              savedSuccessfullyBottomSheet, reloadPage);
        }
      }
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
  }

  Future<void> updateScheduleAPI(bool updatedVal, TimerChargeInfo scheduleObj,
      Function errorCallBack) async {
    _scheduleList.sink.add(null);

    UpdateScheduleRequestEntity reqBodyObj;
    if (scheduleObj.endTime != null) {
      reqBodyObj = UpdateScheduleRequestEntity(
        settingId: scheduleObj.settingId as int?,
        enabled: updatedVal,
        daysOfTheWeek: scheduleObj.daysOfTheWeek,
        startTime: u.StartTime(
          hour: int.parse(scheduleObj.startTime!.split(':').first),
          minute: int.parse(scheduleObj.startTime!.split(':').last),
        ),
        endTime: u.StartTime(
          hour: int.parse(scheduleObj.endTime!.split(':').first),
          minute: int.parse(scheduleObj.endTime!.split(':').last),
        ),
      );
    } else {
      reqBodyObj = UpdateScheduleRequestEntity(
        settingId: scheduleObj.settingId as int?,
        enabled: updatedVal,
        daysOfTheWeek: scheduleObj.daysOfTheWeek,
        startTime: u.StartTime(
          hour: int.parse(scheduleObj.startTime!.split(':').first),
          minute: int.parse(scheduleObj.startTime!.split(':').last),
        ),
      );
    }

    final commonResponse = await api.updateMultidayChargeSchedule(
        reqBodyObj,
        vehicleItem!.brand!,
        Global.getInstance().vin!,
        'application/json',
        vehicleItem?.generation ?? "",
        Global.getInstance().fcmDeviceId!);
    // return commonResponse;

    if (commonResponse.response != null) {
      String requestNo = commonResponse.response!.payload!.appRequestNo!;
      int startTime = DateTime.now().millisecondsSinceEpoch;
      fetchMultidayScheduleRemoteControlStatusAPI(
          startTime, requestNo, null, null);
    } else if (commonResponse.error != null) {
      errorCallBack(commonResponse.error!.errorMessage);
    }
  }

  ChargeTimerBodyHelper constructImmediateChargeBody() {
    ChargeTimerBodyHelper chargeTimerBodyHelper = ChargeTimerBodyHelper();

    chargeTimerBodyHelper = ChargeTimerBodyHelper(
        command: IMMEDIATE_CHARGE, remoteHvac: null, reservationCharge: null);

    return chargeTimerBodyHelper;
  }

  void refreshUI(String requestNo) {
    //Refreshing the schedule data using Remote Control API with request number from the previous operation
    _scheduleList.sink.add(null);
    int startTime = DateTime.now().millisecondsSinceEpoch;
    fetchMultidayScheduleRemoteControlStatusAPI(
        startTime, requestNo, null, null);
  }

  void errorCallBack(Function savedSuccessfullyBottomSheet) {
    Future.delayed(const Duration(milliseconds: 500), () {
      savedSuccessfullyBottomSheet();
    });
  }

  @override
  void dispose() {
    _scheduleList.close();
    _createScheduleSuccess.close();
    _lastUpdatedTime.close();
  }
}
