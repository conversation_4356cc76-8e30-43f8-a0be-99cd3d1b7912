// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_wallet_and_enrollment_validation.dart';

// Project imports:
import '../../../../log/vehicle_analytic_event.dart';
import '../../../vehicle_finance/vehicle_finance_page.dart';
import 'vehicle_charge_info_station_partner_bloc.dart';

class StationPartnersListCard extends StatelessWidget {
  StationPartnersListCard(
      {this.partnerEnrolledData,
      Key? key,
      this.onRegisterCallBackFunction,
      this.bloc})
      : super(key: key);
  final Function? onRegisterCallBackFunction;
  final PartnerStatus? partnerEnrolledData;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final VehicleChargeInfoStationPartnerBloc? bloc;

  _showSetUpWalletScreen<bool>(context) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(
                child: VehicleFinancePage(
              fromChargeStation: true,
              showPassDueNotification: (showNotification) {},
            )),
          ],
        ),
      ),
    ).then((_) => bloc!.updatePartnersList());
  }

  @override
  Widget build(BuildContext buildContext) {
    final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
    final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
    return Container(
      child: Padding(
        padding: EdgeInsets.all(8.h),
        child: Card(
          color: _colorUtil.tile02,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Container(
            height: 60.h,
            alignment: Alignment.center,
            child: ListTile(
              title: Text(
                partnerEnrolledData!.partnerName,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body4, _colorUtil.tertiary00),
              ),
              trailing: AbsorbPointer(
                absorbing: partnerEnrolledData?.isEnrolled ?? false,
                child: GestureDetector(
                  onTap: () {
                    if (partnerEnrolledData?.status ==
                        OneAppString.of().setupWallet) {
                      FireBaseAnalyticsLogger.logMarketingGroupEvent(
                        VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                        childEventName:
                            VehicleAnalyticsEvent.VEHICLE_EV_WALLET_SET_UP,
                      );
                      _showSetUpWalletScreen(buildContext);
                    } else {
                      onRegisterCallBackFunction!(
                          partnerEnrolledData?.partnerName);
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: _colorUtil.button05b,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.only(
                          top: 6.h, bottom: 8.h, left: 16.w, right: 16.w),
                      child: Text(
                        partnerEnrolledData!.status,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1, _colorUtil.tertiary00),
                        // textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          // elevation: 0.5,
          // shadowColor: _colorUtil.tertiary00,
        ),
      ),
    );
  }
}
