// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/dialog/bottom_error_conformation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';
import '../../../vehicle_account/vehicle_account_page.dart';
import '../../ev_common/ev_enum_const_util.dart';
import '../../vehicle_search_charge_station_location/LegalTermsAndCondition/ev_Legal_Terms_and_Condition_page.dart';
import '../../vehicle_search_charge_station_location/ev_driver_account_enrollment/ev_driver_account_enrollment_page.dart';
import 'station_partners_list_card.dart';
import 'vehicle_charge_info_station_partner_bloc.dart';

class VehicleChargeInfoStationPartnerPage extends StatefulWidget {
  const VehicleChargeInfoStationPartnerPage({
    Key? key,
    required this.isWalletFeatureEnabled,
  }) : super(key: key);
  final bool isWalletFeatureEnabled;

  @override
  _VehicleChargeInfoStationPartnerPageState createState() =>
      _VehicleChargeInfoStationPartnerPageState();
}

class _VehicleChargeInfoStationPartnerPageState
    extends State<VehicleChargeInfoStationPartnerPage> {
  final _partnerBloc = VehicleChargeInfoStationPartnerBloc();
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;

  @override
  void initState() {
    _partnerBloc.init(widget.isWalletFeatureEnabled);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            child: StreamBuilder(
              stream: _partnerBloc.listOfPartners,
              builder: (ctx, AsyncSnapshot snapshot) {
                return snapshot.hasData
                    ? Column(
                        children: [
                          ListView.builder(
                            shrinkWrap: true,
                            itemCount: snapshot.data.length,
                            itemBuilder: (BuildContext context, int index) =>
                                StationPartnersListCard(
                              partnerEnrolledData: snapshot.data[index],
                              onRegisterCallBackFunction:
                                  _showAcceptTermsAndConditionScreen,
                              bloc: _partnerBloc,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(
                                top: 20.h, right: 10.w, left: 10.w),
                            child: Container(
                                child: Text(
                              OneAppString.of().evgoTariffMessage,
                              textAlign: TextAlign.center,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout2,
                                  _colorUtil.tertiary00),
                            )),
                          ),
                        ],
                      )
                    : Padding(
                        padding:
                            EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h),
                        child: Column(
                          children: [
                            shimmerRectangle(
                                80.h, double.maxFinite, CARD_RADIUS_SMALL),
                            Padding(
                              padding: EdgeInsets.only(top: 15.h),
                              child: shimmerRectangle(
                                  80.h, double.maxFinite, CARD_RADIUS_SMALL),
                            ),
                          ],
                        ),
                      );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _partnerBloc.dispose();
    super.dispose();
  }

  void onRegisterFunction(String? partnerName) {
    showModalBottomSheet(
      context: context,
      isDismissible: true,
      barrierColor: Colors.transparent,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
          child: Container(
        height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_60,
        child: OneAppScaffold(
          resizeToAvoidBottomInset: true,
          body: Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: EVDriverAccountEnrollmentPage(
              continueCallback: continueCallBack,
              partnerName: partnerName,
              screenAction: screenActionCallBack,
            ),
          ),
        ),
      )),
    ).then((_) => _partnerBloc.updatePartnersList());
  }

  _showAcceptTermsAndConditionScreen<bool>(String partnerName) {
    return showModalBottomSheet(
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(
                child: EvNewTermsAndConditionPage(
              acceptTermsCallBackFunction: acceptTermsCallBackFunction,
              partnerName: partnerName,
            )),
          ],
        ),
      ),
    );
  }

  void acceptTermsCallBackFunction({String? partnerName}) {
    Future.delayed(Duration(milliseconds: 100), () {
      onRegisterFunction(partnerName);
    });
  }

  void continueCallBack() {
    VehicleRepo().fetchIsEvPublicChargingEnabled().then(
          (isPublicChargingEnabled) => NavigateService.pushNamedRoute(
            RoutePath.VEHICLE_SEARCH_CHARGE_STATION_LOCATION,
            arguments: isPublicChargingEnabled,
          ).then(
            (_) => _partnerBloc.updatePartnersList(),
          ),
        );
  }

  void screenActionCallBack(EV_SCREENS val, {String? errorMessage}) {
    if (val == EV_SCREENS.UPDATE_PROFILE) {
      BottomErrorConfirmationDialog().showBottomDialog(
          context,
          chargeAlertIcon,
          OneAppString.of().updateProfileText,
          OneAppString.of().updateProfileErrorMessage(errorMessage!),
          OneAppString.of().updateProfileButtonText,
          OneAppString.of().cancel,
          _updateProfileClickListener);
    }
  }

  // redirect to contact support
  void _updateProfileClickListener() {
    _showVehicleAccountPage();
  }

  _showVehicleAccountPage<bool>() {
    return showMaterialModalBottomSheet(
      expand: true,
      context: context,
      isDismissible: true,
      useRootNavigator: true,
      backgroundColor: _colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(child: VehicleAccountPage()),
          ],
        ),
      ),
    );
  }
}
