// Package imports:

// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_network/entity/common_response.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:oneapp_network_implementation/ev/entity/ev_wallet_and_enrollment_validation.dart';
import 'package:oneapp_network_implementation/ev/ev_api_client.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';
import '../../vehicle_search_charge_station_location/vehicle_search_charge_station_location_util.dart';

class VehicleChargeInfoStationPartnerBloc extends BlocBase {
  final EVApiClient evApi = APIClientConfig.evApiClient;

  Stream<List<PartnerStatus>> get listOfPartners => _listOfPartners.stream;
  final _listOfPartners = BehaviorSubject<List<PartnerStatus>>();

  Stream<bool> get isWalletFeatureEnabled => _isWalletFeatureEnabled.stream;
  final _isWalletFeatureEnabled = BehaviorSubject<bool>();

  final partnerNameValues =
      EnumValues({"chargepoint": "ChargePoint", "evgo": "EVgo"});

  Future<void> updatePartnersList() async {
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();

    final CommonResponse<EvWalletAndEnrollmentValidation> commonResponse =
        await evApi.checkWalletAndDriverExistence(
      email: Global.getInstance().userEmail ?? '',
      guid: Global.getInstance().guid ?? '',
      make: vehicleInfo?.make ?? '',
    );
    PartnerEnrollment partnerEnrollment =
        commonResponse.response!.payload.partnerEnrollment;
    Global.getInstance().evgoFreeExpireDate =
        partnerEnrollment.evgoFreeExpirationDate();

    var partnersListData = partnerEnrollment.partnerStatus.map((eachPartner) {
      eachPartner.isEnrolled = eachPartner.status.toUpperCase() == 'FOUND';
      eachPartner.status =
          VehicleSearchChargeStationLocationUtil.partnerEnrollmentStatus(
              partners: eachPartner, wallet: partnerEnrollment.wallet);
      eachPartner.partnerName =
          partnerNameValues.map[eachPartner.partnerName] ?? '';
      return eachPartner;
    }).toList();
    _listOfPartners.sink.add(partnersListData);
    _listOfPartners.value = partnersListData;
  }

  void init(bool isWalletFeatureEnabled) {
    _isWalletFeatureEnabled.sink.add(isWalletFeatureEnabled);
    updatePartnersList();
  }

  @override
  void dispose() {
    _listOfPartners.close();
    _isWalletFeatureEnabled.close();
  }

// if the wallet is turned off, treat chargepoint like they are not a partner
// .where((p) => _isWalletFeatureEnabled.value
//     ? true
//     : p.partnerName != EV_PARTNERS.CHARGE_POINT)
}
