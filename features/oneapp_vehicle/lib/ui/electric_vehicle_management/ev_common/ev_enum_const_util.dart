enum EV_SCREENS {
  ENROLLMENT_SCREEN,
  TAR<PERSON><PERSON>_SCREEN,
  CHARGING_SCREEN,
  SETUP_WALLET,
  TERMS_AND_CONDITION,
  UPDATE_PROFILE,
}

enum ENROLLMENT_SCREENS {
  ACCOUNT_LINKED,
  ACCOUNT_CREATED,
  EVGO_UPDATE_PROFILE,
  CHARGEPOINT_UPDATE_PROFILE,
  CONTINUE_CALL_BACK,
  EVGO_ERROR
}

enum EnrollmentAccountState {
  ACCOUNT_NOT_FOUND,
  ACCOUNT_FOUND,
  CHARGEPOINT_ACCOUNT_LINKED,
  LOGIN,
  REGISTER,
  ERROR
}

class EV_PARTNERS {
  static String get CHARGE_POINT => 'chargepoint';
  static String get EVGO => 'evgo';
}

// for displaying charge now and find station buttin on dashboard
enum CHARGING_EV_SCREEN { CHARGING_SCREEN, NEARBY_STATION }

///  "type": "integer",
///  "format": "int32",
///  "example": 12,
///  "description": "Charge and Power Supply status.
///  [0：Undefined, 12:Parked, 24:Running, 32:Power source judgment in progress,
///  36:Waiting for timer, 40:Charging, 45:Stop charging,
///  56:During fast charging, 60:Fast charge stop, 49:In V2H mode,
///  64:Preparing for power supply,
///  72:During power supply.（EV mode）, 73:During power supply.（HV mode）,
///  76:Power supply stop, (Invalid value:-1)]"
enum ChargeInfoPlugStatus {
  UNDEFINED,
  PARKED,
  RUNNING,
  POWER_SOURCE_JUDGMENT_IN_PROGRESS,
  WAITING_FOR_TIMER,
  CHARGING,
  STOP_CHARGING,
  DURING_FAST_CHARGING,
  FAST_CHARGE_STOP,
  IN_V2H_MODE,
  PREPARING_FOR_POWER_SUPPLY,
  DURING_EV_MODE_POWER_SUPPLY,
  DURING_HV_MODE_POWER_SUPPLY,
  POWER_SUPPLY_STOP,
  IN_VALID_VALUE
}

enum ChargeInfoConnectorStatus {
  UNDEFINED,
  DISCONNECTED,
  AC_CHARGING_CONNECTOR_IS_CONNECTED,
  AC_CHARGING_CONNECTOR_IS_CONNECTED_AND_LOCKED,
  VPC,
  VPC_AND_LOCKED,
  DC_CHARGING_CONNECTOR_IS_CONNECTED,
  IN_VALID_VALUE
}

extension ChargeInfoPlugStatusExtension on ChargeInfoPlugStatus {
  int get value {
    switch (this) {
      case ChargeInfoPlugStatus.UNDEFINED:
        return 0;
      case ChargeInfoPlugStatus.PARKED:
        return 12;
      case ChargeInfoPlugStatus.RUNNING:
        return 24;
      case ChargeInfoPlugStatus.POWER_SOURCE_JUDGMENT_IN_PROGRESS:
        return 32;
      case ChargeInfoPlugStatus.WAITING_FOR_TIMER:
        return 36;
      case ChargeInfoPlugStatus.CHARGING:
        return 40;
      case ChargeInfoPlugStatus.STOP_CHARGING:
        return 45;
      case ChargeInfoPlugStatus.DURING_FAST_CHARGING:
        return 56;
      case ChargeInfoPlugStatus.FAST_CHARGE_STOP:
        return 60;
      case ChargeInfoPlugStatus.IN_V2H_MODE:
        return 49;
      case ChargeInfoPlugStatus.PREPARING_FOR_POWER_SUPPLY:
        return 64;
      case ChargeInfoPlugStatus.DURING_EV_MODE_POWER_SUPPLY:
        return 72;
      case ChargeInfoPlugStatus.DURING_HV_MODE_POWER_SUPPLY:
        return 73;
      case ChargeInfoPlugStatus.POWER_SUPPLY_STOP:
        return 76;
      case ChargeInfoPlugStatus.IN_VALID_VALUE:
      default:
        return -1;
    }
  }
}

extension ChargeInfoConnectorStatusExtension on ChargeInfoConnectorStatus {
  int get value {
    switch (this) {
      case ChargeInfoConnectorStatus.UNDEFINED:
        return 0;
      case ChargeInfoConnectorStatus.DISCONNECTED:
        return 2;
      case ChargeInfoConnectorStatus.AC_CHARGING_CONNECTOR_IS_CONNECTED:
        return 4;
      case ChargeInfoConnectorStatus
            .AC_CHARGING_CONNECTOR_IS_CONNECTED_AND_LOCKED:
        return 5;
      case ChargeInfoConnectorStatus.VPC:
        return 6;
      case ChargeInfoConnectorStatus.VPC_AND_LOCKED:
        return 7;
      case ChargeInfoConnectorStatus.DC_CHARGING_CONNECTOR_IS_CONNECTED:
        return 8;
      case ChargeInfoConnectorStatus.IN_VALID_VALUE:
      default:
        return -1;
    }
  }
}
