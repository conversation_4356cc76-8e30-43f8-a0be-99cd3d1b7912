// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/format_util.dart';

class DefaultButtonWithTimer extends StatefulWidget {
  DefaultButtonWithTimer({
    Key? key,
    this.backgroundColor = Colors.black,
    this.buttonTextColor = Colors.white,
    this.text = "Button Text",
    this.textStyle,
    required this.press,
    this.primaryButtonState = PrimaryButtonState.ACTIVE,
    this.disabledBackgroundColor = Colors.grey,
    this.disabledButtonTextColor = Colors.white,
    this.borderColor = Colors.grey,
    this.horizontalPadding = 16,
    this.verticalPadding = 16,
    this.showProgressOnDisabled = false,
    this.disableOnClick = false,
    this.enableButtonAfter = Duration.zero,
  }) : super(key: key);
  final String text;
  final TextStyle? textStyle;
  final Function press;
  final Color backgroundColor;
  final Color buttonTextColor;
  final Color disabledBackgroundColor;
  final Color disabledButtonTextColor;
  final Color borderColor;
  final double horizontalPadding;
  final double verticalPadding;
  final PrimaryButtonState primaryButtonState;
  final bool showProgressOnDisabled;
  final bool disableOnClick;
  final Duration enableButtonAfter;

  @override
  _DefaultButtonWithTimer createState() => _DefaultButtonWithTimer();
}

class _DefaultButtonWithTimer extends State<DefaultButtonWithTimer> {
  PrimaryButtonState _primaryButtonState = PrimaryButtonState.ACTIVE;
  Color _newBackgroundColor = Color.fromRGBO(40, 40, 48, 1);
  late Color _newButtonTextColor;

  final textStyleUtil = ThemeConfig.current().textStyleUtil;
  final colorUtil = ThemeConfig.current().colorUtil;

  @override
  void initState() {
    super.initState();
    _primaryButtonState = widget.primaryButtonState;
  }

  @override
  Widget build(BuildContext context) {
    final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
    if (_primaryButtonState == PrimaryButtonState.ACTIVE) {
      _newBackgroundColor = widget.backgroundColor;
      _newButtonTextColor = widget.buttonTextColor;
    } else {
      _newBackgroundColor = widget.disabledBackgroundColor;
      _newButtonTextColor = widget.disabledButtonTextColor;
    }
    return SizedBox(
      child: TextButton(
        onPressed: () {
          if (_primaryButtonState == PrimaryButtonState.ACTIVE) {
            widget.press.call();
          }
          if (widget.disableOnClick) {
            setState(() {
              _primaryButtonState = PrimaryButtonState.INACTIVE;
            });
          }
          if (widget.enableButtonAfter != Duration.zero) {
            Future.delayed(widget.enableButtonAfter, () {
              setState(() {
                _primaryButtonState = PrimaryButtonState.ACTIVE;
              });
            });
          }
        },
        style: TextButton.styleFrom(
            backgroundColor: _newBackgroundColor,
            shape: StadiumBorder(),
            minimumSize: Size(MIN_BUTTON_WIDTH, MIN_BUTTON_HEIGHT),
            padding: EdgeInsets.symmetric(
                horizontal: widget.horizontalPadding,
                vertical: widget.verticalPadding)),
        child: widget.showProgressOnDisabled &&
                _primaryButtonState == PrimaryButtonState.INACTIVE
            ? CircularProgressIndicator()
            : Text(
                formatTextForLexusAndToyota(widget.text),
                style: TextStyleExtension().newStyleWithColor(
                  widget.textStyle ?? _textStyleUtil.buttonLink1,
                  _newButtonTextColor,
                ),
              ),
      ),
    );
  }
}
