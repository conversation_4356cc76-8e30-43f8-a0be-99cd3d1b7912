// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';

class EVAlertPopUpState extends StatefulWidget {
  final String? title;

  final String? description;

  final TextAlign? descriptionTextAlign;

  final String? primaryButtonText;

  final String? additionalButtonText;

  final VoidCallback? primaryButtonPressed;

  final VoidCallback? additionalButtonPressed;

  final bool success;

  final bool? showIcon;

  const EVAlertPopUpState({
    Key? key,
    this.title,
    this.description,
    this.descriptionTextAlign,
    this.primaryButtonText,
    this.additionalButtonText,
    this.primaryButtonPressed,
    this.additionalButtonPressed,
    this.success = true,
    this.showIcon,
  }) : super(key: key);

  @override
  State<EVAlertPopUpState> createState() => _EVAlertPopUpStateState();
}

class _EVAlertPopUpStateState extends State<EVAlertPopUpState> {
  @override
  Widget build(BuildContext context) {
    bool success = widget.success;
    return Padding(
      padding: EdgeInsets.fromLTRB(32.w, 24.h, 32.w, 32.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Visibility(
              visible: widget.showIcon ?? true,
              child: SizedBox(
                width: 48.w,
                height: 48.w,
                child: CommonCircleIconImage(
                  success ? shopInfoIcon : errorIconImage,
                  iconWidth: 24.w,
                  iconHeight: 24.w,
                  iconPadding: EdgeInsets.all(12.w),
                  iconTintColor: success
                      ? ThemeConfig.current().colorUtil.success01
                      : ThemeConfig.current().colorUtil.primary01,
                  circleBackgroundColor: success
                      ? ThemeConfig.current().colorUtil.success02
                      : ThemeConfig.current().colorUtil.primary02,
                ),
              )),
          SizedBox(
            height: 16.h,
          ),
          Text(
            widget.title ?? '',
            style: ThemeConfig.current().textStyleUtil.subHeadline1,
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 8.h,
          ),
          Text(
            widget.description ?? '',
            style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary05,
                ),
            textAlign: widget.descriptionTextAlign ?? TextAlign.center,
          ),
          Spacer(),
          Visibility(
            visible: widget.additionalButtonText != null,
            child: SizedBox(
              width: 192.w,
              child: _transparentButton(
                widget.additionalButtonText ?? '',
                widget.additionalButtonPressed,
              ),
            ),
          ),
          SizedBox(
            height: 4.h,
          ),
          SizedBox(
            width: 192.w,
            height: 52.h,
            child: CustomDefaultButton(
              backgroundColor: ThemeConfig.current().colorUtil.button01b,
              buttonTextColor: ThemeConfig.current().colorUtil.button01a,
              disabledBackgroundColor:
                  ThemeConfig.current().colorUtil.button02c,
              borderColor: ThemeConfig.current().colorUtil.button01b,
              text: widget.primaryButtonText ?? '',
              primaryButtonState: widget.primaryButtonPressed != null
                  ? PrimaryButtonState.ACTIVE
                  : PrimaryButtonState.INACTIVE,
              press: widget.primaryButtonPressed,
              verticalPadding: 4.h,
            ),
          ),
          SizedBox(
            height: 32.h,
          ),
        ],
      ),
    );
  }

  Widget _transparentButton(String text, VoidCallback? onPressed) {
    return TextButton(
      onPressed: onPressed,
      child: Text(
        formatTextForLexusAndToyota(text),
        style: ThemeConfig.current()
            .textStyleUtil
            .buttonLink1
            .copyWith(color: ThemeConfig.current().colorUtil.button02a),
      ),
      style: TextButton.styleFrom(
        foregroundColor: ThemeConfig.current().colorUtil.button05b,
        minimumSize: Size(MIN_BUTTON_WIDTH.w, MIN_BUTTON_HEIGHT.h),
        backgroundColor: Colors.transparent,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(56)),
        ),
        padding: EdgeInsets.symmetric(horizontal: 32, vertical: 4),
      ),
    );
  }
}
