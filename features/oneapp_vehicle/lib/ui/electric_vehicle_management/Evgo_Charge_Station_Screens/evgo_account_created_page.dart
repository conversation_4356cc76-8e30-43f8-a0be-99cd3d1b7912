// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';

class EvGoAccountCreatedPage extends StatefulWidget {
  EvGoAccountCreatedPage({Key? key, required this.findActionCallback})
      : super(key: key);
  final Function findActionCallback;

  @override
  _EvGoAccountCreatedPageState createState() => _EvGoAccountCreatedPageState();
}

final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

class _EvGoAccountCreatedPageState extends State<EvGoAccountCreatedPage> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: CircleAvatar(
              radius: 25.r,
              backgroundColor: _colorUtil.success02,
              child: Icon(
                Icons.check,
                size: 25,
                color: _colorUtil.success01,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              top: 40.h,
            ),
            child: Text(
              OneAppString.of().accountCreated,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 20.h, left: 24.h, right: 24.h),
            child: Text(
              OneAppString.of().evgoAccountCreatedText,
              textAlign: TextAlign.center,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary05),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 120.h, bottom: 10.h),
            child: CustomDefaultButton(
              backgroundColor: _colorUtil.tertiary03,
              text: OneAppString.of().findStations,
              press: () {
                Navigator.of(context).pop();
                widget.findActionCallback();
              },
              buttonTextColor: _colorUtil.tertiary15,
            ),
          )
        ],
      ),
    );
  }
}
