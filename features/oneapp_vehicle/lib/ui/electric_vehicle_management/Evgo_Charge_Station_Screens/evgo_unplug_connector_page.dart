// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';

class EvGoUplugConnectorPage extends StatefulWidget {
  const EvGoUplugConnectorPage({Key? key}) : super(key: key);

  @override
  _EvGoUplugConnectorPageState createState() => _EvGoUplugConnectorPageState();
}

final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
bool plugStatus = true;

class _EvGoUplugConnectorPageState extends State<EvGoUplugConnectorPage> {
  @override
  Widget build(BuildContext context) {
    return OneAppScaffold(
      body: _textLayout(),
    );
  }

  Widget _textLayout() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: Center(
            /// second msg after checking plug status Vehicle is not unplugged, please try again.
            child: Text(
              OneAppString.of().vehicleUnplugPlaceItBackMessage,
              textAlign: TextAlign.center,
              style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.body3,
                _colorUtil.button02a,
              ),
            ),
          ),
        ),
        _buttonLayout()
      ],
    );
  }

  Widget _buttonLayout() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        padding: EdgeInsets.only(bottom: 40.h),
        child: CustomDefaultButton(
          text: OneAppString.of().continueText,
        ),
      ),
    );
  }
}
