// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_network/api_config.dart';

class EvgoForgetPasswordPage extends StatefulWidget {
  const EvgoForgetPasswordPage({Key? key}) : super(key: key);

  @override
  _EvgoForgetPasswordPageState createState() => _EvgoForgetPasswordPageState();
}

final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

class _EvgoForgetPasswordPageState extends State<EvgoForgetPasswordPage> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: CircleAvatar(
              radius: 25.r,
              backgroundColor: _colorUtil.success02,
              child: Icon(
                Icons.check,
                size: 25,
                color: _colorUtil.success01,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 40.h),
            child: Text(
              OneAppString.of().forgetPasswordHeadingText,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 20.h, left: 24.w, right: 24.w),
            child: Text(
              OneAppString.of().forgetPasswordConfirmationText,
              textAlign: TextAlign.center,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary05),
            ),
          ),
          Padding(
              padding: EdgeInsets.only(top: 100.h, bottom: 10.h),
              child: InkWell(
                onTap: () {
                  _callNowOnClickListners();
                },
                child: Text(
                  OneAppString.of().forgetPasswordCallNowButtonText,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.buttonLink1, _colorUtil.tertiary00),
                ),
              )),
          Padding(
            padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
            child: CustomDefaultButton(
              backgroundColor: _colorUtil.tertiary03,
              text: OneAppString.of().forgetPasswordOpenWebsiteButtonText,
              press: () {
                _openWebUrlOnClickListener();
              },
              buttonTextColor: _colorUtil.tertiary15,
            ),
          )
        ],
      ),
    );
  }

//// Open Web Functionality
  void _openWebUrlOnClickListener() async {
    await urlLauncher(APIConfig.evgoForgetPasswordUrl);
  }

//// Call Now Functinality
  void _callNowOnClickListners() async {
    //Tempory Phone Number
    String evgoHelpinenumber = "**************";
    dialerLauncher(evgoHelpinenumber);
  }
}
