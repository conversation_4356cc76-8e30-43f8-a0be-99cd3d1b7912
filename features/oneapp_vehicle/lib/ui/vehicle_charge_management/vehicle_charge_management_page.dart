// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ev_module/charge_info/widgets/helper/range_info.dart';
import 'package:ev_module/charge_info/widgets/hvac_range_widget/hvac_range_widget.dart';
import 'package:ev_module/core/ev_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geocoding/geocoding.dart' as geoCoding;
import 'package:geolocator/geolocator.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/custom_page_route.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/graphics/fuel_electric_graphics.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:super_tooltip/super_tooltip.dart';

// Project imports:
import '../electric_vehicle_management/vehicle_charge_info/clean_assist/vehicle_clean_assist_consent_detail_page.dart';
import '../electric_vehicle_management/vehicle_charge_info/clean_assist/vehicle_clean_assist_graph.dart';
import '../electric_vehicle_management/vehicle_charge_info/managedcharging/evmc_utils/util.dart';
import '../electric_vehicle_management/vehicle_charge_info/managedcharging/schedule_create_detail_page.dart';
import '../electric_vehicle_management/vehicle_charge_info/managedcharging/schedule_listing_page.dart';
import '../electric_vehicle_management/vehicle_charge_info/vehicle_charge_info_stations/vehicle_charge_info_stations_page.dart';
import '/log/vehicle_analytic_event.dart';
import 'start_charging_button.dart';
import 'vehicle_charge_management_bloc.dart';
import 'vehicle_charge_management_eco_history/vehicle_charge_management_eco_history_page.dart';
import 'vehicle_charge_management_timer/vehicle_charge_management_timer_page.dart';

class VehicleChargeManagementPage extends StatefulWidget {
  final geoCoding.Location? customerlocation;
  final Position? devicePosition;
  final bool? shouldStartCharging;
  final bool? isCloseToHome;

  VehicleChargeManagementPage(
      {Key? key,
      this.customerlocation,
      this.devicePosition,
      this.shouldStartCharging,
      this.isCloseToHome})
      : super(key: key);

  @override
  _VehicleChargeManagementPageState createState() =>
      _VehicleChargeManagementPageState();
}

class _VehicleChargeManagementPageState
    extends State<VehicleChargeManagementPage>
    with
        AutomaticKeepAliveClientMixin<VehicleChargeManagementPage>,
        TickerProviderStateMixin {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  late AnimationController _animationController;
  BuildContext? _materialContext;
  VehicleChargeManagementBloc _bloc = VehicleChargeManagementBloc();

  int lowBatteryValue = 30;

  late List<Widget> _tabs;

  int selectedtabIndex = 0;
  bool showTabbedView = false;
  SuperTooltip? tooltip;
  BuildContext? chargeDescWidgetContext;

  @override
  void initState() {
    super.initState();
    _animationController =
        AnimationController(vsync: this, duration: Duration(seconds: 1));
    _animationController.repeat(reverse: true);
    resetProgressState();
    _bloc.init(_progressHandlerCallback);
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_CHARGE_MANAGEMENT_PAGE);
    if (widget.shouldStartCharging != null && widget.shouldStartCharging!) {
      Future.microtask(() => _bloc.startChargeRequest(_savedResponseCallback));
    }
    selectedtabIndex =
        widget.isCloseToHome != null && widget.isCloseToHome! ? 1 : 0;
  }

  @override
  void dispose() {
    _bloc.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    _preprareTabItems();

    return PopScope(
      onPopInvoked: (didPop) async {
        if (didPop) return;
        goBackToNative();
      },
      child: Material(
          child: Navigator(
              onGenerateRoute: (_) =>
                  MaterialPageRoute(builder: (materialContext) {
                    _materialContext = materialContext;
                    return Builder(builder: (builderContext) {
                      return BlocProvider(
                          bloc: _bloc,
                          child: OneAppScaffold(
                              backgroundColor: _colorUtil.tile01,
                              body: SafeArea(
                                child: Stack(
                                  children: [
                                    SingleChildScrollView(
                                      child: Center(
                                        child: StreamBuilder<bool>(
                                            stream: _bloc.isEmptyPage,
                                            builder: (context, snapshot) {
                                              return (snapshot.hasData &&
                                                      snapshot.data!)
                                                  ? _noDataAvailableLayout()
                                                  : Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        _topLayout(),
                                                        _bottomLayout(),
                                                      ],
                                                    );
                                            }),
                                      ),
                                    ),
                                    floatingUIWidget()
                                  ],
                                ),
                              )));
                    });
                  }))),
    );
  }

  Widget floatingUIWidget() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: StreamBuilder(
        initialData: false,
        stream: _bloc.showSchedule,
        builder: (context, snapshot) {
          if (snapshot.data != null && snapshot.data == true) {
            return StreamBuilder<ScheduleType>(
              stream: _bloc.scheduleType,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return snapshot.data == ScheduleType.MULTIDAY
                      ? (selectedtabIndex == 1 || showTabbedView == false)
                          /* Show floating widget when:
                          1. Tabs present - when "Home" tab is selected
                          2. Tabs absent - always */
                          ? floatingGradient(floatingUI())
                          : Container()
                      : Container();
                } else {
                  return Container();
                }
              },
            );
          } else {
            return floatingGradient(floatingUI());
          }
        },
      ),
    );
  }

  Widget _bottomLayout() {
    return StreamBuilder<bool>(
        stream: _bloc.isPublicChargingControlAllowed,
        initialData: false,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data == false) {
            showTabbedView = false;
            return Container(
              height: MediaQuery.of(context).size.height * 0.75,
              child: _childBody(),
            );
          } else {
            showTabbedView = true;
            return DefaultTabController(
                //Default to Home tab if user is within 1000 meters from home; else Stations tab
                initialIndex: selectedtabIndex,
                length: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: TabBar(
                        onTap: (index) {
                          selectedtabIndex = index;
                          setState(() {});
                        },
                        isScrollable: true,
                        indicatorColor: _colorUtil.button05b,
                        automaticIndicatorColorAdjustment: true,
                        unselectedLabelColor: _colorUtil.button02a,
                        labelColor: _colorUtil.button05b,
                        labelStyle: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout2, _colorUtil.button05b),
                        unselectedLabelStyle: TextStyleExtension()
                            .newStyleWithColor(
                                _textStyleUtil.callout2, _colorUtil.button02a),
                        indicator: BoxDecoration(
                            borderRadius:
                                BorderRadius.circular(50), // Creates border
                            color: _colorUtil.tertiary00),
                        //Change background color from here
                        tabs: [
                          Tab(
                            child: Text(OneAppString.of().stationsHeading),
                          ),
                          Tab(
                            child: Text(OneAppString.of().home),
                          ),
                        ],
                      ),
                    ),
                    _tabs[selectedtabIndex],
                  ],
                ));
          }
        });
  }

  Widget floatingGradient(Widget child) {
    return Container(
      decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: _colorUtil.tile02.withOpacity(0.5),
              spreadRadius: 6,
              blurRadius: 7,
              offset: Offset(0.0, -15.0),
            ),
          ],
          gradient: LinearGradient(begin: Alignment.bottomRight, stops: [
            0.5,
            0.8
          ], colors: [
            _colorUtil.tile02.withOpacity(1),
            _colorUtil.tile02.withOpacity(0.5)
          ])),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [child],
      ),
    );
  }

  Widget floatingUI() {
    /* Floating UI implemented for Home Tab for following scenarios
      - If plugged in, show Start Charging
      - If vehicle is multiday schedule capable, show Create Schedule/Max Limit Reached
    */
    return StreamBuilder<bool>(
      stream: _bloc.showStartCharge,
      builder: (context, snapshot) {
        return (snapshot.hasData && snapshot.data == true)
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  StartChargingButton(
                    onTap: () {
                      _bloc.startChargeRequest(_savedResponseCallback);
                    },
                  )
                ],
              )
            : StreamBuilder<bool>(
                stream: _bloc.showSchedule,
                builder: (context, snapshot) {
                  if (snapshot.hasData && snapshot.data == true) {
                    return StreamBuilder<ScheduleType>(
                      stream: _bloc.scheduleType,
                      builder: (context, snapshot) {
                        if (snapshot.hasData &&
                            snapshot.data == ScheduleType.MULTIDAY) {
                          return StreamBuilder<bool>(
                            stream: _bloc.scheduleLimitReached,
                            builder: (context, snapshot) {
                              return (snapshot.hasData && snapshot.data == true)
                                  ? maxSchedulesReachedWidget()
                                  : Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        createScheduleButton(OneAppString.of()
                                            .evmcCreateScheduleButtonText)
                                      ],
                                    );
                            },
                          );
                        }
                        return Container();
                      },
                    );
                  }
                  return Container();
                },
              );
      },
    );
  }

  Widget _stationInfo() {
    return StreamBuilder<bool>(
        stream: _bloc.isWalletFeatureEnabled,
        initialData: true,
        builder: (context, snapshot) {
          return DefaultTabController(
            length: snapshot.data! ? 3 : 2,
            child: Column(
              children: [
                VehicleChargeInfoStationsPage(
                  isWalletFeatureEnabled: snapshot.data,
                ),
              ],
            ),
          );
        });
  }

  List<Widget> _preprareTabItems() {
    // views to show on each tab
    // remove height once you add your contents
    return _tabs = <Widget>[
      Container(
        child: _stationInfo(),
      ),
      Container(
        child: _childBody(),
      ),
    ];
  }

  // Empty layout if data is null
  Widget _noDataAvailableLayout() {
    return Container(
      width: double.maxFinite,
      margin: EdgeInsets.only(top: 50.h),
      child: Center(
        child: Text(
          OneAppString.of().dataNotAvailableFor(OneAppString.of().chargeInfo),
          textAlign: TextAlign.center,
          style: TextStyleExtension().newStyleWithColor(
              _textStyleUtil.subHeadline3, _colorUtil.tertiary07),
        ),
      ),
    );
  }

  Widget _topLayout() {
    return Container(
      child: Column(
        children: [
          Container(
            height: 60.h,
            child: bottomSheetCustomAppBar(
              OneAppString.of().chargeInfo,
              onBackPressed: () {
                goBackToNative();
              },
              elevation: 0,
              actionWidget: ecoHistoryWidget(),
            ),
          ),
          StreamBuilder<ChargeInfo?>(
              stream: _bloc.chargeInfoValue,
              builder: (context, snapshot) {
                return snapshot.hasData ? _buildLayout() : Container();
              }),
        ],
      ),
    );
  }

  Widget _buildLayout() {
    // bool isPluggedIn = (_chargePlugStatus == ChargePlugStatus.PLUG_LOCKED ||
    //     _chargePlugStatus == ChargePlugStatus.PLUG_UNLOCKED);

    // isPluggedIn = true;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 15.h, bottom: 6.h),
          child: _chargePercentCommonLayout(),
        ),
        _chargeMeterCommonLayout(),
        Padding(
          padding: EdgeInsets.only(top: 10.h),
          child: Container(
            width: 311.w,
            alignment: Alignment.center,
            child: StreamBuilder<bool>(
              stream: _bloc.isChargingOrWaiting,
              builder: (context, snapshot) {
                return (snapshot.hasData &&
                        snapshot
                            .data!) //Show label when it is either Charging or Charge Complete
                    ? _chargeStatusPlugInLayout()
                    : _bloc.isEV
                        ? _hvacRangeLayout()
                        : _distanceToEmptyLayout();
              },
            ),
          ),
        ),
        _bloc.isEV ? Container() : _chargeDescriptionLayout()
      ],
    );
  }

  // Common layout for percentage
  Widget _chargePercentCommonLayout() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        StreamBuilder<bool>(
            stream: _bloc.isCharging,
            builder: (context, snapshot) {
              return (snapshot.hasData && snapshot.data!)
                  ? _electricIconWithAnimation()
                  : _electricIconWithoutAnimation();
            }),
        Padding(
          padding: EdgeInsets.only(left: 10.w),
          child: StreamBuilder<int>(
              stream: _bloc.chargeRemaining,
              builder: (context, snapshot) {
                return RichText(
                    text: TextSpan(children: <TextSpan>[
                  TextSpan(
                      text: snapshot.hasData ? snapshot.data.toString() : "0",
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.title2, _colorUtil.tertiary00)),
                  TextSpan(
                      text: "%",
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.subHeadline3, _colorUtil.tertiary00)),
                ]));
              }),
        )
      ],
    );
  }

  // Animate electric icon
  Widget _electricIconWithAnimation() {
    return StreamBuilder<int>(
        stream: _bloc.chargeRemaining,
        builder: (context, snapshot) {
          int chargeRemaining = snapshot.hasData ? snapshot.data! : 0;
          return FadeTransition(
              opacity: _animationController,
              child: SvgPicture.asset(
                electricStationIcon,
                height: 25.33.w,
                width: 43.43.w,
                colorFilter: ColorFilter.mode(
                  _bloc.isEV
                      ? _colorUtil.tertiary00
                      : chargeRemaining <= lowBatteryValue
                          ? _colorUtil.primary01
                          : _colorUtil.secondary01,
                  BlendMode.srcIn,
                ),
                allowDrawingOutsideViewBox: false,
              ));
        });
  }

  //  Electric icon without animation
  Widget _electricIconWithoutAnimation() {
    return StreamBuilder<int>(
        stream: _bloc.chargeRemaining,
        builder: (context, snapshot) {
          int chargeRemaining = snapshot.hasData ? snapshot.data! : 0;
          return SvgPicture.asset(
            electricStationIcon,
            height: 25.33.h,
            width: 43.43.w,
            colorFilter: ColorFilter.mode(
              _bloc.isEV
                  ? _colorUtil.tertiary00
                  : chargeRemaining <= lowBatteryValue
                      ? _colorUtil.primary01
                      : _colorUtil.secondary01,
              BlendMode.srcIn,
            ),
            allowDrawingOutsideViewBox: false,
          );
        });
  }

  //Common charging meter layout (plugin/plugout)
  Widget _chargeMeterCommonLayout() {
    return Container(
      width: 168.w,
      child: StreamBuilder<int>(
          stream: _bloc.chargeRemaining,
          builder: (context, snapshot) {
            int chargeRemaining = snapshot.hasData ? snapshot.data! : 0;
            return Semantics(
              container: true,
              child: FuelElectricOverviewGraphics(
                  animatedDuration: Duration(seconds: 1),
                  currentValueInPercent: chargeRemaining,
                  size: 8.h,
                  borderRadius: 3.h,
                  backgroundColor: _colorUtil.tertiary10,
                  borderColor: _colorUtil.tertiary10,
                  isGradient: false,
                  progressColor: _bloc.isEV
                      ? EvUtil.getColor(chargeRemaining, false)
                      : chargeRemaining <= lowBatteryValue
                          ? _colorUtil.primary01
                          : _colorUtil.secondary01),
            );
          }),
    );
  }

  // Description - Common for both - Time remaining to complete charge or distance left with ac on
  Widget _chargeDescriptionLayout() {
    return StreamBuilder<String>(
      stream: _bloc.chargeDescription,
      builder: (context, snapshot) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              snapshot.hasData ? snapshot.data! : "",
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary05),
            ),
          ],
        );
      },
    );
  }

  // Charge stats - Charging - Plugged
  Widget _chargeStatusPlugInLayout() {
    return StreamBuilder<bool>(
        stream: _bloc.isBatteryLow,
        builder: (context, snapshot) {
          final isBatteryLow = snapshot.data ?? false;
          return Container(
              height: 34.h,
              alignment: Alignment.center,
              child: Chip(
                backgroundColor: isBatteryLow
                    ? _colorUtil.primary02
                    : _colorUtil.secondary02,
                label: StreamBuilder<String>(
                    stream: _bloc.chargeStatusBadgeText,
                    builder: (context, snapshot) {
                      return Text(
                        snapshot.data ?? "",
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1,
                            isBatteryLow
                                ? _colorUtil.primary01
                                : _colorUtil.button03b),
                      );
                    }),
              ));
        });
  }

  Widget _refreshIconLayout() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        children: [
          InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              _bloc.refreshLastUpdatedData(
                  _savedResponseCallback, _reloadChargeManagementPage, true);
            },
            child: CircleAvatar(
              radius: 24.0.r,
              backgroundColor: _colorUtil.button02d,
              child: SvgPicture.asset(
                refreshIcon,
                colorFilter: ColorFilter.mode(
                  _colorUtil.button02a,
                  BlendMode.srcIn,
                ),
                semanticsLabel: REFRESH_BUTTON,
              ),
            ),
          ),
          StreamBuilder<String>(
              stream: _bloc.acquisitionDateTimeValue,
              builder: (context, snapshot) {
                return Container(
                  margin: EdgeInsets.only(top: 8.h),
                  child: Text(
                    snapshot.hasData ? snapshot.data! : " - ",
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.caption1, _colorUtil.tertiary05),
                  ),
                );
              }),
        ],
      ),
    );
  }

  Widget _hvacRangeLayout() {
    //HVAC ON/OFF
    return StreamBuilder<RangeInfo?>(
      stream: _bloc.rangeInfo,
      builder: (context, snapshot) {
        return (snapshot.hasData)
            ? Padding(
                padding: EdgeInsets.only(top: 20.h, bottom: 20.h),
                child: HVACRangeWidget(rangeInfo: snapshot.data),
              )
            : Container();
      },
    );
  }

  // Distance to empty
  Widget _distanceToEmptyLayout() {
    return Container(
      height: 76.h,
      width: 311.w,
      child: Center(
        child: StreamBuilder<String>(
            stream: _bloc.distanceWithoutAC,
            builder: (context, snapshot) {
              return StreamBuilder<String?>(
                  stream: _bloc.distanceUnit,
                  builder: (context, distanceSnapShot) {
                    return RichText(
                        text: TextSpan(children: <TextSpan>[
                      TextSpan(
                          text: snapshot.hasData ? snapshot.data : "0",
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.title4, _colorUtil.tertiary00)),
                      TextSpan(
                          text: distanceSnapShot.hasData
                              ? distanceSnapShot.data
                              : "mi",
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.subHeadline4,
                              _colorUtil.tertiary00)),
                    ]));
                  });
            }),
      ),
    );
  }

  // Widget _customTabLayout() {
  //   return StreamBuilder<List<String>>(
  //       stream: _bloc.tabTitleList,
  //       builder: (context, snapshot) {
  //         if (snapshot.hasData && snapshot.data.isNotEmpty) {
  //           _tabList = snapshot.data;
  //           if (_tabController == null ||
  //               (_tabController != null &&
  //                   _tabController.length != _tabList.length))
  //             _tabController =
  //                 TabController(length: _tabList.length, vsync: this);

  //           return DefaultTabController(
  //             //Default to Home tab if user is within 1000 meters from home; else Stations tab
  //             initialIndex: isCloseToHome ? 1 : 0,
  //             length: _tabList.length,
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Container(
  //                   height: kToolbarHeight,
  //                   child: TabBar(
  //                       indicatorPadding:
  //                           EdgeInsets.only(left: 16.w, right: 16.w),
  //                       indicatorWeight: 3,
  //                       indicatorSize: TabBarIndicatorSize.label,
  //                       indicatorColor: _colorUtil.tertiary00,
  //                       unselectedLabelColor: _colorUtil.tertiary05,
  //                       labelColor: _colorUtil.tertiary00,
  //                       unselectedLabelStyle: TextStyleExtension()
  //                           .newStyleWithColor(
  //                               _textStyleUtil.body3, _colorUtil.tertiary05),
  //                       labelStyle: TextStyleExtension().newStyleWithColor(
  //                           _textStyleUtil.body4, _colorUtil.tertiary00),
  //                       controller: _tabController,
  //                       tabs: List<Widget>.generate(_tabList.length, (index) {
  //                         return Tab(
  //                           child: Text(
  //                             _tabList[index],
  //                             maxLines: 1,
  //                             overflow: TextOverflow.ellipsis,
  //                           ),
  //                         );
  //                       })),
  //                 ),
  //                 _childBody()
  //               ],
  //             ),
  //           );
  //         } else {
  //           return Padding(
  //             padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h),
  //             child: Column(
  //               children: [
  //                 shimmerRectangle(140.h, double.maxFinite, CARD_RADIUS_SMALL),
  //                 Padding(
  //                   padding: EdgeInsets.only(top: 8.h),
  //                   child: shimmerRectangle(
  //                       140.h, double.maxFinite, CARD_RADIUS_SMALL),
  //                 ),
  //               ],
  //             ),
  //           );
  //         }
  //       });
  // }

// body/content for tab strip
  Widget _childBody() {
    return Column(
      children: [
        SizedBox(
          height: 10.h,
        ),
        StreamBuilder(
          initialData: false,
          stream: _bloc.showSchedule,
          builder: (context, snapshot) {
            if (snapshot.data != null && snapshot.data == true) {
              return StreamBuilder<ScheduleType>(
                stream: _bloc.scheduleType,
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return snapshot.data == ScheduleType.NONMULTIDAY
                        ? StreamBuilder<bool>(
                            stream: _bloc.showStartCharge,
                            builder: (context, snapshot) {
                              return (snapshot.hasData && snapshot.data == true)
                                  ? Column(
                                      children: [
                                        SizedBox(height: 10.h),
                                        floatingUI(),
                                      ],
                                    )
                                  : Container();
                            },
                          )
                        : Container();
                  } else {
                    return Container();
                  }
                },
              );
            } else {
              return Container();
            }
          },
        ),
        caWidget(),
        StreamBuilder(
          initialData: false,
          stream: _bloc.showSchedule,
          builder: (context, snapshot) {
            // for (final String child in _tabList) {
            // if (child == OneAppString.of().schedule) {
            if (snapshot.data != null && snapshot.data == true) {
              return StreamBuilder<ScheduleType>(
                stream: _bloc.scheduleType,
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return snapshot.data == ScheduleType.MULTIDAY
                        ? ScheduleListing(
                            chargeInfo: _bloc.chargeInfo,
                            acquisitionDatetime: _bloc.acquisitionDatetime,
                            scrollController: ModalScrollController.of(context),
                            materialContext: _materialContext,
                            reloadChargeManagementPage: (reload) {
                              _reloadChargeManagementPage(reload, null);
                            },
                            vehicleBloc: _bloc,
                          )
                        : Flexible(
                            child: VehicleChargeManagementTimerPage(
                              chargeInfo: _bloc.chargeInfo,
                              acquisitionDatetime: _bloc.acquisitionDatetime,
                              scrollController:
                                  ModalScrollController.of(context),
                              materialContext: _materialContext,
                              reloadChargeManagementPage: (reload, detail) {
                                _reloadChargeManagementPage(reload, detail);
                              },
                              vehicleBloc: _bloc,
                            ),
                          );
                  } else {
                    return Container();
                  }
                },
              );
            } else {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  schedulesInfoImg(),
                  StreamBuilder<bool>(
                    stream: _bloc.showStartCharge,
                    builder: (context, snapshot) {
                      return (snapshot.hasData && snapshot.data == true)
                          ? Container()
                          : Container(
                              child: Center(
                                child: _refreshIconLayout(),
                              ),
                            );
                    },
                  ),
                ],
              );
            }

            // else if (child == OneAppString.of().ecoHistory) {
            //   _tabChildren.add(VehicleChargeManagementEcoHistoryPage(
            //       chargeInfo: _bloc.chargeInfo,
            //       scrollController: ModalScrollController.of(context),
            //       ecoHistoryPayLoad: _bloc.ecoHistoryPayLoad));
            // } else if (child == OneAppString.of().ranking) {
            //   _tabChildren.add(VehicleChargeManagementRankingPage(
            //       chargeInfo: _bloc.chargeInfo,
            //       scrollController: ModalScrollController.of(context),
            //       ecoHistoryPayLoad: _bloc.ecoHistoryPayLoad));
            // } else if (child == OneAppString.of().cleanAssist) {
            //   _tabChildren.add(VehicleCleanAssistPage(
            //     dashboardEnroll: false,
            //   ));
            // }
            // }
          },
        ),
      ],
    );
  }

  Widget _responseToastCustomWidget(String toastMessage, bool isSuccessful) {
    return CommonToast(
      iconColor: _colorUtil.tertiary15,
      iconPath: isSuccessful ? checkIcon : closeIcon,
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
    );
  }

  void _reloadChargeManagementPage(
      bool reload, ChargeManagementDetailEntity? detail) {
    _bloc.refreshUI(detail ?? null);
  }

  void _savedResponseCallback() {
    showCustomToast(
        _responseToastCustomWidget(
            OneAppString.of().loginUnableProcessYourRequest, false),
        3);
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  Widget createScheduleButton(String title) {
    return Center(
      child: InkWell(
        onTap: () {
          showMaterialModalBottomSheet(
            expand: false,
            isDismissible: true,
            context: context,
            useRootNavigator: true,
            backgroundColor: _colorUtil.tile01,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(CARD_RADIUS),
              ),
            ),
            builder: (context) => SafeArea(
              top: true,
              bottom: true,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(child: ScheduleDetailPage()),
                ],
              ),
            ),
          ).then((value) {
            if (value != null) {
              ScheduleBoxedReturns returnVal = value;
              String requestNo = returnVal.requestNo ?? '';
              showCustomToast(_toastCustomWidget(returnVal.toastMsg), 2);
              _bloc.refreshMultidaySchedules(requestNo);
            }
          });
        },
        child: Container(
          width: 192.w,
          height: 52.h,
          decoration: BoxDecoration(
              color: ThemeConfig.current().colorUtil.primaryButton02,
              borderRadius: BorderRadius.circular(100.r)),
          child: Center(
            child: Text(
              title,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout2, _colorUtil.primaryButton01),
            ),
          ),
        ),
      ),
    );
  }

  Widget _toastCustomWidget(String toastMessage) {
    return CommonToast(
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
      showIcon: false,
    );
  }

  Widget ecoHistoryButton() {
    return InkWell(
      onTap: () {
        showMaterialModalBottomSheet(
          expand: true,
          isDismissible: true,
          context: context,
          useRootNavigator: true,
          backgroundColor: _colorUtil.tile01,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(CARD_RADIUS),
            ),
          ),
          builder: (context) => SafeArea(
            top: true,
            bottom: true,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SwipeBarIcon(),
                bottomSheetCustomAppBar(OneAppString.of().ecoHistory,
                    onBackPressed: () {
                  Navigator.of(context).pop();
                }, elevation: 0),
                Expanded(
                    child: VehicleChargeManagementEcoHistoryPage(
                        chargeInfo: _bloc.chargeInfo,
                        scrollController: ModalScrollController.of(context))),
              ],
            ),
          ),
        ).then((value) {
          _reloadChargeManagementPage(true, null);
        });
      },
      child: Padding(
        padding: EdgeInsets.only(right: 10.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: Container(
                  width: 32.w,
                  height: 32.h,
                  child: Center(
                      child: SvgPicture.asset(
                    icEcoHistory,
                  ))),
            ),
            Text(
              OneAppString.of().history,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.tabLabel02, _colorUtil.button02a),
            )
          ],
        ),
      ),
    );
  }

  Widget ecoHistoryWidget() {
    return StreamBuilder<bool>(
      initialData: false,
      stream: _bloc.showEcoHistory,
      builder: (context, snapshot) {
        return (snapshot.hasData && snapshot.data == true)
            ? ecoHistoryButton()
            : Container();
      },
    );
  }

  Widget caWidget() {
    return StreamBuilder(
        initialData: false,
        stream: _bloc.isLCFSEnabled,
        builder: (context, snapshot) {
          return (snapshot.hasData && snapshot.data == true)
              ? StreamBuilder(
                  initialData: false,
                  stream: _bloc.isLCFSEligible, // validating for 17 CY vehicles
                  builder: (context, snapshot) {
                    return (snapshot.data == true)
                        ? StreamBuilder(
                            initialData: null,
                            stream: _bloc.isLCFSShowGraph,
                            builder: (context, snapshot) {
                              return Container(
                                height: 100.h,
                                child: InkWell(
                                  onTap: () {
                                    if (snapshot.hasData &&
                                        snapshot.data == true) {
                                      // Show graph
                                      Navigator.of(context).push(CustomPageRoute(
                                          page: Container(
                                              color: _colorUtil.tile01,
                                              child:
                                                  VehicleCleanAssistGraph())));
                                    } else {
                                      // Enroll for CA
                                      Navigator.of(context)
                                          .push(CustomPageRoute(
                                              page:
                                                  VehicleCleanAssistConsentDetailPage()))
                                          .then((value) {
                                        _reloadChargeManagementPage(true, null);
                                      });
                                    }
                                  },
                                  child: Container(
                                    child: Padding(
                                        padding: EdgeInsets.all(16),
                                        child: Container(
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              color: _colorUtil.tile01,
                                              boxShadow: [
                                                BoxShadow(
                                                    color: Color.fromRGBO(
                                                        0, 0, 0, 0.07),
                                                    blurRadius: 15.w,
                                                    spreadRadius: 0.0,
                                                    offset: Offset(0.0, 5.w))
                                              ]),
                                          height: 72.h,
                                          child: Row(
                                            children: [
                                              Padding(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 16.0),
                                                child: Container(
                                                  width: 24,
                                                  height: 24,
                                                  child: SvgPicture.asset(
                                                      cleanAssistSvgIcon,
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                        _colorUtil.tertiary00,
                                                        BlendMode.srcIn,
                                                      ),
                                                      width: 40,
                                                      height: 40),
                                                ),
                                              ),
                                              Text(
                                                OneAppString.of().cleanAssist,
                                                textAlign: TextAlign.left,
                                                style: TextStyleExtension()
                                                    .newStyleWithColor(
                                                        _textStyleUtil
                                                            .subHeadline1,
                                                        _colorUtil.tertiary03),
                                              ),
                                              Spacer(),
                                              caActionButton()
                                            ],
                                          ),
                                        )),
                                  ),
                                ),
                              );
                            })
                        : Container();
                  })
              : Container();
        });
  }

  Widget caActionButton() {
    return StreamBuilder(
        initialData: null,
        stream: _bloc.isLCFSShowGraph,
        builder: (context, snapshot) {
          return Padding(
            padding: EdgeInsets.only(right: 16),
            child: (snapshot.data == null)
                ? Container()
                : Container(
                    width: 84,
                    height: 34,
                    decoration: BoxDecoration(
                        color: snapshot.hasData
                            ? _colorUtil.secondary02
                            : _colorUtil.button05b,
                        borderRadius: BorderRadius.circular(100)),
                    child: Center(
                        child: Text(
                      (snapshot.hasData && snapshot.data == true)
                          ? OneAppString.of().view
                          : OneAppString.of().enroll,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1,
                          snapshot.hasData
                              ? _colorUtil.button03b
                              : _colorUtil.button02a),
                    )),
                  ),
          );
        });
  }

  void triggerChargeDescTooltip() {
    Future.delayed(Duration(seconds: 1), () {
      if (chargeDescWidgetContext != null) {
        showChargeDescToolTip(chargeDescWidgetContext);
      }
    });
  }

  void showChargeDescToolTip(BuildContext? widgetContext) {
    if (tooltip != null && tooltip!.isOpen) {
      tooltip!.close();
      return;
    }
    tooltip = SuperTooltip(
      popupDirection: TooltipDirection.down,
      borderRadius: 30,
      hasShadow: false,
      arrowBaseWidth: 40.0,
      arrowLength: 15.0,
      borderWidth: 0,
      containsBackgroundOverlay: true,
      outsideBackgroundColor: Color.fromRGBO(0, 0, 0, 0.5),
      dismissOnTapOutside: true,
      content: Material(
          child: Padding(
        padding: EdgeInsets.only(top: 20.h),
        child: Container(
          height: 190.h,
          width: 343.w,
          child: Column(
            children: [
              SvgPicture.asset(
                chargeTooltipIcon,
                height: 48.w,
                width: 48.w,
              ),
              Padding(
                padding: EdgeInsets.only(top: 16.h),
                child: Text(
                  OneAppString.of().chargeDescToolTipTitle,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 8.h, left: 20.w, right: 20.w),
                child: Container(
                  // width: 200.w,
                  child: Text(
                    OneAppString.of().chargeDescToolTipSubTitle,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      )),
    );

    tooltip!.show(widgetContext!);
  }

  Widget maxSchedulesReachedWidget() {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 2.h),
            child: Text(
              OneAppString.of().evmcMaxSchedulesReachedText,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.buttonLink1, _colorUtil.tertiary03),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 8.h),
            child: Text(
              OneAppString.of().evmcMaxSchedulesReachedInfoText,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.footNote1, _colorUtil.tertiary07),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget schedulesInfoImg() {
    Color gradientStart = Colors.black;
    Color gradientEnd = Colors.transparent;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: ShaderMask(
              shaderCallback: (rect) {
                return LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [gradientStart, gradientEnd],
                ).createShader(
                    Rect.fromLTRB(0, 0, rect.width - 40, rect.height - 49));
              },
              blendMode: BlendMode.darken,
              child: Container(
                height: 296.h,
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: ExactAssetImage(mcBGImg), fit: BoxFit.cover),
                ),
              ),
            ),
          ),
          Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 30.h, left: 16.w, right: 16.w),
                child: Text(
                  OneAppString.of().noScheduleFeatureTitleText,
                  style: TextStyleExtension()
                      .newStyleWithColor(_textStyleUtil.callout2, Colors.white),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 24.h, left: 24.w, right: 24.w),
                child: Text(
                  OneAppString.of().noScheduleFeatureSubtitleText,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline3, Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
