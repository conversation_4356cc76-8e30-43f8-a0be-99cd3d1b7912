// Flutter imports:

// Dart imports:
import 'dart:math';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/eco_history_chart_helper.dart';

class VehicleChargeManagementEcoHistoryChart extends StatelessWidget {
  final List<EcoHistoryEntity> ecoHistoryList;
  final EcoHistoryEntity maxItem;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  VehicleChargeManagementEcoHistoryChart(this.ecoHistoryList, this.maxItem);

  double get maxY {
    if (maxItem.value <= 0) {
      return 30;
    }
    return yAxisInterval * 6;
  }

  double get yAxisInterval {
    final splitFive = (maxItem.value) / 5;
    if (splitFive > 1) {
      return max(splitFive.roundToDouble(), 1);
    } else {
      return (splitFive * 10).roundToDouble() / 10.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BarChart(
      BarChartData(
          maxY: maxY,
          titlesData: titlesData,
          borderData: borderData,
          barGroups: barGroups,
          alignment: BarChartAlignment.spaceAround,
          gridData: FlGridData(show: false)),
    );
  }

  FlTitlesData get titlesData => FlTitlesData(
        show: true,
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: (double value, meta) {
              return Padding(
                padding: EdgeInsets.only(top: 5),
                child: Text(
                  getMonth(ecoHistoryList
                      .firstWhere((history) => history.year == value.toInt())
                      .year
                      .toStringAsFixed(0)),
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.caption1, _colorUtil.tertiary05),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 45.w,
            interval: yAxisInterval,
          ),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
      );

  FlBorderData get borderData => FlBorderData(
        show: false,
      );

  List<BarChartGroupData> get barGroups => ecoHistoryList
      .map(
        (e) => BarChartGroupData(
          x: e.year,
          barsSpace: 4,
          barRods: [
            BarChartRodData(toY: e.value, color: _colorUtil.tertiary03)
          ],
          //showingTooltipIndicators: [0],
        ),
      )
      .toList();
}
