// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/widget/custom_divider.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/eco_history_chart_helper.dart';

// Project imports:
import 'vehicle_charge_management_eco_history_bloc.dart';
import 'vehicle_charge_management_eco_history_chart.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/eco_history_detail_entity.dart'
    as ecoHistoryDetail;

class VehicleChargeManagementEcoHistoryPage extends StatefulWidget {
  final ChargeInfo? chargeInfo;
  final ScrollController? scrollController;

  VehicleChargeManagementEcoHistoryPage(
      {this.chargeInfo, this.scrollController});

  @override
  _VehicleChargeManagementEcoHistoryPageState createState() =>
      _VehicleChargeManagementEcoHistoryPageState();
}

class _VehicleChargeManagementEcoHistoryPageState
    extends State<VehicleChargeManagementEcoHistoryPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  VehicleChargeManagementEcoHistoryBloc _bloc =
      VehicleChargeManagementEcoHistoryBloc();

  @override
  void initState() {
    super.initState();
    _bloc.init(widget.chargeInfo, _progressHandlerCallback);
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        child: OneAppScaffold(
          backgroundColor: _colorUtil.tile01,
          body: SingleChildScrollView(
            controller: widget.scrollController,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              child: StreamBuilder<bool>(
                  stream: _bloc.isEmptyPage,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return snapshot.data!
                          ? _noDataAvailableLayout()
                          : _ecoHistoryBody();
                    } else {
                      return Container();
                    }
                  }),
            ),
          ),
        ),
        bloc: _bloc);
  }

  // Empty layout if data is null
  Widget _noDataAvailableLayout() {
    return Container(
      width: double.maxFinite,
      margin: EdgeInsets.only(top: 34.h),
      child: Center(
        child: Text(
          OneAppString.of().dataNotAvailableFor(OneAppString.of().ecoHistory),
          textAlign: TextAlign.center,
          style: TextStyleExtension().newStyleWithColor(
              _textStyleUtil.callout1, _colorUtil.tertiary07),
        ),
      ),
    );
  }

  Widget _ecoHistoryBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        StreamBuilder<List<EcoHistoryEntity>>(
            stream: _bloc.ecoHistoryList,
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                EcoHistoryEntity maxItem =
                    snapshot.data!.reduce((a, b) => a.value > b.value ? a : b);
                return Container(
                    height: 260.h,
                    width: double.maxFinite,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: 22.h),
                    child: VehicleChargeManagementEcoHistoryChart(
                        snapshot.data!, maxItem));
              } else {
                return Container();
              }
            }),
        _bottomListLayout()
      ],
    );
  }

  Widget _bottomListLayout() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 26.h, bottom: 26.h),
          child: CustomDivider(
            lineColor: _colorUtil.tertiary12,
          ),
        ),
        StreamBuilder<List<ecoHistoryDetail.DailyInformation>?>(
            stream: _bloc.dailyInformationList,
            builder: (context, snapshot) {
              return (snapshot.hasData)
                  ? ListView.builder(
                      shrinkWrap: true,
                      itemCount: snapshot.data!.length,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (BuildContext context, int index) {
                        ecoHistoryDetail.DailyInformation item =
                            snapshot.data![index];
                        return Column(
                          children: [
                            _historyListTile(
                                item.fuelEfficiency!.value! +
                                    " " +
                                    item.fuelEfficiency!.unit!,
                                changeDateTimeFormat(
                                    item.tripDate!, "yyyy MM dd", "MM/dd/yy"),
                                index),
                            Padding(
                              padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
                              child: CustomDivider(
                                lineColor: _colorUtil.tertiary12,
                              ),
                            ),
                          ],
                        );
                      })
                  : Container();
            }),
      ],
    );
  }

  Widget _historyListTile(String fuelEfficiency, String date, int index) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          fuelEfficiency,
          style: TextStyleExtension()
              .newStyleWithColor(_textStyleUtil.body2, _colorUtil.tertiary03),
        ),
        Text(
          date,
          style: TextStyleExtension().newStyleWithColor(_textStyleUtil.body3,
              (index == 0) ? _colorUtil.tertiary05 : _colorUtil.tertiary07),
        ),
      ],
    );
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }
}
