// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/eco_history_chart_helper.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../local_repo/vehicle_repo.dart';
import '../../../log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/eco_history_detail_entity.dart'
    as ecoHistoryDetail;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleChargeManagementEcoHistoryBloc extends BlocBase {
  double powerGeneratedValue = 0;
  double totalEvDistanceValue = 0;
  ChargeInfo? chargeInfo;
  OneAppClient api = APIClientConfig.oneAppClient;
  ecoHistoryDetail.EcoHistoryDetailPayload? ecoHistoryDetailPayload;
  vehicleInfo.Payload? vehicleInfoEntity;
  late Function(bool) progressHandlerCallback;

  Stream<List<EcoHistoryEntity>> get ecoHistoryList => _ecoHistoryList.stream;
  final _ecoHistoryList = BehaviorSubject<List<EcoHistoryEntity>>();

  Stream<List<ecoHistoryDetail.DailyInformation>?> get dailyInformationList =>
      _dailyInformationList.stream;
  final _dailyInformationList =
      BehaviorSubject<List<ecoHistoryDetail.DailyInformation>?>();

  Stream<String> get powerGenerated => _powerGenerated.stream;
  final _powerGenerated = BehaviorSubject<String>();

  Stream<String> get totalEvDistance => _totalEvDistance.stream;
  final _totalEvDistance = BehaviorSubject<String>();

  Stream<bool> get isEmptyPage => _isEmptyPage.stream;
  final _isEmptyPage = BehaviorSubject<bool>();

  void init(ChargeInfo? chargeInfoPayload, Function(bool) progressHandler) {
    progressHandlerCallback = progressHandler;
    chargeInfo = chargeInfoPayload;
    fetchEcoHistoryDetail();
  }

// Fetch Vehicle Eco History Details with VIN
  Future<void> fetchEcoHistoryDetail() async {
    progressHandlerCallback(true);
    String vin = Global.getInstance().vin!;
    vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
    final commonResponse = await api.fetchEcoHistoryDetail(
        vehicleInfoEntity!.brand!,
        vehicleInfoEntity!.generation!,
        vin,
        vehicleInfoEntity!.region!);
    final payLoad = commonResponse.response?.payload;
    ecoHistoryDetailPayload = payLoad;

    if (ecoHistoryDetailPayload != null) {
      _isEmptyPage.sink.add(false);
      List<ecoHistoryDetail.MonthlyInformation>? monthlyInformationList =
          ecoHistoryDetailPayload!.resESPo!.monthlyInformation;
      List<EcoHistoryEntity> ecoHistoryLists = [];

      monthlyInformationList = monthlyInformationList!
          .where((i) => (i.fuelEfficiency!.doubleValue != null))
          .toList();

      // ignore: unnecessary_null_comparison
      if (monthlyInformationList != null) {
        monthlyInformationList = monthlyInformationList.reversed.toList();
        for (final ecoHistoryDetail.MonthlyInformation item
            in monthlyInformationList) {
          List dateArray = item.tripMonth!.split(' ');
          String month = dateArray[1];
          EcoHistoryEntity ecoHistoryEntity = EcoHistoryEntity(
              int.parse(month), item.fuelEfficiency!.doubleValue!);
          ecoHistoryLists.add(ecoHistoryEntity);
          powerGeneratedValue = powerGeneratedValue +
              (item.powerGenerationValue != null
                  ? item.powerGenerationValue
                  : 0);
          totalEvDistanceValue = totalEvDistanceValue +
              (item.oneMonthEvMileage != null
                  ? double.parse(item.oneMonthEvMileage!.value!)
                  : 0);
        }
        if (monthlyInformationList.length < 6) {
          List<EcoHistoryEntity> ecoHistoryListsOne = [];
          List<EcoHistoryEntity> ecoHistoryListsTwo = [];
          ecoHistoryLists.clear();
          int j = monthlyInformationList.length;
          List dateArray = monthlyInformationList.first.tripMonth!.split(' ');
          int lastMonth = int.parse(dateArray[1]);
          int toBeFilledCount = lastMonth;
          if (lastMonth < 6) {
            int toBeFilledMonthCount = 6 - j;
            for (int i = 0; i < toBeFilledMonthCount; i++) {
              int checkCount = toBeFilledMonthCount - i;
              EcoHistoryEntity ecoHistoryEntity;
              if (checkCount <= toBeFilledCount) {
                int newMonth = toBeFilledCount - checkCount;
                if (newMonth != 0) {
                  ecoHistoryEntity = EcoHistoryEntity(newMonth, 0.0);
                } else {
                  ecoHistoryEntity = EcoHistoryEntity(12, 0.0);
                }
                ecoHistoryListsOne.add(ecoHistoryEntity);
              } else {
                ecoHistoryEntity = EcoHistoryEntity(11 - i, 0.0);
                ecoHistoryListsTwo.add(ecoHistoryEntity);
              }
            }
            ecoHistoryListsTwo = ecoHistoryListsTwo.reversed.toList();
            ecoHistoryLists.addAll(ecoHistoryListsTwo);
            ecoHistoryLists.addAll(ecoHistoryListsOne);
            for (final ecoHistoryDetail.MonthlyInformation item
                in monthlyInformationList) {
              List dateArray = item.tripMonth!.split(' ');
              String month = dateArray[1];
              EcoHistoryEntity ecoHistoryEntity = EcoHistoryEntity(
                  int.parse(month), item.fuelEfficiency!.doubleValue!);
              ecoHistoryLists.add(ecoHistoryEntity);
            }
          } else {
            int toBeFilledMonthCount = 6 - j;
            for (int i = 1; i <= toBeFilledMonthCount; i++) {
              EcoHistoryEntity ecoHistoryEntity;
              ecoHistoryEntity = EcoHistoryEntity(lastMonth - i, 0.0);
              ecoHistoryListsTwo.add(ecoHistoryEntity);
            }
            ecoHistoryListsTwo = ecoHistoryListsTwo.reversed.toList();
            ecoHistoryLists.addAll(ecoHistoryListsTwo);
            for (final ecoHistoryDetail.MonthlyInformation item
                in monthlyInformationList) {
              List dateArray = item.tripMonth!.split(' ');
              String month = dateArray[1];
              EcoHistoryEntity ecoHistoryEntity = EcoHistoryEntity(
                  int.parse(month), item.fuelEfficiency!.doubleValue!);
              ecoHistoryLists.add(ecoHistoryEntity);
            }
          }
        }
        _ecoHistoryList.sink.add(ecoHistoryLists);
        _powerGenerated.sink
            .add(powerGeneratedValue.toStringAsFixed(2) + " wh");
        if (chargeInfo != null &&
            chargeInfo!.evDistanceUnit != null &&
            chargeInfo!.evDistanceUnit!.isNotEmpty) {
          _totalEvDistance.sink.add(totalEvDistanceValue.toStringAsFixed(2) +
              " " +
              chargeInfo!.evDistanceUnit!);
        } else {
          _totalEvDistance.sink
              .add(totalEvDistanceValue.toStringAsFixed(2) + " mi");
        }
      } else {
        _ecoHistoryList.sink.add(ecoHistoryLists);
      }
      List<ecoHistoryDetail.DailyInformation>? dailyInfoList = [];
      if (ecoHistoryDetailPayload!.resESPo!.dailyInformation != null) {
        dailyInfoList = ecoHistoryDetailPayload!.resESPo!.dailyInformation;
      }

      _dailyInformationList.sink.add(dailyInfoList);
    } else {
      _isEmptyPage.sink.add(true);
    }
    FireBaseAnalyticsLogger.logSuccessAPI(
        VehicleAnalyticsEvent.VEHICLE_FETCH_ECO_HISTORY_SUCCESS,
        category: LogCategory.FL_VEHI);

    if (commonResponse.error != null) {
      _isEmptyPage.sink.add(true);
      ecoHistoryDetailPayload = null;
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_ECO_HISTORY_FAILURE,
          category: LogCategory.FL_VEHI);
    }
    progressHandlerCallback(false);
  }

  @override
  void dispose() {
    _ecoHistoryList.close();
    _dailyInformationList.close();
    _powerGenerated.close();
    _totalEvDistance.close();
    _isEmptyPage.close();
  }
}
