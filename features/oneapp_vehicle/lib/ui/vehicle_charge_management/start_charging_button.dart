// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

class StartChargingButton extends StatelessWidget {
  const StartChargingButton({
    Key? key,
    required this.onTap,
  }) : super(key: key);

  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 192.w,
        height: 52.h,
        decoration: BoxDecoration(
            color: ThemeConfig.current().colorUtil.primaryButton02,
            borderRadius: BorderRadius.circular(100)),
        child: Center(
          child: Text(
            OneAppString.of().evmcStartChargingButtonText,
            style: TextStyleExtension().newStyleWithColor(
              ThemeConfig.current().textStyleUtil.callout2,
              ThemeConfig.current().colorUtil.primaryButton01,
            ),
          ),
        ),
      ),
    );
  }
}
