// Dart imports:
import 'dart:async';

// Package imports:
import 'package:intl/intl.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/charge_timer_body_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '/local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleChargeManagementTimerBloc extends BlocBase {
  OneAppClient api = APIClientConfig.oneAppClient;
  ChargeInfo? chargeInfo;
  String? acquisitionDatetime;
  int? chargeType = 15;
  int timeOut = 180;
  vehicleInfo.Payload? vehicleInfoEntity;
  late Function(bool) progressHandlerCallback;

  Stream<int> get chargeTypeSelected => _chargeTypeSelected.stream;
  final _chargeTypeSelected = BehaviorSubject<int>();

  Stream<String> get lastUpdatedTime => _lastUpdatedTime.stream;
  final _lastUpdatedTime = BehaviorSubject<String>();

  void init(ChargeInfo? chargeInfoData, String? acquisitionDatetimeData,
      Function(bool) progressHandler) {
    chargeInfo = chargeInfoData;
    acquisitionDatetime = acquisitionDatetimeData;
    progressHandlerCallback = progressHandler;
    if (chargeInfo != null) {
      chargeType = chargeInfo!.chargeType;
      if (chargeType == 1) {
        // Start Time
        _chargeTypeSelected.sink.add(1);
      } else if (chargeType == 2 || chargeType == 3) {
        // Departure Time
        _chargeTypeSelected.sink.add(0);
      }
    }
    if (acquisitionDatetime != null) {
      _setLastUpdatedDate(acquisitionDatetime);
    }
  }

  void _setLastUpdatedDate(String? lastUpdatedDate) {
    if (lastUpdatedDate != null && lastUpdatedDate.isNotEmpty) {
      String lastUpdated = lastUpdatedDate;
      DateTime dateTime =
          DateFormat("yyyy-MM-ddTHH:mm:ssZ").parse(lastUpdated, true).toLocal();
      String updatedTime = "";
      String? todayOrYesterday = checkDateIsTodayOrYesterday(dateTime);
      if (todayOrYesterday != null) {
        updatedTime = todayOrYesterday +
            convertDateTimeFormat(
                lastUpdated, "yyyy-MM-ddTHH:mm:ssZ", " @ h:mma");
      } else {
        updatedTime = convertDateTimeFormat(
            lastUpdated, "yyyy-MM-ddTHH:mm:ssZ", "MMM dd @ h:mma");
      }
      updatedTime = updatedTime
          .replaceAll('@', OneAppString.of().at)
          .replaceAll("AM", "am")
          .replaceAll("PM", "pm")
          .replaceAll('.', '');
      _lastUpdatedTime.sink.add(OneAppString.of().updated(updatedTime));
    }
  }

  Future<void> refreshLastUpdatedData(
      Function savedSuccessfullyBottomSheet, Function reloadPage) async {
    String? vin = Global.getInstance().vin;
    String? deviceId = Global.getInstance().fcmDeviceId;
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    if (vehicleItem != null) {
      progressHandlerCallback(true);
      int startTime = DateTime.now().millisecondsSinceEpoch;
      final postResponse = await api.postRealTimeStatusRequest(
          vin!, vehicleItem.brand!, vehicleItem.generation ?? "", deviceId!);
      final payLoad = postResponse.response?.payload;
      // progressHandlerCallback(false);
      if (payLoad != null) {
        if (payLoad.returnCode == "ONE-RES-10000" ||
            payLoad.returnCode == "000000") {
          // progressHandlerCallback(true);
          fetchChargeManagementStatus(startTime, "", () {}, reloadPage);
        } else {
          // progressHandlerCallback(true);
          fetchChargeManagementStatus(startTime, payLoad.appRequestNo ?? "",
              savedSuccessfullyBottomSheet, reloadPage);
        }
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_SUCCESS,
            category: LogCategory.FL_VEHI);
      }

      if (postResponse.error != null) {
        progressHandlerCallback(false);
        FireBaseAnalyticsLogger.logError(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  // Future<void> refreshLastUpdatedData(
  //     Function savedSuccessfullyBottomSheet, Function reloadPage) async {
  //   String vin = Global.getInstance().vin;
  //   String deviceId = await getFcmDeviceId();
  //   vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
  //   if (vehicleInfoEntity != null) {
  //     progressHandlerCallback(true);
  //     bool isPreCY17 = isPRECY17Vehicle(vehicleInfoEntity.generation);
  //     int startTime = DateTime.now().millisecondsSinceEpoch;
  //     final postResponse = await api.postRealTimeStatusRequest(
  //         vin, vehicleInfoEntity.generation, deviceId);
  //     final payLoad = postResponse.response?.payload;
  //     progressHandlerCallback(false);
  //     if (payLoad != null) {
  //       if (payLoad.returnCode == "ONE-RES-10000" && isPreCY17) {
  //         progressHandlerCallback(true);
  //         fetchChargeManagementStatus(
  //             startTime, null, savedSuccessfullyBottomSheet, reloadPage);
  //       } else {
  //         progressHandlerCallback(true);
  //         fetchChargeManagementStatus(startTime, payLoad.appRequestNo,
  //             savedSuccessfullyBottomSheet, reloadPage);
  //       }
  //       FireBaseAnalyticsLogger.logSuccessAPI(
  //           VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_SUCCESS,
  //           category: LogCategory.FL_VEHI);
  //     }

  //     if (postResponse?.error != null) {
  //       FireBaseAnalyticsLogger.logError(
  //           VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
  //           category: LogCategory.FL_VEHI);
  //     }
  //   }
  // }

  Future<void> startImmediateChargingAPI(
      Function savedSuccessfullyBottomSheet, Function reloadPage) async {
    String vin = Global.getInstance().vin ?? "";
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    ChargeTimerBodyHelper chargeTimerBodyHelper =
        constructImmediateChargeBody();
    progressHandlerCallback(true);

    final commonResponse = await api.postVehicleChargeTimerRequest(
        vehicleInfoEntity!.brand!,
        vin,
        vehicleItem!.generation!,
        Global.getInstance().fcmDeviceId!,
        chargeTimerBodyHelper);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      String? requestNo = commonResponse.response!.payload!.appRequestNo;
      int startTime = DateTime.now().millisecondsSinceEpoch;
      fetchChargeManagementTimerStatus(
          startTime, requestNo, savedSuccessfullyBottomSheet, reloadPage);
      // fetchChargeManagementStatus(
      //     startTime, requestNo, savedSuccessfullyBottomSheet, reloadPage);
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_CHARGE_TIMER_SETTING_SUCCESS,
          category: LogCategory.FL_VEHI);
    }

    if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(savedSuccessfullyBottomSheet);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_CHARGE_TIMER_SETTING_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  ChargeTimerBodyHelper constructImmediateChargeBody() {
    ChargeTimerBodyHelper chargeTimerBodyHelper = ChargeTimerBodyHelper();

    chargeTimerBodyHelper = ChargeTimerBodyHelper(
        command: IMMEDIATE_CHARGE, remoteHvac: null, reservationCharge: null);

    return chargeTimerBodyHelper;
  }

  Future<void> fetchChargeManagementStatus(int startTime, String appRequestNo,
      Function savedSuccessfullyBottomSheet, Function reloadPage) async {
    String vin = Global.getInstance().vin ?? "";
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    final commonResponse = await api.fetchClimateRealTimeStatus(
        vehicleItem!.generation ?? "",
        vin,
        appRequestNo,
        vehicleItem.brand ?? Global.getInstance().appBrand);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      int? status = payLoad.realtimeStatusResult?.status;
      int? result = payLoad.realtimeStatusResult?.result;
      if (status != null && status == 0) {
        if (result != null) {
          if (result == 0) {
            progressHandlerCallback(false);
            reloadPage(true);
          } else if (result == 1) {
            progressHandlerCallback(false);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 2) {
            progressHandlerCallback(false);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 3) {
            progressHandlerCallback(false);
            errorCallBack(savedSuccessfullyBottomSheet);
          }
        } else {
          errorCallBack(savedSuccessfullyBottomSheet);
          progressHandlerCallback(false);
        }
      } else {
        int currentTime = DateTime.now().millisecondsSinceEpoch;
        double timeElapsed = (currentTime - startTime) / 1000;
        if (timeElapsed >= timeOut) {
          progressHandlerCallback(false);
          errorCallBack(savedSuccessfullyBottomSheet);
        } else {
          fetchChargeManagementStatus(startTime, appRequestNo,
              savedSuccessfullyBottomSheet, reloadPage);
        }
      }
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        fetchChargeManagementStatus(
            startTime, appRequestNo, savedSuccessfullyBottomSheet, reloadPage);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      } else {
        errorCallBack(savedSuccessfullyBottomSheet);
        progressHandlerCallback(false);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
    //  else {
    //   reloadPage(true);
    // }
  }

  Future<void> fetchChargeManagementTimerStatus(
      int startTime,
      String? requestNo,
      Function savedSuccessfullyBottomSheet,
      Function reloadPage) async {
    String vin = Global.getInstance().vin!;
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);

    final commonResponse = await api.fetchChargeManagementTimerStatus(
        vehicleItem!.generation!,
        vin,
        vehicleItem.brand ?? Global.getInstance().appBrand,
        requestNo: requestNo);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);
      if (payLoad.remoteControlResult != null) {
        int? status = payLoad.remoteControlResult!.status;
        int? result = payLoad.remoteControlResult!.result;
        if (status != null) {
          if (status == 0) {
            progressHandlerCallback(false);
            if (result == 0) {
              reloadPage(true);
            } else if (result == 1) {
              errorCallBack(savedSuccessfullyBottomSheet);
            } else if (result == 2) {
              errorCallBack(savedSuccessfullyBottomSheet);
            } else if (result == 3) {
              errorCallBack(savedSuccessfullyBottomSheet);
            } else {
              errorCallBack(savedSuccessfullyBottomSheet);
            }
          } else {
            int now = DateTime.now().millisecondsSinceEpoch;
            double timeElapsed = (now - startTime) / 1000;
            if (timeElapsed >= timeOut) {
              progressHandlerCallback(false);
              errorCallBack(savedSuccessfullyBottomSheet);
            } else {
              fetchChargeManagementTimerStatus(startTime, requestNo,
                  savedSuccessfullyBottomSheet, reloadPage);
            }
          }
        } else {
          errorCallBack(savedSuccessfullyBottomSheet);
          progressHandlerCallback(false);
        }
      }
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        fetchChargeManagementTimerStatus(
            startTime, requestNo, savedSuccessfullyBottomSheet, reloadPage);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
            category: LogCategory.FL_VEHI);
      } else {
        progressHandlerCallback(false);
        errorCallBack(savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  void errorCallBack(Function savedSuccessfullyBottomSheet) {
    Future.delayed(const Duration(milliseconds: 500), () {
      savedSuccessfullyBottomSheet();
    });
  }

  @override
  void dispose() {
    _chargeTypeSelected.close();
    _lastUpdatedTime.close();
  }
}
