// Flutter imports:
import 'package:flutter/cupertino.dart';

// Package imports:
import 'package:ev_module/log/ev_analytics_events.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/charge_timer_body_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/charge_timer_realtime_status_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '/local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleChargeManagementTimerDepartureBloc extends BlocBase {
  OneAppClient api = APIClientConfig.oneAppClient;
  int timeOut = 150;
  ChargeInfo? chargeInfo;
  bool keyBoardPopup = false;
  late bool inputHour;
  FixedExtentScrollController? hourCupertinoPickerController;

  FixedExtentScrollController? minuteCupertinoPickerController;

  TextEditingController departureHourEditingController =
      TextEditingController();
  TextEditingController departureTimeEditingController =
      TextEditingController();
  late VoidCallback _departureHourPickerListener;
  late VoidCallback _departureHourEditingListener;
  late VoidCallback _departureMinutePickerListener;
  late VoidCallback _departureMinuteEditingListener;
  late Duration selectedTime;
  vehicleInfo.Payload? vehicleInfoEntity;
  late Function(bool) progressHandlerCallback;

  Stream<int> get daySelectedIndex => _daySelectedIndex.stream;
  final _daySelectedIndex = BehaviorSubject<int>();

  Stream<bool> get acStatus => _acStatus.stream;
  final _acStatus = BehaviorSubject<bool>();

  Stream<bool> get uiAfternoonSwitch => _afternoonSwitchController.stream;
  final _afternoonSwitchController = BehaviorSubject<bool>();

  void init(ChargeInfo? chargeInfoData, Function(bool) progressHandler) {
    progressHandlerCallback = progressHandler;
    chargeInfo = chargeInfoData;
    // Check ac switch is on or off for departure time schedule
    _acStatus.sink.add(false);
    _daySelectedIndex.sink.add(0);
    _afternoonSwitchController.sink.add(false);
    if (chargeInfo != null) {
      if (chargeInfo!.chargeType == 2 || chargeInfo!.chargeType == 3) {
        if (chargeInfo!.chargeWeek == 0 || chargeInfo!.chargeWeek! > 7) {
          _daySelectedIndex.sink.add(0);
        } else {
          _daySelectedIndex.sink.add(chargeInfo!.chargeWeek! - 1);
        }
        if (chargeInfo!.chargeType == 3) {
          _acStatus.sink.add(true);
        }
      }

      String? endString = chargeInfo!.chargeEndTime;
      // To handle API data miss-match
      if (endString != null && endString.isNotEmpty && endString.length > 4) {
        // Time format: 15:35
        int endHour = int.parse(endString.substring(0, 2));
        int endMinute = int.parse(endString.substring(3, 5));
        selectedTime = Duration(hours: endHour, minutes: endMinute);
        if (endHour >= 12) {
          _afternoonSwitchController.sink.add(true);
          if (endHour > 12) {
            endHour = endHour - 12;
          }
          hourCupertinoPickerController =
              FixedExtentScrollController(initialItem: endHour - 1);
          minuteCupertinoPickerController =
              FixedExtentScrollController(initialItem: endMinute);
          initialiseTimerControllers();
        } else {
          _afternoonSwitchController.sink.add(false);
          if (endHour == 0) {
            endHour = 12;
          }
        }

        hourCupertinoPickerController =
            FixedExtentScrollController(initialItem: endHour - 1);
        minuteCupertinoPickerController =
            FixedExtentScrollController(initialItem: endMinute);
        initialiseTimerControllers();
      } else {
        hourCupertinoPickerController =
            FixedExtentScrollController(initialItem: 11);
        minuteCupertinoPickerController =
            FixedExtentScrollController(initialItem: 60);
        initialiseTimerControllers();
        selectedTime = Duration(hours: 12, minutes: 00);
      }
    } else {
      hourCupertinoPickerController =
          FixedExtentScrollController(initialItem: 11);
      minuteCupertinoPickerController =
          FixedExtentScrollController(initialItem: 60);
      initialiseTimerControllers();
      selectedTime = Duration(hours: 12, minutes: 00);
    }
  }

  void initialiseTimerControllers() {
    _departureHourEditingListener = _pickerInputTextListener(
        12, departureHourEditingController, hourCupertinoPickerController);
    departureHourEditingController.addListener(_departureHourEditingListener);
    _departureMinuteEditingListener = _pickerInputTextListener(
        59, departureTimeEditingController, minuteCupertinoPickerController);
    departureTimeEditingController.addListener(_departureMinuteEditingListener);
    _departureHourPickerListener = _timePickerListener(
        12, hourCupertinoPickerController, departureHourEditingController);
    hourCupertinoPickerController!.addListener(_departureHourPickerListener);
    _departureMinutePickerListener = _timePickerListener(
        60, minuteCupertinoPickerController, departureTimeEditingController);
    minuteCupertinoPickerController!
        .addListener(_departureMinutePickerListener);
  }

  // Ac switch toggle change
  void onAcStatusChanged(bool toggle) {
    _acStatus.sink.add(toggle);
  }

  // Time picker time change
  void onTimePickerChange(Duration selectedValue) {
    selectedTime = selectedValue;
  }

  // AM/PM toggle change
  void onForenoonChange(bool toggle) {
    // am=false, pm=true;
    _afternoonSwitchController.sink.add(toggle);
  }

  // Day selection change
  void daySelected(int selectedIndex) {
    _daySelectedIndex.sink.add(selectedIndex);
  }

  // Confirm new time change
  Future<void> confirmNewTimerSettings(
      Function savedSuccessfullyBottomSheet) async {
    progressHandlerCallback(true);
    String vin = Global.getInstance().vin!;
    vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
    ChargeTimerBodyHelper chargeTimerBodyHelper = constructBody();
    int startTime = DateTime.now().millisecondsSinceEpoch;
    FireBaseAnalyticsLogger.logMarketingGroupEvent(
        VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
        childEventName: EVAnalyticsEvent.SCHEDULE_DEP_TIME);
    final commonResponse = await api.postVehicleChargeTimerRequest(
        vehicleInfoEntity!.brand!,
        vin,
        vehicleInfoEntity!.generation!,
        Global.getInstance().fcmDeviceId!,
        chargeTimerBodyHelper);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      fetchVehicleChargeStatus(
          startTime, payLoad.appRequestNo, savedSuccessfullyBottomSheet);
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_CHARGE_TIMER_SETTING_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
    if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(savedSuccessfullyBottomSheet);
      FireBaseAnalyticsLogger.logError(
          VehicleAnalyticsEvent.VEHICLE_CHARGE_TIMER_SETTING_FAILURE,
          category: LogCategory.FL_RENT);
    }
  }

  // Check charge status after changing the time
  Future<void> fetchVehicleChargeStatus(int startTime, String? requestNo,
      Function savedSuccessfullyBottomSheet) async {
    String vin = Global.getInstance().vin!;
    vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
    final commonResponse = await api.fetchChargeManagementTimerStatus(
        vehicleInfoEntity!.generation!,
        vin,
        vehicleInfoEntity!.brand ?? Global.getInstance().appBrand,
        requestNo: requestNo);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);
      int? status = payLoad.remoteControlResult?.status;
      int? result = payLoad.remoteControlResult?.result;
      if (status != null && status == 0) {
        if (result != null && result == 0) {
          ChargeInfo? item = payLoad.vehicleInfo!.chargeInfo;
          if (item != null) {
            if (item.chargeRemainingAmount == 0 ||
                (item.evDistance == 254.0 || item.evDistance == 0.0)) {
              ChargeTimerRealTimeStatusHelper body =
                  ChargeTimerRealTimeStatusHelper(
                      callbackUrl: "", timeout: 120, uploadInterval: 0);
              int startTime = DateTime.now().millisecondsSinceEpoch;
              final postResponse = await api.postElectricVehicleRealTimeStatus(
                  vin,
                  vehicleInfoEntity!.brand!,
                  vehicleInfoEntity!.generation!,
                  Global.getInstance().fcmDeviceId!,
                  body);
              final payLoad = postResponse.response?.payload;
              if (payLoad != null) {
                FireBaseAnalyticsLogger.logSuccessAPI(
                    VehicleAnalyticsEvent
                        .VEHICLE_POST_ELECTRIC_REALTIME_STATUS_SUCCESS,
                    category: LogCategory.FL_VEHI);
                fetchRealTimeTimerSettingsStatus(startTime,
                    payLoad.appRequestNo!, savedSuccessfullyBottomSheet);
              }

              if (postResponse.error != null) {
                progressHandlerCallback(false);
                errorCallBack(savedSuccessfullyBottomSheet);
                FireBaseAnalyticsLogger.logErrorAPI(
                    VehicleAnalyticsEvent
                        .VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
                    category: LogCategory.FL_VEHI);
              }
            } else {
              progressHandlerCallback(false);
              successCallBack(
                  savedSuccessfullyBottomSheet, commonResponse.response);
              // timing setting change and go back
            }
          } else {
            progressHandlerCallback(false);
            successCallBack(
                savedSuccessfullyBottomSheet, commonResponse.response);
            // timing setting change and go back
          }
        } else if (result == 1) {
          progressHandlerCallback(false);
          errorCallBack(savedSuccessfullyBottomSheet);
          //Error - changes cannot be preformed
        } else if (result == 2) {
          progressHandlerCallback(false);
          errorCallBack(savedSuccessfullyBottomSheet);
          // server error
        } else if (result == 3) {
          progressHandlerCallback(false);
          errorCallBack(savedSuccessfullyBottomSheet);
          // time out
        }
      } else {
        int currentTime = DateTime.now().millisecondsSinceEpoch;
        double timeElapsed = (currentTime - startTime) / 1000;
        if (timeElapsed >= timeOut) {
          progressHandlerCallback(false);
          errorCallBack(savedSuccessfullyBottomSheet);
          // time out
        } else {
          fetchVehicleChargeStatus(
              startTime, requestNo, savedSuccessfullyBottomSheet);
        }
      }
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        fetchVehicleChargeStatus(
            startTime, requestNo, savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      } else {
        progressHandlerCallback(false);
        errorCallBack(savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  Future<void> fetchRealTimeTimerSettingsStatus(int startTime,
      String appRequestNo, Function savedSuccessfullyBottomSheet) async {
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.fetchClimateRealTimeStatus(
        vehicleInfoEntity!.generation!,
        vin,
        appRequestNo,
        vehicleInfoEntity!.brand ?? Global.getInstance().appBrand);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      int? status = payLoad.realtimeStatusResult?.status;
      int? result = payLoad.realtimeStatusResult?.result;
      if (status != null && status == 0) {
        if (result != null) {
          if (result == 0) {
            progressHandlerCallback(false);
            successCallBack(
                savedSuccessfullyBottomSheet, commonResponse.response);
          } else if (result == 1) {
            progressHandlerCallback(false);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 2) {
            progressHandlerCallback(false);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 3) {
            progressHandlerCallback(false);
            errorCallBack(savedSuccessfullyBottomSheet);
          }
        } else {
          progressHandlerCallback(false);
          errorCallBack(savedSuccessfullyBottomSheet);
        }
      } else {
        int currentTime = DateTime.now().millisecondsSinceEpoch;
        double timeElapsed = (currentTime - startTime) / 1000;
        if (timeElapsed >= timeOut) {
          progressHandlerCallback(false);
          errorCallBack(savedSuccessfullyBottomSheet);
        } else {
          fetchRealTimeTimerSettingsStatus(
              startTime, appRequestNo, savedSuccessfullyBottomSheet);
        }
      }
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        fetchRealTimeTimerSettingsStatus(
            startTime, appRequestNo, savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      } else {
        progressHandlerCallback(false);
        errorCallBack(savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  // Construct body for time change request
  ChargeTimerBodyHelper constructBody() {
    ChargeTimerBodyHelper chargeTimerBodyHelper = ChargeTimerBodyHelper();
    String timerType = CHARGE_TYPE_END;
    if (_acStatus.value) {
      timerType = CHARGE_TYPE_AC_END;
    }
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitHours = twoDigits(selectedTime.inHours);
    String twoDigitMinutes = twoDigits(selectedTime.inMinutes.remainder(60));
    String amPm = (_afternoonSwitchController.value) ? "pm" : "am";
    int hoursSelected = int.parse(twoDigitHours);
    int minuteSelected = int.parse(twoDigitMinutes);
    if (amPm == "pm" && hoursSelected != 12) hoursSelected = hoursSelected + 12;
    if (amPm == "am" && hoursSelected == 12) hoursSelected = 0;
    StartTime endTime = StartTime(hour: hoursSelected, minute: minuteSelected);
    ReservationCharge reservationCharge = ReservationCharge(
        chargeType: timerType,
        day: weekDayEnglishList[_daySelectedIndex.value],
        startTime: null,
        endTime: endTime);
    chargeTimerBodyHelper = ChargeTimerBodyHelper(
        command: RESERVE_CHARGE,
        remoteHvac: null,
        reservationCharge: reservationCharge);

    return chargeTimerBodyHelper;
  }

  void errorCallBack(Function savedSuccessfullyBottomSheet) {
    Future.delayed(const Duration(milliseconds: 500), () {
      savedSuccessfullyBottomSheet(false, null);
    });
  }

  void successCallBack(Function savedSuccessfullyBottomSheet,
      ChargeManagementDetailEntity? detail) {
    Future.delayed(const Duration(milliseconds: 500), () {
      savedSuccessfullyBottomSheet(true, detail);
    });
  }

  // Text listener for text
  VoidCallback _pickerInputTextListener(
      int largeValue,
      TextEditingController controller,
      FixedExtentScrollController? fxCupertinoPickerController) {
    return () {
      if (largeValue == 12) {
        fxCupertinoPickerController!
            .removeListener(_departureHourPickerListener);
      } else {
        fxCupertinoPickerController!
            .removeListener(_departureMinutePickerListener);
      }
      String text = controller.text;
      if (text != "") {
        int index = int.tryParse(text)!;
        if (index > largeValue) {
          controller.text = text.substring(text.length - 1, text.length);
          controller.selection = TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream,
              offset: controller.text.length));
        } else {
          if (text.length > 2) {
            controller.text = index.toString();
            controller.selection = TextSelection.fromPosition(TextPosition(
                affinity: TextAffinity.downstream,
                offset: controller.text.length));
          } else {
            if (largeValue == 12) {
              fxCupertinoPickerController.jumpToItem(index - 1);
            } else {
              fxCupertinoPickerController.jumpToItem(index);
            }
          }
        }
      }

      if (largeValue == 12) {
        fxCupertinoPickerController.addListener(_departureHourPickerListener);
      } else {
        fxCupertinoPickerController.addListener(_departureMinutePickerListener);
      }
    };
  }

  VoidCallback _timePickerListener(
      int itemCount,
      FixedExtentScrollController? fxCupertinoPickerController,
      TextEditingController controller) {
    return () {
      int index = fxCupertinoPickerController!.selectedItem;
      if (index < 0) {
        index = index % itemCount;
        index = itemCount + index;
      }
      if (index >= itemCount) {
        index = index % itemCount;
      }
      if (itemCount == 12) {
        controller.removeListener(_departureHourEditingListener);
        controller.text = (index + 1).toString();
        controller.addListener(_departureHourEditingListener);
      } else {
        controller.removeListener(_departureMinuteEditingListener);
        controller.text = index.toString();
        controller.addListener(_departureMinuteEditingListener);
      }
    };
  }

  @override
  void dispose() {
    timeOut = 0;
    _daySelectedIndex.close();
    _afternoonSwitchController.close();
    _acStatus.close();
  }
}
