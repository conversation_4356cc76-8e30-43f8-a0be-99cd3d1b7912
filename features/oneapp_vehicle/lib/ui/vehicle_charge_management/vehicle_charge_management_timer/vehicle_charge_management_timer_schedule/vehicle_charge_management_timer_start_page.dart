// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/custom_divider.dart';
import 'package:oneapp_common/widget/time_picker/custom_timer_picker.dart';
import 'package:oneapp_common/widget/time_picker/time_switch.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';

// Project imports:
import 'vehicle_charge_management_timer_start_bloc.dart';

class VehicleChargeManagementTimerStartPage extends StatefulWidget {
  final Function(bool, ChargeManagementDetailEntity?)?
      reloadChargeManagementPage;
  final ChargeInfo? chargeInfo;
  final ScrollController? scrollController;

  VehicleChargeManagementTimerStartPage(
      {this.chargeInfo,
      this.scrollController,
      this.reloadChargeManagementPage});

  @override
  _VehicleChargeManagementTimerStartPageState createState() =>
      _VehicleChargeManagementTimerStartPageState();
}

class _VehicleChargeManagementTimerStartPageState
    extends State<VehicleChargeManagementTimerStartPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  FocusNode hourFocusNode = FocusNode();
  FocusNode timeFocusNode = FocusNode();
  double _viewPickerSqueeze = 1;
  bool _isHourHighlight = false;
  bool _isMinuteHighlight = false;
  double _timePickerHeight = 44;
  double _timePickerWidth = 100;

  VehicleChargeManagementTimerStartBloc _bloc =
      VehicleChargeManagementTimerStartBloc();

  @override
  void initState() {
    super.initState();
    resetProgressState();
    _bloc.init(widget.chargeInfo, _progressHandlerCallback);
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      bloc: _bloc,
      child: OneAppScaffold(
        backgroundColor: _colorUtil.tile01,
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                controller: widget.scrollController,
                child: Container(
                  margin:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 30),
                        child: bottomSheetCustomAppBar(
                            OneAppString.of().newSchedule, onBackPressed: () {
                          Navigator.of(context).pop();
                        }, elevation: 0),
                      ),
                      Container(
                        height: 16.h,
                      ),
                      _timerLayout(OneAppString.of().startTime,
                          OneAppString.of().startTimeSubHeading),
                      Container(
                        height: 8.h,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(bottom: 16.h),
              child: CustomDefaultButton(
                backgroundColor: _colorUtil.button01b,
                buttonTextColor: _colorUtil.button01a,
                text: OneAppString.of().commonConfirm,
                press: _confirmClickListener,
                borderColor: _colorUtil.button01b,
                horizontalPadding: 32,
                verticalPadding: 4,
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _timerLayout(String headingText, String subHeadingText) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: _colorUtil.tile02,
          borderRadius: BorderRadius.all(Radius.circular(CARD_RADIUS_SMALL))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 8.h),
            child: Text(
              headingText,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 24.h, bottom: 24.h),
            child: CustomDivider(
              lineColor: _colorUtil.tertiary10,
            ),
          ),
          Text(
            subHeadingText,
            style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.callout1, _colorUtil.tertiary05),
          ),
          Container(
            margin: EdgeInsets.only(top: 24.h, bottom: 24.h),
            child: _startTimePicker(),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 24.h),
            child: CustomDivider(
              lineColor: _colorUtil.tertiary10,
            ),
          ),
          Text(
            OneAppString.of().dayOfWeek,
            style: TextStyleExtension()
                .newStyleWithColor(_textStyleUtil.body4, _colorUtil.tertiary03),
          ),
          Container(
            height: 44.h,
            width: double.maxFinite,
            margin: EdgeInsets.only(top: 16.h, bottom: 16.h),
            alignment: Alignment.center,
            child: _weekDayListLayout(),
          )
        ],
      ),
    );
  }

  Widget _startTimePicker() {
    return Row(
      children: [
        Container(
          height: _timePickerHeight,
          width: _timePickerWidth,
          child: Stack(
            fit: StackFit.expand,
            children: [
              _inputTimeView(),
              OneAppCupertinoTimerPicker(
                mode: CupertinoTimerPickerMode.hm,
                timerPickerSqueeze: _viewPickerSqueeze,
                hourCupertinoPickerController:
                    _bloc.hourCupertinoPickerController!,
                minuteCupertinoPickerController:
                    _bloc.minuteCupertinoPickerController!,
                textStyle: ThemeConfig.current()
                    .textStyleUtil
                    .title2
                    .copyWith(fontSize: 22, fontWeight: FontWeight.w400),
                isHourHighlight: _isHourHighlight,
                isMinuteHighlight: _isMinuteHighlight,
                initialTimerDuration: Duration(hours: 12, minutes: 00),
                onTimerDurationChanged: (Duration value) {
                  _bloc.onTimePickerChange(value);
                  if (_viewPickerSqueeze == 1) {
                    _changePickerSqueeze(1.45);
                  }
                },
                dragEnd: (bool end) {
                  if (!_bloc.keyBoardPopup) {
                    _changePickerSqueeze(1.h);
                  } else {
                    _changePickerSqueeze(1.45.h);
                  }
                },
                timerHourTapUpCallback: (TapUpDetails details) {
                  _bloc.startHourEditingController.text = "";
                  FocusScope.of(context).requestFocus(hourFocusNode);
                },
                timerTimeTapUpCallback: (TapUpDetails details) {
                  _bloc.startTimeEditingController.text = "";
                  FocusScope.of(context).requestFocus(timeFocusNode);
                },
              ),
            ],
          ),
        ),
        Container(
            margin: EdgeInsets.only(left: 6.w),
            decoration: BoxDecoration(
              color: _colorUtil.tile02,
              borderRadius: BorderRadius.circular(CARD_RADIUS),
            ),
            child: StreamBuilder(
              stream: _bloc.uiAfternoonSwitch,
              builder: (BuildContext context,
                  AsyncSnapshot<bool> uiAfternoonSwitch) {
                return TimerSwitch(
                  value: uiAfternoonSwitch.data ?? false,
                  width: 101.w,
                  height: 36.h,
                  borderRadius: CARD_RADIUS,
                  toggleHeight: 32.h,
                  toggleWidth: 52.w,
                  backColor: _colorUtil.tertiary10,
                  toggleColor: _colorUtil.tertiary15,
                  onToggle: (bool toggle) {
                    _bloc.onForenoonChange(toggle);
                  },
                  textStyle: _textStyleUtil.callout1
                      .copyWith(fontSize: 15, color: _colorUtil.tertiary03),
                );
              },
            )),
      ],
    );
  }

  // Layout for enabling editable time picker
  Widget _inputTimeView() {
    return IgnorePointer(
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _bloc.startHourEditingController,
              focusNode: hourFocusNode,
              cursorColor: _colorUtil.tertiary15,
              style: TextStyle(color: Colors.transparent),
              keyboardType: TextInputType.number,
              inputFormatters: [
                LengthLimitingTextInputFormatter(3),
              ],
              enableInteractiveSelection: false,
              decoration: InputDecoration(
                border: InputBorder.none,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: _bloc.startTimeEditingController,
              focusNode: timeFocusNode,
              cursorColor: _colorUtil.tertiary15,
              style: TextStyle(color: Colors.transparent),
              keyboardType: TextInputType.number,
              inputFormatters: [
                LengthLimitingTextInputFormatter(3),
              ],
              enableInteractiveSelection: false,
              decoration: InputDecoration(
                border: InputBorder.none,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _weekDayListLayout() {
    return StreamBuilder<int>(
        stream: _bloc.daySelectedIndex,
        builder: (context, snapshot) {
          int? selectedIndex = snapshot.hasData ? snapshot.data : 0;
          return ListView.builder(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemCount: weekDayShortList.length,
              itemBuilder: (BuildContext context, int index) {
                String weekDay = weekDayShortList[index];
                return InkWell(
                  splashColor: Colors.transparent,
                  onTap: () {
                    _bloc.daySelected(index);
                  },
                  child: Container(
                    height: 40.w,
                    width: 40.w,
                    decoration: index == selectedIndex
                        ? BoxDecoration(
                            color: _colorUtil.tertiary03,
                            shape: BoxShape.circle,
                          )
                        : null,
                    alignment: Alignment.center,
                    child: Text(
                      weekDay,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1,
                          index == selectedIndex
                              ? _colorUtil.tertiary15
                              : _colorUtil.tertiary03),
                    ),
                  ),
                );
              });
        });
  }

  Widget _toastCustomWidget(String toastMessage, bool isSuccessful) {
    return CommonToast(
      iconColor: _colorUtil.tertiary15,
      iconPath: isSuccessful ? checkIcon : closeIcon,
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
    );
  }

  // Size measure for spread in time picker spinner
  void _changePickerSqueeze(double squeeze) {
    setState(() {
      _viewPickerSqueeze = squeeze;
      if (_bloc.keyBoardPopup) {
        if (_bloc.inputHour) {
          _isHourHighlight = true;
          _isMinuteHighlight = false;
        } else {
          _isMinuteHighlight = true;
          _isHourHighlight = false;
        }
      } else {
        _isHourHighlight = false;
        _isMinuteHighlight = false;
      }
    });
  }

  void _confirmClickListener() {
    _bloc.confirmNewTimerSettings(_savedResponseCallback);
  }

  void _savedResponseCallback(
      bool isSuccessful, ChargeManagementDetailEntity? detail) {
    if (isSuccessful) {
      showCustomToast(
          _toastCustomWidget(
              OneAppString.of().chargeScheduleSuccess, isSuccessful),
          3);
      widget.reloadChargeManagementPage!(true, detail);
      Navigator.of(context).pop();
    } else {
      showCustomToast(
          _toastCustomWidget(
              OneAppString.of().chargeScheduleFailure, isSuccessful),
          3);
    }
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }
}
