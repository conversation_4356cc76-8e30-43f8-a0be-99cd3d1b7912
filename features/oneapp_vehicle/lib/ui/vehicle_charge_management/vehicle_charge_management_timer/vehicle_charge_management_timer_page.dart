// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/custom_divider.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';

// Project imports:
import '../vehicle_charge_management_bloc.dart';
import 'vehicle_charge_management_timer_bloc.dart';
import 'vehicle_charge_management_timer_schedule/vehicle_charge_management_timer_departure_page.dart';
import 'vehicle_charge_management_timer_schedule/vehicle_charge_management_timer_start_page.dart';

class VehicleChargeManagementTimerPage extends StatefulWidget {
  final ChargeInfo? chargeInfo;
  final String? acquisitionDatetime;
  final ScrollController? scrollController;
  final BuildContext? materialContext;
  final Function(bool, ChargeManagementDetailEntity?)?
      reloadChargeManagementPage;
  final VehicleChargeManagementBloc? vehicleBloc;

  VehicleChargeManagementTimerPage(
      {this.chargeInfo,
      this.acquisitionDatetime,
      this.scrollController,
      this.materialContext,
      this.reloadChargeManagementPage,
      this.vehicleBloc});

  @override
  _VehicleChargeManagementTimerPageState createState() =>
      _VehicleChargeManagementTimerPageState();
}

class _VehicleChargeManagementTimerPageState
    extends State<VehicleChargeManagementTimerPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  VehicleChargeManagementTimerBloc _bloc = VehicleChargeManagementTimerBloc();
  int? toggleValue;

  @override
  void initState() {
    super.initState();
    _bloc.init(widget.chargeInfo, widget.acquisitionDatetime,
        _progressHandlerCallback);
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      bloc: _bloc,
      child: OneAppScaffold(
        backgroundColor: _colorUtil.tile01,
        body: SingleChildScrollView(
          controller: widget.scrollController,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            child: StreamBuilder<ChargeInfo?>(
                stream: widget.vehicleBloc!.chargeInfoValue,
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    if (widget.vehicleBloc?.chargeInfo?.chargeType != null) {
                      int? chargeType =
                          widget.vehicleBloc!.chargeInfo!.chargeType;
                      if (chargeType == 1) {
                        // Start Time
                        toggleValue = 1;
                      } else if (chargeType == 2 || chargeType == 3) {
                        // Departure Time
                        toggleValue = 0;
                      } else {
                        toggleValue = 2;
                      }
                    }
                  }
                  // int toggleValue = snapshot.hasData ? snapshot.data : 2;
                  return Column(
                    children: [
                      Container(
                        height: 16.h,
                      ),
                      InkWell(
                        onTap: () {
                          if (widget.vehicleBloc?.chargeInfo?.chargeType !=
                              null) {
                            if (widget.vehicleBloc!.chargeInfo!.chargeType !=
                                    0 &&
                                widget.vehicleBloc!.chargeInfo!.chargeType !=
                                    15) {
                              Navigator.of(widget.materialContext!).push(
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          VehicleChargeManagementTimerStartPage(
                                              chargeInfo: widget
                                                  .vehicleBloc!.chargeInfo,
                                              scrollController:
                                                  widget.scrollController,
                                              reloadChargeManagementPage:
                                                  (reload, detail) {
                                                _reloadChargeManagementPage(
                                                    reload, detail);
                                              })));
                            } else {
                              _setTimerInVehicleToast();
                            }
                          } else {
                            _setTimerInVehicleToast();
                          }
                        },
                        child: _timerLayout(
                            OneAppString.of().startTime,
                            OneAppString.of().startTimeSubHeading,
                            toggleValue == 1 ? true : false),
                      ),
                      Container(
                        height: 8.h,
                      ),
                      InkWell(
                        onTap: () {
                          if (widget.vehicleBloc?.chargeInfo?.chargeType !=
                              null) {
                            if (widget.vehicleBloc!.chargeInfo!.chargeType !=
                                    0 &&
                                widget.vehicleBloc!.chargeInfo!.chargeType !=
                                    15) {
                              Navigator.of(widget.materialContext!).push(
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          VehicleChargeManagementTimerDeparturePage(
                                              chargeInfo: widget
                                                  .vehicleBloc!.chargeInfo,
                                              scrollController:
                                                  widget.scrollController,
                                              reloadChargeManagementPage:
                                                  (reload, detail) {
                                                _reloadChargeManagementPage(
                                                    reload, detail);
                                              })));
                            } else {
                              _setTimerInVehicleToast();
                            }
                          } else {
                            _setTimerInVehicleToast();
                          }
                        },
                        child: _timerLayout(
                            OneAppString.of().departureTime,
                            OneAppString.of().departureTimeSubHeading,
                            toggleValue == 0 ? true : false),
                      ),
                      StreamBuilder<bool>(
                        stream: widget.vehicleBloc!.showStartCharge,
                        builder: (context, snapshot) {
                          return (snapshot.hasData && snapshot.data == true)
                              ? Container()
                              : Container(
                                  child: Center(
                                    child: _refreshIconLayout(),
                                  ),
                                );
                        },
                      ),
                    ],
                  );
                }),
          ),
        ),
      ),
    );
  }

  // Common layout for both start and departure time
  Widget _timerLayout(
      String headingText, String subHeadingText, bool switchStatus) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: _colorUtil.tile02,
          borderRadius: BorderRadius.all(Radius.circular(CARD_RADIUS_SMALL))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child: Text(
                headingText,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
              )),
              Container(
                  margin: EdgeInsets.only(left: 8.w),
                  child: Container(
                    padding: EdgeInsets.only(
                        left: 22.w, right: 22.w, top: 8.h, bottom: 8.h),
                    decoration: BoxDecoration(
                        border: Border.all(
                          color: switchStatus
                              ? _colorUtil.secondary02
                              : _colorUtil.button05b,
                        ),
                        color: switchStatus
                            ? _colorUtil.secondary02
                            : _colorUtil.button05b,
                        borderRadius: BorderRadius.all(Radius.circular(22.r))),
                    child: Text(
                        switchStatus
                            ? OneAppString.of().onText
                            : OneAppString.of().offText,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1, _colorUtil.button02a)),
                  ))
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 24.h, bottom: 24.h),
            child: CustomDivider(
              lineColor: _colorUtil.tertiary10,
            ),
          ),
          Container(
            margin: EdgeInsets.only(bottom: 8.h),
            child: Text(
              subHeadingText,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary05),
            ),
          ),
        ],
      ),
    );
  }

  Widget _refreshIconLayout() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        children: [
          InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              _bloc.refreshLastUpdatedData(
                  _savedResponseCallback, _reloadChargeManagementPage);
            },
            child: CircleAvatar(
              radius: 24.0.r,
              backgroundColor: _colorUtil.button02d,
              child: SvgPicture.asset(
                refreshIcon,
                colorFilter: ColorFilter.mode(
                  _colorUtil.button02a,
                  BlendMode.srcIn,
                ),
                semanticsLabel: REFRESH_BUTTON,
              ),
            ),
          ),
          StreamBuilder<String>(
              stream: widget.vehicleBloc!.acquisitionDateTimeValue,
              builder: (context, snapshot) {
                return Container(
                  padding: EdgeInsets.only(bottom: 8.h),
                  margin: EdgeInsets.only(top: 8.h),
                  child: Text(
                    snapshot.hasData ? snapshot.data! : " - ",
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.caption1, _colorUtil.tertiary05),
                  ),
                );
              }),
        ],
      ),
    );
  }

  Widget _toastCustomWidget() {
    return CommonToast(
      iconColor: _colorUtil.tertiary15,
      iconPath: closeIcon,
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: OneAppString.of().setTimerInVehicle,
    );
  }

  Widget _responseToastCustomWidget(String toastMessage, bool isSuccessful) {
    return CommonToast(
      iconColor: _colorUtil.tertiary15,
      iconPath: isSuccessful ? checkIcon : closeIcon,
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
    );
  }

  void _savedResponseCallback() {
    showCustomToast(
        _responseToastCustomWidget(
            OneAppString.of().loginUnableProcessYourRequest, false),
        3);
  }

  void _reloadChargeManagementPage(
      bool reload, ChargeManagementDetailEntity? detail) {
    // Future.delayed(const Duration(milliseconds: 500), () {
    _progressHandlerCallback(false);
    widget.reloadChargeManagementPage!(reload, detail);
    // });
  }

  void _setTimerInVehicleToast() {
    showCustomToast(_toastCustomWidget(), 3);
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }
}
