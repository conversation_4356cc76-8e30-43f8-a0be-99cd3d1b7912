// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

// Package imports:
import 'package:ev_module/charge_info/widgets/helper/range_info.dart';
import 'package:intl/intl.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network/api_config.dart';
import 'package:oneapp_network/oneapp_network_packages.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/ca/entity/ca_dataconsent_entity.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/charge_timer_body_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '/local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/mc/entity/schedule_listing_entity.dart'
    as se;

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleChargeManagementBloc extends BlocBase {
  vehicleInfo.Payload? vehicleInfoEntity;
  OneAppClient api = APIClientConfig.oneAppClient;
  ChargeInfo? chargeInfo;
  String? acquisitionDatetime;
  VehicleInfo? vehicleInfoPayload;
  bool? isMultidayChargingEnabled;
  List<String> tabList = [];
  int timeOut = 180;
  late Function(bool) progressHandlerCallback;
  Timer? _chargeInfoTimer;
  bool isEV = false;

  DateTime? _previousOccurrenceDate;

  Stream<bool> get is21MMGeneration => _is21MMGeneration.stream;
  final _is21MMGeneration = BehaviorSubject<bool>();

  Stream<ChargeInfo?> get chargeInfoValue => _chargeInfoValue.stream;
  final _chargeInfoValue = BehaviorSubject<ChargeInfo?>();

  Stream<String> get acquisitionDateTimeValue =>
      _acquisitionDateTimeValue.stream;
  final _acquisitionDateTimeValue = BehaviorSubject<String>();

  Stream<bool> get isEvPhpVehicle => _isEvPhpVehicle.stream;
  final _isEvPhpVehicle = BehaviorSubject<bool>();

  Stream<bool> get isPublicChargingControlAllowed =>
      _isPublicChargingControlAllowed.stream;
  final _isPublicChargingControlAllowed = BehaviorSubject<bool>();

  Stream<bool> get isWalletFeatureEnabled => _isWalletFeatureEnabled.stream;
  final _isWalletFeatureEnabled = BehaviorSubject<bool>();

  Stream<bool> get isCV17Vehicle => _isCV17Vehicle.stream;
  final _isCV17Vehicle = BehaviorSubject<bool>();

  Stream<int> get chargeRemaining => _chargeRemaining.stream;
  final _chargeRemaining = BehaviorSubject<int>();

  Stream<String> get distanceWithoutAC => _distanceWithoutAC.stream;
  final _distanceWithoutAC = BehaviorSubject<String>();

  Stream<String?> get distanceUnit => _distanceUnit.stream;
  final _distanceUnit = BehaviorSubject<String?>();

  Stream<String> get chargeDescription => _chargeDescription.stream;
  final _chargeDescription = BehaviorSubject<String>();

  Stream<String> get chargeStatusBadgeText => _chargeStatusBadgeText.stream;
  final _chargeStatusBadgeText = BehaviorSubject<String>();

  Stream<List<String>> get tabTitleList => _tabTitleList.stream;
  final _tabTitleList = BehaviorSubject<List<String>>();

  Stream<bool> get isBatteryLow => _isBatteryLow.stream;
  final _isBatteryLow = BehaviorSubject<bool>();

  Stream<bool> get isCharging => _isCharging.stream;
  final _isCharging = BehaviorSubject<bool>();

  Stream<bool> get isChargingOrWaiting => _isChargingOrWaiting.stream;
  final _isChargingOrWaiting = BehaviorSubject<bool>();

  Stream<bool> get isEmptyPage => _isEmptyPage.stream;
  final _isEmptyPage = BehaviorSubject<bool>();

  Stream<bool> get isLCFSEnabled => _isLCFSEnabled.stream;
  final _isLCFSEnabled = BehaviorSubject<bool>();

  Stream<bool> get isLCFSShowGraph => _isLCFSShowGraph.stream;
  final _isLCFSShowGraph = BehaviorSubject<bool>();

  Stream<bool> get isLCFSEligible => _isLCFSEligible.stream;
  final _isLCFSEligible = BehaviorSubject<bool>();

  Stream<bool> get showEcoHistory => _showEcoHistory.stream;
  final _showEcoHistory = BehaviorSubject<bool>();

  Stream<bool?> get isMultidayChargingAllowed =>
      _isMultidayChargingAllowed.stream;
  final _isMultidayChargingAllowed = BehaviorSubject<bool?>();

  Stream<ScheduleType> get scheduleType => _scheduleType.stream;
  final _scheduleType = BehaviorSubject<ScheduleType>();

  Stream<bool> get showSchedule => _showSchedule.stream;
  final _showSchedule = BehaviorSubject<bool>();

  Stream<String> get chargeButtonText => _chargeButtonText.stream;
  final _chargeButtonText = BehaviorSubject<String>();

  Stream<bool> get showStartCharge => _showStartCharge.stream;
  final _showStartCharge = BehaviorSubject<bool>();

  Stream<bool> get scheduleLimitReached => _scheduleLimitReached.stream;
  final _scheduleLimitReached = BehaviorSubject<bool>();

  Stream<String> get reloadMultidaySchedules => _reloadMultidaySchedules.stream;
  final _reloadMultidaySchedules = BehaviorSubject<String>();

  Stream<RangeInfo?> get rangeInfo => _rangeInfo.stream;
  final _rangeInfo = BehaviorSubject<RangeInfo?>();

  StreamSubscription? _vehicleInfoSubscription;

  void init(Function(bool) progressHandler) {
    progressHandlerCallback = progressHandler;
    tabList.clear();

    /// code change for 032D project
    /// to Rearange  the  tab view  change here
    // tabList.add(OneAppString.of().schedule);
    // tabList.add(OneAppString.of().ecoHistory);
    // tabList.add(OneAppString.of().ranking);
    // tabList.add(OneAppString.of().cleanAssist);

    _initAsync().whenComplete(() => FireBaseAnalyticsLogger.logInfo(
        "at=VehicleChargeManagementBloc status=init-async-complete"));
    checkCleanAssistEligibility();
    String? vin = Global.getInstance().vin;
    VehicleRepo().getEVVehicleInfoRepository(vin).then(
        (repository) async => await repository.triggerVehicleToUpdateTSC());
  }

  Future<void> _initAsync() async {
    _showStartCharge.sink.add(false);
    String? vin = Global.getInstance().vin;
    vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);

    isEV = (vehicleInfoEntity != null) ? isEvModel(vehicleInfoEntity!) : false;
    bool isCY17 = isCY17Vehicle(vehicleInfoEntity!.generation);
    bool isCY17PLUS = isCY17PlusVehicle(vehicleInfoEntity!.generation);
    bool _is21MMVehicle = is21MMVehicle(vehicleInfoEntity!.generation);
    _is21MMGeneration.sink.add(_is21MMVehicle);
    bool _isEvPhpModel = isEvPhpModel(vehicleInfoEntity!);
    _isEvPhpVehicle.sink.add(_isEvPhpModel);
    isMultidayChargingEnabled =
        isMultidayChargingFeatureEnabled(vehicleInfoEntity!.features!);
    _isMultidayChargingAllowed.sink.add(isMultidayChargingEnabled);

    bool isEcoHistoryEnabled =
        isFeatureEnabled(ECOHISTORY, vehicleInfoEntity!.features);
    bool isEcoRankingEnabled =
        isFeatureEnabled(ECORANKING, vehicleInfoEntity!.features);
    bool isLCFSEnabled = (vehicleInfoEntity != null)
        ? isLcfsEnabled(vehicleInfoEntity!.features!)
        : false;
    bool _isEvPublicChargingControlEnabled =
        isEvPublicChargingControlEnabled(vehicleInfoEntity!.features!);
    _isPublicChargingControlAllowed.sink.add(_isEvPublicChargingControlEnabled);
    bool _walletFeatureEnabled =
        isFeatureEnabled(WALLET, vehicleInfoEntity!.features);
    _isWalletFeatureEnabled.sink.add(_walletFeatureEnabled);

    _isCV17Vehicle.sink.add(isCY17);

    if ((isCY17PLUS && _isEvPhpModel) ||
        (_is21MMVehicle && _isEvPhpModel) ||
        isMultidayChargingEnabled!) {
      tabList.add(OneAppString.of().schedule);
      _showSchedule.sink.add(true);
    }

    isMultidayChargingEnabled!
        ? _scheduleType.sink.add(ScheduleType.MULTIDAY)
        : _scheduleType.sink.add(ScheduleType.NONMULTIDAY);

    if (isEcoHistoryEnabled) {
      tabList.add(OneAppString.of().ecoHistory);
      _showEcoHistory.sink.add(true);
    }
    if (isEcoRankingEnabled) {
      tabList.add(OneAppString.of().ranking);
    }
    if (isLCFSEnabled) {
      tabList.add(OneAppString.of().cleanAssist);
      _isLCFSEnabled.sink.add(true);
    }

    final evRepository = await VehicleRepo().getEVVehicleInfoRepository(vin);
    _vehicleInfoSubscription = evRepository.evVehicleInfo.listen((vehicleInfo) {
      vehicleInfoPayload = vehicleInfo;
      if (vehicleInfoPayload != null) {
        _updateUIWithVehicleInfo();
      }
    });
    vehicleInfoPayload = evRepository.lastReceived;
    _updateUIWithVehicleInfo();
  }

  void _updateUIWithVehicleInfo() {
    if (vehicleInfoPayload == null) {
      return;
    }
    acquisitionDatetime = vehicleInfoPayload!.acquisitionDatetime;
    chargeInfo = vehicleInfoPayload!.chargeInfo;
    _setLastUpdatedDate(acquisitionDatetime);

    if (chargeInfo != null) {
      _chargeInfoValue.sink.add(chargeInfo);
      _isCharging.sink.add(false);

      int chargeRemaining = chargeInfo!.chargeRemainingAmount == null
          ? 0
          : chargeInfo!.chargeRemainingAmount!;
      _chargeRemaining.sink.add(chargeRemaining);
      _isBatteryLow.sink.add(chargeRemaining <= 30);
      _distanceUnit.sink.add(chargeInfo!.evDistanceUnit);

      String timeRemaining =
          getTimeFormattedDuration(chargeInfo!.remainingChargeTime ?? 0);
      String distanceRemaining =
          checkForZeroWithTrimmedDecimal(chargeInfo!.evDistance!, 1) +
              " " +
              chargeInfo!.evDistanceUnit!;

      _isCharging.sink.add(chargeInfo!.isCharging);
      _isChargingOrWaiting.sink.add(chargeInfo!.isChargingOrWaiting);

      debugPrint(
          "${DateTime.now().toIso8601String()} at=charge-info-interpret step=start");
      if (chargeInfo!.isCharging) {
        _chargeDescription.sink.add(timeRemaining.isEmpty
            ? ""
            : isMultidayChargingEnabled!
                ? 'Est. ${OneAppString.of().timeUntilFullyCharged(timeRemaining)}'
                : OneAppString.of().timeUntilFullyCharged(timeRemaining));
        _chargeStatusBadgeText.sink.add(OneAppString.of().charging);
        _showStartCharge.sink.add(false);
      } else if (chargeInfo!.isPlugWaitingForTimerToCharge) {
        _chargeDescription.sink
            .add(OneAppString.of().distanceRemaining(distanceRemaining));
        _chargeStatusBadgeText.sink.add(OneAppString.of().pluggedIn);
        _chargeButtonText.sink.add(OneAppString.of().startCharging);
        _showStartCharge.sink.add(true);
      } else {
        debugPrint(
            "${DateTime.now().toIso8601String()} at=charge-info-interpret step=else");
        _distanceWithoutAC.sink
            .add(checkForZeroWithTrimmedDecimal(chargeInfo!.evDistance!, 1));
        if (isEV) {
          _rangeInfo.sink.add(RangeInfo(
              range: chargeInfo?.evDistance ?? 0.0,
              rangeWithAC: chargeInfo?.evDistanceAC ?? 0.0,
              unit:
                  chargeInfo?.evDistanceUnit ?? OneAppString.of().short_miles));
        }
        debugPrint(
            "${DateTime.now().toIso8601String()} at=charge-info-interpret step=distance-without-ac");
        if (isMultidayChargingEnabled ?? false) {
          _chargeDescription.sink.add(OneAppString.of().withoutAC);
          debugPrint(
              "${DateTime.now().toIso8601String()} at=charge-info-interpret step=charge-off");
        } else {
          _chargeDescription.sink.add(OneAppString.of().orDistanceWithACon(
              checkForZeroWithTrimmedDecimal(chargeInfo!.evDistanceAC!, 1) +
                  " " +
                  chargeInfo!.evDistanceUnit!));
          debugPrint(
              "${DateTime.now().toIso8601String()} at=charge-info-interpret step=charge-full");
        }
        _chargeButtonText.sink.add(OneAppString.of().startPowerMode);
        _showStartCharge.sink.add(false);
        debugPrint(
            "${DateTime.now().toIso8601String()} at=charge-info-interpret step=else-end");
      }
    } else {
      _isEmptyPage.sink.add(true);
    }
  }

  void _setLastUpdatedDate(String? lastUpdatedDate) {
    if (lastUpdatedDate != null && lastUpdatedDate.isNotEmpty) {
      String lastUpdated = lastUpdatedDate;
      DateTime dateTime =
          DateFormat("yyyy-MM-ddTHH:mm:ssZ").parse(lastUpdated, true).toLocal();
      String updatedTime = "";
      String? todayOrYesterday = checkDateIsTodayOrYesterday(dateTime);
      if (todayOrYesterday != null) {
        updatedTime = todayOrYesterday +
            convertDateTimeFormat(
                lastUpdated, "yyyy-MM-ddTHH:mm:ssZ", " @ h:mma");
      } else {
        updatedTime = convertDateTimeFormat(
            lastUpdated, "yyyy-MM-ddTHH:mm:ssZ", "MMM dd @ h:mma");
      }
      updatedTime = updatedTime.replaceAll('@', OneAppString.of().at);
      _acquisitionDateTimeValue.sink
          .add(OneAppString.of().updated(updatedTime));
    }
  }

  void refreshUI(ChargeManagementDetailEntity? detail) {
    tabList.clear();
    _tabTitleList.sink.add([]);
    // tabList.add(OneAppString.of().schedule);
    checkCleanAssistEligibility();
    if (detail != null) {
      String? vin = Global.getInstance().vin;
      final repoFuture = VehicleRepo().getEVVehicleInfoRepository(vin);
      repoFuture.then(
        (repository) => repository.receivedExtraChargeManagementDetail(detail),
      );
    }
  }

  void updateFloatingUI(List<se.TimerChargeInfo> _listingData) {
    _scheduleLimitReached.sink.add(_listingData.length == 15);
  }

  void refreshMultidaySchedules(String requestNo) {
    _reloadMultidaySchedules.sink.add(requestNo);
  }

  // Start Charge option
  Future<void> startChargeRequest(Function savedSuccessfullyBottomSheet) async {
    String vin = Global.getInstance().vin!;
    vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
    ChargeTimerBodyHelper chargeTimerBodyHelper = ChargeTimerBodyHelper(
        command: IMMEDIATE_CHARGE, remoteHvac: null, reservationCharge: null);
    progressHandlerCallback(true);
    final commonResponse = await api.postVehicleChargeTimerRequest(
        vehicleInfoEntity!.brand!,
        vin,
        vehicleInfoEntity!.generation!,
        Global.getInstance().fcmDeviceId!,
        chargeTimerBodyHelper);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      int startTime = DateTime.now().millisecondsSinceEpoch;
      if (payLoad.returnCode == "ONE-RES-10000") {
        if (isPRECY17Vehicle(vehicleInfoEntity!.generation!)) {
          fetchVehicleClimateStatusPreCY17(
              vin, startTime, savedSuccessfullyBottomSheet);
        } else {
          fetchChargeManagementStatus(
              startTime, payLoad.appRequestNo, savedSuccessfullyBottomSheet);
        }
      }
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_CHARGE_TIMER_SETTING_SUCCESS,
          category: LogCategory.FL_VEHI);
    } else {
      progressHandlerCallback(false);
    }

    if (commonResponse.error != null) {
      progressHandlerCallback(false);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_CHARGE_TIMER_SETTING_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  Future<void> fetchChargeManagementStatus(int startTime, String? requestNo,
      Function savedSuccessfullyBottomSheet) async {
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.fetchChargeManagementTimerStatus(
        vehicleInfoEntity!.generation!,
        vin,
        vehicleInfoEntity!.brand ?? Global.getInstance().appBrand,
        requestNo: requestNo);
    final chargeManagementDetail = commonResponse.response;
    final payLoad = chargeManagementDetail?.payload;
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);
      if (payLoad.remoteControlResult != null) {
        int? status = payLoad.remoteControlResult!.status;
        int? result = payLoad.remoteControlResult!.result;
        if (status != null) {
          if (status == 0) {
            progressHandlerCallback(false);
            if (result == 0) {
              refreshUI(chargeManagementDetail);
            } else if (result == 1) {
              errorCallBack(savedSuccessfullyBottomSheet);
            } else if (result == 2) {
              errorCallBack(savedSuccessfullyBottomSheet);
            } else if (result == 3) {
              errorCallBack(savedSuccessfullyBottomSheet);
            } else {
              errorCallBack(savedSuccessfullyBottomSheet);
            }
          } else {
            int now = DateTime.now().millisecondsSinceEpoch;
            double timeElapsed = (now - startTime) / 1000;
            if (timeElapsed >= timeOut) {
              progressHandlerCallback(false);
              errorCallBack(savedSuccessfullyBottomSheet);
            } else {
              fetchChargeManagementStatus(
                  startTime, requestNo, savedSuccessfullyBottomSheet);
            }
          }
        } else {
          errorCallBack(savedSuccessfullyBottomSheet);
          progressHandlerCallback(false);
        }
      }
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        fetchVehicleChargeStatus(
            startTime, requestNo!, savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
            category: LogCategory.FL_VEHI);
      } else {
        progressHandlerCallback(false);
        errorCallBack(savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  Future<void> fetchVehicleClimateStatusPreCY17(
      String vin, int startTime, Function savedSuccessfullyBottomSheet) async {
    final commonResponse = await api.fetchChargeManagementTimerStatus(
        vehicleInfoEntity!.generation!,
        vin,
        vehicleInfoEntity!.brand ?? Global.getInstance().appBrand);
    final chargeManagementDetail = commonResponse.response;
    final payLoad = chargeManagementDetail?.payload;
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_PRE_CY17_STATUS_SUCCESS,
          category: LogCategory.FL_VEHI);
      if (payLoad.returnCode != null &&
          (payLoad.returnCode == ELECTRIC_STATUS_RETURN_CODE_SUCCESS ||
              payLoad.returnCode!.isEmpty) &&
          payLoad.vehicleStatusResult != null &&
          payLoad.vehicleStatusResult!.occurrenceDate != null) {
        DateTime occurrenceDate = DateFormat(CTP_DATE_FORMAT)
            .parse(payLoad.vehicleStatusResult!.occurrenceDate!, true)
            .toLocal();

        if (_previousOccurrenceDate == null) {
          _previousOccurrenceDate = occurrenceDate;
        }
        if (!occurrenceDate.isAfter(_previousOccurrenceDate!)) {
          int now = DateTime.now().millisecondsSinceEpoch;
          double timeElapsed = (now - startTime) / 1000;
          if (timeElapsed >= timeOut) {
            failureCase(savedSuccessfullyBottomSheet);
          } else {
            Future.delayed(const Duration(milliseconds: 3000), () {
              fetchVehicleClimateStatusPreCY17(
                  vin, startTime, savedSuccessfullyBottomSheet);
            });
          }
        } else {
          _previousOccurrenceDate = occurrenceDate; //Updating to latest
          refreshUI(chargeManagementDetail);
          progressHandlerCallback(false); //Stopping the spinner
        }
      } else {
        failureCase(savedSuccessfullyBottomSheet);
      }
    }
  }

  void failureCase(Function bottomSheet) {
    errorCallBack(bottomSheet);
    progressHandlerCallback(false);
  }

  // Check climate status after changing the settings
  Future<void> fetchVehicleChargeStatus(int startTime, String requestNo,
      Function savedSuccessfullyBottomSheet) async {
    String vin = Global.getInstance().vin!;
    final commonResponse = await api.fetchClimateRealTimeStatus(
        vehicleInfoEntity!.generation!,
        vin,
        requestNo,
        vehicleInfoEntity!.brand ?? Global.getInstance().appBrand);
    final chargeManagementDetail = commonResponse.response;
    final payLoad = chargeManagementDetail?.payload;
    if (payLoad != null) {
      int? status = payLoad.realtimeStatusResult?.status;
      int? result = payLoad.realtimeStatusResult?.result;
      if (status != null && status == 0) {
        if (result != null) {
          if (result == 0) {
            progressHandlerCallback(false);
            refreshUI(chargeManagementDetail);
          } else if (result == 1) {
            progressHandlerCallback(false);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 2) {
            progressHandlerCallback(false);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 3) {
            progressHandlerCallback(false);
            errorCallBack(savedSuccessfullyBottomSheet);
          }
        } else {
          errorCallBack(savedSuccessfullyBottomSheet);
          progressHandlerCallback(false);
        }
      } else {
        int currentTime = DateTime.now().millisecondsSinceEpoch;
        double timeElapsed = (currentTime - startTime) / 1000;
        if (timeElapsed >= timeOut) {
          progressHandlerCallback(false);
          errorCallBack(savedSuccessfullyBottomSheet);
        } else {
          fetchVehicleChargeStatus(
              startTime, requestNo, savedSuccessfullyBottomSheet);
        }
      }
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        fetchVehicleChargeStatus(
            startTime, requestNo, savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      } else {
        errorCallBack(savedSuccessfullyBottomSheet);
        progressHandlerCallback(false);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }

    if (commonResponse.error != null) {
      if (commonResponse.error!.errorCode != null &&
          commonResponse.error!.errorCode == 500) {
        fetchVehicleChargeStatus(
            startTime, requestNo, savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      } else {
        progressHandlerCallback(false);
        errorCallBack(savedSuccessfullyBottomSheet);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  Future<void> refreshLastUpdatedData(Function savedSuccessfullyBottomSheet,
      Function reloadPage, bool loadingView) async {
    String? vin = Global.getInstance().vin;
    String? deviceId = Global.getInstance().fcmDeviceId;
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    if (vehicleItem != null) {
      if (loadingView) progressHandlerCallback(true);
      int startTime = DateTime.now().millisecondsSinceEpoch;
      final postResponse = await api.postRealTimeStatusRequest(
          vin!, vehicleItem.brand!, vehicleItem.generation ?? "", deviceId!);
      final payLoad = postResponse.response?.payload;
      // progressHandlerCallback(false);
      if (payLoad != null) {
        if (payLoad.returnCode == REALTIME_STATUS_RETURN_CODE_SUCCESS) {
          // progressHandlerCallback(true);
          fetchVehicleChargeStatus(startTime, payLoad.appRequestNo ?? '',
              savedSuccessfullyBottomSheet);
        } else {
          // progressHandlerCallback(true);
          fetchVehicleChargeStatus(
              startTime, payLoad.appRequestNo!, savedSuccessfullyBottomSheet);
        }
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_SUCCESS,
            category: LogCategory.FL_VEHI);
      }

      if (postResponse.error != null) {
        progressHandlerCallback(false);
        FireBaseAnalyticsLogger.logError(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  void errorCallBack(Function savedSuccessfullyBottomSheet) {
    Future.delayed(const Duration(milliseconds: 500), () {
      savedSuccessfullyBottomSheet();
    });
  }

  void checkCleanAssistEligibility() async {
    CommonResponse<CAEligibilityEntity> response =
        await api.checkCleanAssistEligibility(
            Global.getInstance().vin!,
            APIConfig.xApiKey,
            Global.getInstance().appBrand,
            Global.getInstance().guid!);
    final payload = response.response?.payload;
    if (payload != null) {
      // Success
      if (payload.lcfsEligible != null && payload.lcfsEligible == "true") {
        _isLCFSEligible.sink.add(true);
      } else {
        _isLCFSEligible.sink.add(false);
      }
      if (payload.lcfsOptIn != null && payload.lcfsOptIn == 'In') {
        _isLCFSShowGraph.sink.add(true);
      } else {
        _isLCFSShowGraph.sink.add(false);
      }
    } else {
      // Failure
      _isLCFSEligible.sink.add(false);
      _isLCFSShowGraph.sink.add(false);
    }
  }

  @override
  void dispose() {
    _is21MMGeneration.close();
    _isEvPhpVehicle.close();
    _isPublicChargingControlAllowed.close();
    _isWalletFeatureEnabled.close();
    _isMultidayChargingAllowed.close();
    _isCV17Vehicle.close();
    _isBatteryLow.close();
    _chargeRemaining.close();
    _distanceWithoutAC.close();
    _distanceUnit.close();
    _chargeDescription.close();
    _chargeStatusBadgeText.close();
    _tabTitleList.close();
    _isCharging.close();
    _isChargingOrWaiting.close();
    _isEmptyPage.close();
    _chargeButtonText.close();
    _showStartCharge.close();
    _scheduleLimitReached.close();
    _reloadMultidaySchedules.close();
    _isLCFSEnabled.close();
    _isLCFSShowGraph.close();
    _showEcoHistory.close();
    _scheduleType.close();
    _chargeInfoValue.close();
    _acquisitionDateTimeValue.close();
    _showSchedule.close();
    _isLCFSEligible.close();
    _chargeInfoTimer?.cancel();
    _vehicleInfoSubscription?.cancel();
    _rangeInfo.close();
  }
}
