// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/eco_ranking_chart_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/user_ranking_list_helper.dart';
import 'package:rxdart/rxdart.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/eco_history_detail_entity.dart'
    as ecoHistoryDetail;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleChargeManagementRankingBloc extends BlocBase {
  ecoHistoryDetail.EcoHistoryDetailPayload? ecoHistoryDetailPayload;

  Stream<List<EcoRankingEntity>> get ecoRankingList => _ecoRankingList.stream;
  final _ecoRankingList = BehaviorSubject<List<EcoRankingEntity>>();

  Stream<UserRankingModel> get userRankingEntity => _userRankingEntity.stream;
  final _userRankingEntity = BehaviorSubject<UserRankingModel>();

  Stream<List<ecoHistoryDetail.RankingDetail>?> get rankingDetailList =>
      _rankingDetailList.stream;
  final _rankingDetailList =
      BehaviorSubject<List<ecoHistoryDetail.RankingDetail>?>();
  String? vehicleNickName, vehicleName;

  vehicleInfo.Payload? vehicleInfoPayload;

  Stream<bool> get isEmptyPage => _isEmptyPage.stream;
  final _isEmptyPage = BehaviorSubject<bool>();

  void init(ecoHistoryDetail.EcoHistoryDetailPayload? ecoHistoryPayload,
      vehicleInfo.Payload? vehicleInfoEntity) {
    ecoHistoryDetailPayload = ecoHistoryPayload;
    vehicleInfoPayload = vehicleInfoEntity;
    if (ecoHistoryDetailPayload != null) {
      _isEmptyPage.sink.add(false);
      List<ecoHistoryDetail.MonthlyRanking>? monthlyRankingList =
          ecoHistoryDetailPayload!.resESPo!.monthlyRanking;
      List<EcoRankingEntity> ecoHistoryList = [];
      List<ecoHistoryDetail.RankingDetail>? rankingList = [];
      _rankingDetailList.sink.add(rankingList);
      bool hasNonZero = false;
      if (monthlyRankingList != null) {
        monthlyRankingList = monthlyRankingList.reversed.toList();
        for (final ecoHistoryDetail.MonthlyRanking item in monthlyRankingList) {
          List dateArray = item.rankingMonth!.split(' ');
          String month = dateArray[1];
          EcoRankingEntity ecoRankingEntity = EcoRankingEntity(int.parse(month),
              item.yourRanking != null ? double.parse(item.yourRanking!) : 0);
          if (ecoRankingEntity.value != 0) hasNonZero = true;
          ecoHistoryList.add(ecoRankingEntity);
        }
        if (hasNonZero) {
          _ecoRankingList.sink.add(ecoHistoryList);
        } else {
          _ecoRankingList.sink.add([]);
        }
        String? userFuelEfficiency = ecoHistoryDetailPayload!
            .resESPo!.monthlyInformation?.first.fuelEfficiency?.value;
        String? userMonthMileage = ecoHistoryDetailPayload!
            .resESPo!.monthlyInformation?.first.oneMonthMileage?.value;
        String topValue =
            (userFuelEfficiency != null && userFuelEfficiency != "null")
                ? userFuelEfficiency
                : "-";
        String bottomValue =
            (userMonthMileage != null && userMonthMileage != "null")
                ? userMonthMileage
                : "-";
        String userRating = topValue + "/" + bottomValue;
        updateNickName();
        UserRankingModel userRankingModel = UserRankingModel(
            userRating: monthlyRankingList.last.yourRanking,
            nickName: vehicleName,
            userRanking: userRating);
        _userRankingEntity.sink.add(userRankingModel);
        rankingList = ecoHistoryDetailPayload!
            .resESPo!.monthlyRanking!.first.rankingDetail;
        _rankingDetailList.sink.add(rankingList);
      } else {
        _ecoRankingList.sink.add(ecoHistoryList);
      }
    } else {
      _isEmptyPage.sink.add(true);
    }
  }

  Future<void> updateNickName() async {
    if (vehicleInfoPayload != null) {
      if (vehicleInfoPayload!.nickName != null) {
        vehicleNickName = vehicleInfoPayload!.nickName;
        if (vehicleNickName != null && vehicleNickName!.isNotEmpty) {
          vehicleName = "$vehicleNickName";
        } else {
          vehicleName = composeVehicleName(vehicleInfoPayload).trim().isEmpty
              ? " - "
              : composeVehicleName(vehicleInfoPayload);
        }
      }
    }
  }

  String composeVehicleName(vehicleInfo.Payload? vehicleItem) {
    String vehicleName = '';
    if (vehicleItem != null) {
      vehicleName =
          vehicleItem.modelYear! + " " + (vehicleItem.modelName ?? "");
    }
    return vehicleName;
  }

  @override
  void dispose() {
    _ecoRankingList.close();
    _userRankingEntity.close();
    _rankingDetailList.close();
    _isEmptyPage.close();
  }
}
