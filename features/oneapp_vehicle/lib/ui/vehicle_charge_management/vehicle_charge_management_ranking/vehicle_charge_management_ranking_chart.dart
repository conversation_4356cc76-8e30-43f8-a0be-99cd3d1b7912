// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:fl_chart/fl_chart.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/eco_ranking_chart_helper.dart';

class VehicleChargeManagementRankingChart extends StatelessWidget {
  final List<EcoRankingEntity>? ecoRankingList;
  final EcoRankingEntity maxItem;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  VehicleChargeManagementRankingChart(this.ecoRankingList, this.maxItem);

  @override
  Widget build(BuildContext context) {
    return BarChart(
      BarChartData(
        maxY: !maxItem.value.isNegative ? (maxItem.value) + 30 : -3,
        titlesData: titlesData,
        borderData: borderData,
        barGroups: barGroups,
        alignment: BarChartAlignment.spaceAround,
      ),
    );
  }

  FlTitlesData get titlesData => FlTitlesData(
        show: true,
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: (double value, meta) {
              return Padding(
                padding: EdgeInsets.all(32),
                child: Text(
                  getMonth(
                      ecoRankingList![value.toInt()].year.toStringAsFixed(0)),
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.caption1, _colorUtil.tertiary05),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              interval: !maxItem.value.isNegative
                  ? ((maxItem.value) / 5).roundToDouble()
                  : -1,
              getTitlesWidget: (double value, meta) {
                return Padding(padding: EdgeInsets.all(16));
              }),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
      );

  FlBorderData get borderData => FlBorderData(
        show: false,
      );

  List<BarChartGroupData> get barGroups => ecoRankingList!
      .map(
        (e) => BarChartGroupData(
          x: e.year,
          barsSpace: 4,
          barRods: [
            BarChartRodData(
              toY: e.value,
              color: _colorUtil.tertiary03,
            )
          ],
          showingTooltipIndicators: maxItem.value.isNegative ? [0] : [],
        ),
      )
      .toList();
}
