// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/custom_divider.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/eco_ranking_chart_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/user_ranking_list_helper.dart';

// Project imports:
import 'vehicle_charge_management_ranking_bloc.dart';
import 'vehicle_charge_management_ranking_chart.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/eco_history_detail_entity.dart'
    as ecoHistoryDetail;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleChargeManagementRankingPage extends StatefulWidget {
  final ChargeInfo? chargeInfo;
  final ScrollController? scrollController;
  final ecoHistoryDetail.EcoHistoryDetailPayload? ecoHistoryPayLoad;
  final vehicleInfo.Payload? vehicleInfoPayload;

  VehicleChargeManagementRankingPage(
      {this.chargeInfo,
      this.scrollController,
      this.ecoHistoryPayLoad,
      this.vehicleInfoPayload});

  @override
  _VehicleChargeManagementRankingPageState createState() =>
      _VehicleChargeManagementRankingPageState();
}

class _VehicleChargeManagementRankingPageState
    extends State<VehicleChargeManagementRankingPage> {
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  VehicleChargeManagementRankingBloc _bloc =
      VehicleChargeManagementRankingBloc();

  @override
  void initState() {
    super.initState();
    _bloc.init(widget.ecoHistoryPayLoad, widget.vehicleInfoPayload);
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          child: StreamBuilder<bool>(
              stream: _bloc.isEmptyPage,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return snapshot.data!
                      ? _noDataAvailableLayout()
                      : _rankingLayoutBody();
                } else {
                  return Container();
                }
              }),
        ),
        bloc: _bloc);
  }

  // Empty layout if data is null
  Widget _noDataAvailableLayout() {
    return Container(
      width: double.maxFinite,
      margin: EdgeInsets.only(top: 34.h),
      child: Center(
        child: Text(
          OneAppString.of().dataNotAvailableFor(OneAppString.of().ranking),
          textAlign: TextAlign.center,
          style: TextStyleExtension().newStyleWithColor(
              _textStyleUtil.callout1, _colorUtil.tertiary07),
        ),
      ),
    );
  }

  Widget _rankingLayoutBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        StreamBuilder<List<EcoRankingEntity>>(
            stream: _bloc.ecoRankingList,
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                return Padding(
                  padding: EdgeInsets.only(top: 8.h, bottom: 16.h),
                  child: Text(
                    OneAppString.of().fuelConsumption,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
                  ),
                );
              } else {
                return Container();
              }
            }),
        StreamBuilder<List<EcoRankingEntity>>(
            stream: _bloc.ecoRankingList,
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                EcoRankingEntity maxItem =
                    snapshot.data!.reduce((a, b) => a.value > b.value ? a : b);
                return Container(
                    height: 260.h,
                    width: double.maxFinite,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: 16.h),
                    child: VehicleChargeManagementRankingChart(
                        snapshot.data, maxItem));
              } else {
                return Container();
              }
            }),
        _yourRankingHeadingLayout(),
        _yourRankingLayout(),
        _topThreeHeadingLayout(),
        _topThreeListLayout()
      ],
    );
  }

  Widget _yourRankingHeadingLayout() {
    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            OneAppString.of().yourRanking,
            style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
          ),
          Text(
            OneAppString.of().mpgMI,
            style: TextStyleExtension()
                .newStyleWithColor(_textStyleUtil.body3, _colorUtil.tertiary03),
          ),
        ],
      ),
    );
  }

  // Our rank in the entire drivers
  Widget _yourRankingLayout() {
    return StreamBuilder<UserRankingModel>(
        stream: _bloc.userRankingEntity,
        builder: (context, snapshot) {
          return (snapshot.hasData)
              ? Padding(
                  padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
                  child: Row(
                    children: [
                      Container(
                          decoration: BoxDecoration(
                            color: _colorUtil.secondary01,
                            borderRadius: BorderRadius.circular(
                              32.r,
                            ),
                          ),
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.w, vertical: 8.h),
                          margin: EdgeInsets.only(right: 16.w),
                          child: Text(
                            snapshot.data!.userRating != null
                                ? snapshot.data!.userRating!
                                : "-",
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.buttonLink1,
                                _colorUtil.button01a),
                          )),
                      Flexible(
                        child: Text(
                          snapshot.data!.nickName != null
                              ? snapshot.data!.nickName!
                              : "-",
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.body3, _colorUtil.tertiary03),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          snapshot.data!.userRanking != null
                              ? snapshot.data!.userRanking!
                              : "-",
                          textAlign: TextAlign.right,
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.body3, _colorUtil.tertiary05),
                        ),
                      )
                    ],
                  ),
                )
              : Container();
        });
  }

  Widget _topThreeHeadingLayout() {
    return StreamBuilder<List<ecoHistoryDetail.RankingDetail>?>(
        stream: _bloc.rankingDetailList,
        builder: (context, snapshot) {
          return (snapshot.hasData && snapshot.data!.isNotEmpty)
              ? Padding(
                  padding: EdgeInsets.only(top: 8.h, bottom: 16.h),
                  child: Text(
                    OneAppString.of().top3,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
                  ),
                )
              : Container();
        });
  }

  // Top 3 rank list layout
  Widget _topThreeListLayout() {
    return StreamBuilder<List<ecoHistoryDetail.RankingDetail>?>(
        stream: _bloc.rankingDetailList,
        builder: (context, snapshot) {
          return (snapshot.hasData)
              ? ListView.builder(
                  shrinkWrap: true,
                  itemCount: snapshot.data!.length,
                  physics: NeverScrollableScrollPhysics(),
                  itemBuilder: (BuildContext context, int index) {
                    ecoHistoryDetail.RankingDetail item = snapshot.data![index];
                    return Column(
                      children: [
                        _topThreeListTile(
                            item.orderForDisplay,
                            item.nickname!,
                            item.value!.value! +
                                "/" +
                                item.oneMonthMileage!.value!,
                            index),
                        CustomDivider(
                          lineColor: _colorUtil.tertiary12,
                        )
                      ],
                    );
                  })
              : Container();
        });
  }

  // List Tile view for top 3 rank
  Widget _topThreeListTile(
      String? userRating, String nickname, String userRanking, int index) {
    return Padding(
      padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
      child: Row(
        children: [
          (index == 0)
              ? CircleAvatar(
                  backgroundColor: _colorUtil.tertiary10,
                  child: Center(
                    child: SvgPicture.asset(
                      starIcon,
                      height: 16.w,
                      width: 16.w,
                      colorFilter: ColorFilter.mode(
                        _colorUtil.tertiary03,
                        BlendMode.srcIn,
                      ),
                      allowDrawingOutsideViewBox: false,
                      semanticsLabel: STAR_ICON,
                    ),
                  ),
                )
              : CircleAvatar(
                  backgroundColor: _colorUtil.tertiary10,
                  child: Center(
                    child: Text(
                      userRating!,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.buttonLink1, _colorUtil.tertiary03),
                    ),
                  ),
                ),
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: Text(
              nickname,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body3, _colorUtil.tertiary03),
            ),
          ),
          Expanded(
            child: Text(
              userRanking,
              textAlign: TextAlign.right,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body3, _colorUtil.tertiary05),
            ),
          ),
        ],
      ),
    );
  }
}
