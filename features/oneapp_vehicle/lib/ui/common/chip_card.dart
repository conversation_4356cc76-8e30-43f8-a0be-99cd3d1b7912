// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

class ChipCard extends StatelessWidget {
  final String label;
  final TextStyle labelStyle;
  final Color backgroundColor;
  final Color? iconColor;
  final bool isBorderRequired;
  final bool isFilterAvailable;
  final int selectedFilter;
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;

  ChipCard(
      {Key? key,
      required this.label,
      required this.labelStyle,
      required this.backgroundColor,
      this.iconColor,
      this.isBorderRequired = false,
      this.isFilterAvailable = false,
      this.selectedFilter = 0})
      : super(key: key);

  @override
  Widget build(BuildContext context) => _buildChipCard();

  Widget _buildChipCard() {
    return Card(
      elevation: 0,
      color: backgroundColor,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.r),
          side: isBorderRequired
              ? BorderSide(color: _colorUtil.tertiary10)
              : BorderSide.none),
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(
                  left: 16.w,
                  right: selectedFilter != 0
                      ? 8.w
                      : isFilterAvailable
                          ? 2.w
                          : 16.w),
              child: Text(label, style: labelStyle),
            ),
            Padding(
              padding: EdgeInsets.only(
                  right: selectedFilter != 0
                      ? 10.w
                      : isFilterAvailable
                          ? 8.w
                          : 0.w),
              child: Visibility(
                  visible: isFilterAvailable,
                  child: selectedFilter != 0
                      ? CircleAvatar(
                          radius: 15.r,
                          child: Text("$selectedFilter",
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout2,
                                  _colorUtil.tertiary00)),
                          backgroundColor: _colorUtil.tertiary15,
                        )
                      : Icon(Icons.keyboard_arrow_down_rounded,
                          color: iconColor)),
            )
          ],
        ),
      ),
    );
  }
}
