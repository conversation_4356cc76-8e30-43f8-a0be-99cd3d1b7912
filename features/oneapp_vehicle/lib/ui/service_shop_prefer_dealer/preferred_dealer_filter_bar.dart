// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_section_list/flutter_section_list.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/select_preferred_dealer_helper.dart';

// Project imports:
import '/log/vehicle_analytic_event.dart';
import 'preferred_dealer_filter_bar_bloc.dart';
import 'slide_title_widget.dart';

class PreferredDealerFilterBar extends StatefulWidget {
  final int? sectionIndex;
  final DealerFilterModel? filterModel;
  const PreferredDealerFilterBar(
      {Key? key, this.sectionIndex, this.filterModel})
      : super(key: key);

  @override
  _PreferredDealerFilterBarState createState() =>
      _PreferredDealerFilterBarState();
}

class _PreferredDealerFilterBarState extends State<PreferredDealerFilterBar> {
  PreferredDealerFilterBarBloc _bloc = PreferredDealerFilterBarBloc();

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.PREFERRED_DEALER_FILTER_BAR_PAGE);
    if (widget.filterModel != null) {
      _bloc.filterModel = widget.filterModel;
    }
    _bloc.init(widget.sectionIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: BlocProvider(
        child: Column(
          children: [
            SlideTitle(
              paddingTop: 46,
              title: OneAppString.of().filters,
              menuItem: resetButton(),
              onBackPressed: () {
                Navigator.of(context).pop();
              },
            ),
            Expanded(
                child: StreamBuilder(
              stream: _bloc.totalListDataUI,
              builder: (ctx, AsyncSnapshot<List<SectionContent>> snapData) {
                return SectionListView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.zero,
                  adapter: ListAdapter(_bloc, snapData.data),
                );
              },
            )),
            bottomButton(),
          ],
        ),
        bloc: _bloc,
      ),
      color: ThemeConfig.current().colorUtil.tertiary15,
    );
  }

  Widget resetButton() {
    return InkWell(
      onTap: () {
        _bloc.resetFilterData();
      },
      child: Text(
        OneAppString.of().commonReset,
        style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
              color: ThemeConfig.current().colorUtil.button02a,
            ),
      ),
    );
  }

  Widget bottomButton() {
    return Padding(
      padding: EdgeInsets.only(bottom: 32.h, top: 10.h),
      child: CustomDefaultButton(
        backgroundColor: ThemeConfig.current().colorUtil.button01b,
        buttonTextColor: ThemeConfig.current().colorUtil.button01a,
        borderColor: ThemeConfig.current().colorUtil.button01b,
        disabledBackgroundColor: ThemeConfig.current().colorUtil.button02c,
        text: OneAppString.of().applyFilters,
        verticalPadding: 4.h,
        press: () {
          DealerFilterModel filterModel = _bloc.addFilterData();
          NavigateService.maybePopRoute(result: filterModel);
        },
      ),
    );
  }
}

class ListAdapter with SectionAdapterMixin {
  final PreferredDealerFilterBarBloc barBloc;
  final List<SectionContent>? totalData;

  ListAdapter(
    this.barBloc,
    this.totalData,
  );

  @override
  int numberOfSections() {
    return totalData?.length ?? 0;
  }

  @override
  int numberOfItems(int section) {
    return 1;
  }

  @override
  Widget getItem(BuildContext context, IndexPath indexPath) {
    List<ItemContent> itemListData = totalData![indexPath.section].itemList!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: itemListData.asMap().keys.map(
              (e) {
                String iconPath =
                    totalData![indexPath.section].isSingleSelect == false
                        ? (itemListData[e].isItemSelected == true
                            ? filterCheckboxSelIcon
                            : filterCheckboxIcon)
                        : (itemListData[e].isItemSelected == true
                            ? filterRadioIcon
                            : filterCheckboxIcon);
                return Container(
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  margin: EdgeInsets.symmetric(
                    horizontal: 32.w,
                  ),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                    color: ThemeConfig.current().colorUtil.tertiary10,
                    width: 1,
                  ))),
                  child: GestureDetector(
                    onTap: () {
                      if (totalData![indexPath.section].isSingleSelect ==
                          true) {
                        itemListData.forEach((element) {
                          element.isItemSelected = false;
                        });
                      }
                      bool isClick = itemListData[e].isItemSelected!;
                      isClick = !isClick;
                      itemListData[e].isItemSelected = isClick;
                      barBloc.updateSectionIndex(
                          totalData![indexPath.section], indexPath.section);
                    },
                    behavior: HitTestBehavior.opaque,
                    child: Row(
                      children: [
                        SizedBox(
                          height: 24.w,
                          width: 24.w,
                          child: CommonCircleIconImage(
                            iconPath,
                            iconWidth: 24.w,
                            iconHeight: 24.w,
                            circleBackgroundColor:
                                ThemeConfig.current().colorUtil.tertiary15,
                          ),
                        ),
                        SizedBox(
                          width: 16.w,
                        ),
                        Expanded(
                          child: Text(
                            itemListData[e].title!,
                            style: ThemeConfig.current()
                                .textStyleUtil
                                .body4
                                .copyWith(
                                  color: ThemeConfig.current()
                                      .colorUtil
                                      .tertiary03,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ).toList(),
          ),
          visible: totalData![indexPath.section].isSectionClick == true,
        ),
        Container(
          height: 8.h,
        )
      ],
    );
  }

  @override
  Widget getSectionHeader(BuildContext context, int section) {
    SectionContent sectionData = totalData![section];
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: GestureDetector(
        onTap: () {
          bool isClick = sectionData.isSectionClick!;
          isClick = !isClick;
          sectionData.isSectionClick = isClick;
          barBloc.updateSectionIndex(sectionData, section);
        },
        behavior: HitTestBehavior.opaque,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(8.r),
            ),
            color: ThemeConfig.current().colorUtil.tile02,
          ),
          padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 16.w),
          child: Row(
            children: [
              SvgPicture.asset(
                sectionData.iconPath!,
                height: 24.w,
                width: 24.w,
                colorFilter: ColorFilter.mode(
                  ThemeConfig.current().colorUtil.button02a,
                  BlendMode.srcIn,
                ),
              ),
              SizedBox(
                width: 16.w,
              ),
              Expanded(
                child: Text(
                  sectionData.title!,
                  style: ThemeConfig.current().textStyleUtil.body4.copyWith(
                        color: ThemeConfig.current().colorUtil.tertiary03,
                      ),
                ),
              ),
              SvgPicture.asset(
                sectionData.isSectionClick == true
                    ? filterMinimizeIcon
                    : filterPlusIcon,
                height: 24.w,
                width: 24.w,
                colorFilter: ColorFilter.mode(
                  ThemeConfig.current().colorUtil.button02a,
                  BlendMode.srcIn,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  bool shouldExistSectionHeader(int section) {
    return true;
  }

  @override
  bool shouldExistHeader() {
    return false;
  }
}
