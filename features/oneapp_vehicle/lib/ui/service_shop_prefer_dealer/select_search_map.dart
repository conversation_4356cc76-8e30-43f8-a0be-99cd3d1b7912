// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/config/oneapp_ui_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/select_preferred_dealer_helper.dart';

typedef OnMapPinTap = void Function(DealerSearchResultHelper item);

class SelectSearchMap extends StatefulWidget {
  // final List<DealerDetailInfo> dealerDetailInfoList;
  final List<DealerSearchResultHelper>? dealerships;
  final Position? currentLocation;

  final OnMapPinTap? onMapPinTap;

  final double paddingBottom;

  final double paddingTop;

  final double paddingLeft;

  final double paddingRight;

  const SelectSearchMap({
    Key? key,
    // this.dealerDetailInfoList,
    this.dealerships,
    this.currentLocation,
    this.onMapPinTap,
    this.paddingBottom = 60,
    this.paddingTop = 60,
    this.paddingLeft = 60,
    this.paddingRight = 60,
  }) : super(key: key);

  @override
  SelectSearchMapState createState() => SelectSearchMapState();
}

class SelectSearchMapState extends State<SelectSearchMap> {
  MapboxMap? controller;
  late PointAnnotationManager _pointAnnotationManager;

  GlobalKey mapGlobalKey = GlobalKey();

  Future<void>? addImageFromAsset(
    String name,
    String assetName, {
    required int width,
    required int height,
  }) async {
    final ByteData bytes = await rootBundle.load(assetName);
    final Uint8List list = bytes.buffer.asUint8List();
    return controller?.style.addStyleImage(
        name,
        1,
        MbxImage(data: list, width: width, height: height),
        false,
        [],
        [],
        null);
  }

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = isDarkTheme();
    addAllPIN();
    return MapWidget(
      androidHostingMode: AndroidPlatformViewHostingMode.TLHC_HC,
      key: mapGlobalKey,
      styleUri: isDarkMode == true ? MapboxStyles.DARK : MapboxStyles.LIGHT,
      onMapCreated: _onMapCreated,
      onStyleLoadedListener: _onStyleLoaded,
      cameraOptions: CameraOptions(
        center: Point(coordinates: Position(40.745323, -73.989551)),
        zoom: 11.0,
      ),
      gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>[
        Factory<OneSequenceGestureRecognizer>(
          () => EagerGestureRecognizer(),
        ),
      ].toSet(),
      onTapListener: (_) {
        FocusScope.of(OneAppUiConfig.appContext).requestFocus(FocusNode());
      },
    );
  }

  @override
  void dispose() {
    // TODO controller?.onSymbolTapped.remove(_onSymbolTapped);
    controller?.dispose();
    super.dispose();
  }

  Future<void> _onMapCreated(MapboxMap controller) async {
    this.controller = controller;
    controller.location
        .updateSettings(LocationComponentSettings(enabled: true));
    this._pointAnnotationManager =
        await controller.annotations.createPointAnnotationManager();
    _pointAnnotationManager.setIconAllowOverlap(true);
    _pointAnnotationManager.setIconIgnorePlacement(true);
    // TODO controller.onSymbolTapped.add(_onSymbolTapped);
    addAllPIN();
  }

  Future _onStyleLoaded([StyleLoadedEventData? _]) async {
    addImageFromAsset(
      "pinFlexImage",
      flexMapPinIcon,
      height: 145,
      width: 145,
    );
  }

  Future<void> addAllPIN({double? dx, double? dy}) async {
    if (controller == null) return;
    await _removeAll();
    final latLngBoundsBuilder = LatLngBoundsBuilder();
    final List<PointAnnotationOptions> annotationsList = [];
    if (widget.dealerships != null && widget.dealerships!.isNotEmpty) {
      await _onStyleLoaded();

      annotationsList.addAll(widget.dealerships!.map((i) {
        String iconImage = 'pinFlexImage';
        latLngBoundsBuilder.includeLatLon(i.latitude, i.longitude);
        final options = PointAnnotationOptions(
          symbolSortKey: -1,
          geometry: Point(
            coordinates: Position(
              i.longitude,
              i.latitude,
            ),
          ),
          iconImage: iconImage,
          iconSize: 0.5,
        );
        return options;
      }).toList());
    }

    try {
      if (annotationsList.isNotEmpty) {
        addSymbolsToMapboxController(
          _pointAnnotationManager,
          annotationsList,
        );
      }
    } catch (e) {
      debugPrint(e.toString());
    }

    final latLngBounds = latLngBoundsBuilder.build();
    if (controller != null) {
      if (widget.currentLocation != null) {
        await controller!.flyTo(
          CameraOptions(
            center: Point(coordinates: widget.currentLocation!),
            zoom: 13,
          ),
          null,
        );
      } else {
        if (widget.dealerships != null && widget.dealerships!.isNotEmpty) {
          if (widget.dealerships!.length == 1) {
            await controller!.flyTo(
              CameraOptions(
                center: Point(
                  coordinates: latLngBoundsBuilder.points.first,
                ),
                zoom: 13,
              ),
              null,
            );
          } else {
            final centeredLatLong = getCenter(latLngBounds);
            await controller!.flyTo(
              CameraOptions(
                center: Point(coordinates: centeredLatLong),
                zoom: 13,
                padding: MbxEdgeInsets(
                  left: widget.paddingLeft,
                  right: widget.paddingRight,
                  top: widget.paddingTop,
                  bottom: widget.paddingBottom,
                ),
              ),
              null,
            );
          }
        } else {
          await controller!.flyTo(
            CameraOptions(
              center: Point(
                  coordinates: widget.currentLocation ??
                      Position(40.745323, -73.989551)),
              zoom: 13,
            ),
            null,
          );
        }
      }
    }
    mapGlobalKey.currentState?.setState(() {});
  }

  void animateCamera(double lat, double lon) {
    controller!.flyTo(
      CameraOptions(center: Point(coordinates: Position(lon, lat))),
      null,
    );
  }

  Future _removeAll() async {
    removeSymbolsFromMapboxController(_pointAnnotationManager);
  }

  // TODO
  // void _onSymbolTapped(Symbol symbol) {
  //   DealerSearchResultHelper? it;
  //   try {
  //     it = symbol.data!['key'];
  //   } catch (e) {}
  //   if (it != null) {
  //     widget.onMapPinTap?.call(it);
  //   }
  // }
}
