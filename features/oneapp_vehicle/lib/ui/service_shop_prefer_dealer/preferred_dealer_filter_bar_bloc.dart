// Flutter imports:

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/select_preferred_dealer_helper.dart';
import 'package:rxdart/subjects.dart';

class PreferredDealerFilterBarBloc implements BlocBase {
  Stream<List<SectionContent>> get totalListDataUI => _tableListaDataCtl.stream;
  final _tableListaDataCtl = BehaviorSubject<List<SectionContent>>();

  Stream<bool> get itemSelectUI => _itemSelectCtl.stream;
  final _itemSelectCtl = BehaviorSubject<bool>();

  List<SectionContent> totalData = [];

  DealerFilterModel? filterModel;

  DealerFilterModel filterPayLoad = DealerFilterModel();

  final String monday = 'Monday';
  final String tuesday = 'Tuesday';
  final String wednesday = 'Wednesday';
  final String thursday = 'Thursday';
  final String friday = 'Friday';
  final String saturday = 'Saturday';
  final String sunday = 'Sunday';
  final String services = 'Services';
  final String serviceHours = 'Service Hours';
  final String accessibility = 'Accessibility';
  final String transportation = 'Transportation';
  final String paymentMethods = 'Payment Methods';
  final String amenities = 'Amenities';
  final int distanceLimitTen = 10;
  final int distanceLimitTwentyFive = 25;
  final int distanceLimitFifty = 50;
  static const filterIndexZero = 0;
  static const filterIndexOne = 1;
  static const filterIndexTwo = 2;
  static const filterIndexThree = 3;
  static const filterIndexFour = 4;
  static const filterIndexFive = 5;
  static const filterIndexSix = 6;
  static const filterIndexSeven = 7;
//Distance filter going to be commented and will be added on next release
  List<SectionContent> getListData(int? sectionIndex) {
    List<String> setionIconList = [
      filterDealersIcon,
      filterServicesIcon,
      filterServiceHoursIcon,
      filterDistanceIcon,
      filterAsseceibilityIcon,
      filterTransportationIcon,
      filterPaymentIcon,
      filterWifiIcon,
    ];
    List<String> sectionTitle = [
      OneAppString.of().dealers,
      OneAppString.of().services,
      OneAppString.of().serviceHours,
      OneAppString.of().distance,
      OneAppString.of().accessibility,
      OneAppString.of().transportationHeading,
      OneAppString.of().paymentMethods,
      OneAppString.of().amenities
    ];
    List<SectionContent> totalData = [];
    for (int i = 0; i < sectionTitle.length; i++) {
      SectionContent headerData = SectionContent(
        isSingleSelect: false,
        iconPath: setionIconList[i],
        title: sectionTitle[i],
        itemList: itemData(i),
        isSectionClick: sectionIndex == i ? true : false,
      );
      totalData.add(headerData);
    }
    return totalData;
  }

  List<ItemContent> itemData(int i) {
    List<ItemContent> itemData = [];
    switch (i) {
      case filterIndexZero:
        bool smartPathDealer = filterModel?.smartPath ?? false;
        List<String> titleList = [
          OneAppString.of().smartpathDealersOnly,
        ];
        itemData.addAll(titleList
            .asMap()
            .keys
            .map((e) => ItemContent(
                  isItemSelected: smartPathDealer,
                  title: titleList[e],
                ))
            .toList());
        return itemData;
      case filterIndexOne:
        {
          List<bool> titleSelect = List<bool>.generate(
              filterPayLoad.service?.length ?? 0, (i) => false);
          if (filterModel?.service?.isNotEmpty == true) {
            filterModel!.service!.forEach((element) {
              int index =
                  filterPayLoad.service!.indexWhere((ele) => ele == element);
              titleSelect[index] = true;
            });
          }
          List<String> titleList = filterPayLoad.service ?? [];
          itemData.addAll(titleList
              .asMap()
              .keys
              .map((e) => ItemContent(
                    isItemSelected: titleSelect[e],
                    title: titleList[e],
                  ))
              .toList());
          return itemData;
        }
      case filterIndexTwo:
        {
          List<bool> titleSelect = [
            false,
            false,
            false,
            false,
            false,
            false,
            false,
          ];
          if (filterModel?.dayOpen?.isNotEmpty == true) {
            filterModel!.dayOpen!.forEach((element) {
              if (element == monday) {
                titleSelect[0] = true;
              } else if (element == tuesday) {
                titleSelect[1] = true;
              } else if (element == wednesday) {
                titleSelect[2] = true;
              } else if (element == thursday) {
                titleSelect[3] = true;
              } else if (element == friday) {
                titleSelect[4] = true;
              } else if (element == saturday) {
                titleSelect[5] = true;
              } else if (element == sunday) {
                titleSelect[6] = true;
              }
            });
          }
          List<String> dayList = [
            monday,
            tuesday,
            wednesday,
            thursday,
            friday,
            saturday,
            sunday,
          ];
          itemData.addAll(dayList
              .asMap()
              .keys
              .map((e) => ItemContent(
                    isItemSelected: titleSelect[e],
                    title: dayList[e],
                  ))
              .toList());
          return itemData;
        }
      case filterIndexThree:
        List<bool> titleSelect = [
          false,
          false,
          false,
        ];
        List<String> titleList = [
          OneAppString.of().tenMiles,
          OneAppString.of().twentyFiveMiles,
          OneAppString.of().fiftyMiles,
        ];
        if (filterModel?.limit != null) {
          if (filterModel?.limit == distanceLimitTen) {
            titleSelect[0] = true;
          } else if (filterModel?.limit == distanceLimitTwentyFive) {
            titleSelect[1] = true;
          } else if (filterModel?.limit == distanceLimitFifty) {
            titleSelect[2] = true;
          }
        }
        itemData.addAll(titleList
            .asMap()
            .keys
            .map((e) => ItemContent(
                  isItemSelected: titleSelect[e],
                  title: titleList[e],
                ))
            .toList());
        return itemData;
      case filterIndexFour:
        {
          List<bool> titleSelect = List<bool>.generate(
              filterPayLoad.accessibilityOption?.length ?? 0, (i) => false);
          if (filterModel?.accessibilityOption?.isNotEmpty == true) {
            filterModel!.accessibilityOption!.forEach((element) {
              int index = filterPayLoad.accessibilityOption!
                  .indexWhere((ele) => ele == element);
              titleSelect[index] = true;
            });
          }
          List<String> titleList = filterPayLoad.accessibilityOption ?? [];
          itemData.addAll(titleList
              .asMap()
              .keys
              .map((e) => ItemContent(
                    isItemSelected: titleSelect[e],
                    title: titleList[e],
                  ))
              .toList());
          return itemData;
        }
      case filterIndexFive:
        {
          List<bool> titleSelect = List<bool>.generate(
              filterPayLoad.transportationOption?.length ?? 0, (i) => false);
          if (filterModel?.transportationOption?.isNotEmpty == true) {
            filterModel!.transportationOption!.forEach((element) {
              int index = filterPayLoad.transportationOption!
                  .indexWhere((ele) => ele == element);
              titleSelect[index] = true;
            });
          }
          List<String> titleList = filterPayLoad.transportationOption ?? [];
          itemData.addAll(titleList
              .asMap()
              .keys
              .map((e) => ItemContent(
                    isItemSelected: titleSelect[e],
                    title: titleList[e],
                  ))
              .toList());
          return itemData;
        }
      case filterIndexSix:
        {
          List<bool> titleSelect = List<bool>.generate(
              filterPayLoad.paymentOption?.length ?? 0, (i) => false);
          if (filterModel?.paymentOption?.isNotEmpty == true) {
            filterModel!.paymentOption!.forEach((element) {
              int index = filterPayLoad.paymentOption!
                  .indexWhere((ele) => ele == element);
              titleSelect[index] = true;
            });
          }
          List<String> titleList = filterPayLoad.paymentOption ?? [];
          itemData.addAll(titleList
              .asMap()
              .keys
              .map((e) => ItemContent(
                    isItemSelected: titleSelect[e],
                    title: titleList[e],
                  ))
              .toList());
          return itemData;
        }
      case filterIndexSeven:
        {
          List<bool> titleSelect = List<bool>.generate(
              filterPayLoad.amenity?.length ?? 0, (i) => false);
          if (filterModel?.amenity?.isNotEmpty == true) {
            filterModel!.amenity!.forEach((element) {
              int index =
                  filterPayLoad.amenity!.indexWhere((ele) => ele == element);
              titleSelect[index] = true;
            });
          }
          List<String> titleList = filterPayLoad.amenity ?? [];
          itemData.addAll(titleList
              .asMap()
              .keys
              .map((e) => ItemContent(
                    isItemSelected: titleSelect[e],
                    title: titleList[e],
                  ))
              .toList());
          return itemData;
        }
      default:
        {
          return itemData;
        }
    }
  }

  void init(int? sectionIndex) async {
    await checkData();
    totalData = getListData(sectionIndex);
    _tableListaDataCtl.add(totalData);
  }

  // ignore: type_annotate_public_apis
  checkData() {
    GetIt locator = GetIt.instance;
    try {
      filterPayLoad = locator<DealerFilterModel>();
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_VEHI);
    }
  }

  void updateSectionIndex(SectionContent tableData, int section) {
    totalData.removeAt(section);
    totalData.insert(section, tableData);
    _tableListaDataCtl.add(totalData);
  }

  void resetFilterData() {
    totalData.forEach((element) {
      element.itemList?.forEach((element) {
        if (element.isItemSelected == true) {
          element.isItemSelected = false;
        }
      });
    });
    _tableListaDataCtl.add(totalData);
  }

  void selectItem(SectionContent tableData, int section) {
    totalData.removeAt(section);
    totalData.insert(section, tableData);
    _tableListaDataCtl.add(totalData);
  }

  DealerFilterModel addFilterData() {
    DealerFilterModel filterModel = DealerFilterModel();
    totalData.forEach((element) {
      if (element.title == OneAppString.of().dealers) {
        element.itemList?.forEach((ele) {
          if (ele.isItemSelected == true) {
            if (ele.title == OneAppString.of().smartpathDealersOnly) {
              filterModel.smartPath = true;
            }
          }
        });
      } else if (element.title == OneAppString.of().services) {
        List<String> tmpList = [];
        element.itemList?.forEach((ele) {
          if (ele.isItemSelected == true && ele.title != null) {
            tmpList.add(ele.title!);
          }
        });
        filterModel.service = tmpList;
      } else if (element.title == OneAppString.of().serviceHours) {
        List<String> tmpList = [];
        element.itemList?.forEach((ele) {
          if (ele.isItemSelected == true) {
            if (ele.title == monday) {
              tmpList.add(monday);
            } else if (ele.title == tuesday) {
              tmpList.add(tuesday);
            } else if (ele.title == wednesday) {
              tmpList.add(wednesday);
            } else if (ele.title == thursday) {
              tmpList.add(thursday);
            } else if (ele.title == friday) {
              tmpList.add(friday);
            } else if (ele.title == saturday) {
              tmpList.add(saturday);
            } else if (ele.title == sunday) {
              tmpList.add(sunday);
            }
          }
        });
        filterModel.dayOpen = tmpList;
      } else if (element.title == OneAppString.of().distance) {
        element.itemList?.forEach((ele) {
          if (ele.isItemSelected == true) {
            if (ele.title == OneAppString.of().tenMiles) {
              filterModel.limit = distanceLimitTen;
            } else if (ele.title == OneAppString.of().twentyFiveMiles) {
              filterModel.limit = distanceLimitTwentyFive;
            } else if (ele.title == OneAppString.of().fiftyMiles) {
              filterModel.limit = distanceLimitFifty;
            }
          }
        });
      } else if (element.title == OneAppString.of().accessibility) {
        List<String> tmpList = [];
        element.itemList?.forEach((ele) {
          if (ele.isItemSelected == true && ele.title != null) {
            tmpList.add(ele.title!);
          }
        });
        filterModel.accessibilityOption = tmpList;
      } else if (element.title == OneAppString.of().transportationHeading) {
        List<String> tmpList = [];
        element.itemList?.forEach((ele) {
          if (ele.isItemSelected == true && ele.title != null) {
            tmpList.add(ele.title!);
          }
        });
        filterModel.transportationOption = tmpList;
      } else if (element.title == OneAppString.of().paymentMethods) {
        List<String> tmpList = [];
        element.itemList?.forEach((ele) {
          if (ele.isItemSelected == true && ele.title != null) {
            tmpList.add(ele.title!);
          }
        });
        filterModel.paymentOption = tmpList;
      } else if (element.title == OneAppString.of().amenities) {
        List<String> tmpList = [];
        element.itemList?.forEach((ele) {
          if (ele.isItemSelected == true && ele.title != null) {
            tmpList.add(ele.title!);
          }
        });
        filterModel.amenity = tmpList;
      }
    });

    return filterModel;
  }

  @override
  void dispose() {
    _tableListaDataCtl.close();
    _itemSelectCtl.close();
  }
}
