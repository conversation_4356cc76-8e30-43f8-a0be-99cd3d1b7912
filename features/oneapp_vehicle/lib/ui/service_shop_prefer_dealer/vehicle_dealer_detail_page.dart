// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:app_settings/app_settings.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_section_list/flutter_section_list.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_shop_get_dealership_detail_by_id_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/select_preferred_dealer_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:shimmer/shimmer.dart';

// Project imports:
import '/log/vehicle_analytic_event.dart';
import '/log/vehicle_marketing_event.dart';
import 'vehicle_dealer_detail_bloc.dart';

const int servicesIndex = 1;
const int amenitiesIndex = 2;
const int paymentMethodsIndex = 3;
const int accessibilityIndex = 4;
const int transportationHeadingIndex = 5;

class VehicleDealerDetailPage extends StatefulWidget {
  final String? dealerCode;
  final PreferredDealerStatus? preferredDealerStatus;
  final DealerInfoHelper? dealerInfoHelper;
  final bool? isFromNative;
  final bool? FromSelectDealer;
  final bool? isFromOdometerFlow;
  final String? odometerValue;
  final String? odometerUnit;

  const VehicleDealerDetailPage(
      {Key? key,
      this.dealerCode,
      this.preferredDealerStatus,
      this.dealerInfoHelper,
      this.isFromNative,
      this.FromSelectDealer,
      this.isFromOdometerFlow,
      this.odometerValue,
      this.odometerUnit})
      : super(key: key);

  @override
  _VehicleDealerDetailPageState createState() =>
      _VehicleDealerDetailPageState();
}

class _VehicleDealerDetailPageState extends State<VehicleDealerDetailPage> {
  // //final SheetController _scrollSheetController = SheetController();

  VehicleDealerDetailBloc _bloc = VehicleDealerDetailBloc();
  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_DEALER_DETAIL_PAGE);
    _bloc.init(widget.dealerCode!);
    _bloc.appLocationDenied.listen((event) {
      if (event == true) {
        BottomConfirmationDialog().showBottomDialog(
            context,
            OneAppString.of().locationPermissionHeading,
            OneAppString.of().locationPermissionSubHeading,
            findIcon,
            OneAppString.of().commonOK,
            OneAppString.of().commonCancel,
            _appPermissionConfirmClick);
      }
    });
    _bloc.uiToast.listen((event) {
      BottomConfirmationDialog().showInformationDialog(
          context,
          OneAppString.of().success,
          OneAppString.of().youHaveSetYourPreferredDealer,
          statusCompleteIcon,
          OneAppString.of().commonDone, () {
        FBroadcast.instance().broadcast(DEALER_REFRESH);
        if (widget.FromSelectDealer == true) {
          goBackToNative();
        } else {
          if (ModalRoute.of(context)?.canPop == true) {
            Navigator.removeRouteBelow(context, ModalRoute.of(context)!);
          } else {
            goBackToNative();
          }
        }
      }, true);
    });
  }

  void _appPermissionConfirmClick() {
    AppSettings.openAppSettings();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      child: _layout(),
      onPopInvoked: _willPopBack,
    );
  }

  void _willPopBack(bool didPop) {
    if (didPop) return;
    _closeFlutterScreen();
  }

  Widget _layout() {
    return BlocProvider(
      child: Scaffold(
        backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
        body: StreamBuilder(
          stream: _bloc.getDealerDataByIdUI,
          builder: (ctx, AsyncSnapshot<DealerSearchResultHelper> snapData) {
            return SafeArea(
              child: Column(
                children: [
                  _bottomSheetAppBar(snapData.data),
                  _dealerInfo(snapData.data),
                  SizedBox(height: 10.h),
                  _contentList(snapData.data),
                  showSnapDataUI(snapData),
                  SizedBox(
                    height: 32.h,
                  ),
                ],
              ),
            );
          },
        ),
      ),
      bloc: _bloc,
    );
  }

  Widget _bottomSheetAppBar(DealerSearchResultHelper? shipData) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: kToolbarHeight,
            child: Stack(
              children: [
                Positioned(left: 0, child: _backIcon(shipData)),
                Align(child: _pageTitle()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _closeFlutterScreen() async {
    if (widget.isFromNative == true) {
      Navigator.pop(context);
      goBackToNative();
    } else {
      if (!Navigator.canPop(context)) {
        Navigator.pop(context);
        goBackToNative();
      } else {
        Navigator.pop(context);
      }
    }
  }

  Widget _backIcon(DealerSearchResultHelper? shipData) {
    return StreamBuilder(
        stream: _bloc.preferredDealerUI,
        builder: (context, snapshot) {
          return InkWell(
            onTap: _closeFlutterScreen,
            child: Container(
              decoration: BoxDecoration(
                color: ThemeConfig.current().colorUtil.button02b,
                shape: BoxShape.circle,
              ),
              margin: EdgeInsets.only(left: 16.w),
              padding: EdgeInsets.all(12.w),
              child: Icon(
                snapshot.data == true
                    ? widget.isFromNative == true
                        ? Icons.chevron_left
                        : Icons.close
                    : Icons.chevron_left,
                color: ThemeConfig.current().colorUtil.button02a,
                semanticLabel:
                    snapshot.data == true ? REMOVE_ICON : BACK_BUTTON,
              ),
            ),
          );
        });
  }

  Widget _pageTitle() {
    return StreamBuilder<bool>(
        stream: _bloc.preferredDealerUI,
        builder: (context, snapshot) {
          return Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: Text(
              getAppBarText(snapshot.data),
              style: TextStyleExtension().newStyleWithColor(
                  ThemeConfig.current().textStyleUtil.subHeadline3,
                  ThemeConfig.current().colorUtil.tertiary03),
              textAlign: TextAlign.center,
            ),
          );
        });
  }

  String getAppBarText(bool? state) {
    String text = OneAppString.of().dealerDetails;
    if (state == true) {
      text = OneAppString.of().preferredDealer;
    }
    return text;
  }

  Widget _dealerInfo(DealerSearchResultHelper? data) {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 15.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                (data?.dealershipName != null
                    ? data?.dealershipName
                    : (widget.dealerInfoHelper?.dealershipName ?? ""))!,
                style: ThemeConfig.current().textStyleUtil.body4,
              ),
              Text(
                data?.distance != null && data?.distance != 0
                    ? _bloc.addDistanceUnit(data?.distance)
                    : widget.dealerInfoHelper?.distance != null &&
                            widget.dealerInfoHelper?.distance != 0
                        ? _bloc
                            .addDistanceUnit(widget.dealerInfoHelper?.distance)
                        : "",
                style: ThemeConfig.current().textStyleUtil.callout1,
              ),
            ],
          ),
          SizedBox(
            height: 4.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data?.line1 != null
                          ? data!.line1
                          : (widget.dealerInfoHelper?.line1 ?? ""),
                      style: ThemeConfig.current().textStyleUtil.callout1,
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    Text(
                      "${data?.city != null ? data?.city : (widget.dealerInfoHelper?.city ?? "")}, ${data?.state != null ? data?.state : (widget.dealerInfoHelper?.state ?? "")} ${data?.zipCode != null ? data?.zipCode : (widget.dealerInfoHelper?.zipCode ?? "")}",
                      style: ThemeConfig.current().textStyleUtil.callout1,
                    ),
                  ],
                ),
              ),
              data?.smartPath != null
                  ? spmLogo(data?.spmLogo)
                  : SizedBox.shrink()
            ],
          ),
          SizedBox(
            height: 37.h,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 22.w),
            child: Row(
              mainAxisAlignment: getAlignment(data),
              children: [
                Visibility(
                  child: InkWell(
                    onTap: () {
                      FireBaseAnalyticsLogger.logMarketingGroupEvent(
                          VehicleMarketingEvent.SERVICE_SCHEDULE,
                          childEventName:
                              VehicleMarketingEvent.PREFERREDDEALER_CALL);
                      dialerLauncher(data!.phoneNumber);
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.all(12.w),
                          decoration: BoxDecoration(
                            color: ThemeConfig.current().colorUtil.button02b,
                            borderRadius:
                                BorderRadius.all(Radius.circular(48.r)),
                          ),
                          alignment: Alignment.center,
                          child: SvgPicture.asset(
                            phoneSmallIcon,
                            height: 24.w,
                            width: 24.w,
                            colorFilter: ColorFilter.mode(
                              ThemeConfig.current().colorUtil.button02a,
                              BlendMode.srcIn,
                            ),
                            semanticsLabel: PHONE_SMALL_ICON,
                          ),
                        ),
                        SizedBox(
                          height: 8.h,
                        ),
                        Text(
                          OneAppString.of().call,
                          style: ThemeConfig.current()
                              .textStyleUtil
                              .body3
                              .copyWith(
                                  color: ThemeConfig.current()
                                      .colorUtil
                                      .button02a),
                        ),
                      ],
                    ),
                  ),
                  visible: data?.phoneNumber.isNotEmpty == true,
                ),
                Visibility(
                  child: InkWell(
                    onTap: () async {
                      FireBaseAnalyticsLogger.logMarketingGroupEvent(
                          VehicleMarketingEvent.SERVICE_SCHEDULE,
                          childEventName:
                              VehicleMarketingEvent.PREFERREDDEALER_WEBSITE);
                      await launchUrlInBrowser(data!.website);
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: ThemeConfig.current().colorUtil.button02b,
                            borderRadius:
                                BorderRadius.all(Radius.circular(48.r)),
                          ),
                          padding: EdgeInsets.all(12.w),
                          alignment: Alignment.center,
                          child: SvgPicture.asset(
                            webIcon,
                            height: 24.w,
                            width: 24.w,
                            colorFilter: ColorFilter.mode(
                              ThemeConfig.current().colorUtil.button02a,
                              BlendMode.srcIn,
                            ),
                            semanticsLabel: WEB_ICON,
                          ),
                        ),
                        SizedBox(
                          height: 8.h,
                        ),
                        Text(
                          OneAppString.of().website,
                          style: ThemeConfig.current()
                              .textStyleUtil
                              .body3
                              .copyWith(
                                  color: ThemeConfig.current()
                                      .colorUtil
                                      .button02a),
                        ),
                      ],
                    ),
                  ),
                  visible: data?.website.isNotEmpty == true,
                ),
                Visibility(
                  child: InkWell(
                    onTap: () {
                      FireBaseAnalyticsLogger.logMarketingGroupEvent(
                          VehicleMarketingEvent.SERVICE_SCHEDULE,
                          childEventName:
                              VehicleMarketingEvent.PREFERREDDEALER_DIRECTIONS);
                      openMap(
                        data!.longitude,
                        data.latitude,
                        address: data.dealershipName,
                      );
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.all(12.w),
                          decoration: BoxDecoration(
                            color: ThemeConfig.current().colorUtil.button02b,
                            borderRadius:
                                BorderRadius.all(Radius.circular(48.r)),
                          ),
                          alignment: Alignment.center,
                          child: SvgPicture.asset(
                            detailDirectionIcon,
                            height: 24.w,
                            width: 24.w,
                            colorFilter: ColorFilter.mode(
                              ThemeConfig.current().colorUtil.button02a,
                              BlendMode.srcIn,
                            ),
                            semanticsLabel: DETAIL_DIRECTION_ICON,
                          ),
                        ),
                        SizedBox(
                          height: 8.h,
                        ),
                        Text(
                          OneAppString.of().directions,
                          style: ThemeConfig.current()
                              .textStyleUtil
                              .body3
                              .copyWith(
                                  color: ThemeConfig.current()
                                      .colorUtil
                                      .button02a),
                        ),
                      ],
                    ),
                  ),
                  visible: data?.longitude != null || data?.latitude != null,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget spmLogo(String? spmLogo) {
    if (spmLogo != null && spmLogo.isNotEmpty == true) {
      bool isDarkTheme = Global.getInstance().isDarkTheme;
      String? spmImage =
          isDarkTheme ? spmLogo.split(',').last : spmLogo.split(',').first;
      if (spmImage.isNotEmpty == true) {
        return CachedNetworkImage(
          height: 34.h,
          width: 90.w,
          imageUrl: spmImage,
          errorWidget: (context, url, error) => SizedBox.shrink(),
        );
      } else {
        return SizedBox.shrink();
      }
    } else {
      return SizedBox.shrink();
    }
  }

  Widget _contentList(DealerSearchResultHelper? data) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            StreamBuilder(
                stream: _bloc.tableListaDataUI,
                builder: (ctx, AsyncSnapshot<List<TableData>> snapData) {
                  if (snapData.connectionState == ConnectionState.waiting) {
                    return Column(
                      children: [
                        shimmerRectangle(180.h, double.infinity, 8.r),
                        SizedBox(
                          height: 30.h,
                        ),
                        shimmerRectangle(180.h, double.infinity, 8.r),
                      ],
                    );
                  } else {
                    return SectionListView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      adapter: ListAdapter(_bloc, snapData.data!),
                    );
                  }
                }),
          ],
        ),
      ),
    );
  }

  Widget shimmerRectangle(double height, double width, double borderRadius) {
    return Shimmer.fromColors(
        baseColor: ThemeConfig.current().colorUtil.tertiary10,
        highlightColor: ThemeConfig.current().colorUtil.tertiary12,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(borderRadius.r))),
          height: height.h,
          width: width.w,
        ));
  }

  void changeDealerClick() {
    NavigateService.pushNamedRoute(RoutePath.FX_SELECTPREFERDEALERPAGE,
        arguments: {"isFromNative": "false"});
  }

  Widget bottomButton(DealerSearchResultHelper? dealerModel) {
    return StreamBuilder(
        stream: _bloc.preferredDealerUI,
        builder: (ctx, AsyncSnapshot<bool> data) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Visibility(
                child: CustomDefaultButton(
                  backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
                  buttonTextColor: ThemeConfig.current().colorUtil.button02a,
                  borderColor: ThemeConfig.current().colorUtil.tertiary15,
                  // disabledBackgroundColor: ThemeConfig.current().colorUtil.tile03,
                  text: data.data == true
                      ? OneAppString.of().changeDealer
                      : OneAppString.of().setAsPreferredDealer,
                  verticalPadding: 4.h,
                  press: () async {
                    if (data.data == true) {
                      FireBaseAnalyticsLogger.logMarketingGroupEvent(
                          VehicleMarketingEvent.SERVICE_SCHEDULE,
                          childEventName:
                              VehicleMarketingEvent.PREFERREDDEALER_CHANGE);
                      if (widget.preferredDealerStatus ==
                          PreferredDealerStatus.SET_EXSIT_PREFERRED) {
                        NavigateService.pushNamedRoute(
                            RoutePath.FX_SELECTPREFERDEALERPAGE,
                            arguments: {
                              "preferredDealerStatus":
                                  PreferredDealerStatus.SET_EXSIT_PREFERRED,
                              "isFromNative": "false"
                            });
                      } else {
                        changeDealerClick();
                      }
                    } else {
                      FireBaseAnalyticsLogger.logMarketingGroupEvent(
                          VehicleMarketingEvent.SERVICE_SCHEDULE,
                          childEventName:
                              VehicleMarketingEvent.SET_AS_PREFERREDDEALER);
                      _bloc.updatePreferredDealer(dealerModel?.toyotaCode);
                    }
                  },
                ),
                visible: widget.preferredDealerStatus !=
                        PreferredDealerStatus.MAKE_APPOINTMENT &&
                    data.data != null &&
                    widget.isFromOdometerFlow == false &&
                    dealerModel != null,
              ),
              StreamBuilder(
                  stream: _bloc.dealerAppointmentAvailableUI,
                  builder: (ctx, AsyncSnapshot<bool> snapData) {
                    return snapData.data != null && dealerModel != null
                        ? CustomDefaultButton(
                            backgroundColor:
                                ThemeConfig.current().colorUtil.button01b,
                            buttonTextColor:
                                ThemeConfig.current().colorUtil.button01a,
                            borderColor:
                                ThemeConfig.current().colorUtil.button01b,
                            disabledBackgroundColor:
                                ThemeConfig.current().colorUtil.button02c,
                            text: snapData.data == true
                                ? (widget.preferredDealerStatus !=
                                        PreferredDealerStatus.MAKE_APPOINTMENT
                                    ? OneAppString.of().makeAnAppointment
                                    : OneAppString.of().selectDealer)
                                : OneAppString.of().callDealer,
                            verticalPadding: 4.h,
                            press: () {
                              if (snapData.data == true) {
                                if (widget.preferredDealerStatus ==
                                    PreferredDealerStatus.MAKE_APPOINTMENT) {
                                  FireBaseAnalyticsLogger.logMarketingGroupEvent(
                                      VehicleMarketingEvent.SERVICE_SCHEDULE,
                                      childEventName: VehicleMarketingEvent
                                          .SUPPORTDC_MAKEAPPOINTMENT_SELECTDEALER);
                                }
                                _clearDataAndNavigateToAppointment();
                              } else {
                                dialerLauncher(dealerModel.phoneNumber);
                              }
                            },
                          )
                        : shimmerRectangle(90.h, double.infinity, 8.r);
                  }),
            ],
          );
        });
  }

  _clearDataAndNavigateToAppointment() {
    _clearOldData();
    Navigator.of(context).pushNamed(
        RoutePath.VEHICLE_SERVICE_APPOINTMENT_ODOMETER_SETUP_PAGE,
        arguments: {
          "isInitialPage": true,
          "preferredDealerInfo": _bloc.preferredDealerInfo,
          "odometerValue":
              widget.odometerValue == "0" ? null : widget.odometerValue,
        });
  }

  Widget showSnapDataUI(AsyncSnapshot snapData) {
    if (snapData.data != null) {
      return bottomButton(snapData.data);
    } else {
      return snapData.error == null
          ? shimmerRectangle(180.h, double.infinity, 8.r)
          : Container();
    }
  }

  void _clearOldData() {
    _bloc.clearOldData();
  }

  MainAxisAlignment getAlignment(DealerSearchResultHelper? data) {
    bool hasPhone = data?.phoneNumber.isNotEmpty ?? false;
    bool hasWebsite = data?.website.isNotEmpty ?? false;
    bool hasDirection = (data?.longitude != null && data?.latitude != null);
    List dataList = [hasPhone, hasWebsite, hasDirection];
    int dataCount =
        dataList.where((element) => element == true).toList().length;
    MainAxisAlignment alignment = dataCount >= 3
        ? MainAxisAlignment.spaceBetween
        : dataCount >= 2
            ? MainAxisAlignment.spaceEvenly
            : dataCount == 1
                ? MainAxisAlignment.center
                : MainAxisAlignment.center;
    return alignment;
  }
}

class ListAdapter with SectionAdapterMixin {
  final VehicleDealerDetailBloc tableBloc;
  final List<TableData> data;

  ListAdapter(
    this.tableBloc,
    this.data,
  );

  List<String> dayList = [
    OneAppString.of().monday,
    OneAppString.of().tuesday,
    OneAppString.of().wednesday,
    OneAppString.of().thursday,
    OneAppString.of().friday,
    OneAppString.of().saturday,
    OneAppString.of().sunday,
  ];

  @override
  int numberOfSections() {
    return data.length;
  }

  @override
  int numberOfItems(int section) {
    return 1;
  }

  @override
  Widget getItem(BuildContext context, IndexPath indexPath) {
    TableData itemData = data[indexPath.section];
    List<String> item = getItemBySectionIndex(indexPath.section);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 16),
            alignment: Alignment.centerLeft,
            child: indexPath.section != 0
                ? Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: item
                        .asMap()
                        .keys
                        .map((e) => Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 18),
                                  child: Text(
                                    item[e].isNotEmpty == true
                                        ? "${item[e]}"
                                        : "",
                                    style: ThemeConfig.current()
                                        .textStyleUtil
                                        .body3
                                        .copyWith(
                                          color: ThemeConfig.current()
                                              .colorUtil
                                              .tertiary03,
                                        ),
                                  ),
                                ),
                                e + 1 != item.length
                                    ? Container(
                                        height: 1,
                                        color: ThemeConfig.current()
                                            .colorUtil
                                            .tertiary10,
                                      )
                                    : Container(),
                              ],
                            ))
                        .toList(),
                  )
                : opendayWidget(itemData),
          ),
          visible: itemData.isClick == true,
        ),
      ],
    );
  }

  @override
  Widget getSectionHeader(BuildContext context, int section) {
    var itemData = data[section];
    List<String> item = getItemBySectionIndex(section);
    return Visibility(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: InkWell(
          onTap: () {
            bool isClick = itemData.isClick;
            isClick = !isClick;
            itemData.isClick = isClick;
            tableBloc.updateSectionIndex(itemData, section);
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(
                Radius.circular(8.r),
              ),
              color: ThemeConfig.current().colorUtil.tile02,
            ),
            padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 16.w),
            child: Row(
              children: [
                SvgPicture.asset(
                  itemData.iconString,
                  height: 24.w,
                  width: 24.w,
                  colorFilter: ColorFilter.mode(
                    ThemeConfig.current().colorUtil.button02a,
                    BlendMode.srcIn,
                  ),
                ),
                SizedBox(
                  width: 16.w,
                ),
                Expanded(
                  child: Text(
                    itemData.title,
                    style: ThemeConfig.current().textStyleUtil.body4.copyWith(
                          color: ThemeConfig.current().colorUtil.tertiary03,
                        ),
                  ),
                ),
                SvgPicture.asset(
                  itemData.isClick == false
                      ? filterPlusIcon
                      : filterMinimizeIcon,
                  height: 24.w,
                  width: 24.w,
                  colorFilter: ColorFilter.mode(
                    ThemeConfig.current().colorUtil.button02a,
                    BlendMode.srcIn,
                  ),
                  semanticsLabel: FILTER_PLUS_MINIMIZE,
                ),
              ],
            ),
          ),
        ),
      ),
      visible: section != 0
          ? item.isNotEmpty == true
          : itemData.businessHours.isNotEmpty == true,
    );
  }

  @override
  Widget getSectionFooter(BuildContext context, int section) {
    var itemData = data[section];
    List<String> item = getItemBySectionIndex(section);
    return Visibility(
      visible: section != 0
          ? item.isNotEmpty == true
          : itemData.businessHours.isNotEmpty == true,
      child: Container(height: 8.h),
    );
  }

  @override
  bool shouldExistSectionHeader(int section) {
    return true;
  }

  @override
  bool shouldExistSectionFooter(int section) {
    return true;
  }

  @override
  bool shouldExistHeader() {
    return false;
  }

  List<String> getItemBySectionIndex(int section) {
    var itemData = data[section];
    List<String> item = [];
    switch (section) {
      case servicesIndex:
        {
          item = itemData.services;
          break;
        }
      case amenitiesIndex:
        {
          item = itemData.amenities;
          break;
        }
      case paymentMethodsIndex:
        {
          item = itemData.paymentOptions;
          break;
        }
      case accessibilityIndex:
        {
          item = itemData.accessibilityOptions;
          break;
        }
      case transportationHeadingIndex:
        {
          item = itemData.transportationOptions;
          break;
        }
    }
    return item;
  }

  Widget opendayWidget(TableData itemData) {
    List<String> dayList = [
      OneAppString.of().monday,
      OneAppString.of().tuesday,
      OneAppString.of().wednesday,
      OneAppString.of().thursday,
      OneAppString.of().friday,
      OneAppString.of().saturday,
      OneAppString.of().sunday,
    ];
    int today = DateTime.now().weekday;
    return Container(
      padding: EdgeInsets.only(top: 10.h),
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: dayList.map((e) {
          BusinessHours hours = BusinessHours();
          if (itemData.businessHours.isNotEmpty == true) {
            for (final BusinessHours item in itemData.businessHours) {
              bool? isExist =
                  item.dayOfWeek?.containsIgnoreCase(e, ignoreCase: true);
              if (isExist == true) {
                hours = item;
                break;
              }
            }
          }

          return Container(
            padding: EdgeInsets.only(bottom: 3.h),
            child: Row(
              children: [
                Expanded(
                    child: Text(e,
                        style: e == dayList[today - 1]
                            ? ThemeConfig.current()
                                .textStyleUtil
                                .callout3
                                .copyWith(
                                  color: ThemeConfig.current()
                                      .colorUtil
                                      .tertiary03,
                                )
                            : ThemeConfig.current()
                                .textStyleUtil
                                .callout1
                                .copyWith(
                                  color: ThemeConfig.current()
                                      .colorUtil
                                      .tertiary07,
                                ))),
                Expanded(
                    child: Text(tableBloc.getTimeScope(hours),
                        style: e == dayList[today - 1]
                            ? ThemeConfig.current()
                                .textStyleUtil
                                .callout3
                                .copyWith(
                                  color: ThemeConfig.current()
                                      .colorUtil
                                      .tertiary03,
                                )
                            : ThemeConfig.current()
                                .textStyleUtil
                                .callout1
                                .copyWith(
                                  color: ThemeConfig.current()
                                      .colorUtil
                                      .tertiary07,
                                ))),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
