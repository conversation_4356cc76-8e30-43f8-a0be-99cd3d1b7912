// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';

class MakeAppointmentWidget extends StatefulWidget {
  final Function? bottomButtonClick;
  final String? bottomButtonString;

  final Function? subButtonClick;
  final String? subButtonString;
  const MakeAppointmentWidget(
      {Key? key,
      this.bottomButtonClick,
      this.subButtonClick,
      this.bottomButtonString,
      this.subButtonString})
      : super(key: key);

  @override
  _MakeAppointmentWidgetState createState() => _MakeAppointmentWidgetState();
}

class _MakeAppointmentWidgetState extends State<MakeAppointmentWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 380.h,
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 24.h),
            child: SizedBox(
              width: 48.w,
              height: 48.w,
              child: CommonCircleIconImage(
                pinIcon,
                iconWidth: 24.w,
                iconHeight: 24.w,
                circleBackgroundColor:
                    ThemeConfig.current().colorUtil.secondary02,
                iconTintColor: ThemeConfig.current().colorUtil.secondary01,
              ),
            ),
            /*
            SvgPicture.asset(
              circleLocIcon,
              height: 48.w,
              width: 48.w,
            ),

                 */
          ),
          Text(
            OneAppString.of().makeAnAppointment,
            style: ThemeConfig.current().textStyleUtil.subHeadline1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary03,
                ),
          ),
          SizedBox(
            height: 10.h,
          ),
          Text(
            OneAppString.of().makeAppointmentTips,
            style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary05,
                ),
            textAlign: TextAlign.center,
          ),
          Expanded(child: Container()),
          CustomDefaultButton(
            backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
            buttonTextColor: ThemeConfig.current().colorUtil.button02a,
            borderColor: ThemeConfig.current().colorUtil.tertiary15,
            // disabledBackgroundColor: ThemeConfig.current().colorUtil.tertiary15,
            text: widget.subButtonString,
            verticalPadding: 4.h,
            press: widget.subButtonClick,
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: CustomDefaultButton(
              backgroundColor: ThemeConfig.current().colorUtil.button01b,
              buttonTextColor: ThemeConfig.current().colorUtil.button01a,
              borderColor: ThemeConfig.current().colorUtil.button01b,
              disabledBackgroundColor:
                  ThemeConfig.current().colorUtil.button02c,
              text: widget.bottomButtonString,
              verticalPadding: 4.h,
              press: widget.bottomButtonClick,
            ),
          ),
          SizedBox(
            height: 32.h,
          ),
        ],
      ),
    );
  }
}
