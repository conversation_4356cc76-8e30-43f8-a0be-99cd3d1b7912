// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';

class DealerDoNotOfferSchedulingWidget extends StatefulWidget {
  final Function? bottomButtonClick;
  final String? bottomButtonString;

  final Function? subButtonClick;
  final String? subButtonString;
  const DealerDoNotOfferSchedulingWidget(
      {Key? key,
      this.bottomButtonClick,
      this.bottomButtonString,
      this.subButtonClick,
      this.subButtonString})
      : super(key: key);

  @override
  _DealerDoNotOfferSchedulingWidgetState createState() =>
      _DealerDoNotOfferSchedulingWidgetState();
}

class _DealerDoNotOfferSchedulingWidgetState
    extends State<DealerDoNotOfferSchedulingWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 500.h,
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 24.h),
            child: SvgPicture.asset(
              pvlMapErrorIcon,
              height: 48.w,
              width: 48.w,
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8.h),
            child: Text(
              OneAppString.of().schedulingInAppUnavailable,
              style: ThemeConfig.current().textStyleUtil.subHeadline1.copyWith(
                    color: ThemeConfig.current().colorUtil.tertiary03,
                  ),
            ),
          ),
          Text(
            OneAppString.of().offerSchedulingTips,
            style: ThemeConfig.current().textStyleUtil.callout1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary05,
                ),
            textAlign: TextAlign.center,
          ),
          Expanded(child: Container()),
          CustomDefaultButton(
            backgroundColor: ThemeConfig.current().colorUtil.tertiary15,
            buttonTextColor: ThemeConfig.current().colorUtil.button02a,
            borderColor: ThemeConfig.current().colorUtil.tertiary15,
            // disabledBackgroundColor: ThemeConfig.current().colorUtil.tile03,
            text: widget.subButtonString,
            verticalPadding: 4.h,
            press: widget.subButtonClick,
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: CustomDefaultButton(
              backgroundColor: ThemeConfig.current().colorUtil.button01b,
              buttonTextColor: ThemeConfig.current().colorUtil.button01a,
              borderColor: ThemeConfig.current().colorUtil.button01b,
              disabledBackgroundColor:
                  ThemeConfig.current().colorUtil.button02c,
              text: widget.bottomButtonString,
              verticalPadding: 4.h,
              press: widget.bottomButtonClick,
            ),
          ),
          SizedBox(
            height: 32.h,
          ),
        ],
      ),
    );
  }
}
