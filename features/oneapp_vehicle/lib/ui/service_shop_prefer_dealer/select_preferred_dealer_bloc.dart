// Dart imports:
import 'dart:async';

// Package imports:
import 'package:geocoding/geocoding.dart' as Geocoding;
import 'package:get_it/get_it.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network/oneapp_network_packages.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/DealerSearchFilter.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_shop_get_dealer_detail_by_coordinates.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/select_preferred_dealer_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_search_dealer_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

// Flutter imports:

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class SelectPreferredDealerBloc implements BlocBase {
  OneAppClient api = APIClientConfig.oneAppClient;

  Position? currentLocation;
  List<DealerSearchResultHelper> resultDealerListData = [];

  final _overViewMapPinCtl = BehaviorSubject<bool>();

  Stream<bool> get overViewMapPinUI => _overViewMapPinCtl.stream;

  final _getResultDealerDataCtl =
      BehaviorSubject<List<DealerSearchResultHelper>>();

  Stream<List<DealerSearchResultHelper>> get getResultDealerDataUI =>
      _getResultDealerDataCtl.stream;

  Position? distanceLocation;

  vehicleInfo.Payload? _vehicleInfoPayload;

  final _searchText = BehaviorSubject<bool>();

  Stream<bool> get searchText => _searchText.stream;

  final _updateSelectData = BehaviorSubject<bool>();

  Stream<bool> get updateSelectDataUI => _updateSelectData.stream;

  Stream<bool> get appLocationDenied => _appLocationDenied.stream;
  final _appLocationDenied = BehaviorSubject<bool>();

  final defaultLocation = Position(-96.84320, 33.08118);
  double milesToRadius = 0;
  double defaultRadius = 40250;

  void updateSelectData() {
    _updateSelectData.add(true);
  }

  void init() async {
    await showProgress(NavigateService.context);
    String? vin = Global.getInstance().vin;
    _vehicleInfoPayload = await VehicleRepo().getLocalPayloadFromVin(vin);
    if (await Permission.location.request().isGranted) {
      currentLocation = await MapUtil.getCurrentLocation();
    } else {
      await dismissProgress(NavigateService.context);
      _appLocationDenied.add(true);
      return;
    }

    getADSFilterList();

    await getDealerDataByCoordinates(
      currentLocation!.lat.toDouble(),
      currentLocation!.lng.toDouble(),
    );
    await dismissProgress(NavigateService.context);
  }

  Future<void> requestTotalDealer() async {
    List<String> brandList = [];
    if (_vehicleInfoPayload?.brand == "T") {
      brandList.add("Toyota");
    } else {
      brandList.add("Lexus");
    }

    if (_vehicleInfoPayload?.region != null &&
        _vehicleInfoPayload?.brand != null) {
      final commonResponse = await api.getAllDealerships(
          brand: brandList,
          region: _vehicleInfoPayload!.region,
          headerBrand: _vehicleInfoPayload!.brand);
      resultDealerListData.clear();
      if (commonResponse.response != null &&
          commonResponse.response?.payload?.dealerships?.isNotEmpty == true) {
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.GET_TOTAL_DEALER_WITH_FILTER_MODEL_SUCCESS,
            category: LogCategory.FL_SESH);
        for (final element in commonResponse.response!.payload!.dealerships!) {
          final double? distance = await getDistanceMetre(
            element.addresses?.first.coordinate?.latitude,
            element.addresses?.first.coordinate?.longitude,
          );
          resultDealerListData.add(DealerSearchResultHelper(
            element.accessibilityOptions ?? [],
            element.amenities ?? [],
            element.brand ?? '',
            element.dealershipName ?? '',
            element.emails ?? [],
            element.paymentOptions ?? [],
            element.services ?? [],
            element.smartPath ?? false,
            element.spmLogo ?? '',
            element.toyotaCode ?? '',
            element.transportationOptions ?? [],
            element.website ?? '',
            element.phoneNumbers?.first.number ?? '',
            element.addresses?.first.coordinate?.latitude ?? 0,
            element.addresses?.first.coordinate?.longitude ?? 0,
            element.addresses?.first.line1 ?? '',
            element.addresses?.first.line2 ?? '',
            distance ?? 0,
            element.addresses?.first.city ?? '',
            element.addresses?.first.state ?? '',
            element.addresses?.first.country ?? '',
            element.addresses?.first.zipCode ?? '',
          ));
        }
        resultDealerListData
            .sort(((left, right) => left.distance.compareTo(right.distance)));

        _getResultDealerDataCtl.add(resultDealerListData);
      }
      if (commonResponse.error != null) {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.GET_TOTAL_DEALER_WITH_FILTER_MODEL_FAILURE,
            category: LogCategory.FL_SESH);
      }
    }
  }

  vehicleInfo.Payload? get vehicleInfoPayload {
    return _vehicleInfoPayload;
  }

  void requestTotalDataWithModel(
    DealerFilterModel filterModel, {
    double? latitude,
    double? longitude,
  }) async {
    await showProgress(NavigateService.context);
    List<String> brandList = [];
    if (_vehicleInfoPayload?.brand == "T") {
      brandList.add("Toyota");
    } else {
      brandList.add("Lexus");
    }

    if (_vehicleInfoPayload?.region != null &&
        _vehicleInfoPayload?.brand != null) {
      if (filterModel.limit != null && filterModel.limit! > 0) {
        milesToRadius = filterModel.limit! * 1609.34;
      } else {
        milesToRadius = defaultRadius;
      }
      final commonResponse = await api.getAllDealerships(
          latitude: latitude ?? 0.0,
          longitude: longitude ?? 0.0,
          accessibilityOption: filterModel.accessibilityOption,
          amenity: filterModel.amenity,
          dayOfWeek: filterModel.dayOpen,
          sortBy: filterModel.sortBy,
          sortDir: filterModel.sortDir,
          paymentOption: filterModel.paymentOption,
          q: filterModel.q,
          service: filterModel.service,
          transportationOption: filterModel.transportationOption,
          brand: brandList,
          region: _vehicleInfoPayload!.region,
          headerBrand: _vehicleInfoPayload!.brand,
          smartPath: filterModel.smartPath,
          radius: milesToRadius,
          onChangedCoordinates: () async {
            ParseSearchLocation? parseLocation =
                await parseAddress(filterModel.q);
            return parseLocation;
          },
          firebaseLogger: (isSuccess) {
            if (isSuccess) {
              FireBaseAnalyticsLogger.logSuccessAPI(
                  VehicleAnalyticsEvent.GET_DEALER_BY_COORDINATES_SUCCESS,
                  category: LogCategory.FL_SESH);
            } else {
              FireBaseAnalyticsLogger.logErrorAPI(
                  VehicleAnalyticsEvent.GET_DEALER_BY_COORDINATES_FAILURE,
                  category: LogCategory.FL_SESH);
            }
          });
      await dismissProgress(NavigateService.context);
      resultDealerListData.clear();
      if (commonResponse.response != null) {
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.GET_TOTAL_DEALER_WITH_FILTER_MODEL_SUCCESS,
            category: LogCategory.FL_SESH);
        for (final element in commonResponse.response!.payload!.dealerships!) {
          final double? distance = await getDistanceMetre(
            element.addresses?.first.coordinate?.latitude,
            element.addresses?.first.coordinate?.longitude,
          );
          resultDealerListData.add(DealerSearchResultHelper(
            element.accessibilityOptions ?? [],
            element.amenities ?? [],
            element.brand ?? '',
            element.dealershipName ?? '',
            element.emails ?? [],
            element.paymentOptions ?? [],
            element.services ?? [],
            element.smartPath ?? false,
            element.spmLogo ?? '',
            element.toyotaCode ?? '',
            element.transportationOptions ?? [],
            element.website ?? '',
            element.phoneNumbers?.first.number ?? '',
            element.addresses?.first.coordinate?.latitude ?? 0,
            element.addresses?.first.coordinate?.longitude ?? 0,
            element.addresses?.first.line1 ?? '',
            element.addresses?.first.line2 ?? '',
            distance ?? 0,
            element.addresses?.first.city ?? '',
            element.addresses?.first.state ?? '',
            element.addresses?.first.country ?? '',
            element.addresses?.first.zipCode ?? '',
          ));
        }
        if (filterModel.q == null ||
            commonResponse.response?.allowSort == true) {
          resultDealerListData
              .sort(((left, right) => left.distance.compareTo(right.distance)));
        }
        _getResultDealerDataCtl.add(resultDealerListData);
      } else {
        _getResultDealerDataCtl.add(resultDealerListData);
      }
      if (commonResponse.error != null) {
        _getResultDealerDataCtl.add(resultDealerListData);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.GET_TOTAL_DEALER_WITH_FILTER_MODEL_FAILURE,
            category: LogCategory.FL_SESH);
      }
    }
  }

  Future<double?> getDistanceMetre(double? latitude, double? longitude) async {
    double distanceInMeters = 0;
    if (latitude == null || longitude == null || currentLocation == null) {
      return null;
    }
    distanceInMeters = await MapUtil.distanceBetween(
      currentLocation!.lat.toDouble(),
      currentLocation!.lng.toDouble(),
      latitude,
      longitude,
    );
    bool isCanada = Global.getInstance()
        .locale
        .countryCode!
        .containsIgnoreCase("CA", ignoreCase: true);
    if (isCanada) {
      return double.tryParse((distanceInMeters * 0.001).toStringAsFixed(2));
    } else {
      return double.tryParse((distanceInMeters * 0.0006214).toStringAsFixed(2));
    }
  }

  void filterById(String locationId) {
    final List<DealerSearchResultHelper> list = [];
    resultDealerListData.forEach((element) {
      if (element.toyotaCode == locationId) {
        list.add(element);
      }
    });
    _getResultDealerDataCtl.add(list);
  }

  void overViewMapPinClick(bool isClick) {
    _overViewMapPinCtl.add(isClick);
  }

  void getAllDealershipsData() {
    _getResultDealerDataCtl.add(resultDealerListData);
  }

  Future<void> clearSearchCloseIcon() async {
    _searchText.add(false);
    if (await Permission.location.request().isGranted) {
      currentLocation = await MapUtil.getCurrentLocation();
      if (currentLocation?.lat != null && currentLocation?.lng != null) {
        await showProgress(NavigateService.context);
        await getDealerDataByCoordinates(
          currentLocation!.lat.toDouble(),
          currentLocation!.lng.toDouble(),
        );
        await dismissProgress(NavigateService.context);
      }
    }
  }

  void showSearchCloseIcon() {
    _searchText.add(true);
  }

  final _isTFEditingCtl = BehaviorSubject<bool>();

  Stream<bool> get getTFEditingModeUI => _isTFEditingCtl.stream;

  void editTFMode(bool isEdit) {
    _isTFEditingCtl.add(isEdit);
  }

  void getDealerDataByAddress(
    String address,
  ) async {
    DealerFilterModel filterModel = DealerFilterModel();
    filterModel.q = address;
    if (currentLocation != null) {
      requestTotalDataWithModel(filterModel,
          latitude: currentLocation!.lat.toDouble(),
          longitude: currentLocation!.lng.toDouble());
    } else {
      requestTotalDataWithModel(filterModel, latitude: 0.0, longitude: 0.0);
    }
  }

  Future<void> getDealerDataByCoordinates(double latitude, double longitude,
      {DealerFilterModel? filterModel}) async {
    List<String> brandList = [];
    CommonResponse<ServiceShopGetDealerDetailByCoordinates> commonResponse;
    if (_vehicleInfoPayload?.brand == "T") {
      brandList.add("Toyota");
    } else {
      brandList.add("Lexus");
    }

    if (_vehicleInfoPayload?.region != null &&
        _vehicleInfoPayload?.brand != null) {
      if (filterModel != null) {
        await showProgress(NavigateService.context);
        if (filterModel.limit != null && filterModel.limit! > 0) {
          milesToRadius = filterModel.limit! * 1609.34;
        } else {
          milesToRadius = defaultRadius;
        }
        commonResponse = await api.getDealerDetailByCoordinates(
            latitude, longitude,
            radius: milesToRadius,
            headerRegion: _vehicleInfoPayload!.region,
            headerBrand: _vehicleInfoPayload!.brand,
            brand: brandList,
            dayOpen: filterModel.dayOpen,
            amenity: filterModel.amenity,
            sortBy: filterModel.sortBy,
            sortDir: filterModel.sortDir,
            paymentOption: filterModel.paymentOption,
            service: filterModel.service,
            accessibilityOption: filterModel.accessibilityOption,
            transportationOption: filterModel.transportationOption,
            q: filterModel.q,
            smartPathDealer: filterModel.smartPath);
      } else {
        commonResponse = await api.getDealerDetailByCoordinates(
            latitude, longitude,
            radius: defaultRadius,
            headerRegion: _vehicleInfoPayload!.region,
            headerBrand: _vehicleInfoPayload!.brand,
            brand: brandList);
      }
      await dismissProgress(NavigateService.context);
      resultDealerListData.clear();
      if (commonResponse.response != null &&
          commonResponse.response?.payload?.dealerships?.isNotEmpty == true) {
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.GET_DEALER_BY_COORDINATES_SUCCESS,
            category: LogCategory.FL_SESH);
        for (final element in commonResponse.response!.payload!.dealerships!) {
          final double? distance = await getDistanceMetre(
            element.addresses?.first.coordinate?.latitude,
            element.addresses?.first.coordinate?.longitude,
          );
          resultDealerListData.add(DealerSearchResultHelper(
            element.accessibilityOptions ?? [],
            element.amenities ?? [],
            element.brand ?? '',
            element.dealershipName ?? '',
            element.emails ?? [],
            element.paymentOptions ?? [],
            element.services ?? [],
            element.smartPath ?? false,
            element.spmLogo ?? '',
            element.toyotaCode ?? '',
            element.transportationOptions ?? [],
            element.website ?? '',
            getPhoneNumber(element.phoneNumbers) ?? '',
            element.addresses?.first.coordinate?.latitude ?? 0,
            element.addresses?.first.coordinate?.longitude ?? 0,
            element.addresses?.first.line1 ?? '',
            element.addresses?.first.line2 ?? '',
            distance ?? 0,
            element.addresses?.first.city ?? '',
            element.addresses?.first.state ?? '',
            element.addresses?.first.country ?? '',
            element.addresses?.first.zipCode ?? '',
          ));
        }
        resultDealerListData
            .sort(((left, right) => left.distance.compareTo(right.distance)));
        _getResultDealerDataCtl.add(resultDealerListData);
      } else {
        _getResultDealerDataCtl.add(resultDealerListData);
      }
      if (commonResponse.error != null) {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.GET_DEALER_BY_COORDINATES_FAILURE,
            category: LogCategory.FL_SESH);
      }
    }
  }

  void getDeviceLocation() async {
    await showProgress(NavigateService.context);
    if (await Permission.location.request().isGranted) {
      currentLocation = await MapUtil.getCurrentLocation();
    } else {
      await dismissProgress(NavigateService.context);
      _appLocationDenied.add(true);
      return;
    }
    await getDealerDataByCoordinates(
      currentLocation?.lat.toDouble() ?? defaultLocation.lat.toDouble(),
      currentLocation?.lng.toDouble() ?? defaultLocation.lng.toDouble(),
    );
    await dismissProgress(NavigateService.context);
  }

  Future<ParseSearchLocation?> parseAddress(String? address) async {
    ParseSearchLocation? parseLocation;
    if (address != null && address.isNotEmpty) {
      try {
        List<Geocoding.Location> locations =
            await Geocoding.locationFromAddress(address);
        final element = locations.first;

        parseLocation =
            ParseSearchLocation(lat: element.latitude, long: element.longitude);
      } catch (e) {
        parseLocation = null;
        FireBaseAnalyticsLogger.logError(e.toString(),
            category: LogCategory.FL_SESH);
      }
    }
    return parseLocation;
  }

  String addDistanceUnit(double distance) {
    return distance.toString() +
        " " +
        distanceMetric(_vehicleInfoPayload!.region!);
  }

  @override
  void dispose() {
    _getResultDealerDataCtl.close();
  }

  String? getPhoneNumber(List<PhoneNumbers>? phoneNumbers) {
    String? phone = '';
    if (phoneNumbers != null && phoneNumbers.isNotEmpty) {
      phone = phoneNumbers.first.number;
    }
    return phone;
  }

  Future<void> getADSFilterList() async {
    String xBrand = _vehicleInfoPayload?.brand ?? Global.getInstance().appBrand;
    GetIt locator = GetIt.instance;
    DealerFilterModel filterList = DealerFilterModel();
    var filterPayLoad;
    try {
      filterPayLoad = locator<DealerFilterModel>();
    } catch (e) {
      filterPayLoad = null;
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_VEHI);
    }

    bool isFilterAvailable =
        isFeatureEnabled(ADVANCED_DEALER_SEARCH, _vehicleInfoPayload!.features);

    if ((filterPayLoad == null ||
            filterPayLoad.service == null ||
            filterPayLoad.service?.isEmpty == true) &&
        isFilterAvailable) {
      final commonResponse = await api.fetchADSFilterList(xBrand);
      if (commonResponse.response?.filters != null) {
        Filters filterData = commonResponse.response!.filters!;
        filterList.service = filterData.services ?? [];
        filterList.amenity = filterData.amenities ?? [];
        filterList.paymentOption = filterData.payments ?? [];
        filterList.accessibilityOption = filterData.accessibility ?? [];
        filterList.transportationOption = filterData.transportation ?? [];
        locator.registerSingleton<DealerFilterModel>(filterList);
      }
    }
  }
}
