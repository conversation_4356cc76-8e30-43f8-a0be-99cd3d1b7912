// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';

class SlideTitle extends StatefulWidget {
  final String? title;

  final VoidCallback? onBackPressed;

  final Widget? menuItem;

  final double? paddingTop;

  const SlideTitle(
      {Key? key,
      this.title,
      this.onBackPressed,
      this.menuItem,
      this.paddingTop})
      : super(key: key);

  @override
  _SlideTitleState createState() => _SlideTitleState();
}

class _SlideTitleState extends State<SlideTitle> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: widget.paddingTop ?? 16.h,
        ),
        _slideLine(),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 9.h,
        ),
        Row(
          children: [
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              child: _backActionItem(),
            ),
            _titleItem(),
            Expanded(
              child: _menuActionItem(),
            ),
          ],
        ),
        SizedBox(
          height: 23.h,
        ),
      ],
    );
  }

  Widget _backActionItem() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Opacity(
        opacity: widget.onBackPressed != null ? 1.0 : 0,
        child: IconButton(
          // padding: EdgeInsets.only(left: 16),
          padding: EdgeInsets.zero,
          alignment: Alignment.centerLeft,
          icon: SvgPicture.asset(
            circleBackIcon,
            width: 72.w,
            height: 72.h,
          ),
          color: Colors.black,
          onPressed: widget.onBackPressed,
        ),
      ),
    );
  }

  Widget _menuActionItem() {
    return Align(
      alignment: Alignment.centerRight,
      child: Padding(
        padding: EdgeInsets.only(right: 16.w),
        child: widget.menuItem,
      ),
    );
  }

  Widget _titleItem() {
    return Align(
      alignment: Alignment.center,
      child: Padding(
        padding: EdgeInsets.only(left: 16.w, right: 16.w),
        child: Text(
          widget.title ?? '',
          style: ThemeConfig.current().textStyleUtil.subHeadline3,
        ),
      ),
    );
  }

  Widget _slideLine() {
    return Container(
      height: 4.h,
      width: 28.w,
      decoration: BoxDecoration(
        color: ThemeConfig.current().colorUtil.tertiary10,
        borderRadius: BorderRadius.circular(100),
      ),
    );
  }
}
