// Dart imports:
import 'dart:async';
import 'dart:convert';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:location/location.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/flexcfai/entity_helper/toast_msg_helper.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_appointment_advisor_list_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_appointment_service_list_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_appointment_transportation_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_shop_get_dealership_detail_by_id_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/select_preferred_dealer_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/vehicle_preferred_dealer_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/dealer_info_entity.dart'
    as dealerInfo;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleDealerDetailBloc implements BlocBase {
  OneAppClient api = APIClientConfig.oneAppClient;
  GetIt locator = GetIt.instance;

  final _getDealerDataByIdCtl = BehaviorSubject<DealerSearchResultHelper>();

  Stream<DealerSearchResultHelper> get getDealerDataByIdUI =>
      _getDealerDataByIdCtl.stream;

  final _tableListaDataCtl = BehaviorSubject<List<TableData>>();

  Stream<List<TableData>> get tableListaDataUI => _tableListaDataCtl.stream;

  Stream<PreferredDealerButtonStatus> get saveButtonState =>
      _saveButtonState.stream;
  final _saveButtonState = BehaviorSubject<PreferredDealerButtonStatus>();

  final _toastController = BehaviorSubject<ToastMsgData>();

  Stream<ToastMsgData> get uiToast => _toastController.stream;

  Stream<bool> get appLocationDenied => _appLocationDenied.stream;
  final _appLocationDenied = BehaviorSubject<bool>();

  Location location = Location();
  Position? currentLocation;

  dealerInfo.DealerInfoPayload? preferredDealerInfo;

  List<String> iconList = [
    filterServiceHoursIcon,
    filterServicesIcon,
    filterWifiIcon,
    filterPaymentIcon,
    filterAsseceibilityIcon,
    filterTransportationIcon,
  ];
  List<String> titleList = [
    OneAppString.of().serviceHours,
    OneAppString.of().services,
    OneAppString.of().amenities,
    OneAppString.of().paymentMethods,
    OneAppString.of().accessibility,
    OneAppString.of().transportationHeading
  ];

  List<TableData> data = [];
  String? odometerValue = "0";
  String? region = "US";

  void init(String dealerId) async {
    Permission.location.request().isGranted.then((granted) async {
      if (granted) {
        currentLocation = await MapUtil.getCurrentLocation();
      } else {
        _appLocationDenied.add(true);
      }
    });
    fetchDealerInfoDetails(dealerId);
    fetchPreferredServiceDealer(dealerId);
    String? vin = Global.getInstance().vin;
    vehicleInfo.Payload? vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(vin);
    final commonResponse = await api.getDealerShipsDetailById(
      dealerId,
      vehicleItem?.region,
      headerBrand: vehicleItem?.brand,
    );

    if (vehicleItem?.region != null) {
      region = vehicleItem?.region;
    }

    // _getDealerDataByIdCtl.add(commonResponse.response.payload.dealership);

    if (commonResponse.response != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.GET_DEALER_BY_DEALER_ID_SUCCESS,
          category: LogCategory.FL_SESH);
      double? distance = await getDistanceMetre(
        commonResponse.response!.payload!.dealership?.addresses?.first
            .coordinate?.latitude,
        commonResponse.response!.payload!.dealership?.addresses?.first
            .coordinate?.longitude,
      );
      _getDealerDataByIdCtl.add(DealerSearchResultHelper(
          commonResponse.response!.payload!.dealership!.accessibilityOptions!,
          commonResponse.response!.payload!.dealership!.amenities!,
          commonResponse.response!.payload!.dealership!.brand!,
          commonResponse.response!.payload!.dealership!.dealershipName!,
          commonResponse.response!.payload!.dealership!.emails!,
          commonResponse.response!.payload!.dealership!.paymentOptions!,
          commonResponse.response!.payload!.dealership!.services!,
          commonResponse.response!.payload!.dealership!.smartPath!,
          commonResponse.response!.payload!.dealership!.spmLogo!,
          commonResponse.response!.payload!.dealership!.toyotaCode!,
          commonResponse.response!.payload!.dealership!.transportationOptions!,
          commonResponse.response!.payload!.dealership!.website!,
          getPhoneNumber(
              commonResponse.response!.payload!.dealership!.phoneNumbers)!,
          commonResponse.response!.payload!.dealership!.addresses!.first
              .coordinate!.latitude!,
          commonResponse.response!.payload!.dealership!.addresses!.first
              .coordinate!.longitude!,
          commonResponse.response!.payload!.dealership!.addresses!.first.line1!,
          commonResponse.response!.payload!.dealership!.addresses!.first.line2!,
          distance ?? 0,
          commonResponse.response!.payload!.dealership!.addresses!.first.city!,
          commonResponse.response!.payload!.dealership!.addresses!.first.state!,
          commonResponse
              .response!.payload!.dealership!.addresses!.first.country!,
          commonResponse
              .response!.payload!.dealership!.addresses!.first.zipCode!));
      data = iconList
          .asMap()
          .keys
          .map(
            (e) => TableData(
              false,
              iconList[e],
              titleList[e],
              commonResponse.response!.payload!.dealership!.businessHours!,
              commonResponse.response!.payload!.dealership!.services!,
              commonResponse.response!.payload!.dealership!.amenities!,
              commonResponse.response!.payload!.dealership!.paymentOptions!,
              commonResponse
                  .response!.payload!.dealership!.accessibilityOptions!,
              commonResponse
                  .response!.payload!.dealership!.transportationOptions!,
            ),
          )
          .toList();
      _tableListaDataCtl.add(data);
      saveDealerData(commonResponse.response?.payload?.dealership);
    } else {
      _getDealerDataByIdCtl.addError(commonResponse.error!);
      _tableListaDataCtl.add([]);
    }
    debugPrint("---commonResponse.response----${commonResponse.response}");

    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.GET_TOTAL_DEALER_WITH_FILTER_MODEL_FAILURE,
          category: LogCategory.FL_SESH);
    }
  }

  void updateSectionIndex(TableData tableData, int section) {
    data.removeAt(section);
    data.insert(section, tableData);
    _tableListaDataCtl.add(data);
  }

  Future<double?> getDistanceMetre(double? latitude, double? longitude) async {
    double distanceInMeters = 0;
    if (latitude == null || longitude == null || currentLocation == null) {
      return null;
    }
    distanceInMeters = await MapUtil.distanceBetween(
        currentLocation?.lat.toDouble() ?? 0,
        currentLocation?.lng.toDouble() ?? 0,
        latitude,
        longitude);
    bool isCanada = Global.getInstance()
        .locale
        .countryCode!
        .containsIgnoreCase("CA", ignoreCase: true);
    if (isCanada) {
      return double.tryParse((distanceInMeters * 0.001).toStringAsFixed(2));
    } else {
      return double.tryParse((distanceInMeters * 0.0006214).toStringAsFixed(2));
    }
  }

  Future<void> updatePreferredDealer(String? dealerCode) async {
    // progressHandlerCallback(true);
    if (dealerCode == null) {
      return;
    }
    await showProgress(NavigateService.context);
    String vin = Global.getInstance().vin!;
    String? guid = Global.getInstance().guid;
    String brandCode = Global.getInstance().appBrand;
    VehiclePreferredDealerHelper requestBody = VehiclePreferredDealerHelper(
        dlrCode: dealerCode,
        guid: guid,
        vin: vin,
        brandCode: brandCode,
        region: region);

    final commonResponse = await api.updatePreferredDealer(vin, requestBody);
    await dismissProgress(NavigateService.context);

    // progressHandlerCallback(false);
    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_UPDATE_PREFERRED_SERVICEDEALER_FAILURE,
          category: LogCategory.FL_SESH);
    } else {
      if (commonResponse.response!.code == 200) {
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent
                .VEHICLE_UPDATE_PREFERRED_SERVICEDEALER_SUCCESS,
            category: LogCategory.FL_SESH);
        _saveButtonState.add(PreferredDealerButtonStatus.MAKE_APPOINTMENT);

        await showProgress(NavigateService.context);
        locator.isReady<List<vehicleInfo.Payload>>().then((value) async {
          List<vehicleInfo.Payload> vehicleListPayload = [];
          try {
            vehicleListPayload = locator<List<vehicleInfo.Payload>>();
            if (vehicleListPayload.isNotEmpty) {
              vehicleInfo.Payload selectedVinPayload =
                  vehicleListPayload.firstWhere(
                      (element) => element.vin == Global.getInstance().vin);
              if (is21MMVehicle(selectedVinPayload.generation)) {
                savePreferredDealerToUserProfile(
                    vin, jsonEncode(preferredDealerInfo));
              }
            }
          } catch (e) {}
        });
        await dismissProgress(NavigateService.context);

        _toastController.add(
            ToastMsgData(OneAppString.of().successSetYourPreferredDealerTip));
        _preferredDealer.sink.add(true);
      } else {
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent
                .VEHICLE_UPDATE_PREFERRED_SERVICEDEALER_FAILURE,
            category: LogCategory.FL_SESH);
      }
    }
  }

  void saveButtonClick() {
    _saveButtonState.add(PreferredDealerButtonStatus.MAKE_APPOINTMENT);
  }

  String getTimeScope(BusinessHours hours) {
    try {
      String openField = "";
      if ((hours.openingTime == "00:00" || hours.openingTime == "0000") &&
          (hours.closingTime == "00:00" || hours.closingTime == "0000")) {
        return OneAppString.of().closed.capitalize();
      }
      if (hours.openingTime != null) {
        final openDate = DateFormat("HH").parse(hours.openingTime!);
        openField =
            "${(openDate.hour % 12)}:${hours.openingTime!.split(':').last} ${(openDate.hour ~/ 13) == 1 ? 'PM' : 'AM'}";
      }
      String closeField = "";
      if (hours.closingTime != null) {
        final closeDate = DateFormat("HH").parse(hours.closingTime!);
        closeField =
            "${(closeDate.hour % 12)}:${hours.closingTime!.split(':').last} ${(closeDate.hour ~/ 13) == 1 ? 'PM' : 'AM'}";
      }
      if (openField.isNotEmpty == true || closeField.isNotEmpty == true) {
        return "$openField - $closeField";
      } else {
        return OneAppString.of().closed.capitalize();
      }
    } catch (_) {}
    return OneAppString.of().closed.capitalize();
  }

  Stream<bool> get preferredDealerUI => _preferredDealer.stream;
  final _preferredDealer = BehaviorSubject<bool>();

  Future<void> fetchPreferredServiceDealer(String dealerCode) async {
    String vin = Global.getInstance().vin!;
    vehicleInfo.Payload? vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(vin);
    if (vehicleItem?.region == null) {
      _preferredDealer.sink.add(false);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_SERVICEDEALER_FAILURE,
          category: LogCategory.FL_SESH);
      return;
    }
    final commonResponse = await api.fetchPreferredDealer(
      vehicleItem!.region!,
      vin,
      vehicleBrand: vehicleItem.brand,
    );
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null && payLoad.isNotEmpty) {
      _preferredDealer.sink.add(false);
      for (final dealer in payLoad) {
        if (dealer.dealerCode == dealerCode) {
          _preferredDealer.sink.add(true);
        }
      }
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_SERVICEDEALER_SUCCESS,
          category: LogCategory.FL_SESH);
    } else {
      _preferredDealer.sink.add(false);
    }
    if (commonResponse.error != null) {
      _preferredDealer.sink.add(false);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_SERVICEDEALER_FAILURE,
          category: LogCategory.FL_SESH);
    }
  }

  void setPreferredDealerSuccess() {
    _preferredDealer.sink.add(true);
  }

  Stream<bool> get dealerAppointmentAvailableUI =>
      _dealerAppointmentAvailable.stream;
  final _dealerAppointmentAvailable = BehaviorSubject<bool>();

  Future<void> fetchDealerInfoDetails(String dealerCode) async {
    String? vin = Global.getInstance().vin;
    vehicleInfo.Payload? vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(vin);
    if (isFeatureEnabled(DEALER_APPOINTMENTS, vehicleItem?.features)) {
      final commonResponse = await api.fetchServiceAppointmentInitializeDetail(
          dealerCode, vin!,
          vehicleBrand: vehicleItem?.brand);
      if (commonResponse.error == null && commonResponse.response != null) {
        bool appointmentAvailable =
            commonResponse.response!.schedulingAvailable ?? false;
        _dealerAppointmentAvailable.add(appointmentAvailable);
        if (commonResponse.response!.odometer != null &&
            commonResponse.response!.odometer != "1") {
          odometerValue = commonResponse.response!.odometer;
          Global.getInstance().serviceMileage = odometerValue!;
        } else {
          odometerValue = Global.getInstance().mileage;
          Global.getInstance().serviceMileage = odometerValue!;
        }
        locator.registerSingletonAsync(
          () async => commonResponse.response!.id!,
          instanceName: SERVICE_APPOINTMENT_ID,
        );
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.DEALER_SERVICE_APPOINTMENT_INITIALIZE_SUCCESS,
            category: LogCategory.FL_SESH);
      } else {
        _dealerAppointmentAvailable.add(false);
      }
      Locale locale = Global.getInstance().locale;
      if (locale.countryCode!.containsIgnoreCase("CA", ignoreCase: true)) {
        _dealerAppointmentAvailable.add(false);
      }
      if (commonResponse.error != null) {
        _dealerAppointmentAvailable.add(false);
        odometerValue = Global.getInstance().mileage;
        Global.getInstance().serviceMileage = odometerValue!;
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.DEALER_SERVICE_APPOINTMENT_INITIALIZE_FAILURE,
            category: LogCategory.FL_SESH);
      }
    } else {
      _dealerAppointmentAvailable.add(false);
    }
  }

  void saveDealerData(Dealership? item) {
    preferredDealerInfo = dealerInfo.DealerInfoPayload(
        dealerCode: item?.toyotaCode,
        dealerName: item?.dealershipName ?? "",
        regionCode: item?.addresses?.first.zipCode ?? "",
        tdaCode: item?.toyotaCode,
        proximityMiles: 0,
        address: item?.addresses?.first.line1 ?? "",
        city: item?.addresses?.first.city,
        state: item?.addresses?.first.state,
        country: item?.addresses?.first.country,
        zip: item?.addresses?.first.zipCode,
        latitude: item?.addresses?.first.coordinate?.latitude,
        longitude: item?.addresses?.first.coordinate?.longitude,
        dealerType: "",
        phone: getPhoneNumber(item?.phoneNumbers),
        distance: 0,
        distanceUnit: Global.getInstance().locale.countryCode!.contains("CA")
            ? "km"
            : "mi",
        webUrls: []);
    locator.registerSingletonAsync(
      () async => preferredDealerInfo!,
      instanceName: SERVICE_DEALER,
    );
  }

  void clearOldData() {
    Global.getInstance().canAppointmentServiceExit = false;
    Advisors selectedAdvisorData = Advisors();
    Transportations selectedTransportationData = Transportations();
    DateTime selectedDate = DateTime.now();
    String selectedTime = "";
    TransportationLocationAddress pickUpAddress =
        TransportationLocationAddress();
    locator.registerSingletonAsync(
        // ignore: sdk_version_since
        () async => List<ServicesList>.empty(growable: true));
    locator.registerSingletonAsync(() async => selectedAdvisorData);
    locator.registerSingletonAsync(() async => selectedTransportationData);
    locator.registerSingletonAsync(() async => selectedDate);
    locator.registerSingletonAsync(() async => selectedTime,
        instanceName: TIME_SLOT_LOCATOR);
    locator.registerSingletonAsync(() async => pickUpAddress,
        instanceName: DELIVERY_ADDRESS);
    locator.registerSingletonAsync(() async => pickUpAddress,
        instanceName: PICK_UP_ADDRESS);
    locator.registerSingletonAsync(() async => false,
        instanceName: SAME_AS_PICKUP);
  }

  String addDistanceUnit(double? distance) {
    return distance.toString() + " " + distanceMetric(region!);
  }

  String? getPhoneNumber(List<PhoneNumbers>? phoneNumbers) {
    String? phone = '';
    if (phoneNumbers != null && phoneNumbers.isNotEmpty) {
      phone = phoneNumbers.first.number;
    }
    return phone;
  }

  @override
  void dispose() {
    _getDealerDataByIdCtl.close();
    _tableListaDataCtl.close();
    _preferredDealer.close();
    _dealerAppointmentAvailable.close();
    _appLocationDenied.close();
  }
}
