// Flutter imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:app_settings/app_settings.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/search_history_storage_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/select_preferred_dealer_helper.dart';
import 'package:sliding_sheet/sliding_sheet.dart';

// Project imports:
import '../../log/vehicle_marketing_event.dart';
import '/log/vehicle_analytic_event.dart';
import 'preferred_dealer_filter_bar_bloc.dart';
import 'select_dealerships_empty_view.dart';
import 'select_preferred_dealer_bloc.dart';
import 'select_search_map.dart';
import 'slide_title_widget.dart';

class SelectPreferredDealerPage extends StatefulWidget {
  // final IntentToSearch intent;
  final PreferredDealerStatus? preferredDealerStatus;
  final bool? isFromNative;
  final bool? isFromOdometerFlow;

  const SelectPreferredDealerPage(
      {Key? key,
      this.preferredDealerStatus,
      this.isFromNative,
      this.isFromOdometerFlow})
      : super(key: key);

  @override
  _SelectPreferredDealerPageState createState() =>
      _SelectPreferredDealerPageState();
}

class _SelectPreferredDealerPageState extends State<SelectPreferredDealerPage>
    with TickerProviderStateMixin {
  SelectPreferredDealerBloc _bloc = SelectPreferredDealerBloc();
  final _controller = TextEditingController();

  final SheetController _scrollSheetController = SheetController();

  final GlobalKey<SelectSearchMapState> _mapKey = GlobalKey();

  final GlobalKey _slideHeaderKey = GlobalKey();

  FocusNode _focusNode = FocusNode();

  double _screenHeight = 0;

  double _progress = -1;

  PreferredDealerFilterBarBloc? filterBloc;

  DealerFilterModel? filterModel;

  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.SELECT_PREFERRED_DEALER_PAGE);
    FireBaseAnalyticsLogger.logMarketingEvent(
        VehicleMarketingEvent.SELECT_PREFERRED_DEALER);
    _tabController = TabController(length: 2, vsync: this);
    _bloc.init();
    _bloc.appLocationDenied.listen((event) {
      if (event == true) {
        BottomConfirmationDialog().showBottomDialog(
            context,
            OneAppString.of().locationPermissionHeading,
            OneAppString.of().locationPermissionSubHeading,
            findIcon,
            OneAppString.of().commonOK,
            OneAppString.of().commonCancel,
            _appPermissionConfirmClick);
      }
    });
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _bloc.editTFMode(true);
      } else {
        _bloc.editTFMode(false);
      }
    });
    SearchDealerHistoryStorage.getInstance();
  }

  void _appPermissionConfirmClick() {
    AppSettings.openAppSettings();
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _screenHeight = MediaQuery.of(context).size.height;
    return PopScope(
      child: BlocProvider(
        child: Scaffold(
            backgroundColor: Colors.transparent,
            body: Stack(
              children: [
                TabBarView(
                  controller: _tabController,
                  physics: NeverScrollableScrollPhysics(),
                  children: [
                    StreamBuilder(
                        stream: _bloc.getResultDealerDataUI,
                        builder: (ctx,
                            AsyncSnapshot<List<DealerSearchResultHelper>>
                                snapData) {
                          _scrollSheetController.expand();
                          return SlidingSheet(
                            color: ThemeConfig.current().colorUtil.tertiary15,
                            cornerRadiusOnFullscreen: 0,
                            addTopViewPaddingOnFullscreen: true,
                            controller: _scrollSheetController,
                            elevation: 0,
                            cornerRadius: 30,
                            snapSpec: const SnapSpec(
                              snap: true,
                              positioning:
                                  SnapPositioning.relativeToAvailableSpace,
                              snappings: const [
                                SnapSpec.headerSnap,
                                0.6,
                                SnapSpec.expanded,
                              ],
                            ),
                            body: _mapArea(snapData.data),
                            headerBuilder: (context, state) {
                              return SingleChildScrollView(
                                child: Column(
                                  key: _slideHeaderKey,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    _bottomSheetAppBar(),
                                    StreamBuilder(
                                        stream: _bloc.updateSelectDataUI,
                                        builder:
                                            (ctx, AsyncSnapshot<bool> data) {
                                          return Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              _searchArea(),
                                              Visibility(
                                                child: filterContent(),
                                                visible: _bloc
                                                        .vehicleInfoPayload
                                                        ?.features
                                                        ?.serviceShopAdvancedSearch ==
                                                    1,
                                              ),
                                            ],
                                          );
                                        }),
                                    SizedBox(height: 10),
                                  ],
                                ),
                              );
                            },
                            // footerBuilder: (ctx, state) {},
                            builder: (context, state) {
                              _scrollSheetController.expand();
                              return _contentList(snapData.data);
                            },
                            listener: (state) {
                              _calculateOffsetY(state.progress);
                            },
                          );
                        }),
                    SingleChildScrollView(
                      child: StreamBuilder(
                          stream: _bloc.getResultDealerDataUI,
                          builder: (ctx,
                              AsyncSnapshot<List<DealerSearchResultHelper>>
                                  snapData) {
                            return Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SlideTitle(
                                  paddingTop: 30.h,
                                  title: OneAppString.of().selectDealer,
                                  onBackPressed: () {
                                    //Navigator.of(context).pop();
                                    if (Navigator.canPop(context)) {
                                      NavigateService.maybePopRoute(
                                          result: filterModel);
                                    } else {
                                      goBackToNative();
                                    }
                                  },
                                ),
                                _inputSearchArea(),
                                Visibility(
                                  child: filterContent(),
                                  visible: snapData.hasData &&
                                      _bloc.vehicleInfoPayload?.features
                                              ?.serviceShopAdvancedSearch ==
                                          1,
                                ),
                                StreamBuilder(
                                    stream: _bloc.getTFEditingModeUI,
                                    builder:
                                        (ctx, AsyncSnapshot<bool> editData) {
                                      return editData.data == true
                                          ? _locationList()
                                          : _contentList(snapData.data);
                                    }),
                              ],
                            );
                          }),
                    ),
                  ],
                )
              ],
            )),
        bloc: _bloc,
      ),
      onPopInvoked: (didPop) async {
        if (didPop) return;
        if (_tabController!.index == 1) {
          _focusNode.unfocus();
          _tabController!.animateTo(0, duration: Duration(milliseconds: 0));
        } else if (widget.isFromNative == true) {
          goBackToNative();
          return Future(() => false);
        } else {
          return Future(() => true);
        }
      },
    );
  }

  Widget _locationList() {
    List<String> titleList = [
      OneAppString.of().currentLocation,
    ];
    if (SearchDealerHistoryStorage.getInstance().stringList != null) {
      titleList.addAll(SearchDealerHistoryStorage.getInstance().stringList!);
    }
    return Container(
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: titleList.length,
        itemBuilder: (BuildContext context, int position) {
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              _focusNode.unfocus();
              if (position != 0) {
                _bloc.getDealerDataByAddress(titleList[position]);
              } else {
                _bloc.getDeviceLocation();
              }
            },
            child: position != 0
                ? Container(
                    padding: EdgeInsets.symmetric(vertical: 18.h),
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                    decoration: BoxDecoration(
                        border: Border(
                      bottom: BorderSide(
                          width: 1,
                          color: ThemeConfig.current().colorUtil.tertiary10),
                    )),
                    child: Text(
                      titleList[position],
                      style: ThemeConfig.current().textStyleUtil.callout1,
                    ),
                  )
                : Container(
                    padding: EdgeInsets.symmetric(vertical: 18.h),
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                    decoration: BoxDecoration(
                        border: Border(
                      bottom: BorderSide(
                          width: 1,
                          color: ThemeConfig.current().colorUtil.tertiary10),
                    )),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            titleList[position],
                            style: ThemeConfig.current().textStyleUtil.callout1,
                          ),
                        ),
                        SvgPicture.asset(
                          currentLocIcon,
                          width: 24.w,
                          height: 24.w,
                          colorFilter: ColorFilter.mode(
                            ThemeConfig.current().colorUtil.button02a,
                            BlendMode.srcIn,
                          ),
                          semanticsLabel: CURRENT_LOCATION_ICON,
                        ),
                      ],
                    ),
                  ),
          );
        },
      ),
    );
  }

  Widget _bottomSheetAppBar() {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SwipeBarIcon(),
          Container(
            height: kToolbarHeight,
            child: Stack(
              children: [
                Positioned(left: 0, child: _backIcon()),
                Align(child: _pageTitle()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _backIcon() {
    return StreamBuilder<bool>(
        stream: _bloc.overViewMapPinUI,
        builder: (context, snapshot) {
          return InkWell(
            onTap: () async {
              if (widget.isFromNative == true) {
                goBackToNative();
              } else {
                Navigator.of(context).pop();
              }
            },
            child: Container(
              decoration: BoxDecoration(
                color: ThemeConfig.current().colorUtil.button02b,
                shape: BoxShape.circle,
              ),
              margin: EdgeInsets.only(left: 16.w),
              padding: EdgeInsets.all(12.w),
              child: Icon(
                Icons.chevron_left,
                color: ThemeConfig.current().colorUtil.button02a,
                semanticLabel: BACK_BUTTON,
              ),
            ),
          );
        });
  }

  Widget _pageTitle() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      child: Text(
        OneAppString.of().selectDealer,
        style: TextStyleExtension().newStyleWithColor(
            ThemeConfig.current().textStyleUtil.subHeadline3,
            ThemeConfig.current().colorUtil.tertiary03),
        textAlign: TextAlign.center,
      ),
    );
  }

  _calculateOffsetY(double progress) {
    if ((progress == 0 || progress == 1) && _progress != progress) {
      _progress = progress;
      debugPrint('------isExpanded: ${progress == 1}');
      debugPrint('------isCollapsed: ${progress == 0}');
      RenderBox box =
          _slideHeaderKey.currentContext!.findRenderObject() as RenderBox;
      final double deltaHeight = box.localToGlobal(Offset.zero).dy.abs();
      _mapKey.currentState?.addAllPIN(dy: _screenHeight - deltaHeight);
    }
  }

  Widget _mapArea(List<DealerSearchResultHelper>? dealerships) {
    return SelectSearchMap(
      key: _mapKey,
      dealerships: dealerships,
      currentLocation: _bloc.currentLocation,
      onMapPinTap: (item) {
        // TODO _onItemClick(context, item);
        _bloc.filterById(item.toyotaCode);
        _bloc.overViewMapPinClick(true);
      },
    );
  }

  Widget _inputSearchArea() {
    return Container(
      height: 56.h,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: ThemeConfig.current().colorUtil.tertiary12,
        borderRadius: BorderRadius.all(Radius.circular(50.r)),
      ),
      alignment: Alignment.center,
      child: StreamBuilder(
          stream: _bloc.searchText,
          builder: (context, snapshot) {
            return TextField(
              focusNode: _focusNode,
              style: ThemeConfig.current()
                  .textStyleUtil
                  .body3
                  .copyWith(fontSize: 16.sp),
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                prefixIcon: Icon(
                  Icons.search,
                  color: ThemeConfig.current().colorUtil.tertiary03,
                  semanticLabel: SEARCH_ICON,
                ),
                border: InputBorder.none,
                hintText: OneAppString.of().searchTextFiledPlaceHolder,
                hintStyle: ThemeConfig.current().textStyleUtil.body3.copyWith(
                      color: ThemeConfig.current().colorUtil.tertiary05,
                      fontSize: 16.sp,
                    ),
                suffixIcon: Visibility(
                  visible: snapshot.hasData,
                  child: Container(
                    padding: EdgeInsets.all(14.w),
                    child: InkWell(
                      onTap: () {
                        _controller.clear();
                        _bloc.clearSearchCloseIcon();
                      }, // close click listener
                      child: SvgPicture.asset(
                        removeIcon,
                        height: 6,
                        width: 6,
                        colorFilter: ColorFilter.mode(
                          ThemeConfig.current().colorUtil.tertiary07,
                          BlendMode.srcIn,
                        ),
                        allowDrawingOutsideViewBox: false,
                        semanticsLabel: REMOVE_ICON,
                      ),
                    ),
                  ),
                ),
              ),
              textInputAction: TextInputAction.search,
              controller: _controller,
              onSubmitted: (value) {
                if (value.trim().isEmpty) {
                  _focusNode.requestFocus();
                  return;
                }
                List<String> list = [];
                if (SearchDealerHistoryStorage.getInstance().stringList !=
                    null) {
                  list.addAll(
                      SearchDealerHistoryStorage.getInstance().stringList!);
                }
                if (!list.contains(value) && value.trim().isNotEmpty) {
                  list.add(value);
                }
                if (list.length > 4) {
                  list.removeRange(3, list.length - 1);
                }
                List<String> reverseList = []..addAll(list.reversed);
                SearchDealerHistoryStorage.getInstance().stringList =
                    reverseList;
                _bloc.getDealerDataByAddress(value);
              },
              onChanged: (value) {
                if (value.isNotEmpty == true) {
                  _bloc.showSearchCloseIcon();
                } else {
                  _bloc.clearSearchCloseIcon();
                }
              },
            );
          }),
    );
  }

  Widget _searchArea() {
    return GestureDetector(
      onTap: () async {
        _focusNode.requestFocus();
        _tabController!.animateTo(1, duration: Duration(milliseconds: 0));
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: 56.h,
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: ThemeConfig.current().colorUtil.tertiary12,
          borderRadius: BorderRadius.all(Radius.circular(50.r)),
        ),
        alignment: Alignment.center,
        child: TextField(
          style: ThemeConfig.current()
              .textStyleUtil
              .body3
              .copyWith(fontSize: 16.sp),
          textAlignVertical: TextAlignVertical.center,
          enabled: false,
          decoration: InputDecoration(
            prefixIcon: Icon(
              Icons.search,
              color: ThemeConfig.current().colorUtil.tertiary03,
              semanticLabel: SEARCH_ICON,
            ),
            border: InputBorder.none,
            hintText: OneAppString.of().searchTextFiledPlaceHolder,
            hintStyle: ThemeConfig.current().textStyleUtil.body3.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary05,
                  fontSize: 16.sp,
                ),
          ),
          textInputAction: TextInputAction.search,
          controller: _controller,
        ),
      ),
    );
  }

  Widget filterContent() {
    List<bool> titleSelect = [
      false,
      false,
      false,
      false,
      false,
      false,
      false,
      false,
    ];
    if (filterModel != null) {
      if (filterModel?.smartPath == true) {
        titleSelect[0] = true;
      }
      if (filterModel?.service?.isNotEmpty == true) {
        titleSelect[1] = true;
      }
      if (filterModel?.dayOpen?.isNotEmpty == true) {
        titleSelect[2] = true;
      }
      if (filterModel?.limit != null) {
        titleSelect[3] = true;
      }
      if (filterModel?.accessibilityOption?.isNotEmpty == true) {
        titleSelect[4] = true;
      }
      if (filterModel?.transportationOption?.isNotEmpty == true) {
        titleSelect[5] = true;
      }
      if (filterModel?.paymentOption?.isNotEmpty == true) {
        titleSelect[6] = true;
      }
      if (filterModel?.amenity?.isNotEmpty == true) {
        titleSelect[7] = true;
      }
    }
    List<String> titleList = [
      OneAppString.of().dealers,
      OneAppString.of().services,
      OneAppString.of().serviceHours,
      OneAppString.of().distance,
      OneAppString.of().accessibility,
      OneAppString.of().transportationHeading,
      OneAppString.of().paymentMethods,
      OneAppString.of().amenities
    ];
    return Container(
      height: 44.h,
      margin: EdgeInsets.symmetric(vertical: 10.h),
      child: Row(
        children: [
          SizedBox(width: 16.w),
          GestureDetector(
            onTap: () async {
              if (_tabController!.index == 1) {
                _focusNode.unfocus();
              }
              NavigateService.pushNamedRoute(
                RoutePath.FX_PREFERREDDEALERFILTERBAR,
                arguments: {
                  "sectionIndex": null,
                  "filterModel": filterModel,
                },
              ).then((value) {
                if (_tabController!.index == 1) {
                  _focusNode.unfocus();
                }
                if (value != null) {
                  filterModel = value;
                  bool isDistanceFilterAvailable =
                      filterModel?.isDistanceFilterAvailable() ?? false;
                  bool isAnyOneOfServicesAvailable =
                      filterModel?.isAnyOneOfServicesAvailable() ?? false;
                  if (_bloc.currentLocation != null) {
                    if (isDistanceFilterAvailable ||
                        !isAnyOneOfServicesAvailable) {
                      _bloc.getDealerDataByCoordinates(
                          _bloc.currentLocation?.lat.toDouble() ?? 0.0,
                          _bloc.currentLocation?.lng.toDouble() ?? 0.0,
                          filterModel: filterModel);
                    } else {
                      _bloc.requestTotalDataWithModel(value,
                          latitude:
                              _bloc.currentLocation?.lat.toDouble() ?? 0.0,
                          longitude:
                              _bloc.currentLocation?.lng.toDouble() ?? 0.0);
                    }
                  }
                }
              });
            },
            behavior: HitTestBehavior.opaque,
            child: SvgPicture.asset(
              filterIcon,
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(
                ThemeConfig.current().colorUtil.button02a,
                BlendMode.srcIn,
              ),
              semanticsLabel: FILTER_ICON,
            ),
          ),
          Expanded(
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              shrinkWrap: true,
              itemBuilder: (ctx, index) {
                return GestureDetector(
                  onTap: () async {
                    if (_tabController!.index == 1) {
                      _focusNode.unfocus();
                    }
                    NavigateService.pushNamedRoute(
                      RoutePath.FX_PREFERREDDEALERFILTERBAR,
                      arguments: {
                        "sectionIndex": index,
                        "filterModel": filterModel,
                      },
                    ).then((value) {
                      if (_tabController!.index == 1) {
                        _focusNode.unfocus();
                      }
                      if (value != null) {
                        filterModel = value;
                        bool isDistanceFilterAvailable =
                            filterModel?.isDistanceFilterAvailable() ?? false;
                        bool isAnyOneOfServicesAvailable =
                            filterModel?.isAnyOneOfServicesAvailable() ?? false;
                        if (_bloc.currentLocation != null) {
                          if (isDistanceFilterAvailable ||
                              !isAnyOneOfServicesAvailable) {
                            _bloc.getDealerDataByCoordinates(
                                _bloc.currentLocation?.lat.toDouble() ?? 0.0,
                                _bloc.currentLocation?.lng.toDouble() ?? 0.0,
                                filterModel: filterModel);
                          } else {
                            _bloc.requestTotalDataWithModel(value,
                                latitude:
                                    _bloc.currentLocation?.lat.toDouble() ??
                                        0.0,
                                longitude:
                                    _bloc.currentLocation?.lng.toDouble() ??
                                        0.0);
                          }
                        }
                      }
                    });
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                    ),
                    decoration: BoxDecoration(
                      color: titleSelect[index]
                          ? ThemeConfig.current().colorUtil.button02a
                          : ThemeConfig.current().colorUtil.button05b,
                      borderRadius: BorderRadius.circular(44.r),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      titleList[index],
                      style: ThemeConfig.current()
                          .textStyleUtil
                          .callout2
                          .copyWith(
                              color: titleSelect[index]
                                  ? ThemeConfig.current().colorUtil.button05b
                                  : ThemeConfig.current().colorUtil.button02a),
                    ),
                  ),
                );
              },
              itemCount: titleList.length,
              separatorBuilder: (BuildContext context, int index) {
                return SizedBox(width: 8.w);
              },
            ),
          )
        ],
      ),
    );
  }

  Widget _contentList(List<DealerSearchResultHelper>? dealerships) {
    return dealerships?.isNotEmpty == true
        ? Container(
            child: ListView.separated(
              padding: EdgeInsets.only(left: 16.w, right: 16.w),
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: dealerships?.length ?? 0,
              itemBuilder: (BuildContext context, int position) {
                DealerInfoHelper dealerInfoHelper = DealerInfoHelper(
                  dealershipName: dealerships![position].dealershipName,
                  distance: dealerships[position].distance,
                  zipCode: dealerships[position].zipCode,
                  state: dealerships[position].state,
                  country: dealerships[position].country,
                  city: dealerships[position].city,
                  line1: dealerships[position].line1,
                  line2: dealerships[position].line2,
                );
                return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    if (dealerships[position].smartPath) {
                      FireBaseAnalyticsLogger.logMarketingGroupEvent(
                          VehicleMarketingEvent.SERVICE_SCHEDULE,
                          childEventName:
                              VehicleMarketingEvent.SPM_DEALER_LOGO);
                    } else {
                      FireBaseAnalyticsLogger.logMarketingGroupEvent(
                          VehicleMarketingEvent.SERVICE_SCHEDULE,
                          childEventName:
                              VehicleMarketingEvent.SPM_NON_DEALER_LOGO);
                    }
                    NavigateService.pushReplacementNamed(
                        RoutePath.FX_VEHICLEDEALERDETAILPAGE,
                        arguments: {
                          "dealerCode": dealerships[position].toyotaCode,
                          "preferredDealerStatus": widget.preferredDealerStatus,
                          "dealerInfoHelper": dealerInfoHelper,
                          "directFromSelectDealer":
                              (widget.isFromNative == true) ? "true" : "false",
                          "isFromOdometerFlow":
                              (widget.isFromOdometerFlow == true)
                                  ? true
                                  : false,
                        });
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 15.h,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              dealerships[position].dealershipName,
                              style: ThemeConfig.current().textStyleUtil.body4,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Text(
                            dealerships[position].distance != 0
                                ? _bloc.addDistanceUnit(
                                    dealerships[position].distance)
                                : "",
                            style: ThemeConfig.current().textStyleUtil.callout1,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 4.h,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 4.h,
                                ),
                                Text(
                                  dealerships[position].line1,
                                  style: ThemeConfig.current()
                                      .textStyleUtil
                                      .callout1,
                                ),
                                SizedBox(
                                  height: 4.h,
                                ),
                                Text(
                                  "${dealerships[position].city}, ${dealerships[position].state}  ${dealerships[position].zipCode} ",
                                  style: ThemeConfig.current()
                                      .textStyleUtil
                                      .callout1,
                                ),
                                SizedBox(
                                  height: 4.h,
                                ),
                              ],
                            ),
                          ),
                          dealerships[position].smartPath
                              ? spmLogo(dealerships[position].spmLogo)
                              : SizedBox.shrink()
                        ],
                      ),
                      SizedBox(
                        height: 10.h,
                      )
                    ],
                  ),
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                return Divider(
                  height: 0,
                  color: ThemeConfig.current().colorUtil.button02c,
                );
              },
            ),
          )
        : SelectDealershipsEmptyView();
  }

  Widget spmLogo(String? spmLogo) {
    if (spmLogo != null && spmLogo.isNotEmpty == true) {
      bool isDarkTheme = Global.getInstance().isDarkTheme;
      String? spmImage =
          isDarkTheme ? spmLogo.split(',').last : spmLogo.split(',').first;
      if (spmImage.isNotEmpty == true) {
        return CachedNetworkImage(
          height: 34.h,
          width: 90.w,
          imageUrl: spmImage,
          errorWidget: (context, url, error) => SizedBox.shrink(),
        );
      } else {
        return SizedBox.shrink();
      }
    } else {
      return SizedBox.shrink();
    }
  }
}
