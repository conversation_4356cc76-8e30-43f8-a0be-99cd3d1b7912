// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/image/icon_widget.dart';

class SelectDealershipsEmptyView extends StatelessWidget {
  const SelectDealershipsEmptyView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 59.w),
      margin: EdgeInsets.symmetric(vertical: 70.h),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            OneAppString.of().noResultsFound,
            style: ThemeConfig.current().textStyleUtil.subHeadline1,
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 24.h,
          ),
          Text(
            OneAppString.of().noSearchResultTips,
            style: ThemeConfig.current().textStyleUtil.body1.copyWith(
                  color: ThemeConfig.current().colorUtil.tertiary07,
                ),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 21.h,
          ),
          SizedBox(
            width: 48.w,
            height: 48.w,
            child: CommonCircleIconImage(
              errorSearchIcon,
              iconWidth: 24.w,
              iconHeight: 24.w,
              iconPadding: EdgeInsets.all(12.w),
              circleBackgroundColor: ThemeConfig.current().colorUtil.primary02,
              iconTintColor: ThemeConfig.current().colorUtil.primary01,
            ),
          ),
        ],
      ),
    );
  }
}
