// Dart imports:
import 'dart:io';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/custom_divider.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/maintenance_timeline_entity.dart';

// Project imports:
import '../../../log/vehicle_marketing_event.dart';
import 'maintenance_schedule_detail_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/dealer_info_entity.dart'
    as dealerInfo;

class MaintenanceScheduleDetailPage extends StatefulWidget {
  final ScheduleMaintenanceDetails? maintenancePayLoad;
  final bool isScheduleAppointmentAvailable;
  final dealerInfo.DealerInfoPayload? preferredDealerPayload;
  final List<FootNotes>? footNotes;
  final String? title;
  final String? subTitle;
  final bool? fromNative;

  MaintenanceScheduleDetailPage(
      {this.maintenancePayLoad,
      this.isScheduleAppointmentAvailable = false,
      this.preferredDealerPayload,
      this.footNotes,
      this.title,
      this.subTitle,
      this.fromNative});

  @override
  _MaintenanceScheduleDetailPageState createState() =>
      _MaintenanceScheduleDetailPageState();
}

class _MaintenanceScheduleDetailPageState
    extends State<MaintenanceScheduleDetailPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  MaintenanceScheduleBloc _bloc = MaintenanceScheduleBloc();
  bool isAdditionalListVisible = false;
  bool isRecommendedListVisible = true;
  List<String> alphabets = [];

  @override
  void initState() {
    super.initState();
    _bloc.init(widget.maintenancePayLoad, widget.footNotes);
    alphabets = List<String>.generate(
      'Z'.codeUnitAt(0) - 'A'.codeUnitAt(0) + 1,
      (index) => String.fromCharCode('A'.codeUnitAt(0) + index),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _scheduleBody();
  }

  Widget _scheduleBody() {
    return Container(
      color: _colorUtil.tertiary15,
      child: Material(
          child: Navigator(
        onGenerateRoute: (_) => MaterialPageRoute(
          builder: (context2) => Builder(
            builder: (context) => Scaffold(
              body: Container(
                  color: _colorUtil.tertiary15,
                  child: _maintenanceScheduleDetailLayout()),
            ),
          ),
        ),
      )),
    );
  }

  double headerHeight() {
    if (Platform.isAndroid) {
      return (widget.fromNative == true) ? (kToolbarHeight) : 20.h;
    } else {
      return (widget.fromNative == true) ? (kToolbarHeight + 20.h) : 40.h;
    }
  }

  Widget _maintenanceScheduleDetailLayout() {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
      child: Column(
        children: [
          SizedBox(height: headerHeight()),
          bottomSheetHeader("${widget.title!} ${widget.subTitle ?? ''}",
              onBackPressed: _popBack),
          SizedBox(height: 16.h),
          StreamBuilder<bool>(
              stream: _bloc.isSubCategoriesListNotAvailable,
              builder: (context, subcategories) {
                return subcategories.hasData && subcategories.hasData
                    ? Expanded(
                        child: Column(
                          children: [
                            Expanded(child: _timeRecommendedListLayout())
                          ],
                        ),
                      )
                    : Expanded(
                        child: Center(
                          child: Text(OneAppString.of().noDataFound,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.callout1,
                                  _colorUtil.tertiary05)),
                        ),
                      );
              }),
          SizedBox(height: 16.h),
          if (_bloc.getInterval(widget.maintenancePayLoad) ==
              OneAppString.of().currentText)
            _bottomConditionButton(),
        ],
      ),
    );
  }

  Widget _bottomConditionButton() {
    bool isDealerAvailable = widget.isScheduleAppointmentAvailable;
    return Container(
        margin: EdgeInsets.only(top: 18.h),
        child: CustomDefaultButton(
          backgroundColor: _colorUtil.button01b,
          buttonTextColor: _colorUtil.button01a,
          text: isDealerAvailable
              ? OneAppString.of().makeAnAppointment
              : OneAppString.of().call,
          press: isDealerAvailable
              ? _makeAppointmentButtonPress
              : _handleCallButtonPress,
          borderColor: _colorUtil.button01b,
          horizontalPadding: 22.w,
          verticalPadding: 6.h,
        ));
  }

  void _handleCallButtonPress() {
    _bloc.openPhoneDialer(widget.preferredDealerPayload?.phone);
  }

  void _makeAppointmentButtonPress() {
    FireBaseAnalyticsLogger.logMarketingEvent(
        VehicleMarketingEvent.SCHEDULE_MAINTENANCE_CLICKED);
    NavigateService.pushNamedRoute(
        RoutePath.VEHICLE_SERVICE_APPOINTMENT_ODOMETER_SETUP_PAGE,
        arguments: {
          "isInitialPage": true,
          "preferredDealerInfo": widget.preferredDealerPayload,
          "odometerValue":
              _bloc.odometerValue == "0" ? null : _bloc.odometerValue,
          "comeFromConfirmPage": false
        });
  }

  Widget bottomSheetHeader(String title, {VoidCallback? onBackPressed}) {
    return Container(
      child: Column(
        children: [
          Container(
            height: kToolbarHeight,
            child: Row(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                    onTap: onBackPressed,
                    child: Container(
                      decoration: BoxDecoration(
                        color: _colorUtil.tertiary12,
                        shape: BoxShape.circle,
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(8.w),
                        child: Icon(
                          Icons.chevron_left,
                          color: _colorUtil.tertiary00,
                          semanticLabel: BACK_BUTTON,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Container(
                    child: Align(
                      alignment: Alignment.center,
                      child: Container(
                        margin: EdgeInsets.only(right: 16.w),
                        child: Text(formatTextForLexusAndToyota(title),
                            textAlign: TextAlign.center,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.subHeadline3,
                                _colorUtil.tertiary03)),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _timelineDetailListLayout() {
    return Column(
      children: [
        StreamBuilder<bool>(
          initialData: false,
          stream: _bloc.isAdditionalListVisible,
          builder: (context, visibilitySnapshot) {
            isAdditionalListVisible = visibilitySnapshot.data ?? false;
            return StreamBuilder<List<SubCatogeries>>(
                stream: _bloc.vehicleTimeLineAdditionalList,
                builder: (context, snapshot) {
                  if (!snapshot.hasData) {
                    return Container();
                  } else {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () {
                            if (isAdditionalListVisible == false &&
                                isRecommendedListVisible == true) {
                              _bloc.changeRecommendedListVisibilityState(
                                  !isRecommendedListVisible);
                            }
                            _bloc.changeAdditionalListVisibilityState(
                                !isAdditionalListVisible);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(
                                Radius.circular(8.r),
                              ),
                              color: ThemeConfig.current().colorUtil.tile02,
                            ),
                            padding: EdgeInsets.symmetric(
                                vertical: 18.h, horizontal: 0.w),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 16.w,
                                ),
                                Expanded(
                                  child: Text(
                                    OneAppString.of().additionalMaintenance,
                                    style: ThemeConfig.current()
                                        .textStyleUtil
                                        .body4
                                        .copyWith(
                                          color: ThemeConfig.current()
                                              .colorUtil
                                              .tertiary03,
                                        ),
                                  ),
                                ),
                                SvgPicture.asset(
                                  isAdditionalListVisible
                                      ? filterMinimizeIcon
                                      : filterPlusIcon,
                                  height: 24.w,
                                  width: 24.w,
                                  colorFilter: ColorFilter.mode(
                                    ThemeConfig.current().colorUtil.button02a,
                                    BlendMode.srcIn,
                                  ),
                                  semanticsLabel: FILTER_PLUS_MINIMIZE,
                                ),
                                SizedBox(
                                  width: 16.w,
                                ),
                              ],
                            ),
                          ),
                        ),
                        isAdditionalListVisible
                            ? Align(
                                alignment: Alignment.centerLeft,
                                child: Padding(
                                  padding:
                                      EdgeInsets.only(top: 8.h, left: 16.w),
                                  child: Text(
                                      OneAppString.of()
                                          .specialOperationConditions,
                                      style: TextStyleExtension()
                                          .newStyleWithColor(
                                              _textStyleUtil.callout1,
                                              _colorUtil.tertiary05)),
                                ),
                              )
                            : Container(),
                        isAdditionalListVisible
                            ? Padding(
                                padding: EdgeInsets.all(8.h),
                                child: ListView.builder(
                                  itemBuilder: (context, index) {
                                    SubCatogeries data = snapshot.data![index];
                                    return Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: <Widget>[
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              height: 24.w,
                                              width: 24.w,
                                              child: CircleAvatar(
                                                child: Center(
                                                  child: Text(alphabets[index],
                                                      style: TextStyleExtension()
                                                          .newStyleWithColor(
                                                              _textStyleUtil
                                                                  .callout2,
                                                              _colorUtil
                                                                  .tertiary03)),
                                                ),
                                                backgroundColor:
                                                    _colorUtil.tile05,
                                              ),
                                            ),
                                            SizedBox(
                                              width: 8.w,
                                            ),
                                            Expanded(
                                              child: Text(
                                                '${data.operatingCondDescription}',
                                                style: TextStyleExtension()
                                                    .newStyleWithColor(
                                                        _textStyleUtil.body4,
                                                        _colorUtil.tertiary03),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Padding(
                                          padding: EdgeInsets.only(
                                              left: 30.w,
                                              right: 30.w,
                                              top: 8.h,
                                              bottom: 10.h),
                                          child: ListView.separated(
                                            itemBuilder: (context, index) {
                                              String? serviceItemDescription = data
                                                      .maintenanceTasks?[index]
                                                      .serviceItemDescription ??
                                                  '';
                                              String? superScript = '';
                                              if (data.maintenanceTasks?[
                                                          index] !=
                                                      null &&
                                                  data.maintenanceTasks?[index]
                                                          .footNotesId !=
                                                      null) {
                                                superScript = data
                                                    .maintenanceTasks![index]
                                                    .footNotesId
                                                    .toString();
                                              }
                                              return serviceItemDescription
                                                      .isNotEmpty
                                                  ? Padding(
                                                      padding: EdgeInsets.only(
                                                          top: 16.h,
                                                          bottom: 12.h),
                                                      child: RichText(
                                                        text: TextSpan(
                                                          style: TextStyleExtension()
                                                              .newStyleWithColor(
                                                                  _textStyleUtil
                                                                      .body3,
                                                                  _colorUtil
                                                                      .tertiary03),
                                                          children: [
                                                            TextSpan(
                                                              text: convertToSentenceCase(
                                                                  serviceItemDescription),
                                                            ),
                                                            WidgetSpan(
                                                              child: Transform
                                                                  .translate(
                                                                offset:
                                                                    const Offset(
                                                                        0.0,
                                                                        -7.0),
                                                                child: Text(
                                                                  superScript,
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          11.sp,
                                                                      color: _colorUtil
                                                                          .tertiary03),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    )
                                                  : Container();
                                            },
                                            separatorBuilder: (context, index) {
                                              String? serviceItemDescription = data
                                                      .maintenanceTasks?[index]
                                                      .serviceItemDescription ??
                                                  '';
                                              return serviceItemDescription
                                                      .isNotEmpty
                                                  ? CustomDivider(
                                                      lineColor:
                                                          _colorUtil.tertiary10)
                                                  : Container();
                                            },
                                            itemCount:
                                                data.maintenanceTasks!.length,
                                            shrinkWrap: true,
                                            // todo comment this out and check the result
                                            physics:
                                                NeverScrollableScrollPhysics(), // todo comment this out and check the result
                                          ),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.only(
                                              left: 30.w,
                                              right: 30.w,
                                              bottom: 10.h),
                                          child: CustomDivider(
                                              lineColor: _colorUtil.tertiary10),
                                        )
                                      ],
                                    );
                                  },
                                  itemCount: snapshot.data!.length,
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                ),
                              )
                            : Container(),
                      ],
                    );
                  }
                });
          },
        ),
      ],
    );
  }

  Widget _timeRecommendedListLayout() {
    return SingleChildScrollView(
      child: Column(
        children: [
          StreamBuilder<bool>(
            stream: _bloc.isRecommendedListVisible,
            initialData: true,
            builder: (context, visibilitySnapshot) {
              isRecommendedListVisible = visibilitySnapshot.data!;
              return StreamBuilder<List<MaintenanceTasks>>(
                  stream: _bloc.vehicleTimeLineRecommendedList,
                  builder: (context, snapshot) {
                    if (!snapshot.hasData) {
                      return Container();
                    } else {
                      return Column(
                        children: [
                          InkWell(
                            onTap: () {
                              if (isRecommendedListVisible == false &&
                                  isAdditionalListVisible == true) {
                                _bloc.changeAdditionalListVisibilityState(
                                    !isAdditionalListVisible);
                              }
                              _bloc.changeRecommendedListVisibilityState(
                                  !isRecommendedListVisible);
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(8.r),
                                ),
                                color: ThemeConfig.current().colorUtil.tile02,
                              ),
                              padding: EdgeInsets.symmetric(
                                  vertical: 18.h, horizontal: 0.w),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 16.w,
                                  ),
                                  Expanded(
                                    child: Text(
                                      OneAppString.of().recommendedMaintenance,
                                      style: ThemeConfig.current()
                                          .textStyleUtil
                                          .body4
                                          .copyWith(
                                            color: ThemeConfig.current()
                                                .colorUtil
                                                .tertiary03,
                                          ),
                                    ),
                                  ),
                                  SvgPicture.asset(
                                    isRecommendedListVisible
                                        ? filterMinimizeIcon
                                        : filterPlusIcon,
                                    height: 24.w,
                                    width: 24.w,
                                    colorFilter: ColorFilter.mode(
                                      ThemeConfig.current().colorUtil.button02a,
                                      BlendMode.srcIn,
                                    ),
                                    semanticsLabel: FILTER_PLUS_MINIMIZE,
                                  ),
                                  SizedBox(
                                    width: 16.w,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          isRecommendedListVisible
                              ? ListView.builder(
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  itemCount: snapshot.data!.length,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    String? serviceItemDescription = snapshot
                                            .data?[index]
                                            .serviceItemDescription ??
                                        '';
                                    String superScript = '';
                                    if (snapshot.data?[index] != null &&
                                        snapshot.data?[index].footNotesId !=
                                            null) {
                                      superScript = snapshot
                                              .data?[index].footNotesId
                                              .toString() ??
                                          '';
                                    }
                                    return serviceItemDescription.isNotEmpty
                                        ? Container(
                                            margin: EdgeInsets.only(
                                                bottom: 8.h,
                                                right: 8.w,
                                                left: 8.w),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                RichText(
                                                  text: TextSpan(
                                                    style: TextStyleExtension()
                                                        .newStyleWithColor(
                                                            _textStyleUtil
                                                                .body3,
                                                            _colorUtil
                                                                .tertiary03),
                                                    children: [
                                                      TextSpan(
                                                        text: convertToSentenceCase(
                                                            serviceItemDescription),
                                                      ),
                                                      WidgetSpan(
                                                        child:
                                                            Transform.translate(
                                                          offset: const Offset(
                                                              0.0, -7.0),
                                                          child: Text(
                                                            superScript,
                                                            style: TextStyle(
                                                                fontSize: 11.sp,
                                                                color: _colorUtil
                                                                    .tertiary03),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Padding(
                                                  padding: EdgeInsets.only(
                                                      top: 8.h, bottom: 8.h),
                                                  child: CustomDivider(
                                                      lineColor: _colorUtil
                                                          .tertiary10),
                                                ),
                                              ],
                                            ),
                                          )
                                        : Container();
                                  })
                              : Container(),
                        ],
                      );
                    }
                  });
            },
          ),
          SizedBox(
            height: 4.h,
          ),
          _timelineDetailListLayout(),
          SizedBox(
            height: 4.h,
          ),
          _footNotesLayout()
        ],
      ),
    );
  }

  Widget _footNotesLayout() {
    return Column(
      children: [
        StreamBuilder<List<FootNotes>>(
          stream: _bloc.footNotesList,
          builder: (context, footNotesSnapshot) {
            if (footNotesSnapshot.data?.isNotEmpty == true) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(
                        Radius.circular(8.r),
                      ),
                      color: ThemeConfig.current().colorUtil.tile02,
                    ),
                    padding:
                        EdgeInsets.symmetric(vertical: 18.h, horizontal: 16.w),
                    child: Text(
                      OneAppString.of().footNotes,
                      style: ThemeConfig.current().textStyleUtil.body4.copyWith(
                            color: ThemeConfig.current().colorUtil.tertiary03,
                          ),
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 8.w, right: 8.w, bottom: 4.h),
                    child: ListView.builder(
                      itemBuilder: (context, index) {
                        FootNotes data = footNotesSnapshot.data![index];
                        return Padding(
                          padding: EdgeInsets.only(
                              left: 8.w, right: 8.w, bottom: 10.h),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: <Widget>[
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Center(
                                    child: Text('${index + 1}',
                                        style: TextStyleExtension()
                                            .newStyleWithColor(
                                                _textStyleUtil.callout1,
                                                _colorUtil.tertiary05)),
                                  ),
                                  SizedBox(
                                    width: 12.w,
                                  ),
                                  Expanded(
                                    child: Text(
                                      '${data.footNoteDesc}',
                                      style: TextStyleExtension()
                                          .newStyleWithColor(
                                              _textStyleUtil.callout1,
                                              _colorUtil.tertiary05),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                      itemCount: footNotesSnapshot.data!.length,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                    ),
                  )
                ],
              );
            } else {
              return SizedBox.shrink();
            }
          },
        ),
      ],
    );
  }

  void _popBack() {
    Navigator.of(context).pop();
  }
}
