// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/maintenance_timeline_entity.dart';
import 'package:rxdart/rxdart.dart';

class MaintenanceScheduleBloc extends BlocBase {
  ScheduleMaintenanceDetails? maintenanceTimelinePayload;

  final _vehicleTimeLineRecommendedList =
      BehaviorSubject<List<MaintenanceTasks>>();

  Stream<List<MaintenanceTasks>> get vehicleTimeLineRecommendedList =>
      _vehicleTimeLineRecommendedList.stream;

  final _vehicleTimeLineAdditionalList = BehaviorSubject<List<SubCatogeries>>();

  Stream<List<SubCatogeries>> get vehicleTimeLineAdditionalList =>
      _vehicleTimeLineAdditionalList.stream;

  Stream<bool> get isCurrentMileage => _isCurrentMileage.stream;
  final _isCurrentMileage = BehaviorSubject<bool>();

  Stream<bool> get isAdditionalListVisible => _isAdditionalListVisible.stream;
  final _isAdditionalListVisible = BehaviorSubject<bool>();

  Stream<bool> get isRecommendedListVisible => _isRecommendedListVisible.stream;
  final _isRecommendedListVisible = BehaviorSubject<bool>();

  Stream<bool> get isSubCategoriesListNotAvailable =>
      _isSubCategoriesListNotAvailable.stream;
  final _isSubCategoriesListNotAvailable = BehaviorSubject<bool>();

  final _footNotesList = BehaviorSubject<List<FootNotes>>();

  Stream<List<FootNotes>> get footNotesList => _footNotesList.stream;

  String odometerValue = "0";
  List<SubCatogeries>? additional = [];
  final String normal = 'normal';
  final String current = 'current';
  final String previous = 'previous';

  void init(ScheduleMaintenanceDetails? payload, List<FootNotes>? footNotes) {
    maintenanceTimelinePayload = payload;
    if (maintenanceTimelinePayload != null &&
        maintenanceTimelinePayload?.subCategories != null &&
        maintenanceTimelinePayload?.subCategories?.isNotEmpty == true) {
      _isSubCategoriesListNotAvailable.add(false);
      getRecommendedList();
      getAdditionalList();
    } else {
      _isSubCategoriesListNotAvailable.add(true);
    }
    if (payload?.interval != null &&
        payload?.interval?.toLowerCase() == current) {
      _isCurrentMileage.sink.add(true);
    } else {
      _isCurrentMileage.sink.add(false);
    }
    odometerValue = Global.getInstance().mileage;
    if (footNotes != null && footNotes.isNotEmpty) {
      _footNotesList.sink.add(footNotes);
    } else {
      _footNotesList.sink.add([]);
    }
  }

  void getRecommendedList() {
    try {
      List<MaintenanceTasks>? recommended = maintenanceTimelinePayload!
          .subCategories!
          .firstWhere((element) => (element.operatingCondDescription ?? '')
              .toLowerCase()
              .startsWith(normal))
          .maintenanceTasks;
      if (recommended != null && recommended.isNotEmpty) {
        _isRecommendedListVisible.sink.add(true);
        _vehicleTimeLineRecommendedList.sink.add(recommended);
      }
    } catch (e) {}
  }

  void getAdditionalList() {
    try {
      List<SubCatogeries> subCategories =
          maintenanceTimelinePayload!.subCategories!;
      subCategories.forEach((element) {
        if (element.maintenanceTasks != null &&
            element.maintenanceTasks!.isNotEmpty &&
            element.operatingCondDescription != null &&
            !element.operatingCondDescription!
                .toLowerCase()
                .startsWith(normal)) {
          additional?.add(element);
        }
      });
      if (additional != null && additional?.isNotEmpty == true) {
        _isAdditionalListVisible.sink.add(false);
        _vehicleTimeLineAdditionalList.sink.add(additional!);
      }
    } catch (e) {}
  }

  void changeAdditionalListVisibilityState(bool value) {
    _isAdditionalListVisible.sink.add(value);
  }

  void changeRecommendedListVisibilityState(bool value) {
    _isRecommendedListVisible.sink.add(value);
  }

  String getInterval(ScheduleMaintenanceDetails? maintenanceDetail) {
    String interval = '';
    if (maintenanceDetail != null && maintenanceDetail.interval != null) {
      if (maintenanceDetail.interval!.toLowerCase() == previous) {
        interval = OneAppString.of().past;
      } else {
        interval =
            convertFirstLetterToCaps(maintenanceDetail.interval!.toLowerCase());
      }
    }
    return interval;
  }

  @override
  void dispose() {
    _isSubCategoriesListNotAvailable.close();
    _vehicleTimeLineRecommendedList.close();
    _vehicleTimeLineAdditionalList.close();
    _isRecommendedListVisible.close();
    _isAdditionalListVisible.close();
    _isCurrentMileage.close();
    _footNotesList.close();
  }

  void openPhoneDialer(String? phoneNumber) async {
    if (phoneNumber != null && phoneNumber.isNotEmpty) {
      dialerLauncher(phoneNumber);
    }
  }
}
