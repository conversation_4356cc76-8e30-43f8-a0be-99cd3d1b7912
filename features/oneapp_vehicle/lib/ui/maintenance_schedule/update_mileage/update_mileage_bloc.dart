// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/maintenance_timeline_entity.dart'
    as maintenanceTimeline;

class UpdateMileageBloc extends BlocBase {
  final _isErrorOccurred = BehaviorSubject<bool>();

  Stream<bool> get isErrorOccurred => _isErrorOccurred.stream;

  void showOrHideErrorText(bool isErrorOccurred) {
    _isErrorOccurred.sink.add(isErrorOccurred);
  }

  Future<maintenanceTimeline.MaintenanceTimelinePayload?>
      getMaintenanceTimeline(String odometerValue, String odometerUnit) async {
    String value = await odometerValue.replaceAll(RegExp('[^0-9]'), '');
    maintenanceTimeline.MaintenanceTimelinePayload? paylaod;
    showProgress(NavigateService.context);
    try {
      await VehicleRepo().storeMaintenanceTimeline(value, odometerUnit);
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_VEHI);
    }
    await dismissProgress(NavigateService.context);
    try {
      paylaod =
          GetIt.instance<maintenanceTimeline.MaintenanceTimelinePayload>();
    } catch (e) {}
    return paylaod;
  }

  @override
  void dispose() {
    _isErrorOccurred.close();
  }
}
