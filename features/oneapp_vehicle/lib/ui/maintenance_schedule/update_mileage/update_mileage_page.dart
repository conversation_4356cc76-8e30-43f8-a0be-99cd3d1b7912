//Flutter imports:

// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_outlined_button.dart';

// Project imports:
import '../../../log/vehicle_marketing_event.dart';
import '/ui/maintenance_schedule/update_mileage/update_mileage_bloc.dart';

//Package imports:

class UpdateMileagePage extends StatefulWidget {
  final String? lastUpdatedMileage;

  final bool isFromFirstCheck;

  final bool fromNative;

  UpdateMileagePage(
      {this.lastUpdatedMileage,
      this.isFromFirstCheck = false,
      this.fromNative = false});

  @override
  _UpdateMileagePageState createState() => _UpdateMileagePageState();
}

class _UpdateMileagePageState extends State<UpdateMileagePage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  TextEditingController odometerController = TextEditingController();
  UpdateMileageBloc _bloc = UpdateMileageBloc();

  @override
  void initState() {
    _bloc.showOrHideErrorText(false);
    odometerController = TextEditingController(
        text: widget.lastUpdatedMileage != null
            ? widget.lastUpdatedMileage!.trim()
            : '');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: _willPopCallback,
      child: OneAppScaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: _colorUtil.tertiary15,
        body: Container(
          color: _colorUtil.tertiary15,
          margin: EdgeInsets.fromLTRB(16.w, 32.h, 16.w, 24.h),
          child: _updateServiceRecordLayout(),
        ),
      ),
    );
  }

  Future<void> _willPopCallback(bool didPop) async {
    if (didPop) return;
    _popBack();
  }

  Widget _updateServiceRecordLayout() {
    return Column(
      children: [
        _bottomSheetHeader(OneAppString.of().updateMileage,
            onBackPressed: _popBack),
        Expanded(child: _odometerLayout()),
        _makeAppointmentLayout(),
      ],
    );
  }

  Widget _bottomSheetHeader(String title, {VoidCallback? onBackPressed}) {
    return Container(
      child: Column(
        children: [
          Container(
            height: kToolbarHeight,
            child: Row(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                    onTap: onBackPressed,
                    child: Container(
                      decoration: BoxDecoration(
                        color: _colorUtil.tertiary12,
                        shape: BoxShape.circle,
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(8.w),
                        child: Icon(
                          Icons.chevron_left,
                          color: _colorUtil.tertiary00,
                          semanticLabel: BACK_BUTTON,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Container(
                    child: Align(
                      alignment: Alignment.center,
                      child: Container(
                        margin: EdgeInsets.only(right: 16.w),
                        child: Text(formatTextForLexusAndToyota(title),
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.subHeadline3,
                                _colorUtil.tertiary03)),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _odometerLayout() {
    return Container(
      margin: EdgeInsets.only(top: 12.h, bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            onChanged: (value) {
              odometerController.value = odometerController.value
                  .copyWith(text: convertThousandsWithComma(value));
              odometerController.selection = TextSelection.fromPosition(
                  TextPosition(offset: odometerController.text.length));
            },
            style: TextStyleExtension()
                .newStyleWithColor(_textStyleUtil.body3, _colorUtil.tertiary03),
            controller: odometerController,
            keyboardType: TextInputType.numberWithOptions(decimal: false),
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(6)
            ],
            decoration: InputDecoration(
                hintText: OneAppString.of().odometer,
                hintStyle: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.body3, _colorUtil.tertiary07),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.r)),
                  borderSide: BorderSide(color: _colorUtil.tertiary07),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.0)),
                  borderSide: BorderSide(color: _colorUtil.tertiary07),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(4)),
                  borderSide: BorderSide(color: _colorUtil.tertiary07),
                ),
                filled: true,
                fillColor: _colorUtil.tertiary15),
          ),
          StreamBuilder<bool>(
              stream: _bloc.isErrorOccurred,
              builder: (context, snapshot) {
                if (snapshot.hasData && snapshot.data!) {
                  return Visibility(
                      visible: snapshot.data!,
                      child: Container(
                          margin: EdgeInsets.only(top: 4.h),
                          child: Text(
                            OneAppString.of().odometerValidation,
                            style: TextStyle(color: _colorUtil.primary01),
                          )));
                } else {
                  return Container();
                }
              })
        ],
      ),
    );
  }

  void _popBack() {
    FocusManager.instance.primaryFocus?.unfocus();

    if (widget.fromNative) {
      goBackToNative();
    } else {
      if (widget.isFromFirstCheck == true) {
        Navigator.of(context).pop();
      }
      Navigator.of(context).pop();
    }
  }

  Widget _makeAppointmentLayout() {
    return Container(
      margin: EdgeInsets.only(top: 24.h),
      child: Align(
        alignment: Alignment.bottomCenter,
        child: CustomOutlinedButton(
          buttonText: OneAppString.of().save,
          borderColor: _colorUtil.button01b,
          textColor: _colorUtil.button01a,
          backgroundColor: _colorUtil.button01b,
          clickCallBack: () async {
            FocusManager.instance.primaryFocus?.unfocus();
            if (odometerController.text.isEmpty ||
                odometerController.text == '0') {
              _bloc.showOrHideErrorText(true);
            } else {
              FireBaseAnalyticsLogger.logMarketingEvent(
                  VehicleMarketingEvent.ADD_MILEAGE_CLICKED);
              String value = await odometerController.text
                  .replaceAll(RegExp('[^0-9]'), '');
              final payload =
                  await _bloc.getMaintenanceTimeline(value.trim(), "mi");
              Navigator.of(context).pop(payload);
            }
          },
        ),
      ),
    );
  }
}
