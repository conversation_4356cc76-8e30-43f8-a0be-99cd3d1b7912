// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/list_tile/custom_list_tile.dart';

// Project imports:
import '/log/vehicle_analytic_event.dart';
import 'service_appointment_detail_bloc.dart';
import 'service_appointment_map.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart'
    as serviceHistory;

class ServiceAppointmentDetailPage extends StatefulWidget {
  final serviceHistory.ServiceHistories? payload;
  final bool? fromNative;

  ServiceAppointmentDetailPage({this.payload, this.fromNative});

  @override
  _ServiceAppointmentDetailPageState createState() =>
      _ServiceAppointmentDetailPageState();
}

class _ServiceAppointmentDetailPageState
    extends State<ServiceAppointmentDetailPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final GlobalKey<ServiceAppointmentMapState> _mapKey = GlobalKey();
  ServiceAppointmentDetailBloc _bloc = ServiceAppointmentDetailBloc();

  @override
  void initState() {
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.SERVICE_APPOINTMENT_PAGE);
    _bloc.phonePermissionDenied.listen((event) {
      if (event == true) {
        BottomConfirmationDialog().showBottomDialog(
            context,
            OneAppString.of().phonePermissionText,
            OneAppString.of().phoneAppSettingsText,
            findIcon,
            OneAppString.of().commonOK,
            OneAppString.of().commonCancel,
            openAppSettingsPage);
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.fromNative == true) {
      return OneAppScaffold(
        containAppBarShadow: false,
        body: SafeArea(
          child: Container(
            color: _colorUtil.tertiary15,
            child: SingleChildScrollView(
              child: _serviceAppointmentLayout(),
            ),
          ),
        ),
      );
    } else {
      return Container(
        child: Material(
            child: Navigator(
          onGenerateRoute: (_) => MaterialPageRoute(
            builder: (context2) => Builder(
              builder: (context) => Scaffold(
                body: Container(
                    color: _colorUtil.tertiary15,
                    child: SingleChildScrollView(
                        child: _serviceAppointmentLayout())),
              ),
            ),
          ),
        )),
      );
    }
  }

  Widget _serviceAppointmentLayout() {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h, left: 16.w, right: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _bottomSheetHeader(OneAppString.of().serviceDetails,
              onBackPressed: _popBack),
          Container(
              margin: EdgeInsets.only(bottom: 8.h),
              child: Center(child: _dateLayout())),
          Container(
              height: 24.h,
              margin: EdgeInsets.only(bottom: 16.h),
              child: Center(
                  child: Text(
                      _bloc.getFormattedServiceDate(
                          widget.payload?.serviceDate ?? ''),
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body4, _colorUtil.tertiary03)))),
          (widget.payload != null && widget.payload?.servicingDealer != null)
              ? Stack(
                  children: [
                    (widget.payload!.servicingDealer!.latitude != null &&
                            widget.payload!.servicingDealer!.longitude != null)
                        ? Container(
                            height: 280.h,
                            margin: EdgeInsets.only(bottom: 16.h),
                            child: Card(
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(CARD_RADIUS_SMALL),
                              ),
                              color: _colorUtil.tile01,
                              child: ClipRRect(
                                borderRadius: BorderRadius.all(
                                    Radius.circular(CARD_RADIUS_SMALL)),
                                child: ServiceAppointmentMap(
                                  key: _mapKey,
                                  serviceCentrePosition: Position(
                                    widget.payload!.servicingDealer!.longitude!,
                                    widget.payload!.servicingDealer!.latitude!,
                                  ),
                                ),
                              ),
                            ),
                          )
                        : SizedBox.shrink(),
                    Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(left: 4.w, right: 4.w),
                          child: CustomListTile(
                            tileState: DisplayStyleState.SUCCESS,
                            tapOption: ListTileTapOption.NONE,
                            svgAvatarIconName: maintenanceIcon,
                            titleText: _bloc.getServiceDealerName(
                              widget.payload!.servicingDealer!
                                      .servicingDealerName ??
                                  '',
                            ),
                            subTitleText: _bloc.getServiceDealerAddressAndCity(
                                widget.payload!.servicingDealer?.address ?? '',
                                widget.payload!.servicingDealer?.city ?? ''),
                            tileBackgroundColor: _colorUtil.tile01,
                            callBackFunction: () {},
                          ),
                        ),
                        Container(
                          color: _colorUtil.tile05,
                          padding: EdgeInsets.all(12.w),
                          margin: EdgeInsets.only(left: 4.w, right: 4.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                mobileIcon,
                                height: 14.h,
                                width: 14.w,
                                colorFilter: ColorFilter.mode(
                                  _colorUtil.button02a,
                                  BlendMode.srcIn,
                                ),
                                semanticsLabel: MOBILE_ICON,
                              ),
                              SizedBox(width: 8.w),
                              InkWell(
                                onTap: () {
                                  _bloc.openPhoneDialer(
                                      widget.payload!.servicingDealer?.phone ??
                                          '');
                                },
                                child: Text(OneAppString.of().callDealership,
                                    style: TextStyleExtension()
                                        .newStyleWithColor(
                                            _textStyleUtil.callout2,
                                            _colorUtil.tertiary03)),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                )
              : SizedBox.shrink(),
          Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(OneAppString.of().odometer,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.callout1,
                                _colorUtil.tertiary07)),
                        SizedBox(height: 8.h),
                        Text(
                            checkIsEmptyOrNull(convertThousandsWithComma(
                                widget.payload?.mileage ?? '')),
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body3, _colorUtil.tertiary03)),
                      ],
                    ),
                    SizedBox(width: 48.h),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(OneAppString.of().orderNumber,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.callout1,
                                _colorUtil.tertiary07)),
                        SizedBox(height: 8.h),
                        Text(checkIsEmptyOrNull(widget.payload?.roNumber ?? ''),
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body3, _colorUtil.tertiary03)),
                      ],
                    ),
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(top: 16.h, bottom: 8.h),
                  child: Text(OneAppString.of().serviceItem,
                      textAlign: TextAlign.start,
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.callout1, _colorUtil.tertiary07)),
                ),
                Text(
                    _bloc.getOperationsPerformedList(
                        widget.payload?.operationsPerformed),
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.body3, _colorUtil.tertiary03)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _popBack() {
    Navigator.of(context).pop();
  }

  Widget _dateLayout() {
    return Container(
      width: 50.w,
      height: 60.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: _colorUtil.tertiary15,
        boxShadow: [
          BoxShadow(color: _colorUtil.button03b, spreadRadius: 3),
        ],
      ),
      child: Column(
        children: [
          Container(
              color: _colorUtil.button03b,
              alignment: Alignment.center,
              width: double.maxFinite,
              padding: EdgeInsets.only(top: 4.w),
              child: Text(
                  _bloc.getServiceMonth(widget.payload?.serviceDate ?? ''),
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.tabLabel01, _colorUtil.tile03))),
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: Container(
              height: 30.h,
              child: Text(
                  _bloc.getServiceDate(widget.payload?.serviceDate ?? ''),
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline3, _colorUtil.tertiary03)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _bottomSheetHeader(String title, {VoidCallback? onBackPressed}) {
    return Container(
      child: Column(
        children: [
          (widget.fromNative == true) ? Container() : SwipeBarIcon(),
          Container(
            height: kToolbarHeight,
            child: Stack(
              fit: StackFit.loose,
              children: [
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.center,
                    child: Container(
                      margin: EdgeInsets.only(right: 16.h),
                      child: Text(formatTextForLexusAndToyota(title),
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.subHeadline3,
                              _colorUtil.tertiary03)),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: InkWell(
                      onTap: onBackPressed,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _colorUtil.tertiary12,
                          shape: BoxShape.circle,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(8.w),
                          child: Icon(
                            Icons.chevron_left,
                            color: _colorUtil.tertiary00,
                            semanticLabel: BACK_BUTTON,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
