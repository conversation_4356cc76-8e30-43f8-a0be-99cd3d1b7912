// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/map_util.dart';

class ServiceAppointmentMap extends StatefulWidget {
  final Position? serviceCentrePosition;

  const ServiceAppointmentMap({Key? key, this.serviceCentrePosition})
      : super(key: key);

  @override
  ServiceAppointmentMapState createState() => ServiceAppointmentMapState();
}

class ServiceAppointmentMapState extends State<ServiceAppointmentMap> {
  MapboxMap? _controller;
  PointAnnotationManager? _pointAnnotationManager;
  Position? serviceCentrePosition;

  Future<void> addImageFromAsset(
    String name,
    String assetName, {
    required int width,
    required int height,
  }) async {
    final ByteData bytes = await rootBundle.load(assetName);
    final Uint8List list = bytes.buffer.asUint8List();
    return _controller?.style.addStyleImage(
        name,
        1,
        MbxImage(data: list, width: width, height: height),
        false,
        [],
        [],
        null);
  }

  @override
  void initState() {
    super.initState();
    serviceCentrePosition = widget.serviceCentrePosition;
  }

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = isDarkTheme();
    Future(() {
      _removeAll();
      _addAllPIN();
    });
    return MapWidget(
      androidHostingMode: AndroidPlatformViewHostingMode.TLHC_HC,
      onMapCreated: _onMapCreated,
      styleUri: isDarkMode == true ? MapboxStyles.DARK : MapboxStyles.LIGHT,
      onStyleLoadedListener: _onStyleLoaded,
      cameraOptions: CameraOptions(
        center: Point(coordinates: Position(40.745323, -73.989551)),
        zoom: 2,
      ),
    );
  }

  Future<void> _onMapCreated(MapboxMap controller) async {
    _controller = controller;
    _pointAnnotationManager =
        await controller.annotations.createPointAnnotationManager();
    // _controller!.onSymbolTapped.add(_onSymbolTapped);
    _pointAnnotationManager?.create(PointAnnotationOptions(
        geometry: Point(coordinates: Position(40.745323, -73.989551))));
    _removeAll();
    _addAllPIN();
  }

  void _onStyleLoaded([StyleLoadedEventData? _]) {
    addImageFromAsset(
      "mapCarLocationPin",
      navigatorWithBackgroundImage,
      height: 156,
      width: 156,
    );
  }

  Future<void> _addAllPIN() async {
    if (_controller == null) return;
    final List<PointAnnotationOptions> symbolOptionsList = [];

    Future.delayed(const Duration(milliseconds: 100), () {
      try {
        if (serviceCentrePosition?.lat != null &&
            serviceCentrePosition?.lng != null) {
          symbolOptionsList.add(
            PointAnnotationOptions(
              symbolSortKey: -1,
              geometry: Point(coordinates: serviceCentrePosition!),
              iconImage: 'mapCarLocationPin',
              iconSize: 0.3,
            ),
          );
          addSymbolsToMapboxController(
            _pointAnnotationManager!,
            symbolOptionsList,
          );
          if (_controller != null) {
            if (serviceCentrePosition != null) {
              _controller!.flyTo(
                CameraOptions(
                  center: Point(coordinates: serviceCentrePosition!),
                  zoom: 17,
                ),
                null,
              );
            }
          }
        }
      } catch (e) {
        FireBaseAnalyticsLogger.logError(e.toString());
      }
    });
  }

  void _removeAll() {
    if (_controller != null) {
      removeSymbolsFromMapboxController(_pointAnnotationManager!);
    }
  }

  // TODO
  // void _onSymbolTapped(Symbol symbol) {
  //   if (serviceCentrePosition?.latitude != null &&
  //       serviceCentrePosition?.longitude != null) {
  //     String googleMapslocationUrl =
  //         "https://www.google.com/maps/search/?api=1&query=${serviceCentrePosition!.latitude},${serviceCentrePosition!.longitude}";
  //     urlLauncher(googleMapslocationUrl);
  //   }
  // }
}
