// Flutter imports:

// Dart imports:
import 'dart:async';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../local_repo/vehicle_repo.dart';

// Project imports:
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart'
    as serviceHistory;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleServiceHistoryBloc extends BlocBase {
  serviceHistory.Payload? serviceHistoryPayload;
  OneAppClient api = APIClientConfig.oneAppClient;
  late Function(bool) progressHandlerCallback;
  GetIt locator = GetIt.instance;

  final _serviceHistoryList =
      BehaviorSubject<List<serviceHistory.ServiceHistories>?>();

  Stream<List<serviceHistory.ServiceHistories>?> get serviceHistoryList =>
      _serviceHistoryList.stream;
  final _isAddServiceEnabled = BehaviorSubject<bool>();

  Stream<bool> get isAddServiceEnabled => _isAddServiceEnabled.stream;

  void init(Function(bool) progressHandler) {
    progressHandlerCallback = progressHandler;
    locator.isReady<serviceHistory.Payload>().then((value) async {
      try {
        serviceHistoryPayload = locator<serviceHistory.Payload>();
      } catch (e) {}
      if (serviceHistoryPayload != null) {
        _serviceHistoryList.sink.add(serviceHistoryPayload!.serviceHistories);
      }
    });
    checkAddServiceEnabled();
  }

  //Fetch Service history list when the new service record is added.
  Future<void> fetchHealthServiceHistory() async {
    progressHandlerCallback(true);
    locator.isReady<serviceHistory.Payload>().then((value) async {
      serviceHistoryPayload = null;
      try {
        serviceHistoryPayload = locator<serviceHistory.Payload>();
      } catch (e) {}
      _serviceHistoryList.sink.add(serviceHistoryPayload?.serviceHistories);
    });

    progressHandlerCallback(false);
  }

  Future<void> clearServiceHistoryData() async {
    locator.registerSingletonAsync<serviceHistory.Payload>(
        () async => serviceHistory.Payload(serviceHistories: null));
  }

  String? getDealerName(serviceHistory.ServiceHistories payload) {
    String? dealerName = '';
    if (payload.customerCreatedRecord!) {
      dealerName = payload.serviceProvider;
    } else {
      if (payload.servicingDealer?.servicingDealerName != null) {
        dealerName = payload.servicingDealer?.servicingDealerName;
      }
    }
    return dealerName;
  }

  void getUpdatedServiceDetail(dynamic value) {
    locator.isReady<serviceHistory.Payload>().then((value) async {
      serviceHistory.Payload? serviceHistoryPayload;
      try {
        serviceHistoryPayload = locator<serviceHistory.Payload>();
      } catch (e) {}
      if (serviceHistoryPayload != null) {
        _serviceHistoryList.sink.add(serviceHistoryPayload.serviceHistories);
      }
    });
  }

  void checkAddServiceEnabled() async {
    vehicleInfo.Payload? vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    bool available = isFeatureEnabled(ADD_SERVICE, vehicleItem?.features);
    _isAddServiceEnabled.sink.add(available);
  }

  @override
  void dispose() {
    _serviceHistoryList.close();
    _isAddServiceEnabled.close();
  }
}
