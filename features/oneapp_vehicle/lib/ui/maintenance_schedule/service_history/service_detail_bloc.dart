// Flutter imports:

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart'
    as serviceHistory;

class ServiceDetailBloc extends BlocBase {
  serviceHistory.ServiceHistories? serviceDetailPayload;
  late Function(bool) progressHandlerCallback;
  OneAppClient api = APIClientConfig.oneAppClient;
  GetIt locator = GetIt.instance;

  final _updateServiceDetail = BehaviorSubject<bool>();
  Stream<bool> get updateServiceDetail => _updateServiceDetail.stream;

  void init(serviceHistory.ServiceHistories? payload,
      Function(bool) progressHandler) {
    serviceDetailPayload = payload;
    progressHandlerCallback = progressHandler;
    _updateServiceDetail.sink.add(false);
  }

  String getOperationsPerformedList() {
    String services = '';
    if (serviceDetailPayload != null &&
        serviceDetailPayload?.operationsPerformed != null &&
        serviceDetailPayload?.operationsPerformed?.isNotEmpty == true) {
      services = serviceDetailPayload!.operationsPerformed
          .toString()
          .replaceAll('[', '')
          .replaceAll(']', '');
    }
    return services;
  }

  String getOdometerValue() {
    String odometerValue = '';
    try {
      if (serviceDetailPayload != null &&
          serviceDetailPayload?.mileage != null &&
          serviceDetailPayload?.unit != null) {
        odometerValue =
            convertThousandsWithComma(serviceDetailPayload!.mileage!) +
                ' ' +
                serviceDetailPayload!.unit!;
      }
    } catch (e) {
      FireBaseAnalyticsLogger.logError(e.toString(),
          category: LogCategory.FL_VEHI);
    }
    return odometerValue;
  }

  String getDealerName() {
    String dealerName = '';
    if (serviceDetailPayload != null) {
      if (serviceDetailPayload!.customerCreatedRecord ?? false) {
        dealerName = serviceDetailPayload!.serviceProvider ?? '';
      } else {
        dealerName =
            serviceDetailPayload!.servicingDealer?.servicingDealerName ?? '';
      }
    }
    return dealerName;
  }

  String getNotes() {
    String notes = '--';
    notes = serviceDetailPayload?.notes ?? '';
    return notes;
  }

  //To get date in a format like Feb 20, 2021.
  String getFormattedServiceDate() {
    String formattedDate = '';
    if (serviceDetailPayload != null) {
      formattedDate = getFormattedDate(serviceDetailPayload!.serviceDate ?? '');
    }
    return formattedDate;
  }

  //To get month from date.
  String? getServiceMonth() {
    String serviceMonth = '';
    if (serviceDetailPayload != null) {
      if (getFormattedDateAsList(serviceDetailPayload!.serviceDate ?? '')
              ?.isNotEmpty ==
          true) {
        serviceMonth =
            getFormattedDateAsList(serviceDetailPayload!.serviceDate ?? '')
                    ?.first
                    .toUpperCase() ??
                '';
      }
    }
    return serviceMonth;
  }

  //To get Date alone.
  String getServiceDate() {
    String serviceAppointmentDate = '';
    if (serviceDetailPayload != null) {
      if (getFormattedDateAsList(serviceDetailPayload!.serviceDate ?? '')
              ?.isNotEmpty ==
          true) {
        serviceAppointmentDate = getFormattedDateAsList(
                serviceDetailPayload!.serviceDate ?? '')?[1] ??
            '';
      }
    }
    return serviceAppointmentDate;
  }

  //To Delete service record
  Future<void> deleteServiceRecord(
      String serviceRecordId, Function redirectCallBack) async {
    progressHandlerCallback(true);
    final commonResponse = await api.deleteServiceHistoryRecord(
        Global.getInstance().vin!, serviceRecordId);
    progressHandlerCallback(false);
    if (commonResponse.response != null) {
      Global.getInstance().needAppointmentRefresh =
          true; //To refresh appointments list.
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.DELETE_SERVICE_SUCCESS,
          category: LogCategory.FL_VEHI);
      fetchHealthServiceHistory(redirectCallBack);
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.DELETE_SERVICE_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  //Update the history after deleting a record.
  Future<void> fetchHealthServiceHistory(Function redirectCallBack) async {
    progressHandlerCallback(true);
    VehicleRepo().storeServiceHistory();
    locator.isReady<serviceHistory.Payload>().then((value) async {
      progressHandlerCallback(false);
      redirectCallBack();
    });
  }

  void getUpdatedServiceDetail() {
    locator.isReady<serviceHistory.Payload>().then((value) async {
      serviceHistory.Payload? serviceHistoryPayload;
      try {
        serviceHistoryPayload = locator<serviceHistory.Payload>();
      } catch (e) {}
      if (serviceHistoryPayload != null &&
          serviceDetailPayload?.serviceHistoryId != null) {
        //From service history list get the current service detail data & update in service detail payload.
        serviceDetailPayload = serviceHistoryPayload.serviceHistories!
            .firstWhere((element) =>
                element.serviceHistoryId ==
                serviceDetailPayload?.serviceHistoryId);
        _updateServiceDetail.sink.add(
            true); //To update the entire layout with the updated service details.
      }
    });
  }

  @override
  void dispose() {
    _updateServiceDetail.close();
  }
}
