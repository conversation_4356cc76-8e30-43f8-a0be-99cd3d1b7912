// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';

// Project imports:
import '/log/vehicle_analytic_event.dart';
import '/ui/maintenance_schedule/service_history/service_detail_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart'
    as serviceHistory;

class ServiceDetailPage extends StatefulWidget {
  final serviceHistory.ServiceHistories? payload;

  ServiceDetailPage({this.payload});

  @override
  _ServiceDetailPageState createState() => _ServiceDetailPageState();
}

class _ServiceDetailPageState extends State<ServiceDetailPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  ServiceDetailBloc _bloc = ServiceDetailBloc();
  bool isServiceRecordEdited = false;

  @override
  void initState() {
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_SERVICE_DETAIL_PAGE);
    _bloc.init(widget.payload, _progressHandlerCallback);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Material(
          child: Navigator(
        onGenerateRoute: (_) => MaterialPageRoute(
          builder: (context2) => Builder(
            builder: (context) => Scaffold(
              body: Container(
                  color: _colorUtil.tertiary15,
                  child: SingleChildScrollView(
                    child: StreamBuilder<bool>(
                        stream: _bloc.updateServiceDetail,
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return _serviceDetailLayout();
                          } else {
                            return Container();
                          }
                        }),
                  )),
            ),
          ),
        ),
      )),
    );
  }

  Widget _serviceDetailLayout() {
    return Container(
      margin: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _bottomSheetHeader(OneAppString.of().serviceDetails,
              onBackPressed: _popBack),
          SizedBox(height: 16.h),
          Container(
              margin: EdgeInsets.only(bottom: 8.h),
              child: Center(child: _dateLayout())),
          Container(
              margin: EdgeInsets.only(bottom: 16.h),
              child: Center(
                  child: Text(_bloc.getFormattedServiceDate(),
                      style: TextStyleExtension().newStyleWithColor(
                          _textStyleUtil.body4, _colorUtil.tertiary03)))),
          _dealerNameLayout(),
          SizedBox(height: 24.h),
          Text(OneAppString.of().odometer,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary07)),
          SizedBox(height: 8.h),
          Text(_bloc.getOdometerValue(),
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body3, _colorUtil.tertiary03)),
          SizedBox(height: 24.h),
          Text(OneAppString.of().serviceItem,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary07)),
          SizedBox(height: 8.h),
          Text(_bloc.getOperationsPerformedList(),
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body3, _colorUtil.tertiary03)),
          SizedBox(height: 24.h),
          Text(OneAppString.of().notes,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary07)),
          SizedBox(height: 8.h),
          Text(_bloc.getNotes(),
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body3, _colorUtil.tertiary03)),
          SizedBox(height: 16.h),
          Center(
            child: TextButton(
              onPressed: () {
                BottomConfirmationDialog().showBottomDialog(
                    context,
                    OneAppString.of().deleteService,
                    OneAppString.of().deleteServiceRecordConfirmation,
                    alertIcon,
                    OneAppString.of().deleteText,
                    OneAppString.of().commonCancel,
                    _deleteExistingRecord);
              },
              child: Text(
                OneAppString.of().deleteService,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.buttonLink1, _colorUtil.button02a),
              ),
            ),
          ),
          SizedBox(height: 12.h),
          _editServiceRecordLayout(),
        ],
      ),
    );
  }

  void _deleteExistingRecord() {
    _bloc.deleteServiceRecord(
        widget.payload!.serviceHistoryId, _redirectToServiceHistory);
  }

  Widget _dateLayout() {
    return Container(
      width: 50.w,
      height: 60.h,
      margin: EdgeInsets.only(right: 16.w, bottom: 8.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: _colorUtil.tertiary15,
        boxShadow: [
          BoxShadow(color: _colorUtil.button03b, spreadRadius: 3),
        ],
      ),
      child: Column(
        children: [
          Container(
              color: _colorUtil.button03b,
              alignment: Alignment.center,
              width: double.maxFinite,
              padding: EdgeInsets.only(top: 4.w),
              child: Text(_bloc.getServiceMonth() ?? '',
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.tabLabel01, _colorUtil.tile03))),
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: Text(_bloc.getServiceDate(),
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.subHeadline3, _colorUtil.tertiary03)),
          ),
        ],
      ),
    );
  }

  Widget _dealerNameLayout() {
    return Card(
      elevation: 1,
      margin: EdgeInsets.all(0),
      color: _colorUtil.tile03,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
      child: Padding(
          padding: EdgeInsets.all(8.w),
          child: Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: _colorUtil.tertiary15,
                  shape: BoxShape.circle,
                ),
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: SvgPicture.asset(
                    maintenanceIcon,
                    width: 18.w,
                    height: 18.h,
                    colorFilter: ColorFilter.mode(
                      _colorUtil.secondary01,
                      BlendMode.srcIn,
                    ),
                    semanticsLabel: MAINTENANCE_ICON,
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(_bloc.getDealerName(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.body4, _colorUtil.tertiary03)),
              ),
            ],
          )),
    );
  }

  void _redirectToServiceHistory() {
    Future.delayed(const Duration(milliseconds: 300), () {
      Navigator.of(context).pop(true);
    });
  }

  void _popBack() {
    if (isServiceRecordEdited) {
      Navigator.of(context).pop(true);
    } else {
      Navigator.of(context).pop(false);
    }
  }

  Widget _editServiceRecordLayout() {
    return Container(
      margin: EdgeInsets.only(bottom: 32.h),
      child: Center(
        child: CustomDefaultButton(
          text: OneAppString.of().edit,
          borderColor: _colorUtil.button01b,
          buttonTextColor: _colorUtil.button01a,
          backgroundColor: _colorUtil.button01b,
          press: () {
            isServiceRecordEdited = true;
            NavigateService.pushNamedRoute(RoutePath.UPDATE_SERVICE_RECORD_PAGE,
                    arguments: _bloc.serviceDetailPayload)
                .then((value) => updateServiceDetail(value));
          },
          horizontalPadding: 36.w,
          verticalPadding: 16.h,
        ),
      ),
    );
  }

  void updateServiceDetail(dynamic value) {
    if (value != null) {
      _bloc.getUpdatedServiceDetail();
    }
  }

  Widget _bottomSheetHeader(String title, {VoidCallback? onBackPressed}) {
    return Container(
      child: Column(
        children: [
          SwipeBarIcon(),
          Container(
            height: kToolbarHeight,
            child: Stack(
              fit: StackFit.loose,
              children: [
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.center,
                    child: Container(
                      child: Text(formatTextForLexusAndToyota(title),
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.subHeadline3,
                              _colorUtil.tertiary03)),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: InkWell(
                      onTap: onBackPressed,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _colorUtil.tertiary12,
                          shape: BoxShape.circle,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(8.w),
                          child: Icon(
                            Icons.chevron_left,
                            color: _colorUtil.tertiary00,
                            semanticLabel: BACK_BUTTON,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }
}
