// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:rxdart/rxdart.dart';

class ServiceAppointmentDetailBloc extends BlocBase {
  Stream<bool> get phonePermissionDenied => _phonePermissionDenied.stream;
  final _phonePermissionDenied = BehaviorSubject<bool>();

  String getOperationsPerformedList(List<String>? operationsPerformedList) {
    String services = '';
    if (operationsPerformedList != null && operationsPerformedList.isNotEmpty) {
      services = operationsPerformedList
          .toString()
          .replaceAll('[', '')
          .replaceAll(']', '');
    }
    return services;
  }

  String getServiceDealerPhoneNumber(String phoneNumber) {
    String dealerNumber = checkIsEmptyOrNull(phoneNumber);
    if (dealerNumber.isNotEmpty) {
      if (dealerNumber.length > 8) {
        // If length is greater than 8 display the first three digits in brackets and leave a space after sixth digit.
        dealerNumber = '(' +
            dealerNumber.substring(0, 3) +
            ') ' +
            dealerNumber.substring(3, 6) +
            ' ' +
            dealerNumber.substring(6);
      }
    } else {
      dealerNumber = OneAppString.of().notAvailable;
    }
    return dealerNumber;
  }

  String getServiceDealerName(String dealerName) {
    String name = '';
    String dealer = checkIsEmptyOrNull(dealerName);
    if (dealer.isNotEmpty && dealer.trim().isNotEmpty) {
      name = convertFirstLetterToCaps(dealer);
    }
    return name;
  }

  String getServiceDealerAddressAndCity(String? address, String? city) {
    String addressAndCity = '';

    if (address != null && address.isNotEmpty) {
      addressAndCity = address;
    }
    if (city != null && city.isNotEmpty) {
      if (addressAndCity.isEmpty) {
        addressAndCity = city;
      } else {
        addressAndCity = addressAndCity + ', ' + city;
      }
    }

    return addressAndCity;
  }

  String getServiceDealerStateAndZip(String state, String zip) {
    String stateAndZipcode = '';

    if (state.isNotEmpty) {
      stateAndZipcode = state;
    }
    if (zip.isNotEmpty) {
      if (stateAndZipcode.isEmpty) {
        stateAndZipcode = zip;
      } else {
        stateAndZipcode = stateAndZipcode + ', ' + zip;
      }
    }

    return stateAndZipcode;
  }

  //To get month from date.
  String getServiceMonth(String? serviceDate) {
    String serviceMonth = '';
    if (serviceDate != null && serviceDate.isNotEmpty) {
      if (getFormattedDateAsList(serviceDate)?.isNotEmpty == true) {
        serviceMonth =
            getFormattedDateAsList(serviceDate)?.first.toUpperCase() ?? '';
      }
    }
    return serviceMonth;
  }

  //To get Date alone.
  String getServiceDate(String? serviceDate) {
    String serviceAppointmentDate = '';
    if (serviceDate != null && serviceDate.isNotEmpty) {
      if (getFormattedDateAsList(serviceDate)?.isNotEmpty == true) {
        serviceAppointmentDate = getFormattedDateAsList(serviceDate)?[1] ?? '';
      }
    }
    return serviceAppointmentDate;
  }

  //To get date in a format like Feb 20, 2021.
  String getFormattedServiceDate(String? serviceDate) {
    String formattedDate = '';
    if (serviceDate != null) {
      formattedDate = getFormattedDate(serviceDate);
    }
    return formattedDate;
  }

  void openPhoneDialer(String? phoneNumber) async {
    if (phoneNumber != null && phoneNumber.isNotEmpty) {
      dialerLauncherPermissionDialog(phoneNumber).then((value) {
        if (value == false) _phonePermissionDenied.sink.add(true);
      });
    }
  }

  @override
  void dispose() {
    _phonePermissionDenied.close();
  }
}
