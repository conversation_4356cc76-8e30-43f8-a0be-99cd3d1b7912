// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';

// Project imports:
import 'add_service_record/add_service_record.dart';
import 'service_appointment_detail_page.dart';
import 'service_detail_page.dart';
import 'vehicle_service_history_bloc.dart';

//Package imports:
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart'
    as serviceHistory;

class VehicleServiceHistoryPage extends StatefulWidget {
  final bool? fromNative;
  VehicleServiceHistoryPage({this.fromNative});
  @override
  _VehicleServiceHistoryPageState createState() =>
      _VehicleServiceHistoryPageState();
}

class _VehicleServiceHistoryPageState extends State<VehicleServiceHistoryPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  VehicleServiceHistoryBloc _bloc = VehicleServiceHistoryBloc();

  @override
  void initState() {
    super.initState();
    _bloc.init(_progressHandlerCallback);
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        child: content(),
        onPopInvoked: (didPop) async {
          if (didPop) return;
          if (widget.fromNative == true) {
            goBackToNative();
          }
        });
  }

  Widget content() {
    if (widget.fromNative == true) {
      return OneAppScaffold(
        backgroundColor: _colorUtil.tertiary15,
        appBar: PreferredSize(
            preferredSize: Size.fromHeight(kToolbarHeight),
            child: Container(
              child: _appBarLayout(),
            )),
        containAppBarShadow: false,
        body: SafeArea(child: Container(child: _serviceHistoryLayout())),
      );
    } else {
      return Container(
        color: _colorUtil.tertiary15,
        child: SafeArea(
          child: Material(
              child: Navigator(
            onGenerateRoute: (_) => MaterialPageRoute(
              builder: (context2) => Builder(
                builder: (context) => OneAppScaffold(
                  body: Container(
                      color: _colorUtil.tertiary15,
                      child: _serviceHistoryLayout()),
                ),
              ),
            ),
          )),
        ),
      );
    }
  }

  // app bar with title
  Widget _appBarLayout() {
    return AppBar(
      backgroundColor: _colorUtil.tertiary15,
      centerTitle: true,
      elevation: 0,
      title: Text(
        formatTextForLexusAndToyota(OneAppString.of().serviceHistory),
        style: TextStyleExtension().newStyleWithColor(
            _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
      ),
      leading: InkWell(
          onTap: _popBack,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: 48.w,
                width: 48.w,
                margin: EdgeInsets.only(left: 16.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: ThemeConfig.current().colorUtil.button02b,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.chevron_left,
                  color: ThemeConfig.current().colorUtil.button02a,
                  semanticLabel: BACK_BUTTON,
                ),
              ),
            ],
          )),
    );
  }

  Widget _headerLayout() {
    if (widget.fromNative == true) {
      return Container();
    } else {
      return _bottomSheetHeader(OneAppString.of().serviceHistory,
          onBackPressed: _popBack);
    }
  }

  // Service History List Layout
  Widget _serviceHistoryLayout() {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
      child: Column(
        children: [
          _headerLayout(),
          StreamBuilder<List<serviceHistory.ServiceHistories>?>(
              stream: _bloc.serviceHistoryList,
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return Expanded(child: Container());
                } else if (snapshot.hasData && snapshot.data?.isEmpty == true) {
                  return Expanded(
                    child: Container(
                      alignment: Alignment.center,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: _colorUtil.primary02,
                                shape: BoxShape.circle,
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(16.w),
                                child: SvgPicture.asset(
                                  calendarIcon,
                                  height: 18.h,
                                  width: 18.w,
                                  colorFilter: ColorFilter.mode(
                                    _colorUtil.primary01,
                                    BlendMode.srcIn,
                                  ),
                                  semanticsLabel: CALENDAR_ICON,
                                ),
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Text(OneAppString.of().noServiceHistory,
                                style: TextStyleExtension().newStyleWithColor(
                                    _textStyleUtil.subHeadline1,
                                    _colorUtil.tertiary03)),
                            SizedBox(height: 8.h),
                            Container(
                              margin: EdgeInsets.only(left: 24.w, right: 24.w),
                              child: Text(OneAppString.of().serviceHistoryNote,
                                  textAlign: TextAlign.center,
                                  style: TextStyleExtension().newStyleWithColor(
                                      _textStyleUtil.callout1,
                                      _colorUtil.tertiary05)),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                } else {
                  return Expanded(
                    child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: snapshot.data?.length ?? 0,
                        controller: ModalScrollController.of(context),
                        itemBuilder: (BuildContext context, int index) {
                          return InkWell(
                            onTap: () {
                              if (snapshot.data?[index].customerCreatedRecord !=
                                      null &&
                                  snapshot.data?[index].customerCreatedRecord ==
                                      true) {
                                Navigator.of(context)
                                    .push(
                                      MaterialPageRoute(
                                        builder: (context) => ServiceDetailPage(
                                          payload: snapshot.data?[index],
                                        ),
                                      ),
                                    )
                                    .then((value) =>
                                        _updateServiceHistoryAfterEdit(value));
                              } else {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        ServiceAppointmentDetailPage(
                                            payload: snapshot.data?[index],
                                            fromNative: widget.fromNative),
                                  ),
                                );
                              }
                            },
                            child: Container(
                              margin: EdgeInsets.only(bottom: 8.h),
                              child: Card(
                                elevation: 0,
                                color: _colorUtil.tile02,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(8.0.r))),
                                child: Padding(
                                  padding: EdgeInsets.all(16.w),
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.only(
                                            left: 8.w, right: 8.w, top: 4.h),
                                        decoration: BoxDecoration(
                                            color: _colorUtil.secondary01,
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(
                                                    CARD_RADIUS_SMALL))),
                                        child: Column(
                                          children: [
                                            Text(
                                                getFormattedDateAsList(
                                                      snapshot.data?[index]
                                                          .serviceDate,
                                                    )?.first.toUpperCase() ??
                                                    '',
                                                style: TextStyleExtension()
                                                    .newStyleWithColor(
                                                        _textStyleUtil.callout1,
                                                        _colorUtil.tile02)),
                                            Container(
                                              color: _colorUtil.tile01,
                                              padding: EdgeInsets.only(
                                                left: 8.w,
                                                right: 8.w,
                                                top: 4.h,
                                              ),
                                              margin: EdgeInsets.all(4.w),
                                              child: Text(
                                                getFormattedDateAsList(
                                                      snapshot.data?[index]
                                                          .serviceDate,
                                                    )?[1] ??
                                                    '',
                                                style: TextStyleExtension()
                                                    .newStyleWithColor(
                                                  _textStyleUtil.subHeadline3,
                                                  _colorUtil.tertiary03,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 8.w),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                                (snapshot.data?[index].unit !=
                                                            null &&
                                                        snapshot.data?[index]
                                                                .mileage !=
                                                            null)
                                                    ? convertThousandsWithComma(
                                                          snapshot.data?[index]
                                                                  .mileage ??
                                                              "",
                                                        ) +
                                                        ' ' +
                                                        (snapshot.data?[index]
                                                                .unit ??
                                                            "")
                                                    : '',
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: TextStyleExtension()
                                                    .newStyleWithColor(
                                                        _textStyleUtil.body4,
                                                        _colorUtil.tertiary03)),
                                            Padding(
                                              padding: EdgeInsets.only(top: 4),
                                              child: Text(
                                                  (snapshot.data?[index] !=
                                                          null)
                                                      ? _bloc.getDealerName(
                                                              snapshot.data![
                                                                  index]) ??
                                                          ''
                                                      : '',
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: TextStyleExtension()
                                                      .newStyleWithColor(
                                                          _textStyleUtil
                                                              .callout1,
                                                          _colorUtil
                                                              .tertiary05)),
                                            ),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        }),
                  );
                }
              }),
          _addServiceRecordLayout()
        ],
      ),
    );
  }

  _addServiceRecordScreen() {
    return showMaterialModalBottomSheet(
            expand: false,
            context: context,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(20.r),
              ),
            ),
            clipBehavior: Clip.antiAliasWithSaveLayer,
            backgroundColor: _colorUtil.tertiary15,
            builder: (context) => AddServiceRecordPage(payload: null))
        .then(updateServiceHistory);
  }

  Widget _bottomSheetHeader(String title, {VoidCallback? onBackPressed}) {
    return Container(
      child: Column(
        children: [
          SwipeBarIcon(),
          Container(
            height: kToolbarHeight,
            child: Stack(
              fit: StackFit.loose,
              children: [
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.center,
                    child: Center(
                      child: Text(formatTextForLexusAndToyota(title),
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.subHeadline3,
                              _colorUtil.tertiary03)),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _popBack() async {
    _bloc.clearServiceHistoryData();
    goBackToNative();
  }

  void updateServiceHistory(dynamic value) {
    if (value != null) {
      _bloc.fetchHealthServiceHistory();
    }
  }

  Widget _addServiceRecordLayout() {
    return StreamBuilder<Object>(
        stream: _bloc.isAddServiceEnabled,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data == true) {
            return Container(
              margin: EdgeInsets.only(top: 8.h, bottom: 8.h),
              child: Center(
                child: CustomDefaultButton(
                  text: OneAppString.of().addRecord,
                  borderColor: _colorUtil.button01b,
                  buttonTextColor: _colorUtil.button01a,
                  backgroundColor: _colorUtil.button01b,
                  press: () {
                    _addServiceRecordScreen();
                  },
                  horizontalPadding: 40.w,
                  verticalPadding: 12.h,
                ),
              ),
            );
          } else {
            return SizedBox.shrink();
          }
        });
  }

  void _updateServiceHistoryAfterEdit(dynamic value) {
    if (value != null && value == true) {
      _bloc.getUpdatedServiceDetail(value);
    }
  }
}
