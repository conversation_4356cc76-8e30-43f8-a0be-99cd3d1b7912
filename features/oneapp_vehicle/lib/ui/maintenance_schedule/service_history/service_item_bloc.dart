//Package imports:

// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/day_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle_singleton/service_item_dto_helper.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:

class ServiceItemBloc extends BlocBase {
  final _serviceItemList = BehaviorSubject<List<DayHelper>?>();
  Stream<List<DayHelper>?> get serviceItemList => _serviceItemList.stream;

  List<DayHelper>? userModifiedServiceItemList;

  void getServiceItemList() {
    String? userSelectedServiceItem = ServiceItemDtoHelper().getServiceItem();
    List<String> userSelectedServiceItemList = [];
    if (userSelectedServiceItem != null) {
      userSelectedServiceItem.split(",").forEach((element) {
        userSelectedServiceItemList.add(element);
      });
    }

    List<DayHelper> serviceItemList = <DayHelper>[
      DayHelper(
        day: OneAppString.of().airConditioning,
      ),
      DayHelper(
        day: OneAppString.of().brakes,
      ),
      DayHelper(
        day: OneAppString.of().clutch,
      ),
      DayHelper(
        day: OneAppString.of().coolingSystem,
      ),
      DayHelper(
        day: OneAppString.of().electrical,
      ),
      DayHelper(
        day: OneAppString.of().engine,
      ),
      DayHelper(
        day: OneAppString.of().exhaust,
      ),
      DayHelper(
        day: OneAppString.of().factoryMaintenance,
      ),
      DayHelper(
        day: OneAppString.of().fuel,
      ),
      DayHelper(
        day: OneAppString.of().maintenance,
      ),
      DayHelper(
        day: OneAppString.of().suspension,
      ),
      DayHelper(
        day: OneAppString.of().transmission,
      ),
      DayHelper(
        day: OneAppString.of().miscellaneousService,
      ),
    ];

    for (final String selectedItem in userSelectedServiceItemList) {
      for (final DayHelper element in serviceItemList) {
        if (selectedItem.trim().toString() == element.day!.trim().toString()) {
          element.isSelected = true;
        }
      }
    }

    userModifiedServiceItemList = serviceItemList;

    _serviceItemList.sink.add(serviceItemList);
  }

  void updateServiceItemList(bool isSelected, int index) {
    userModifiedServiceItemList![index].isSelected = isSelected;
    _serviceItemList.sink.add(userModifiedServiceItemList);

    ServiceItemDtoHelper().setServiceItem(getServiceItemText()!);
  }

  String? getServiceItemText() {
    String? serviceItemText = '';
    for (final DayHelper serviceItem in userModifiedServiceItemList!) {
      if (serviceItem.isSelected) {
        if (serviceItemText!.isEmpty) {
          serviceItemText = serviceItem.day;
        } else {
          serviceItemText = serviceItemText + ', ' + serviceItem.day!;
        }
      }
    }
    return serviceItemText;
  }

  @override
  void dispose() {
    _serviceItemList.close();
  }
}
