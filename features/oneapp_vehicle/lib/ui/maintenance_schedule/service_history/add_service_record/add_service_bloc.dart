// Dart imports:
import 'dart:async';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/service_record_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle_singleton/service_item_dto_helper.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../../local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart'
    as serviceHistory;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class AddServiceBloc extends BlocBase {
  OneAppClient api = APIClientConfig.oneAppClient;
  GetIt locator = GetIt.instance;
  late Function(bool) progressHandlerCallback;
  Function(String)? dialogHandlerCallback;
  vehicleInfo.Payload? vehicleItem;

  final _isServiceItemSelected = BehaviorSubject<bool>();

  Stream<bool> get isServiceItemSelected => _isServiceItemSelected.stream;

  final _isServiceDateSelected = BehaviorSubject<bool>();

  Stream<bool> get isServiceDateSelected => _isServiceDateSelected.stream;

  Stream<PrimaryButtonState> get saveButtonState => _saveButtonState.stream;
  final _saveButtonState = StreamController<PrimaryButtonState>();

  void init(Function(bool) progressHandler, Function(String) dialogHandler) {
    progressHandlerCallback = progressHandler;
    dialogHandlerCallback = dialogHandler;
    fetchVehicleInfo();
    updateButtonStatus(false); // To disable button on load of the page.
  }

  Future<void> fetchVehicleInfo() async {
    String? vin = Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);
  }

  void updateButtonStatus(bool isEnabled) {
    if (isEnabled) {
      _saveButtonState.sink.add(PrimaryButtonState.ACTIVE);
    } else {
      _saveButtonState.sink.add(PrimaryButtonState.INACTIVE);
    }
  }

  String? getServiceItem() {
    String? serviceItem = '';
    if (ServiceItemDtoHelper().getServiceItem() != null &&
        ServiceItemDtoHelper().getServiceItem()!.isNotEmpty) {
      serviceItem = ServiceItemDtoHelper().getServiceItem();
    }
    return serviceItem;
  }

  void saveChanges(ServiceRecordHelper requestBody, Function redirectCallBack,
      Function errorCallBack) async {
    progressHandlerCallback(true);
    final commonResponse = await api.createServiceHistoryRecord(
        Global.getInstance().vin!, requestBody);
    progressHandlerCallback(false);
    Global.getInstance().needAppointmentRefresh = true;
    if (commonResponse.response != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.ADD_SERVICE_SUCCESS,
          category: LogCategory.FL_VEHI);
      fetchServiceHistory(redirectCallBack);
    } else {
      errorCallBack(OneAppString.of().internalServerError);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.ADD_SERVICE_FAILURE,
          category: LogCategory.FL_VEHI);
    }

    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.ADD_SERVICE_FAILURE,
          category: LogCategory.FL_VEHI);
      if (commonResponse.error?.errorMessage != null) {
        errorCallBack(commonResponse.error!.errorMessage);
      }
    }
  }

  void updateServiceRecord(ServiceRecordHelper requestBody,
      String serviceRecordId, Function redirectCallBack) async {
    progressHandlerCallback(true);

    final commonResponse = await api.updateServiceHistoryRecord(
        Global.getInstance().vin!,
        serviceRecordId,
        Global.getInstance().correlationId,
        requestBody,
        vehicleItem!.brand ?? Global.getInstance().appBrand);
    progressHandlerCallback(false);
    Global.getInstance().needAppointmentRefresh = true;
    if (commonResponse.response != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.ADD_SERVICE_SUCCESS,
          category: LogCategory.FL_VEHI);
      fetchServiceHistory(redirectCallBack);
    } else {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.ADD_SERVICE_FAILURE,
          category: LogCategory.FL_VEHI);
    }

    if (commonResponse.error != null) {
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.ADD_SERVICE_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  void updateServiceItem(bool isChanged) {
    _isServiceItemSelected.sink.add(isChanged);
  }

  void updateServiceDate(bool isChanged) {
    _isServiceDateSelected.sink.add(isChanged);
  }

  //Fetch Service History to get the updated list after edit and save.
  void fetchServiceHistory(Function redirectCallBack) {
    progressHandlerCallback(true);
    VehicleRepo().storeServiceHistory();
    locator.isReady<serviceHistory.Payload>().then((value) async {
      progressHandlerCallback(false);
      redirectCallBack();
    });
  }

  @override
  void dispose() {
    _isServiceItemSelected.close();
    _isServiceDateSelected.close();
    _saveButtonState.close();
  }
}
