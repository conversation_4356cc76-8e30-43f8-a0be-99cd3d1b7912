// Dart imports:

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/regex_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/service_record_helper.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle_singleton/service_item_dto_helper.dart';

// Project imports:
import '../service_item_page.dart';
import 'add_service_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_history_entity.dart'
    as serviceHistory;

//Project imports:

class AddServiceRecordPage extends StatefulWidget {
  final serviceHistory.ServiceHistories? payload;

  AddServiceRecordPage({this.payload});

  @override
  _AddServiceRecordPageState createState() => _AddServiceRecordPageState();
}

class _AddServiceRecordPageState extends State<AddServiceRecordPage> {
  final _formKey = GlobalKey<FormState>();
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  TextEditingController serviceProviderController = TextEditingController();
  TextEditingController odometerController = TextEditingController();
  TextEditingController serviceDateController = TextEditingController();
  TextEditingController serviceItemController = TextEditingController();
  TextEditingController notesController = TextEditingController();
  DateTime selectedDate = DateTime.now();
  AddServiceBloc _bloc = AddServiceBloc();
  String? operationsPerformed;
  bool isErrorOccurred = true;
  bool isAddServiceRecordFlow = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: OneAppScaffold(
        resizeToAvoidBottomInset:
            true, //To avoid keyboard hiding the notes textfield.
        body: SingleChildScrollView(
          child: Container(
              color: _colorUtil.tertiary15,
              child: SafeArea(child: _addServiceRecord())),
        ),
      ),
    );
  }

  @override
  void initState() {
    serviceItemController = TextEditingController(text: '');

    if (widget.payload != null) {
      isAddServiceRecordFlow = false;
      setPreviouslySavedData();
    } else {
      //If widget.payload is null then it will be fresh add record flow.
      //Setting it as null so that previously selected values will not be displayed as selected in service items list.
      ServiceItemDtoHelper().setServiceItem("");
    }
    _bloc.init(_progressHandlerCallback, _dialogHandlerCallback);
    if (widget.payload?.serviceDate?.isNotEmpty == true) {
      selectedDate = DateTime.parse(widget.payload!.serviceDate!);
    }
    checkIfAllMandatoryFieldsAreEntered();
    super.initState();
  }

  void setPreviouslySavedData() {
    serviceProviderController =
        TextEditingController(text: widget.payload!.serviceProvider);
    odometerController = TextEditingController(text: widget.payload!.mileage);
    serviceDateController =
        TextEditingController(text: widget.payload!.serviceDate);

    serviceItemController = TextEditingController(
        text: widget.payload!.operationsPerformed != null
            ? widget.payload!.operationsPerformed!.toList().join(",")
            : '');
    if (widget.payload!.operationsPerformed != null) {
      ServiceItemDtoHelper().setServiceItem(
          widget.payload!.operationsPerformed!.toList().join(","));
    }
    notesController = TextEditingController(text: widget.payload!.notes);
  }

  void _popBack() {
    Navigator.of(context).pop();
  }

  Widget _addServiceRecord() {
    return Container(
      margin: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 16.h),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _bottomSheetHeader(
                isAddServiceRecordFlow
                    ? OneAppString.of().addServiceRecord
                    : OneAppString.of().serviceDetails,
                onBackPressed: _popBack),
            isAddServiceRecordFlow
                ? Text(OneAppString.of().serviceRecordText,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.body3, _colorUtil.tertiary05))
                : Container(),
            SizedBox(height: 12.h),
            _serviceProviderLayout(),
            Container(
              margin: EdgeInsets.only(top: 12.h, bottom: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _odometerLayout(),
                  SizedBox(width: 16.w),
                  _serviceDateLayout(),
                ],
              ),
            ),
            _serviceItemsLayout(),
            _notesLayout(),
            _saveChangesLayout()
          ],
        ),
      ),
    );
  }

  Widget _serviceProviderLayout() {
    return Container(
      margin: EdgeInsets.only(top: 12.h, bottom: 4.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 16.h),
            child: Text(OneAppString.of().serviceProvider,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.callout1, _colorUtil.tertiary07)),
          ),
          TextFormField(
            style: TextStyleExtension()
                .newStyleWithColor(_textStyleUtil.body3, _colorUtil.tertiary05),
            controller: serviceProviderController,
            smartQuotesType: SmartQuotesType.disabled,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            validator: (text) {
              if (text == null || text.isEmpty) {
                return OneAppString.of().required;
              } else if (text.length < 5) {
                return OneAppString.of().providerNameErrorText;
              }
              return null;
            },
            onChanged: (text) {
              checkIfAllMandatoryFieldsAreEntered();
            },
            decoration: InputDecoration(
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(4.r)),
                  borderSide: BorderSide(color: _colorUtil.tertiary10),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(4.r)),
                  borderSide: BorderSide(color: _colorUtil.tertiary10),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: _colorUtil.tertiary10),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(4.r)),
                  borderSide: BorderSide(color: _colorUtil.gradientOneStart),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(4.r)),
                  borderSide: BorderSide(color: _colorUtil.tertiary10),
                ),
                filled: true,
                fillColor: _colorUtil.tertiary15,
                errorMaxLines: 2),
          )
        ],
      ),
    );
  }

  Widget _odometerLayout() {
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 16.h),
            child: Text(OneAppString.of().odometer,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.callout1, _colorUtil.tertiary07)),
          ),
          TextFormField(
            style: TextStyleExtension()
                .newStyleWithColor(_textStyleUtil.body3, _colorUtil.tertiary05),
            controller: odometerController,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            keyboardType: TextInputType.numberWithOptions(decimal: false),
            maxLength: 10,
            validator: (text) {
              if (text == null || text.isEmpty) {
                return OneAppString.of().required;
              } else if (text.length > 10) {
                return OneAppString.of().odometerValidationText;
              }
              return null;
            },
            onChanged: (text) {
              checkIfAllMandatoryFieldsAreEntered();
            },
            decoration: InputDecoration(
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(4.r)),
                borderSide: BorderSide(color: _colorUtil.tertiary10),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(4.r)),
                borderSide: BorderSide(color: _colorUtil.tertiary10),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: _colorUtil.tertiary10),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(4.r)),
                borderSide: BorderSide(color: _colorUtil.gradientOneStart),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(4.r)),
                borderSide: BorderSide(color: _colorUtil.tertiary10),
              ),
              filled: true,
              fillColor: _colorUtil.tertiary15,
              counterText: "",
            ),
          )
        ],
      ),
    );
  }

  Widget _serviceDateLayout() {
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 16.h),
            child: Text(OneAppString.of().serviceDate,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.callout1, _colorUtil.tertiary07)),
          ),
          StreamBuilder<bool>(
              stream: _bloc.isServiceDateSelected,
              builder: (context, snapshot) {
                return TextFormField(
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.body3, _colorUtil.tertiary05),
                  controller: serviceDateController,
                  readOnly: true,
                  autofocus: false,
                  onTap: () {
                    _selectDate(context);
                  },
                  validator: (text) {
                    if (text == null || text.isEmpty) {
                      return OneAppString.of().required;
                    }
                    return null;
                  },
                  decoration: InputDecoration(
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4.r)),
                        borderSide: BorderSide(color: _colorUtil.tertiary10),
                      ),
                      disabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4.r)),
                        borderSide: BorderSide(color: _colorUtil.tertiary10),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: _colorUtil.tertiary10),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4.r)),
                        borderSide: BorderSide(color: _colorUtil.tertiary10),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4.r)),
                        borderSide:
                            BorderSide(color: _colorUtil.gradientOneStart),
                      ),
                      filled: true,
                      fillColor: _colorUtil.tertiary15),
                );
              })
        ],
      ),
    );
  }

  _selectServiceItemsScreen() {
    return showMaterialModalBottomSheet(
        expand: false,
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20.r),
          ),
        ),
        clipBehavior: Clip.antiAliasWithSaveLayer,
        backgroundColor: _colorUtil.tertiary15,
        builder: (context) => ServiceItemPage()).then(updateServiceItem);
  }

  void updateServiceItem(dynamic value) {
    serviceItemController = TextEditingController(text: _bloc.getServiceItem());
    checkIfAllMandatoryFieldsAreEntered();
    _bloc.updateServiceItem(true);
  }

  Widget _serviceItemsLayout() {
    return Container(
        margin: EdgeInsets.only(top: 12.h, bottom: 4.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(bottom: 16.h),
              child: Text(OneAppString.of().serviceItem,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout1, _colorUtil.tertiary07)),
            ),
            StreamBuilder<bool>(
                stream: _bloc.isServiceItemSelected,
                builder: (context, snapshot) {
                  return TextFormField(
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.body3, _colorUtil.tertiary05),
                    controller: serviceItemController,
                    readOnly: true,
                    autofocus: false,
                    maxLines: null,
                    onTap: () {
                      _selectServiceItemsScreen();
                    },
                    validator: (text) {
                      if (text == null || text.isEmpty) {
                        return OneAppString.of().required;
                      }
                      return null;
                    },
                    decoration: InputDecoration(
                        suffixIcon: Icon(
                          Icons.keyboard_arrow_down,
                          color: _colorUtil.tertiary03,
                          semanticLabel: CHEVRON_DOWN_ICON,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(4.r)),
                          borderSide: BorderSide(color: _colorUtil.tertiary10),
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(4.r)),
                          borderSide: BorderSide(color: _colorUtil.tertiary10),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: _colorUtil.tertiary10),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(4.r)),
                          borderSide:
                              BorderSide(color: _colorUtil.gradientOneStart),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(4.r)),
                          borderSide: BorderSide(color: _colorUtil.tertiary10),
                        ),
                        hintText: OneAppString.of().select,
                        hintStyle: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.body3, _colorUtil.tertiary07),
                        filled: true,
                        fillColor: _colorUtil.tertiary15),
                  );
                }),
          ],
        ));
  }

  Widget _notesLayout() {
    return Container(
      margin: EdgeInsets.only(top: 12.h, bottom: 4.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 16.h),
            child: Text(OneAppString.of().notes,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.callout1, _colorUtil.tertiary07)),
          ),
          Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: TextFormField(
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.body3, _colorUtil.tertiary05),
              controller: notesController,
              maxLines: 4,
              textInputAction: TextInputAction.done,
              inputFormatters: [
                LengthLimitingTextInputFormatter(300),
                FilteringTextInputFormatter.allow(
                    RegExp(ADD_SERVICE_HISTORY_NOTES)),
              ],
              onChanged: (text) {
                checkIfAllMandatoryFieldsAreEntered();
              },
              decoration: InputDecoration(
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    borderSide: BorderSide(color: _colorUtil.tertiary10),
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    borderSide: BorderSide(color: _colorUtil.tertiary10),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: _colorUtil.tertiary10),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    borderSide: BorderSide(color: _colorUtil.gradientOneStart),
                  ),
                  filled: true,
                  fillColor: _colorUtil.tertiary15),
            ),
          )
        ],
      ),
    );
  }

  Widget _saveChangesLayout() {
    return Container(
      margin: EdgeInsets.only(bottom: 24.h, top: 16.h),
      child: Align(
        alignment: FractionalOffset.bottomCenter,
        child: StreamBuilder<PrimaryButtonState>(
            stream: _bloc.saveButtonState,
            builder: (context, snapshot) {
              PrimaryButtonState _buttonState = snapshot.hasData
                  ? snapshot.data!
                  : PrimaryButtonState.INACTIVE;
              return Semantics(
                label: SAVE_BUTTON,
                child: CustomDefaultButton(
                  backgroundColor: _colorUtil.button01b,
                  buttonTextColor: _colorUtil.button01a,
                  primaryButtonState: _buttonState,
                  press: _buttonState == PrimaryButtonState.INACTIVE
                      ? () {}
                      : _saveServiceRecordCallBack,
                  text: OneAppString.of().save,
                  borderColor: _colorUtil.button01b,
                  horizontalPadding: 80.w,
                  verticalPadding: 16.h,
                ),
              );
            }),
      ),
    );
  }

  void checkIfAllMandatoryFieldsAreEntered() async {
    bool isNoErrorFound = true;
    if (serviceItemController.text.isNotEmpty &&
        (serviceProviderController.text.isNotEmpty) &&
        (odometerController.text.isNotEmpty) &&
        (serviceDateController.text.isNotEmpty)) {
      isNoErrorFound = true;
    } else {
      isNoErrorFound = false;
    }

    _bloc.updateButtonStatus(isNoErrorFound);
  }

  void _restoreSelectedDate() {
    if (widget.payload != null && widget.payload?.serviceDate != null) {
      if (serviceDateController.value.text == widget.payload!.serviceDate) {
        selectedDate = DateTime.parse(widget.payload!.serviceDate!);
      }
    }
  }

  void _saveServiceRecordCallBack() async {
    _restoreSelectedDate();
    if (_formKey.currentState!.validate()) {
      Global.getInstance().needAppointmentRefresh = true;
      List<ServiceItems> serviceItems = [];
      for (final String serviceItem in serviceItemController.text.split(',')) {
        serviceItems.add(ServiceItems(serviceOther: serviceItem.trim()));
      }

      ServiceRecordHelper requestBody = ServiceRecordHelper(
        serviceDate: getFormattedDateStartingFromYear(selectedDate),
        notes: notesController.text,
        odoMeter: odometerController.text,
        serviceProvider: serviceProviderController.text,
        serviceItems: serviceItems,
      );

      if (isAddServiceRecordFlow) {
        _bloc.saveChanges(
            requestBody, _redirectToServiceHistory, _showErrorToastMessage);
      } else {
        _bloc.updateServiceRecord(requestBody, widget.payload!.serviceHistoryId,
            _redirectToServiceHistory);
      }
    }
  }

  Widget _bottomSheetHeader(String title, {VoidCallback? onBackPressed}) {
    return Container(
      child: Column(
        children: [
          SwipeBarIcon(),
          Container(
            height: kToolbarHeight,
            child: Stack(
              fit: StackFit.loose,
              children: [
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.center,
                    child: Container(
                      margin: EdgeInsets.only(left: 16.w),
                      child: Center(
                        child: Text(formatTextForLexusAndToyota(title),
                            textAlign: TextAlign.center,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.subHeadline3,
                                _colorUtil.tertiary03)),
                      ),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: InkWell(
                      onTap: onBackPressed,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _colorUtil.tertiary12,
                          shape: BoxShape.circle,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(8.w),
                          child: Icon(
                            Icons.chevron_left,
                            color: _colorUtil.tertiary00,
                            semanticLabel: BACK_BUTTON,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _redirectToServiceHistory() {
    Future.delayed(const Duration(milliseconds: 300), () {
      Navigator.of(context).pop(true);
    });
  }

  /// This decides which day will be enabled
  /// This will be called every time while displaying day in calender.
  bool _decideWhichDayToEnable(DateTime day) {
    if (day.isBefore(DateTime.now())) {
      return true;
    }
    return false;
  }

  _selectDate(BuildContext context) {
    return buildMaterialDatePicker(context);
  }

  void buildMaterialDatePicker(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
      initialEntryMode: DatePickerEntryMode.calendar,
      initialDatePickerMode: DatePickerMode.day,
      selectableDayPredicate: _decideWhichDayToEnable,
      cancelText: OneAppString.of().commonCancel,
      confirmText: OneAppString.of().commonOK,
      builder: (context, child) {
        return Theme(
          data: ThemeData.light(),
          child: child!,
        );
      },
    );

    if (picked != null && picked != selectedDate) {
      selectedDate = picked;
      serviceDateController = TextEditingController(
          text: getFormattedDate(selectedDate.toString()));
      checkIfAllMandatoryFieldsAreEntered();
      _bloc.updateServiceDate(true);
    }
  }

  void _showErrorToastMessage(String message) {
    showCustomToast(_toastCustomWidget(message), 3);
  }

  Widget _toastCustomWidget(String message) {
    return CommonToast(
      iconColor: _colorUtil.tertiary15,
      avatarContainerEndColor: _colorUtil.primary01,
      avatarContainerStartColor: _colorUtil.primary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8,
      toastColor: _colorUtil.tertiary15,
      toastMessage: message,
    );
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  void _dialogHandlerCallback(String dialogText) {
    showBaseDialog(context, message: dialogText);
  }
}
