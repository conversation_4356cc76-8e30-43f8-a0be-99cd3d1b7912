// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/custom_divider.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/day_helper.dart';

// Project imports:
import 'service_item_bloc.dart';

class ServiceItemPage extends StatefulWidget {
  @override
  _ServiceItemPageState createState() => _ServiceItemPageState();
}

class _ServiceItemPageState extends State<ServiceItemPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;

  ServiceItemBloc _bloc = ServiceItemBloc();

  @override
  void initState() {
    _bloc.getServiceItemList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: OneAppScaffold(
        body: Container(
            color: _colorUtil.tertiary15,
            child: SafeArea(child: _serviceItemLayout())),
      ),
    );
  }

  void _popBack() {
    Navigator.of(context).pop();
  }

  Widget _serviceItemLayout() {
    return Container(
      margin: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 16.h),
      child: Column(
        children: [
          _bottomSheetHeader(OneAppString.of().serviceItem,
              onBackPressed: _popBack),
          SizedBox(height: 8.h),
          Expanded(
            child: StreamBuilder<List<DayHelper>?>(
                stream: _bloc.serviceItemList,
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return Container(
                        margin: EdgeInsets.only(top: 16.h),
                        child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: snapshot.data!.length,
                            itemBuilder: (BuildContext context, int index) {
                              return InkWell(
                                onTap: () {
                                  bool isSelected =
                                      snapshot.data![index].isSelected
                                          ? false
                                          : true;
                                  _bloc.updateServiceItemList(
                                      isSelected, index);
                                },
                                child: Container(
                                  margin:
                                      EdgeInsets.only(top: 8.h, bottom: 8.h),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          Text(snapshot.data![index].day!,
                                              style: TextStyleExtension()
                                                  .newStyleWithColor(
                                                      _textStyleUtil.body3,
                                                      _colorUtil.tertiary03)),
                                          Spacer(),
                                          Visibility(
                                            visible: snapshot
                                                .data![index].isSelected,
                                            child: SvgPicture.asset(
                                              tickIcon,
                                              height: 14.w,
                                              width: 14.w,
                                              colorFilter: ColorFilter.mode(
                                                _colorUtil.secondary01,
                                                BlendMode.srcIn,
                                              ),
                                              semanticsLabel: TICK_ICON,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Container(
                                          margin: EdgeInsets.only(
                                              top: 8.h, bottom: 4.h),
                                          child: CustomDivider(
                                              lineColor: _colorUtil.tertiary10))
                                    ],
                                  ),
                                ),
                              );
                            }));
                  } else {
                    return Container();
                  }
                }),
          ),
        ],
      ),
    );
  }

  Widget _bottomSheetHeader(String title, {VoidCallback? onBackPressed}) {
    return Container(
      child: Column(
        children: [
          SwipeBarIcon(),
          Container(
            height: kToolbarHeight,
            child: Stack(
              fit: StackFit.loose,
              children: [
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.center,
                    child: Container(
                      margin: EdgeInsets.only(right: 16.h),
                      child: Text(formatTextForLexusAndToyota(title),
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.subHeadline3,
                              _colorUtil.tertiary03)),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: InkWell(
                      onTap: onBackPressed,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _colorUtil.tertiary12,
                          shape: BoxShape.circle,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(8.h),
                          child: Icon(
                            Icons.chevron_left,
                            color: _colorUtil.tertiary00,
                            semanticLabel: BACK_BUTTON,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
