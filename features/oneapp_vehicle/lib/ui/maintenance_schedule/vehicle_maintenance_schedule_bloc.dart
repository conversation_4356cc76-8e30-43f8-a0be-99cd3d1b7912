// Dart imports:
import 'dart:async';

// Package imports:
import 'package:collection/collection.dart' show IterableExtension;
import 'package:get_it/get_it.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/maintenance_timeline_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_appointment_advisor_list_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_appointment_service_list_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/service_appointment_transportation_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_service_appointment_initialize_entity.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../local_repo/vehicle_repo.dart';
import '/log/vehicle_analytic_event.dart';

// Flutter imports:

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/dealer_info_entity.dart'
    as dealerInfo;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/maintenance_timeline_entity.dart'
    as maintenanceTimeline;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class VehicleMaintenanceScheduleBloc extends BlocBase {
  VehicleMaintenanceScheduleBloc({this.vin});
  final String? vin;

  OneAppClient api = APIClientConfig.oneAppClient;
  maintenanceTimeline.MaintenanceTimelinePayload? maintenanceDetailPayload;
  dealerInfo.DealerInfoPayload? preferredDealerInfo;
  late Function(bool) progressHandlerCallback;
  GetIt locator = GetIt.instance;
  String odometerValue = "0";
  vehicleInfo.Payload? vehicleItem;
  bool isBrandToyota = false;
  bool isLMEX = false;
  bool fromNative = false;

  final _scheduleMaintenanceList =
      BehaviorSubject<List<ScheduleMaintenanceDetails?>>();

  Stream<List<ScheduleMaintenanceDetails?>> get scheduleMaintenanceList =>
      _scheduleMaintenanceList.stream;

  final _lastKnownMileage = BehaviorSubject<String>();

  Stream<String> get lastKnownMileage => _lastKnownMileage.stream;

  final _lastKnownMileageUnit = BehaviorSubject<String>();

  Stream<String> get lastKnownMileageUnit => _lastKnownMileageUnit.stream;

  final _isScheduleDataAvailable = BehaviorSubject<bool>();

  Stream<bool> get isScheduleDataAvailable => _isScheduleDataAvailable.stream;

  Stream<bool> get isScheduleAppointmentAvailable =>
      _isScheduleAppointmentAvailable.stream;
  final _isScheduleAppointmentAvailable = BehaviorSubject<bool>();

  List<FootNotes> footNotes = [];

  String odometerValueData = "";
  String odometerUnitData = "";

  void init(
      String odometerValue,
      String odometerUnit,
      maintenanceTimeline.MaintenanceTimelinePayload? maintenancePayload,
      bool? fromNativeNav,
      {Function(bool)? progressHandler}) {
    maintenanceDetailPayload = maintenancePayload;
    fromNative = fromNativeNav ?? false;
    if (progressHandler != null) {
      progressHandlerCallback = progressHandler;
    }
    odometerValueData = odometerValue;
    odometerUnitData = odometerUnit;
    clearOldData();
    fetchMaintenanceTimeline();
    fetchPreferredServiceDealer();
  }

  Future<void> fetchMaintenanceTimeline() async {
    if (maintenanceDetailPayload == null) {
      progressHandlerCallback(true);
      String? vin = this.vin ?? Global.getInstance().vin;
      vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);

      if (isFeatureEnabled(TELEMETRY, vehicleItem?.features) &&
          isFeatureEnabled(MAINTENANCE_TIMELINE, vehicleItem!.features) &&
          odometerValueData.isNotEmpty &&
          odometerUnitData.isNotEmpty) {
        final result = await VehicleRepo()
            .fetchMaintenanceTimeline(odometerValueData, odometerUnitData);
        if (result != null) {
          maintenanceDetailPayload = result;
        }
      }
    }
    progressHandlerCallback(false);
    processMaintananceDetailPayload();
    _getOdometerValue();
  }

  void processMaintananceDetailPayload() {
    if (maintenanceDetailPayload != null &&
        maintenanceDetailPayload!.scheduleMaintenanceDetails != null &&
        maintenanceDetailPayload!.scheduleMaintenanceDetails!.isNotEmpty) {
      _isScheduleDataAvailable.sink.add(true);
      if (maintenanceDetailPayload!.lastKnownMileage != null) {
        String lastKnownValue =
            maintenanceDetailPayload!.lastKnownMileage!.round().toString();
        _lastKnownMileage.sink.add(convertThousandsWithComma(lastKnownValue));
        odometerValue = lastKnownValue;
        Global.getInstance().serviceMileage = odometerValue;
        if (maintenanceDetailPayload!.scheduleMaintenanceDetails!.isNotEmpty) {
          String? timeUnit = maintenanceDetailPayload!
              .scheduleMaintenanceDetails!
              .firstWhereOrNull((element) =>
                  element.interval?.toLowerCase() ==
                  OneAppString.of().currentText.toLowerCase())
              ?.mileageUnit;
          if (timeUnit != null) {
            _lastKnownMileageUnit.sink.add(timeUnit);
          } else {
            _lastKnownMileageUnit.sink
                .add(convertFirstLetterToCaps(OneAppString.of().miles));
          }
        }
      }
      _scheduleMaintenanceList.sink
          .add(maintenanceDetailPayload!.scheduleMaintenanceDetails!);
    } else {
      _isScheduleDataAvailable.sink.add(false);
    }

    if (maintenanceDetailPayload != null &&
        maintenanceDetailPayload?.footNotes != null &&
        maintenanceDetailPayload?.footNotes?.isNotEmpty == true) {
      footNotes = maintenanceDetailPayload!.footNotes!;
    }
  }

  void _getOdometerValue() async {
    String? currentVin = this.vin ?? Global.getInstance().vin;
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(currentVin);
    if (vehicleItem != null) {
      if (vehicleItem!.brand!.toLowerCase() == 't') {
        isBrandToyota = true;
      } else {
        isBrandToyota = false;
      }
    }
    String odometerRange = '';
    //If no maintenance status scheduleMaintenanceDetails array will have maintenanceTasks empty array.
    if (maintenanceDetailPayload != null &&
        maintenanceDetailPayload!.lastKnownMileage != null) {
      try {
        odometerRange = convertThousandsWithComma(
            maintenanceDetailPayload!.lastKnownMileage!.round().toString());
        if (maintenanceDetailPayload!.scheduleMaintenanceDetails != null &&
            maintenanceDetailPayload!.scheduleMaintenanceDetails!.isNotEmpty) {
          if (maintenanceDetailPayload!
                  .scheduleMaintenanceDetails!.first.timeUnit !=
              null) {
            _lastKnownMileageUnit.sink.add(maintenanceDetailPayload!
                .scheduleMaintenanceDetails!.first.mileageUnit!
                .toLowerCase());
          } else {
            _lastKnownMileageUnit.sink.add(OneAppString.of().miles);
          }
        }
      } catch (e) {
        FireBaseAnalyticsLogger.logError(e.toString(),
            category: LogCategory.FL_VEHI);
      }
    }
    _lastKnownMileage.sink.add(odometerRange.toLowerCase());
  }

  String getTitle(ScheduleMaintenanceDetails? maintenanceDetail) {
    String title = '';
    if (maintenanceDetail?.intervalMileage != null &&
        maintenanceDetail?.mileageUnit != null) {
      String? interval = maintenanceDetail?.intervalMileage?.replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},');
      String miles =
          maintenanceDetail!.mileageUnit!.toLowerCase().startsWith('m')
              ? 'mi'
              : maintenanceDetail.mileageUnit!;
      title = interval ?? '' + ' ' + miles;
    }
    return title.toLowerCase();
  }

  String getSubTitle(ScheduleMaintenanceDetails? maintenanceDetail) {
    String subTitle = '';
    if (maintenanceDetail?.serviceIntervalTime != null &&
        maintenanceDetail?.timeUnit != null) {
      subTitle = OneAppString.of().orText +
          ' ' +
          maintenanceDetail!.serviceIntervalTime! +
          ' ' +
          maintenanceDetail.timeUnit!;
    }
    return subTitle.toLowerCase();
  }

  String getInterval(ScheduleMaintenanceDetails? maintenanceDetail) {
    String interval = '';
    if (maintenanceDetail?.interval != null) {
      if (maintenanceDetail?.interval?.toLowerCase() ==
          OneAppString.of().previous.toLowerCase()) {
        interval = OneAppString.of().past;
      } else {
        interval = convertFirstLetterToCaps(
            maintenanceDetail!.interval!.toLowerCase());
      }
    }
    return interval;
  }

  // Fetch previous preferred dealer for the vin
  Future<void> fetchPreferredServiceDealer() async {
    final tmpVin = this.vin;
    String? vehicleVin = (tmpVin != null && tmpVin.isNotEmpty)
        ? tmpVin
        : Global.getInstance().vin;
    if (vehicleVin == null) return;
    vehicleInfo.Payload? vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(vehicleVin);
    if (vehicleItem?.region != null) {
      final commonResponse = await api.fetchPreferredDealer(
          vehicleItem!.region!, vehicleVin,
          vehicleBrand: vehicleItem.brand);
      final payLoad = commonResponse.response?.payload;
      if (payLoad != null && payLoad.isNotEmpty) {
        var item = payLoad.first;
        preferredDealerInfo = dealerInfo.DealerInfoPayload(
            dealerCode: item.dealerCode,
            dealerName: item.dealerName,
            regionCode: item.regionCode,
            tdaCode: item.tdaCode,
            proximityMiles: 0,
            address: item.address,
            city: item.city,
            state: item.state,
            country: item.country,
            zip: item.zip,
            latitude: item.latitude,
            longitude: item.longitude,
            dealerType: item.dealerType,
            phone: item.phone,
            distance: 0,
            distanceUnit: item.distanceUnit,
            webUrls: item.webUrls);

        locator.registerSingletonAsync(
          () async => preferredDealerInfo!,
          instanceName: SERVICE_DEALER,
        );

        fetchScheduleInitialization();

        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.VEHICLE_FETCH_SERVICEDEALER_SUCCESS,
            category: LogCategory.FL_VEHI);
      } else {
        _isScheduleAppointmentAvailable.sink.add(false);
      }
      if (commonResponse.error != null) {
        _isScheduleAppointmentAvailable.sink.add(false);

        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.VEHICLE_FETCH_SERVICEDEALER_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  // fetch Dealer Info Details - Schedule Initialization API
  Future<void> fetchScheduleInitialization() async {
    String? vin = this.vin ?? Global.getInstance().vin;
    if (vin != null &&
        isFeatureEnabled(DEALER_APPOINTMENTS, vehicleItem!.features)) {
      String dealerCode = preferredDealerInfo!.dealerCode!;
      final commonResponse = await api.fetchServiceAppointmentInitializeDetail(
          dealerCode, vin,
          vehicleBrand: vehicleItem?.brand);
      if (commonResponse.error == null && commonResponse.response != null) {
        processAppointmentInitialisationPayload(commonResponse.response!);
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.DEALER_SERVICE_APPOINTMENT_INITIALIZE_SUCCESS,
            category: LogCategory.FL_VEHI);
      }
      if (commonResponse.error != null) {
        _isScheduleAppointmentAvailable.sink.add(false);
        FireBaseAnalyticsLogger.logErrorAPI(
            VehicleAnalyticsEvent.DEALER_SERVICE_APPOINTMENT_INITIALIZE_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    } else {
      _isScheduleAppointmentAvailable.sink.add(false);
    }
  }

  void processAppointmentInitialisationPayload(
      VehicleServiceAppointmentInitializeEntity payload) {
    bool appointmentAvailable = payload.schedulingAvailable ?? false;
    locator.registerSingletonAsync(
      () async => payload.id!,
      instanceName: SERVICE_APPOINTMENT_ID,
    );
    _isScheduleAppointmentAvailable.sink.add(appointmentAvailable);
  }

  void clearOldData() {
    Global.getInstance().canAppointmentServiceExit = false;
    Advisors selectedAdvisorData = Advisors();
    Transportations selectedTransportationData = Transportations();
    DateTime selectedDate = DateTime.now();
    String selectedTime = "";
    TransportationLocationAddress pickUpAddress =
        TransportationLocationAddress();
    locator.registerSingletonAsync(
        // ignore: sdk_version_since
        () async => List<ServicesList>.empty(growable: true));
    locator.registerSingletonAsync(() async => selectedAdvisorData);
    locator.registerSingletonAsync(() async => selectedTransportationData);
    locator.registerSingletonAsync(() async => selectedDate);
    locator.registerSingletonAsync(() async => selectedTime,
        instanceName: TIME_SLOT_LOCATOR);
    locator.registerSingletonAsync(() async => pickUpAddress,
        instanceName: DELIVERY_ADDRESS);
    locator.registerSingletonAsync(() async => pickUpAddress,
        instanceName: PICK_UP_ADDRESS);
    locator.registerSingletonAsync(() async => false,
        instanceName: SAME_AS_PICKUP);
  }

  void openPhoneDialer(String? phoneNumber) async {
    if (phoneNumber != null && phoneNumber.isNotEmpty) {
      dialerLauncher(phoneNumber);
    }
  }

  @override
  void dispose() {
    _scheduleMaintenanceList.close();
    _lastKnownMileage.close();
    _lastKnownMileageUnit.close();
    _isScheduleDataAvailable.close();
    _isScheduleAppointmentAvailable.close();
  }
}
