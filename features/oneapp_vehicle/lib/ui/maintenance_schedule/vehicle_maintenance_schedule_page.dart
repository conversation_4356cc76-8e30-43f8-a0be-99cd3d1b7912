// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/maintenance_timeline_entity.dart';

// Project imports:
import '../../log/vehicle_marketing_event.dart';
import '../../ui/maintenance_schedule/update_mileage/update_mileage_page.dart';
import 'maintenance_schedule_detail/maintenance_schedule_detail_page.dart';
import 'vehicle_maintenance_schedule_bloc.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/maintenance_timeline_entity.dart'
    as maintenanceTimeline;

class VehicleMaintenanceSchedulePage extends StatefulWidget {
  final maintenanceTimeline.MaintenanceTimelinePayload?
      maintenanceDetailPayload;
  final bool? fromNative;
  final String? vin;
  final String odometerValue;
  final String odometerUnit;

  VehicleMaintenanceSchedulePage(
      {this.maintenanceDetailPayload,
      this.fromNative,
      this.vin,
      required this.odometerValue,
      required this.odometerUnit});

  @override
  _VehicleMaintenanceSchedulePageState createState() =>
      _VehicleMaintenanceSchedulePageState();
}

class _VehicleMaintenanceSchedulePageState
    extends State<VehicleMaintenanceSchedulePage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  late VehicleMaintenanceScheduleBloc _bloc;
  String? lastKnownMileage = '';

  bool isFirstCheck = true;
  bool appointmentAvailable = false;

  @override
  void initState() {
    super.initState();
    _bloc = VehicleMaintenanceScheduleBloc(vin: widget.vin);
    _bloc.init(widget.odometerValue, widget.odometerUnit,
        widget.maintenanceDetailPayload, widget.fromNative,
        progressHandler: _progressHandlerCallback);
    _bloc.isScheduleDataAvailable.listen((event) async {
      if (event == false && isFirstCheck) {
        await _progressHandlerCallback(false);
        Navigator.of(context)
            .push(
          MaterialPageRoute(
            builder: (context) => UpdateMileagePage(
              fromNative: widget.fromNative ?? false,
              lastUpdatedMileage:
                  lastKnownMileage!.replaceAll(OneAppString.of().miles, ''),
              isFromFirstCheck: true,
            ),
          ),
        )
            .then((value) {
          if (value != null &&
              value is maintenanceTimeline.MaintenanceTimelinePayload) {
            _bloc.init(widget.odometerValue, widget.odometerUnit, value,
                widget.fromNative);
          }
        });

        isFirstCheck = false;
      }
    });

    _bloc.isScheduleAppointmentAvailable.listen((event) {
      if (event) {
        appointmentAvailable = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.fromNative == true) {
      return BlocProvider(
        bloc: _bloc,
        child: OneAppScaffold(
          backgroundColor: _colorUtil.tertiary15,
          appBar: _appBarLayout() as PreferredSizeWidget?,
          containAppBarShadow: false,
          body: Container(
              color: _colorUtil.tertiary15,
              child: _displayMaintenanceScheduleList()),
        ),
      );
    } else {
      return Container(
        color: _colorUtil.tertiary15,
        child: SafeArea(
          child: Material(
            child: Scaffold(
              body: Container(
                  color: _colorUtil.tertiary15,
                  child: _displayMaintenanceScheduleList()),
            ),
          ),
        ),
      );
    }
  }

  // app bar with title
  Widget _appBarLayout() {
    return AppBar(
      backgroundColor: _colorUtil.tertiary15,
      centerTitle: true,
      elevation: 0,
      title: Text(
        formatTextForLexusAndToyota(OneAppString.of().maintenanceSchedule),
        style: TextStyleExtension().newStyleWithColor(
            _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
      ),
      leading: InkWell(
          onTap: _popBack,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: 48.w,
                width: 48.w,
                margin: EdgeInsets.only(left: 16.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: ThemeConfig.current().colorUtil.button02b,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.chevron_left,
                  color: ThemeConfig.current().colorUtil.button02a,
                  semanticLabel: BACK_BUTTON,
                ),
              ),
            ],
          )),
    );
  }

  Future<void> _popBack() async {
    if (widget.fromNative == true) {
      goBackToNative();
    } else {
      Navigator.of(context).maybePop();
    }
  }

  Widget _headerLayout() {
    if (widget.fromNative == true) {
      return Container(
        height: kToolbarHeight + 12,
      );
    } else {
      return _appBarLayout();
    }
  }

  Widget _displayMaintenanceScheduleList() {
    return Container(
      margin: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 16.h),
      child: StreamBuilder<bool>(
          stream: _bloc.isScheduleDataAvailable,
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data!) {
              return Column(
                children: [
                  _headerLayout(),
                  SizedBox(height: 30.h),
                  _odometerLayout(),
                  Expanded(
                    child: StreamBuilder<List<ScheduleMaintenanceDetails?>>(
                        stream: _bloc.scheduleMaintenanceList,
                        builder: (context, snapshot) {
                          if (!snapshot.hasData) {
                            return Container();
                          } else {
                            return Column(
                              children: [
                                Container(
                                  child: ListView.builder(
                                      shrinkWrap: true,
                                      itemCount: snapshot.data!.length,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        if (snapshot.data![index]!
                                                .intervalMileage !=
                                            null) {
                                          return Container(
                                              margin: EdgeInsets.all(8.w),
                                              child: InkWell(
                                                  onTap: () {
                                                    if (snapshot.data?[index] !=
                                                        null) {
                                                      Navigator.of(context)
                                                          .push(
                                                        MaterialPageRoute(
                                                          builder: (context) => MaintenanceScheduleDetailPage(
                                                              isScheduleAppointmentAvailable:
                                                                  appointmentAvailable,
                                                              preferredDealerPayload:
                                                                  _bloc
                                                                      .preferredDealerInfo,
                                                              maintenancePayLoad: snapshot
                                                                  .data?[index],
                                                              title: _bloc.getTitle(
                                                                  snapshot.data?[
                                                                      index]),
                                                              subTitle: _bloc
                                                                  .getSubTitle(
                                                                      snapshot.data?[
                                                                          index]),
                                                              footNotes: _bloc
                                                                  .footNotes,
                                                              fromNative: widget
                                                                  .fromNative),
                                                        ),
                                                      );
                                                    }
                                                  },
                                                  child: Container(
                                                    child: Card(
                                                      color: _colorUtil.tile02,
                                                      elevation: 0,
                                                      shape: RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius.all(
                                                                  Radius
                                                                      .circular(
                                                                          8.0))),
                                                      child: Padding(
                                                        padding: EdgeInsets.all(
                                                            16.w),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .start,
                                                          mainAxisSize:
                                                              MainAxisSize.max,
                                                          children: [
                                                            Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .max,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                    _bloc.getTitle(
                                                                        snapshot.data?[
                                                                            index]),
                                                                    maxLines: 1,
                                                                    overflow:
                                                                        TextOverflow
                                                                            .ellipsis,
                                                                    style: TextStyleExtension().newStyleWithColor(
                                                                        _textStyleUtil
                                                                            .body4,
                                                                        _colorUtil
                                                                            .tertiary03)),
                                                                Padding(
                                                                  padding:
                                                                      EdgeInsets
                                                                          .only(
                                                                              top: 4),
                                                                  child: Text(
                                                                      _bloc.getSubTitle(
                                                                          snapshot.data?[
                                                                              index]),
                                                                      maxLines:
                                                                          1,
                                                                      overflow:
                                                                          TextOverflow
                                                                              .ellipsis,
                                                                      style: TextStyleExtension().newStyleWithColor(
                                                                          _textStyleUtil
                                                                              .callout1,
                                                                          _colorUtil
                                                                              .tertiary05)),
                                                                ),
                                                              ],
                                                            ),
                                                            Spacer(),
                                                            Text(
                                                                _bloc.getInterval(
                                                                    snapshot.data?[
                                                                        index]),
                                                                style: TextStyleExtension().newStyleWithColor(
                                                                    _textStyleUtil
                                                                        .callout1,
                                                                    _colorUtil
                                                                        .tertiary05)),
                                                            SizedBox(
                                                                width: 8.0),
                                                            Container(
                                                              width: 18,
                                                              height: 12,
                                                              margin: EdgeInsets
                                                                  .only(
                                                                      top: 10.0,
                                                                      bottom:
                                                                          10.0),
                                                              decoration: BoxDecoration(
                                                                  color: snapshot.data![index]!.interval!.toLowerCase() == OneAppString.of().currentText.toLowerCase()
                                                                      ? _colorUtil.primary01
                                                                      : snapshot.data![index]!.interval?.toLowerCase() == OneAppString.of().next.toLowerCase()
                                                                          ? _colorUtil.tertiary05
                                                                          : _colorUtil.tertiary10,
                                                                  shape: BoxShape.circle),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  )));
                                        } else {
                                          FireBaseAnalyticsLogger.logMarketingEvent(
                                              VehicleMarketingEvent
                                                  .RECOMMENDED_SERVICES_UNAVAILABLE);
                                          return Container(
                                            child: Center(
                                                child: Column(
                                              children: [
                                                Text(
                                                    OneAppString.of()
                                                        .noMaintenanceTimeline,
                                                    style: TextStyleExtension()
                                                        .newStyleWithColor(
                                                            _textStyleUtil
                                                                .body4,
                                                            _colorUtil
                                                                .tertiary03)),
                                                SizedBox(height: 16.h),
                                                Text(
                                                    OneAppString.of()
                                                        .maintenanceTimelineNote(
                                                            _bloc.isBrandToyota
                                                                ? OneAppString
                                                                        .of()
                                                                    .toyotaName
                                                                : OneAppString
                                                                        .of()
                                                                    .lexusName),
                                                    textAlign: TextAlign.left,
                                                    style: TextStyleExtension()
                                                        .newStyleWithColor(
                                                            _textStyleUtil
                                                                .callout1,
                                                            _colorUtil
                                                                .tertiary05)),
                                              ],
                                            )),
                                          );
                                        }
                                      }),
                                ),
                              ],
                            );
                          }
                        }),
                  ),
                  _bottomConditionButton(),
                ],
              );
            } else {
              return isFirstCheck == false
                  ? Column(
                      children: [
                        _noScheduleDataLayout(),
                      ],
                    )
                  : Container();
            }
          }),
    );
  }

  Widget _noScheduleDataLayout() {
    return Expanded(
      child: Container(
        alignment: Alignment.center,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: _colorUtil.primary02,
                  shape: BoxShape.circle,
                ),
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: SvgPicture.asset(
                    calendarIcon,
                    height: 18.h,
                    width: 18.w,
                    colorFilter: ColorFilter.mode(
                      _colorUtil.primary01,
                      BlendMode.srcIn,
                    ),
                    semanticsLabel: CALENDAR_ICON,
                  ),
                ),
              ),
              SizedBox(height: 8.h),
              Text(OneAppString.of().noMaintenanceFound,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline1, _colorUtil.tertiary03)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _odometerLayout() {
    return Container(
      child: Stack(
        children: [
          InkWell(
            onTap: () {
              Navigator.of(context)
                  .push(
                MaterialPageRoute(
                  builder: (context) => UpdateMileagePage(
                    fromNative: widget.fromNative ?? false,
                    lastUpdatedMileage: lastKnownMileage?.replaceAll(
                        OneAppString.of().miles, ''),
                  ),
                ),
              )
                  .then((value) {
                if (value != null) {
                  _bloc.init(widget.odometerValue, widget.odometerUnit, value,
                      widget.fromNative);
                }
              });
            },
            child: Container(
              child: Padding(
                padding: EdgeInsets.all(30.w),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(bottom: 4.h),
                      child: Text(
                        OneAppString.of().odometer,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.caption2, _colorUtil.button03a),
                      ),
                    ),
                    StreamBuilder<String>(
                        stream: _bloc.lastKnownMileage,
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            if (snapshot.data != null &&
                                snapshot.data!.isNotEmpty) {
                              lastKnownMileage = snapshot.data;
                            }
                            return Text(lastKnownMileage!,
                                style: TextStyleExtension().newStyleWithColor(
                                    _textStyleUtil.subHeadline2,
                                    _colorUtil.button01a));
                          } else {
                            return Container();
                          }
                        }),
                    StreamBuilder<String>(
                        stream: _bloc.lastKnownMileageUnit,
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return Padding(
                              padding: EdgeInsets.only(top: 4.h),
                              child: Text(
                                snapshot.data!.toLowerCase(),
                                style: TextStyleExtension().newStyleWithColor(
                                    _textStyleUtil.caption2,
                                    _colorUtil.button03a),
                              ),
                            );
                          } else {
                            return Container();
                          }
                        }),
                  ],
                ),
              ),
              decoration: BoxDecoration(
                color: _colorUtil.secondary01,
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            top: 0,
            right: 16,
            child: InkWell(
              onTap: () {
                Navigator.of(context)
                    .push(
                  MaterialPageRoute(
                    builder: (context) => UpdateMileagePage(
                        fromNative: widget.fromNative ?? false,
                        lastUpdatedMileage: lastKnownMileage!
                            .replaceAll(OneAppString.of().miles, '')),
                  ),
                )
                    .then((value) {
                  if (value != null) {
                    _bloc.init(widget.odometerValue, widget.odometerUnit, value,
                        widget.fromNative);
                  }
                });
              },
              child: Container(
                decoration: BoxDecoration(
                  color: _colorUtil.tertiary15,
                  shape: BoxShape.circle,
                ),
                child: Padding(
                  padding: EdgeInsets.all(8.w),
                  child: SvgPicture.asset(
                    editIcon,
                    height: 12.w,
                    width: 12.w,
                    colorFilter: ColorFilter.mode(
                      _colorUtil.tertiary03,
                      BlendMode.srcIn,
                    ),
                    semanticsLabel: EDIT_ICON,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _bottomConditionButton() {
    return StreamBuilder<bool>(
        stream: _bloc.isScheduleAppointmentAvailable,
        builder: (context, isDealerAvailable) {
          if (isDealerAvailable.connectionState == ConnectionState.waiting) {
            return shimmerWithTwoTile();
          } else {
            return isDealerAvailable.hasData
                ? Container(
                    margin: EdgeInsets.only(top: 48.h),
                    child: CustomDefaultButton(
                      backgroundColor: _colorUtil.button01b,
                      buttonTextColor: _colorUtil.button01a,
                      text: isDealerAvailable.data!
                          ? OneAppString.of().makeAnAppointment
                          : OneAppString.of().call,
                      press: isDealerAvailable.data!
                          ? _handlePreferredDealerPress
                          : _handleCallButtonPress,
                      borderColor: _colorUtil.button01b,
                      horizontalPadding: 22.w,
                      verticalPadding: 6.h,
                    ))
                : Container();
          }
        });
  }

  Future<void> _progressHandlerCallback(bool show) async {
    if (show) {
      showProgress(context);
    } else {
      await dismissProgress(context);
    }
  }

  void _handleCallButtonPress() {
    if (_bloc.preferredDealerInfo != null) {
      _bloc.openPhoneDialer(_bloc.preferredDealerInfo!.phone);
    } else {
      NavigateService.pushNamedRoute(RoutePath.FX_SELECTPREFERDEALERPAGE,
          arguments: {
            "preferredDealerStatus":
                PreferredDealerStatus.EMPTY_PREFERRED_DEALER,
            "isFromNative": "false"
          });
    }
  }

  // if we already have an preferred dealer, we can make appointment else set preferred dealer
  void _handlePreferredDealerPress() {
    FireBaseAnalyticsLogger.logMarketingEvent(
        VehicleMarketingEvent.SCHEDULE_MAINTENANCE_CLICKED);
    FireBaseAnalyticsLogger.logMarketingGroupEvent(
        VehicleMarketingEvent.SERVICE_SCHEDULE,
        childEventName:
            VehicleMarketingEvent.APPOINTMENT_FROM_MAINTENANCESCHEDULE);
    _navigateToAppointmentPage();
  }

  _navigateToAppointmentPage() {
    _clearOldData();
    String? odometerValue = _bloc.odometerValue;
    if (odometerValue.isNotEmpty) {
      odometerValue = convertThousandsWithComma(odometerValue);
    } else {
      odometerValue = widget.odometerValue == "0" ? null : widget.odometerValue;
    }
    NavigateService.pushNamedRoute(
        RoutePath.VEHICLE_SERVICE_APPOINTMENT_ODOMETER_SETUP_PAGE,
        arguments: {
          "isInitialPage": true,
          "preferredDealerInfo": _bloc.preferredDealerInfo,
          "odometerValue": odometerValue,
          "comeFromConfirmPage": false
        });
  }

  void _clearOldData() {
    _bloc.clearOldData();
  }
}
