class FilterCardHelper {
  final bool arrowIconEnabled;
  String labelText;
  final bool isDateTimePickerEnabled;
  bool isSelected;
  int? initialMonth;
  int? initialYear;
  FilterCardHelper({
    this.arrowIconEnabled = false,
    this.isSelected = false,
    required this.labelText,
    this.isDateTimePickerEnabled = false,
    this.initialMonth = 0,
    this.initialYear = 0,
  });
}

final List<FilterCardHelper> filterHelper = [
  FilterCardHelper(
    labelText: "Month",
    arrowIconEnabled: true,
    isDateTimePickerEnabled: true,
  ),
  FilterCardHelper(labelText: "Home"),
  FilterCardHelper(labelText: "Public")
];
