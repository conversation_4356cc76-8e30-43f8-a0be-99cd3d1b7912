class ChargingSessions {
  String? chargeLocationType;
  DateTime? startTime;
  DateTime? endTime;
  String? socBeforeCharging;
  String? socAfterCharging;
  String? totalChargeKwhr;
  String? latitude;
  String? longitude;
  WatttimeDetails? watttimeDetails;
  CdrDetails? cdrDetails;

  ChargingSessions(
      {this.chargeLocationType,
      this.startTime,
      this.endTime,
      this.socBeforeCharging,
      this.socAfterCharging,
      this.totalChargeKwhr,
      this.latitude,
      this.longitude,
      this.watttimeDetails,
      this.cdrDetails});

  ChargingSessions.fromJson(Map<String, dynamic> json) {
    chargeLocationType = json['charge_location_type'];
    startTime = DateTime.parse(json["start_time"]);
    endTime = DateTime.parse(json["end_time"]);
    socBeforeCharging = json['soc_before_charging'];
    socAfterCharging = json['soc_after_charging'];
    totalChargeKwhr = json['total_charge_kwhr'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    watttimeDetails = json['watttime_details'] != null
        ? WatttimeDetails.fromJson(json['watttime_details'])
        : null;
    cdrDetails = json['cdr_details'] != null
        ? CdrDetails.fromJson(json['cdr_details'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['charge_location_type'] = this.chargeLocationType;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    data['soc_before_charging'] = this.socBeforeCharging;
    data['soc_after_charging'] = this.socAfterCharging;
    data['total_charge_kwhr'] = this.totalChargeKwhr;
    data['latitude'] = this.latitude;
    data['longitude'] = this.longitude;
    if (this.watttimeDetails != null) {
      data['watttime_details'] = this.watttimeDetails!.toJson();
    }
    if (this.cdrDetails != null) {
      data['cdr_details'] = this.cdrDetails!.toJson();
    }
    return data;
  }
}

class WatttimeDetails {
  String? chargeClasification;
  String? region;
  String? co2Value;
  String? healthDollarValue;

  WatttimeDetails(
      {this.chargeClasification,
      this.region,
      this.co2Value,
      this.healthDollarValue});

  WatttimeDetails.fromJson(Map<String, dynamic> json) {
    chargeClasification = json['charge_clasification'];
    region = json['region'];
    co2Value = json['co2_value'];
    healthDollarValue = json['health_dollar_value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['charge_clasification'] = this.chargeClasification;
    data['region'] = this.region;
    data['co2_value'] = this.co2Value;
    data['health_dollar_value'] = this.healthDollarValue;
    return data;
  }
}

class CdrDetails {
  String? cardLast4;
  String? partnerName;
  String? totalAmount;

  CdrDetails({this.cardLast4, this.partnerName, this.totalAmount});

  CdrDetails.fromJson(Map<String, dynamic> json) {
    cardLast4 = json['card_last4'];
    partnerName = json['partner_name'];
    totalAmount = json['total_amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['card_last4'] = this.cardLast4;
    data['partner_name'] = this.partnerName;
    data['total_amount'] = this.totalAmount;
    return data;
  }
}
