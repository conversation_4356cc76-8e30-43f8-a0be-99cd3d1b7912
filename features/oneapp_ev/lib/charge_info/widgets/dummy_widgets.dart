// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../core/widgets/tile_simple_widget.dart';

/// Create a card widget it will accept child as widget and optional background color. It will take card of border rounding and shadows and background of the widget

class DummySimpleTile extends StatelessWidget {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  DummySimpleTile({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EVSimpleTile(
      title: "Schedule",
      description:
          "Manage your vehicle’s ECO charging modes and set specific charging times. ",
      leftIcon: SvgPicture.asset(
        scheduleIcon,
        colorFilter: ColorFilter.mode(
          _colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        width: 24.w,
        height: 24.h,
      ),
      rightIcon: SvgPicture.asset(
        chevronRightIcon,
        colorFilter: ColorFilter.mode(
          _colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        width: 7.w,
        height: 12.h,
      ),
    );
  }
}
