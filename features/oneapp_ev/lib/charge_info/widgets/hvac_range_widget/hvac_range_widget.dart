// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';
import '../helper/range_info.dart';

class HVACRangeWidget extends ThemedStatelessWidget {
  final RangeInfo? rangeInfo;

  HVACRangeWidget({
    Key? key,
    this.rangeInfo,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _buildRangeLayout();
  }

  Widget _buildRangeLayout() {
    bool isDarkMode = isDarkTheme();
    final info = rangeInfo;
    if (info == null) {
      return Container();
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        rangeWidget(
            "${rangeInfo?.rangeWithAC.toInt()} ",
            rangeInfo?.unit,
            isDarkMode ? climateOnIconDarkMode : climateOnIcon,
            OneAppString.of().climateOnText),
        Container(
          margin: EdgeInsets.only(left: 15.w, right: 15.w),
          height: 80.h,
          width: 1.w,
          color: colorUtil.tertiary10,
        ),
        rangeWidget(
            "${rangeInfo?.range.toInt()} ",
            rangeInfo?.unit,
            isDarkMode ? climateOffIconDarkMode : climateOffIcon,
            OneAppString.of().climateOffText)
      ],
    );
  }

  Widget rangeWidget(
      String range, String? unit, String iconPath, String title) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            children: [
              TextSpan(
                text: range,
                style: TextStyleExtension().newStyleWithColor(
                  textStyleUtil.title2,
                  colorUtil.tertiary00,
                ),
              ),
              TextSpan(
                text: unit,
                style: TextStyleExtension().newStyleWithColor(
                  textStyleUtil.body3,
                  colorUtil.tertiary00,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyleExtension().newStyleWithColor(
                textStyleUtil.textLink1,
                colorUtil.tertiary05,
              ),
            ),
            SizedBox(width: 9.w),
            Image.asset(
              iconPath,
              width: getWidth(20),
              height: getHeight(20),
              fit: BoxFit.fill,
            )
          ],
        )
      ],
    );
  }
}
