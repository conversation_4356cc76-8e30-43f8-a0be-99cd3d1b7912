// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../core/widgets/themed_widget.dart';

class Tips {
  final String text;
  final String? highlightText;
  Tips({
    required this.text,
    required this.highlightText,
  });
}

class TipsWidget extends ThemedStatelessWidget {
  final Tips tips;

  TipsWidget({
    Key? key,
    required this.tips,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: colorUtil.tile05,
        ),
        child: ListTile(
          title: Row(children: [
            Container(
              height: 32.h,
              width: 32.w,
              child: Center(
                child: SvgPicture.asset(
                  tipIcon,
                  colorFilter: ColorFilter.mode(
                    colorUtil.tertiary03,
                    BlendMode.srcIn,
                  ),
                  allowDrawingOutsideViewBox: false,
                ),
              ),
            ),
            SizedBox(width: 16.w),
            Flexible(child: Text.rich(this.buildFormattedText()))
          ]),
        ),
      ),
    );
  }

  Map<String, TextStyle>? get _patternMap {
    final hightLightText = tips.highlightText;
    if (hightLightText != null) {
      return {
        hightLightText: textStyleUtil.footNote1.copyWith(
          color: colorUtil.tertiary03,
          fontWeight: FontWeight.w600,
        ),
      };
    }
    return null;
  }

  TextSpan buildFormattedText() {
    final children = <TextSpan>[];
    Map<String, TextStyle>? map = _patternMap;
    if (map != null) {
      RegExp allRegex =
          RegExp(map.keys.map((e) => RegExp(e).pattern).join('|'));
      this.tips.text.splitMapJoin(
        allRegex,
        onMatch: (match) {
          final key = map.entries
              .singleWhere((element) =>
                  element.key.allMatches(match[0] ?? '').isNotEmpty)
              .key;
          children.add(TextSpan(text: match[0], style: map[key]));
          return '';
        },
        onNonMatch: (span) {
          children.add(TextSpan(
              text: span,
              style: textStyleUtil.footNote1
                  .copyWith(color: colorUtil.tertiary03)));
          return '';
        },
      );
    } else {
      children.add(TextSpan(
          text: this.tips.text,
          style:
              textStyleUtil.footNote1.copyWith(color: colorUtil.tertiary03)));
    }
    return TextSpan(
        style: textStyleUtil.footNote1.copyWith(color: colorUtil.tertiary03),
        children: children);
  }
}
