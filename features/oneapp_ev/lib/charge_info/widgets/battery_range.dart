// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../../core/shimmering/widgets/shimmering_range_widget.dart';
import '../../core/widgets/themed_widget.dart';
import 'helper/range_info.dart';
import 'hvac_range_widget/hvac_range_widget.dart';

class BatteryRangeWidget extends StatelessWidget {
  final RangeInfo rangeInfo;
  final bool isLoading;
  final bool isEV;
  BatteryRangeWidget({
    Key? key,
    required this.rangeInfo,
    required this.isLoading,
    required this.isEV,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? ShimmeringRangeWidget()
        : RangeWidget(
            rangeInfo: rangeInfo,
            isEV: isEV,
          );
  }
}

class RangeWidget extends ThemedStatelessWidget {
  final RangeInfo rangeInfo;
  final bool isEV;
  RangeWidget({
    Key? key,
    required this.rangeInfo,
    required this.isEV,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: isEV
          ?
          //HVAC ON/OFF
          HVACRangeWidget(rangeInfo: rangeInfo)
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "${rangeInfo.range.toInt()} ",
                        style: TextStyleExtension().newStyleWithColor(
                          textStyleUtil.title2,
                          colorUtil.tertiary00,
                        ),
                      ),
                      TextSpan(
                        text: rangeInfo.unit,
                        style: TextStyleExtension().newStyleWithColor(
                          textStyleUtil.body3,
                          colorUtil.tertiary00,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  rangeInfo.chargeDescription,
                  style: TextStyleExtension().newStyleWithColor(
                    textStyleUtil.footNote1,
                    colorUtil.tertiary05,
                  ),
                )
              ],
            ),
    );
  }
}
