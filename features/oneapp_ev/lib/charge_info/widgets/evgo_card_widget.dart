// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/dialog/bottom_error_conformation_dialog.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/ev_common/ev_enum_const_util.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/vehicle_search_charge_station_location/LegalTermsAndCondition/ev_Legal_Terms_and_Condition_page.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/vehicle_search_charge_station_location/ev_driver_account_enrollment/ev_driver_account_enrollment_page.dart';
import 'package:vehicle_module/ui/vehicle_account/vehicle_account_page.dart';

// Project imports:
import '../../core/widgets/themed_widget.dart';
import '../../core/widgets/tile_simple_widget.dart';
import '../blocs/partner_details_Info_bloc.dart';

class EvgoCardWidget extends ThemedStatelessWidget {
  final String subTitle;
  final bool loadingShimmer;
  final bool isRegistered;
  late final BuildContext? _buildContext;
  final PartnerDetailsInfoBloc? partnerBloc;
  EvgoCardWidget({
    Key? key,
    this.subTitle = '',
    this.loadingShimmer = false,
    required this.isRegistered,
    this.partnerBloc,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    _buildContext = context;
    return EVSimpleTile(
      onPress: () => {
        // Evgo Tile click event
      },
      loadingShimmer: loadingShimmer,
      leftIcon: SvgPicture.asset(
        chargeStationIcon,
        colorFilter: ColorFilter.mode(
          colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        height: 32.h,
        width: 32.w,
        allowDrawingOutsideViewBox: false,
      ),
      rightIcon: this.isRegistered
          ? SizedBox(width: 0.w, height: 0.h)
          : GestureDetector(
              onTap: () {
                FireBaseAnalyticsLogger.logMarketingGroupEvent(
                  VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                  childEventName:
                      VehicleAnalyticsEvent.VEHICLE_EV_EVGO_REGISTER,
                );
                _showAcceptTermsAndConditionScreen(
                    OneAppString.of().evgoHeadingText);
              },
              child: SizedBox(
                  height: 36.h,
                  width: 88.w,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100.w),
                      border: Border.all(
                        color: colorUtil.tertiary10,
                        width: 2.w,
                      ),
                    ),
                    child: Row(
                      children: [
                        Padding(
                            padding: EdgeInsets.only(
                          left: 16.w,
                          top: 8.h,
                          bottom: 8.h,
                        )),
                        Text(
                          OneAppString.of().loginRegister,
                          style: TextStyleExtension().newStyleWithColor(
                              textStyleUtil.callout2, colorUtil.button02a),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )),
            ),
      title: OneAppString.of().evgoHeadingText,
      description: this.subTitle,
    );
  }

  void onRegisterFunction(String? partnerName) {
    showModalBottomSheet(
      context: _buildContext!,
      isDismissible: true,
      useRootNavigator: true,
      barrierColor: Colors.black.withOpacity(0.5),
      backgroundColor: colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
          child: Container(
        height: ScreenUtil().screenHeight * BOTTOM_SHEET_PERCENT_60,
        child: OneAppScaffold(
          resizeToAvoidBottomInset: true,
          body: Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: EVDriverAccountEnrollmentPage(
              continueCallback: continueCallBack,
              partnerName: partnerName,
              screenAction: screenActionCallBack,
            ),
          ),
        ),
      )),
    ).then((_) => this.partnerBloc?.fetchPartnerInfo());
  }

  _showAcceptTermsAndConditionScreen<bool>(
    String partnerName,
  ) {
    return showMaterialModalBottomSheet(
      expand: true,
      context: _buildContext!,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: colorUtil.tertiary15,
      builder: (context) => EvNewTermsAndConditionPage(
        acceptTermsCallBackFunction: acceptTermsCallBackFunction,
        partnerName: partnerName,
      ),
    );
  }

  void acceptTermsCallBackFunction({String? partnerName}) {
    Future.delayed(Duration(milliseconds: 100), () {
      onRegisterFunction(partnerName);
    });
  }

  void continueCallBack() {
    VehicleRepo().fetchIsEvPublicChargingEnabled().then(
          (isPublicChargingEnabled) => NavigateService.pushNamedRoute(
            RoutePath.VEHICLE_SEARCH_CHARGE_STATION_LOCATION,
            arguments: isPublicChargingEnabled,
          ).then(
            (_) => partnerBloc!.fetchPartnerInfo(),
          ),
        );
  }

  void screenActionCallBack(EV_SCREENS val, {String? errorMessage}) {
    if (val == EV_SCREENS.UPDATE_PROFILE) {
      BottomErrorConfirmationDialog().showBottomDialog(
          _buildContext!,
          chargeAlertIcon,
          OneAppString.of().updateProfileText,
          OneAppString.of().updateProfileErrorMessage(errorMessage!),
          OneAppString.of().updateProfileButtonText,
          OneAppString.of().cancel,
          _updateProfileClickListener);
    }
  }

  // redirect to contact support
  void _updateProfileClickListener() {
    _showVehicleAccountPage();
  }

  _showVehicleAccountPage<bool>() {
    return showMaterialModalBottomSheet(
      expand: true,
      context: _buildContext!,
      isDismissible: true,
      useRootNavigator: true,
      backgroundColor: colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(child: VehicleAccountPage()),
          ],
        ),
      ),
    );
  }
}
