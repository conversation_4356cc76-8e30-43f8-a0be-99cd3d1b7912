// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';

class Accordion extends StatefulWidget {
  final String title;
  final String content;
  final String hyperLinkText;
  final String hyperLinkURL;
  final bool showContent;
  final Widget? htmlContent;
  final bool isHtmlContent;
  final TextStyle? textStyle;
  final ColorUtil colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil textStyleUtil = ThemeConfig.current().textStyleUtil;

  Accordion({
    Key? key,
    required this.title,
    required this.content,
    this.hyperLinkText = "",
    this.hyperLinkURL = "",
    this.showContent = true,
    this.isHtmlContent = false,
    this.htmlContent,
    this.textStyle,
  }) : super(key: key);
  @override
  _AccordionState createState() => _AccordionState();
}

class _AccordionState extends State<Accordion> {
  bool _showContent = true;
  @override
  void initState() {
    _showContent = widget.showContent;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      ListTile(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
        tileColor: widget.colorUtil.tile05,
        title: Text(
          widget.title,
          style: TextStyleExtension().newStyleWithColor(
            widget.textStyleUtil.body4,
            widget.colorUtil.tertiary03,
          ),
        ),
        trailing: IconButton(
          icon: Icon(_showContent ? Icons.remove : Icons.add),
          onPressed: () {
            setState(() {
              _showContent = !_showContent;
            });
          },
        ),
      ),
      Visibility(
        visible: _showContent,
        child: Container(
          padding: widget.isHtmlContent
              ? EdgeInsets.only(left: 10.w, right: 10.w, top: 0.h)
              : EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
          child: widget.isHtmlContent
              ? widget.htmlContent
              : Text.rich(TextSpan(
                  style: TextStyle(
                    fontSize: 27,
                  ),
                  children: [
                      for (final word in widget.content.split(" "))
                        word == widget.hyperLinkText
                            ? TextSpan(
                                style: TextStyleExtension().newStyleWithColor(
                                  widget.textStyleUtil.callout3,
                                  widget.colorUtil.tertiary03,
                                ),
                                text: "$word ",
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () async {
                                    urlLauncher(widget.hyperLinkURL);
                                  })
                            : TextSpan(
                                text: "$word ",
                                style: TextStyleExtension().newStyleWithColor(
                                  widget.textStyleUtil.callout1,
                                  widget.colorUtil.tertiary03,
                                ),
                              ),
                    ])),
        ),
      )
    ]);
  }
}
