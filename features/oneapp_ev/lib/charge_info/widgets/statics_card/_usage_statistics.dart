// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

class StatisticsBannerWidget extends StatelessWidget {
  StatisticsBannerWidget({Key? key}) : super(key: key);

  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Card(
            color: _colorUtil.tertiary12,
            child: ListTile(
              title: Text(
                '52.3 MPGE',
                style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout2,
                  _colorUtil.tertiary03,
                ),
              ),
              subtitle: Text(
                'AVG CONSUMPTION',
                style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.footNote1,
                  _colorUtil.tertiary05,
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: Card(
            color: _colorUtil.tertiary12,
            child: ListTile(
              title: RichText(
                text: TextSpan(
                    text: '52.3',
                    style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.tabLabel02,
                      _colorUtil.tertiary03,
                    ),
                    children: [
                      TextSpan(
                          text: 'AVG CONSUMPTION',
                          style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.tabLabel01,
                            _colorUtil.tertiary03,
                          ))
                    ]),
              ),
              subtitle: Text(
                'SPENT IN EV MODE',
                style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.footNote1,
                  _colorUtil.tertiary05,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }
}
