// Flutter imports:
import 'package:flutter/cupertino.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';

// Project imports:
import 'yearmonth_picker_bloc.dart';

class YearMonthPicker extends StatefulWidget {
  final Function? onPress;
  final int? initialMonth;
  final int? initialYear;

  YearMonthPicker({
    Key? key,
    this.onPress,
    this.initialMonth,
    this.initialYear,
  }) : super(key: key);
  @override
  _YearMonthPickerState createState() => _YearMonthPickerState();
}

class _YearMonthPickerState extends State<YearMonthPicker> {
  YearMonthPickerBloc _bloc = YearMonthPickerBloc();
  final ColorUtil colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil textStyleUtil = ThemeConfig.current().textStyleUtil;
  double itemExtent = 32;
  FixedExtentScrollController? monthScrollController;
  FixedExtentScrollController? yearScrollController;

  @override
  void initState() {
    _bloc.init(
      initialMonth: widget.initialMonth ?? 0,
      initialYear: widget.initialYear ?? 0,
    );
    monthScrollController = FixedExtentScrollController(
        initialItem: int.parse(_bloc.selectedMonth) - 1);
    yearScrollController = FixedExtentScrollController(
        initialItem: _bloc.yearsArray.indexOf(_bloc.selectedYear));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _buildCupertinoPicker();
  }

  Widget _buildCupertinoPicker() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: StreamBuilder<List<String>?>(
                  stream: _bloc.monthList,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return CupertinoPicker(
                        scrollController: monthScrollController,
                        itemExtent: 32,
                        onSelectedItemChanged: (int index) {
                          _bloc.monthChanged(index);
                        },
                        children: List<Widget>.generate(
                          snapshot.data!.length,
                          (int index) {
                            return Center(
                              child: Text(
                                '${snapshot.data![index]}',
                                style: TextStyleExtension().newStyleWithColor(
                                    textStyleUtil.subHeadline1,
                                    colorUtil.tertiary05),
                              ),
                            );
                          },
                        ),
                      );
                    } else {
                      return Container();
                    }
                  },
                ),
              ),
              Expanded(
                child: StreamBuilder<List<String>?>(
                  stream: _bloc.yearList,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return CupertinoPicker(
                        scrollController: yearScrollController,
                        itemExtent: 32,
                        onSelectedItemChanged: (int index) {
                          _bloc.yearChanged(index);
                        },
                        children: List<Widget>.generate(
                          snapshot.data!.length,
                          (int index) {
                            return Center(
                              child: Text(
                                '${snapshot.data![index]}',
                                style: TextStyleExtension().newStyleWithColor(
                                    textStyleUtil.subHeadline1,
                                    colorUtil.tertiary05),
                              ),
                            );
                          },
                        ),
                      );
                    } else {
                      return Container();
                    }
                  },
                ),
              ),
            ],
          ),
        ),
        Container(
          child: CustomDefaultButton(
            backgroundColor: colorUtil.button01b,
            buttonTextColor: colorUtil.button01a,
            text: OneAppString.of().commonOkay,
            press: () {
              if (widget.onPress != null) {
                MonthYearModel selectedMonthYear = MonthYearModel(
                    month: _bloc.selectedMonth, year: _bloc.selectedYear);
                widget.onPress!(selectedMonthYear);
              }
              Navigator.pop(context);
            },
            borderColor: colorUtil.button01b,
            horizontalPadding: 22.w,
            verticalPadding: 8.h,
          ),
        ),
      ],
    );
  }
}
