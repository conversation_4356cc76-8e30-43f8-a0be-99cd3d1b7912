// Package imports:
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../../../core/constants.dart';

class YearMonthPickerBloc extends BlocBase {
  static List<String> _years = [];

  YearMonthPickerBloc() {
    _prepareYearArray();
  }
  List<String> get yearsArray => _years;

  static List<String> _prepareYearArray() {
    _years = [];
    //WattTime Go-Live year = 2022
    for (int current = 2022; current <= DateTime.now().year; current++) {
      _years.add(current.toString());
    }
    return _years;
  }

  Stream<List<String>?> get monthList => _monthList.stream;
  final _monthList = BehaviorSubject<List<String>?>();

  Stream<List<String>?> get yearList => _yearList.stream;
  final _yearList = BehaviorSubject<List<String>?>();

  String selectedMonth = '';
  String selectedYear = '';
  DateTime dateToday = DateTime.now();

  void init({int initialMonth = 0, int initialYear = 0}) {
    selectedMonth = initialMonth > 0
        ? initialMonth.toString()
        : dateToday.month.toString(); //10
    selectedYear = initialYear > 0
        ? initialYear.toString()
        : dateToday.year.toString(); //2022
    _yearList.sink.add(yearsArray);
    yearChanged(yearsArray.indexOf(dateToday.year.toString()));
  }

  void monthChanged(int index) {
    selectedMonth = '${index + 1}'; //10
  }

  void yearChanged(int index) {
    selectedYear = yearsArray[index]; //2022
    var dt = DateTime.now();
    if (yearsArray[index] == dt.year.toString()) {
      List<String> filteredMonths = monthsArray
          .asMap()
          .entries
          .where((e) => e.key <= dt.month - 1)
          .map((e) => e.value)
          .toList();
      if ((int.parse(selectedMonth)) > filteredMonths.length) {
        selectedMonth = dateToday.month.toString();
      }
      _monthList.sink.add(filteredMonths);
    } else {
      _monthList.sink.add(monthsArray);
    }
  }

  @override
  void dispose() {
    _monthList.close();
    _yearList.close();
  }
}

class MonthYearModel {
  MonthYearModel({
    required this.month,
    required this.year,
  });

  final String month;
  final String year;
}
