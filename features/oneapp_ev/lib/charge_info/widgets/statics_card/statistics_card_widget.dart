// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';
import '../../../core/widgets/tile_expanded_widget.dart';
import '../../../log/ev_analytics_events.dart';
import '../../blocs/charge_statistics/charging_statistics_bloc.dart';
import '../../help_me_improve_page.dart';
import '../../statistics_data_list_cards_page.dart';
import '../tips_widget.dart';
import '_bar_chart.dart';
import '_eco_statistics.dart';

class StatisticsCardInfo {
  final int achievedLeaves;
  final List<BarData>? bars;
  final ChargeStatisticsBloc bloc;
  final Tips? tips;
  // final Widget Function(BuildContext) onTabCallback;

  StatisticsCardInfo({
    required this.achievedLeaves,
    required this.bars,
    required this.tips,
    required this.bloc,
    // required this.onTabCallback,
  });
}

class StatisticsCard extends ThemedStatelessWidget {
  final StatisticsCardInfo? info;
  final bool isLoading;

  StatisticsCard({
    Key? key,
    required this.info,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        EVExpandedTile(
          leftIcon: SvgPicture.asset(
            cleanAssistSvgIcon,
            colorFilter: ColorFilter.mode(
              colorUtil.tertiary00,
              BlendMode.srcIn,
            ),
            width: 24.w,
            height: 24.h,
          ),
          rightIcon: SvgPicture.asset(
            rightArrowIcon,
            colorFilter: ColorFilter.mode(
              colorUtil.tertiary00,
              BlendMode.srcIn,
            ),
            width: 24.w,
            height: 24.h,
          ),
          title: OneAppString.of().statistics,
          cardChild: getContent(context),
          loadingShimmer: isLoading,
          onPress: () {
            FireBaseAnalyticsLogger.logMarketingGroupEvent(
                VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                childEventName: EVAnalyticsEvent.STATISTICS_TILE);
            final _info = info;
            if (_info == null) {
              return Container();
            }
            return showChargingStatisticList(context, _info.bloc);
          },
        ),
      ],
    );
  }

  Widget statisticsInitialWidget(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 16.0.h, right: 16.0.h, bottom: 16.0.h),
          child: TipsWidget(
            tips:
                Tips(text: OneAppString.of().statisticsInfo, highlightText: ''),
          ),
        ),
        getLearnMoreWidget(context),
      ],
    );
  }

  Widget getContent(BuildContext context) {
    final _info = info;
    if (_info == null) {
      return statisticsInitialWidget(context);
    }
    bool isAnyNonZeroValue =
        _info.bars?.any((element) => element.value > 0) ?? false;
    final displayBarChart = _info.bars != null && isAnyNonZeroValue;

    return InkWell(
      onTap: () => showChargingStatisticList(context, _info.bloc),
      child: Column(
        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          EcoStatistics(achievedLeaves: _info.achievedLeaves),
          if (displayBarChart) StatisticsBarChart(bars: info!.bars!),
          if (_info.tips != null)
            Padding(
              padding: EdgeInsets.only(bottom: 16.0.h),
              child: TipsWidget(
                tips: _info.tips!,
              ),
            ),
          getLearnMoreWidget(context),
        ],
      ),
    );
  }

  Future<Widget?> showChargingStatisticList<bool>(
      BuildContext context, ChargeStatisticsBloc bloc) {
    FireBaseAnalyticsLogger.logMarketingGroupEvent(
      VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
      childEventName:
          VehicleAnalyticsEvent.VEHICLE_EV_CHARGE_INFO_STATS_CARD_TAP,
    );
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary12,
      builder: (context) => StatisticsDataListCardsPage(bloc: bloc),
    );
  }

  Widget getLearnMoreWidget(BuildContext context) {
    return InkWell(
      onTap: () {
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
            VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
            childEventName: EVAnalyticsEvent.LEARN_MORE);
        showMaterialModalBottomSheet(
          expand: true,
          isDismissible: true,
          useRootNavigator: true,
          context: context,
          clipBehavior: Clip.antiAliasWithSaveLayer,
          backgroundColor: colorUtil.tertiary15,
          builder: (parentContext) => HelpMeImprove(
            showSwipeBar: true,
            hideHealthImpact: false,
          ),
        );
      },
      child: Padding(
        padding: EdgeInsets.only(left: 16.0.h, right: 16.0.h, bottom: 16.0.h),
        child: SizedBox(
            height: 36.h,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100.w),
                border: Border.all(
                  color: colorUtil.tertiary10,
                  width: 2.w,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    OneAppString.of().learnMore,
                    style: TextStyleExtension().newStyleWithColor(
                        textStyleUtil.callout2, colorUtil.button02a),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )),
      ),
    );
  }
}
