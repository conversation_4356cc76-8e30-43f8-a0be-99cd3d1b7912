// Dart imports:
import 'dart:math';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:d_chart/d_chart.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';

// Project imports:
import '../../blocs/charge_statistics/_statistics_utility.dart';

class StatisticsBarChart extends StatelessWidget {
  final List<BarData> bars;
  late final BarData minBarData;
  late final BarData maxBarData;
  late final double offset;

  StatisticsBarChart({
    Key? key,
    required this.bars,
  }) {
    minBarData = StatisticsUtility.getNonZeroBarData(bars).reduce(
        (prevElement, nextElement) =>
            prevElement.value < nextElement.value ? prevElement : nextElement);
    maxBarData = StatisticsUtility.getNonZeroBarData(bars).reduce(
        (prevElement, nextElement) =>
            prevElement.value > nextElement.value ? prevElement : nextElement);
    offset = max(1, minBarData.value * .1);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80.h,
      child: Transform.translate(
        offset: Offset(0, 0),
        child: DChartBar(
          measureMin: offset.toInt(),
          measureMax: maxBarData.value.toInt(),
          minimumPaddingBetweenLabel: 10,
          domainLabelPaddingToAxisLine: 8,
          data: [
            {
              'id': 'chart_id',
              'data': bars
                  .map((e) => {'domain': e.label, 'measure': e.value + offset})
                  .toList()
            }
          ],
          axisLinePointWidth: 0,
          domainLabelFontSize: 9,
          measureLabelFontSize: 0,
          domainLabelColor: ThemeConfig.current().colorUtil.tertiary05,
          barColor: (barData, index, id) => bars[index ?? 0].highlight
              ? ThemeConfig.current().colorUtil.button03b
              : (isDarkTheme()
                  ? ThemeConfig.current().colorUtil.button05a
                  : ThemeConfig.current().colorUtil.button02c),
          showDomainLine: false,
          showMeasureLine: false,
          showBarValue: false,
        ),
      ),
    );
  }
}

class BarData {
  final double value;
  final String label;
  final bool highlight;

  BarData({
    required this.value,
    required this.label,
    required this.highlight,
  });
}
