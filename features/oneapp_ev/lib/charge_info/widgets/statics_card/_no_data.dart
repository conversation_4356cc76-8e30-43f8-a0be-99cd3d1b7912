// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';
import '../../blocs/charge_statistics/charging_statistics_bloc.dart';

class StatisticsNoData extends ThemedStatelessWidget {
  StatisticsNoData({Key? key, required this.bloc}) : super(key: key);

  final ChargeStatisticsBloc bloc;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: SizedBox(
        height: MediaQuery.of(context).size.height * .6,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _svgImage,
            _noDataText,
            _firstTimeText,
          ],
        ),
      ),
    );
  }

  Widget get _firstTimeText {
    return Text(
      OneAppString.of().statisticsAvailability,
      textAlign: TextAlign.center,
      style: TextStyleExtension().newStyleWithColor(
          textStyleUtil.body1.copyWith(height: 1.5.h), colorUtil.tertiary05),
    );
  }

  Widget get _noDataText {
    return Text(
      OneAppString.of().statisticsNoData,
      style: TextStyleExtension()
          .newStyleWithColor(textStyleUtil.subHeadline1, colorUtil.tertiary03),
    );
  }

  Align get _svgImage {
    return Align(
      alignment: Alignment.center,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: colorUtil.secondary02,
        ),
        child: Padding(
          padding: EdgeInsets.all(8.0.w),
          child: SvgPicture.asset(
            cleanAssistSvgIcon,
            colorFilter: ColorFilter.mode(
              colorUtil.tertiary03,
              BlendMode.srcIn,
            ),
            width: 28.w,
            height: 28.w,
          ),
        ),
      ),
    );
  }

  InkWell getPageHeader(BuildContext context) {
    return InkWell(
      onTap: () => Navigator.pop(context),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 16.0.w),
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: colorUtil.tertiary12,
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: SvgPicture.asset(
                  arrowLeftIcon,
                  colorFilter: ColorFilter.mode(
                    colorUtil.button02a,
                    BlendMode.srcIn,
                  ),
                  width: 24.w,
                  height: 24.h,
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 64.0),
            child: Text(
              OneAppString.of().statistics,
              style: textStyleUtil.subHeadline3,
            ),
          ),
        ],
      ),
    );
  }
}
