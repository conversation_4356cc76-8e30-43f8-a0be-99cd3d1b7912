// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';

class StatisticsPageListTileCard extends ThemedStatelessWidget {
  StatisticsPageListTileCard({
    Key? key,
    required this.leftIcon,
    required this.mainHeading,
    required this.subHeading1,
    required this.leftIconBgColor,
    required this.leftIconColor,
    this.leftIconPadding,
    this.iconHeight,
    this.iconWidth,
    this.subHeading2,
  }) : super(key: key);

  final String leftIcon;
  final double? iconHeight;
  final double? leftIconPadding;
  final double? iconWidth;
  final String mainHeading;
  final String subHeading1;
  final String? subHeading2;
  final Color leftIconBgColor;
  final Color leftIconColor;

  @override
  Widget build(BuildContext context) {
    return Card(
      color: colorUtil.tile02,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(8.r)),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              decoration:
                  BoxDecoration(shape: BoxShape.circle, color: leftIconBgColor),
              child: Padding(
                padding: EdgeInsets.all(leftIconPadding ?? 16.r),
                child: SvgPicture.asset(
                  leftIcon,
                  colorFilter: ColorFilter.mode(
                    leftIconColor,
                    BlendMode.srcIn,
                  ),
                  width: iconWidth?.w ?? 30.w,
                  height: iconHeight?.h ?? 20.h,
                ),
              ),
            ),
            SizedBox(
              width: 8.0.w,
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 8.h, bottom: 4.h),
                    child: Text(
                      mainHeading,
                      style: TextStyleExtension().newStyleWithColor(
                        textStyleUtil.subHeadline3,
                        colorUtil.tertiary03,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  if (subHeading2 == null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Text(
                        subHeading1,
                        style: TextStyleExtension().newStyleWithColor(
                          textStyleUtil.callout1,
                          colorUtil.tertiary05,
                        ),
                      ),
                    ),
                  if (subHeading2 != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4.0),
                      child: Text(
                        subHeading1,
                        style: TextStyleExtension().newStyleWithColor(
                          textStyleUtil.callout1,
                          colorUtil.tertiary05,
                        ),
                      ),
                    ),
                  if (subHeading2 != null)
                    Padding(
                      padding: EdgeInsets.only(bottom: 16.0.h),
                      child: Text(
                        subHeading2 ?? '',
                        style: TextStyleExtension().newStyleWithColor(
                            textStyleUtil.footNote1, colorUtil.tertiary05),
                      ),
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
