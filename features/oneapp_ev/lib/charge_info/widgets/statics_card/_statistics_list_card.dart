// Flutter imports:
import 'package:flutter/cupertino.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';
import '../../blocs/charge_statistics/charging_statistics_bloc.dart';
import 'Statistics_page_list_tile_card.dart';

class StatisticsListCards extends ThemedStatelessWidget {
  StatisticsListCards({
    Key? key,
    required this.bloc,
  }) : super(key: key);

  final ChargeStatisticsBloc bloc;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.0.w),
      child: Column(
        children: [
          _co2EmissionCard,
          _ecoHistoryCard,
          if (!bloc.hideHealthImpact) _evHealthCard,
          _evTreeCard,
        ],
      ),
    );
  }

  Widget get _co2EmissionCard {
    return StatisticsPageListTileCard(
      leftIcon: co2EmissionIcon,
      leftIconBgColor: colorUtil.button02c,
      leftIconColor: colorUtil.tertiary03,
      mainHeading: OneAppString.of().lbs(bloc.totalEmissionForSelectedMonth),
      subHeading1: OneAppString.of().emissionSaved,
    );
  }

  Widget get _ecoHistoryCard {
    return StatisticsPageListTileCard(
      leftIcon: icEcoHistory,
      leftIconColor: colorUtil.button01a,
      leftIconBgColor: colorUtil.button03d,
      mainHeading: OneAppString.of().monthlyChallenge,
      subHeading1: OneAppString.of().monthlyChallengeDescription(
          bloc.leavesEarnedForSelectedMonth, bloc.totalDaysForSelectedMonth),
    );
  }

  Widget get _evHealthCard {
    return StatisticsPageListTileCard(
      leftIcon: evHealthIcon,
      leftIconColor: colorUtil.primaryButton01,
      leftIconBgColor: colorUtil.success01,
      mainHeading: OneAppString.of().healthImpactReduction,
      subHeading1: double.parse(bloc.healthImpactReductionBy) > 0
          ? OneAppString.of().healthImpactReductionPositiveFootNote(
              bloc.healthImpactReductionBy,
            )
          : OneAppString.of().healthImpactReductionZeroNegativeFootNote,
    );
  }

  Widget get _evTreeCard {
    return StatisticsPageListTileCard(
        leftIcon: evTreeIcon,
        iconHeight: 30,
        iconWidth: 30,
        leftIconPadding: 10,
        leftIconColor: colorUtil.tertiary15,
        leftIconBgColor: colorUtil.button03d,
        mainHeading: OneAppString.of().treesPlanted,
        subHeading1: OneAppString.of().treesPlantedByCount(
            bloc.totalEmissionForSelectedMonth,
            bloc.totalTreesPlantedForSelectedMonth,
            bloc.totalYearsOfTreesPlanted));
  }
}

class StatisticsPageInfo {
  final double emissionsSaved;
  final int leavesEarned;
  final double healthImpact;
  final int treesPlanted;
  final bool hideHealthImpact;
  final bool isNoData;

  StatisticsPageInfo._({
    required this.emissionsSaved,
    required this.leavesEarned,
    required this.healthImpact,
    required this.treesPlanted,
    required this.hideHealthImpact,
    required this.isNoData,
  });

  factory StatisticsPageInfo.noData() {
    return StatisticsPageInfo._(
      emissionsSaved: 0,
      healthImpact: 0,
      hideHealthImpact: true,
      leavesEarned: 0,
      treesPlanted: 0,
      isNoData: true,
    );
  }

  factory StatisticsPageInfo.data({
    required emissionsSaved,
    required leavesEarned,
    required healthImpact,
    required treesPlanted,
    required hideHealthImpact,
  }) {
    return StatisticsPageInfo._(
      emissionsSaved: 175,
      healthImpact: 2.4,
      hideHealthImpact: false,
      leavesEarned: 16,
      treesPlanted: 5,
      isNoData: false,
    );
  }
}
