// Dart imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:d_chart/d_chart.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';

class StatisticsEcoPieChart extends StatelessWidget {
  final int achievedLeaves;

  const StatisticsEcoPieChart({
    Key? key,
    required this.achievedLeaves,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 88.h,
      width: 88.w,
      child: Stack(
        children: [
          Container(
            child: Center(
              child: SvgPicture.asset(
                icEcoLeaf,
                colorFilter: ColorFilter.mode(
                  ThemeConfig.current().colorUtil.button03d,
                  BlendMode.srcIn,
                ),
                semanticsLabel: ECO_LEAF_ICON,
                width: 24.w,
                height: 18.35.h,
                allowDrawingOutsideViewBox: true,
              ),
            ),
          ),
          Center(
            child: Container(
              height: 88.h,
              child: AspectRatio(
                aspectRatio: 1,
                child: DChartPie(
                  showLabelLine: false,
                  labelFontSize: 0,
                  data: [
                    {'domain': 'DATA_1', 'measure': 10},
                    {'domain': 'DATA_2', 'measure': 10},
                    {'domain': 'DATA_3', 'measure': 10},
                    {'domain': 'DATA_4', 'measure': 10},
                    {'domain': 'DATA_5', 'measure': 10},
                    {'domain': 'DATA_6', 'measure': 10},
                    {'domain': 'DATA_7', 'measure': 10},
                  ],
                  fillColor: (pieData, index) => ((index ?? 0) < achievedLeaves
                      ? ThemeConfig.current().colorUtil.button03d
                      : ThemeConfig.current().colorUtil.tertiary10),
                  donutWidth: 2,
                  strokeWidth: 3.r,
                  labelColor: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
