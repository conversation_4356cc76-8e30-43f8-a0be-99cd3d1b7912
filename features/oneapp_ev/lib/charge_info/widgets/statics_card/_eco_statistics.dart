// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';
import '_eco_pie_chart.dart';

class EcoStatistics extends ThemedStatelessWidget {
  final int achievedLeaves;

  EcoStatistics({
    required this.achievedLeaves,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      Flexible(
        flex: 2,
        child: StatisticsEcoPieChart(achievedLeaves: achievedLeaves),
      ),
      Flexible(
        flex: 3,
        fit: FlexFit.tight,
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            OneAppString.of().ecoCharging,
            style: TextStyleExtension().newStyleWithColor(
              textStyleUtil.callout2,
              colorUtil.tertiary03,
            ),
          ),
          Text(
            OneAppString.of().ecoChargingAchievementText(achievedLeaves),
            style: TextStyleExtension().newStyleWithColor(
                textStyleUtil.footNote1, colorUtil.tertiary05),
          )
        ]),
      ),
    ]);
  }
}
