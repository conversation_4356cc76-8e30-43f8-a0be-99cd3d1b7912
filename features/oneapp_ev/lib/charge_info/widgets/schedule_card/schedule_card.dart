// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../../core/shimmering/widgets/shimmering_tile_widget.dart';
import '../../../core/widgets/themed_widget.dart';
import '../../../core/widgets/tile_simple_widget.dart';

class ScheduleCard extends ThemedStatelessWidget {
  final bool isLoading;
  final Function? onPress;
  ScheduleCard({
    this.isLoading = false,
    this.onPress,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isLoading ? EVShimmeringTile() : _scheduleCardWidget();
  }

  Widget _scheduleCardWidget() {
    return EVSimpleTile(
      title: OneAppString.of().schedule,
      description: OneAppString.of().ecoChargingDescription,
      leftIcon: SvgPicture.asset(
        wattTimescheduleIcon,
        colorFilter: ColorFilter.mode(
          colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        width: 24.w,
        height: 24.h,
      ),
      rightIcon: SvgPicture.asset(
        chevronRightIcon,
        colorFilter: ColorFilter.mode(
          colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        width: 7.w,
        height: 12.h,
      ),
      onPress: onPress,
    );
  }
}
