// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../core/widgets/themed_widget.dart';
import '../../core/widgets/tile_simple_widget.dart';

class CleanAssistCardWidget extends ThemedStatelessWidget {
  final String title;
  final String subTitle;
  final bool loadingShimmer;
  final bool isLCFSShowGraph;
  final Function? onPress;
  CleanAssistCardWidget({
    Key? key,
    required this.title,
    required this.subTitle,
    this.loadingShimmer = false,
    required this.isLCFSShowGraph,
    this.onPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EVSimpleTile(
      onPress: () {
        if (onPress != null) {
          onPress!();
        }
      },
      loadingShimmer: loadingShimmer,
      leftIcon: SvgPicture.asset(
        cleanAssistSvgWattTimeIcon,
        colorFilter: ColorFilter.mode(
          colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        height: 32.h,
        width: 32.w,
        allowDrawingOutsideViewBox: false,
      ),
      rightIcon: SizedBox(
          height: 36.h,
          width: 72.w,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100.w),
              border: Border.all(
                color: colorUtil.tertiary10,
                width: 2.w,
              ),
            ),
            child: Row(
              children: [
                Padding(
                    padding:
                        EdgeInsets.only(left: 16.w, top: 8.h, bottom: 8.h)),
                Text(
                  this.isLCFSShowGraph
                      ? OneAppString.of().view
                      : OneAppString.of().enroll,
                  style: TextStyleExtension().newStyleWithColor(
                      textStyleUtil.callout2, colorUtil.button02a),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )),
      title: this.title,
      description: this.subTitle,
    );
  }
}
