// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oa_network_impl/one_app/entity/wallet/card_entity.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:vehicle_module/ui/vehicle_finance/wallet/add_wallet/add_wallet_page.dart';
import 'package:vehicle_module/ui/vehicle_finance/wallet/wallet_home/wallet_home_page.dart';

// Project imports:
import '../../core/widgets/themed_widget.dart';
import '../../core/widgets/tile_simple_widget.dart';
import '../blocs/wallet_details_info_bloc.dart';

// ignore: must_be_immutable
class WalletCardWidget extends ThemedStatelessWidget {
  final String subTitle;
  final bool loadingShimmer;
  final bool isWalletPresence;
  final WalletCard? card;
  WalletDetailsInfoBloc? walletDetailsInfoBloc;

  WalletCardWidget({
    Key? key,
    this.subTitle = '',
    this.loadingShimmer = false,
    required this.isWalletPresence,
    required this.card,
    this.walletDetailsInfoBloc,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EVSimpleTile(
      onPress: () => {
        _showSetUpWalletScreen(
          this.isWalletPresence,
          context,
        )
      },
      loadingShimmer: loadingShimmer,
      title: OneAppString.of().wallet,
      leftIcon: SvgPicture.asset(
        walletIcon,
        colorFilter: ColorFilter.mode(
          colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        height: 32.h,
        width: 32.w,
        allowDrawingOutsideViewBox: false,
      ),
      rightIcon: this.isWalletPresence
          ? Text(
              card?.last4 != null ? '•••• ${card?.last4}' : "",
              style: TextStyleExtension().newStyleWithColor(
                  textStyleUtil.callout1, colorUtil.button02a),
              textAlign: TextAlign.center,
            )
          : SizedBox(
              height: 36.h,
              width: 75.w,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.w),
                  border: Border.all(
                    color: colorUtil.tertiary10,
                    width: 2.w,
                  ),
                ),
                child: Row(
                  children: [
                    Padding(
                        padding:
                            EdgeInsets.only(left: 16.w, top: 8.h, bottom: 8.h)),
                    Text(
                      OneAppString.of().setup,
                      style: TextStyleExtension().newStyleWithColor(
                          textStyleUtil.callout2, colorUtil.button02a),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )),
    );
  }

  _showSetUpWalletScreen(bool isPresent, BuildContext context) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: colorUtil.tertiary15,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(child: isPresent ? WalletHomePage() : AddWallet()),
          ],
        ),
      ),
    ).then((_) => this.walletDetailsInfoBloc?.initializeWallet());
  }
}
