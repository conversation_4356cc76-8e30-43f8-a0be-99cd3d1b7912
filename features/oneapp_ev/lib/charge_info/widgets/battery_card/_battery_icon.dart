// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../../core/ev_util.dart';
import '../../../core/widgets/themed_widget.dart';

class BatteryIcon extends ThemedStatelessWidget {
  final bool isCharging;
  final int chargingPercentage;
  final bool isPlugConnected;
  final Color? iconColor;

  BatteryIcon({
    Key? key,
    required this.isCharging,
    required this.chargingPercentage,
    required this.isPlugConnected,
    this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      getIcon(),
      height: 12.33.w,
      width: 10.43.w,
      colorFilter: ColorFilter.mode(
        (iconColor != null)
            ? iconColor!
            : EvUtil.getColor(chargingPercentage, isCharging),
        BlendMode.srcIn,
      ),
      allowDrawingOutsideViewBox: false,
    );
  }

  String getIcon() {
    if (isCharging) {
      return batteryChargingIcon;
    }
    if (isPlugConnected) {
      return batteryPlugConnectedIcon;
    }

    if ((chargingPercentage < 90 && chargingPercentage >= 40)) {
      return batteryPartialChargeIcon;
    } else if ((chargingPercentage < 40 && chargingPercentage >= 10)) {
      return batteryLowChargeIcon;
    } else if (chargingPercentage < 10) {
      return batteryDeadIcon;
    }

    return batteryFullChargeIcon;
  }
}
