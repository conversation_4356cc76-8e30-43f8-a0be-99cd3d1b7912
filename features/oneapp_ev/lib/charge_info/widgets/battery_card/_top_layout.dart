// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';
import '_battery_icon.dart';

class BatteryTopLayout extends ThemedStatelessWidget {
  final bool isCharging;
  final int chargingPercentage;
  final String? nextScheduleTime;
  final bool isPlugConnected;
  final bool showNextSchedule;
  final bool isPHEV;

  BatteryTopLayout({
    Key? key,
    required this.isCharging,
    required this.chargingPercentage,
    required this.nextScheduleTime,
    required this.isPlugConnected,
    required this.isPHEV,
    this.showNextSchedule = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Padding(
              padding: EdgeInsets.only(top: 2.h),
              child: BatteryIcon(
                isCharging: isCharging,
                chargingPercentage: chargingPercentage,
                isPlugConnected: isPlugConnected,
                iconColor: colorUtil.tertiary00,
              ),
            ),
            SizedBox(
              width: 4.w,
            ),
            Text(
              "$chargingPercentage%",
              style: TextStyleExtension().newStyleWithColor(
                  textStyleUtil.callout2, colorUtil.tertiary03),
            ),
            Text(
              isCharging
                  ? " • ${OneAppString.of().charging}".toUpperCase()
                  : isPlugConnected
                      ? " • ${OneAppString.of().pluggedIn}".toUpperCase()
                      : '',
              style: TextStyleExtension().newStyleWithColor(
                  textStyleUtil.callout2, colorUtil.tertiary03),
            )
          ],
        ),
        if (!isCharging && showNextSchedule) _scheduleCharging()
      ],
    );
  }

  _scheduleCharging() {
    return Align(
        alignment: Alignment.topRight,
        child: Text(
          nextScheduleTime!,
          style: TextStyleExtension()
              .newStyleWithColor(textStyleUtil.callout1, colorUtil.tertiary03),
        ));
  }
}
