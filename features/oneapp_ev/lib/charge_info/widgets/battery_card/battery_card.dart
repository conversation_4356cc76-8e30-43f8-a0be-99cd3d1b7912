// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../../../core/shimmering/widgets/shimmering_battery_card_widget.dart';
import '../../../core/widgets/card_widget.dart';
import '../../../core/widgets/themed_widget.dart';
import '_charge_meter.dart';
import '_top_layout.dart';

class BatteryInfo {
  final bool isCharging;
  final int chargingPercentage;
  final bool isPlugConnected;
  final String? estimatedTime;
  final String? nextScheduleTime;

  BatteryInfo({
    required this.isCharging,
    required this.chargingPercentage,
    required this.isPlugConnected,
    this.estimatedTime = "",
    this.nextScheduleTime = "",
  });
}

class BatteryCard extends ThemedStatelessWidget {
  final BatteryInfo? batteryInfo;
  final bool isLoading;

  BatteryCard({
    Key? key,
    this.batteryInfo,
    this.isLoading = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isLoading ? ShimmeringBatteryCard() : _batteryChargingCard();
  }

  Widget _batteryChargingCard() {
    final info = batteryInfo;
    if (info == null) {
      return Container();
    }
    return OACard(
      cardStyle: CardStyle(backgroundColor: colorUtil.button05b, elevation: 1),
      cardChild: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            BatteryTopLayout(
              isCharging: info.isCharging,
              chargingPercentage: info.chargingPercentage,
              isPlugConnected: info.isPlugConnected,
              nextScheduleTime: info.nextScheduleTime,
              showNextSchedule: true,
              isPHEV: false,
            ),
            Padding(
              padding: EdgeInsets.only(top: 16.h),
              child: BatterChargeMeter(
                chargingPercentage: info.chargingPercentage,
                isPlugConnected: info.isPlugConnected,
                isCharging: info.isCharging,
              ),
            ),
            Visibility(
                visible: info.isCharging,
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.only(top: 16.h),
                    child: Text(
                      info.estimatedTime ?? "",
                      style: TextStyleExtension().newStyleWithColor(
                          textStyleUtil.caption1, colorUtil.tertiary03),
                    ),
                  ),
                ))
          ],
        ),
      ),
    );
  }
}
