// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Project imports:
import '../../../core/shimmering/widgets/shimmering_battery_card_widget.dart';
import '../../../core/widgets/card_widget.dart';
import '../../../core/widgets/themed_widget.dart';
import '../helper/range_info.dart';
import '_phev_bottom_layout.dart';
import '_phev_charge_meter.dart';
import '_top_layout.dart';

class PHevBatteryInfo {
  final bool isCharging;
  final int chargingPercentage;
  final int? fuelLevel;
  final bool isPlugConnected;
  final String? estimatedTime;
  final String? nextScheduleTime;
  final RangeInfo gasRangeInfo;
  final RangeInfo evRangeInfo;
  final RangeInfo gasMaxRangeInfo;
  final RangeInfo evMaxRangeInfo;
  PHevBatteryInfo({
    required this.isCharging,
    required this.chargingPercentage,
    required this.fuelLevel,
    required this.isPlugConnected,
    this.estimatedTime,
    this.nextScheduleTime,
    required this.gasRangeInfo,
    required this.evRangeInfo,
    required this.gasMaxRangeInfo,
    required this.evMaxRangeInfo,
  });
}

class PHevBatteryCard extends ThemedStatelessWidget {
  final PHevBatteryInfo batteryInfo;
  final bool isLoading;
  PHevBatteryCard({Key? key, required this.batteryInfo, this.isLoading = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isLoading ? ShimmeringBatteryCard() : _batteryChargingCard();
  }

  Widget _batteryChargingCard() {
    final info = batteryInfo;
    // ignore: unnecessary_null_comparison
    if (info == null) {
      return Container();
    } else if (info.fuelLevel == null) {
      return Container();
    }
    return OACard(
      cardStyle: CardStyle(backgroundColor: colorUtil.button05b, elevation: 1),
      cardChild: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Column(
          children: [
            BatteryTopLayout(
              isCharging: info.isCharging,
              chargingPercentage: info.chargingPercentage,
              isPlugConnected: info.isPlugConnected,
              nextScheduleTime: info.nextScheduleTime,
              showNextSchedule: false,
              isPHEV: true,
            ),
            Padding(
              padding: EdgeInsets.only(top: 15.h),
              child: Semantics(
                container: true,
                child: PHevBatterChargeMeter(
                  batteryInfo: batteryInfo,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 15.h),
              child: PHevBatteryBottomLayout(batteryInfo: batteryInfo),
            ),
          ],
        ),
      ),
    );
  }
}
