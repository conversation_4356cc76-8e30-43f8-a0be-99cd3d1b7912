// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/widget/graphics/fuel_electric_graphics.dart';

// Project imports:
import '../../../core/ev_util.dart';
import '../../../core/widgets/themed_widget.dart';

class BatterChargeMeter extends ThemedStatelessWidget {
  final int chargingPercentage;
  final bool isCharging;
  final bool isPlugConnected;

  BatterChargeMeter(
      {Key? key,
      required this.chargingPercentage,
      required this.isCharging,
      required this.isPlugConnected})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FuelElectricOverviewGraphics(
      animatedDuration: Duration(seconds: 1),
      currentValueInPercent: chargingPercentage,
      size: 10.h,
      borderRadius: 32.h,
      backgroundColor: colorUtil.tile01,
      borderColor: colorUtil.tile01,
      isGradient: false,
      progressColor: EvUtil.getColor(chargingPercentage, isCharging),
    );
  }
}
