// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/widget/graphics/custom_fuel_electric_graphics.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';
import 'phev_battery_card.dart';

class PHevBatterChargeMeter extends ThemedStatelessWidget {
  PHevBatterChargeMeter({Key? key, required this.batteryInfo})
      : super(key: key);

  final PHevBatteryInfo batteryInfo;
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CustomFuelElectricOverviewGraphics(
          animatedDuration: Duration(seconds: 1),
          currentValueInPercent: batteryInfo.chargingPercentage,
          currentFuelValue: batteryInfo.fuelLevel ?? null,
          maxValue: batteryInfo.evMaxRangeInfo.range.toInt() +
              batteryInfo.gasMaxRangeInfo.range.toInt() +
              50,
          size: 10.h,
          borderRadius: 32.h,
          backgroundColor: Colors.transparent,
          borderColor: colorUtil.tile01,
          isGradient: false,
          progressColor: colorUtil.button03d, //EV bar - always green
          fuelProgressColor: (batteryInfo.fuelLevel ?? 0) <= 30
              ? colorUtil.primary01
              : colorUtil.secondary01,
        ),
      ],
    );
  }
}
