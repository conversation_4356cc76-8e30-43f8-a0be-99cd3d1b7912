// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../core/ev_util.dart';
import '../../../core/widgets/CircularIcon.dart';
import 'phev_battery_card.dart';

class PHevBatteryBottomLayout extends StatelessWidget {
  PHevBatteryBottomLayout({
    Key? key,
    required this.batteryInfo,
  }) : super(key: key);

  final PHevBatteryInfo batteryInfo;
  final ColorUtil colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil textStyleUtil = ThemeConfig.current().textStyleUtil;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "${batteryInfo.gasRangeInfo.range.toInt()} ",
                    style: TextStyleExtension().newStyleWithColor(
                      textStyleUtil.title1,
                      colorUtil.tertiary00,
                    ),
                  ),
                  TextSpan(
                    text: batteryInfo.gasRangeInfo.unit,
                    style: TextStyleExtension().newStyleWithColor(
                      textStyleUtil.body3,
                      colorUtil.tertiary00,
                    ),
                  ),
                ],
              ),
            ),
            Row(
              children: [
                CircularIcon(
                  color: Colors.blue,
                  size: 10,
                ),
                Text(OneAppString.of().gasRange,
                    style: TextStyleExtension().newStyleWithColor(
                      textStyleUtil.caption1,
                      colorUtil.tertiary00,
                    )),
              ],
            )
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "${batteryInfo.evRangeInfo.range.toInt()} ",
                    style: TextStyleExtension().newStyleWithColor(
                      textStyleUtil.title1,
                      colorUtil.tertiary00,
                    ),
                  ),
                  TextSpan(
                    text: batteryInfo.evRangeInfo.unit,
                    style: TextStyleExtension().newStyleWithColor(
                      textStyleUtil.body3,
                      colorUtil.tertiary00,
                    ),
                  ),
                ],
              ),
            ),
            getEvBottomText()
          ],
        )
      ],
    );
  }

  Widget getEvBottomText() {
    String? infoText = '';
    if (batteryInfo.isPlugConnected && batteryInfo.nextScheduleTime != null) {
      infoText = batteryInfo.nextScheduleTime;
    }
    if (batteryInfo.isPlugConnected && batteryInfo.nextScheduleTime == null) {
      infoText = OneAppString.of().noScheduleChargingText;
    }
    if (batteryInfo.isCharging) {
      infoText = batteryInfo.estimatedTime;
    }
    if (infoText == "") {
      return _evRangeText();
    }
    return Padding(
      padding: EdgeInsets.only(top: 5.h),
      child: Text(
        infoText!,
        style: TextStyleExtension()
            .newStyleWithColor(textStyleUtil.caption1, colorUtil.tertiary03),
      ),
    );
  }

  Row _evRangeText() {
    return Row(
      children: [
        Text(OneAppString.of().evRange,
            style: TextStyleExtension().newStyleWithColor(
              textStyleUtil.caption1,
              colorUtil.tertiary00,
            )),
        CircularIcon(
          color: EvUtil.getColor(
              batteryInfo.chargingPercentage, batteryInfo.isCharging),
          size: 10,
        )
      ],
    );
  }
}
