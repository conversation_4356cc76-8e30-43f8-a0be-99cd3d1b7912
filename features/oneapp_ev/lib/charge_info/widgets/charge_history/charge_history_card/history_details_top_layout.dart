// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../../../../core/constants.dart';
import '../../../../core/widgets/custom_calender_view.dart';
import '../../../../core/widgets/themed_widget.dart';

class ChargeHistoryDetailsTopLayout extends ThemedStatelessWidget {
  ChargeHistoryDetailsTopLayout({
    Key? key,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.partnerName,
    required this.date,
    required this.day,
    required this.month,
  }) : super(key: key);
  final String partnerName;
  final String? address;
  final String? latitude;
  final String? longitude;
  final String date;
  final String day;
  final String month;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomCalenderView(
          info: CustomCalenderInfo(day: day, month: month),
        ),
        Padding(
          padding: EdgeInsets.symmetric(vertical: 20.h),
          child: Text(date),
        ),
        Container(
          decoration: BoxDecoration(
            color: colorUtil.tertiary15,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: colorUtil.tertiary00.withOpacity(0.1),
                spreadRadius: 0.5,
                blurRadius: 5,
                offset: Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: Card(
            color: colorUtil.tertiary15,
            elevation: 0.0,
            child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: colorUtil.success02,
                  radius: 20,
                  child: Icon(
                    partnerName == homeText
                        ? Icons.home_outlined
                        : Icons.location_on_outlined,
                    color: colorUtil.secondary01,
                  ),
                ),
                title: Text(
                  partnerName,
                  style: TextStyleExtension().newStyleWithColor(
                      textStyleUtil.body4, colorUtil.tertiary00),
                ),
                subtitle: Text(
                  address ?? "",
                  style: TextStyleExtension().newStyleWithColor(
                      textStyleUtil.callout1, colorUtil.tertiary03),
                  textAlign: TextAlign.start,
                )),
          ),
        ),
      ],
    );
  }
}
