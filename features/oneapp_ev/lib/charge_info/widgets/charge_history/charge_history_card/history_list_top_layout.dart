// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';
import '../../../blocs/charge_history_bloc.dart';
import '../../../models/charge_history_filter_helper.dart';
import 'filter_chip_card.dart';

class TopFilterLayout extends ThemedStatelessWidget {
  TopFilterLayout({Key? key, required this.bloc, required this.buildContext})
      : super(key: key);
  final ChargeHistoryBloc bloc;
  final BuildContext buildContext;
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<FilterCardHelper>?>(
        stream: bloc.filterCardList,
        builder: (context, snapshot) {
          return snapshot.hasData && snapshot.data != null
              ? Expanded(
                  flex: 1,
                  child: ListView.builder(
                      itemCount: snapshot.data!.length,
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (context, index) {
                        return Padding(
                            padding: EdgeInsets.symmetric(horizontal: 5.w),
                            child: FilterChipCard(
                                context: buildContext,
                                cardHelper: snapshot.data![index],
                                onTapCallBack: bloc.filterTapCallBack,
                                index: index));
                      }),
                )
              : Container();
        });
  }
}
