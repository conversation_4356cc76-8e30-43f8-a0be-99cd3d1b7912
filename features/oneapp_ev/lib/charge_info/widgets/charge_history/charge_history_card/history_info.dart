// Flutter imports:

class ChargeHistoryInfo {
  final String chargeLocationType;
  final DateTime startTime;
  final DateTime endTime;
  final String socBefore;
  final String socAfter;
  final String latitude;
  final String longitude;
  final String? address;
  final String totalCharge;
  final bool isEcho;
  final CdrDetailsInfo? cdrDetails;
  ChargeHistoryInfo(
      {required this.chargeLocationType,
      required this.startTime,
      required this.endTime,
      required this.socBefore,
      required this.socAfter,
      required this.latitude,
      required this.longitude,
      required this.address,
      required this.totalCharge,
      required this.isEcho,
      required this.cdrDetails});
}

class CdrDetailsInfo {
  CdrDetailsInfo({
    this.cardLast4,
    this.partnerName,
    this.totalAmount,
  });

  String? cardLast4;
  String? partnerName;
  String? totalAmount;
}
