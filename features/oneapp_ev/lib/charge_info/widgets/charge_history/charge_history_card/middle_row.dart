// Dart imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';
import 'history_info.dart';

class HistoryMiddleRow extends ThemedStatelessWidget {
  //Get history data from the API model
  final ChargeHistoryInfo historyInfo;

  HistoryMiddleRow({
    Key? key,
    required this.historyInfo,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Text(
              this.historyInfo.socBefore,
              style: textStyleUtil.callout1,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: SvgPicture.asset(
                lineArrowRightIcon,
                colorFilter: ColorFilter.mode(
                  colorUtil.tertiary00,
                  BlendMode.srcIn,
                ),
                width: 16.w,
                height: 16.h,
              ),
            ),
            Text(
              this.historyInfo.socAfter,
              style: textStyleUtil.callout2,
            ),
            Spacer(),
            Row(
              children: [
                Text(
                  this.historyInfo.totalCharge,
                  style: textStyleUtil.body4,
                ),
                Text(
                  ' kWh',
                  style: textStyleUtil.callout1,
                ),
              ],
            )
          ],
        ),
      ],
    );
  }
}
