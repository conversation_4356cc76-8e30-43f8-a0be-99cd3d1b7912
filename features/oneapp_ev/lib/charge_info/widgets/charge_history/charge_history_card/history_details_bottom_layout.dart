// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';
import '../../../help_me_improve_page.dart';

class HistoryPageBottomDetailsHelper extends ThemedStatelessWidget {
  final String title;
  final IconData? ecoChargeicon;
  final String? ecoChargeIconImage;
  final String? ecoChargeText;
  final String? startingChargeLevel;
  final String? endingChargeLevel;
  final String? unit;
  final String? trailingValue;
  final Color? trailingTextColor;
  final bool redirectToLearnMoreScreen;
  HistoryPageBottomDetailsHelper(
      {required this.title,
      this.ecoChargeicon,
      this.ecoChargeIconImage,
      this.ecoChargeText,
      this.startingChargeLevel,
      this.endingChargeLevel,
      this.unit,
      this.trailingValue,
      this.trailingTextColor,
      this.redirectToLearnMoreScreen = false});
}

class HistoryPageBottomDetails extends ThemedStatelessWidget {
  HistoryPageBottomDetails({
    Key? key,
    required this.info,
  }) : super(key: key);
  final HistoryPageBottomDetailsHelper info;
  @override
  Widget build(BuildContext context) {
    return ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 10.h, vertical: 5.w),
        leading: Text(
          info.title,
          style: TextStyleExtension()
              .newStyleWithColor(textStyleUtil.body3, colorUtil.tertiary03),
        ),
        trailing: trailingWidget(context));
  }

  Widget trailingWidget(BuildContext context) {
    if (info.ecoChargeText != null) {
      return InkWell(
        onTap: () {
          if (info.redirectToLearnMoreScreen) {
            showMaterialModalBottomSheet(
              expand: true,
              context: context,
              clipBehavior: Clip.antiAliasWithSaveLayer,
              backgroundColor: colorUtil.tertiary15,
              builder: (context) => HelpMeImprove(
                showSwipeBar: true,
                hideHealthImpact: false,
              ),
            );
          }
        },
        child: RichText(
            text: TextSpan(children: [
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: _ecoChargeIconOrImage(),
          ),
          TextSpan(
            text: " ${info.ecoChargeText}",
            style: TextStyleExtension().newStyleWithColor(
                textStyleUtil.callout1,
                info.redirectToLearnMoreScreen
                    ? colorUtil.secondary01
                    : info.trailingTextColor ?? colorUtil.tertiary03),
          )
        ])),
      );
    }
    if (info.startingChargeLevel != null && info.endingChargeLevel != null) {
      return RichText(
          textAlign: TextAlign.center,
          text: TextSpan(children: [
            TextSpan(
              text: "${info.startingChargeLevel} ",
              style: TextStyleExtension().newStyleWithColor(
                  textStyleUtil.callout1,
                  info.trailingTextColor ?? colorUtil.tertiary03),
            ),
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: Icon(
                Icons.arrow_forward,
                color: info.trailingTextColor ?? colorUtil.tertiary03,
              ),
            ),
            TextSpan(
              text: " ${info.endingChargeLevel}",
              style: TextStyleExtension().newStyleWithColor(
                  textStyleUtil.callout1,
                  info.trailingTextColor ?? colorUtil.tertiary03),
            ),
          ]));
    }
    if (info.trailingValue != null) {
      return RichText(
          text: TextSpan(children: [
        TextSpan(
          text: "${info.trailingValue} ",
          style: TextStyleExtension().newStyleWithColor(textStyleUtil.callout1,
              info.trailingTextColor ?? colorUtil.tertiary03),
        ),
        TextSpan(
          text: info.unit ?? "",
          style: TextStyleExtension().newStyleWithColor(textStyleUtil.callout1,
              info.trailingTextColor ?? colorUtil.tertiary03),
        ),
      ]));
    }
    return Container();
  }

  Widget _ecoChargeIconOrImage() {
    if (info.ecoChargeIconImage != null) {
      return SvgPicture.asset(
        info.ecoChargeIconImage!,
        colorFilter: ColorFilter.mode(
          info.trailingTextColor ?? colorUtil.button03d,
          BlendMode.srcIn,
        ),
        semanticsLabel: ECO_LEAF_ICON,
        width: 24.w,
        height: 17.35.h,
        allowDrawingOutsideViewBox: true,
      );
    }
    if (info.ecoChargeicon != null) {
      return Icon(info.ecoChargeicon, size: 18.h, color: colorUtil.secondary01);
    }
    return Container();
  }
}
