// Dart imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/entity/watttime_format_model.dart';
import 'package:oneapp_common/util/watttime_format_util.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';
import 'history_info.dart';

class HistoryBottomRow extends ThemedStatelessWidget {
  //Get history data from the API model
  final ChargeHistoryInfo historyInfo;

  HistoryBottomRow({
    Key? key,
    required this.historyInfo,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    WattTimeFormat wattTimeFormat =
        getWattTimeFormat(this.historyInfo.startTime, this.historyInfo.endTime);
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
                wattTimeFormat.day ??
                    "" +
                        " " "•" " " +
                        wattTimeFormat.startTime! +
                        " " "-" " " +
                        wattTimeFormat.endTime!,
                style: textStyleUtil.caption1,
                overflow: TextOverflow.ellipsis),
            Text(wattTimeFormat.duration ?? "",
                style: textStyleUtil.caption1, overflow: TextOverflow.ellipsis),
          ],
        ),
        if (historyInfo.cdrDetails != null)
          SizedBox(
            height: 10.h,
          ),
        if (historyInfo.cdrDetails != null)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                  this.historyInfo.cdrDetails!.cardLast4!.isNotEmpty
                      ? "****${this.historyInfo.cdrDetails!.cardLast4}"
                      : "",
                  style: textStyleUtil.caption1,
                  overflow: TextOverflow.ellipsis),
              Text(
                  this.historyInfo.cdrDetails!.totalAmount!.isNotEmpty
                      ? "\$${this.historyInfo.cdrDetails!.totalAmount!}"
                      : "",
                  style: textStyleUtil.caption1,
                  overflow: TextOverflow.ellipsis),
            ],
          ),
      ],
    );
  }
}
