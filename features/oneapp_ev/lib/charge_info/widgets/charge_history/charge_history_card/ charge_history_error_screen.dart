// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/widget/button/custom_primary_button.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';

class ChargeHistoryErrorHelper {
  IconData? icon;
  String? svgImage;
  String heading;
  String subHeading;
  bool showGotoProfile;
  ChargeHistoryErrorHelper(
      {this.icon,
      this.svgImage,
      required this.heading,
      required this.subHeading,
      this.showGotoProfile = false});
}

class ChargeHistoryListErrorScreen extends ThemedStatelessWidget {
  ChargeHistoryListErrorScreen({
    required this.info,
    Key? key,
  }) : super(key: key);
  final ChargeHistoryErrorHelper info;
  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: 10,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 150.h),
            child: Column(
              children: [
                if (info.icon != null)
                  Padding(
                    padding: EdgeInsets.all(5.0.w),
                    child: CircleAvatar(
                      backgroundColor: colorUtil.success02,
                      radius: 20,
                      child: Icon(
                        Icons.location_on_outlined,
                        color: colorUtil.secondary01,
                      ),
                    ),
                  ),
                if (info.svgImage != null)
                  Padding(
                    padding: EdgeInsets.all(5.0.w),
                    child: CircleAvatar(
                      backgroundColor: colorUtil.success02,
                      radius: 20,
                      child: SvgPicture.asset(
                        info.svgImage!,
                        height: 15.h,
                        width: 10.w,
                        colorFilter: ColorFilter.mode(
                          colorUtil.tertiary03,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                Padding(
                  padding: EdgeInsets.all(5.0.w),
                  child: Text(
                    info.heading,
                    style: TextStyleExtension().newStyleWithColor(
                        textStyleUtil.body4, colorUtil.tertiary00),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(5.0.w),
                  child: Text(
                    info.subHeading,
                    style: TextStyleExtension().newStyleWithColor(
                        textStyleUtil.callout1, colorUtil.tertiary03),
                    overflow: TextOverflow.visible,
                    textAlign: TextAlign.center,
                  ),
                )
              ],
            ),
          ),
          Visibility(
            visible: info.showGotoProfile,
            child: Padding(
              padding: EdgeInsets.only(top: 8.h, bottom: 30.h),
              child: CustomDefaultButton(
                backgroundColor: colorUtil.primaryButton02,
                buttonTextColor: colorUtil.primaryButton01,
                text: OneAppString.of().goToAccountSettingsText,
                press: () {
                  loadAccountNativePage().then((_) => Navigator.pop(context));
                },
                borderColor: colorUtil.primaryButton02,
                horizontalPadding: 22.w,
                verticalPadding: 8.h,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
