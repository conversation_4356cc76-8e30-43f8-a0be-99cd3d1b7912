// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';
import '../../../../core/widgets/tile_expanded_widget.dart';
import '../../../../core/widgets/tile_simple_widget.dart';
import '../../../../log/ev_analytics_events.dart';
import '../charge_history_shimmer/history_shimmer_card.dart';
import 'charge_history.dart';
import 'history_info.dart';
import 'history_list.dart';

class ChargeHistoryCard extends ThemedStatelessWidget {
  final ChargeHistoryInfo? lastCharge;
  final bool isLoading;
  ChargeHistoryCard({
    Key? key,
    required this.lastCharge,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final info = lastCharge;
    if (isLoading) {
      return ChargeHistoryShimmerCard();
    } else if (info != null) {
      return _chargeHistory(info, context);
    } else {
      return _noHistory(context);
    }
  }

  EVSimpleTile _noHistory(BuildContext context) {
    return EVSimpleTile(
      onPress: () {
        showChargingHistoryList(context);
      },
      title: OneAppString.of().history,
      leftIcon: SvgPicture.asset(
        scheduleIcon,
        colorFilter: ColorFilter.mode(
          colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        width: 24.w,
        height: 24.h,
      ),
      rightIcon: SvgPicture.asset(
        chevronRightIcon,
        colorFilter: ColorFilter.mode(
          colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        width: 7.w,
        height: 12.h,
      ),
      description: OneAppString.of().historyDescription,
    );
  }

  EVExpandedTile _chargeHistory(
      ChargeHistoryInfo lastCharge, BuildContext context) {
    return EVExpandedTile(
      onPress: () {
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
            VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
            childEventName: EVAnalyticsEvent.CHARGE_HISTORY);
        showChargingHistoryList(context);
      },
      title: OneAppString.of().history,
      leftIcon: SvgPicture.asset(
        scheduleIcon,
        colorFilter: ColorFilter.mode(
          colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        width: 24.w,
        height: 24.h,
      ),
      rightIcon: SvgPicture.asset(
        chevronRightIcon,
        colorFilter: ColorFilter.mode(
          colorUtil.tertiary00,
          BlendMode.srcIn,
        ),
        width: 7.w,
        height: 12.h,
      ),
      cardChild: ChargeHistory(
        cardColor: colorUtil.tile05,
        historyInfo: lastCharge,
        buildContext: context,
      ),
    );
  }

  Future<void> showChargingHistoryList<bool>(BuildContext context) {
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: ThemeConfig.current().colorUtil.tertiary12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        bottom: false,
        child: Column(
          children: [
            SwipeBarIcon(),
            Expanded(child: ChargeHistoryList()),
          ],
        ),
      ),
    );
  }
}
