// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';
import 'history_details_bottom_layout.dart';
import 'history_details_top_layout.dart';

class ChargeHistoryDetailsHelper {
  String? address;
  String? partnerName;
  String date;
  String day;
  String month;
  String lat;
  String long;
  List<HistoryPageBottomDetailsHelper> bottomHelper;
  String? totalAmount;
  String? currecy;
  bool isTipWidgetVisible;
  String? tipText;
  ChargeHistoryDetailsHelper(
      {this.address,
      required this.lat,
      required this.long,
      required this.partnerName,
      required this.date,
      required this.day,
      required this.month,
      required this.bottomHelper,
      this.totalAmount,
      this.currecy,
      this.isTipWidgetVisible = false,
      this.tipText});
}

class ChargeHistoryDetails extends ThemedStatelessWidget {
  ChargeHistoryDetails({
    Key? key,
    required this.info,
  }) : super(key: key);
  final ChargeHistoryDetailsHelper info;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: colorUtil.tertiary15,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ChargeHistoryDetailsTopLayout(
              date: info.date,
              day: info.day,
              partnerName: info.partnerName!,
              month: info.month,
              latitude: info.lat,
              longitude: info.long,
              address: info.address,
            ),
            ListView.separated(
                scrollDirection: Axis.vertical,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return HistoryPageBottomDetails(
                      info: info.bottomHelper[index]);
                },
                separatorBuilder: (context, index) {
                  return index != info.bottomHelper.length
                      ? Divider(
                          height: 1.h,
                          thickness: 1.0,
                          color: colorUtil.tertiary10,
                        )
                      : Container();
                },
                itemCount: info.bottomHelper.length),
            if (info.totalAmount != null && info.currecy != null)
              Container(
                color: colorUtil.tertiary12,
                child: ListTile(
                    leading: Text(
                      OneAppString.of().total,
                      style: TextStyleExtension().newStyleWithColor(
                          textStyleUtil.subHeadline1, colorUtil.tertiary03),
                    ),
                    trailing: Text(
                      "${info.currecy} ${info.totalAmount}",
                      style: TextStyleExtension().newStyleWithColor(
                          textStyleUtil.subHeadline1, colorUtil.tertiary03),
                    )),
              ),
          ],
        ),
      ),
    );
  }
}
