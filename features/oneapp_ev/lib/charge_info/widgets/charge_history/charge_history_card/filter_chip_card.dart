// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';
import '../../../models/charge_history_filter_helper.dart';
import '../../statics_card/yearmonth_lib/yearmonth_picker.dart';
import '../../statics_card/yearmonth_lib/yearmonth_picker_bloc.dart';

class FilterChipCard extends ThemedStatelessWidget {
  FilterChipCard({
    Key? key,
    required this.cardHelper,
    required this.index,
    required this.context,
    this.onTapCallBack,
  }) : super(key: key);
  final FilterCardHelper cardHelper;
  final void Function({required int index, MonthYearModel? monthYearModel})?
      onTapCallBack;
  final int index;
  final BuildContext context;

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: callBackFunction,
        child: Chip(
            backgroundColor: cardHelper.isSelected
                ? colorUtil.button02a
                : colorUtil.button05b,
            label: cardHelper.arrowIconEnabled
                ? Container(
                    padding: EdgeInsets.symmetric(horizontal: 5.w),
                    height: 30.h,
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            cardHelper.labelText,
                            style: TextStyleExtension().newStyleWithColor(
                              textStyleUtil.callout2,
                              cardHelper.isSelected
                                  ? colorUtil.button05b
                                  : colorUtil.button02a,
                            ),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Icon(
                            Icons.keyboard_arrow_down_rounded,
                            color: cardHelper.isSelected
                                ? colorUtil.button05b
                                : colorUtil.button02a,
                          )
                        ]))
                : Container(
                    padding: EdgeInsets.symmetric(horizontal: 18.w),
                    height: 30.h,
                    child: Center(
                        child: Text(
                      cardHelper.labelText,
                      style: TextStyleExtension().newStyleWithColor(
                        textStyleUtil.callout2,
                        cardHelper.isSelected
                            ? colorUtil.button05b
                            : colorUtil.button02a,
                      ),
                    )))));
  }

  void callBackFunction() async {
    MonthYearModel? selectedMonthYearInfo;
    if (cardHelper.isDateTimePickerEnabled) {
      await showCupertinoModalPopup<void>(
          context: context,
          builder: (BuildContext context) {
            return _buildBottomPicker(YearMonthPicker(
              initialMonth: cardHelper.initialMonth,
              initialYear: cardHelper.initialYear,
              onPress: (MonthYearModel selectedMonthYear) {
                selectedMonthYearInfo = selectedMonthYear;
                debugPrint(
                    selectedMonthYear.month + ' ' + selectedMonthYear.year);
              },
            ));
          });
    }
    onTapCallBack!(index: index, monthYearModel: selectedMonthYearInfo ?? null);
  }

  Widget _buildBottomPicker(Widget picker) {
    return Container(
      height: 200,
      padding: const EdgeInsets.only(top: 6.0),
      color: colorUtil.tile01,
      child: DefaultTextStyle(
        style: TextStyle(
          color: colorUtil.brand01,
          fontSize: 22.0,
        ),
        child: GestureDetector(
          // Blocks taps from propagating to the modal sheet and popping.
          onTap: () {},
          child: SafeArea(
            top: false,
            child: picker,
          ),
        ),
      ),
    );
  }
}
