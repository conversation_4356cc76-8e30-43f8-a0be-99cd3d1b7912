// Dart imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';
import 'bottom_row.dart';
import 'history_info.dart';
import 'middle_row.dart';
import 'top_row.dart';

// Package imports:

class ChargeHistory extends ThemedStatelessWidget {
  //Get history data from the API model
  final ChargeHistoryInfo historyInfo;
  final Color? cardColor;
  final BuildContext buildContext;
  ChargeHistory({
    Key? key,
    required this.historyInfo,
    this.cardColor,
    required this.buildContext,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.h),
      child: Container(
        decoration: BoxDecoration(
            color: cardColor ?? colorUtil.tile03,
            borderRadius: BorderRadius.all(Radius.circular(8.r))),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.h),
          child: Column(
            children: [
              HistoryTopRow(
                  historyInfo: historyInfo, buildContext: buildContext),
              SizedBox(height: 10.h),
              HistoryMiddleRow(historyInfo: historyInfo),
              SizedBox(height: 10.h),
              HistoryBottomRow(historyInfo: historyInfo),
            ],
          ),
        ),
      ),
    );
  }
}
