// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:intl/src/intl/date_format.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/features/core/utils/utils.dart';

// Project imports:
import '%20charge_history_error_screen.dart';
import '../../../../core/constants.dart';
import '../../../../log/ev_analytics_events.dart';
import '../../../blocs/charge_history_bloc.dart';
import 'charge_history.dart';
import 'history_detail.dart';
import 'history_details_bottom_layout.dart';
import 'history_info.dart';
import 'history_list_top_layout.dart';

class ChargeHistoryList extends StatefulWidget {
  ChargeHistoryList({
    Key? key,
  }) : super(key: key);
  final List<HistoryPageBottomDetailsHelper> bottomHelper = [];
  @override
  State<ChargeHistoryList> createState() => _ChargeHistoryListState();
}

class _ChargeHistoryListState extends State<ChargeHistoryList> {
  final ColorUtil colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil textStyleUtil = ThemeConfig.current().textStyleUtil;
  ChargeHistoryBloc bloc = ChargeHistoryBloc();
  BuildContext? buildContext;
  @override
  void initState() {
    super.initState();
    bloc.init();
  }

  @override
  void dispose() {
    super.dispose();
    bloc.clearFilterList();
  }

  @override
  Widget build(BuildContext context) {
    buildContext = context;
    return OneAppScaffold(
      body: Column(
        children: [
          bottomSheetCustomAppBar(OneAppString.of().history, onBackPressed: () {
            Navigator.of(context).pop();
          }, elevation: 0),
          TopFilterLayout(bloc: bloc, buildContext: context),
          StreamBuilder<List<ChargeHistoryInfo>?>(
              stream: bloc.chargeHistoryInfo,
              builder: (context, snapshot) {
                List<ChargeHistoryInfo>? historyInfoList = snapshot.data;
                return snapshot.hasData
                    ? _showChargeHistoryList(historyInfoList)
                    : bloc.isApiCalled
                        ? Expanded(
                            flex: 10,
                            child: Center(
                              child: CircularProgressIndicator(),
                            ),
                          )
                        : _errorScreenWidget();
              })
        ],
      ),
    );
  }

  Expanded _showChargeHistoryList(List<ChargeHistoryInfo>? historyInfoList) {
    return Expanded(
      flex: 12,
      child: ListView.builder(
          itemCount: historyInfoList!.length,
          shrinkWrap: true,
          scrollDirection: Axis.vertical,
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: () {
                FireBaseAnalyticsLogger.logMarketingGroupEvent(
                    VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                    childEventName: EVAnalyticsEvent.CHARGE_HISTORY_DETAILS);
                openChargeHistoryDetailScreen(historyInfoList[index], context);
              },
              child: ChargeHistory(
                  buildContext: buildContext!,
                  cardColor: colorUtil.tile03,
                  historyInfo: ChargeHistoryInfo(
                    address: historyInfoList[index].address,
                    latitude: historyInfoList[index].latitude,
                    longitude: historyInfoList[index].longitude,
                    chargeLocationType:
                        historyInfoList[index].chargeLocationType,
                    startTime: historyInfoList[index].startTime,
                    endTime: historyInfoList[index].endTime,
                    socBefore: historyInfoList[index].socBefore,
                    socAfter: historyInfoList[index].socAfter,
                    totalCharge: historyInfoList[index].totalCharge,
                    isEcho: historyInfoList[index].isEcho,
                    cdrDetails: historyInfoList[index].cdrDetails,
                  )),
            );
          }),
    );
  }

  StreamBuilder<ChargeHistoryErrorHelper?> _errorScreenWidget() {
    return StreamBuilder<ChargeHistoryErrorHelper?>(
        stream: bloc.chargeHistoryListError,
        builder: (context, snapshot) {
          return snapshot.hasData && snapshot.data != null
              ? ChargeHistoryListErrorScreen(info: snapshot.data!)
              : Expanded(flex: 10, child: Container());
        });
  }

  Future<void> chargeHistoryDetailsScreen(
      BuildContext context, ChargeHistoryInfo chargeHistoryInfo) {
    String month =
        DateFormat("MMM").format(chargeHistoryInfo.startTime.toLocal());
    String day = DateFormat("d").format(chargeHistoryInfo.startTime.toLocal());
    return showMaterialModalBottomSheet(
      expand: true,
      isDismissible: true,
      context: context,
      useRootNavigator: true,
      backgroundColor: colorUtil.tile01,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(CARD_RADIUS),
        ),
      ),
      builder: (context) => SafeArea(
        top: true,
        bottom: true,
        child: Column(
          children: [
            SwipeBarIcon(),
            bottomSheetAppBar(OneAppString.of().chargeDetails,
                onBackPressed: () {
              Navigator.pop(context);
            }),
            Expanded(
                child: ChargeHistoryDetails(
              info: ChargeHistoryDetailsHelper(
                  lat: chargeHistoryInfo.latitude,
                  long: chargeHistoryInfo.longitude,
                  totalAmount: chargeHistoryInfo.cdrDetails != null &&
                          chargeHistoryInfo.cdrDetails!.totalAmount!.isNotEmpty
                      ? chargeHistoryInfo.cdrDetails!.totalAmount
                      : null,
                  currecy: chargeHistoryInfo.cdrDetails != null ? "\$" : null,
                  date: DateFormat.yMMMd()
                      .format(chargeHistoryInfo.startTime.toLocal()),
                  day: day,
                  partnerName: chargeHistoryInfo.chargeLocationType,
                  month: month,
                  address: chargeHistoryInfo.address,
                  bottomHelper: widget.bottomHelper,
                  tipText: OneAppString.of().chargeHistoryDetailsTipText),
            )),
          ],
        ),
      ),
    );
  }

  void openChargeHistoryDetailScreen(
      ChargeHistoryInfo chargeHistoryInfo, BuildContext context) {
    widget.bottomHelper.clear();
    final ecoDuration =
        getDurationFromTwoTime(chargeHistoryInfo.endTime, DateTime.now())
            .split(" ");
    if (chargeHistoryInfo.chargeLocationType.toUpperCase() ==
        homeText.toUpperCase()) {
      if (chargeHistoryInfo.isEcho) {
        widget.bottomHelper.add(HistoryPageBottomDetailsHelper(
          title: OneAppString.of().ecoChargeText,
          ecoChargeIconImage: icEcoLeaf,
          ecoChargeText: OneAppString.of().ecoEarnedLeafText,
        ));
      } else if (int.parse(ecoDuration[0]) >= 30) {
        widget.bottomHelper.add(HistoryPageBottomDetailsHelper(
          title: OneAppString.of().ecoChargeText,
          trailingValue: OneAppString.of().pending,
        ));
      } else {
        widget.bottomHelper.add(HistoryPageBottomDetailsHelper(
            title: OneAppString.of().ecoChargeText,
            ecoChargeicon: Icons.info_outline,
            redirectToLearnMoreScreen: true,
            ecoChargeText: "Not eligible"));
      }
    }
    widget.bottomHelper.addAll([
      HistoryPageBottomDetailsHelper(
        title: OneAppString.of().chargeLevelText,
        startingChargeLevel: chargeHistoryInfo.socBefore,
        endingChargeLevel: chargeHistoryInfo.socAfter,
      ),
      HistoryPageBottomDetailsHelper(
        title: OneAppString.of().time,
        trailingValue: calculateTimeDifferenceWithoutDuration(
            chargeHistoryInfo.startTime, chargeHistoryInfo.endTime),
      ),
      HistoryPageBottomDetailsHelper(
        title: OneAppString.of().durationText,
        trailingValue: getDurationFromTwoTime(
                chargeHistoryInfo.startTime, chargeHistoryInfo.endTime)
            .toLowerCase(),
      ),
      HistoryPageBottomDetailsHelper(
        title: OneAppString.of().energyUsedText,
        unit: "kWh",
        trailingValue: chargeHistoryInfo.totalCharge,
      ),
    ]);
    if (chargeHistoryInfo.cdrDetails != null &&
        chargeHistoryInfo.cdrDetails!.cardLast4!.isNotEmpty) {
      widget.bottomHelper.add(HistoryPageBottomDetailsHelper(
        title: OneAppString.of().cardText,
        trailingValue: "**** ${chargeHistoryInfo.cdrDetails!.cardLast4}",
      ));
    }
    chargeHistoryDetailsScreen(context, chargeHistoryInfo);
  }
}
