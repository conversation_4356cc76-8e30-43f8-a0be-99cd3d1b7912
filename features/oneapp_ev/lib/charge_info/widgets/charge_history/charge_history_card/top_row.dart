// Dart imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/dialog/bottom_error_conformation_dialog.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';
import 'history_info.dart';

class HistoryTopRow extends ThemedStatelessWidget {
  //Get history data from the API model
  final ChargeHistoryInfo historyInfo;
  final BuildContext buildContext;
  HistoryTopRow(
      {Key? key, required this.historyInfo, required this.buildContext})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            _locationIcon(),
            Text(historyInfo.chargeLocationType, style: textStyleUtil.body4),
            Spacer(),
            _rightIcon(),
          ],
        ),
      ],
    );
  }

  Widget _rightIcon() {
    if (historyInfo.isEcho) {
      return Container(
        width: 32.w,
        height: 32.h,
        child: Center(
            child: SvgPicture.asset(
          icEcoHistory,
          height: 32.h,
          width: 32.w,
        )),
      );
    } else if (historyInfo.chargeLocationType == "Unknown") {
      return Container(
        width: 32.w,
        height: 32.h,
        child: Center(
            child: IconButton(
          icon: Icon(Icons.info_outline),
          color: colorUtil.secondary01,
          onPressed: () {
            showUnknownPopup();
          },
        )),
      );
    }
    return Container();
  }

  Widget _locationIcon() {
    String? iconName;
    switch (historyInfo.chargeLocationType.toUpperCase()) {
      case "HOME":
        iconName = homeIcon;
        break;
      case "Unknown":
        iconName = null;
        break;
      default:
        iconName = pinIcon;
    }
    if (iconName != null) {
      return Padding(
        padding: EdgeInsets.only(right: 8.0),
        child: SvgPicture.asset(
          iconName,
          colorFilter: ColorFilter.mode(
            colorUtil.tertiary00,
            BlendMode.srcIn,
          ),
          width: 24.w,
          height: 24.h,
        ),
      );
    }
    return Container();
  }

  void showUnknownPopup() {
    BottomErrorConfirmationDialog().showBottomDialog(
        buildContext,
        null,
        OneAppString.of().unKnownLocationHeadingText,
        OneAppString.of().unKnownLocationSubHeadingText,
        OneAppString.of().goToAccountSettingsText,
        OneAppString.of().closeText, () {
      loadAccountNativePage().then((_) => Navigator.maybePop(buildContext));
    },
        barrierDismissible: true,
        subHeadingText1: OneAppString.of().unKnownLocationSubHeading1Text,
        icon: Icons.info_outline,
        iconBackgroundColor: colorUtil.secondary02,
        iconColor: colorUtil.secondary01);
  }

  void showUnknownPopupSuccess() {
    BottomErrorConfirmationDialog().showBottomDialog(
        buildContext,
        icAlertIcon,
        OneAppString.of().unKnownLocationHeadingText,
        OneAppString.of().unKnownLocationSuccessMessage,
        OneAppString.of().commonOkay,
        "",
        () {},
        barrierDismissible: true,
        iconBackgroundColor: colorUtil.secondary02,
        iconColor: colorUtil.secondary01);
  }
}
