// Flutter imports:
import 'package:flutter/material.dart';

// Project imports:
import '../../../../core/shimmering/widgets/common/shimmerCircle.dart';
import '../../../../core/shimmering/widgets/common/shimmerExpandedTile.dart';
import '../../../../core/shimmering/widgets/common/shimmerRectangle.dart';
import '../../../../core/widgets/themed_widget.dart';
import 'history_shimmer_widget.dart';

class ChargeHistoryShimmerCard extends ThemedStatelessWidget {
  ChargeHistoryShimmerCard({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _chargeHistoryShimmer();
  }

  Widget _chargeHistoryShimmer() {
    return ExpandedTileShimmer(
      leftIcon: ShimmerCircle(height: 24, width: 24, borderRadius: 300),
      rightIcon: ShimmerCircle(height: 24, width: 24, borderRadius: 300),
      title: ShimmerRectangle(height: 18, width: 131, borderRadius: 3),
      cardChild: HistoryShimmerWidget(),
    );
  }
}
