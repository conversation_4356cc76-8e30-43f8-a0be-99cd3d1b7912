// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';

// Project imports:
import '../../../../core/shimmering/widgets/common/shimmerCircle.dart';
import '../../../../core/widgets/themed_widget.dart';

class HistoryShimmerWidget extends ThemedStatelessWidget {
  HistoryShimmerWidget({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.h),
      child: Container(
        decoration: BoxDecoration(
            color: colorUtil.tile05,
            borderRadius: BorderRadius.all(Radius.circular(8.r))),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.h),
          child: Column(
            children: [
              _topRow(),
              <PERSON><PERSON><PERSON><PERSON>(height: 17.h),
              _middleRow(),
              Sized<PERSON>ox(height: 17.h),
              _bottomRow()
            ],
          ),
        ),
      ),
    );
  }

  Widget _topRow() {
    return Row(
      children: [
        ShimmerCircle(height: 18, width: 18, borderRadius: 300),
        SizedBox(width: 14.w),
        shimmerRectangle(14, 54, 3),
        Spacer(),
        ShimmerCircle(height: 24, width: 24, borderRadius: 300),
      ],
    );
  }

  Widget _middleRow() {
    return Row(
      children: [
        shimmerRectangle(14, 88, 3),
        Spacer(),
        shimmerRectangle(18, 73, 3),
      ],
    );
  }

  Widget _bottomRow() {
    return Row(
      children: [
        shimmerRectangle(14, 139, 3),
        Spacer(),
        shimmerRectangle(14, 73, 3),
      ],
    );
  }
}
