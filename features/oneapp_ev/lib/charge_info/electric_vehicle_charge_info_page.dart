// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oa_network_impl/electric_vehicle/entity/charge_statistics.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/config/channel_config.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/route/navigate_service.dart';
import 'package:oneapp_common/route/route_path.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/custom_page_route.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/ev_common/evgo_complimentary_alert_util.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/vehicle_charge_info/clean_assist/vehicle_clean_assist_consent_detail_page.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/vehicle_charge_info/clean_assist/vehicle_clean_assist_graph.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/vehicle_search_charge_station_location/helper/evgo_complimentry_expiry_validation.dart';

// Project imports:
import '../core/constants.dart';
import '../core/shimmering/widgets/shimmering_range_widget.dart';
import '../core/widgets/floating_button_widget.dart';
import '../core/widgets/scrollable_page_widget.dart';
import '../log/ev_analytics_events.dart';
import '../scheduled_charging/scheduled_charging_page.dart';
import 'blocs/battery_info_bloc.dart';
import 'blocs/charge_statistics/charging_statistics_bloc.dart';
import 'blocs/clean_assist_bloc.dart';
import 'blocs/partner_details_Info_bloc.dart';
import 'blocs/wallet_details_info_bloc.dart';
import 'widgets/battery_card/battery_card.dart';
import 'widgets/battery_card/phev_battery_card.dart';
import 'widgets/battery_range.dart';
import 'widgets/charge_history/charge_history_card/history_card.dart';
import 'widgets/chargepoint_card_widget.dart';
import 'widgets/clean_assist_card_widget.dart';
import 'widgets/evgo_card_widget.dart';
import 'widgets/helper/range_info.dart';
import 'widgets/schedule_card/schedule_card.dart';
import 'widgets/statics_card/statistics_card_widget.dart';
import 'widgets/tips_widget.dart';
import 'widgets/wallet_card_wdget.dart';

class ElectricVehicleChargeInfoPage extends StatefulWidget {
  final bool? shouldStartCharging;

  ElectricVehicleChargeInfoPage({
    Key? key,
    this.shouldStartCharging,
  }) : super(key: key);

  @override
  _ElectricVehicleChargeInfoPageState createState() =>
      _ElectricVehicleChargeInfoPageState();
}

class _ElectricVehicleChargeInfoPageState
    extends State<ElectricVehicleChargeInfoPage> {
  GetIt locator = GetIt.instance;
  final colorUtil = ThemeConfig.current().colorUtil;
  final textStyleUtil = ThemeConfig.current().textStyleUtil;
  final _batteryBloc = BatteryInfoBloc();
  final _statisticsBloc = ChargeStatisticsBloc();

  @override
  void initState() {
    super.initState();
    FireBaseAnalyticsLogger.logScreenVisit(EVAnalyticsEvent.CHARGE_INFO_SCREEN);
    _batteryBloc.init(_progressHandlerCallback);
    locator.registerSingletonAsync(
        () async => await VehicleRepo().fetchWalletImages());
    _statisticsBloc.init();
    if (widget.shouldStartCharging != null &&
        widget.shouldStartCharging == true) {
      Future.microtask(
          () => _batteryBloc.startChargeRequest(_savedResponseCallback));
    }
  }

  @override
  void dispose() {
    _batteryBloc.dispose();
    _statisticsBloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (didPop) async {
        if (didPop) return;
        goBackToNative();
      },
      child: Navigator(
        onGenerateRoute: (_) => MaterialPageRoute(
          builder: (materialContext) {
            return Builder(
              builder: (builderContext) {
                return SafeArea(
                  top: true,
                  child: Container(
                    child: Column(
                      children: [
                        _AppBar(),
                        ScrollablePageWidget(
                          [
                            _BatteryRangeInfo(_batteryBloc),
                            SizedBox(height: 8.h),
                            _ChargeMeterInfo(_batteryBloc),
                            SizedBox(height: 16.h),
                            _ChargeHistory(_batteryBloc, _statisticsBloc),
                            SizedBox(height: 16.h),
                            _ScheduleCard(),
                            SizedBox(height: 16.h),
                            _ChargeStatistics(_statisticsBloc),
                            SizedBox(height: 10.h),
                            _CleanAssistCard(_batteryBloc),
                            SizedBox(height: 8.h),
                            _PublicChargingAllowed(_batteryBloc),
                            _WalletDetails(_batteryBloc),
                            SizedBox(height: 8.h),
                            _PartnerDetails(_batteryBloc),
                          ],
                        ),
                        _EVGoValidation(_batteryBloc),
                        _ChargeHelperAction(_batteryBloc),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  void _savedResponseCallback(String? toastMessage, bool isSuccess) {
    showCustomToast(
        _responseToastCustomWidget(
            toastMessage ?? OneAppString.of().loginUnableProcessYourRequest,
            isSuccess),
        3);
  }

  Widget _responseToastCustomWidget(String toastMessage, bool isSuccessful) {
    return CommonToast(
      iconColor: colorUtil.tertiary15,
      iconPath: isSuccessful ? checkIcon : closeIcon,
      avatarContainerEndColor: colorUtil.secondary01,
      avatarContainerStartColor: colorUtil.secondary01,
      containerColor: colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: colorUtil.tertiary15,
      toastMessage: toastMessage,
    );
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }
}

class _AppBar extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorUtil = ThemeConfig.current().colorUtil;
    final headerStyle = ThemeConfig.current()
        .textStyleUtil
        .subHeadline3
        .copyWith(color: colorUtil.tertiary03);
    return Stack(
      children: [
        Row(mainAxisAlignment: MainAxisAlignment.start, children: [
          Padding(
              padding: EdgeInsets.only(left: 16.w, top: 16.h),
              child: GestureDetector(
                onTap: () {
                  try {
                    goBackToNative();
                  } catch (e) {
                    FireBaseAnalyticsLogger.logErrorChannelConfig(e.toString());
                  }
                },
                child: SvgPicture.asset(
                  arrowLeftIcon,
                  height: 24.h,
                  width: 24.w,
                  colorFilter: ColorFilter.mode(
                    colorUtil.button02a,
                    BlendMode.srcIn,
                  ),
                  allowDrawingOutsideViewBox: true,
                  semanticsLabel: CLOSE_ICON,
                ),
              )),
        ]),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              OneAppString.of().chargeInfo,
              style: headerStyle,
            )
          ],
        ),
      ],
    );
  }
}

class _BatteryRangeInfo extends StatelessWidget {
  const _BatteryRangeInfo(this._batteryBloc);

  final BatteryInfoBloc _batteryBloc;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<BatteryRangeInfo?>(
      stream: _batteryBloc.batteryInfo,
      builder: (context, snapshot) {
        return snapshot.hasData
            ? BatteryRangeWidget(
                rangeInfo: snapshot.data!.range,
                isLoading: snapshot.data!.isLoading,
                isEV: _batteryBloc.isEV,
              )
            : ShimmeringRangeWidget();
      },
    );
  }
}

class _ChargeMeterInfo extends StatelessWidget {
  const _ChargeMeterInfo(this._batteryBloc);

  final BatteryInfoBloc _batteryBloc;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ChargeMeterInfo?>(
      stream: _batteryBloc.chargeMeterInfo,
      builder: (context, snapshot) {
        return (snapshot.hasData && snapshot.data != null)
            ? snapshot.data?.vehicleType == VehicleType.EV
                ? Semantics(
                    container: true,
                    child: BatteryCard(
                      batteryInfo: BatteryInfo(
                        chargingPercentage: snapshot.data?.chargeRemaining ?? 0,
                        isCharging: snapshot.data!.isCharging,
                        isPlugConnected: snapshot.data!.isPluggedIn,
                        estimatedTime: snapshot.data?.estimatedTime,
                        nextScheduleTime: snapshot.data?.nextScheduleTime,
                      ),
                      isLoading: snapshot.data!.isLoading,
                    ),
                  )
                : snapshot.data?.vehicleType == VehicleType.EV_PHP
                    ? Semantics(
                        container: true,
                        child: PHevBatteryCard(
                            batteryInfo: PHevBatteryInfo(
                          chargingPercentage:
                              snapshot.data?.chargeRemaining ?? 0,
                          fuelLevel: snapshot.data!.fuelLevel ?? null,
                          isCharging: snapshot.data!.isCharging,
                          isPlugConnected: snapshot.data!.isPluggedIn,
                          estimatedTime: snapshot.data?.estimatedTime,
                          nextScheduleTime: snapshot.data?.nextScheduleTime,
                          evRangeInfo: RangeInfo(
                              range: snapshot.data!.evRangeInfo!.range,
                              unit: snapshot.data!.evRangeInfo!.unit),
                          gasRangeInfo: RangeInfo(
                              range: snapshot.data?.gasRangeInfo?.range ?? 0,
                              unit: snapshot.data?.gasRangeInfo?.unit ?? ""),
                          evMaxRangeInfo: RangeInfo(
                              range: snapshot.data?.evRangeInfo?.range ?? 0,
                              unit: snapshot.data?.evRangeInfo?.unit ?? ""),
                          gasMaxRangeInfo: RangeInfo(
                              range: snapshot.data?.gasRangeInfo?.range ?? 0,
                              unit: snapshot.data?.gasRangeInfo?.unit ?? ""),
                        )),
                      )
                    : const SizedBox()
            : const SizedBox();
      },
    );
  }
}

class _ChargeHistory extends StatelessWidget {
  const _ChargeHistory(this._batteryBloc, this._statisticsBloc);

  final BatteryInfoBloc _batteryBloc;
  final ChargeStatisticsBloc _statisticsBloc;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ChargeStatisticsEntity?>(
      stream: _statisticsBloc.chargeStatisticsData,
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          _batteryBloc.formLastChargeObject(snapshot.data!.lastCharge ?? null);
          return ChargeHistoryCard(
            lastCharge: _batteryBloc.lastChargeObj,
            isLoading: false,
          );
        } else {
          return ChargeHistoryCard(
            lastCharge: null,
            isLoading: true,
          );
        }
      },
    );
  }
}

class _CleanAssistCard extends StatelessWidget {
  const _CleanAssistCard(this.batteryInfoBloc);

  final BatteryInfoBloc batteryInfoBloc;

  @override
  Widget build(BuildContext context) {
    final colorUtil = ThemeConfig.current().colorUtil;
    return StreamBuilder<bool>(
      stream: batteryInfoBloc.cleanAssistBloc.isLCFSEligible,
      builder: (context, snapshot) {
        if (snapshot.data != null && snapshot.data == true) {
          return StreamBuilder<CleanAssistDetails>(
              stream: batteryInfoBloc.cleanAssistBloc.cleanAssistDetails,
              builder: (context, snapshot) {
                return snapshot.hasData
                    ? CleanAssistCardWidget(
                        title: OneAppString.of().cleanAssist,
                        subTitle: OneAppString.of().renewableEnergyCredit,
                        loadingShimmer: snapshot.data!.isLoading,
                        isLCFSShowGraph: snapshot.data!.isLCFSShowGraph,
                        onPress: () {
                          if (snapshot.data!.isLCFSShowGraph) {
                            // Show graph
                            FireBaseAnalyticsLogger.logMarketingGroupEvent(
                                VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                                childEventName:
                                    EVAnalyticsEvent.CLEAN_ASSIST_VIEW);
                            Navigator.of(context).push(CustomPageRoute(
                                page: Container(
                                    color: colorUtil.tile01,
                                    child: VehicleCleanAssistGraph())));
                          } else {
                            // Enroll for CA
                            FireBaseAnalyticsLogger.logMarketingGroupEvent(
                                VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                                childEventName:
                                    EVAnalyticsEvent.CLEAN_ASSIST_ENROLL);
                            FireBaseAnalyticsLogger.logMarketingGroupEvent(
                              VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                              childEventName: VehicleAnalyticsEvent
                                  .VEHICLE_EV_CLEAN_ASSIST_ENROLL,
                            );

                            Navigator.of(context)
                                .push(CustomPageRoute(
                                    page:
                                        VehicleCleanAssistConsentDetailPage()))
                                .then((value) {
                              //reload charge info page
                            });
                          }
                        },
                      )
                    : const SizedBox();
              });
        } else {
          return const SizedBox();
        }
      },
    );
  }
}

class _ScheduleCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorUtil = ThemeConfig.current().colorUtil;
    return ScheduleCard(
      isLoading: false,
      onPress: () {
        FireBaseAnalyticsLogger.logMarketingGroupEvent(
            VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
            childEventName: EVAnalyticsEvent.SCHEDULE_TILE);
        return showMaterialModalBottomSheet(
          expand: true,
          isDismissible: true,
          context: context,
          useRootNavigator: true,
          backgroundColor: colorUtil.tile01,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(CARD_RADIUS),
            ),
          ),
          builder: (context) => SafeArea(
            top: true,
            bottom: true,
            child: Column(
              children: [
                SwipeBarIcon(),
                Expanded(child: ScheduledChargingPage()),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _ChargeStatistics extends StatelessWidget {
  const _ChargeStatistics(this._statisticsBloc);

  final ChargeStatisticsBloc _statisticsBloc;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ChargeStatisticsEntity?>(
      stream: _statisticsBloc.chargeStatisticsData,
      builder: (BuildContext context, chargeStatisticsSnapshot) {
        final tipText = _statisticsBloc.calculateAndGetTipsText();
        return chargeStatisticsSnapshot.hasData &&
                chargeStatisticsSnapshot.data != null
            ? StatisticsCard(
                info: StatisticsCardInfo(
                  bloc: _statisticsBloc,
                  achievedLeaves: _statisticsBloc.getAchievedLeaves(),
                  bars: _statisticsBloc.barChatData,
                  tips: _statisticsBloc.tipsWidget
                      ? Tips(
                          text: tipText?['descriptionText'] ?? '',
                          highlightText: tipText?['highlightText'] ?? '')
                      : null,
                ),
                isLoading: false,
              )
            : StatisticsCard(
                info: null,
                isLoading: true,
              );
      },
    );
  }
}

class _PublicChargingAllowed extends StatelessWidget {
  const _PublicChargingAllowed(this._batteryBloc);

  final BatteryInfoBloc _batteryBloc;

  @override
  Widget build(BuildContext context) {
    final colorUtil = ThemeConfig.current().colorUtil;
    final textUtil = ThemeConfig.current().textStyleUtil;
    return StreamBuilder<bool>(
      stream: _batteryBloc.isPublicChargingControlAllowed,
      builder: (context, snapshot) {
        return (snapshot.hasData && snapshot.data == true)
            ? Column(
                children: [
                  SizedBox(height: 24.h),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 16.h),
                      child: Text(
                        OneAppString.of().chargeSettingsText,
                        style: textUtil.subHeadline4
                            .copyWith(color: colorUtil.tertiary03),
                      ),
                    ),
                  ),
                ],
              )
            : const SizedBox();
      },
    );
  }
}

class _WalletDetails extends StatelessWidget {
  const _WalletDetails(this._batteryBloc);

  final BatteryInfoBloc _batteryBloc;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<WalletDetails>(
      stream: _batteryBloc.walletDetailsInfoBloc.walletDetail,
      builder: (context, snapshot) {
        return snapshot.hasData &&
                (snapshot.data != null && snapshot.data!.walletPresence)
            ? WalletCardWidget(
                loadingShimmer: snapshot.data!.isLoading,
                isWalletPresence:
                    snapshot.data?.walletDetail?.paymentMethodDefault == true
                        ? true
                        : false,
                card: snapshot.data?.walletDetail?.card,
                walletDetailsInfoBloc: _batteryBloc.walletDetailsInfoBloc,
              )
            : const SizedBox();
      },
    );
  }
}

class _PartnerDetails extends StatelessWidget {
  const _PartnerDetails(this._batteryBloc);

  final BatteryInfoBloc _batteryBloc;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: _batteryBloc.isPublicChargingControlAllowed,
      builder: (context, snapshot) {
        return (snapshot.hasData && snapshot.data == true)
            ? StreamBuilder<PartnerDetails?>(
                stream: _batteryBloc.partnerDetailsInfoBloc.partnerDetails,
                builder: (context, snapshot) {
                  return snapshot.hasData
                      ? Column(children: [
                          ChargePointCardWidget(
                            loadingShimmer: snapshot.data!.isLoading,
                            isRegistered:
                                snapshot.data!.chargePoint.isRegistered,
                            partnerBloc: _batteryBloc.partnerDetailsInfoBloc,
                          ),
                          SizedBox(height: 8.h),
                          Visibility(
                            visible:
                                _batteryBloc.partnerDetailsInfoBloc.isToyata &&
                                    isFeatureEnabled(EV_PARTNER_EVGO,
                                        _batteryBloc.vehicleItem?.features) &&
                                    snapshot.data?.evgoExpired == false,
                            child: EvgoCardWidget(
                                subTitle: snapshot
                                            .data?.evgoExpiryDate.isNotEmpty ==
                                        true
                                    ? OneAppString.of().evgoExpiryDate(snapshot
                                            .data?.evgoExpiryDate ??
                                        EVGOComplimentaryUtil.evgoEmptyString)
                                    : OneAppString.of().evgoFreeOneYear,
                                loadingShimmer: snapshot.data!.isLoading,
                                isRegistered: snapshot.data!.evgo.isRegistered,
                                partnerBloc:
                                    _batteryBloc.partnerDetailsInfoBloc),
                          ),
                        ])
                      : const SizedBox();
                })
            : const SizedBox();
      },
    );
  }
}

class _EVGoValidation extends StatelessWidget {
  const _EVGoValidation(this._batteryBloc);

  final BatteryInfoBloc _batteryBloc;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<PartnerDetails?>(
      stream: _batteryBloc.partnerDetailsInfoBloc.partnerDetails,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data != null) {
          return EVGOComplimentaryExpiryValidation();
        } else {
          return const SizedBox();
        }
      },
    );
  }
}

class _ChargeHelperAction extends StatelessWidget {
  const _ChargeHelperAction(this._batteryBloc);

  final BatteryInfoBloc _batteryBloc;

  @override
  Widget build(BuildContext context) {
    final colorUtil = ThemeConfig.current().colorUtil;
    return StreamBuilder<ChargeButtonHelper>(
      stream: _batteryBloc.chargeButton,
      builder: (context, snapshot) {
        if (snapshot.hasData &&
            snapshot.connectionState == ConnectionState.active) {
          if (snapshot.data!.isCharging == false) {
            return FloatingButtonWidget(
              textColor: Global.getInstance().isDarkTheme
                  ? Colors.black
                  : Colors.white,
              backgroundColor: Global.getInstance().isDarkTheme
                  ? Colors.white
                  : Colors.black,
              text: snapshot.data!.buttonText,
              isEnabled: true,
              disabledBackgroundColor: colorUtil.button02d,
              disabledButtonTextColor: colorUtil.button05a,
              onPressed: () {
                if (snapshot.data?.buttonText ==
                    OneAppString.of().startCharging) {
                  FireBaseAnalyticsLogger.logMarketingGroupEvent(
                      VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                      childEventName:
                          EVAnalyticsEvent.FIND_STATIONS_FROM_CHARGE_INFO);
                }

                _findActions(snapshot.data!.evScreen);
              },
            );
          } else {
            return Container();
          }
        }
        return Container();
      },
    );
  }

  void _findActions(CHARGING_EV_SCREEN ev_screens) {
    switch (ev_screens) {
      case CHARGING_EV_SCREEN.CHARGING_SCREEN:
        // Call Start Charging API [Home Charging]
        _batteryBloc.startChargeRequest(_savedResponseCallback);
        break;
      case CHARGING_EV_SCREEN.NEARBY_STATION:
        VehicleRepo()
            .fetchIsEvPublicChargingEnabled()
            .then((isPublicChargingEnabled) {
          if (isPublicChargingEnabled) {
            NavigateService.pushNamedRoute(
              RoutePath.VEHICLE_SEARCH_CHARGE_STATION_LOCATION,
              arguments: {
                "publicCharging": isPublicChargingEnabled,
                "isFromNative": false
              },
            );
          } else {
            NavigateService.pushNamedRoute(
                RoutePath.VEHICLE_SEARCH_CHARGE_STATION_LOCATION,
                arguments: {"isFromNative": false});
          }
        });
        break;
    }
  }

  void _savedResponseCallback(String? toastMessage, bool isSuccess) {
    showCustomToast(
        _responseToastCustomWidget(
            toastMessage ?? OneAppString.of().loginUnableProcessYourRequest,
            isSuccess),
        3);
  }

  Widget _responseToastCustomWidget(String toastMessage, bool isSuccessful) {
    final colorUtil = ThemeConfig.current().colorUtil;
    return CommonToast(
      iconColor: colorUtil.tertiary15,
      iconPath: isSuccessful ? checkIcon : closeIcon,
      avatarContainerEndColor: colorUtil.secondary01,
      avatarContainerStartColor: colorUtil.secondary01,
      containerColor: colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: colorUtil.tertiary15,
      toastMessage: toastMessage,
    );
  }
}
