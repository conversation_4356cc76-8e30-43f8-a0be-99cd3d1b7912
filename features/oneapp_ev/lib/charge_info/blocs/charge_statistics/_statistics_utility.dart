// Flutter imports:

// Package imports:
import 'package:oa_network_impl/electric_vehicle/entity/charge_statistics.dart';

// Project imports:
import '../../widgets/statics_card/_bar_chart.dart';
import '_extensions.dart';

enum ReportType { MONTHLY, YEARLY }

class StatisticsUtility {
  static List<MonthlyReport> prepareAllMonthsData(
      {List<String> customMonthsArray = const [],
      List<MonthlyReport>? actualMonthlyReport = const [],
      List<int> next3MonthsIds = const []}) {
    List<MonthlyReport> preparedReport = [];
    customMonthsArray.forEach((String monthName) {
      final MonthlyReport defaultMonthlyReport =
          MonthlyReport(monthName: monthName, totalChargeKwhr: 0);
      final foundReport = actualMonthlyReport?.firstWhere((element) {
        return (element.monthName?.toUpperCase() == monthName &&
            !next3MonthsIds
                .contains(element.monthName?.toUpperCase().month.id));
      }, orElse: () => defaultMonthlyReport);
      if (foundReport != null) preparedReport.add(foundReport);
    });
    return preparedReport;
  }

  static String getMonthLabelByPosition(int monthPosition) {
    monthPosition = monthPosition >= 1 ? monthPosition : 1;
    return allMonths[monthPosition - 1].month.label;
  }

  static List<BarData> getNonZeroBarData(List<BarData> barData) {
    final List<BarData> nonZeroValues = [];
    nonZeroValues.addAll(barData);
    nonZeroValues.retainWhere((element) => element.value > 0);
    return nonZeroValues;
  }

  static List<String> get allMonths => [
        "JAN",
        "FEB",
        "MAR",
        "APR",
        "MAY",
        "JUN",
        "JUL",
        "AUG",
        "SEP",
        "OCT",
        "NOV",
        "DEC"
      ];
}
