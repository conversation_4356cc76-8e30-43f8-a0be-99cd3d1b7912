// Project imports:
import '_month_meta_data.dart';

extension removeTrailZero on double {
  String get displayValue {
    RegExp removeZeroRegex = RegExp(r'([.]*0)(?!.*\d)');
    double doubleValue = this;
    return (doubleValue >= 0
        ? doubleValue.ceil().toStringAsFixed(2).replaceAll(removeZeroRegex, '')
        : '0');
  }
}

extension monthExtn on String {
  MonthMetadata get month {
    switch (this.toUpperCase()) {
      case "JAN":
        return MonthMetadata(label: 'January', totalDays: 31, id: 1);
      case "FEB":
        return MonthMetadata(label: 'February', totalDays: 28, id: 2);
      case "MAR":
        return MonthMetadata(label: 'March', totalDays: 30, id: 3);
      case "APR":
        return MonthMetadata(label: 'April', totalDays: 30, id: 4);
      case "MAY":
        return MonthMetadata(label: 'May', totalDays: 31, id: 5);
      case "JUN":
        return MonthMetadata(label: 'June', totalDays: 30, id: 6);
      case "JUL":
        return MonthMetadata(label: 'July', totalDays: 31, id: 7);
      case "AUG":
        return MonthMetadata(label: 'August', totalDays: 31, id: 8);
      case "SEP":
        return MonthMetadata(label: 'September', totalDays: 30, id: 9);
      case "OCT":
        return MonthMetadata(label: 'October', totalDays: 31, id: 10);
      case "NOV":
        return MonthMetadata(label: 'November', totalDays: 30, id: 11);
      case "DEC":
        return MonthMetadata(label: 'December', totalDays: 31, id: 12);
      default:
        return MonthMetadata(label: '', totalDays: 0, id: 0);
    }
  }
}

extension monthNameExt on int {
  String get monthName {
    switch (this) {
      case 1:
        return "JAN";
      case 2:
        return "FEB";
      case 3:
        return "MAR";
      case 4:
        return "APR";
      case 5:
        return "MAY";
      case 6:
        return "JUN";
      case 7:
        return "JUL";
      case 8:
        return "AUG";
      case 9:
        return "SEP";
      case 10:
        return "OCT";
      case 11:
        return "NOV";
      case 12:
        return "DEC";
      default:
        return '';
    }
  }
}
