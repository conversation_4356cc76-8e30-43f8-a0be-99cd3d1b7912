// Flutter imports:

// Package imports:
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/electric_vehicle/entity/charge_statistics.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

// Project imports:
import '../../widgets/statics_card/_bar_chart.dart';
import '_extensions.dart';
import '_statistics_utility.dart';

class ChargeStatisticsBloc extends BlocBase {
  final _evApi = APIClients.evApiClient;

  late final ReportType reportType;

  String _tipUsageDescription = '';

  bool hideHealthImpact = false;

  List<BarData> barChatData = [];

  bool tipsWidget = false;

  MonthlyReport? selectedMonthRecord;

  Stream<ChargeStatisticsEntity?> get chargeStatisticsData =>
      _chargeStatisticsData.stream;
  final _chargeStatisticsData = BehaviorSubject<ChargeStatisticsEntity?>();

  String formattedMMYYYY = OneAppString.of().monthCaption;
  String formattedMM = '';
  int intMM = 0;
  int intYYYY = 0;
  int lastMonthIndex = 0;
  int prevMonthIndex = 0;
  MonthlyReport? lastMonthReport;
  MonthlyReport? prevMonthReport;

  List<String> customMonthNamesArray = [];
  List<int> customMonthIdsArray = [];

  Stream<String?> get selectedMonthMMYYYY => _selectedMonthMMYYYY.stream;
  final _selectedMonthMMYYYY = BehaviorSubject<String?>();

  String get selectedMonthYear =>
      (_selectedMonthMMYYYY.value ?? '').replaceAll('_', ' ');

  String get totalEmissionForSelectedMonth =>
      selectedMonthRecord?.totalCo2?.toDouble().displayValue ?? '0';

  int get leavesEarnedForSelectedMonth {
    int leafCount = selectedMonthRecord?.leafCount?.toInt() ?? 0;
    return leafCount >= 0 ? leafCount : 0;
  }

  int get totalDaysForSelectedMonth =>
      selectedMonthRecord?.monthName?.month.totalDays ?? 0;

  bool isSelectedMonthApiLoading = false;

  String get healthImpactReductionBy =>
      selectedMonthRecord?.healthDollarPercentage?.toDouble().displayValue ??
      '0';

  int get totalTreesPlantedForSelectedMonth {
    int treesCount =
        selectedMonthRecord?.totalCo2EquivalentTreesPlanted?.toInt() ?? 0;
    return treesCount >= 0 ? treesCount : 0;
  }

  int get totalYearsOfTreesPlanted => 10;

  void init() {
    fetchChargeStatisticsYearlyData();
    _chargeStatisticsData.listen((value) {
      updateDefaultMothData(value);
    });
  }

  Future<void> fetchChargeStatisticsYearlyData() async {
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    final commonResponse = await _evApi.fetchEVWattChargeStatistics(
      guid: Global.getInstance().guid ?? '',
      brand: vehicleItem?.brand ?? '',
      generation: vehicleItem?.generation ?? '',
      vin: Global.getInstance().vin ?? '',
      reportType: 'year_week',
    );
    if (commonResponse.error?.errorMessage != null) {
      _chargeStatisticsData.add(null);
    }
    prepareBarChartData(commonResponse.response?.monthlyReports ?? []);
    _chargeStatisticsData.add(commonResponse.response);
  }

  void updateDefaultMothData(ChargeStatisticsEntity? statisticsData) {
    if ((statisticsData?.monthlyReports?.isNotEmpty ?? false)) {
      selectedMonthRecord = statisticsData?.monthlyReports?.first;
      var now = DateTime.now();
      var statisticsMonth =
          selectedMonthRecord?.monthName?.toString().month.id ?? 0;
      var year = now.month < statisticsMonth ? now.year - 1 : now.year;
      setSelectedMonthMMYYYY(year, statisticsMonth);
    }
  }

  void updateSelectedMonthYear({required int year, required int month}) {
    setSelectedMonthMMYYYY(year, month);
    fetchChargeStatisticsMonthlyData();
  }

  void setSelectedMonthMMYYYY(int year, int month) {
    formattedMMYYYY =
        '${StatisticsUtility.getMonthLabelByPosition(month)} $year';
    formattedMM = '${StatisticsUtility.getMonthLabelByPosition(month)}';
    intYYYY = year;
    intMM = month;
    _selectedMonthMMYYYY.add('${month.toString().padLeft(2, '0')}$year');
  }

  Future<void> fetchChargeStatisticsMonthlyData() async {
    isSelectedMonthApiLoading = true;
    _chargeStatisticsData.add(null);
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    final commonResponse = await _evApi.fetchEVWattChargeStatistics(
      guid: Global.getInstance().guid ?? '',
      brand: vehicleItem?.brand ?? '',
      generation: vehicleItem?.generation ?? '',
      vin: Global.getInstance().vin ?? '',
      reportType: 'monthly',
      yearMonth: _selectedMonthMMYYYY.value?.replaceAll('_', ''),
    );
    isSelectedMonthApiLoading = false;
    if (commonResponse.error?.errorMessage != null) {
      _chargeStatisticsData.add(null);
    }

    if (commonResponse.response?.monthlyReports?.isNotEmpty ?? false) {
      selectedMonthRecord = commonResponse.response?.monthlyReports?.first;
    }
    hideHealthImpact = selectedMonthRecord?.healthDollarPercentage == null;
    _chargeStatisticsData.add(commonResponse.response);
  }

  int getAchievedLeaves() {
    return _chargeStatisticsData.value?.currentWeekReport?.leafCount ?? 0;
  }

  void prepareBarChartData(List<MonthlyReport>? monthlyReports) async {
    if (monthlyReports == null || monthlyReports.isEmpty) {
      return;
    }
    lastMonthReport = monthlyReports.first;
    int lastMonthId = lastMonthReport!.monthName!.month.id;

    if (monthlyReports.length > 1) {
      prevMonthReport = monthlyReports[1];
    }

    final List<BarData> chartBarData = [];
    barChatData = [];
    List<int> next3MonthsIds = [];
    List<int> last8MonthsIds = [];

    next3MonthsIds.add(lastMonthId + 1);
    next3MonthsIds.add(lastMonthId + 2);
    next3MonthsIds.add(lastMonthId + 3);
    next3MonthsIds.asMap().forEach((key, element) {
      if (element > 12) {
        int newMonthVal = element - 12;
        next3MonthsIds.removeAt(key);
        next3MonthsIds.insert(key, newMonthVal);
      }
    });

    int i = 0;
    last8MonthsIds = [];
    while (i++ < 8) {
      last8MonthsIds.add(lastMonthId - i);
    }
    last8MonthsIds.asMap().forEach((key, element) {
      if (element <= 0) {
        int newMonthVal = 12 - element.abs();
        last8MonthsIds.removeAt(key);
        last8MonthsIds.insert(key, newMonthVal);
      }
    });

    customMonthIdsArray.addAll(last8MonthsIds.reversed);
    customMonthIdsArray.add(lastMonthId);
    customMonthIdsArray.addAll(next3MonthsIds);

    customMonthIdsArray.forEach((element) {
      customMonthNamesArray.add(element.monthName);
    });

    StatisticsUtility.prepareAllMonthsData(
            customMonthsArray: customMonthNamesArray,
            actualMonthlyReport: monthlyReports,
            next3MonthsIds: next3MonthsIds)
        .asMap()
        .forEach((int key, MonthlyReport eachMonthReport) {
      final monthName = eachMonthReport.monthName ?? '';
      chartBarData.add(BarData(
        value: eachMonthReport.totalChargeKwhr?.toDouble() ?? 0,
        label: monthName,
        highlight: eachMonthReport.monthName == lastMonthReport?.monthName,
      ));
    });
    barChatData.addAll(chartBarData);
    tipsWidget = StatisticsUtility.getNonZeroBarData(chartBarData).length >= 2;
    calculateUsageDetails();
  }

  Map<String, String>? calculateAndGetTipsText() {
    if (prevMonthReport != null && lastMonthReport != null) {
      calculateUsageDetails();

      String lastMonthName = "";
      String prevMonthName = "";
      lastMonthName = lastMonthReport!.monthName!.month.label;
      prevMonthName = prevMonthReport!.monthName!.month.label;
      return {
        'descriptionText': OneAppString.of().tipUsageDescriptionText(
            lastMonthName, _tipUsageDescription, prevMonthName),
        'highlightText': '$_tipUsageDescription'
      };
    }
    return null;
  }

  void calculateUsageDetails() {
    if (prevMonthReport != null && lastMonthReport != null) {
      num percentage = ((prevMonthReport!.totalChargeKwhr! -
              lastMonthReport!.totalChargeKwhr!) /
          prevMonthReport!.totalChargeKwhr! *
          100);
      final moreOrLessText = percentage.isNegative ? 'more' : 'less';
      _tipUsageDescription = '${percentage.toInt().abs()}% $moreOrLessText';
    } else {
      tipsWidget = false;
    }
  }

  @override
  void dispose() {
    _chargeStatisticsData.close();
    _selectedMonthMMYYYY.close();
  }
}
