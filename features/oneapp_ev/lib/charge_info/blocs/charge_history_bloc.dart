// Dart imports:
import 'dart:developer';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:intl/intl.dart';
import 'package:oa_network_impl/api_clients.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/entity/profile_detail_entity.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/vehicle_search_charge_station_location/ev_driver_account_enrollment/enrolment_api_wrapper.dart';
import 'package:vehicle_module/ui/vehicle_finance/tfs/features/core/utils/utils.dart';

// Project imports:
import '../../core/constants.dart';
import '../../log/ev_analytics_events.dart';
import '../models/charge_history_filter_helper.dart';
import '../widgets/charge_history/charge_history_card/ charge_history_error_screen.dart';
import '../widgets/charge_history/charge_history_card/history_info.dart';
import '../widgets/statics_card/yearmonth_lib/yearmonth_picker_bloc.dart';

class ChargeHistoryBloc extends BlocBase {
  final _api = APIClientConfig.oneAppClient;
  final _evApi = APIClients.evApiClient;
  bool isAddressNull = true;
  String chargingType = "";
  List<FilterCardHelper> selectedFilter = [];
  bool isApiCalled = false;
  bool isMonthSelected = false;
  String? startDateString;
  String? endDateString;
  Stream<List<ChargeHistoryInfo>?> get chargeHistoryInfo =>
      _chargeHistoryInfo.stream;
  final _chargeHistoryInfo = BehaviorSubject<List<ChargeHistoryInfo>?>();

  Stream<List<FilterCardHelper>?> get filterCardList => _filterCardList.stream;
  final _filterCardList = BehaviorSubject<List<FilterCardHelper>?>();

  Stream<ChargeHistoryErrorHelper?> get chargeHistoryListError =>
      _chargeHistoryListError.stream;
  final _chargeHistoryListError = BehaviorSubject<ChargeHistoryErrorHelper?>();

  void init() {
    selectedFilter.addAll(filterHelper);
    _filterCardList.sink.add(selectedFilter);
    fetchEvChargeHistoryList();
    getOCPRProfileDetails();
  }

  @override
  void dispose() {
    _chargeHistoryInfo.close();
    _filterCardList.close();
    _chargeHistoryListError.close();
  }

  void fetchEvChargeHistoryList({
    String? startDate,
    String? endDate,
    String? chargingTypeInfo,
  }) async {
    String? startDateString = startDate;
    String? endDateString = endDate;
    if (startDate == null && endDate == null) {
      startDateString = '';
      endDateString = '';
    }
    isApiCalled = true;
    final httpResponse = await _evApi.getEvChargeHistoryList(
        startDate: startDateString ?? "",
        endDate: endDateString ?? "",
        chargingType: chargingTypeInfo ?? "",
        vin: Global.getInstance().vin ?? "",
        guid: Global.getInstance().guid ?? '');
    isApiCalled = false;
    if (httpResponse.error != null) {
      log(httpResponse.error.toString());
      _chargeHistoryInfo.sink.add(null);
      addErrorScreen();
      return;
    }
    final chargingSession = httpResponse.response!.chargingSessions;
    if (chargingSession.isNotEmpty) {
      List<ChargeHistoryInfo> chargeInfo = [];
      chargingSession.forEach((val) {
        String partnerNameVal =
            val.cdrDetails != null ? val.cdrDetails?.partnerName ?? "" : "";
        String chargeLocationTypeVal = val.chargeLocationType ?? "";

        ChargeHistoryInfo historyInfo = ChargeHistoryInfo(
          chargeLocationType: partnerNameVal.isNotEmpty
              ? partnerNameVal.capitalize()
              : chargeLocationTypeVal.isNotEmpty
                  ? chargeLocationTypeVal.capitalize()
                  : OneAppString.of().publicText,
          startTime: DateTime.parse(val.startTime ?? "${DateTime.now()}"),
          endTime: DateTime.parse(val.endTime ?? "${DateTime.now()}"),
          socBefore: val.socBeforeCharging ?? "0%",
          socAfter: val.socAfterCharging ?? "0%",
          totalCharge: val.totalChargeKwhr ?? "0",
          isEcho:
              val.watttimeDetails?.chargeClasification == "echo" ? true : false,
          cdrDetails: val.cdrDetails != null
              ? CdrDetailsInfo(
                  cardLast4: val.cdrDetails?.cardLast4,
                  partnerName: val.cdrDetails?.partnerName,
                  totalAmount: val.cdrDetails?.totalAmount,
                )
              : null,
          latitude: val.latitude ?? "",
          longitude: val.longitude ?? "",
          address: val.address,
        );
        chargeInfo.add(historyInfo);
      });
      _chargeHistoryInfo.sink.add(chargeInfo);
    } else {
      _chargeHistoryInfo.sink.add(null);
      addErrorScreen();
    }
  }

  void filterTapCallBack({required int index, MonthYearModel? monthYearModel}) {
    if (monthYearModel != null) {
      FireBaseAnalyticsLogger.logMarketingGroupEvent(
          VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
          childEventName: EVAnalyticsEvent.CHARGE_HISTORY_DATE_FILTER);
      int month = int.parse(monthYearModel.month);
      int year = int.parse(monthYearModel.year);
      selectedFilter[index].initialMonth = month;
      selectedFilter[index].initialYear = year;
      selectedFilter[index].labelText =
          "${monthsArray[month - 1].toUpperCase()} ${monthYearModel.year}";
      var monthStartDate = DateTime.utc(year, month, 1);
      var monthEndDate = DateTime.utc(year, month + 1, 0);
      startDateString =
          DateFormat('yyyy-MM-dd').format(monthStartDate).toString();
      endDateString = DateFormat('yyyy-MM-dd').format(monthEndDate).toString();
      isMonthSelected = true;
      selectedFilter[index].isSelected = false;
    }
    if (index == 0 && monthYearModel == null) {
      selectedFilter[index].isSelected = true;
    }
    _filterCardList.sink.add([]);
    selectedFilter[index].isSelected = !selectedFilter[index].isSelected;
    if (index == 0 && selectedFilter[index].isSelected == false) {
      selectedFilter[index].labelText = "Month";
      startDateString = null;
      endDateString = null;
      isMonthSelected = false;
      selectedFilter[index].initialMonth = 0;
      selectedFilter[index].initialYear = 0;
    }
    if (index != 0 && selectedFilter[index].isSelected == true) {
      chargingType = selectedFilter[index].labelText.toLowerCase();
      for (int i = 1; i < selectedFilter.length; i++) {
        if (i != index) {
          selectedFilter[i].isSelected = false;
        }
      }
    }
    if (index != 0 && selectedFilter[index].isSelected == false) {
      for (int i = 1; i < selectedFilter.length; i++) {
        if (selectedFilter[i].isSelected == false) {
          chargingType = "";
        }
      }
    }
    fetchEvChargeHistoryList(
        startDate: startDateString ?? "",
        endDate: endDateString ?? "",
        chargingTypeInfo: chargingType);
    _filterCardList.sink.add(selectedFilter);
  }

  void clearFilterList() {
    _filterCardList.sink.add([]);
    selectedFilter.clear();
    for (int i = 0; i < filterHelper.length; i++) {
      if (i == 0) {
        filterHelper[i].labelText = "Month";
      }
      filterHelper[i].isSelected = false;
    }
    selectedFilter = filterHelper;
    _filterCardList.sink.add(selectedFilter);
  }

  void addErrorScreen() {
    _chargeHistoryListError.sink.add(null);
    if (chargingType.toLowerCase() == homeText.toLowerCase() && isAddressNull) {
      _chargeHistoryListError.sink.add(ChargeHistoryErrorHelper(
          icon: Icons.location_on_outlined,
          heading: OneAppString.of().noHomeAddressHeading,
          subHeading: OneAppString.of().noHomeAddressSubHeading,
          showGotoProfile: isAddressNull));
    } else if (isMonthSelected) {
      _chargeHistoryListError.sink.add(ChargeHistoryErrorHelper(
          svgImage: icNoChargeSession,
          heading: OneAppString.of().noChargingSessionHeading,
          subHeading: OneAppString.of().noChargingSessionSubHeading));
    } else {
      _chargeHistoryListError.sink.add(ChargeHistoryErrorHelper(
          svgImage: icNoChargeHistory,
          heading: OneAppString.of().noChargeHistoryHeading,
          subHeading: OneAppString.of().noChargeHistorySubHeading));
    }
  }

  Future<void> getOCPRProfileDetails() async {
    if (isAddressNull) {
      final commonResponse = await _api.fetchProfileDetails("application/json",
          Global.getInstance().correlationId, Global.getInstance().appBrand);
      final payload = commonResponse.response!.payload!;
      if (payload.customer != null) {
        Customer? customer = payload.customer;
        Addresses? address = EvEnrolmentAPI.getOCPRAddress(customer: customer!);

        if (address != null) {
          isAddressNull = false;
          addErrorScreen();
        }
      }
    }
  }
}
