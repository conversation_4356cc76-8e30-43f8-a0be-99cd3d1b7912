// Package imports:
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/electric_vehicle/entity/ev_near_by_charge_station_location_entity.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/ev_common/evgo_complimentary_alert_util.dart';

// Project imports:
import '../../core/ev_util.dart';

class PartnerDetails {
  final bool isLoading;
  ChargePointInfo chargePoint;
  EvgoInfo evgo;
  final bool evgoExpiredSoon;
  final bool evgoExpired;
  final bool isWalletSetupDone;
  final String evgoExpiryDate;
  PartnerDetails(
      {required this.isLoading,
      required this.chargePoint,
      required this.evgo,
      required this.evgoExpiredSoon,
      required this.evgoExpired,
      required this.isWalletSetupDone,
      required this.evgoExpiryDate});
}

class ChargePointInfo {
  final bool isRegistered;
  ChargePointInfo({
    required this.isRegistered,
  });
}

class EvgoInfo {
  final bool isRegistered;
  EvgoInfo({
    required this.isRegistered,
  });
}

class PartnerDetailsInfoBloc extends BlocBase {
  final _evApi = APIClients.evApiClient;
  final partnerNameValues =
      EnumValues({"chargepoint": "ChargePoint", "evgo": "EVgo"});
  bool isToyata = true;
  Stream<PartnerDetails?> get partnerDetails => _partnerDetails.stream;
  final _partnerDetails = BehaviorSubject<PartnerDetails?>();

  PartnerDetailsInfoBloc() {
    fetchPartnerInfo();
  }

  Future<void> fetchPartnerInfo() async {
    final vehicleInfo = await VehicleRepo().cachedGlobalVehicleInfo();
    if (vehicleInfo != null) {
      if (vehicleInfo.make.toLowerCase() == makeLexus) {
        isToyata = false;
      }
      _partnerDetails.sink.add(PartnerDetails(
          isLoading: true,
          chargePoint: ChargePointInfo(isRegistered: false),
          evgo: EvgoInfo(isRegistered: false),
          evgoExpiredSoon: false,
          evgoExpired: false,
          isWalletSetupDone: false,
          evgoExpiryDate: EVGOComplimentaryUtil.evgoEmptyString));
      final httpResponse = await _evApi.checkWalletAndDriverExistence(
        email: Global.getInstance().userEmail ?? '',
        guid: Global.getInstance().guid ?? '',
        make: vehicleInfo.make,
      );
      final partnerEnrollment =
          httpResponse.response?.payload.partnerEnrollment;
      if (partnerEnrollment != null) {
        var partnersListData =
            partnerEnrollment.partnerStatus.map((eachPartner) {
          var partner = Map();
          partner['name'] =
              partnerNameValues.map[eachPartner.partnerName] ?? '';
          partner['isRegistered'] =
              EvUtil.isPartnerRegistered(partner: eachPartner);
          partner['isEnrolled'] = eachPartner.status.toUpperCase() ==
              EVGOComplimentaryUtil.evgoFound;
          partner['expiry_date'] = eachPartner.expiryDate;
          partner['wallet'] = partnerEnrollment.wallet;
          return partner;
        });

        var chargePointInfo = partnersListData.firstWhere((partner) =>
            partner['name'] == partnerNameValues.map['chargepoint']);
        var evgoInfo = partnersListData.firstWhere(
            (partner) => partner['name'] == partnerNameValues.map['evgo'],
            orElse: () => {});
        bool evgoExpiringSoon = false;
        bool evgoExpired = false;
        bool isWalletSetupDone =
            EvUtil.isWalletSetupDone(wallet: partnerEnrollment.wallet);
        DateTime? _evgoExpiryDate;
        if (evgoInfo["expiry_date"].isNotEmpty) {
          var evgoExpiryDate = evgoInfo["expiry_date"];
          var expiry = DateTime.parse(evgoExpiryDate);
          _evgoExpiryDate = expiry;
          final expiryDays = expiry.difference(DateTime.now()).inDays;
          evgoExpiringSoon = expiryDays <= 30 && expiryDays >= 0;
          evgoExpired = expiryDays < 0;
        }

        _partnerDetails.sink.add(
          PartnerDetails(
              isLoading: false,
              chargePoint: ChargePointInfo(
                isRegistered: chargePointInfo['isEnrolled'],
              ),
              evgo: EvgoInfo(
                isRegistered:
                    evgoInfo.isNotEmpty ? evgoInfo['isEnrolled'] : false,
              ),
              evgoExpiredSoon: evgoExpiringSoon,
              evgoExpired: evgoExpired,
              isWalletSetupDone: isWalletSetupDone,
              evgoExpiryDate: _evgoExpiryDate != null
                  ? dateTimeToString(
                      _evgoExpiryDate, EVGOComplimentaryUtil.evgoDateFormat)
                  : ""),
        );
      } else {
        _partnerDetails.sink.add(PartnerDetails(
            isLoading: false,
            chargePoint: ChargePointInfo(isRegistered: false),
            evgo: EvgoInfo(isRegistered: false),
            evgoExpiredSoon: false,
            evgoExpired: false,
            isWalletSetupDone: false,
            evgoExpiryDate: EVGOComplimentaryUtil.evgoEmptyString));
      }
    }
  }

  @override
  void dispose() {
    _partnerDetails.close();
  }
}
