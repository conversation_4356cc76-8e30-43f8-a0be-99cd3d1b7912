// Dart imports:
import 'dart:async';

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_extension.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/charge_timer_body_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/subjects.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';

// Project imports:
import '../../core/constants.dart';
import '../widgets/charge_history/charge_history_card/history_info.dart';
import '../widgets/helper/range_info.dart';
import 'clean_assist_bloc.dart';
import 'partner_details_Info_bloc.dart';
import 'wallet_details_info_bloc.dart';

import 'package:oa_network_impl/electric_vehicle/entity/charge_statistics.dart'
    as st;

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/odometer_detail_entity.dart'
    as odometerDetail;

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfoEntity;

class BatteryRangeInfo {
  final bool isLoading;
  final RangeInfo range;

  BatteryRangeInfo({
    required this.isLoading,
    required this.range,
  });
}

class ChargeMeterInfo {
  final bool isLoading;
  final bool isCharging;
  final int chargeRemaining;
  final int? fuelLevel;
  final bool isPluggedIn;
  final String? estimatedTime;
  final String? nextScheduleTime;
  final VehicleType vehicleType;
  final RangeInfo? evRangeInfo;
  final RangeInfo? gasRangeInfo;
  final RangeInfo? evMaxRangeInfo;
  final RangeInfo? gasMaxRangeInfo;

  ChargeMeterInfo({
    required this.isLoading,
    required this.isCharging,
    required this.chargeRemaining,
    required this.fuelLevel,
    required this.isPluggedIn,
    required this.vehicleType,
    this.evRangeInfo,
    this.gasRangeInfo,
    this.gasMaxRangeInfo,
    this.evMaxRangeInfo,
    this.estimatedTime,
    this.nextScheduleTime,
  });
}

class ChargeButtonInfo {
  final String chargeBtnText;
  final CHARGING_EV_SCREEN chargeScreen;
  ChargeButtonInfo({
    required this.chargeBtnText,
    required this.chargeScreen,
  });
}

class ChargeButtonHelper {
  final bool isCharging;
  final int chargeRemaining;
  final bool isPluggedIn;
  final String buttonText;
  final CHARGING_EV_SCREEN evScreen;

  ChargeButtonHelper({
    required this.isCharging,
    required this.chargeRemaining,
    required this.isPluggedIn,
    required this.buttonText,
    required this.evScreen,
  });
}

class BatteryInfoBloc extends BlocBase {
  VehicleInfo? vehicleInfo;
  OneAppClient api = APIClientConfig.oneAppClient;
  GetIt locator = GetIt.instance;
  late Function(bool) progressHandlerCallback;
  int timeOut = 180;
  DateTime? _previousOccurrenceDate;

  Stream<BatteryRangeInfo?> get batteryInfo => _batteryInfo.stream;
  final _batteryInfo = BehaviorSubject<BatteryRangeInfo?>();

  Stream<ChargeMeterInfo?> get chargeMeterInfo => _chargeMeterInfo.stream;
  final _chargeMeterInfo = BehaviorSubject<ChargeMeterInfo?>();

  Stream<ChargeButtonHelper> get chargeButton => _chargeButton.stream;
  final _chargeButton = BehaviorSubject<ChargeButtonHelper>();

  Stream<List<ChargeHistoryInfo>?> get chargeHistoryInfo =>
      _chargeHistoryInfo.stream;
  final _chargeHistoryInfo = BehaviorSubject<List<ChargeHistoryInfo>?>();
  Stream<bool> get isPublicChargingControlAllowed =>
      _isPublicChargingControlAllowed.stream;
  final _isPublicChargingControlAllowed = BehaviorSubject<bool>();

  StreamSubscription? _vehicleInfoSubscription;
  CleanAssistBloc _cleanAssistBloc;
  PartnerDetailsInfoBloc _partnerDetailsInfoBloc;
  WalletDetailsInfoBloc _walletDetailsInfoBloc;
  String vin = Global.getInstance().vin!;
  bool isEVFindStationsEnabled = false;
  bool _isEvPublicChargingControlEnabled = false;
  vehicleInfoEntity.Payload? vehicleItem;
  bool isEvPhp = false;
  bool isEV = false;
  odometerDetail.OdometerDetailPayload? odometerDetailsPayload;
  final _numberFormatter = NumberFormat("#,##0.00", "en_US");
  ChargeHistoryInfo? lastChargeObj;

  BatteryInfoBloc()
      : _cleanAssistBloc = CleanAssistBloc(),
        _partnerDetailsInfoBloc = PartnerDetailsInfoBloc(),
        _walletDetailsInfoBloc = WalletDetailsInfoBloc();

  CleanAssistBloc get cleanAssistBloc => _cleanAssistBloc;
  PartnerDetailsInfoBloc get partnerDetailsInfoBloc => _partnerDetailsInfoBloc;
  WalletDetailsInfoBloc get walletDetailsInfoBloc => _walletDetailsInfoBloc;

  void init(Function(bool) progressHandler) {
    progressHandlerCallback = progressHandler;
    _initAsync().whenComplete(() => FireBaseAnalyticsLogger.logInfo(
        "at=EVChargeManagementBloc status=init-async-complete"));
  }

  Future<void> _initAsync() async {
    vehicleItem = await VehicleRepo().getLocalPayloadFromVin(vin);
    odometerDetailsPayload = await VehicleRepo().fetchTelemetry();
    final evRepository = await VehicleRepo().getEVVehicleInfoRepository(vin);
    _vehicleInfoSubscription = evRepository.evVehicleInfo.listen((vehicleInfo) {
      vehicleInfo = vehicleInfo;
      if (vehicleInfo != null) {
        _loadChargeManagementDetail(vehicleInfo);
      }
    });
    vehicleInfo = evRepository.lastReceived;
    if (vehicleItem?.features != null) {
      isEVFindStationsEnabled =
          isFeatureEnabled(EV_CHARGE_STATION, vehicleItem?.features);
      _isEvPublicChargingControlEnabled =
          isEvPublicChargingControlEnabled(vehicleItem?.features);
    }
    _isPublicChargingControlAllowed.sink.add(_isEvPublicChargingControlEnabled);
    if (vehicleItem?.generation != null) {
      isEvPhp = isEvPhpModel(vehicleItem!);
      isEV = isEvModel(vehicleItem!);
    }

    _loadChargeManagementDetail(vehicleInfo);
  }

  void _loadChargeManagementDetail(VehicleInfo? vehicleInfo) {
    if (vehicleInfo != null) {
      final chargeInfo = vehicleInfo.chargeInfo;
      if (chargeInfo != null) {
        int? fuelLevel = odometerDetailsPayload?.fuelLevel;
        int chargeRemaining = chargeInfo.chargeRemainingAmount ?? 0;

        double phevEVRange = chargeInfo.evDistance ?? 0.0;
        double phevHVRange =
            odometerDetailsPayload?.distanceToEmpty?.value ?? 0.0;

        double? calculatedRange = phevEVRange + phevHVRange;
        String rangeInString = removeDecimalZeroFromString(
            _numberFormatter.format(calculatedRange));
        String phevTotalRangeString =
            // ignore: unnecessary_null_comparison
            calculatedRange == null ? "0" : rangeInString;
        String phevTotalRangeUnit =
            (odometerDetailsPayload?.distanceToEmpty == null)
                ? formatTextForLexusAndToyota("mi")
                : formatTextForLexusAndToyota(
                    odometerDetailsPayload?.distanceToEmpty?.unit ?? "");
        double phevTotalRange = double.tryParse(phevTotalRangeString) ?? 0;

        double phevMaxRange =
            double.tryParse("${chargeInfo.evTravelableDistance}") ?? 0;
        double phGasMaxEVRange =
            double.tryParse("${chargeInfo.gasolineTravelableDistance}") ?? 0;

        String nextScheduleText = '';

        String timeRemaining =
            getTimeFormattedDuration(chargeInfo.remainingChargeTime ?? 0);
        String distanceRemaining =
            checkForZeroWithTrimmedDecimal(chargeInfo.evDistance!, 1) +
                " " +
                (chargeInfo.evDistanceUnit ?? OneAppString.of().short_miles);
        String chargeDesc = '';
        if (isEV) {
          chargeDesc = OneAppString.of().withoutAC;
        } else {
          chargeDesc = OneAppString.of().estimateRangeText;
        }

        String chargeChargingDescription = '';
        if (chargeInfo.isCharging) {
          chargeChargingDescription = timeRemaining.isEmpty
              ? ""
              : isEV
                  ? 'Est. ${OneAppString.of().timeUntilFullyCharged(timeRemaining)}'
                  : OneAppString.of().timeUntilFullyCharged(timeRemaining);
        } else if (chargeInfo.isPlugWaitingForTimerToCharge) {
          chargeChargingDescription =
              OneAppString.of().distanceRemaining(distanceRemaining);
        } else {
          chargeChargingDescription = '';
        }

        final chargeMeter = ChargeMeterInfo(
          isLoading: false,
          isCharging: chargeInfo.isCharging,
          chargeRemaining: chargeRemaining,
          isPluggedIn: chargeInfo.isPlugWaitingForTimerToCharge,
          estimatedTime: chargeChargingDescription,
          vehicleType: isEV
              ? VehicleType.EV
              : isEvPhp
                  ? VehicleType.EV_PHP
                  : VehicleType.CV,
          evRangeInfo: RangeInfo(
            range: chargeInfo.evDistance ?? 0.0,
            unit: chargeInfo.evDistanceUnit ?? OneAppString.of().short_miles,
          ),
          gasRangeInfo: RangeInfo(
            range: phevHVRange,
            unit: phevTotalRangeUnit,
          ),
          evMaxRangeInfo: RangeInfo(
            range: phevMaxRange,
            unit: chargeInfo.evDistanceUnit ?? OneAppString.of().short_miles,
          ),
          gasMaxRangeInfo: RangeInfo(
            range: phGasMaxEVRange,
            unit: chargeInfo.evDistanceUnit ?? OneAppString.of().short_miles,
          ),
          nextScheduleTime: nextScheduleText,
          fuelLevel: fuelLevel,
        );

        _chargeMeterInfo.sink.add(chargeMeter);
        if (isEV) {
          _batteryInfo.sink.add(BatteryRangeInfo(
              isLoading: false,
              range: RangeInfo(
                range: chargeInfo.evDistance ?? 0.0,
                rangeWithAC: chargeInfo.evDistanceAC ?? 0.0,
                unit: OneAppString.of().unitEstText(
                    chargeInfo.evDistanceUnit ?? ""), //"mi/km est." for EV,
                chargeDescription: chargeDesc,
              )));
        } else if (isEvPhp) {
          _batteryInfo.sink.add(BatteryRangeInfo(
              isLoading: false,
              range: RangeInfo(
                range: double.tryParse(
                        removeDecimalZeroFromDouble(phevTotalRange)) ??
                    0,
                unit: phevTotalRangeUnit,
                chargeDescription: chargeDesc,
              )));
        }

        if (!_isEvPublicChargingControlEnabled &&
            !chargeInfo.isCharging &&
            chargeInfo.isPlugWaitingForTimerToCharge) {
          _chargeButton.sink.add(_getChargeButtonType(
              isCharging: chargeInfo.isCharging,
              chargeRemaining: chargeRemaining,
              isPluggedIn: chargeInfo.isPlugWaitingForTimerToCharge,
              showStartCharge: true));
        } else if (isEVFindStationsEnabled) {
          _chargeButton.sink.add(_getChargeButtonType(
              isCharging: chargeInfo.isCharging,
              chargeRemaining: chargeRemaining,
              isPluggedIn: chargeInfo.isPlugWaitingForTimerToCharge,
              showStartCharge: false));
        }
      }
    } else {
      _chargeButton.sink.add(_getChargeButtonType(
          isCharging: false, chargeRemaining: 0, isPluggedIn: false));
      _batteryInfo.sink.add(null);
      _chargeMeterInfo.sink.add(null);
      FireBaseAnalyticsLogger.logError(
          "batteryInfoBloc step=_loadChargeManagementDetail value=null");
    }
  }

  ChargeButtonHelper _getChargeButtonType({
    required bool isCharging,
    required int chargeRemaining,
    required bool isPluggedIn,
    bool showStartCharge = false,
  }) {
    return ChargeButtonHelper(
        isCharging: isCharging,
        chargeRemaining: chargeRemaining,
        isPluggedIn: isPluggedIn,
        buttonText: isPluggedIn
            ? OneAppString.of().startCharging
            : OneAppString.of().findNearByStation,
        evScreen: isPluggedIn
            ? CHARGING_EV_SCREEN.CHARGING_SCREEN
            : CHARGING_EV_SCREEN.NEARBY_STATION);
  }

  void formLastChargeObject(st.LastCharge? lastCharge) {
    if (lastCharge != null) {
      String partnerNameVal = lastCharge.cdrDetails != null
          ? lastCharge.cdrDetails?.partnerName ?? ""
          : "";
      String chargeLocationTypeVal = lastCharge.chargeLocationType ?? "";

      lastChargeObj = ChargeHistoryInfo(
        chargeLocationType: partnerNameVal.isNotEmpty
            ? partnerNameVal.capitalize()
            : chargeLocationTypeVal.isNotEmpty
                ? chargeLocationTypeVal.capitalize()
                : OneAppString.of().publicText,
        startTime: DateTime.parse(lastCharge.startTime ?? "${DateTime.now()}"),
        endTime: DateTime.parse(lastCharge.endTime ?? "${DateTime.now()}"),
        socBefore: lastCharge.socBeforeCharging ?? "0%",
        socAfter: lastCharge.socAfterCharging ?? "0%",
        totalCharge: lastCharge.totalChargeKwhr != null
            ? lastCharge.totalChargeKwhr.toString()
            : "0",
        isEcho: lastCharge.watttimeDetails?.chargeClasification == "echo"
            ? true
            : false,
        cdrDetails: lastCharge.cdrDetails != null
            ? CdrDetailsInfo(
                cardLast4: lastCharge.cdrDetails?.cardLast4,
                partnerName: lastCharge.cdrDetails?.partnerName ?? "",
                totalAmount: lastCharge.cdrDetails?.totalAmount,
              )
            : null,
        latitude: lastCharge.latitude.toString(),
        longitude: lastCharge.longitude.toString(),
        address: '',
      );
    }
  }

  // Start Charge option
  Future<void> startChargeRequest(Function savedSuccessfullyBottomSheet) async {
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    ChargeTimerBodyHelper chargeTimerBodyHelper = ChargeTimerBodyHelper(
        command: IMMEDIATE_CHARGE, remoteHvac: null, reservationCharge: null);
    final brand = vehicleItem?.brand;
    final generation = vehicleItem?.generation;
    final fcmDeviceId = Global.getInstance().fcmDeviceId;
    if (brand == null || generation == null || fcmDeviceId == null) {
      failureCase(savedSuccessfullyBottomSheet);
      return;
    }
    progressHandlerCallback(true);
    final commonResponse = await api.postVehicleChargeTimerRequest(
        brand, vin, generation, fcmDeviceId, chargeTimerBodyHelper);
    final payLoad = commonResponse.response?.payload;
    if (payLoad != null) {
      int startTime = DateTime.now().millisecondsSinceEpoch;
      if (payLoad.returnCode == "ONE-RES-10000") {
        if (isPRECY17Vehicle(generation)) {
          fetchVehicleClimateStatusPreCY17(
              vin, startTime, savedSuccessfullyBottomSheet);
        } else {
          fetchChargeManagementStatus(
              startTime, payLoad.appRequestNo, savedSuccessfullyBottomSheet);
        }
      } else {
        progressHandlerCallback(false);
      }

      if (commonResponse.error != null) {
        failureCase(savedSuccessfullyBottomSheet);
        progressHandlerCallback(false);
      }
    } else {
      savedSuccessfullyBottomSheet(commonResponse.error?.errorMessage, false);
      progressHandlerCallback(false);
    }
  }

  Future<void> fetchVehicleClimateStatusPreCY17(
      String vin, int startTime, Function savedSuccessfullyBottomSheet) async {
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    final commonResponse = await api.fetchChargeManagementTimerStatus(
        vehicleItem?.generation ?? "",
        vin,
        vehicleItem?.brand ?? Global.getInstance().appBrand);
    final chargeManagementDetail = commonResponse.response;
    final payLoad = chargeManagementDetail?.payload;
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CLIMATE_PRE_CY17_STATUS_SUCCESS,
          category: LogCategory.FL_VEHI);
      if (payLoad.returnCode != null &&
          (payLoad.returnCode == ELECTRIC_STATUS_RETURN_CODE_SUCCESS ||
              payLoad.returnCode!.isEmpty) &&
          payLoad.vehicleStatusResult != null &&
          payLoad.vehicleStatusResult?.occurrenceDate != null) {
        DateTime occurrenceDate = DateFormat(CTP_DATE_FORMAT)
            .parse(payLoad.vehicleStatusResult?.occurrenceDate ?? "", true)
            .toLocal();

        if (_previousOccurrenceDate == null) {
          _previousOccurrenceDate = occurrenceDate;
        }
        if (!occurrenceDate.isAfter(_previousOccurrenceDate!)) {
          int now = DateTime.now().millisecondsSinceEpoch;
          double timeElapsed = (now - startTime) / 1000;
          if (timeElapsed >= timeOut) {
            failureCase(savedSuccessfullyBottomSheet);
          } else {
            Future.delayed(const Duration(milliseconds: 3000), () {
              fetchVehicleClimateStatusPreCY17(
                  vin, startTime, savedSuccessfullyBottomSheet);
            });
          }
        } else {
          _previousOccurrenceDate = occurrenceDate; //Updating to latest
          refreshUI(chargeManagementDetail);
          progressHandlerCallback(false); //Stopping the spinner
        }
      } else {
        failureCase(savedSuccessfullyBottomSheet);
      }
    }
  }

  Future<void> fetchChargeManagementStatus(int startTime, String? requestNo,
      Function savedSuccessfullyBottomSheet) async {
    String vin = Global.getInstance().vin!;
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    final commonResponse = await api.fetchChargeManagementTimerStatus(
        vehicleItem?.generation ?? "",
        vin,
        vehicleItem?.brand ?? Global.getInstance().appBrand,
        requestNo: requestNo);
    final chargeManagementDetail = commonResponse.response;
    final payLoad = chargeManagementDetail?.payload;
    if (payLoad != null) {
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);
      if (payLoad.remoteControlResult != null) {
        int? status = payLoad.remoteControlResult?.status;
        int? result = payLoad.remoteControlResult?.result;
        if (status != null) {
          if (status == 0) {
            progressHandlerCallback(false);
            if (result == 0) {
              refreshUI(chargeManagementDetail);
            } else {
              errorCallBack(savedSuccessfullyBottomSheet);
            }
          } else {
            int now = DateTime.now().millisecondsSinceEpoch;
            double timeElapsed = (now - startTime) / 1000;
            if (timeElapsed >= timeOut) {
              progressHandlerCallback(false);
              errorCallBack(savedSuccessfullyBottomSheet);
            } else {
              fetchChargeManagementStatus(
                  startTime, requestNo, savedSuccessfullyBottomSheet);
            }
          }
        } else {
          errorCallBack(savedSuccessfullyBottomSheet);
          progressHandlerCallback(false);
        }
      }
    }

    if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(savedSuccessfullyBottomSheet);
      FireBaseAnalyticsLogger.logErrorAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_FAILURE,
          category: LogCategory.FL_VEHI);
    }
  }

  void refreshUI(ChargeManagementDetailEntity? detail) {
    if (detail != null) {
      String? vin = Global.getInstance().vin;
      final repoFuture = VehicleRepo().getEVVehicleInfoRepository(vin);
      repoFuture.then(
        (repository) => repository.receivedExtraChargeManagementDetail(detail),
      );
    }
  }

  void errorCallBack(Function savedSuccessfullyBottomSheet) {
    Future.delayed(const Duration(milliseconds: 500), () {
      savedSuccessfullyBottomSheet();
    });
  }

  void failureCase(Function bottomSheet) {
    errorCallBack(bottomSheet);
    progressHandlerCallback(false);
  }

  @override
  void dispose() {
    _batteryInfo.close();
    _chargeMeterInfo.close();
    _chargeButton.close();
    _chargeHistoryInfo.close();
    _isPublicChargingControlAllowed.close();
    _vehicleInfoSubscription?.cancel();
  }
}
