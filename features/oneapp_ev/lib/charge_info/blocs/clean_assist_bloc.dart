// Package imports:
import 'package:oa_network/entity/oa_response.dart';
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/one_app/entity/ca/ca_dataconsent_entity.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:rxdart/rxdart.dart';

class CleanAssistDetails {
  final bool isLoading;
  final bool isLCFSShowGraph;

  CleanAssistDetails({
    required this.isLoading,
    required this.isLCFSShowGraph,
  });
}

class CleanAssistBloc extends BlocBase {
  final _oaApi = APIClients.oneAppApiClient;

  Stream<CleanAssistDetails> get cleanAssistDetails =>
      _cleanAssistDetails.stream;
  final _cleanAssistDetails = BehaviorSubject<CleanAssistDetails>();

  Stream<bool> get isLCFSEligible => _isLCFSEligible.stream;
  final _isLCFSEligible = BehaviorSubject<bool>();

  CleanAssistBloc() {
    _checkCleanAssistEligibility();
  }

  void _checkCleanAssistEligibility() async {
    _cleanAssistDetails.sink.add(CleanAssistDetails(
      isLoading: true,
      isLCFSShowGraph: false,
    ));
    OAResponse<CAEligibilityEntity> httpResponse =
        await _oaApi.checkCleanAssistEligibility(
      vin: Global.getInstance().vin ?? "",
      brand: Global.getInstance().appBrand,
      guid: Global.getInstance().guid!,
    );
    final payload = httpResponse.response?.payload;
    if (payload != null) {
      // Success
      if (payload.lcfsEligible != null && payload.lcfsEligible == "true") {
        _isLCFSEligible.sink.add(true);
      } else {
        _isLCFSEligible.sink.add(false);
      }
      if (payload.lcfsOptIn != null &&
          payload.lcfsOptIn?.toLowerCase() == 'in') {
        _cleanAssistDetails.sink.add(CleanAssistDetails(
          isLoading: false,
          isLCFSShowGraph: true,
        ));
      } else {
        _cleanAssistDetails.sink.add(CleanAssistDetails(
          isLoading: false,
          isLCFSShowGraph: false,
        ));
      }
    } else {
      // Failure
      _isLCFSEligible.sink.add(false);
      _cleanAssistDetails.sink.add(CleanAssistDetails(
        isLoading: false,
        isLCFSShowGraph: false,
      ));
    }
  }

  @override
  void dispose() {
    _cleanAssistDetails.close();
    _isLCFSEligible.close();
  }
}
