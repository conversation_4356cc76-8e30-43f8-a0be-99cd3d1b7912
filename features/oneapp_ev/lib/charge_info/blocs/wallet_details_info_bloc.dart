// Package imports:
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/one_app/entity/wallet/payment_method_entity.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class WalletDetails {
  PaymentMethod? walletDetail;
  final bool walletPresence;
  final bool isLoading;

  WalletDetails(
      {this.walletDetail,
      required this.walletPresence,
      required this.isLoading});
}

class WalletDetailsInfoBloc extends BlocBase {
  final _oaApi = APIClients.oneAppApiClient;
  vehicleInfo.Payload? vehicleItem;
  Stream<WalletDetails> get walletDetail => _walletDetail.stream;
  final _walletDetail = BehaviorSubject<WalletDetails>();

  WalletDetailsInfoBloc() {
    initializeWallet();
  }

  Future<void> initializeWallet() async {
    vehicleItem = await VehicleRepo().cachedGlobalVehicleInfo();
    if (vehicleItem != null) {
      if (isFeatureEnabled(WALLET, vehicleItem!.features)) {
        _walletDetail.sink
            .add(WalletDetails(walletPresence: true, isLoading: true));
        final commonResponse = await _oaApi.fetchWallet(
          guid: Global.getInstance().guid ?? '',
        );
        final response = commonResponse.response;
        if (response != null &&
            response.status!.messages != null &&
            response.status!.messages!.first.responseCode?.toLowerCase() ==
                'success' &&
            response.payload != null &&
            response.payload!.paymentMethods!.isNotEmpty) {
          PaymentMethod detail = response.payload!.paymentMethods!.firstWhere(
              (element) => (element.paymentMethodDefault != null &&
                  element.paymentMethodDefault == true),
              orElse: () => PaymentMethod());
          _walletDetail.sink.add(WalletDetails(
              walletDetail: detail, walletPresence: true, isLoading: false));
        } else {
          _walletDetail.sink.add(WalletDetails(
              walletDetail: PaymentMethod(),
              walletPresence: true,
              isLoading: false));
        }
      } else {
        _walletDetail.sink
            .add(WalletDetails(walletPresence: false, isLoading: false));
      }
    }
  }

  @override
  void dispose() {
    _walletDetail.close();
  }
}
