// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:oneapp_network/api_config.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';

// Project imports:
import '../core/widgets/floating_button_widget.dart';
import '../core/widgets/themed_widget.dart';
import '../log/ev_analytics_events.dart';
import 'widgets/statics_card/_accordion.dart';

class HelpMeImprove extends ThemedStatelessWidget {
  final bool hideHealthImpact;
  final bool showSwipeBar;
  HelpMeImprove({
    Key? key,
    required this.hideHealthImpact,
    required this.showSwipeBar,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          showSwipeBar ? SwipeBarIcon() : Container(),
          _getPageHeader(context),
          _content,
          _floatingButton,
        ],
      ),
    );
  }

  InkWell _getPageHeader(BuildContext context) {
    return InkWell(
      onTap: () => Navigator.pop(context),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: EdgeInsets.only(left: 16.0.w),
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorUtil.tertiary12,
            ),
            child: Padding(
              padding: EdgeInsets.all(16.0.r),
              child: SvgPicture.asset(
                arrowLeftIcon,
                colorFilter: ColorFilter.mode(
                  colorUtil.button02a,
                  BlendMode.srcIn,
                ),
                width: 24.w,
                height: 24.h,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Expanded get _content {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            _ecoContent,
            SizedBox(
              height: 16.h,
            ),
            if (!hideHealthImpact) _healthContent,
          ],
        ),
      ),
    );
  }

  Padding get _floatingButton {
    return Padding(
      padding: EdgeInsets.only(bottom: 32.0.h, top: 8.h),
      child: FloatingButtonWidget(
        text: OneAppString.of().findOutMore,
        isEnabled: true,
        disabledBackgroundColor: colorUtil.button02d,
        disabledButtonTextColor: colorUtil.button05a,
        onPressed: () {
          FireBaseAnalyticsLogger.logMarketingGroupEvent(
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
              childEventName: EVAnalyticsEvent.LEARN_MORE);
          urlLauncher(APIConfig.WattTimeElectricityAndHealthURL,
              forceWebView: false);
        },
      ),
    );
  }

  Column get _ecoContent {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorUtil.button03d,
            ),
            child: Padding(
              padding: EdgeInsets.all(16.0.r),
              child: SvgPicture.asset(
                icEcoHistory,
                colorFilter: ColorFilter.mode(
                  colorUtil.primaryButton01,
                  BlendMode.srcIn,
                ),
                width: 30.r,
                height: 30.r,
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(bottom: 16.0.h),
          child: Text(
            OneAppString.of().ecoCharging,
            style: TextStyleExtension().newStyleWithColor(
              textStyleUtil.headline1,
              colorUtil.tertiary00,
            ),
          ),
        ),
        Accordion(
          title: OneAppString.of().whatIsEcoCharge,
          hyperLinkText: OneAppString.of().wattTime,
          hyperLinkURL: APIConfig.wattTimeURL,
          content: hideHealthImpact
              ? OneAppString.of().whatIsEcoChargeAnswerWithoutHealth
              : OneAppString.of().whatIsEcoChargeAnswerWithHealth,
        ),
        SizedBox(
          height: 16.h,
        ),
        Accordion(
          title: OneAppString.of().howEcoEarned,
          hyperLinkText: OneAppString.of().wattTime,
          hyperLinkURL: APIConfig.wattTimeURL,
          content: hideHealthImpact
              ? OneAppString.of().howEcoEarnedAnswerWithoutHealth
              : OneAppString.of().howEcoEarnedAnswerWithHealth,
        ),
      ],
    );
  }

  Column get _healthContent {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorUtil.success01,
            ),
            child: Padding(
              padding: EdgeInsets.all(16.0.r),
              child: SvgPicture.asset(
                evHealthIcon,
                colorFilter: ColorFilter.mode(
                  colorUtil.tertiary15,
                  BlendMode.srcIn,
                ),
                width: 30.r,
                height: 30.r,
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(bottom: 16.0.h),
          child: Text(
            OneAppString.of().healthImpactReduction,
            textAlign: TextAlign.center,
            style: TextStyleExtension().newStyleWithColor(
              textStyleUtil.headline1,
              colorUtil.tertiary00,
            ),
          ),
        ),
        Accordion(
          title: OneAppString.of().howHealthImpactCalculated,
          content: OneAppString.of().howHealthImpactCalculatedAnswer,
          hyperLinkText: OneAppString.of().wattTime,
          hyperLinkURL: APIConfig.wattTimeURL,
        ),
      ],
    );
  }
}
