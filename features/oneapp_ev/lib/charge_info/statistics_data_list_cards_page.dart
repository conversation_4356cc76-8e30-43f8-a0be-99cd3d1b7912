// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oa_network_impl/electric_vehicle/entity/charge_statistics.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/dialog/swipe_bar_icon.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';

// Project imports:
import '../core/shimmering/widgets/shimmering_tile_widget.dart';
import '../core/widgets/floating_button_widget.dart';
import '../core/widgets/iconic_floating_button_widget.dart';
import '../core/widgets/themed_widget.dart';
import '../log/ev_analytics_events.dart';
import 'blocs/charge_statistics/charging_statistics_bloc.dart';
import 'help_me_improve_page.dart';
import 'widgets/statics_card/_no_data.dart';
import 'widgets/statics_card/_statistics_list_card.dart';
import 'widgets/statics_card/yearmonth_lib/yearmonth_picker.dart';
import 'widgets/statics_card/yearmonth_lib/yearmonth_picker_bloc.dart';

class StatisticsDataListCardsPage extends ThemedStatelessWidget {
  final ChargeStatisticsBloc bloc;

  StatisticsDataListCardsPage({
    Key? key,
    required this.bloc,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        color: colorUtil.primaryButton01,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.topCenter,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SwipeBarIcon(),
                    _getPageHeader(context),
                    StreamBuilder(
                        stream: bloc.selectedMonthMMYYYY,
                        builder: (context, AsyncSnapshot<String?> snapshot) =>
                            _floatingDatePicker(context)),
                    StreamBuilder(
                        stream: bloc.chargeStatisticsData,
                        builder: (context,
                            AsyncSnapshot<ChargeStatisticsEntity?> snapshot) {
                          final _data = snapshot.data;
                          if (_data == null || bloc.isSelectedMonthApiLoading) {
                            return _loadingLayout;
                          } else if (snapshot.data != null &&
                              !bloc.isSelectedMonthApiLoading &&
                              _data.monthlyReports != null &&
                              _data.monthlyReports!.isNotEmpty) {
                            return StatisticsListCards(bloc: bloc);
                          }
                          return StatisticsNoData(bloc: bloc);
                        }),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: _getFloatingButton(context),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget get _loadingLayout {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: EVShimmeringTile(),
        ),
        Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: EVShimmeringTile(),
        ),
        Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: EVShimmeringTile(),
        ),
        Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: EVShimmeringTile(),
        ),
      ],
    );
  }

  Align _floatingDatePicker(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Padding(
        padding: EdgeInsets.only(bottom: 16.0.h),
        child: IconicFloatingButtonWidget(
          rightIcon: icDownArrow,
          text: bloc.formattedMMYYYY,
          foreColor: bloc.formattedMMYYYY == OneAppString.of().monthCaption
              ? colorUtil.button02a
              : colorUtil.primaryButton01,
          bgColor: bloc.formattedMMYYYY == OneAppString.of().monthCaption
              ? colorUtil.button05b
              : colorUtil.button02a,
          isEnabled: true,
          onPressed: () {
            FireBaseAnalyticsLogger.logMarketingGroupEvent(
                VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                childEventName: EVAnalyticsEvent.CHARGE_STATS_DATE_FILTER);
            showCupertinoModalPopup(
                context: context,
                builder: (BuildContext context) {
                  return _buildBottomPicker(
                      YearMonthPicker(
                        initialMonth: bloc.intMM,
                        initialYear: bloc.intYYYY,
                        onPress: (MonthYearModel selectedMonthYear) =>
                            bloc.updateSelectedMonthYear(
                          year: int.parse(selectedMonthYear.year),
                          month: int.parse(selectedMonthYear.month),
                        ),
                      ),
                      context);
                });
          },
        ),
      ),
    );
  }

  Widget _buildBottomPicker(Widget picker, BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * .4,
      padding: const EdgeInsets.only(top: 6.0),
      color: colorUtil.tile01,
      child: DefaultTextStyle(
        style: TextStyle(
          color: colorUtil.brand01,
          fontSize: 22.0,
        ),
        child: GestureDetector(
          // Blocks taps from propagating to the modal sheet and popping.
          onTap: () {},
          child: SafeArea(
            top: false,
            child: picker,
          ),
        ),
      ),
    );
  }

  Widget _getPageHeader(BuildContext context) {
    return Row(
      children: [
        Padding(
          padding: EdgeInsets.only(left: 16.0.w),
          child: InkWell(
            onTap: () => Navigator.pop(context),
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: colorUtil.tertiary12,
              ),
              child: Padding(
                padding: EdgeInsets.all(16.0.r),
                child: SvgPicture.asset(
                  arrowLeftIcon,
                  colorFilter: ColorFilter.mode(
                    colorUtil.button02a,
                    BlendMode.srcIn,
                  ),
                  width: 24.w,
                  height: 24.h,
                ),
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 64.0.h),
          child: Text(
            OneAppString.of().statistics,
            style: textStyleUtil.subHeadline3,
          ),
        ),
      ],
    );
  }

  Widget _getFloatingButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 32.0.h),
      child: FloatingButtonWidget(
        text: OneAppString.of().learnMore,
        isEnabled: true,
        backgroundColor: colorUtil.button02a,
        textColor: colorUtil.primaryButton01,
        onPressed: () {
          FireBaseAnalyticsLogger.logMarketingGroupEvent(
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
              childEventName: EVAnalyticsEvent.LEARN_MORE);
          showMaterialModalBottomSheet(
            expand: true,
            context: context,
            clipBehavior: Clip.antiAliasWithSaveLayer,
            backgroundColor: colorUtil.tertiary15,
            builder: (context) => HelpMeImprove(
              showSwipeBar: true,
              hideHealthImpact: false,
            ),
          );
        },
      ),
    );
  }
}
