// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/one_app/entity/charge_management_detail_entity.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

import 'package:oa_network_impl/electric_vehicle/entity/mc/entity/create_schedule_req_entity.dart'
    as c;
import 'package:oa_network_impl/electric_vehicle/entity/mc/entity/update_schedule_req_entity.dart'
    as u;

class ScheduleCreateViewBloc extends BlocBase {
  final _oaApi = APIClients.oneAppApiClient;
  late Function(bool) progressHandlerCallback;
  String? _requestNo;

  Stream<bool> get createScheduleSuccess => _createScheduleSuccess.stream;
  final _createScheduleSuccess = BehaviorSubject<bool>();

  Stream<bool> get updateScheduleSuccess => _updateScheduleSuccess.stream;
  final _updateScheduleSuccess = BehaviorSubject<bool>();

  Stream<bool> get deleteScheduleSuccess => _deleteScheduleSuccess.stream;
  final _deleteScheduleSuccess = BehaviorSubject<bool>();

  void init(Function(bool) progressHandler) {
    _requestNo = '';
    progressHandlerCallback = progressHandler;
  }

  String? getRequestNo() {
    return _requestNo;
  }

  Future<void> createScheduleAPI(
      bool startTimeAMSelected,
      bool endTimeAMSelected,
      bool departureTimeSwitch,
      TextEditingController _startHourcontroller,
      TextEditingController _endHourcontroller,
      TextEditingController _startMincontroller,
      TextEditingController _endMincontroller,
      List<String> daysOfWeek,
      Function errorCallBack) async {
    c.CreateScheduleRequestEntity reqBodyObj;
    var startHour, endHour = 0;

    if (startTimeAMSelected) {
      if (int.parse(_startHourcontroller.text) == 12) {
        startHour = 00;
      } else {
        startHour = int.parse(_startHourcontroller.text);
      }
    } else {
      if (int.parse(_startHourcontroller.text) == 12) {
        startHour = 12;
      } else {
        startHour = int.parse(_startHourcontroller.text) + 12;
      }
    }
    if (endTimeAMSelected) {
      if (int.parse(_endHourcontroller.text) == 12) {
        endHour = 00;
      } else {
        endHour = int.parse(_endHourcontroller.text);
      }
    } else {
      if (int.parse(_endHourcontroller.text) == 12) {
        endHour = 12;
      } else {
        endHour = int.parse(_endHourcontroller.text) + 12;
      }
    }
    if (departureTimeSwitch) {
      reqBodyObj = c.CreateScheduleRequestEntity(
        enabled: true,
        startTime: c.StartTime(
          hour: startHour,
          minute: int.parse(_startMincontroller.text),
        ),
        endTime: c.StartTime(
          hour: endHour,
          minute: int.parse(_endMincontroller.text),
        ),
        daysOfTheWeek: daysOfWeek,
      );
    } else {
      reqBodyObj = c.CreateScheduleRequestEntity(
        enabled: true,
        startTime: c.StartTime(
          hour: startHour,
          minute: int.parse(_startMincontroller.text),
        ),
        daysOfTheWeek: daysOfWeek,
      );
    }
    progressHandlerCallback(true);

    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    String? guid = Global.getInstance().guid;

    final commonResponse = await _oaApi.createMultidayChargeSchedule(
        reqBodyObj,
        Global.getInstance().correlationId,
        Global.getInstance().vin ?? "",
        guid!,
        'application/json',
        vehicleItem?.generation ?? "",
        Global.getInstance().fcmDeviceId!);

    if (commonResponse.response != null) {
      _requestNo = commonResponse.response!.payload!.appRequestNo;
      progressHandlerCallback(false);
      _createScheduleSuccess.sink.add(true);
    } else if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(commonResponse.error!.errorMessage);
    }
  }

  Future<void> updateScheduleAPI(
      TimerChargeInfo? scheduleData,
      bool startTimeAMSelected,
      bool endTimeAMSelected,
      bool departureTimeSwitch,
      TextEditingController _startHourcontroller,
      TextEditingController _endHourcontroller,
      TextEditingController _startMincontroller,
      TextEditingController _endMincontroller,
      List<String> daysOfWeek,
      Function errorCallBack) async {
    u.UpdateScheduleRequestEntity reqBodyObj;
    var startHour, endHour = 0;

    if (startTimeAMSelected) {
      if (int.parse(_startHourcontroller.text) == 12) {
        startHour = 00;
      } else {
        startHour = int.parse(_startHourcontroller.text);
      }
    } else {
      if (int.parse(_startHourcontroller.text) == 12) {
        startHour = 12;
      } else {
        startHour = int.parse(_startHourcontroller.text) + 12;
      }
    }
    if (endTimeAMSelected) {
      if (int.parse(_endHourcontroller.text) == 12) {
        endHour = 00;
      } else {
        endHour = int.parse(_endHourcontroller.text);
      }
    } else {
      if (int.parse(_endHourcontroller.text) == 12) {
        endHour = 12;
      } else {
        endHour = int.parse(_endHourcontroller.text) + 12;
      }
    }
    if (departureTimeSwitch) {
      reqBodyObj = u.UpdateScheduleRequestEntity(
        settingId: scheduleData!.settingId as int?,
        enabled: scheduleData.enabled,
        startTime: u.StartTime(
          hour: startHour,
          minute: int.parse(_startMincontroller.text),
        ),
        endTime: u.StartTime(
          hour: endHour,
          minute: int.parse(_endMincontroller.text),
        ),
        daysOfTheWeek: daysOfWeek,
      );
    } else {
      reqBodyObj = u.UpdateScheduleRequestEntity(
        settingId: scheduleData!.settingId as int?,
        enabled: scheduleData.enabled,
        startTime: u.StartTime(
          hour: startHour,
          minute: int.parse(_startMincontroller.text),
        ),
        daysOfTheWeek: daysOfWeek,
      );
    }
    progressHandlerCallback(true);

    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    String? guid = Global.getInstance().guid;

    final commonResponse = await _oaApi.updateMultidayChargeSchedule(
        reqBodyObj,
        Global.getInstance().correlationId,
        Global.getInstance().vin ?? "",
        guid!,
        'application/json',
        vehicleItem?.generation ?? "",
        Global.getInstance().fcmDeviceId!);

    if (commonResponse.response != null) {
      _requestNo = commonResponse.response!.payload!.appRequestNo;
      progressHandlerCallback(false);
      _updateScheduleSuccess.sink.add(true);
    } else if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(commonResponse.error!.errorMessage);
    }
  }

  Future<void> deleteScheduleAPI(
      String settingId, Function errorCallBack) async {
    final vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    String? guid = Global.getInstance().guid;

    progressHandlerCallback(true);

    final commonResponse = await _oaApi.deleteMultidayChargeSchedule(
        settingId,
        Global.getInstance().correlationId,
        Global.getInstance().vin ?? "",
        guid!,
        'application/json',
        vehicleItem?.generation ?? "",
        Global.getInstance().fcmDeviceId!);
    if (commonResponse.response != null) {
      _requestNo = commonResponse.response!.payload!.appRequestNo;
      progressHandlerCallback(false);
      _deleteScheduleSuccess.sink.add(true);
    } else if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(commonResponse.error!.errorMessage);
    }
  }

  @override
  void dispose() {
    _createScheduleSuccess.close();
    _updateScheduleSuccess.close();
    _deleteScheduleSuccess.close();
  }
}
