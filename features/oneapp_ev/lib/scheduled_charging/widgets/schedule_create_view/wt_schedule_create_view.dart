// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:intl/intl.dart';
import 'package:oa_network_impl/one_app/entity/charge_management_detail_entity.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_common/widget/dialog/bottom_confirmation_dialog.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:super_tooltip/super_tooltip.dart';

// Project imports:
import '../../utils/util.dart';
import 'wt_schedule_create_view_bloc.dart';

// Project imports:

class ScheduleCreateViewScreen extends StatefulWidget {
  final TimerChargeInfo? scheduleData;
  final bool? isOffPeakSchedule;

  const ScheduleCreateViewScreen(
      {Key? key, this.scheduleData, this.isOffPeakSchedule})
      : super(key: key);

  @override
  State<ScheduleCreateViewScreen> createState() =>
      _ScheduleCreateViewScreenState();
}

class _ScheduleCreateViewScreenState extends State<ScheduleCreateViewScreen> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;

  List<String> hoursArray = [for (var i = 1; i < 13; i += 1) '$i'];
  List<String> minArray = [
    for (var i = 0; i < 12; i += 1) '${(i * 5).toString().padLeft(2, "0")}'
  ];

  bool showLoader = false;

  bool departureTimeSwitch = false;
  bool? offPeakHoursSwitch = false;
  bool startTimeAMSelected = false;
  bool endTimeAMSelected = false;
  bool _editMode = false;
  final _scheduleNameController = TextEditingController();
  final _startHourcontroller = TextEditingController();
  final _startMincontroller = TextEditingController();
  final _endHourcontroller = TextEditingController();
  final _endMincontroller = TextEditingController();
  TimeOfDay startTimeObj = TimeOfDay(hour: 12, minute: 0); // 3:00 PM
  TimeOfDay endTimeObj = TimeOfDay(hour: 12, minute: 0); // 5:00 AM
  List<String> daysOfWeek = [];

  //12 Hour Format
  final DateFormat amPmHourFormat = DateFormat("h");
  final DateFormat amPmMinFormat = DateFormat("mm");

  //24 Hour Format
  final DateFormat hourFormat = DateFormat("hh");
  String? requestNo = '';

  ScheduleCreateViewBloc _bloc = ScheduleCreateViewBloc();
  SuperTooltip? tooltip;
  BuildContext? offPeakWidgetContext;

  @override
  void initState() {
    super.initState();
    _bloc.init(_progressHandlerCallback);

    if (widget.scheduleData != null) {
      _editMode = true;
      _scheduleNameController.text = widget.scheduleData!.settingId.toString();

      startTimeObj = TimeOfDay(
          hour: int.parse(widget.scheduleData!.startTime!.split(':').first),
          minute: int.parse(
              widget.scheduleData!.startTime!.split(':').last)); // 3:00 PM
      (startTimeObj.period == DayPeriod.am)
          ? startTimeAMSelected = true
          : startTimeAMSelected = false;

      DateTime startTime = DateFormat("hh:mm").parse(
          widget.scheduleData!.startTime!.split(':').first +
              ":" +
              widget.scheduleData!.startTime!.split(':').last);
      _startHourcontroller.text = amPmHourFormat.format(startTime);
      _startMincontroller.text = amPmMinFormat.format(startTime);

      if (widget.scheduleData!.endTime != null) {
        //User may not have set the endTime when creating schedule
        departureTimeSwitch = true;
        endTimeObj = TimeOfDay(
            hour: int.parse(widget.scheduleData!.endTime!.split(':').first),
            minute: int.parse(
                widget.scheduleData!.endTime!.split(':').last)); // 3:00 PM
        (endTimeObj.period == DayPeriod.am)
            ? endTimeAMSelected = true
            : endTimeAMSelected = false;

        DateTime endTime = DateFormat("hh:mm").parse(
            widget.scheduleData!.endTime!.split(':').first +
                ":" +
                widget.scheduleData!.endTime!.split(':').last);
        _endHourcontroller.text = amPmHourFormat.format(endTime);
        _endMincontroller.text = amPmMinFormat.format(endTime);
      } else {
        DateTime endTime = DateFormat("hh:mm").parse('14' + ":" + '0');
        _endHourcontroller.text = amPmHourFormat.format(endTime);
        _endMincontroller.text = amPmMinFormat.format(endTime);
      }
      daysOfWeek.clear();
      daysOfWeek.addAll(widget.scheduleData!.daysOfTheWeek!);
      offPeakHoursSwitch = widget.isOffPeakSchedule;
    } else {
      //Create Mode
      setDefaultStartEndTime('12', '0', '14', '0', false);
    }

    _bloc.createScheduleSuccess.listen((event) {
      if (event == true) {
        requestNo = _bloc.getRequestNo();
        Future.delayed(Duration(seconds: 1), () {
          Navigator.of(context).pop(ScheduleBoxedReturns(
              requestNo, OneAppString.of().evmcScheduleCreatedText));
        });
      }
    });

    _bloc.updateScheduleSuccess.listen((event) {
      if (event == true) {
        requestNo = _bloc.getRequestNo();
        Future.delayed(Duration(seconds: 1), () {
          Navigator.of(context).pop(ScheduleBoxedReturns(
              requestNo, OneAppString.of().evmcScheduleUpdatedText));
        });
      }
    });

    _bloc.deleteScheduleSuccess.listen((event) {
      if (event == true) {
        requestNo = _bloc.getRequestNo();
        Future.delayed(Duration(seconds: 1), () {
          Navigator.of(context).pop(ScheduleBoxedReturns(
              requestNo, OneAppString.of().evmcScheduleDeletedText));
        });
      }
    });
  }

  void setDefaultStartEndTime(String startHour, String startMin, String endHour,
      String endMin, bool isOffPeak) {
    DateTime startTime = DateFormat("hh:mm").parse(startHour + ":" + startMin);
    DateTime endTime = DateFormat("hh:mm").parse(endHour + ":" + endMin);
    TimeOfDay startTimeObj =
        TimeOfDay(hour: startTime.hour, minute: startTime.minute);
    TimeOfDay endTimeObj =
        TimeOfDay(hour: endTime.hour, minute: endTime.minute);
    (startTimeObj.period == DayPeriod.am)
        ? startTimeAMSelected = true
        : startTimeAMSelected = false;
    (endTimeObj.period == DayPeriod.am)
        ? endTimeAMSelected = true
        : endTimeAMSelected = false;

    _startHourcontroller.text = amPmHourFormat.format(startTime);
    _startMincontroller.text = amPmMinFormat.format(startTime);
    _endHourcontroller.text = amPmHourFormat.format(endTime);
    _endMincontroller.text = amPmMinFormat.format(endTime);
    if (isOffPeak) {
      daysOfWeek.clear();
      daysOfWeek.addAll(
          [Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday]);
      departureTimeSwitch = true;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return OneAppScaffold(
      backgroundColor: _colorUtil.tile01,
      body: Scrollbar(
        thumbVisibility: true,
        child: CustomScrollView(
          slivers: [
            SliverFillRemaining(
              hasScrollBody: false,
              child: Column(
                children: <Widget>[
                  SizedBox(
                    height: 50.h,
                  ),
                  bottomSheetCustomAppBar(
                      OneAppString.of().evmcChargeSchedulePageTitleText,
                      onBackPressed: () => Navigator.of(context).pop(),
                      elevation: 0),
                  startTime(),
                  endTime(),
                  daysOfWeekWidget(),
                  Spacer(),
                  bottomControl(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Padding scheduleName() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 37.h),
      child: TextField(
        decoration: InputDecoration(
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.w)),
            borderSide: BorderSide(width: 1, color: _colorUtil.tertiary10),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.w)),
            borderSide: BorderSide(width: 1, color: _colorUtil.tertiary10),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
            borderSide: BorderSide(width: 1.w, color: _colorUtil.tertiary10),
          ),
          hintText: OneAppString.of().evmcScheduleNameHintText,
          hintStyle: TextStyleExtension()
              .newStyleWithColor(_textStyleUtil.body3, _colorUtil.tertiary07),
        ),
        style: TextStyleExtension()
            .newStyleWithColor(_textStyleUtil.body3, _colorUtil.tertiary00),
        controller: _scheduleNameController,
      ),
    );
  }

  _showPicker(
    BuildContext context,
    TextEditingController controller,
    List<String> arr,
  ) async {
    await showModalBottomSheet<DateTime>(
      backgroundColor: _colorUtil.tertiary15,
      context: context,
      builder: (context) {
        // DateTime tempPickedDate;
        String tempPickedVal = arr.first;
        return Container(
          height: 250.h,
          child: Column(
            children: <Widget>[
              Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    CupertinoButton(
                      child: Text(OneAppString.of().commonCancel),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                    CupertinoButton(
                      child: Text(OneAppString.of().commonDone),
                      onPressed: () {
                        controller.text = tempPickedVal;
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                ),
              ),
              Divider(
                height: 0.h,
                thickness: 1.w,
              ),
              Expanded(
                child: Container(
                  child: CupertinoPicker(
                    itemExtent: 40.h,
                    onSelectedItemChanged: (i) {
                      tempPickedVal = arr[i];
                    },
                    children: [
                      for (final name in arr)
                        Center(
                          child: Text(
                            name,
                            style: TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.subHeadline1,
                                _colorUtil.tertiary05),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Container startTime() {
    return Container(
      margin: EdgeInsets.all(16.h),
      height: 144.h,
      decoration: BoxDecoration(
          color: _colorUtil.tile02, borderRadius: BorderRadius.circular(8.r)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 16.w, top: 21.h),
            child: Text(
              OneAppString.of().startTime,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 24.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 88.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                      color: _colorUtil.tile01,
                      borderRadius: BorderRadius.circular(100.r)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 28.w,
                        child: GestureDetector(
                          onTap: () {
                            if (!offPeakHoursSwitch!) {
                              _showPicker(
                                  context, _startHourcontroller, hoursArray);
                            }
                          },
                          child: AbsorbPointer(
                            child: TextField(
                              controller: _startHourcontroller,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.subHeadline3,
                                  _colorUtil.tertiary00),
                              textAlign: TextAlign.center,
                              decoration:
                                  InputDecoration(border: InputBorder.none),
                              onTap: () {},
                            ),
                          ),
                        ),
                      ),
                      Center(
                        child: Text(
                          ":",
                          style: TextStyleExtension().newStyleWithColor(
                              _textStyleUtil.subHeadline3,
                              _colorUtil.tertiary00),
                        ),
                      ),
                      SizedBox(
                        width: 35.w,
                        child: GestureDetector(
                          onTap: () {
                            if (!offPeakHoursSwitch!) {
                              _showPicker(
                                  context, _startMincontroller, minArray);
                            }
                          },
                          child: AbsorbPointer(
                            child: TextField(
                              controller: _startMincontroller,
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.subHeadline3,
                                  _colorUtil.tertiary00),
                              textAlign: TextAlign.center,
                              decoration:
                                  InputDecoration(border: InputBorder.none),
                              onTap: () {},
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 5.w,
                ),
                InkWell(
                  onTap: () {
                    if (!offPeakHoursSwitch!) {
                      setState(() {
                        startTimeAMSelected = true;
                      });
                    }
                  },
                  child: Container(
                    width: 52.w,
                    height: 36.h,
                    decoration: BoxDecoration(
                        color: (startTimeAMSelected)
                            ? _colorUtil.tertiary03
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(100.r)),
                    child: Center(
                      child: Text(
                        "am",
                        style: startTimeAMSelected
                            ? TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body2, _colorUtil.button05b)
                            : TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body2, _colorUtil.tertiary00),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 5.w,
                ),
                InkWell(
                  onTap: () {
                    if (!offPeakHoursSwitch!) {
                      setState(() {
                        startTimeAMSelected = false;
                      });
                    }
                  },
                  child: Container(
                    width: 52.w,
                    height: 36.h,
                    decoration: BoxDecoration(
                        color: (startTimeAMSelected)
                            ? Colors.transparent
                            : _colorUtil.tertiary03,
                        borderRadius: BorderRadius.circular(100.r)),
                    child: Center(
                      child: Text(
                        "pm",
                        style: startTimeAMSelected
                            ? TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body2, _colorUtil.tertiary00)
                            : TextStyleExtension().newStyleWithColor(
                                _textStyleUtil.body2, _colorUtil.button05b),
                      ),
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  Container endTime() {
    return Container(
      margin: EdgeInsets.all(16.h),
      height: 144.h,
      decoration: BoxDecoration(
          color: _colorUtil.tile02, borderRadius: BorderRadius.circular(8.r)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 16.w, top: 21.h),
                child: Text(
                  OneAppString.of().endTime,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
                ),
              ),
              Spacer(),
              Padding(
                padding: EdgeInsets.only(right: 16.w, top: 21.h),
                child: FlutterSwitch(
                  disabled: offPeakHoursSwitch!,
                  width: 60.w,
                  height: 34.h,
                  activeColor: Colors.white,
                  inactiveColor: Colors.white,
                  inactiveToggleColor: _colorUtil.tertiary05,
                  activeToggleColor: _colorUtil.button03b,
                  switchBorder: Border.all(color: _colorUtil.tertiary10),
                  toggleSize: 20.w,
                  value: departureTimeSwitch,
                  borderRadius: 30.w,
                  showOnOff: false,
                  onToggle: (val) {
                    setState(() {
                      departureTimeSwitch = val;
                    });
                  },
                ),
              ),
            ],
          ),
          (departureTimeSwitch)
              ? Padding(
                  padding: EdgeInsets.only(top: 24.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 88.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                            color: _colorUtil.tile01,
                            borderRadius: BorderRadius.circular(100.r)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 28.w,
                              child: GestureDetector(
                                onTap: () {
                                  if (!offPeakHoursSwitch!) {
                                    _showPicker(context, _endHourcontroller,
                                        hoursArray);
                                  }
                                },
                                child: AbsorbPointer(
                                  child: TextField(
                                    controller: _endHourcontroller,
                                    style: TextStyleExtension()
                                        .newStyleWithColor(
                                            _textStyleUtil.subHeadline3,
                                            _colorUtil.tertiary00),
                                    textAlign: TextAlign.center,
                                    decoration: InputDecoration(
                                        border: InputBorder.none),
                                    onTap: () {},
                                  ),
                                ),
                              ),
                            ),
                            Text(
                              ":",
                              style: TextStyleExtension().newStyleWithColor(
                                  _textStyleUtil.subHeadline3,
                                  _colorUtil.tertiary00),
                            ),
                            SizedBox(
                              width: 35.w,
                              child: GestureDetector(
                                onTap: () {
                                  if (!offPeakHoursSwitch!) {
                                    _showPicker(
                                        context, _endMincontroller, minArray);
                                  }
                                },
                                child: AbsorbPointer(
                                  child: TextField(
                                    controller: _endMincontroller,
                                    style: TextStyleExtension()
                                        .newStyleWithColor(
                                            _textStyleUtil.subHeadline3,
                                            _colorUtil.tertiary00),
                                    textAlign: TextAlign.center,
                                    decoration: InputDecoration(
                                        border: InputBorder.none),
                                    onTap: () {},
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 5.w),
                      InkWell(
                        onTap: () {
                          if (!offPeakHoursSwitch!) {
                            setState(() {
                              endTimeAMSelected = true;
                            });
                          }
                        },
                        child: Container(
                          width: 52.w,
                          height: 36.h,
                          decoration: BoxDecoration(
                              color: (endTimeAMSelected)
                                  ? _colorUtil.tertiary03
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(100.r)),
                          child: Center(
                            child: Text(
                              "am",
                              style: endTimeAMSelected
                                  ? TextStyleExtension().newStyleWithColor(
                                      _textStyleUtil.body2,
                                      _colorUtil.button05b)
                                  : TextStyleExtension().newStyleWithColor(
                                      _textStyleUtil.body2,
                                      _colorUtil.tertiary00),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 5.w,
                      ),
                      InkWell(
                        onTap: () {
                          if (!offPeakHoursSwitch!) {
                            setState(() {
                              endTimeAMSelected = false;
                            });
                          }
                        },
                        child: Container(
                          width: 52.w,
                          height: 36.h,
                          decoration: BoxDecoration(
                              color: (endTimeAMSelected)
                                  ? Colors.transparent
                                  : _colorUtil.tertiary03,
                              borderRadius: BorderRadius.circular(100.r)),
                          child: Center(
                            child: Text(
                              "pm",
                              style: endTimeAMSelected
                                  ? TextStyleExtension().newStyleWithColor(
                                      _textStyleUtil.body2,
                                      _colorUtil.tertiary00)
                                  : TextStyleExtension().newStyleWithColor(
                                      _textStyleUtil.body2,
                                      _colorUtil.button05b),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                )
              : Column(
                  children: [
                    Divider(),
                    SizedBox(
                      height: 4.h,
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 16.w, top: 12.h),
                      child: Text(
                        OneAppString.of().evmcDepartureTimeInfoText,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1, _colorUtil.tertiary05),
                      ),
                    )
                  ],
                ),
        ],
      ),
    );
  }

  Container daysOfWeekWidget() {
    return Container(
      height: 144.h,
      margin: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
          color: _colorUtil.tile02, borderRadius: BorderRadius.circular(8.r)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 0.h, left: 16.w, bottom: 24.h),
            child: Text(
              OneAppString.of().daysOfTheWeek,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              dayOfWeek(OneAppString.of().shortSundayFirstLetter, 0),
              dayOfWeek(OneAppString.of().shortMondayFirstLetter, 1),
              dayOfWeek(OneAppString.of().shortTuesdayFirstLetter, 2),
              dayOfWeek(OneAppString.of().shortWednesdayFirstLetter, 3),
              dayOfWeek(OneAppString.of().shortThursdayFirstLetter, 4),
              dayOfWeek(OneAppString.of().shortFridayFirstLetter, 5),
              dayOfWeek(OneAppString.of().shortSaturdayFirstLetter, 6),
            ],
          )
        ],
      ),
    );
  }

  InkWell dayOfWeek(String day, int index) {
    bool selected = false;
    switch (index) {
      case 0:
        if (daysOfWeek.contains(Sunday)) selected = true;
        break;
      case 1:
        if (daysOfWeek.contains(Monday)) selected = true;
        break;
      case 2:
        if (daysOfWeek.contains(Tuesday)) selected = true;
        break;
      case 3:
        if (daysOfWeek.contains(Wednesday)) selected = true;
        break;
      case 4:
        if (daysOfWeek.contains(Thursday)) selected = true;
        break;
      case 5:
        if (daysOfWeek.contains(Friday)) selected = true;
        break;
      case 6:
        if (daysOfWeek.contains(Saturday)) selected = true;
        break;
      default:
    }
    return InkWell(
      onTap: () {
        if (!offPeakHoursSwitch!) {
          switch (index) {
            case 0:
              (daysOfWeek.contains(Sunday))
                  ? daysOfWeek.remove(Sunday)
                  : daysOfWeek.add(Sunday);
              break;
            case 1:
              (daysOfWeek.contains(Monday))
                  ? daysOfWeek.remove(Monday)
                  : daysOfWeek.add(Monday);
              break;
            case 2:
              (daysOfWeek.contains(Tuesday))
                  ? daysOfWeek.remove(Tuesday)
                  : daysOfWeek.add(Tuesday);
              break;
            case 3:
              (daysOfWeek.contains(Wednesday))
                  ? daysOfWeek.remove(Wednesday)
                  : daysOfWeek.add(Wednesday);
              break;
            case 4:
              (daysOfWeek.contains(Thursday))
                  ? daysOfWeek.remove(Thursday)
                  : daysOfWeek.add(Thursday);
              break;
            case 5:
              (daysOfWeek.contains(Friday))
                  ? daysOfWeek.remove(Friday)
                  : daysOfWeek.add(Friday);
              break;
            case 6:
              (daysOfWeek.contains(Saturday))
                  ? daysOfWeek.remove(Saturday)
                  : daysOfWeek.add(Saturday);
              break;
            default:
          }
          setState(() {});
        }
      },
      child: Container(
        width: 40.w,
        height: 40.w,
        decoration: BoxDecoration(
            color: (selected) ? _colorUtil.tertiary03 : _colorUtil.tile02,
            borderRadius: BorderRadius.circular(20.h)),
        child: Center(
          child: Text(
            day,
            style: TextStyleExtension().newStyleWithColor(
                _textStyleUtil.callout1,
                selected ? _colorUtil.button05b : _colorUtil.button02a),
          ),
        ),
      ),
    );
  }

  Container bottomControl() {
    return Container(
      margin: EdgeInsets.only(bottom: 32.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _editMode
              ? InkWell(
                  onTap: () {
                    BottomConfirmationDialog().showBottomDialog(
                        context,
                        OneAppString.of().evmcDeleteScheduleAlertTitleText,
                        OneAppString.of().evmcDeleteScheduleAlertSubtitleText,
                        vehicleStolenIcon,
                        OneAppString.of().evmcYesDelete,
                        OneAppString.of().goBack,
                        _deleteScheduleConfirmation);
                  },
                  child: Container(
                    width: 48.w,
                    height: 48.w,
                    decoration: BoxDecoration(
                        color: _colorUtil.tertiary03,
                        borderRadius: BorderRadius.circular(24.h)),
                    child: Icon(Icons.delete, color: _colorUtil.tile01),
                  ),
                )
              : Container(),
          if (_editMode) SizedBox(width: 35.w),
          InkWell(
            onTap: () {
              if (daysOfWeek.isEmpty) {
                showCustomToast(
                    _toastCustomWidget(
                        OneAppString.of().evmcDaysOfWeekValidationText),
                    3);
                return;
              } else {
                if (_editMode) {
                  _bloc.updateScheduleAPI(
                      widget.scheduleData,
                      startTimeAMSelected,
                      endTimeAMSelected,
                      departureTimeSwitch,
                      _startHourcontroller,
                      _endHourcontroller,
                      _startMincontroller,
                      _endMincontroller,
                      daysOfWeek,
                      _showErrorToastMessage);
                } else {
                  _bloc.createScheduleAPI(
                      startTimeAMSelected,
                      endTimeAMSelected,
                      departureTimeSwitch,
                      _startHourcontroller,
                      _endHourcontroller,
                      _startMincontroller,
                      _endMincontroller,
                      daysOfWeek,
                      _showErrorToastMessage);
                }
              }
            },
            child: Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                  color: _colorUtil.button03b,
                  borderRadius: BorderRadius.circular(24.h)),
              child: Icon(Icons.done, color: _colorUtil.tile01),
            ),
          ),
        ],
      ),
    );
  }

  void _deleteScheduleConfirmation() {
    _bloc.deleteScheduleAPI(
        widget.scheduleData!.settingId.toString(), _showErrorToastMessage);
  }

  void _showErrorToastMessage(String message) {
    showCustomToast(_toastCustomWidget(message), 3);
  }

  Widget _toastCustomWidget(String toastMessage) {
    return CommonToast(
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
      showIcon: false,
    );
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }
}
