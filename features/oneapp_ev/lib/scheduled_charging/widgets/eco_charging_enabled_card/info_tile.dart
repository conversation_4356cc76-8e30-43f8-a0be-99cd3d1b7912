// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';

class InfoTile extends ThemedStatelessWidget {
  final String infoText;

  InfoTile({
    Key? key,
    required this.infoText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 339.w,
        height: 72.h,
        margin: EdgeInsets.symmetric(horizontal: 17.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: colorUtil.tertiary12,
        ),
        child: Center(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 14.h),
            child: Text(
              this.infoText,
              textAlign: TextAlign.center,
              style: TextStyleExtension()
                  .newStyleWithColor(textStyleUtil.body1, colorUtil.tertiary05),
            ),
          ),
        ),
      ),
    );
  }
}
