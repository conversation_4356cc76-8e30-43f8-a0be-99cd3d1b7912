// Package imports:
import 'package:intl/intl.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/electric_vehicle/entity/mc/entity/eco_schedule_detail_entity/eco_schedule_detail_entity.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/format_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/entity_helper/vehicle/charge_timer_body_helper.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';

// Project imports:
import '../../../scheduled_charging_bloc.dart';
import '../../../utils/util.dart';
import '../../eco_charging_card/eco_charging_card_bloc.dart';

import 'package:oa_network_impl/electric_vehicle/entity/mc/entity/create_schedule_req_entity.dart'
    as c;
import 'package:oa_network_impl/electric_vehicle/entity/mc/entity/update_schedule_req_entity.dart'
    as u;
import 'package:oa_network_impl/one_app/entity/charge_management_detail_entity.dart'
    as cm;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleInfo;

class ScheduleListingBloc extends BlocBase {
  final _oaApi = APIClients.oneAppApiClient;
  final api = APIClientConfig.oneAppClient;
  final _evApi = APIClients.evApiClient;
  List<cm.TimerChargeInfo>? listingData = [];
  int? _maxScheduleLimit;
  cm.ChargeInfo? chargeInfo;
  String? acquisitionDatetime;
  int timeOut = 150;
  late Function(bool) progressHandlerCallback;
  // List<num?> _settingIdArray = [];
  ScheduledChargingBloc? _schedulePageBloc = null;
  EcoChargeTimeObject? ecoChargeTimeObject;
  cm.TimerChargeInfo? ecoScheduleObject;

  String? ecoStartHour;
  String? ecoStartMin;
  String? ecoEndHour;
  String? ecoEndMin;
  EcoChargeTimeObject? ecoObject;
  vehicleInfo.Payload? vehicleItem;
  Stream<List<cm.TimerChargeInfo>?> get scheduleList => _scheduleList.stream;
  final _scheduleList = BehaviorSubject<List<cm.TimerChargeInfo>?>();

  Stream<cm.TimerChargeInfo> get offPeakSchedule => _offPeakSchedule.stream;
  final _offPeakSchedule = BehaviorSubject<cm.TimerChargeInfo>();

  Stream<bool> get createScheduleSuccess => _createScheduleSuccess.stream;
  final _createScheduleSuccess = BehaviorSubject<bool>();

  Stream<String> get lastUpdatedTime => _lastUpdatedTime.stream;
  final _lastUpdatedTime = BehaviorSubject<String>();

  Stream<EcoChargeTimeObject?> get ecoTimesObj => _ecoTimesObj.stream;
  final _ecoTimesObj = BehaviorSubject<EcoChargeTimeObject?>();

  void init(cm.ChargeInfo? chargeInfoData, String? acquisitionDatetimeData,
      Function(bool) progressHandler, ScheduledChargingBloc? schedulePageBloc) {
    _schedulePageBloc = schedulePageBloc;
    chargeInfo = chargeInfoData;
    acquisitionDatetime = acquisitionDatetimeData;
    progressHandlerCallback = progressHandler;
    setup();
    if (acquisitionDatetime != null) {
      _setLastUpdatedDate(acquisitionDatetime);
    }
  }

  void setup() async {
    vehicleItem =
        await VehicleRepo().getLocalPayloadFromVin(Global.getInstance().vin);
    await fetchEcoScheduleDetails();
    int startTime = DateTime.now().millisecondsSinceEpoch;
    fetchMultidaySchedules(startTime, '', null, null);
  }

  int? getMaxSchedulesLimit() {
    return _maxScheduleLimit;
  }

  void _setLastUpdatedDate(String? lastUpdatedDate) {
    if (lastUpdatedDate != null && lastUpdatedDate.isNotEmpty) {
      String lastUpdated = lastUpdatedDate;
      DateTime dateTime =
          DateFormat("yyyy-MM-ddTHH:mm:ssZ").parse(lastUpdated, true).toLocal();
      String updatedTime = "";
      String? todayOrYesterday = checkDateIsTodayOrYesterday(dateTime);
      if (todayOrYesterday != null) {
        updatedTime = todayOrYesterday +
            convertDateTimeFormat(
                lastUpdated, "yyyy-MM-ddTHH:mm:ssZ", " @ h:mma");
      } else {
        updatedTime = convertDateTimeFormat(
            lastUpdated, "yyyy-MM-ddTHH:mm:ssZ", "MMM dd @ h:mma");
      }
      updatedTime = updatedTime
          .replaceAll('@', OneAppString.of().at)
          .replaceAll("AM", "am")
          .replaceAll("PM", "pm");
      _lastUpdatedTime.sink.add(OneAppString.of().updated(updatedTime));
    }
  }

  void setEcoTime(
      String startHour, String startMin, String endHour, String endMin) {
    ecoStartHour = startHour;
    ecoStartMin = startMin;
    ecoEndHour = endHour;
    ecoEndMin = endMin;
  }

  //remote-control api
  Future<void> fetchMultidaySchedules(int startTime, String requestNo,
      Function? savedSuccessfullyBottomSheet, Function? reloadPage) async {
    String? vin = Global.getInstance().vin;
    String? guid = Global.getInstance().guid;

    final commonResponse =
        await _oaApi.fetchMultidayScheduleRemoteControlStatus(
      correlationId: Global.getInstance().correlationId,
      generation: vehicleItem?.generation ?? "",
      brand: vehicleItem?.brand ?? "",
      vin: vin ?? "",
      guid: guid ?? "",
      requestNo: requestNo,
    );

    cm.ChargeManagementDetailEntity? scheduleListResponse =
        commonResponse.response;

    if (scheduleListResponse?.payload != null) {
      if (scheduleListResponse!.payload!.remoteControlResult != null) {
        if (scheduleListResponse.payload!.remoteControlResult!.result == 0 &&
            scheduleListResponse.payload!.remoteControlResult!.status == 0) {
          if (reloadPage != null) {
            progressHandlerCallback(false);
            reloadPage(true);
          } else {
            if (scheduleListResponse.payload!.vehicleInfo!.timerChargeInfo !=
                null) {
              listingData =
                  scheduleListResponse.payload!.vehicleInfo!.timerChargeInfo;
              acquisitionDatetime = scheduleListResponse
                  .payload!.vehicleInfo?.acquisitionDatetime!;
              _setLastUpdatedDate(acquisitionDatetime);
              _maxScheduleLimit = scheduleListResponse
                  .payload!.vehicleInfo!.maxNoOfChargeSchedules as int?;
              saveEcoScheduleAndRemoveFromList();
            } else {
              _scheduleList.sink.add([]);
            }
          }
        } else {
          int currentTime = DateTime.now().millisecondsSinceEpoch;
          double timeElapsed = (currentTime - startTime) / 1000;
          if (timeElapsed >= timeOut) {
            _scheduleList.sink.add([]);
          } else {
            Future.delayed(Duration(seconds: 3), () async {
              if (reloadPage != null) {
                fetchMultidaySchedules(startTime, requestNo,
                    savedSuccessfullyBottomSheet, reloadPage);
              } else {
                fetchMultidaySchedules(startTime, requestNo, null, null);
              }
            });
          }
        }
      } else if (scheduleListResponse.payload!.vehicleInfo!.timerChargeInfo !=
          null) {
        listingData =
            scheduleListResponse.payload!.vehicleInfo!.timerChargeInfo;
        acquisitionDatetime =
            scheduleListResponse.payload!.vehicleInfo?.acquisitionDatetime!;
        _setLastUpdatedDate(acquisitionDatetime);
        _maxScheduleLimit = scheduleListResponse
            .payload!.vehicleInfo!.maxNoOfChargeSchedules as int?;
        saveEcoScheduleAndRemoveFromList();
      } else {
        _scheduleList.sink.add([]);
      }
    } else {
      _scheduleList.sink.add([]);
    }
  }

  void ecoChargingToggled(EcoChargeTimeObject ecoChargeObj, bool optIn,
      Function savedSuccessfullyBottomSheet) {
    // ignore: unnecessary_null_comparison
    if (ecoChargeObj == null) return;
    setEcoTime(ecoChargeObj.startHour, ecoChargeObj.startMin,
        ecoChargeObj.endHour, ecoChargeObj.endMin);

    if (optIn) {
      //toggle ON
      //if YES, do the POST call (opt in) to DB, create schedule on vehicle
      optInOptOutAPI(
        savedSuccessfullyBottomSheet,
        optIn: true,
      );
    } else {
      //toggle OFF
      //Do the POST call to DB (opt out) to DB, delete schedule on vehicle
      optInOptOutAPI(
        savedSuccessfullyBottomSheet,
        optIn: false,
      );
    }
    FireBaseAnalyticsLogger.logMarketingGroupEvent(
      VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
      childEventName: VehicleAnalyticsEvent.VEHICLE_EV_ECO_CHARGE_TOGGLE,
    );
  }

  void optInOptOutAPI(Function savedSuccessfullyBottomSheet,
      {bool optIn = false, bool createSchedule = false}) async {
    _scheduleList.sink.add(null);
    String? vin = Global.getInstance().vin;
    String? guid = Global.getInstance().guid;
    String homeLat = Global.getInstance().homeLatitude;
    String homeLong = Global.getInstance().homeLongitude;

    String appLat = '';
    String appLong = '';
    Position? _vehicleLocation = _schedulePageBloc?.vehicleLocation;
    Position? _currentLocation = Global.getInstance().currentLocation ?? null;

    if (_vehicleLocation != null) {
      appLat = _vehicleLocation.lat.toString();
      appLong = _vehicleLocation.lng.toString();
    } else if (_currentLocation != null) {
      appLat = _currentLocation.lat.toString();
      appLong = _currentLocation.lng.toString();
    }

    EcoScheduleDetailEntity? ecoScheduleResponse;
    //API Call
    final commonResponse = await _evApi.saveEcoScheduleDetails(
        vin!, guid!, homeLat, homeLong, appLat, appLong, optIn, '');
    ecoScheduleResponse = commonResponse.response;
    if (ecoScheduleResponse != null) {
      if (ecoScheduleResponse.responseCode == 200) {
        if (optIn) {
          createEcoChargingScheduleAPI(errorCallBack);
        } else {
          deleteScheduleAPI(ecoScheduleObject!.settingId.toString());
        }
      } else {
        refreshUI('');
      }
    } else {
      refreshUI('');
      savedSuccessfullyBottomSheet(false);
    }
  }

  Future<void> refreshScheduleData(
      Function savedSuccessfullyBottomSheet, Function reloadPage) async {
    //Refreshing the schedule data using Realtime Status API
    String? vin = Global.getInstance().vin;
    String? deviceId = Global.getInstance().fcmDeviceId;
    _scheduleList.sink.add(null);
    if (vehicleItem != null) {
      int startTime = DateTime.now().millisecondsSinceEpoch;
      final postResponse = await api.postRealTimeStatusRequest(
          vin ?? "",
          vehicleItem?.brand ?? "",
          vehicleItem?.generation ?? "",
          deviceId ?? "");
      final payLoad = postResponse.response?.payload;
      if (payLoad != null) {
        if (payLoad.returnCode == "ONE-RES-10000") {
          fetchFromRealtimeStatusAPI(
            startTime,
            "",
            savedSuccessfullyBottomSheet,
            reloadPage,
          );
        } else {
          fetchFromRealtimeStatusAPI(startTime, payLoad.appRequestNo!,
              savedSuccessfullyBottomSheet, reloadPage);
        }
        FireBaseAnalyticsLogger.logSuccessAPI(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_SUCCESS,
            category: LogCategory.FL_VEHI);
      }

      if (postResponse.error != null) {
        progressHandlerCallback(false);
        FireBaseAnalyticsLogger.logError(
            VehicleAnalyticsEvent.VEHICLE_POST_ELECTRIC_REALTIME_STATUS_FAILURE,
            category: LogCategory.FL_VEHI);
      }
    }
  }

  Future<void> fetchFromRealtimeStatusAPI(int startTime, String appRequestNo,
      Function savedSuccessfullyBottomSheet, Function reloadPage) async {
    String vin = Global.getInstance().vin ?? "";
    final commonResponse = await _oaApi.fetchClimateRealTimeStatus(
      brand: vehicleItem?.brand ?? Global.getInstance().appBrand,
      generation: vehicleItem?.generation ?? "",
      vin: vin,
      requestNo: appRequestNo,
    );

    cm.ChargeManagementDetailEntity? scheduleListResponse =
        commonResponse.response;

    if (scheduleListResponse!.payload != null) {
      int? status = scheduleListResponse.payload!.realtimeStatusResult?.status;
      int? result = scheduleListResponse.payload!.realtimeStatusResult?.result;
      if (status != null && status == 0) {
        if (result != null) {
          if (result == 0) {
            if (scheduleListResponse.payload!.vehicleInfo!.chargeInfo != null) {
              listingData =
                  scheduleListResponse.payload!.vehicleInfo!.timerChargeInfo;
              _scheduleList.sink.add(listingData);
            } else {
              _scheduleList.sink.add([]);
            }
            acquisitionDatetime =
                scheduleListResponse.payload!.vehicleInfo!.acquisitionDatetime;
            _setLastUpdatedDate(acquisitionDatetime);
          } else if (result == 1) {
            _scheduleList.sink.add([]);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 2) {
            _scheduleList.sink.add([]);
            errorCallBack(savedSuccessfullyBottomSheet);
          } else if (result == 3) {
            _scheduleList.sink.add([]);
            errorCallBack(savedSuccessfullyBottomSheet);
          }
        } else {
          errorCallBack(savedSuccessfullyBottomSheet);

          _scheduleList.sink.add([]);
        }
      } else {
        int currentTime = DateTime.now().millisecondsSinceEpoch;
        double timeElapsed = (currentTime - startTime) / 1000;
        if (timeElapsed >= timeOut) {
          _scheduleList.sink.add([]);
          errorCallBack(savedSuccessfullyBottomSheet);
        } else {
          fetchFromRealtimeStatusAPI(startTime, appRequestNo,
              savedSuccessfullyBottomSheet, reloadPage);
        }
      }
      FireBaseAnalyticsLogger.logSuccessAPI(
          VehicleAnalyticsEvent.VEHICLE_FETCH_CHARGE_MANAGEMENT_DETAILS_SUCCESS,
          category: LogCategory.FL_VEHI);
    }
  }

  Future<void> updateScheduleAPI(bool updatedVal,
      cm.TimerChargeInfo scheduleObj, Function errorCallBack) async {
    _scheduleList.sink.add(null);

    u.UpdateScheduleRequestEntity reqBodyObj;
    if (scheduleObj.endTime != null) {
      reqBodyObj = u.UpdateScheduleRequestEntity(
        settingId: scheduleObj.settingId as int?,
        enabled: updatedVal,
        daysOfTheWeek: scheduleObj.daysOfTheWeek,
        startTime: u.StartTime(
          hour: int.parse(scheduleObj.startTime!.split(':').first),
          minute: int.parse(scheduleObj.startTime!.split(':').last),
        ),
        endTime: u.StartTime(
          hour: int.parse(scheduleObj.endTime!.split(':').first),
          minute: int.parse(scheduleObj.endTime!.split(':').last),
        ),
      );
    } else {
      reqBodyObj = u.UpdateScheduleRequestEntity(
        settingId: scheduleObj.settingId as int?,
        enabled: updatedVal,
        daysOfTheWeek: scheduleObj.daysOfTheWeek,
        startTime: u.StartTime(
          hour: int.parse(scheduleObj.startTime!.split(':').first),
          minute: int.parse(scheduleObj.startTime!.split(':').last),
        ),
      );
    }
    String? guid = Global.getInstance().guid;

    final commonResponse = await _oaApi.updateMultidayChargeSchedule(
        reqBodyObj,
        Global.getInstance().correlationId,
        Global.getInstance().vin ?? "",
        guid!,
        'application/json',
        vehicleItem?.generation ?? "",
        Global.getInstance().fcmDeviceId ?? "");

    if (commonResponse.response != null) {
      String requestNo = commonResponse.response?.payload?.appRequestNo ?? "";
      int startTime = DateTime.now().millisecondsSinceEpoch;
      fetchMultidaySchedules(startTime, requestNo, null, null);
    } else if (commonResponse.error != null) {
      errorCallBack(commonResponse.error!.errorMessage);
    }
  }

  Future<void> deleteScheduleAPI(String settingId) async {
    String? guid = Global.getInstance().guid;

    final commonResponse = await _oaApi.deleteMultidayChargeSchedule(
        settingId,
        Global.getInstance().correlationId,
        Global.getInstance().vin ?? "",
        guid!,
        'application/json',
        vehicleItem?.generation ?? "",
        Global.getInstance().fcmDeviceId ?? "");
    if (commonResponse.response != null) {
      String requestNo = commonResponse.response?.payload?.appRequestNo ?? "";
      refreshUI(requestNo, reloadEcoSchedule: true);
    } else if (commonResponse.error != null) {
      refreshUI('');
    }
  }

  Future<void> fetchEcoScheduleDetails() async {
    final _evApi = APIClients.evApiClient;
    String? vin = Global.getInstance().vin;
    String? guid = Global.getInstance().guid;
    String homeLat = Global.getInstance().homeLatitude;
    String homeLong = Global.getInstance().homeLongitude;

    String appLat = '';
    String appLong = '';
    Position? _vehicleLocation = _schedulePageBloc?.vehicleLocation;
    Position? _currentLocation = Global.getInstance().currentLocation ?? null;

    if (_vehicleLocation != null) {
      appLat = _vehicleLocation.lat.toString();
      appLong = _vehicleLocation.lng.toString();
    } else if (_currentLocation != null) {
      appLat = _currentLocation.lat.toString();
      appLong = _currentLocation.lng.toString();
    }

    EcoScheduleDetailEntity? ecoScheduleResponse;

    //API Call
    final commonResponse = await _evApi.fetchEcoScheduleDetails(
      vin ?? "",
      guid ?? "",
      homeLat,
      homeLong,
      appLat,
      appLong,
    );
    ecoScheduleResponse = commonResponse.response;

    if (ecoScheduleResponse!.baInfo == null) {
      ecoScheduleResponse.baInfo = BaInfo(
          abbrev: 'ERCOT_NORTHCENTRAL',
          name: 'ERCOT North Central',
          echoPercentile: '40',
          echoTimes: EchoTimes(start: '23:00', end: '04:00'));
    }
    String startTime = convertScheduleTimeTo12Hour(
        ecoScheduleResponse.baInfo?.echoTimes?.start ?? "",
        omitMinutes: true);
    String endTime = convertScheduleTimeTo12Hour(
        ecoScheduleResponse.baInfo?.echoTimes?.end ?? "",
        omitMinutes: true);
    String ecoChargingDesc = OneAppString.of().offpeakDescription +
        startTime +
        ' ' +
        OneAppString.of().and +
        ' ' +
        endTime;
    var startArr = ecoScheduleResponse.baInfo?.echoTimes?.start?.split(':');
    var endArr = ecoScheduleResponse.baInfo?.echoTimes?.end?.split(':');

    bool optedIn = false;
    if (ecoScheduleResponse.ecoEnrollmentDetails != null) {
      optedIn = ecoScheduleResponse.ecoEnrollmentDetails?.enabled ?? true;
    }

    EcoChargeTimeObject ecoObject = EcoChargeTimeObject(
        descriptionText: ecoChargingDesc,
        startTime: startTime,
        endTime: endTime,
        startHour: startArr?.first ?? '',
        startMin: startArr?.last ?? '',
        endHour: endArr?.first ?? '',
        endMin: endArr?.last ?? '',
        optedIn: optedIn);
    ecoChargeTimeObject = ecoObject;
    setEcoTime(ecoChargeTimeObject!.startHour, ecoChargeTimeObject!.startMin,
        ecoChargeTimeObject!.endHour, ecoChargeTimeObject!.endMin);
  }

  Future<void> createEcoChargingScheduleAPI(Function errorCallBack) async {
    c.CreateScheduleRequestEntity reqBodyObj = c.CreateScheduleRequestEntity(
      enabled: true,
      startTime: c.StartTime(
        hour: int.parse(ecoStartHour ?? ""),
        minute: int.parse(ecoStartMin ?? ""),
      ),
      endTime: c.StartTime(
        hour: int.parse(ecoEndHour ?? ""),
        minute: int.parse(ecoEndMin ?? ""),
      ),
      daysOfTheWeek: [
        Sunday,
        Monday,
        Tuesday,
        Wednesday,
        Thursday,
        Friday,
        Saturday
      ],
    );

    String? guid = Global.getInstance().guid;

    final commonResponse = await _oaApi.createMultidayChargeSchedule(
        reqBodyObj,
        Global.getInstance().correlationId,
        Global.getInstance().vin ?? "",
        guid!,
        'application/json',
        vehicleItem?.generation ?? "",
        Global.getInstance().fcmDeviceId ?? "");

    if (commonResponse.response != null) {
      String? requestNo = commonResponse.response?.payload?.appRequestNo ?? "";
      refreshUI(requestNo, reloadEcoSchedule: true);
      progressHandlerCallback(false);
      _createScheduleSuccess.sink.add(true);
    } else if (commonResponse.error != null) {
      progressHandlerCallback(false);
      errorCallBack(commonResponse.error!.errorMessage);
    }
  }

  ChargeTimerBodyHelper constructImmediateChargeBody() {
    ChargeTimerBodyHelper chargeTimerBodyHelper = ChargeTimerBodyHelper();

    chargeTimerBodyHelper = ChargeTimerBodyHelper(
        command: IMMEDIATE_CHARGE, remoteHvac: null, reservationCharge: null);

    return chargeTimerBodyHelper;
  }

  void saveEcoScheduleAndRemoveFromList() {
    int? offPeakIndex = wtGetOffPeakIndex(
        listingData ?? [],
        ecoStartHour ?? "",
        ecoStartMin ?? "",
        ecoEndHour ?? "",
        ecoEndMin ?? ""); //Saving the first off-peak index

    if (listingData!.isNotEmpty && offPeakIndex != null) {
      ecoScheduleObject = listingData![offPeakIndex];
      listingData!
          .removeAt(offPeakIndex); //Remove off-peak schedule from listing array
    } else {
      ecoScheduleObject = null;
    }
    _scheduleList.sink.add(listingData);
  }

  void refreshUI(String requestNo, {bool reloadEcoSchedule = false}) async {
    //Refreshing the schedule data using Remote Control API with request number from the previous operation
    ecoScheduleObject = null;
    _scheduleList.sink.add(null);
    if (reloadEcoSchedule) {
      await fetchEcoScheduleDetails();
    }
    int startTime = DateTime.now().millisecondsSinceEpoch;
    fetchMultidaySchedules(startTime, requestNo, null, null);
  }

  void errorCallBack(Function savedSuccessfullyBottomSheet) {
    Future.delayed(const Duration(milliseconds: 500), () {
      savedSuccessfullyBottomSheet();
    });
  }

  @override
  void dispose() {
    _scheduleList.close();
    _createScheduleSuccess.close();
    _lastUpdatedTime.close();
    _offPeakSchedule.close();
    _ecoTimesObj.close();
  }
}
