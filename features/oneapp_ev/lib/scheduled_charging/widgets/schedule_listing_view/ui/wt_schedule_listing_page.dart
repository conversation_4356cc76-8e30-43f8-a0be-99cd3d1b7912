// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/custom_page_route.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:super_tooltip/super_tooltip.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';

// Project imports:
import '../../../../core/widgets/floating_button_widget.dart';
import '../../../scheduled_charging_bloc.dart';
import '../../../utils/util.dart';
import '../../eco_charging_card/eco_charging_card.dart';
import '../../eco_charging_card/eco_charging_card_bloc.dart';
import '../../no_schedules_widget/no_schedules.dart';
import '../../schedule_create_view/wt_schedule_create_view.dart';
import '../bloc/wt_schedule_listing_bloc.dart';
import 'wt_schedule_list_card.dart';

import 'package:oa_network_impl/one_app/entity/charge_management_detail_entity.dart'
    as cm;

//WattTime
class ScheduleListingScreen extends StatefulWidget {
  final cm.ChargeInfo? chargeInfo;
  final String? acquisitionDatetime;
  final ScrollController? scrollController;
  final BuildContext? materialContext;
  final Function(bool)? reloadChargeManagementPage;
  final ScheduledChargingBloc? schedulePageBloc;

  ScheduleListingScreen(
      {Key? key,
      this.chargeInfo,
      this.acquisitionDatetime,
      this.scrollController,
      this.materialContext,
      this.reloadChargeManagementPage,
      this.schedulePageBloc})
      : super(key: key);

  @override
  State<ScheduleListingScreen> createState() => _ScheduleListingScreenState();
}

class _ScheduleListingScreenState extends State<ScheduleListingScreen> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  ScrollController listScrollcontroller = ScrollController();

  bool noSchedule = false;
  bool scheduleLimitReached = false;
  bool showLoader = true;

  String requestNo = '';
  ScheduleListingBloc _bloc = ScheduleListingBloc();
  EcoChargingCardBloc _ecoChargingBloc = EcoChargingCardBloc();
  List<cm.TimerChargeInfo>? _listingData = [];
  cm.TimerChargeInfo? _offPeakSchedule;
  SuperTooltip? tooltip;
  BuildContext? offPeakWidgetContext;
  bool ecoChargingEnabled = false;

  @override
  void initState() {
    super.initState();
    _bloc.init(widget.chargeInfo, widget.acquisitionDatetime,
        _progressHandlerCallback, widget.schedulePageBloc);
    FireBaseAnalyticsLogger.logScreenVisit(
        VehicleAnalyticsEvent.VEHICLE_EV_MULTIDAY_SCHEDULE_PAGE);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: true,
      child: StreamBuilder<List<cm.TimerChargeInfo>?>(
        stream: _bloc.scheduleList,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            _listingData = snapshot.data;
            _offPeakSchedule = null;

            scheduleLimitReached =
                (_listingData!.length == _bloc.getMaxSchedulesLimit());
            noSchedule = _listingData!.isEmpty;
            return Container(
              child: noSchedule
                  ? Column(
                      children: [
                        ecoChargingWidget(),
                        Expanded(
                            child: (_bloc.ecoScheduleObject != null)
                                ? Container()
                                : NoScheduleWidget(
                                    subtitleText:
                                        OneAppString.of().noSchedulesSubtitle,
                                  )),
                        createScheduleButton(),
                        SizedBox(height: 10.h)
                      ],
                    )
                  : Column(
                      children: [
                        ecoChargingWidget(),
                        Expanded(child: scheduleListing(_listingData)),
                        createScheduleButton(),
                        SizedBox(height: 10.h)
                      ],
                    ),
            );
          } else {
            return _shimmerLoadingLayout();
          }
        },
      ),
      // ),
    );
  }

  Widget ecoChargingWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 17.w, vertical: 17.h),
      child: EcoChargingCard(
        ecoChargeTimeObject: _bloc.ecoChargeTimeObject,
        schedulePageBloc: widget.schedulePageBloc,
        showToggle: true,
        onToggle: (val, ecoChargeObject) {
          _bloc.ecoChargingToggled(
              ecoChargeObject, val, _savedResponseCallback);
        },
      ),
    );
  }

  Widget scheduleListing(List<cm.TimerChargeInfo>? listingData) {
    List<cm.TimerChargeInfo>? listingDataArray = [];
    listingDataArray = listingData;

    if (listingDataArray != null && listingDataArray.isEmpty) {
      return schedulesInfoImg();
    }

    return Container(
        padding: EdgeInsets.symmetric(horizontal: 17.w),
        child: ListView.separated(
          separatorBuilder: (context, index) => SizedBox(height: 8.h),
          shrinkWrap: true,
          itemCount: listingDataArray != null ? listingDataArray.length + 1 : 0,
          itemBuilder: (BuildContext context, int index) {
            if (listingDataArray!.length != index) {
              bool hasEndTime = false;
              cm.TimerChargeInfo listingObj = listingDataArray[index];
              if (listingObj.endTime != null) hasEndTime = true;

              String startTimeToDisplay =
                  convertScheduleTimeTo12Hour(listingObj.startTime!);
              String endTimeToDisplay = hasEndTime
                  ? '${convertScheduleTimeTo12Hour(listingObj.endTime!)}'
                  : '';

              String lineOneText = hasEndTime
                  ? '$startTimeToDisplay - $endTimeToDisplay'
                  : '$startTimeToDisplay';

              String lineTwoText =
                  getScheduleDaysString(listingObj.daysOfTheWeek!);

              return ScheduleListCard(
                listingData: listingObj,
                lineOneText: lineOneText,
                lineTwoText: lineTwoText,
                isOffPeakSchedule: false,
                onToggle: (val) {
                  _bloc.updateScheduleAPI(
                      val, listingObj, _showErrorToastMessage);
                },
                onPress: () {
                  Navigator.of(context)
                      .push(CustomPageRoute(
                          page: ScheduleCreateViewScreen(
                    scheduleData: listingObj,
                    isOffPeakSchedule: false,
                  )))
                      .then((value) {
                    if (value != null) {
                      ScheduleBoxedReturns returnVal = value;
                      requestNo = returnVal.requestNo ?? '';
                      showCustomToast(
                          _toastCustomWidget(returnVal.toastMsg), 2);
                      _bloc.refreshUI(requestNo);
                    }
                  });
                },
              );
            } else {
              return _refreshIconLayout();
            }
          },
        ));
  }

  Widget schedulesInfoImg() {
    Color gradientStart = Colors.black;
    Color gradientEnd = Colors.transparent;

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: ShaderMask(
              shaderCallback: (rect) {
                return LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [gradientStart, gradientEnd],
                ).createShader(
                    Rect.fromLTRB(0, 0, rect.width - 40.w, rect.height - 49.h));
              },
              blendMode: BlendMode.darken,
              child: Container(
                height: 296.h,
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: ExactAssetImage(
                          _bloc.vehicleItem?.make.toLowerCase() == makeLexus
                              ? mcBGImgLexus
                              : mcBGImg),
                      fit: BoxFit.cover),
                ),
              ),
            ),
          ),
          Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 30.h, left: 16.w, right: 16.w),
                child: Text(
                  OneAppString.of().evmcNoScheduleTitleText,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.callout2, _colorUtil.tertiary15),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 24.h, left: 24.w, right: 24.w),
                child: Text(
                  OneAppString.of().evmcNoScheduleSubtitleText,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline3, _colorUtil.tertiary15),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget createScheduleButton() {
    return Container(
      color: _colorUtil.tile01,
      width: double.infinity,
      child: Center(
        child: FloatingButtonWidget(
          text: OneAppString.of().evmcCreateScheduleButtonText,
          isEnabled:
              (_offPeakSchedule != null) ? !_offPeakSchedule!.enabled! : true,
          disabledBackgroundColor: _colorUtil.button02d,
          disabledButtonTextColor: _colorUtil.button05a,
          onPressed: () {
            showMaterialModalBottomSheet(
              expand: false,
              isDismissible: true,
              context: context,
              useRootNavigator: true,
              backgroundColor: _colorUtil.tile01,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(CARD_RADIUS),
                ),
              ),
              builder: (context) => SafeArea(
                top: true,
                bottom: true,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(child: ScheduleCreateViewScreen()),
                  ],
                ),
              ),
            ).then((value) {
              if (value != null) {
                ScheduleBoxedReturns returnVal = value;
                String requestNo = returnVal.requestNo ?? '';
                showCustomToast(_toastCustomWidget(returnVal.toastMsg), 2);
                _bloc.refreshUI(requestNo);
              }
            });
            //vehicle_ev_create_a_schedule_button_cta
            FireBaseAnalyticsLogger.logMarketingGroupEvent(
              VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
              childEventName:
                  VehicleAnalyticsEvent.VEHICLE_EV_CREATE_A_SCHEDULE_BUTTON_CTA,
            );
          },
        ),
      ),
    );
  }

  Widget _refreshIconLayout() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        children: [
          InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              _bloc.refreshScheduleData(
                  _savedResponseCallback, _reloadChargeManagementPage);
            },
            child: CircleAvatar(
              radius: 24.r,
              backgroundColor: _colorUtil.button02d,
              child: SvgPicture.asset(
                refreshIcon,
                colorFilter: ColorFilter.mode(
                  _colorUtil.button02a,
                  BlendMode.srcIn,
                ),
                semanticsLabel: REFRESH_BUTTON,
              ),
            ),
          ),
          StreamBuilder<String>(
              stream: _bloc.lastUpdatedTime,
              builder: (context, snapshot) {
                return Container(
                  margin: EdgeInsets.only(top: 8.h),
                  child: Text(
                    snapshot.hasData ? snapshot.data! : " - ",
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.caption1, _colorUtil.tertiary05),
                  ),
                );
              }),
        ],
      ),
    );
  }

  void _reloadChargeManagementPage(bool reload) {
    _progressHandlerCallback(false);
  }

  void _savedResponseCallback() {
    showCustomToast(
        _responseToastCustomWidget(OneAppString.of().failure, false), 3);
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }

  Widget _shimmerLoadingLayout() {
    return Padding(
      padding: EdgeInsets.only(left: 12.w, right: 12.w),
      child: Column(
        children: [
          Container(height: 8.h),
          shimmerRectangle(80.h, double.maxFinite, CARD_RADIUS_SMALL),
          Container(height: 8.h),
          shimmerRectangle(80.h, double.maxFinite, CARD_RADIUS_SMALL),
          Container(height: 8.h),
          shimmerRectangle(80.h, double.maxFinite, CARD_RADIUS_SMALL),
          Container(height: 8.h),
        ],
      ),
    );
  }

  void _showErrorToastMessage(String message) {
    showCustomToast(_toastCustomWidget(message), 3);
  }

  void showOffPeakTooltip(BuildContext? widgetContext) {
    if (tooltip != null && tooltip!.isOpen) {
      tooltip!.close();
      return;
    }
    tooltip = SuperTooltip(
      popupDirection: TooltipDirection.down,
      borderRadius: 30.r,
      hasShadow: false,
      arrowBaseWidth: 40.w,
      arrowLength: 15.h,
      borderWidth: 0,
      containsBackgroundOverlay: true,
      outsideBackgroundColor: Color.fromRGBO(0, 0, 0, 0.5),
      dismissOnTapOutside: true,
      content: Material(
          child: Padding(
        padding: EdgeInsets.only(top: 20.h),
        child: Container(
          height: 190.h,
          width: 343.w,
          child: Column(
            children: [
              Container(
                height: 60.h,
                width: 50.w,
                child: SvgPicture.asset(
                  offPeakTooltipIcon,
                  height: 48.w,
                  width: 48.w,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 16.h),
                child: Text(
                  OneAppString.of().evmcOffPeakButtonText,
                  style: TextStyleExtension().newStyleWithColor(
                      _textStyleUtil.subHeadline3, _colorUtil.tertiary03),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 8.h),
                child: Container(
                  width: 200.w,
                  child: Text(
                    OneAppString.of().evmcOffPeakTooltipText,
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.callout1, _colorUtil.tertiary05),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      )),
    );

    tooltip!.show(widgetContext!);
  }

  Widget _toastCustomWidget(String toastMessage) {
    return CommonToast(
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
      showIcon: false,
    );
  }

  Widget _responseToastCustomWidget(String toastMessage, bool isSuccessful) {
    return CommonToast(
      iconColor: _colorUtil.tertiary15,
      iconPath: isSuccessful ? checkIcon : closeIcon,
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
    );
  }

  @override
  void dispose() {
    _bloc.dispose();
    _ecoChargingBloc.dispose();
    super.dispose();
  }
}
