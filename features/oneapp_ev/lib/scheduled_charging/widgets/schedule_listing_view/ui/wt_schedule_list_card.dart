// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

// Project imports:
import '../../../../core/widgets/themed_widget.dart';

import 'package:oa_network_impl/one_app/entity/charge_management_detail_entity.dart'
    as cm;

// Project imports:

class ScheduleListCard extends ThemedStatelessWidget {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;

  final cm.TimerChargeInfo listingData;
  final String lineOneText;
  final String lineTwoText;
  final bool isOffPeakSchedule;
  final Function? onPress;
  final Function? onToggle;

  ScheduleListCard({
    Key? key,
    required this.listingData,
    required this.lineOneText,
    required this.lineTwoText,
    required this.isOffPeakSchedule,
    this.onPress,
    this.onToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 72.h,
      child: Center(
        child: ListTile(
          tileColor: _colorUtil.tile02,
          title: Text(
            lineOneText,
            style: TextStyleExtension().newStyleWithColor(
                textStyleUtil.subHeadline1, _colorUtil.tertiary03),
          ),
          subtitle: Text(
            '$lineTwoText',
            style: TextStyleExtension().newStyleWithColor(
                textStyleUtil.callout1, _colorUtil.tertiary05),
          ),
          trailing: SizedBox(
            height: 34.0.h,
            width: 60.0.w,
            child: FlutterSwitch(
              width: 60.0.w,
              height: 34.0.h,
              activeColor: Colors.white,
              inactiveColor: Colors.white,
              inactiveToggleColor: _colorUtil.tertiary05,
              activeToggleColor: _colorUtil.button03b,
              switchBorder: Border.all(color: _colorUtil.tertiary10),
              toggleSize: 20.0.w,
              value: listingData.enabled!,
              borderRadius: 30.0.w,
              showOnOff: false,
              onToggle: (val) {
                if (onToggle != null) {
                  onToggle!(val);
                }
              },
            ),
          ),
          onTap: () {
            if (onPress != null) {
              onPress!();
            }
          },
        ),
      ),
    );
  }
}
