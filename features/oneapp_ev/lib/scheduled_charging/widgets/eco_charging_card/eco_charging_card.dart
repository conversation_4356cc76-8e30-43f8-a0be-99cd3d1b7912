// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/firebase_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/shimmer_widget.dart';
import 'package:vehicle_module/log/vehicle_analytic_event.dart';

// Project imports:
import '../../../charge_info/help_me_improve_page.dart';
import '../../../core/widgets/card_widget.dart';
import '../../../log/ev_analytics_events.dart';
import '../../scheduled_charging_bloc.dart';
import 'eco_charging_card_bloc.dart';

class EcoChargingCard extends StatefulWidget {
  final Function? onPress;
  final Function? onToggle;
  final bool showToggle;
  final ScheduledChargingBloc? schedulePageBloc;
  final EcoChargeTimeObject? ecoChargeTimeObject;

  EcoChargingCard({
    Key? key,
    this.onPress,
    this.onToggle,
    required this.showToggle,
    required this.schedulePageBloc,
    this.ecoChargeTimeObject,
  }) : super(key: key);

  @override
  State<EcoChargingCard> createState() => _EcoChargingCardState();
}

class _EcoChargingCardState extends State<EcoChargingCard> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  final EcoChargingCardBloc _bloc = EcoChargingCardBloc();

  @override
  void initState() {
    super.initState();
    _bloc.init(widget.schedulePageBloc, widget.ecoChargeTimeObject);
  }

  @override
  Widget build(BuildContext context) {
    bool _ecoChargingEnabled = false;

    return OACard(
      cardChild: Column(
        children: [
          ListTile(
            minVerticalPadding: 16.h,
            title: Text(OneAppString.of().ecoCharging,
                style: _textStyleUtil.subHeadline1),
            subtitle: StreamBuilder<EcoChargeTimeObject?>(
              stream: _bloc.ecoTimesObj,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return Text.rich(
                    TextSpan(
                      style: _textStyleUtil.footNote1,
                      text: OneAppString.of().offpeakDescription,
                      children: <TextSpan>[
                        TextSpan(
                          text: snapshot.data!.startTime,
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        TextSpan(text: ' '),
                        TextSpan(text: OneAppString.of().and),
                        TextSpan(text: ' '),
                        TextSpan(
                          text: snapshot.data!.endTime,
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  );
                } else {
                  return shimmerRectangle(15.h, 20.w, 5.r);
                }
              },
            ),
            leading: SvgPicture.asset(
              icEcoBadgeCircle,
              width: 48.w,
              height: 48.h,
            ),
            trailing: widget.showToggle
                ? SizedBox(
                    height: 34.0.h,
                    width: 60.0.w,
                    child: StreamBuilder<EcoChargeTimeObject?>(
                      stream: _bloc.ecoTimesObj,
                      builder: (context, snapshot) {
                        if (snapshot.hasData && snapshot.data != null) {
                          _ecoChargingEnabled = snapshot.data!.optedIn;
                          return getSwitch(_ecoChargingEnabled);
                        } else {
                          return getSwitch(false);
                        }
                      },
                    ),
                  )
                : SizedBox(
                    height: 0,
                    width: 0,
                  ),
            onTap: () {
              if (widget.onPress != null) {
                widget.onPress!();
              }
            },
          ),
          InkWell(
            onTap: () {
              FireBaseAnalyticsLogger.logMarketingGroupEvent(
                  VehicleAnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                  childEventName: EVAnalyticsEvent.LEARN_MORE);
              showMaterialModalBottomSheet(
                expand: true,
                context: context,
                clipBehavior: Clip.antiAliasWithSaveLayer,
                backgroundColor: _colorUtil.tertiary15,
                builder: (context) => HelpMeImprove(
                  showSwipeBar: false,
                  hideHealthImpact: false,
                ),
              );
            },
            child: Container(
              padding: EdgeInsets.only(left: 16.w, right: 22.w),
              color: _colorUtil.tile02,
              height: 60.h,
              child: Row(
                children: [
                  Text(
                    OneAppString.of().findOutMore,
                    style: _textStyleUtil.buttonLink1,
                  ),
                  Spacer(),
                  SvgPicture.asset(
                    chevronRightIcon,
                    colorFilter: ColorFilter.mode(
                      _colorUtil.tertiary00,
                      BlendMode.srcIn,
                    ),
                    width: 7.w,
                    height: 12.h,
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget getSwitch(bool enabled) {
    return FlutterSwitch(
      width: 60.0.w,
      height: 34.0.h,
      activeColor: Colors.white,
      inactiveColor: Colors.white,
      inactiveToggleColor: _colorUtil.tertiary05,
      activeToggleColor: _colorUtil.button03b,
      switchBorder: Border.all(color: _colorUtil.tertiary10),
      toggleSize: 20.0.w,
      value: enabled,
      borderRadius: 30.0.w,
      showOnOff: false,
      onToggle: (val) {
        if (widget.onToggle != null) {
          widget.onToggle!(val, _bloc.ecoChargeTimeObject);
        }
      },
    );
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }
}
