// Package imports:
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oa_network_impl/api_clients.dart';
import 'package:oa_network_impl/electric_vehicle/entity/mc/entity/eco_schedule_detail_entity/eco_schedule_detail_entity.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:rxdart/rxdart.dart';

// Project imports:
import '../../scheduled_charging_bloc.dart';

class EcoChargeTimeObject {
  final String descriptionText;
  final String startTime;
  final String endTime;
  final String startHour;
  final String startMin;
  final String endHour;
  final String endMin;
  final bool optedIn;

  EcoChargeTimeObject({
    required this.descriptionText,
    required this.startTime,
    required this.endTime,
    required this.startHour,
    required this.startMin,
    required this.endHour,
    required this.endMin,
    required this.optedIn,
  });
}

class EcoChargingCardBloc extends BlocBase {
  late Function(bool) progressHandlerCallback;
  ScheduledChargingBloc? _schedulePageBloc = null;
  EcoChargeTimeObject? ecoChargeTimeObject;

  Stream<EcoChargeTimeObject?> get ecoTimesObj => _ecoTimesObj.stream;
  final _ecoTimesObj = BehaviorSubject<EcoChargeTimeObject?>();

  void init(ScheduledChargingBloc? schedulePageBloc,
      EcoChargeTimeObject? ecoTimeObject) {
    _schedulePageBloc = schedulePageBloc;
    if (ecoTimeObject != null) {
      ecoChargeTimeObject = ecoTimeObject;
      _ecoTimesObj.sink.add(ecoTimeObject);
    } else {
      fetchEcoScheduleDetails();
    }
  }

  Future<void> fetchEcoScheduleDetails() async {
    final _evApi = APIClients.evApiClient;
    String? vin = Global.getInstance().vin;
    String? guid = Global.getInstance().guid;
    String homeLat = Global.getInstance().homeLatitude;
    String homeLong = Global.getInstance().homeLongitude;

    String appLat = '';
    String appLong = '';
    Position? _vehicleLocation = _schedulePageBloc?.vehicleLocation;
    Position? _currentLocation = Global.getInstance().currentLocation ?? null;

    if (_vehicleLocation != null) {
      appLat = _vehicleLocation.lat.toString();
      appLong = _vehicleLocation.lng.toString();
    } else if (_currentLocation != null) {
      appLat = _currentLocation.lat.toString();
      appLong = _currentLocation.lng.toString();
    }

    EcoScheduleDetailEntity? ecoScheduleResponse;

    //API Call
    final commonResponse = await _evApi.fetchEcoScheduleDetails(
      vin ?? "",
      guid ?? "",
      homeLat,
      homeLong,
      appLat,
      appLong,
    );
    ecoScheduleResponse = commonResponse.response;

    if (ecoScheduleResponse!.baInfo == null) {
      ecoScheduleResponse.baInfo = BaInfo(
          abbrev: 'ERCOT_NORTHCENTRAL',
          name: 'ERCOT North Central',
          echoPercentile: '40',
          echoTimes: EchoTimes(start: '23:00', end: '04:00'));
    }
    String startTime = convertScheduleTimeTo12Hour(
        ecoScheduleResponse.baInfo?.echoTimes?.start ?? "",
        omitMinutes: true);
    String endTime = convertScheduleTimeTo12Hour(
        ecoScheduleResponse.baInfo?.echoTimes?.end ?? "",
        omitMinutes: true);
    String ecoChargingDesc = OneAppString.of().offpeakDescription +
        startTime +
        ' ' +
        OneAppString.of().and +
        ' ' +
        endTime;
    var startArr = ecoScheduleResponse.baInfo?.echoTimes?.start!.split(':');
    var endArr = ecoScheduleResponse.baInfo?.echoTimes?.end?.split(':');

    bool optedIn = false;
    if (ecoScheduleResponse.ecoEnrollmentDetails != null) {
      optedIn = ecoScheduleResponse.ecoEnrollmentDetails?.enabled ?? true;
    }

    EcoChargeTimeObject ecoObject = EcoChargeTimeObject(
        descriptionText: ecoChargingDesc,
        startTime: startTime,
        endTime: endTime,
        startHour: startArr?.first ?? '',
        startMin: startArr?.last ?? '',
        endHour: endArr?.first ?? '',
        endMin: endArr?.last ?? '',
        optedIn: optedIn);
    ecoChargeTimeObject = ecoObject;
    _ecoTimesObj.sink.add(ecoChargeTimeObject);
  }

  @override
  void dispose() {
    _ecoTimesObj.close();
  }
}
