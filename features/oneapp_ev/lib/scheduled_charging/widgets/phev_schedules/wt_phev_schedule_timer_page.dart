// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/base_ui.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/common_util.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/util/semantic_label_util.dart';
import 'package:oneapp_common/widget/custom_divider.dart';
import 'package:oneapp_common/widget/toast/common_toast.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:vehicle_module/ui/vehicle_charge_management/vehicle_charge_management_timer/vehicle_charge_management_timer_schedule/vehicle_charge_management_timer_departure_page.dart';
import 'package:vehicle_module/ui/vehicle_charge_management/vehicle_charge_management_timer/vehicle_charge_management_timer_schedule/vehicle_charge_management_timer_start_page.dart';

// Project imports:
import '../eco_charging_card/eco_charging_card.dart';
import '../no_schedules_widget/no_schedules.dart';
import 'wt_phev_schedule_timer_bloc.dart';

class PhevScheduleTimerPage extends StatefulWidget {
  final Function(bool, ChargeManagementDetailEntity?)?
      reloadChargeManagementPage;

  PhevScheduleTimerPage({
    this.reloadChargeManagementPage,
  });

  @override
  _PhevScheduleTimerPageState createState() => _PhevScheduleTimerPageState();
}

class _PhevScheduleTimerPageState extends State<PhevScheduleTimerPage> {
  final ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil _textStyleUtil = ThemeConfig.current().textStyleUtil;
  PhevScheduleTimerBloc _bloc = PhevScheduleTimerBloc();
  int? toggleValue;

  @override
  void initState() {
    super.initState();
    _bloc.init(_progressHandlerCallback);
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      bloc: _bloc,
      child: OneAppScaffold(
        backgroundColor: _colorUtil.tile01,
        body: Container(
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          child: StreamBuilder<ChargeInfo?>(
              stream: _bloc.chargeInfoObject,
              builder: (context, snapshot) {
                bool showEmptyState = false;
                if (snapshot.hasData && snapshot.data != null) {
                  int? chargeType = snapshot.data!.chargeType;

                  if (chargeType == 0 || chargeType == 15) {
                    showEmptyState = true;
                  }

                  if (chargeType == 1) {
                    // Start Time
                    toggleValue = 1;
                  } else if (chargeType == 2 || chargeType == 3) {
                    // Departure Time
                    toggleValue = 0;
                  } else {
                    toggleValue = 2;
                  }
                }
                return showEmptyState
                    ? Container(
                        child: Column(children: [
                          ecoChargingWidget(),
                          Expanded(
                            child: NoScheduleWidget(
                              subtitleText:
                                  OneAppString.of().phevNoSchedulesSubtitle,
                            ),
                          ),
                          SizedBox(height: 10.h)
                        ]),
                      )
                    : SingleChildScrollView(
                        child: Container(
                          child: Column(
                            children: [
                              ecoChargingWidget(),
                              Container(height: 24.h),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    OneAppString.of().manualScheduleHeading,
                                    style: TextStyleExtension()
                                        .newStyleWithColor(_textStyleUtil.body4,
                                            _colorUtil.tertiary03),
                                  ),
                                ],
                              ),
                              Container(height: 24.h),
                              InkWell(
                                onTap: () {
                                  if (_bloc.chargeInfo?.chargeType != 0 &&
                                      _bloc.chargeInfo?.chargeType != 15) {
                                    Navigator.of(context).push(
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                VehicleChargeManagementTimerStartPage(
                                                    chargeInfo:
                                                        _bloc.chargeInfo,
                                                    // scrollController:
                                                    //     widget.scrollController,
                                                    reloadChargeManagementPage:
                                                        (reload, detail) {
                                                      _reloadChargeManagementPage(
                                                          reload, detail);
                                                    })));
                                  } else {
                                    _setTimerInVehicleToast();
                                  }
                                },
                                child: _timerLayout(
                                    OneAppString.of().startTime,
                                    OneAppString.of().wtStartTimeSubHeading,
                                    toggleValue == 1 ? true : false),
                              ),
                              Container(height: 24.h),
                              InkWell(
                                onTap: () {
                                  if (_bloc.chargeInfo!.chargeType != 0 &&
                                      _bloc.chargeInfo!.chargeType != 15) {
                                    Navigator.of(context).push(
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                VehicleChargeManagementTimerDeparturePage(
                                                    chargeInfo:
                                                        _bloc.chargeInfo,
                                                    reloadChargeManagementPage:
                                                        (reload, detail) {
                                                      _reloadChargeManagementPage(
                                                          reload, detail);
                                                    })));
                                  } else {
                                    _setTimerInVehicleToast();
                                  }
                                },
                                child: _timerLayout(
                                    OneAppString.of().departureTime,
                                    OneAppString.of().wtEndTimeSubHeading,
                                    toggleValue == 0 ? true : false),
                              ),
                              _refreshIconLayout(),
                            ],
                          ),
                        ),
                      );
              }),
        ),
      ),
    );
  }

  // Common layout for both start and departure time
  Widget _timerLayout(
      String headingText, String subHeadingText, bool switchStatus) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: _colorUtil.tile02,
          borderRadius: BorderRadius.all(Radius.circular(CARD_RADIUS_SMALL))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child: Text(
                headingText,
                style: TextStyleExtension().newStyleWithColor(
                    _textStyleUtil.subHeadline2, _colorUtil.tertiary03),
              )),
              Container(
                  margin: EdgeInsets.only(left: 8.w),
                  child: Container(
                    padding: EdgeInsets.only(
                        left: 22.w, right: 22.w, top: 8.h, bottom: 8.h),
                    decoration: BoxDecoration(
                        border: Border.all(
                          color: switchStatus
                              ? _colorUtil.secondary02
                              : _colorUtil.button05b,
                        ),
                        color: switchStatus
                            ? _colorUtil.secondary02
                            : _colorUtil.button05b,
                        borderRadius: BorderRadius.all(Radius.circular(22.r))),
                    child: Text(
                        switchStatus
                            ? OneAppString.of().onText
                            : OneAppString.of().offText,
                        style: TextStyleExtension().newStyleWithColor(
                            _textStyleUtil.callout1, _colorUtil.button02a)),
                  ))
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 24.h, bottom: 24.h),
            child: CustomDivider(
              lineColor: _colorUtil.tertiary10,
            ),
          ),
          Container(
            margin: EdgeInsets.only(bottom: 8.h),
            child: Text(
              subHeadingText,
              style: TextStyleExtension().newStyleWithColor(
                  _textStyleUtil.callout1, _colorUtil.tertiary05),
            ),
          ),
        ],
      ),
    );
  }

  Widget _toastCustomWidget() {
    return CommonToast(
      iconColor: _colorUtil.tertiary15,
      iconPath: closeIcon,
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: OneAppString.of().setTimerInVehicle,
    );
  }

  Widget _responseToastCustomWidget(String toastMessage, bool isSuccessful) {
    return CommonToast(
      iconColor: _colorUtil.tertiary15,
      iconPath: isSuccessful ? checkIcon : closeIcon,
      avatarContainerEndColor: _colorUtil.secondary01,
      avatarContainerStartColor: _colorUtil.secondary01,
      containerColor: _colorUtil.tertiary03,
      outerCircleRadius: 8.r,
      toastColor: _colorUtil.tertiary15,
      toastMessage: toastMessage,
    );
  }

  Widget ecoChargingWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 0.h),
      child: EcoChargingCard(
        schedulePageBloc: null,
        showToggle: false,
      ),
    );
  }

  void _savedResponseCallback() {
    showCustomToast(
        _responseToastCustomWidget(
            OneAppString.of().loginUnableProcessYourRequest, false),
        3);
  }

  void _reloadChargeManagementPage(
      bool reload, ChargeManagementDetailEntity? detail) {
    _progressHandlerCallback(false);
    widget.reloadChargeManagementPage!(reload, detail);
  }

  void _setTimerInVehicleToast() {
    showCustomToast(_toastCustomWidget(), 3);
  }

  Widget _refreshIconLayout() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        children: [
          InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              _bloc.refreshLastUpdatedData(
                  _savedResponseCallback, _reloadChargeManagementPage);
            },
            child: CircleAvatar(
              radius: 24.0.r,
              backgroundColor: _colorUtil.button02d,
              child: SvgPicture.asset(
                refreshIcon,
                colorFilter: ColorFilter.mode(
                  _colorUtil.button02a,
                  BlendMode.srcIn,
                ),
                semanticsLabel: REFRESH_BUTTON,
              ),
            ),
          ),
          StreamBuilder<String>(
              stream: _bloc.acquisitionDateTimeValue,
              builder: (context, snapshot) {
                return Container(
                  padding: EdgeInsets.only(bottom: 8.h),
                  margin: EdgeInsets.only(top: 8.h),
                  child: Text(
                    snapshot.hasData ? snapshot.data! : " - ",
                    style: TextStyleExtension().newStyleWithColor(
                        _textStyleUtil.caption1, _colorUtil.tertiary05),
                  ),
                );
              }),
        ],
      ),
    );
  }

  void _progressHandlerCallback(bool show) {
    if (show) {
      showProgress(context);
    } else {
      dismissProgress(context);
    }
  }
}
