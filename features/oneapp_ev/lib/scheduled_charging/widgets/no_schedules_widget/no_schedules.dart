// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/util/image_util.dart';

// Project imports:
import '../../../core/widgets/themed_widget.dart';

class NoScheduleWidget extends ThemedStatelessWidget {
  final String subtitleText;
  NoScheduleWidget({
    Key? key,
    required this.subtitleText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 8.h),
            child: SvgPicture.asset(
              icScheduleWithBG,
              width: 48.w,
              height: 48.h,
            ),
          ),
          Text(OneAppString.of().noSchedulesTitle,
              style: textStyleUtil.subHeadline1),
          SizedBox(height: 10.h),
          Text(
            subtitleText,
            textAlign: TextAlign.center,
            style: TextStyleExtension().newStyleWithColor(
              textStyleUtil.body1,
              colorUtil.tertiary05,
            ),
          ),
        ],
      ),
    );
  }
}
