// Flutter imports:
import 'package:flutter/foundation.dart';

// Package imports:
import 'package:oa_network_impl/one_app/entity/charge_management_detail_entity.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';

const String Sunday = 'Sunday';
const String Monday = 'Monday';
const String Tuesday = 'Tuesday';
const String Wednesday = 'Wednesday';
const String Thursday = 'Thursday';
const String Friday = 'Friday';
const String Saturday = "Saturday";

class ScheduleBoxedReturns {
  final String? requestNo;
  final String toastMsg;

  ScheduleBoxedReturns(this.requestNo, this.toastMsg);
}

String getScheduleDaysString(List<String> daysSelected) {
  if (daysSelected.contains(Sunday) &&
      daysSelected.contains(Monday) &&
      daysSelected.contains(Tuesday) &&
      daysSelected.contains(Wednesday) &&
      daysSelected.contains(Thursday) &&
      daysSelected.contains(Friday) &&
      daysSelected.contains(Saturday)) {
    return OneAppString.of().everyday;
  } else if (daysSelected.contains(Monday) &&
      daysSelected.contains(Tuesday) &&
      daysSelected.contains(Wednesday) &&
      daysSelected.contains(Thursday) &&
      daysSelected.contains(Friday)) {
    return OneAppString.of().weekdays;
  } else if (daysSelected.length == 2 &&
      (daysSelected.contains(Saturday) && daysSelected.contains(Sunday))) {
    return OneAppString.of().weekend;
  } else {
    List<String> weekDayList = [
      OneAppString.of().monday,
      OneAppString.of().tuesday,
      OneAppString.of().wednesday,
      OneAppString.of().thursday,
      OneAppString.of().friday,
      OneAppString.of().saturday,
      OneAppString.of().sunday
    ];
    String daysToDisplayStr = daysSelected
        .map((item) {
          int index = weekDayEnglishList.indexWhere(
              (element) => element.toUpperCase() == item.toUpperCase());
          return weekDayList[index].substring(0, 3);
        })
        .toList()
        .join(",");
    return daysToDisplayStr;
  }
}

int? wtGetOffPeakIndex(List<TimerChargeInfo> listingDataArray, String startHour,
    String startMin, String endHour, String endMin) {
  debugPrint('wtGetOffPeakIndex');
  for (var i = 0; i < listingDataArray.length; i++) {
    if (wtIsOffPeakSchedule(
        listingDataArray[i], startHour, startMin, endHour, endMin)) {
      return i;
    }
  }
  return null;
}

bool wtIsOffPeakSchedule(TimerChargeInfo scheduleData, String startH,
    String startM, String endH, String endM) {
  int? startHour, startMin, endHour, endMin = 0;
  startHour = int.parse(scheduleData.startTime!.split(':').first);
  startMin = int.parse(scheduleData.startTime!.split(':').last);
  if (scheduleData.endTime != null) {
    endHour = int.parse(scheduleData.endTime!.split(':').first);
    endMin = int.parse(scheduleData.endTime!.split(':').last);
  }

  bool hasOffPeakDays = false;
  if (scheduleData.daysOfTheWeek != null) {
    if (scheduleData.daysOfTheWeek!.contains(Sunday) &&
        scheduleData.daysOfTheWeek!.contains(Monday) &&
        scheduleData.daysOfTheWeek!.contains(Tuesday) &&
        scheduleData.daysOfTheWeek!.contains(Wednesday) &&
        scheduleData.daysOfTheWeek!.contains(Thursday) &&
        scheduleData.daysOfTheWeek!.contains(Friday) &&
        scheduleData.daysOfTheWeek!.contains(Saturday)) hasOffPeakDays = true;
  }

  if (startHour == int.parse(startH) &&
      startMin == int.parse(startM) &&
      endHour == int.parse(endH) &&
      endMin == int.parse(endM) &&
      hasOffPeakDays) {
    return true;
  } else {
    return false;
  }
}
