// Flutter imports:

// Package imports:
import 'package:get_it/get_it.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:oneapp_common/bloc/bloc_provider.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/util/map_util.dart';
import 'package:oneapp_network_implementation/api_client_config.dart';
import 'package:oneapp_network_implementation/oneapp/api_client/oneapp_client.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_info_util.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vehicle_module/local_repo/vehicle_repo.dart';

import 'package:oneapp_network_implementation/oneapp/entity/vehicle/vehicle_info_entity.dart'
    as vehicleEntity;
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/odometer_detail_entity.dart'
    as odometerDetail;

class ScheduledChargingBloc extends BlocBase {
  vehicleEntity.Payload? vehicleInfoEntity;
  Position? currentLocation;
  Position? vehicleLocation;
  odometerDetail.OdometerDetailPayload? odometerDetailsPayload;
  GetIt locator = GetIt.instance;
  OneAppClient api = APIClientConfig.oneAppClient;

  Stream<bool> get is21MMGeneration => _is21MMGeneration.stream;
  final _is21MMGeneration = BehaviorSubject<bool>();

  Stream<bool> get isEvPhpVehicle => _isEvPhpVehicle.stream;
  final _isEvPhpVehicle = BehaviorSubject<bool>();

  Stream<bool?> get isMultidayChargingAllowed =>
      _isMultidayChargingAllowed.stream;
  final _isMultidayChargingAllowed = BehaviorSubject<bool?>();

  Stream<bool> get isPublicChargingControlAllowed =>
      _isPublicChargingControlAllowed.stream;
  final _isPublicChargingControlAllowed = BehaviorSubject<bool>();

  Stream<ScheduleType> get scheduleType => _scheduleType.stream;
  final _scheduleType = BehaviorSubject<ScheduleType>();

  Stream<bool> get showSchedule => _showSchedule.stream;
  final _showSchedule = BehaviorSubject<bool>();

  Stream<bool> get isCV17Vehicle => _isCV17Vehicle.stream;
  final _isCV17Vehicle = BehaviorSubject<bool>();

  Future<void> init() async {
    String? vin = Global.getInstance().vin;
    vehicleInfoEntity = await VehicleRepo().getLocalPayloadFromVin(vin);
    fetchTelemetryData();
    _loadVehicleDetails(vehicleInfoEntity);
  }

  void _loadVehicleDetails(vehicleEntity.Payload? vehicleInfoEntity) {
    bool isCY17 = isCY17Vehicle(vehicleInfoEntity!.generation);
    bool isCY17PLUS = isCY17PlusVehicle(vehicleInfoEntity.generation);
    bool _is21MMVehicle = is21MMVehicle(vehicleInfoEntity.generation);
    _is21MMGeneration.sink.add(_is21MMVehicle);
    bool _isEvPhpModel = isEvPhpModel(vehicleInfoEntity);
    _isEvPhpVehicle.sink.add(_isEvPhpModel);
    _isCV17Vehicle.sink.add(isCY17);

    bool isMultidayChargingEnabled =
        isMultidayChargingFeatureEnabled(vehicleInfoEntity.features!);

    _isMultidayChargingAllowed.sink.add(isMultidayChargingEnabled);

    bool _isEvPublicChargingControlEnabled =
        isEvPublicChargingControlEnabled(vehicleInfoEntity.features!);
    _isPublicChargingControlAllowed.sink.add(_isEvPublicChargingControlEnabled);

    if ((isCY17PLUS && _isEvPhpModel) ||
        (_is21MMVehicle && _isEvPhpModel) ||
        isMultidayChargingEnabled) {
      _showSchedule.sink.add(true);
    } else {
      _showSchedule.sink.add(false);
    }

    isMultidayChargingEnabled
        ? _scheduleType.sink.add(ScheduleType.MULTIDAY)
        : _scheduleType.sink.add(ScheduleType.NONMULTIDAY);
  }

  Future<void> fetchTelemetryData() async {
    try {
      odometerDetailsPayload = await VehicleRepo().fetchTelemetry();
    } catch (e) {}

    if (odometerDetailsPayload != null) {
      if (odometerDetailsPayload!.vehicleLocation != null &&
          odometerDetailsPayload!.vehicleLocation!.longitude != null) {
        double vehicleLatitude =
            odometerDetailsPayload?.vehicleLocation?.latitude ?? 0.00;
        double vehicleLongitude =
            odometerDetailsPayload!.vehicleLocation!.longitude!;
        vehicleLocation = Position(vehicleLongitude, vehicleLatitude);
      }
    } else {
      fetchCurrentLocation();
    }
  }

  Future<void> fetchCurrentLocation() async {
    if (Global.getInstance().currentLocation != null) {
      currentLocation = Global.getInstance().currentLocation;
    }
    bool isGranted = await MapUtil.checkLocationGranted();
    if (isGranted) {
      currentLocation = await MapUtil.getCurrentLocation();
      Global.getInstance().currentLocation = currentLocation;
    }
  }

  void refreshUI(ChargeManagementDetailEntity? detail) {
    if (detail != null) {
      String? vin = Global.getInstance().vin;
      final repoFuture = VehicleRepo().getEVVehicleInfoRepository(vin);
      repoFuture.then(
        (repository) => repository.receivedExtraChargeManagementDetail(detail),
      );
    }
  }

  @override
  void dispose() {
    _is21MMGeneration.close();
    _isEvPhpVehicle.close();
    _isMultidayChargingAllowed.close();
    _isPublicChargingControlAllowed.close();
    _scheduleType.close();
    _showSchedule.close();
    _isCV17Vehicle.close();
  }
}
