// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/oneapp_scaffold.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/string_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:oneapp_common/util/image_util.dart';
import 'package:oneapp_common/widget/common_widget.dart';
import 'package:oneapp_network_implementation/oneapp/entity/vehicle/charge_management_detail_entity.dart';
import 'package:oneapp_network_implementation/oneapp/vehicle_util/vehicle_enum_const_util.dart';

// Project imports:
import 'scheduled_charging_bloc.dart';
import 'widgets/eco_charging_card/eco_charging_card.dart';
import 'widgets/no_schedules_widget/no_schedules.dart';
import 'widgets/phev_schedules/wt_phev_schedule_timer_page.dart';
import 'widgets/schedule_listing_view/ui/wt_schedule_listing_page.dart';

class ScheduledChargingPage extends StatefulWidget {
  ScheduledChargingPage({
    Key? key,
  }) : super(key: key);

  @override
  State<ScheduledChargingPage> createState() => _ScheduledChargingPageState();
}

class _ScheduledChargingPageState extends State<ScheduledChargingPage> {
  final ColorUtil colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil textStyleUtil = ThemeConfig.current().textStyleUtil;
  bool ecoChargingEnabled = false;
  ScheduledChargingBloc _bloc = ScheduledChargingBloc();

  @override
  void initState() {
    super.initState();
    _bloc.init();
  }

  @override
  Widget build(BuildContext context) {
    return Navigator(
        onGenerateRoute: (_) => MaterialPageRoute(builder: (materialContext) {
              return Builder(builder: (builderContext) {
                return OneAppScaffold(
                  backgroundColor: colorUtil.tertiary15,
                  body: Container(
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          bottomSheetCustomAppBar(OneAppString.of().schedule,
                              onBackPressed: () => Navigator.of(context).pop(),
                              elevation: 0),
                          Expanded(
                            child: StreamBuilder(
                              initialData: false,
                              stream: _bloc.showSchedule,
                              builder: (context, snapshot) {
                                if (snapshot.data != null &&
                                    snapshot.data == true) {
                                  return StreamBuilder<ScheduleType>(
                                    stream: _bloc.scheduleType,
                                    builder: (context, snapshot) {
                                      if (snapshot.hasData) {
                                        return snapshot.data ==
                                                ScheduleType.MULTIDAY
                                            ? ScheduleListingScreen(
                                                schedulePageBloc: _bloc)
                                            : PhevScheduleTimerPage(
                                                reloadChargeManagementPage:
                                                    (reload, detail) {
                                                  _reloadChargeManagementPage(
                                                      reload, detail);
                                                },
                                              );
                                      } else {
                                        return Container();
                                      }
                                    },
                                  );
                                } else if (snapshot.data != null &&
                                    snapshot.data == false &&
                                    snapshot.connectionState ==
                                        ConnectionState.active) {
                                  return Container(
                                    child: Column(children: [
                                      ecoChargingWidget(),
                                      Expanded(
                                        child: NoScheduleWidget(
                                          subtitleText: OneAppString.of()
                                              .phevNoSchedulesSubtitle,
                                        ),
                                      ),
                                      SizedBox(height: 10.h)
                                    ]),
                                  );
                                } else {
                                  return Container();
                                }
                              },
                            ),
                          ),
                        ]),
                  ),
                );
              });
            }));
  }

  Widget schedulesInfoImg() {
    Color gradientStart = Colors.black;
    Color gradientEnd = Colors.transparent;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: ShaderMask(
              shaderCallback: (rect) {
                return LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [gradientStart, gradientEnd],
                ).createShader(
                    Rect.fromLTRB(0, 0, rect.width - 40, rect.height - 49));
              },
              blendMode: BlendMode.darken,
              child: Container(
                height: 296.h,
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: ExactAssetImage(mcBGImg), fit: BoxFit.cover),
                ),
              ),
            ),
          ),
          Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 30.h, left: 16.w, right: 16.w),
                child: Text(
                  OneAppString.of().noScheduleFeatureTitleText,
                  style: TextStyleExtension()
                      .newStyleWithColor(textStyleUtil.callout2, Colors.white),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 24.h, left: 24.w, right: 24.w),
                child: Text(
                  OneAppString.of().noScheduleFeatureSubtitleText,
                  style: TextStyleExtension().newStyleWithColor(
                      textStyleUtil.subHeadline3, Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget ecoChargingWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 0.h),
      child: EcoChargingCard(
        schedulePageBloc: null,
        showToggle: false,
      ),
    );
  }

  void _reloadChargeManagementPage(
      bool reload, ChargeManagementDetailEntity? detail) {
    _bloc.refreshUI(detail ?? null);
  }

  @override
  void dispose() {
    _bloc.dispose();
    super.dispose();
  }
}
