// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/util/app_enum_const_util.dart';

// Project imports:
import '../../widgets/card_widget.dart';
import 'common/shimmerRectangle.dart';

class ShimmeringBatteryCard extends StatelessWidget {
  const ShimmeringBatteryCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OACard(
      cardChild:
          Column(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ShimmerRectangle(
                height: 12, width: 150, borderRadius: CARD_RADIUS_SMALL),
            ShimmerRectangle(
                height: 12, width: 150, borderRadius: CARD_RADIUS_SMALL),
          ],
        ),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 20,
        ),
        Shimmer<PERSON>ectangle(
            height: 12,
            width: MediaQuery.of(context).size.width,
            borderRadius: CARD_RADIUS_SMALL),
      ]),
    );
  }
}
