// Dart imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';

// Project imports:
import 'common/shimmerCircle.dart';
import 'common/shimmerRectangle.dart';

// Project imports:

class EVShimmeringTile extends StatelessWidget {
  EVShimmeringTile({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: _shimmerWidget(),
    );
  }

  Widget _shimmerWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        ShimmerCircle(height: 24, width: 24, borderRadius: 300),
        Si<PERSON><PERSON><PERSON>(width: 17.w),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerRectangle(
                height: 12, width: 104, borderRadius: CARD_RADIUS_SMALL),
            <PERSON><PERSON><PERSON><PERSON>(height: 14.h),
            ShimmerRectangle(
                height: 12, width: 260, borderRadius: CARD_RADIUS_SMALL),
          ],
        ),
      ],
    );
  }
}
