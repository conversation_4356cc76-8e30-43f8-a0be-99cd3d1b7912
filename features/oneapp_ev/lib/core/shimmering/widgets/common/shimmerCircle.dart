// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerCircle extends StatelessWidget {
  const ShimmerCircle({
    Key? key,
    required this.height,
    required this.width,
    required this.borderRadius,
  }) : super(key: key);

  final double height;
  final double width;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    return _shimmerCircle();
  }

  Widget _shimmerCircle() {
    return Container(
      width: width.w,
      height: height.h,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(300.r),
        boxShadow: [
          BoxShadow(
            color: Color(0x0c000000),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
        color: ThemeConfig.current().colorUtil.tertiary10,
      ),
      child: Shimmer.fromColors(
        baseColor: ThemeConfig.current().colorUtil.tertiary10,
        highlightColor: ThemeConfig.current().colorUtil.tertiary12,
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: ThemeConfig.current().colorUtil.tertiary10,
        ),
      ),
    );
  }
}
