// Flutter imports:
import 'package:flutter/material.dart';

// Project imports:
import '../../../widgets/card_widget.dart';
import '../../../widgets/themed_widget.dart';

// Package imports:

/// Create a card widget it will accept child as widget and optional background color. It will take card of border rounding and shadows and background of the widget
class ExpandedTileShimmer extends ThemedStatelessWidget {
  final Widget leftIcon;
  final Widget rightIcon;
  final Widget? cardChild;
  final Widget title;
  final bool loadingShimmer;

  ExpandedTileShimmer({
    Key? key,
    required this.leftIcon,
    required this.rightIcon,
    required this.cardChild,
    required this.title,
    this.loadingShimmer = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OACard(cardChild: _expandedTileShimmerWidget());
  }

  Widget _expandedTileShimmerWidget() {
    var _cardChild = cardChild;
    if (_cardChild == null) {
      _cardChild = Container();
    }
    return Column(
      children: [
        ListTile(
          title: title,
          leading: leftIcon,
          trailing: rightIcon,
        ),
        _cardChild,
      ],
    );
  }
}
