// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/util/app_enum_const_util.dart';

// Project imports:
import 'common/shimmerRectangle.dart';

class ShimmeringRangeWidget extends StatelessWidget {
  const ShimmeringRangeWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ShimmerRectangle(
            height: 12, width: 80, borderRadius: CARD_RADIUS_SMALL),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 10,
        ),
        ShimmerRectangle(
            height: 12, width: 150, borderRadius: CARD_RADIUS_SMALL),
      ],
    );
  }
}
