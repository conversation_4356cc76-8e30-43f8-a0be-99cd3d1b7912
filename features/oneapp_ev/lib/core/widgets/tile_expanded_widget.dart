// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../shimmering/widgets/shimmering_tile_widget.dart';
import 'card_widget.dart';
import 'themed_widget.dart';

// Package imports:

/// Create a card widget it will accept child as widget and optional background color. It will take card of border rounding and shadows and background of the widget
class EVExpandedTile extends ThemedStatelessWidget {
  final Widget leftIcon;
  final Widget rightIcon;
  final Widget? cardChild;
  final String title;
  final bool loadingShimmer;
  final Function? onPress;

  EVExpandedTile({
    Key? key,
    required this.leftIcon,
    required this.rightIcon,
    required this.cardChild,
    required this.title,
    this.loadingShimmer = false,
    this.onPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OACard(
      cardChild: loadingShimmer ? EVShimmeringTile() : expandedTileWidget(),
      onPress: onPress,
    );
  }

  Widget expandedTileWidget() {
    final isLexus = Global.getInstance().appBrand == 'L';
    var _cardChild = cardChild;
    if (_cardChild == null) {
      _cardChild = Container();
    }
    return Column(
      children: [
        ListTile(
          title: Text(title,
              style: isLexus
                  ? TextStyleExtension()
                      .newStyleWithColor(
                          textStyleUtil.subHeadline1, colorUtil.tertiary03)
                      .merge(TextStyle(fontWeight: FontWeight.w600))
                  : TextStyleExtension().newStyleWithColor(
                      textStyleUtil.subHeadline1, colorUtil.tertiary03)),
          leading: leftIcon,
          trailing: rightIcon,
        ),
        _cardChild,
      ],
    );
  }
}
