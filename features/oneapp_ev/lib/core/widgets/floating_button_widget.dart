// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/util/app_enum_const_util.dart';
import 'package:oneapp_common/util/format_util.dart';

// Project imports:
import 'themed_widget.dart';

class FloatingButtonWidget extends ThemedStatelessWidget {
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final Color disabledBackgroundColor;
  final Color disabledButtonTextColor;
  final String text;
  final IconData? icon;
  final bool isEnabled;
  final double horizontalPadding;
  final double verticalPadding;
  final Function onPressed;
  final Widget? child;
  final TextStyle? textStyle;

  FloatingButtonWidget({
    Key? key,
    this.child,
    this.textColor = Colors.white,
    this.backgroundColor = Colors.black,
    this.borderColor = Colors.grey,
    this.disabledBackgroundColor = Colors.grey,
    this.disabledButtonTextColor = Colors.white,
    this.text = "Floating Button",
    this.icon,
    this.isEnabled = true,
    required this.onPressed,
    this.horizontalPadding = 16,
    this.verticalPadding = 16,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: () {
        if (this.isEnabled == true) {
          this.onPressed();
        }
        return;
      },
      style: TextButton.styleFrom(
          backgroundColor: this.isEnabled == true
              ? this.backgroundColor
              : this.disabledBackgroundColor,
          shape: StadiumBorder(),
          minimumSize: Size(MIN_BUTTON_WIDTH, MIN_BUTTON_HEIGHT),
          padding: EdgeInsets.symmetric(
              horizontal: this.horizontalPadding,
              vertical: this.verticalPadding)),
      child: Text(formatTextForLexusAndToyota(this.text),
          style: TextStyleExtension().newStyleWithColor(
            this.textStyle ?? textStyleUtil.buttonLink1,
            this.isEnabled == true
                ? this.textColor
                : this.disabledButtonTextColor,
          )),
    );
  }
}
