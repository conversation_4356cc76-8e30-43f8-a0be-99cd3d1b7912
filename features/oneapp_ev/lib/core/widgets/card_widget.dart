// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Project imports:
import 'themed_widget.dart';

/// Create a card widget it will accept child as widget and optional background color. It will take card of border rounding and shadows and background of the widget

class CardStyle {
  final Color? shadowColor;
  final Color? backgroundColor;
  late double borderRadius;
  late double elevation;
  CardStyle({
    this.shadowColor,
    this.backgroundColor,
    double? borderRadius,
    double? elevation,
  }) {
    this.borderRadius = borderRadius ?? 8.r;
    this.elevation = elevation ?? 30.h;
  }
}

class OACard extends ThemedStatelessWidget {
  final Widget? cardChild;
  final Function? onPress;
  final CardStyle? cardStyle;
  OACard({
    Key? key,
    required this.cardChild,
    this.cardStyle,
    this.onPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var _style = cardStyle;
    if (_style == null) {
      _style = CardStyle(
        backgroundColor: colorUtil.tile03,
        shadowColor: Color.fromRGBO(0, 0, 0, 0.7),
        borderRadius: 8.r,
        elevation: 5.h,
      );
    }
    return GestureDetector(
      onTap: () {
        if (onPress != null) {
          onPress!();
        }
      },
      child: Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_style.borderRadius),
          ),
          elevation: _style.elevation,
          shadowColor: _style.shadowColor,
          color: _style.backgroundColor,
          child: cardChild),
    );
  }
}
