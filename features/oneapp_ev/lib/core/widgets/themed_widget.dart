// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/text_style_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';

class ThemedStatelessWidget extends StatelessWidget {
  final ColorUtil colorUtil = ThemeConfig.current().colorUtil;
  final TextStyleUtil textStyleUtil = ThemeConfig.current().textStyleUtil;

  ThemedStatelessWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
