// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/global.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import '../shimmering/widgets/shimmering_tile_widget.dart';
import 'card_widget.dart';
import 'themed_widget.dart';

/// Create a card widget it will accept child as widget and optional background color. It will take card of border rounding and shadows and background of the widget
class EVSimpleTile extends ThemedStatelessWidget {
  final Widget leftIcon;
  final Widget rightIcon;
  final String title;
  final String description;
  final bool loadingShimmer;
  final Function? onPress;

  EVSimpleTile({
    Key? key,
    required this.leftIcon,
    required this.rightIcon,
    required this.title,
    this.description = "",
    this.loadingShimmer = false,
    this.onPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return loadingShimmer
        ? EVShimmeringTile()
        : OACard(cardChild: simpleTileWidget(), onPress: onPress);
  }

  Widget simpleTileWidget() {
    final isLexus = Global.getInstance().appBrand == 'L';
    return ListTile(
      contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      minLeadingWidth: 16.w,
      title: Text(title,
          style: isLexus
              ? TextStyleExtension()
                  .newStyleWithColor(
                      textStyleUtil.subHeadline1, colorUtil.tertiary03)
                  .merge(TextStyle(fontWeight: FontWeight.w600))
              : TextStyleExtension().newStyleWithColor(
                  textStyleUtil.subHeadline1, colorUtil.tertiary03)),
      subtitle: description.isNotEmpty
          ? Padding(
              padding: EdgeInsets.only(top: 8.h),
              child: Text(
                description,
                style: textStyleUtil.footNote1,
              ),
            )
          : null,
      leading: Padding(
        padding: EdgeInsets.only(left: 8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [leftIcon],
        ),
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [rightIcon],
      ),
    );
  }
}
