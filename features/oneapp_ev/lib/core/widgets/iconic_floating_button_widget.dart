// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/import_oneapp_commonui_package.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import 'floating_button_widget.dart';

class IconicFloatingButtonWidget extends FloatingButtonWidget {
  final VoidCallback onPressed;
  final String rightIcon;
  final String text;
  final Color? bgColor;
  final Color? foreColor;

  IconicFloatingButtonWidget({
    Key? key,
    required this.onPressed,
    required this.rightIcon,
    required this.text,
    this.bgColor,
    this.foreColor,
    child,
    textColor = Colors.white,
    backgroundColor = Colors.black,
    borderColor = Colors.grey,
    disabledBackgroundColor = Colors.grey,
    disabledButtonTextColor = Colors.white,
    icon,
    isEnabled = true,
    horizontalPadding = 16,
    verticalPadding = 16,
    textStyle,
  }) : super(
          key: key,
          onPressed: onPressed,
        );

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      label: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(right: 16.0.w),
            child: Text(
              text,
              style: TextStyleExtension().newStyleWithColor(
                  textStyleUtil.callout2,
                  foreColor ?? colorUtil.primaryButton01),
            ),
          ),
          SvgPicture.asset(
            rightIcon,
            colorFilter: ColorFilter.mode(
              foreColor ?? colorUtil.primaryButton01,
              BlendMode.srcIn,
            ),
          ),
        ],
      ),
      backgroundColor: bgColor ?? colorUtil.primaryButton02,
      icon: Container(),
      onPressed: onPressed,
    );
  }
}
