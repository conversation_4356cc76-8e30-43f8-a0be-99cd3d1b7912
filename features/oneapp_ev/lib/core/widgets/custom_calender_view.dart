// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oneapp_common/theme/text_style_util.dart';

// Project imports:
import 'themed_widget.dart';

class CustomCalenderInfo {
  String month;
  String day;
  CustomCalenderInfo({required this.month, required this.day});
}

class CustomCalenderView extends ThemedStatelessWidget {
  CustomCalenderView({
    Key? key,
    required this.info,
  }) : super(key: key);
  final CustomCalenderInfo info;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 44.h, // as per figma
          width: 42.w, // as per figma
          decoration: BoxDecoration(
            color: colorUtil.secondary01,
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(color: colorUtil.secondary01, width: 1.w),
          ),
          child: Column(
            children: [
              SizedBox(
                  height: 16.h, // as per figma
                  child: Text(
                    info.month,
                    style: TextStyleExtension().newStyleWithColor(
                        textStyleUtil.tabLabel01, colorUtil.tertiary15),
                  )),
              Expanded(
                  child: Container(
                decoration: BoxDecoration(
                    color: colorUtil.tertiary15,
                    border: Border.all(color: colorUtil.secondary01, width: 1),
                    borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(10.r),
                        bottomLeft: Radius.circular(10.r))),
                child: Center(
                  child: Text(
                    info.day,
                    style: TextStyleExtension().newStyleWithColor(
                        textStyleUtil.body3,
                        colorUtil.tertiary03), // as per figma
                    textAlign: TextAlign.center,
                  ),
                ),
              ))
            ],
          ),
        )
      ],
    );
  }
}
