// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:oa_network_impl/electric_vehicle/entity/ev_wallet_and_enrollment_validation.dart';
import 'package:oneapp_common/theme/color_util.dart';
import 'package:oneapp_common/theme/theme_config.dart';
import 'package:vehicle_module/ui/electric_vehicle_management/ev_common/evgo_complimentary_alert_util.dart';

class EvUtil {
  static ColorUtil _colorUtil = ThemeConfig.current().colorUtil;
  static Color getColor(int chargingPercentage, bool isCharging) {
    if (chargingPercentage <= 30) {
      return _colorUtil.primary01;
    }
    return _colorUtil.button03d;
  }

  static bool isPartnerRegistered({PartnerStatus? partner}) {
    return partner?.status.toUpperCase() == EVGOComplimentaryUtil.evgoFound
        ? true
        : false;
  }

  static bool isWalletSetupDone({String? wallet}) {
    if (wallet?.toUpperCase() == EVGOComplimentaryUtil.evgoFound) {
      return true;
    } else {
      return false;
    }
  }
}

extension ColorExtension on String {
  Color toColor() {
    var hexString = this;
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}
