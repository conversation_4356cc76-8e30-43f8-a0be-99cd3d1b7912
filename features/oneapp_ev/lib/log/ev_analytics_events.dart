class EVAnalyticsEvent {
  static const CHARGE_INFO_SCREEN = "vehicle_ev_charge_info_screen";
  static const CHARGE_HISTORY = "vehicle_ev_charge_history";
  static const SCHEDULE_TILE = "vehicle_ev_charge_schedule";
  static const STATISTICS_TILE = "vehicle_ev_charge_statistics";
  static const CLEAN_ASSIST_ENROLL = "vehicle_ev_clean_assist";
  static const EVGO_REGISTER = "vehicle_ev_evgo_register";
  static const FIND_STATIONS_FROM_CHARGE_INFO =
      "vehicle_ev_pub_find_from_chargeinfo_link";
  static const CHARGE_HISTORY_DETAILS = "vehicle_ev_charge_details_view";
  static const CHARGE_HISTORY_DATE_FILTER = "vehicle_ev_history_date_selection";
  static const LEARN_MORE = "vehicle_ev_eco_charge_find_out_more";
  static const CHARGE_STATS_DATE_FILTER =
      "vehicle_ev_statistics_date_selection";
  static const SCHEDULE_START_TIME = "vehicle_ev_schedule_start_time";
  static const SCHEDULE_DEP_TIME = "vehicle_ev_schedule_departure_time";
  static const CLEAN_ASSIST_DECLINE = "vehicle_ev_clean_assist_decline";
  static const CLEAN_ASSIST_VIEW = "vehicle_ev_clean_assist_view";
  static const FILTER_PARTNER_TYPE = "vehicle_ev_find_station_filtering";
  static const FAVORITE_STATION = "vehicle_ev_find_station_favorite";
  static const FAVORITE_STATION_SUCCESS =
      "vehicle_ev_find_station_tag_favorite";
}
