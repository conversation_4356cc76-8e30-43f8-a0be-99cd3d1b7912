package com.toyota.oneapp.crashlytics

import com.google.firebase.crashlytics.FirebaseCrashlytics
import javax.inject.Inject

class FirebaseCrashlyticsLogger
    @Inject
    constructor(
        private val firebaseCrashlytics: FirebaseCrashlytics,
    ) : CrashlyticsLogger {
        override fun logMessage(message: String) {
            firebaseCrashlytics.log(message)
        }

        override fun logUser(guid: String) {
            firebaseCrashlytics.setUserId(guid)
        }
    }
