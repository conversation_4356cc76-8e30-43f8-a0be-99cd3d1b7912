package com.toyota.oneapp.crashlytics

import android.content.Context
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module
internal abstract class FirebaseCrashlyticsModule {
    @Binds
    abstract fun bindLogger(logger: FirebaseCrashlyticsLogger): CrashlyticsLogger

    companion object {
        @Provides
        fun provideFirebaseCrashlytics(context: Context): FirebaseCrashlytics = FirebaseCrashlytics.getInstance()
    }
}
