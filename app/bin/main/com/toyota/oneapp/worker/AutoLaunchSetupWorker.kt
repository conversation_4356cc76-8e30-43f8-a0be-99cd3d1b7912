package com.toyota.oneapp.worker

import android.app.ForegroundServiceStartNotAllowedException
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import androidx.work.*
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ToyotaApplication
import com.toyota.oneapp.ui.splash.SplashActivity
import com.toyota.oneapp.util.ToyotaConstants
import toyotaone.commonlib.log.LogTool

private const val LAUNCH_NOTIFICATION_ID = 102

class AutoLaunchSetupWorker(
    context: Context,
    workerParams: WorkerParameters,
) : CoroutineWorker(context, workerParams) {
    companion object {
        @JvmStatic
        fun newInstance(): WorkRequest {
            val builder = OneTimeWorkRequestBuilder<AutoLaunchSetupWorker>()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                builder.setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            }

            return builder.build()
        }
    }

    override suspend fun getForegroundInfo(): ForegroundInfo {
        val builder: NotificationCompat.Builder =
            NotificationCompat.Builder(
                applicationContext,
                "${applicationContext.packageName}.${ToyotaConstants.BLE_NOTIFICATION_CHANNEL_ID_SUFFIX}",
            )
        builder.setSmallIcon(R.drawable.ic_notification)
        builder.setContentTitle(
            applicationContext.getString(R.string.bluetooth_notification_beacon_scanning_title),
        )

        val intent =
            Intent(
                applicationContext,
                SplashActivity::class.java,
            )
        val pendingIntent =
            PendingIntent.getActivity(
                applicationContext,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
            )
        builder.setContentIntent(pendingIntent)

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            ForegroundInfo(
                LAUNCH_NOTIFICATION_ID,
                builder.build(),
                ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE,
            )
        } else {
            ForegroundInfo(LAUNCH_NOTIFICATION_ID, builder.build())
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override suspend fun doWork(): Result =
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                setForeground(getForegroundInfo())
            }

            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Setting up for auto-launch")
            (applicationContext as ToyotaApplication).initializeBeaconMonitoring()

            Result.success()
        } catch (e: ForegroundServiceStartNotAllowedException) {
            LogTool.e(
                ToyotaConstants.BLUETOOTH_TAG,
                "Attempted to start UP service from background",
                e,
            )
            Result.failure()
        }
}
