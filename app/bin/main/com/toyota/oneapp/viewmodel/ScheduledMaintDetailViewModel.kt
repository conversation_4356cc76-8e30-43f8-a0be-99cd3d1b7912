package com.toyota.oneapp.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.toyota.oneapp.model.Advisor
import com.toyota.oneapp.model.ServiceRepair
import com.toyota.oneapp.model.ServiceTime
import com.toyota.oneapp.model.Transport
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ScheduledMaintDetailViewModel
    @Inject
    internal constructor() : ViewModel() {
        data class Data(val serviceId: String?, val advisor: Advisor?, val transport: Transport?)

        private val canBookAppointment = MutableLiveData<Boolean>()
        private val data = MutableLiveData<Data>()
        private var servicesRepairsList: List<ServiceRepair>? = null
        private var advisor: Advisor? = null
        private var transport: Transport? = null
        private var serviceTime: ServiceTime? = null

        fun setServicesRepairsList(servicesRepairsList: List<ServiceRepair>) {
            this.servicesRepairsList = servicesRepairsList
            canBookAppointment.value = enableBook()
            if (!servicesRepairsList.isNullOrEmpty()) {
                data.postValue(
                    Data(
                        servicesRepairsList.joinToString { it.serviceId }.replace(" ", ""),
                        advisor,
                        transport,
                    ),
                )
            }
        }

        fun setServiceTime(serviceTime: ServiceTime?) {
            this.serviceTime = serviceTime
            canBookAppointment.value = enableBook()
        }

        fun setAdvisor(advisor: Advisor?) {
            this.advisor = advisor
            if (!servicesRepairsList.isNullOrEmpty()) {
                data.postValue(
                    Data(servicesRepairsList?.joinToString { it.serviceId }, advisor, transport),
                )
            }
        }

        fun setTransport(transport: Transport?) {
            this.transport = transport
            if (!servicesRepairsList.isNullOrEmpty()) {
                data.postValue(
                    Data(servicesRepairsList?.joinToString { it.serviceId }, advisor, transport),
                )
            }
        }

        fun canBookAppointment(): MutableLiveData<Boolean> = canBookAppointment

        fun getData(): MutableLiveData<Data> = data

        private fun enableBook(): Boolean {
            return servicesRepairsList != null && servicesRepairsList!!.isNotEmpty() && serviceTime != null && !serviceTime!!.isEmpty()
        }
    }
