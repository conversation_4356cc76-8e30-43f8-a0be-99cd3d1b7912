package com.toyota.oneapp.viewmodel

import androidx.lifecycle.liveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.ui.BaseViewModel
import kotlinx.coroutines.Dispatchers
import toyotaone.commonlib.log.LogTool
import java.io.IOException

class LogCatViewModel : BaseViewModel() {
    private val tag = LogCatViewModel::class.java.name

    fun logcatOutput() =
        liveData(viewModelScope.coroutineContext + Dispatchers.IO) {
            try {
                Runtime
                    .getRuntime()
                    .exec("logcat")
                    .inputStream
                    .bufferedReader()
                    .useLines { lines -> lines.forEach { line -> emit(line) } }
            } catch (e: IOException) {
                LogTool.e(tag, "Exception : ${e.message}")
            }
        }
}
