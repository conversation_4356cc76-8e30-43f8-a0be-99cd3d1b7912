package com.toyota.oneapp.sharedpref

import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type
import javax.inject.Inject

class SharedPreferencesHelperImpl
    @Inject
    internal constructor(
        private val preferences: SharedPreferences,
    ) : SharedPreferencesHelper {
        private val gson = Gson()

        override fun <T> put(
            key: String,
            value: T,
        ): Boolean {
            val jsonValue = gson.toJson(value)
            getEditor().putString(key, jsonValue).apply()
            return true
        }

        override fun <T> get(
            key: String,
            clazz: Class<T>,
            defaultValue: T?,
        ): T? {
            val value = preferences.getString(key, null)
            return if (value != null) {
                try {
                    gson.fromJson(value, clazz)
                } catch (e: Exception) {
                    defaultValue
                }
            } else {
                defaultValue
            }
        }

        override fun getHashMap(key: String): HashMap<String, Int>? {
            val json = preferences.getString(key, "")
            return if (json != null) {
                val type: Type = object : TypeToken<HashMap<String, Int>>() {}.type
                Gson().fromJson(json, type)
            } else {
                null
            }
        }

        override fun delete(key: String): Boolean {
            getEditor().remove(key).apply()
            return true
        }

        override fun deleteAll(): Boolean {
            getEditor().clear().apply()
            return true
        }

        override fun count(): Long {
            return preferences.all.size.toLong()
        }

        override fun contains(key: String): Boolean {
            return preferences.contains(key)
        }

        private fun getEditor(): SharedPreferences.Editor {
            return preferences.edit()
        }
    }
