package com.toyota.oneapp.sharedpref.di

import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.sharedpref.SharedPreferencesHelper
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module
class SharedPrefModule {
    @Provides
    fun provideOneAppPreferenceModel(sharePreferenceHelper: SharedPreferencesHelper) = OneAppPreferenceModel(sharePreferenceHelper)
}
