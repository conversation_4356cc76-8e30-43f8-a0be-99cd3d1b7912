package com.toyota.oneapp.sharedpref

import android.content.Context
import android.content.Context.MODE_PRIVATE
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.core.ApplicationContext
import dagger.Module
import dagger.Provides
import dagger.Reusable
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking

@InstallIn(SingletonComponent::class)
@Module
class SharedPreferencesModule {
    private val version: Int = 1
    private val prefName = """${BuildConfig.APPLICATION_ID}$version"""

    @Provides
    @Reusable
    fun provideSharedPreferences(
        @ApplicationContext context: Context,
    ): SharedPreferences {
        return runBlocking(Dispatchers.IO) {
            val sharedPreferences =
                try {
                    val masterKey =
                        MasterKey.Builder(context)
                            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                            .build()

                    EncryptedSharedPreferences.create(
                        context,
                        prefName,
                        masterKey,
                        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM,
                    )
                } catch (exception: Exception) {
                    context.getSharedPreferences(prefName, MODE_PRIVATE)
                }

            // Return the encryptedSharedPreferences instance
            sharedPreferences
        }
    }

    @Provides
    @Reusable
    fun provideSharedPreferencesHelper(sharedPreferences: SharedPreferences): SharedPreferencesHelper {
        return SharedPreferencesHelperImpl(sharedPreferences)
    }
}
