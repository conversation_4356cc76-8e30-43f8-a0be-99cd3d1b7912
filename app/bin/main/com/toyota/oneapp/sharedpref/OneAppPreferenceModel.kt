/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.sharedpref

import com.google.gson.Gson
import com.google.protobuf.ByteString
import com.toyota.oneapp.app.ApplicationDataProvider
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.model.pref.BiometricPreferencesMigrationModel
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.ui.accountsettings.SecuritySettingsExpActivity
import com.toyota.oneapp.util.ToyotaConstants
import javax.inject.Inject

class OneAppPreferenceModel
    @Inject
    internal constructor(
        private val sharePreferenceHelper: SharedPreferencesHelper,
    ) {
        private val preferenceMigrated = "PREFERENCE_MIGRATED_1"

        private val biometricsPreferencesMigrated = "BIOMETRICS_MIGRATED"

        private val deviceToken = "DEVICE_TOKEN"

        /**
         * This GUID represents the user's OneApp account guid
         */
        private val guId = "GUID"

        /**
         * Post Android 12 user required deeplinks verifed, we use it determine if user choosen not to show dialog
         */
        private val verifyLinkDialogShown = "VERIFY_LINK_DIALOG_SHOWN"

        /**
         * Background permission dialog shown only once
         */
        private val backgroundLocationDialogShown = "BACKGROUND_LOCATION_DIALOG_SHOWN"

        /**
         * The guid of the last user that has enabled "Keep me logged in"
         */
        private val keepMeLoggedInGuId = "KEEP_ME_LOGIN_GUID"

        /**
         * Boolean value indicating if the user has signed out or not to accommodate for
         * "Keep me Signed in" functionality
         */
        private val isLoggedIn = "IS_LOGGED_IN"

        private val appUpdateSignIn = "APP_UPDATE_SIGN_IN"

        private val userProfilePacket1 = "USER_PROFILE_PACKET_1"

        private val userProfilePacket2 = "USER_PROFILE_PACKET_2"

        private val appIdpDataKey = "IDP_DATA_KEY"

        private val digitalDataKey = "DIGITAL_DATA_KEY"

        private val accountSubscriber = "ACCOUNT_SUBSCRIBER"

        private val isDigitalKeyPushEnabled = "IS_DIGITALKEY_PUSH_ENABLED"

        private val applicationDataProvider = "APPLICATION_DATA_PROVIDER"
        private val oaDarkMode = "OA_DARK_MODE"
        private val tfsSessionId = "TFS_SESSION_ID_TOKEN"
        private val tfsAccessToken = "TFS_ACCESS_TOKEN"
        private val tfsRefreshToken = "TFS_REFRESH_TOKEN"
        private val tfsExpireIn = "TFS_EXPIRE_IN"
        private val tfsAuthIdToken = "TFS_AUTH_ID_TOKEN"
        private val closedTime = "ClOSED_TIME"
        private val evSwapAnnouncementCount = "EV_SWAP_COUNT"
        private val evSwapExpireDate = "EV_SWAP_EXPRIE_DATE"
        private val announcementCaEnroll = "ANNOUNCEMENT_CA_ENROLL"

        private val isFtueCompleted = "IS_FTUE_COMPLETED"

        private val unlinkUser = "UNLINK_USER"

        private val setStoreChargeSessionId = "SET_STORE_CHARGE_SESSION_ID"
        private val setStoreChargePartner = "SET_STORE_CHARGE_PARTNER_ID"
        private val isSubscriptionSnippetShown = "IS_SUBSCRIPTION_SNIPPET_SHOW"

        private val passwordReset = "PASSWORD_RESET"

        private val climateCaution = "CLIMATE_CAUTION"

        private val dealerServiceSearchHistory = "DEALER_SERVICE_SEARCH_HISTORY"
        private val visitCountKey = "visitCount"
        private val visitCountEnrollmentFailureKey = "visitCountForEnrollmentFailure"

        private val showEVGoComplementaryPopUp1Point1 = "showEVGoComplementaryPopUp1Point1_"
        private val showEVGoComplementaryPopUp1Point2 = "showEVGoComplementaryPopUp1Point2_"
        private val showEVGoComplementaryPopUp1Point3 = "showEVGoComplementaryPopUp1Point3_"
        private val showEVGoComplementaryPopUp1Point4 = "showEVGoComplementaryPopUp1Point4_"

        private val biometricEnabled: String = "BIOMETRIC_ENABLED"
        private val biometricSettingsShown: String = "BIOMETRIC_SETTINGS_SHOWN"
        private val biometricSettingsTimeLabel: String = "BIOMETRIC_SETTINGS_TIME"

        fun getDeviceToken(): String =
            sharePreferenceHelper.get(
                deviceToken,
                String::class.java,
                ToyotaConstants.EMPTY_STRING,
            ) ?: ToyotaConstants.EMPTY_STRING

        fun setDeviceToken(deviceToken: String) {
            sharePreferenceHelper.put(this.deviceToken, deviceToken)
        }

        fun getGuid(): String =
            sharePreferenceHelper.get(guId, String::class.java, ToyotaConstants.EMPTY_STRING)
                ?: ToyotaConstants.EMPTY_STRING

        fun setGuid(guid: String) {
            sharePreferenceHelper.put(guId, guid)
        }

        fun isAppUpdateSignInShown(): Boolean = sharePreferenceHelper.get(appUpdateSignIn, Boolean::class.java, false) ?: false

        fun appUpdateSignInShowed(): Boolean = sharePreferenceHelper.put(appUpdateSignIn, true)

        fun isKeepMeLogin(): Boolean =
            if (sharePreferenceHelper.get(
                    keepMeLoggedInGuId,
                    String::class.java,
                    ToyotaConstants.EMPTY_STRING,
                ) == null
            ) {
                true
            } else {
                (
                    sharePreferenceHelper.get(
                        keepMeLoggedInGuId,
                        String::class.java,
                        ToyotaConstants.EMPTY_STRING,
                    ) ?: ToyotaConstants.EMPTY_STRING
                ) == getGuid()
            }

        fun setKeepMeLogin(guid: String) {
            sharePreferenceHelper.put(keepMeLoggedInGuId, guid)
        }

        fun isVerifiedDeepLinkShown(): Boolean = sharePreferenceHelper.get(verifyLinkDialogShown, Boolean::class.java, false) ?: false

        fun setVerifiedDeepLinkShown() {
            sharePreferenceHelper.put(verifyLinkDialogShown, true)
        }

        fun isBackgroundLocationPermissionShown(): Boolean =
            sharePreferenceHelper.get(
                backgroundLocationDialogShown,
                Boolean::class.java,
                false,
            ) ?: false

        fun setBackgroundLocationPermissionShown() {
            sharePreferenceHelper.put(backgroundLocationDialogShown, true)
        }

        fun firstTUECompleted() {
            sharePreferenceHelper.put(isFtueCompleted, true)
        }

        fun isFTUECompleted(): Boolean = sharePreferenceHelper.get(isFtueCompleted, Boolean::class.java, false) ?: false

        fun isLoggedIn(): Boolean = sharePreferenceHelper.get(isLoggedIn, Boolean::class.java, false) ?: false

        fun setLoggedIn(isLoggedIn: Boolean) {
            sharePreferenceHelper.put(this.isLoggedIn, isLoggedIn)
        }

        fun getUserProfilePacket1(): ByteString? {
            val byteArray =
                sharePreferenceHelper.get(
                    userProfilePacket1,
                    ByteArray::class.java,
                    null,
                )

            return byteArray?.let { ByteString.copyFrom(byteArray) }
        }

        fun setUserProfilePacket1(packet: ByteString) {
            val byteArray = packet.toByteArray()
            sharePreferenceHelper.put(userProfilePacket1, byteArray)
        }

        fun getUserProfilePacket2(): ByteString? {
            val byteArray =
                sharePreferenceHelper.get(
                    userProfilePacket2,
                    ByteArray::class.java,
                    null,
                )

            return byteArray?.let { ByteString.copyFrom(byteArray) }
        }

        fun setUserProfilePacket2(packet: ByteString) {
            val byteArray = packet.toByteArray()
            sharePreferenceHelper.put(userProfilePacket2, byteArray)
        }

        fun setFeatureFlagOverride(
            vin: String,
            featureFlagMap: HashMap<String, Int>,
        ) {
            sharePreferenceHelper.put(vin, featureFlagMap)
        }

        fun getFeatureFlagOverride(vin: String): HashMap<String, Int>? = sharePreferenceHelper.getHashMap(vin)

        fun resetFeatureFlag(vin: String) {
            sharePreferenceHelper.delete(vin)
        }

        fun clearCachedUserProfilePackets() {
            sharePreferenceHelper.delete(userProfilePacket1)
            sharePreferenceHelper.delete(userProfilePacket2)
        }

        fun getIDPData(): IDPData = sharePreferenceHelper.get(appIdpDataKey, IDPData::class.java, IDPData()) ?: IDPData()

        fun setIDPData(data: IDPData) {
            sharePreferenceHelper.put(appIdpDataKey, data)
        }

        fun getDigitalData(): DigitalKeyLocalData =
            sharePreferenceHelper.get(
                digitalDataKey,
                DigitalKeyLocalData::class.java,
                DigitalKeyLocalData(),
            ) ?: DigitalKeyLocalData()

        fun setDigitalData(data: DigitalKeyLocalData) {
            sharePreferenceHelper.put(digitalDataKey, data)
        }

        fun deleteDigitalData() {
            sharePreferenceHelper.delete(digitalDataKey)
        }

        fun getAccountInfoSubscriber(): AccountInfoSubscriber? =
            sharePreferenceHelper.get(
                accountSubscriber,
                AccountInfoSubscriber::class.java,
                null,
            )

        fun setAccountInfoSubscriber(data: AccountInfoSubscriber) {
            sharePreferenceHelper.put(accountSubscriber, data)
        }

        operator fun contains(keyName: String): Boolean = sharePreferenceHelper.contains(keyName)

        fun isPreferenceMigrated(): Boolean = sharePreferenceHelper.get(preferenceMigrated, Boolean::class.java, false) ?: false

        fun setPreferenceMigrated() {
            sharePreferenceHelper.put(preferenceMigrated, true)
        }

        fun isBiometricsPreferenceMigrated(): Boolean =
            sharePreferenceHelper.get(
                biometricsPreferencesMigrated, Boolean::class.java, false,
            ) ?: false

        fun setBiometricsPreferenceMigrated() {
            sharePreferenceHelper.put(biometricsPreferencesMigrated, true)
        }

        fun migrateBiometricsPreferences(biometricPrefs: BiometricPreferencesMigrationModel) {
            setBiometricEnabled(biometricPrefs.isBiometricEnabled)
            if (biometricPrefs.isBiometricSettingsShown) setBiometricSettingsShown(biometricPrefs.guid)
            setBiometricSettingsTime(biometricPrefs.biometricSettingsTime)
            setBiometricsPreferenceMigrated()
        }

        fun isDigitalKeyPushEnabled(): Boolean = sharePreferenceHelper.get(isDigitalKeyPushEnabled, Boolean::class.java, true) ?: false

        fun setDigitalKeyPushEnabled(isDigitalKeyPushEnabled: Boolean): Boolean =
            sharePreferenceHelper.put(this.isDigitalKeyPushEnabled, isDigitalKeyPushEnabled)

        fun isDarkModeEnabled(): Boolean = sharePreferenceHelper.get(oaDarkMode, Boolean::class.java, false) ?: false

        fun setDarkModeEnabled(darkEnabled: Boolean) {
            sharePreferenceHelper.put(oaDarkMode, darkEnabled)
            AppTheme.darkMode.value = darkEnabled
        }

        fun setAnnouncementPopupCloseTime(time: String) {
            sharePreferenceHelper.put(closedTime, time)
        }

        fun setDigitalKeyNoticeEnabled(vin: String?) {
            sharePreferenceHelper.put(ToyotaConstants.DK_NOTICE_PREFERENCE.plus(vin), true)
        }

        fun getDigitalKeyNoticeEnabled(vin: String?): Boolean =
            sharePreferenceHelper.get(
                ToyotaConstants.DK_NOTICE_PREFERENCE.plus(vin),
                Boolean::class.java,
                false,
            ) ?: false

        fun clearDigitalKeyNoticeEnabled(vin: String?) {
            sharePreferenceHelper.delete(ToyotaConstants.DK_NOTICE_PREFERENCE.plus(vin))
        }

        fun setAnnouncementPopupCloseTime(
            time: String,
            vin: String,
        ) {
            sharePreferenceHelper.put("$vin$closedTime", time)
        }

        fun getAnnouncementPopupCloseTime(vin: String): String =
            sharePreferenceHelper.get(
                "$vin$closedTime",
                String::class.java,
                ToyotaConstants.EMPTY_STRING,
            ) ?: ToyotaConstants.EMPTY_STRING

        fun setEvSwapAnnouncement(count: Int) {
            sharePreferenceHelper.put(evSwapAnnouncementCount, count)
        }

        fun getEvSwapAnnouncement(): Int = sharePreferenceHelper.get(evSwapAnnouncementCount, Int::class.java, 0) ?: 0

        fun setEvSwapExpireDate(date: String) {
            sharePreferenceHelper.put(evSwapExpireDate, date)
        }

        fun getEvSwapExpireDate(): String =
            sharePreferenceHelper.get(
                evSwapExpireDate,
                String::class.java,
                ToyotaConstants.EMPTY_STRING,
            ) ?: ToyotaConstants.EMPTY_STRING

        fun setAnnouncementCAEnroll(
            caEnrollStatus: Boolean,
            vin: String,
        ) {
            sharePreferenceHelper.put("$vin$announcementCaEnroll", caEnrollStatus)
        }

        fun getAnnouncementCAEnroll(vin: String): Boolean =
            sharePreferenceHelper.get("$vin$announcementCaEnroll", Boolean::class.java, false) ?: false

        fun setTFSIdToken(tfsIDSessionToken: String) {
            sharePreferenceHelper.put(tfsSessionId, tfsIDSessionToken)
        }

        fun getTFSIdToken(): String =
            sharePreferenceHelper.get(
                tfsSessionId,
                String::class.java,
                ToyotaConstants.EMPTY_STRING,
            ) ?: ToyotaConstants.EMPTY_STRING

        fun setTFSAccessToken(tfsAccessToken: String) {
            sharePreferenceHelper.put(this.tfsAccessToken, tfsAccessToken)
        }

        fun getTFSAccessToken(): String =
            sharePreferenceHelper.get(
                tfsAccessToken,
                String::class.java,
                ToyotaConstants.EMPTY_STRING,
            ) ?: ToyotaConstants.EMPTY_STRING

        fun setTFSRefreshToken(tfsAccessToken: String) {
            sharePreferenceHelper.put(tfsRefreshToken, tfsAccessToken)
        }

        fun getTFSRefreshToken(): String =
            sharePreferenceHelper.get(
                tfsRefreshToken,
                String::class.java,
                ToyotaConstants.EMPTY_STRING,
            ) ?: ToyotaConstants.EMPTY_STRING

        fun setTFSExpireIn(tfsExpireIn: Int) {
            sharePreferenceHelper.put(this.tfsExpireIn, tfsExpireIn)
        }

        fun getTFSExpireIn(): Int =
            sharePreferenceHelper.get(
                tfsExpireIn,
                Int::class.java,
                0,
            ) ?: 0

        fun setAuthenticateIdToken(idToken: String) {
            sharePreferenceHelper.put(tfsAuthIdToken, idToken)
        }

        fun getAuthenticateIdToken(): String =
            sharePreferenceHelper.get(
                tfsAuthIdToken,
                String::class.java,
                ToyotaConstants.EMPTY_STRING,
            ) ?: ToyotaConstants.EMPTY_STRING

        fun setTfsUnLinkUser(unlink: Boolean) {
            sharePreferenceHelper.put(unlinkUser, unlink)
        }

        fun getTfsUnLinkUser(): Boolean = sharePreferenceHelper.get(unlinkUser, Boolean::class.java, false) ?: false

        fun setStoreChargeSessionId(chargeSessionId: String) {
            sharePreferenceHelper.put(setStoreChargeSessionId, chargeSessionId)
        }

        fun getStoreChargeSessionId(): String =
            sharePreferenceHelper.get(
                setStoreChargeSessionId,
                String::class.java,
                ToyotaConstants.EMPTY_STRING,
            ) ?: ToyotaConstants.EMPTY_STRING

        fun setStoreChargePartner(chargePartnerId: String) {
            sharePreferenceHelper.put(setStoreChargePartner, chargePartnerId)
        }

        fun getStoreChargePartner(): String =
            sharePreferenceHelper.get(
                setStoreChargePartner,
                String::class.java,
                ToyotaConstants.EMPTY_STRING,
            ) ?: ToyotaConstants.EMPTY_STRING

        fun disableSubscriptionSnippetData(vin: String) {
            sharePreferenceHelper.put("$vin$isSubscriptionSnippetShown", false)
        }

        fun enableSubscriptionSnippet(vin: String) {
            sharePreferenceHelper.put("$vin$isSubscriptionSnippetShown", true)
        }

        fun getSubscriptionSnippetStatus(vin: String): Boolean =
            sharePreferenceHelper.get(
                "$vin$isSubscriptionSnippetShown",
                Boolean::class.java,
                true,
            ) ?: true

        fun getApplicationDataProvider(): ApplicationDataProvider =
            sharePreferenceHelper.get(
                applicationDataProvider,
                ApplicationDataProvider::class.java,
                null,
            ) ?: ApplicationDataProvider()

        fun setApplicationDataProvider(data: ApplicationDataProvider) {
            sharePreferenceHelper.put(applicationDataProvider, data)
        }

        fun clearApplicationData() {
            sharePreferenceHelper.put(applicationDataProvider, ApplicationDataProvider())
        }

        fun isFlowFromPasswordReset(): Boolean = sharePreferenceHelper.get(passwordReset, Boolean::class.java, false) ?: false

        fun setFlowFromPasswordReset() {
            sharePreferenceHelper.put(passwordReset, true)
        }

        fun clearFlowFromPasswordReset() {
            sharePreferenceHelper.put(passwordReset, false)
        }

        fun isClimateCautionShown(): Boolean = sharePreferenceHelper.get(climateCaution, Boolean::class.java, false) ?: false

        fun setClimateCautionShown() {
            sharePreferenceHelper.put(climateCaution, true)
        }

        fun addDealerServiceSearchHistoryItem(searchString: String) {
            val currentList = getDealerServiceSearchHistory()
            currentList.remove(searchString)
            currentList.add(0, searchString)
            val gson = Gson()
            val jsonText = gson.toJson(currentList)
            sharePreferenceHelper.put(dealerServiceSearchHistory, jsonText)
        }

        fun getDealerServiceSearchHistory(): ArrayList<String> {
            val jsonText =
                sharePreferenceHelper.get(
                    dealerServiceSearchHistory,
                    String::class.java,
                    "",
                )
            val gson = Gson()
            val searchHistory = gson.fromJson(jsonText, Array<String>::class.java)
            return searchHistory?.toCollection(ArrayList()) ?: arrayListOf()
        }

        fun getVisitCount(): Int {
            return sharePreferenceHelper.get(visitCountKey, Int::class.java, 0) ?: 0
        }

        fun getVisitCountForEnrollmentFailure(): Int {
            return sharePreferenceHelper.get(visitCountEnrollmentFailureKey, Int::class.java, 0) ?: 0
        }

        fun incrementVisitCount() {
            val currentCount = getVisitCount()
            sharePreferenceHelper.put(visitCountKey, currentCount + 1)
        }

        fun incrementVisitCountForEnrollmentFailure() {
            val currentCount = getVisitCountForEnrollmentFailure()
            sharePreferenceHelper.put(visitCountEnrollmentFailureKey, currentCount + 1)
        }

        fun resetVisitCount() {
            sharePreferenceHelper.put(visitCountKey, 0)
            sharePreferenceHelper.put(visitCountEnrollmentFailureKey, 0)
        }

        fun getActiveSwitch(): String {
            return sharePreferenceHelper.get("active_switch", String::class.java, "") ?: ""
        }

        fun saveActiveSwitch(switchId: String) {
            sharePreferenceHelper.put("active_switch", switchId)
        }

        /* method for saving, reset and loading isZipEnteredOnEligibilityScreenValid
           for specific user
         */
        fun saveZipValidity(
            userId: String,
            isValid: Boolean?,
        ) {
            sharePreferenceHelper.put(userId, isValid)
        }

        fun resetZipValidity(userId: String) {
            sharePreferenceHelper.delete(userId)
        }

        fun loadZipValidity(userId: String): Boolean? {
            return sharePreferenceHelper.get(userId, Boolean::class.java, null)
        }

        fun showEVGoComplementaryPopUp1Point1(vin: String) {
            sharePreferenceHelper.put("$showEVGoComplementaryPopUp1Point1$vin", true)
        }

        fun getShowEVGoComplementaryPopUp1Point1(vin: String): Boolean =
            sharePreferenceHelper.get(
                "$showEVGoComplementaryPopUp1Point1$vin",
                Boolean::class.java,
                false,
            ) ?: false

        fun showEVGoComplementaryPopUp1Point2(vin: String) {
            sharePreferenceHelper.put("$showEVGoComplementaryPopUp1Point2$vin", true)
        }

        fun getShowEVGoComplementaryPopUp1Point2(vin: String): Boolean =
            sharePreferenceHelper.get(
                "$showEVGoComplementaryPopUp1Point2$vin",
                Boolean::class.java,
                false,
            ) ?: false

        fun showEVGoComplementaryPopUp1Point3(vin: String) {
            sharePreferenceHelper.put("$showEVGoComplementaryPopUp1Point3$vin", true)
        }

        fun getShowEVGoComplementaryPopUp1Point3(vin: String): Boolean =
            sharePreferenceHelper.get(
                "$showEVGoComplementaryPopUp1Point3$vin",
                Boolean::class.java,
                false,
            ) ?: false

        fun showEVGoComplementaryPopUp1Point4(vin: String) {
            sharePreferenceHelper.put("$showEVGoComplementaryPopUp1Point4$vin", true)
        }

        fun getShowEVGoComplementaryPopUp1Point4(vin: String): Boolean =
            sharePreferenceHelper.get(
                "$showEVGoComplementaryPopUp1Point4$vin",
                Boolean::class.java,
                false,
            ) ?: false

        // ******* The following are to replace the deprecated implementations for biometrics *******

        fun isBiometricEnabled(): Boolean {
            return sharePreferenceHelper.get(biometricEnabled, Boolean::class.java, false) == true
        }

        fun setBiometricEnabled(enabled: Boolean) {
            sharePreferenceHelper.put(biometricEnabled, enabled)
        }

        fun isBiometricSettingsShown(guid: String): Boolean {
            return sharePreferenceHelper.get(biometricSettingsShown, String::class.java, "") == guid
        }

        fun setBiometricSettingsShown(guid: String) {
            sharePreferenceHelper.put(biometricSettingsShown, guid)
        }

        fun getBiometricSettingsTime(): String {
            return sharePreferenceHelper.get(
                biometricSettingsTimeLabel,
                String::class.java,
                SecuritySettingsExpActivity.FIFTEEN_MIN,
            ) ?: SecuritySettingsExpActivity.FIFTEEN_MIN
        }

        fun setBiometricSettingsTime(biometricSettingsTime: String) {
            sharePreferenceHelper.put(biometricSettingsTimeLabel, biometricSettingsTime)
        }

        fun deleteBiometricsSettingsTime() {
            sharePreferenceHelper.delete(biometricSettingsTimeLabel)
        }
    }
