package com.toyota.oneapp.ui

import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityBlockFleetVehicleBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BlockFleetVehicleActivity : UiBaseActivity() {
    companion object {
        const val EXTRA_BLOCK_VIN_FROM_QR_SCAN = "EXTRA_BLOCK_VIN_FROM_QR_SCAN"
        const val EXTRA_BLOCK_VIN_FROM_MANUAL_ENTRY = "EXTRA_BLOCK_VIN_FROM_MANUAL_ENTRY"
        const val EXTRA_BLOCK_FLEET_DESCRIPTION = "EXTRA_BLOCK_FLEET_DESCRIPTION"
        const val EXTRA_CALIFORNIA_SENATE_BILL = "EXTRA_CALIFORNIA_SENATE_BILL"
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        val binding: ActivityBlockFleetVehicleBinding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_block_fleet_vehicle,
            )

        if (intent.getBooleanExtra(EXTRA_BLOCK_VIN_FROM_QR_SCAN, false)) {
            binding.layoutBlockFleet21mm.visibility = View.VISIBLE
            binding.layoutBlockFleetPre21mm.visibility = View.GONE
            if (intent.getBooleanExtra(EXTRA_CALIFORNIA_SENATE_BILL, false)) {
                binding.txtBlockFleetDescription21mm.text =
                    intent.getStringExtra(
                        EXTRA_BLOCK_FLEET_DESCRIPTION,
                    )?.replace("<break>", "\n") ?: ""
            } else {
                binding.txtBlockFleetDescription21mm.text =
                    intent.getStringExtra(
                        EXTRA_BLOCK_FLEET_DESCRIPTION,
                    )
            }
            if (intent.getBooleanExtra(EXTRA_CALIFORNIA_SENATE_BILL, false)) {
                binding.txtBlockTitle.text =
                    String.format(
                        getString(R.string.california_senate_bill_title),
                        Brand.currentAppBrand().brandName,
                    )
            }
        } else {
            binding.layoutBlockFleet21mm.visibility = View.GONE
            binding.layoutBlockFleetPre21mm.visibility = View.VISIBLE
            if (intent.getBooleanExtra(EXTRA_CALIFORNIA_SENATE_BILL, false)) {
                binding.txtBlockFleetDescriptionPre21mm.text =
                    intent.getStringExtra(
                        EXTRA_BLOCK_FLEET_DESCRIPTION,
                    )?.replace("<break>", "\n") ?: ""
            } else {
                binding.txtBlockFleetDescriptionPre21mm.text =
                    intent.getStringExtra(
                        EXTRA_BLOCK_FLEET_DESCRIPTION,
                    )
            }

            if (intent.getBooleanExtra(EXTRA_CALIFORNIA_SENATE_BILL, false)) {
                binding.txtBlockTitle.text =
                    String.format(
                        getString(R.string.california_senate_bill_title),
                        Brand.currentAppBrand().brandName,
                    )
            }
        }

        binding.btnGotoDashboard.setOnClickListener {
            startActivity(
                IntentUtil.getOADashBoardIntent(context = this, isDashboardRefresh = true),
            )
        }
    }
}
