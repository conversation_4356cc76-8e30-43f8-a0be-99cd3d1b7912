package com.toyota.oneapp.ui.payment.select

import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.core.view.size
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentPaymentsBinding
import com.toyota.oneapp.ui.BaseViewModelFragment
import com.toyota.oneapp.ui.garage.activity.WebPaymentArguments
import com.toyota.oneapp.ui.garage.activity.WebPaymentContract
import com.toyota.oneapp.ui.garage.activity.WebPaymentResult
import com.toyota.oneapp.ui.garage.subscriptionV2.SubscriptionV2Activity
import com.toyota.oneapp.ui.payment.select.PaymentsFragmentDirections.Companion.actionDetails
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentsFragment : BaseViewModelFragment() {
    private val viewModel: PaymentsViewModel by viewModels()

    private val addPaymentContract =
        registerForActivityResult<WebPaymentArguments, WebPaymentResult>(
            WebPaymentContract(),
        ) { result ->
            if (result is WebPaymentResult.Success) {
                if (result.setAsDefault) {
                    viewModel.makeDefault(result.token)
                } else {
                    viewModel.refreshPayments()
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
        setFragmentResultListener(RESULT_PAYMENTS) { _, bundle ->
            if (bundle.getBoolean(KEY_REFRESH, false)) {
                viewModel.refreshPayments()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View =
        FragmentPaymentsBinding
            .inflate(
                inflater,
                container,
                false,
            ).also { binding ->
                binding.recyclerView.adapter =
                    PaymentsAdapter(
                        viewModel::paymentRecordClicked,
                        viewModel::addPaymentTypeClicked,
                    )
                binding.vehicleImagesRecyclerView.adapter = viewModel.adapter
                binding.vehicleImagesRecyclerView.apply {
                    layoutManager =
                        LinearLayoutManager(
                            binding.recyclerView.context,
                            LinearLayoutManager.HORIZONTAL,
                            false,
                        )
                    addItemDecoration(
                        object : RecyclerView.ItemDecoration() {
                            override fun getItemOffsets(
                                outRect: Rect,
                                view: View,
                                parent: RecyclerView,
                                state: RecyclerView.State,
                            ) {
                                when (parent.getChildAdapterPosition(view)) {
                                    0 -> {
                                        outRect.set(
                                            resources.getDimensionPixelSize(toyotaone.commonlib.R.dimen.margin_32_dp),
                                            0,
                                            resources.getDimensionPixelSize(R.dimen.default_margin_end),
                                            0,
                                        )
                                    }
                                    parent.size - 1 -> {
                                        outRect.set(
                                            resources.getDimensionPixelSize(R.dimen.default_margin_end),
                                            0,
                                            resources.getDimensionPixelSize(R.dimen.default_margin_end),
                                            0,
                                        )
                                    }
                                    else -> {
                                        outRect.set(
                                            resources.getDimensionPixelSize(R.dimen.default_margin_end),
                                            0,
                                            resources.getDimensionPixelSize(R.dimen.default_margin_end),
                                            0,
                                        )
                                    }
                                }
                            }
                        },
                    )
                }

                binding.lifecycleOwner = viewLifecycleOwner
                viewModel.data.observe(viewLifecycleOwner) { data ->
                    DataBindingAdapters.setRecyclerViewAdapterData(binding.vehicleImagesRecyclerView, data.vehicles, emptyList())
                    binding.header.text = getString(R.string.Subscription_payments_for_vehicles, data.vehicleNames)
                }
                viewModel.items.observe(viewLifecycleOwner) { data ->
                    DataBindingAdapters.setRecyclerViewAdapterData(binding.recyclerView, data, emptyList())
                    binding.recyclerView.isVisible = data.isNotEmpty()
                }
            }.root

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.apply {
            onPaymentRecord.observe(viewLifecycleOwner) {
                findNavController().navigate(actionDetails(it, viewModel.getPaymentRecords()))
            }
            onAddPayment.observe(viewLifecycleOwner) {
                addPaymentContract.launch(it)
            }
            items.observe(viewLifecycleOwner) {
                if (it.isNullOrEmpty()) {
                    viewModel.logAnalyticEventForNoPaymentMethods()
                    MaterialAlertDialogBuilder(requireContext()).apply {
                        setMessage(R.string.PaymentPage_empty_payments)
                        setCancelable(false)
                        setPositiveButton(R.string.Subscription_manage_subscription) { _, _ ->
                            startActivity(
                                Intent(context, SubscriptionV2Activity::class.java)
                                    .putExtra(
                                        SubscriptionV2Activity.EXTRA_VEHICLE,
                                        viewModel.selectedVehicle,
                                    )
                                    .putExtra(
                                        SubscriptionV2Activity.EXTRA_SUBSCRIPTION_BTN_FLOW,
                                        true,
                                    ),
                            )
                            findNavController().navigateUp()
                        }
                        setNegativeButton(R.string.Common_cancel) { _, _ ->
                            findNavController().navigateUp()
                        }
                    }.show()
                }
            }
        }
    }

    companion object {
        const val RESULT_PAYMENTS = "result_payments"
        const val KEY_REFRESH = "refresh"
    }
}
