package com.toyota.oneapp.ui.payment.select

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.StringRes
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.NO_POSITION
import com.toyota.oneapp.databinding.ItemPaymentAddBinding
import com.toyota.oneapp.databinding.ItemPaymentHeaderBinding
import com.toyota.oneapp.databinding.ItemPaymentRecordBinding
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.ui.garage.paymentOptions.PaymentOption
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class PaymentsAdapter(
    private val onClickListener: ((PaymentRecord) -> Unit),
    private val onAddListener: ((PaymentOption.Type) -> Unit),
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    BindableRecyclerViewAdapter<PaymentsAdapter.PaymentUiItem> {
    sealed class PaymentUiItem {
        data class Header(
            @StringRes val stringRes: Int,
        ) : PaymentUiItem()

        data class Payment(
            val paymentOption: PaymentOption,
        ) : PaymentUiItem()

        data class Add(
            @StringRes val stringRes: Int,
            val paymentType: PaymentOption.Type,
        ) : PaymentUiItem()
    }

    private val items = mutableListOf<PaymentUiItem>()

    override fun setData(data: List<PaymentUiItem>?) {
        items.clear()
        if (data != null) {
            items += data
        }
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = items.size

    override fun getItemViewType(position: Int): Int =
        when (items[position]) {
            is PaymentUiItem.Header -> TYPE_HEADER
            is PaymentUiItem.Payment -> TYPE_PAYMENT
            is PaymentUiItem.Add -> TYPE_ADD
        }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder =
        LayoutInflater.from(parent.context).let { inflater ->
            when (viewType) {
                TYPE_HEADER ->
                    HeaderHolder(
                        ItemPaymentHeaderBinding.inflate(
                            inflater,
                            parent,
                            false,
                        ),
                    )
                TYPE_PAYMENT ->
                    PaymentHolder(
                        ItemPaymentRecordBinding.inflate(
                            inflater,
                            parent,
                            false,
                        ),
                    ).also { holder ->
                        holder.itemView.setOnClickListener {
                            val adapterPosition = holder.adapterPosition
                            if (adapterPosition != NO_POSITION) {
                                (items[adapterPosition] as? PaymentUiItem.Payment)?.let {
                                    onClickListener.invoke(it.paymentOption.paymentRecord)
                                }
                            }
                        }
                    }
                TYPE_ADD ->
                    AddHolder(
                        ItemPaymentAddBinding.inflate(
                            inflater,
                            parent,
                            false,
                        ),
                    ).also { holder ->
                        holder.itemView.setOnClickListener {
                            val adapterPosition = holder.adapterPosition
                            if (adapterPosition != NO_POSITION) {
                                (items[adapterPosition] as? PaymentUiItem.Add)?.let {
                                    onAddListener.invoke(it.paymentType)
                                }
                            }
                        }
                    }
                else -> throw IllegalArgumentException("Unknown viewType: $viewType")
            }
        }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        items[position].let {
            when (it) {
                is PaymentUiItem.Header -> (holder as HeaderHolder).bind(it)
                is PaymentUiItem.Payment -> (holder as PaymentHolder).bind(it)
                is PaymentUiItem.Add -> (holder as AddHolder).bind(it)
            }
        }
    }

    inner class HeaderHolder(
        private val binding: ItemPaymentHeaderBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PaymentUiItem.Header) {
            binding.item = item
        }
    }

    inner class PaymentHolder(
        private val binding: ItemPaymentRecordBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PaymentUiItem.Payment) {
            binding.logo.setImageResource(item.paymentOption.logoResourceId)
            binding.accountNumber.text = item.paymentOption.accountNumber
            binding.labelDefault.isVisible = item.paymentOption.isDefaultPayment
        }
    }

    inner class AddHolder(
        private val binding: ItemPaymentAddBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PaymentUiItem.Add) {
            binding.item = item
        }
    }

    companion object {
        private const val TYPE_HEADER = 1
        private const val TYPE_PAYMENT = 2
        private const val TYPE_ADD = 3
    }
}
