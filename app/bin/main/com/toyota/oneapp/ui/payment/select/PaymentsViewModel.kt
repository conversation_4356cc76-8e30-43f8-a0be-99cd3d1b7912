package com.toyota.oneapp.ui.payment.select

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.*
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscription.PaymentsResponse
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.garage.activity.WebPaymentArguments
import com.toyota.oneapp.ui.garage.paymentOptions.PaymentOption
import com.toyota.oneapp.ui.garage.paymentOptions.PaymentOption.Type
import com.toyota.oneapp.ui.payment.select.PaymentsAdapter.PaymentUiItem
import com.toyota.oneapp.ui.payment.select.PaymentsAdapter.PaymentUiItem.Header
import com.toyota.oneapp.ui.payment.select.PaymentsAdapter.PaymentUiItem.Payment
import com.toyota.oneapp.ui.payment.service.ServiceVehiclesAdapter
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

@HiltViewModel
class PaymentsViewModel
    @Inject
    constructor(
        private val subscriptionAPIManager: SubscriptionAPIManager,
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
        coroutineContext: CoroutineContext,
        private val state: SavedStateHandle,
    ) : BaseViewModel() {
        @VisibleForTesting
        val paymentRecordsData = MutableLiveData<List<PaymentRecord>>()
        val data: MutableLiveData<Data> = MutableLiveData()
        var vehicle: VehicleInfo = state["vehicleInfo"] ?: VehicleInfo()
        var selectedVehicle: VehicleInfo? =
            if (!applicationData.getSelectedVehicle()?.generation.isNullOrBlank() && applicationData.getSelectedVehicle()?.isCY17 != true) {
                applicationData.getSelectedVehicle()
            } else {
                applicationData.getVehicleList()?.firstOrNull { it.preferred == 1 && !it.isCY17 }
                    ?: applicationData.getVehicleList()?.firstOrNull { !it.generation.isNullOrBlank() && !it.isCY17 }
                    ?: applicationData.getSelectedVehicle()
            }

        @VisibleForTesting
        var hasPaymentRecords = false

        val adapter = ServiceVehiclesAdapter()

        init {
            viewModelScope.launch {
                refreshPayments()
            }
            val vehicles = applicationData.getVehicleList()?.filter { !it.generation.isNullOrBlank() && !it.isCY17 }
            vehicles?.let { vehicleInfoList ->
                val newData =
                    Data(
                        vehicleNames = vehicleInfoList.joinToString { "${it.modelYear ?: ""} ${it.modelDescription ?: ""}" },
                        vehicles = vehicleInfoList,
                    )
                data.postValue(newData)
            }
        }

        val items: LiveData<List<PaymentUiItem>> =
            paymentRecordsData.switchMap { paymentRecords ->
                liveData<List<PaymentUiItem>>(coroutineContext) {
                    val creditCards = mutableListOf<Payment>()
                    val bank = mutableListOf<Payment>()

                    paymentRecords.forEach {
                        val option = Payment(PaymentOption.create(it))
                        when (PaymentOption.selectType(it)) {
                            Type.CREDIT_CARD -> creditCards += option
                            Type.BANK_ACCOUNT -> bank += option
                        }
                    }

                    val items = mutableListOf<PaymentUiItem>()

                    hasPaymentRecords = paymentRecords.isNotEmpty()
                    if (hasPaymentRecords) {
                        items += Header(R.string.ManagePaidSubscription_credit_card)
                        items += creditCards
                        items +=
                            PaymentUiItem.Add(
                                R.string.ManagePaidSubscription_add_credit_card,
                                Type.CREDIT_CARD,
                            )

                        if (applicationData.getVehicleList()?.any {
                                it.isFeatureEnabled(
                                    Feature.ACH_PAYMENT,
                                )
                            } == true
                        ) {
                            items += Header(R.string.ManagePaidSubscription_bank_account)
                            items += bank
                            items +=
                                PaymentUiItem.Add(
                                    R.string.ManagePaidSubscription_add_bank_account,
                                    Type.BANK_ACCOUNT,
                                )
                        } else if (bank.isNotEmpty()) {
                            items += Header(R.string.ManagePaidSubscription_bank_account)
                            items += bank
                        }
                    }

                    emit(items)
                }
            }

        private val paymentRecordClicked = SingleLiveEvent<PaymentRecord>()
        val onPaymentRecord: LiveData<PaymentRecord> = paymentRecordClicked

        fun paymentRecordClicked(paymentRecord: PaymentRecord) {
            paymentRecordClicked.value = paymentRecord
        }

        private val addPayment = SingleLiveEvent<WebPaymentArguments>()
        val onAddPayment: LiveData<WebPaymentArguments> = addPayment

        fun addPaymentTypeClicked(type: Type) {
            paymentRecordsData.value?.firstOrNull()?.accountId.let { accountId ->
                val paymentType =
                    when (type) {
                        Type.CREDIT_CARD -> SubscriptionAPIManager.WebPaymentType.CREDIT_CARD
                        Type.BANK_ACCOUNT -> SubscriptionAPIManager.WebPaymentType.ACH
                    }
                vehicle.let {
                    addPayment.value =
                        WebPaymentArguments(
                            paymentType,
                            hasPaymentRecords,
                            accountId,
                            it.brand,
                            it.region,
                            it.generation,
                        )
                }
            }
        }

        fun refreshPayments() {
            showProgress()
            subscriptionAPIManager.sendGetPayments(
                object : BaseCallback<PaymentsResponse>() {
                    override fun onSuccess(response: PaymentsResponse) {
                        analyticsLogger.logEvent(AnalyticsEvent.ZUORA_GET_PAYMENT_METHODS_SUCCESS)
                        paymentRecordsData.value = response.payload.records ?: emptyList()
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        analyticsLogger.logEvent(AnalyticsEvent.ZUORA_GET_PAYMENT_METHODS_FAILED)
                        paymentRecordsData.value = emptyList()
                        showErrorMessage(errorMsg)
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun makeDefault(token: String) {
            showProgress()
            analyticsLogger.logEventWithParameter(
                AnalyticsEventParam.ZUORA_PAYMENT,
                AnalyticsEventParam.ZUORA_MAKE_DEFAULT_PAYMENT_METHOD,
            )
            subscriptionAPIManager.setDefaultPayment(
                PaymentRecord(id = token),
                object : BaseCallback<BaseResponse>() {
                    override fun onComplete() {
                        hideProgress()
                        refreshPayments()
                    }
                },
            )
        }

        fun logAnalyticEventForNoPaymentMethods() {
            analyticsLogger.logEventWithParameter(
                AnalyticsEventParam.ZUORA_PAYMENT,
                AnalyticsEventParam.ZUORA_ADD_PAYMENT_WITHOUT_SUBSCRIPTIONS,
            )
        }

        fun getPaymentRecords(): Array<PaymentRecord> = paymentRecordsData.value?.toTypedArray() ?: emptyArray()

        data class Data(
            val vehicleNames: String,
            val vehicles: List<VehicleInfo>,
        )
    }
