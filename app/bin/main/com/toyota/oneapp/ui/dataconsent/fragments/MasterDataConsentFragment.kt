package com.toyota.oneapp.ui.dataconsent.fragments

import android.os.Bundle
import android.text.method.LinkMovementMethod
import androidx.fragment.app.activityViewModels
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentMasterDataConsentBinding
import com.toyota.oneapp.extensions.HTMLTextClickListener
import com.toyota.oneapp.extensions.setTextViewHTML
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.dataconsent.viewmodels.MasterDataConsentViewModel
import toyotaone.commonlib.dialog.DialogUtil

class MasterDataConsentFragment :
    BaseDataBindingFragment<FragmentMasterDataConsentBinding>(),
    HTMLTextClickListener {
    private val mdcViewModel: MasterDataConsentViewModel by activityViewModels()

    override fun onViewBound(
        binding: FragmentMasterDataConsentBinding,
        savedInstance: Bundle?,
    ) {
        binding.mdcViewModel = mdcViewModel
        binding.itemDataConsentBody.movementMethod = LinkMovementMethod.getInstance()

        mdcViewModel.mdc.observe(this) {
            binding.itemDataConsentBody.setTextViewHTML(it.description?.body ?: "", it, this)
        }
    }

    override fun getLayout(): Int = R.layout.fragment_master_data_consent

    override fun showLinkMessage(message: String) {
        DialogUtil.showDialog(
            activity,
            null,
            message,
            getString(R.string.ok_label),
        )
    }
}
