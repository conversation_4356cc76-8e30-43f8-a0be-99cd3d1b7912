package com.toyota.oneapp.ui.dataconsent.viewmodels

import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.dataconsent.activities.BannerConsentSuccessDeclineActivity
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@HiltViewModel
class BannerConsentSuccessDeclineViewModel
    @Inject
    constructor() : BaseViewModel() {
        private var _data = MutableLiveData<BannerConsentSuccessDeclineActivity.Companion.BannerConsentSuccessDecline>()
        val data get() = _data

        val backToDashboard = SingleLiveEvent<Unit>()

        fun setData(bannerConsentSuccessDecline: BannerConsentSuccessDeclineActivity.Companion.BannerConsentSuccessDecline) {
            LogTool.d("title", bannerConsentSuccessDecline.titleResId.toString())
            LogTool.d("descripption", bannerConsentSuccessDecline.descriptionLineResId.toString())
            _data.postValue(bannerConsentSuccessDecline)
        }

        fun onBackToDashboardClicked() {
            backToDashboard.call()
        }
    }
