package com.toyota.oneapp.ui.dataconsent.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.model.vehicle.CapabilityItem
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DataConsentLCFSViewModel
    @Inject
    constructor(
        private val accountManager: AccountManager,
        private val subscriptionManager: SubscriptionAPIManager? = null,
        private val repository: CombinedDataConsentRepository,
        private val applicationData: ApplicationData? = null,
    ) : BaseViewModel() {
        private val _progressState: MutableLiveData<Boolean> = MutableLiveData(false)
        val progressState: MutableLiveData<Boolean>
            get() = _progressState

        private val _showDialog: MutableLiveData<String> = MutableLiveData()
        val showDialog: MutableLiveData<String>
            get() = _showDialog

        fun acceptAction(subscriptionGetPayload: SubscriptionGetPayload?) {
            try {
                createSubscriptionRequest(subscriptionGetPayload)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        fun noThanksAction(subscriptionGetPayload: SubscriptionGetPayload?) {
            sendWaiveSubscriptionRequest(subscriptionGetPayload)
        }

        private fun sendWaiveSubscriptionRequest(subscriptionGetPayload: SubscriptionGetPayload?) {
            applicationData?.getSelectedVehicle()?.let {
                if (subscriptionGetPayload != null) {
                    _progressState.postValue(true)

                    val capabilities: ArrayList<CapabilityItem> =
                        if (applicationData.savedVehicles.isEmpty()) {
                            it.capabilityItems
                        } else {
                            applicationData.savedVehicles.firstOrNull()?.capabilities
                                ?: ArrayList()
                        }

                    viewModelScope.launch {
                        val resource =
                            repository.waiveSubscription(
                                isWaiver = false,
                                asiCode = applicationData.getSelectedVehicle()?.asiCode.orEmpty(),
                                hwtType = applicationData.getSelectedVehicle()?.hwType.orEmpty(),
                                subscriptionGetPayload = subscriptionGetPayload,
                                capabilities = capabilities,
                            )

                        when (resource) {
                            is Resource.Success -> {
                                _progressState.postValue(false)
                            }

                            is Resource.Failure -> {
                                _progressState.postValue(false)
                                _showDialog.postValue(resource.message)
                            }
                            else -> {}
                        }
                    }
                }
            }
        }

        private fun createSubscriptionRequest(subscriptionGetPayload: SubscriptionGetPayload?) {
            applicationData?.getSelectedVehicle()?.let {
                val productsTotalAmount = ProductsTotalAmount(CURRENCY, 0.0)
                val productsAmount = ProductsAmount(CURRENCY, 0.0)
                val taxAmount = TaxAmount(CURRENCY, 0.0)

                val capabilities: ArrayList<CapabilityItem>
                if (applicationData.savedVehicles.isEmpty()) {
                    capabilities = it.capabilityItems
                } else {
                    capabilities = applicationData.savedVehicles.firstOrNull()?.capabilities
                        ?: ArrayList()
                }

                viewModelScope.launch {
                    _progressState.postValue(true)
                    val createSubscriptionResource =
                        repository.createSubscription(
                            asiCode = applicationData.getSelectedVehicle()?.asiCode.orEmpty(),
                            hwtType = applicationData.getSelectedVehicle()?.hwType.orEmpty(),
                            refId = "",
                            isPaymentDefault = false,
                            paymentToken = "",
                            accessToken = "",
                            subscriptionGetPayload = subscriptionGetPayload,
                            capabilities = capabilities,
                            consent = null,
                            totalAmount = productsTotalAmount,
                            productAmount = productsAmount,
                            taxAmount = taxAmount,
                        )

                    when (createSubscriptionResource) {
                        is Resource.Success -> {
                            _progressState.postValue(false)
                        }

                        is Resource.Failure -> {
                            _progressState.postValue(false)
                            _showDialog.postValue(createSubscriptionResource.message)
                        }
                        else -> {}
                    }
                }
            }
        }

        fun logout() {
            accountManager.logout()
        }

        companion object {
            private const val CURRENCY = "USD"
        }
    }
