package com.toyota.oneapp.ui.dataconsent.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.extensions.HTMLTextClickListener
import com.toyota.oneapp.extensions.setTextViewHTML
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.combineddataconsent.DataConsentStatus
import com.toyota.oneapp.ui.dataconsent.viewmodels.CombinedDataConsentItemViewModel

class CombinedDataConsentAdapter(
    private val items: List<CombineDataConsent>,
    val listener: CombinedDataConsentListener,
    val htmlTextClickListener: HTMLTextClickListener,
) : RecyclerView.Adapter<CombinedDataConsentAdapter.CombinedDataConsentViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): CombinedDataConsentViewHolder {
        val root =
            LayoutInflater
                .from(parent.context)
                .inflate(R.layout.item_data_consent, parent, false)
        return CombinedDataConsentViewHolder(root)
    }

    override fun getItemCount(): Int = items.size

    override fun onBindViewHolder(
        holder: CombinedDataConsentViewHolder,
        position: Int,
    ) {
        holder.bindData(CombinedDataConsentItemViewModel(items[position]))
    }

    inner class CombinedDataConsentViewHolder(
        itemView: View,
    ) : RecyclerView.ViewHolder(itemView) {
        var title: TextView = itemView.findViewById(R.id.item_data_consent_title)
        var body: TextView = itemView.findViewById(R.id.item_data_consent_body)
        var positiveButton: AppCompatButton =
            itemView.findViewById(
                R.id.item_data_consent_positive_button,
            )
        var negativeButton: AppCompatButton =
            itemView.findViewById(
                R.id.item_data_consent_negative_button,
            )
        val blurView = itemView.findViewById<View>(R.id.item_data_consent_blur_view)

        fun bindData(viewModel: CombinedDataConsentItemViewModel) {
            title.text = viewModel.item.name
            positiveButton.text = viewModel.item.description?.positiveButtonText
            negativeButton.text = viewModel.item.description?.negativeButtonText
            body.setTextViewHTML(
                viewModel.item.description?.body
                    ?: "",
                viewModel.item,
                htmlTextClickListener,
            )
            addListeners(viewModel)
            updateIconVisibilities(viewModel)
            updateButtonsState(viewModel)
            positiveButton.isVisible = viewModel.item.showButtons
            negativeButton.isVisible = viewModel.item.showButtons
            blurView.isVisible = !viewModel.item.canEdit
            positiveButton.isEnabled = viewModel.item.canEdit
            negativeButton.isEnabled = viewModel.item.canEdit
        }

        private fun addListeners(viewModel: CombinedDataConsentItemViewModel) {
            positiveButton.setOnClickListener {
                if (viewModel.status != DataConsentStatus.ACCEPTED) {
                    listener.onAcceptConsent(viewModel.item)
                    viewModel.item.consentStatus = DataConsentStatus.ACCEPTED.value
                    updateIconVisibilities(viewModel)
                    updateButtonsState(viewModel)
                }
            }

            negativeButton.setOnClickListener {
                if (viewModel.status != DataConsentStatus.DECLINED) {
                    if (viewModel.item.masterConsent && viewModel.item.masterConsentUnEditable) {
                        listener.showMasterConsentNotEditable()
                    } else {
                        listener.showDeclineDialog(
                            viewModel,
                        ) {
                            viewModel.item.consentStatus = DataConsentStatus.DECLINED.value
                            updateIconVisibilities(viewModel)
                            updateButtonsState(viewModel)
                            listener.onConfirmDeclineConsent(viewModel.item)
                        }
                    }
                }
            }
        }

        private fun updateIconVisibilities(viewModel: CombinedDataConsentItemViewModel) {
            when (viewModel.status) {
                DataConsentStatus.ACCEPTED -> {
                    positiveButton.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_check_white,
                        0,
                        0,
                        0,
                    )
                    negativeButton.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
                }

                DataConsentStatus.DECLINED -> {
                    positiveButton.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
                    negativeButton.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_check_white,
                        0,
                        0,
                        0,
                    )
                }

                DataConsentStatus.UNSELECTED -> {
                    positiveButton.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
                    negativeButton.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
                }
            }
        }

        private fun updateButtonsState(viewModel: CombinedDataConsentItemViewModel) {
            positiveButton.isSelected = viewModel.item.isAccepted
            negativeButton.isSelected = viewModel.item.isDeclined
        }
    }

    interface CombinedDataConsentListener {
        fun showDeclineDialog(
            viewModel: CombinedDataConsentItemViewModel,
            updateButtonCheckListener: () -> Unit,
        )

        fun onAcceptConsent(consent: CombineDataConsent)

        fun onConfirmDeclineConsent(consent: CombineDataConsent)

        fun showMasterConsentNotEditable()
    }
}
