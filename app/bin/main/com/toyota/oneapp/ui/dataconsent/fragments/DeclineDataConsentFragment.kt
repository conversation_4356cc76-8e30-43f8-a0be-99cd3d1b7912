package com.toyota.oneapp.ui.dataconsent.fragments

import android.os.Bundle
import android.text.method.LinkMovementMethod
import androidx.fragment.app.activityViewModels
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDeclineDataConsentBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.dataconsent.activities.MasterDataConsentActivity
import com.toyota.oneapp.ui.dataconsent.viewmodels.MasterDataConsentViewModel

class DeclineDataConsentFragment : BaseDataBindingFragment<FragmentDeclineDataConsentBinding>() {
    private val mdcViewModel: MasterDataConsentViewModel by activityViewModels()

    override fun onViewBound(
        binding: FragmentDeclineDataConsentBinding,
        savedInstance: Bundle?,
    ) {
        binding.mdcViewModel = mdcViewModel
        binding.toolbar.setOnClickListener {
            (activity as MasterDataConsentActivity).navigateDeclineConfirmationToMasterDataConsent()
        }
        binding.itemDataConsentBody.movementMethod = LinkMovementMethod.getInstance()
    }

    override fun getLayout(): Int = R.layout.fragment_decline_data_consent
}
