package com.toyota.oneapp.ui.dataconsent

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityCdmaAcknowledgeBinding
import com.toyota.oneapp.model.subscription.VehicleSubscriptionAlert
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.GO_TO_APPOINTMENT_DETAIL
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

@AndroidEntryPoint
class VehicleSubscriptionAlertActivity : UiBaseActivity() {
    enum class CategoryConstants(
        val catogery: String,
    ) {
        GPS_OFFSET("GPS_OFFSET"),
        CDMA("3G_CDMA"),
    }

    private val viewModel: VehicleSubscriptionAlertViewModel by viewModels()

    private lateinit var dataBinding: ActivityCdmaAcknowledgeBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        dataBinding =
            DataBindingUtil.setContentView<ActivityCdmaAcknowledgeBinding>(
                this,
                R.layout.activity_cdma_acknowledge,
            )
        dataBinding.viewModel = viewModel
        observeBaseEvents(viewModel)
        dataBinding.lifecycleOwner = this
        dataBinding.executePendingBindings()
        performActivitySetup(dataBinding.toolbar)
        viewModel.apiCallStatus.observe(
            this,
            Observer {
                setResult(RESULT_OK)
                finish()
            },
        )
        viewModel.populateUI.observe(
            this,
            Observer {
                loadAlert(it)
            },
        )

        viewModel.populateSubscriptionAlert(
            intent.getStringExtra(ToyotaConstants.SELECTED_SUBSCRIPTION_NAMES),
            intent.getParcelableArrayListExtra(ToyotaConstants.VEHICLE_SUBSCRIPTION_ALERTS),
        )
    }

    private fun loadAlert(alert: VehicleSubscriptionAlert?) {
        if (alert != null) {
            if (CategoryConstants.GPS_OFFSET.catogery.equals(alert.category, true)) {
                dataBinding.toolbarProceed.visibility = View.VISIBLE
                dataBinding.toolbarProceed.setOnClickListener {
                    DialogUtil.showDialog(
                        this,
                        null,
                        getString(R.string.Common_are_you_sure),
                        getString(R.string.Common_yes),
                        getString(R.string.Common_back),
                        object : OnCusDialogInterface {
                            override fun onConfirmClick() {
                                viewModel.onAccepted("GPS_OFFSET")
                            }

                            override fun onCancelClick() {
                                finish()
                            }
                        },
                        false,
                    )
                }
                dataBinding.gpsOffSetLearnMoreTxt.setTextColor(
                    ContextCompat.getColor(this, R.color.colorAccent),
                )
                dataBinding.gpsOffSetLearnMoreTxt.text = getString(R.string.Common_learn_more)
                dataBinding.gpsOffSetLearnMoreTxt.setOnClickListener {
                    ToyUtil.openBrowser(this, ToyUtil.openPDF(alert.url), null)
                }
                dataBinding.gpsOffSetDescriptionTxt.text = alert.body
                dataBinding.btnContactDealer.text = getString(R.string.contact_dealer)
                dataBinding.btnContactDealer.setOnClickListener {
                    startActivity(
                        DashboardFlutterActivity.createIntent(
                            context = this,
                            screen = GO_TO_APPOINTMENT_DETAIL,
                        ),
                    )
                }
            } else if (CategoryConstants.CDMA.catogery.equals(alert.category, true)) {
                dataBinding.gpsOffSetLearnMoreTxt.setTextColor(
                    ContextCompat.getColor(this, R.color.almostBlack87),
                )
                initLegalInfo(alert.url)
                dataBinding.toolbarProceed.visibility = View.GONE
                dataBinding.ivGpsOffSetLogo.setImageResource(R.drawable.cdma_icon)
                dataBinding.gpsOffSetDescriptionTxt.text = alert.body
                dataBinding.gpsOffSetDescriptionTxt.text =
                    alert.body?.replace(
                        "<SELECTED_PRODUCTS>",
                        viewModel.selectedSubscriptionNames,
                        true,
                    )
                dataBinding.btnContactDealer.text =
                    getString(
                        R.string.DataConsents_agree_and_continue,
                    )
                dataBinding.btnContactDealer.setOnClickListener { viewModel.onAccepted("3G_CDMA") }
            }
            if (alert.image != null) {
                GlideUtil.loadImage(this, alert.image, dataBinding.ivGpsOffSetLogo)
            }
            dataBinding.gpsOffSetHeaderTxt.text = alert.title
            if (alert.subtitle == null) {
                dataBinding.gpsOffsetSubHeaderTxt.visibility = View.GONE
            } else {
                dataBinding.gpsOffsetSubHeaderTxt.visibility = View.VISIBLE
                dataBinding.gpsOffsetSubHeaderTxt.text = alert.subtitle
            }
        }
    }

    private fun initLegalInfo(url: String?) {
        val part1 = getString(R.string.cdma_3g_requirement_bottom)
        dataBinding.gpsOffSetLearnMoreTxt.text = part1
        val spannableString1: SpannableString =
            SpannableString(
                " " + getString(R.string.cdma_3g_requirement_link),
            )
        val totalSpannableString = SpannableString(spannableString1.toString())
        totalSpannableString.setSpan(
            StyleSpan(Typeface.BOLD),
            0,
            spannableString1.length,
            Spanned.SPAN_INCLUSIVE_INCLUSIVE,
        )
        totalSpannableString.setSpan(
            ForegroundColorSpan(getColor(R.color.colorAccent)),
            0,
            spannableString1.length,
            Spanned.SPAN_INCLUSIVE_INCLUSIVE,
        )
        val totalClickableSpan1: ClickableSpan =
            object : ClickableSpan() {
                override fun onClick(view: View) {
                    ToyUtil.openCustomChromeTab(activityContext, url)
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                }
            }
        totalSpannableString.setSpan(
            totalClickableSpan1,
            0,
            spannableString1.length,
            Spanned.SPAN_INCLUSIVE_INCLUSIVE,
        )
        dataBinding.gpsOffSetLearnMoreTxt.append(totalSpannableString)
        dataBinding.gpsOffSetLearnMoreTxt.movementMethod = LinkMovementMethod.getInstance()
    }
}

data class VehicleSubscriptionAlertsArguments(
    val subscriptions: String,
    val vehicleAlerts: ArrayList<VehicleSubscriptionAlert>,
)

sealed class VehicleSubscriptionAlertsResult {
    object Failure : VehicleSubscriptionAlertsResult()

    object Success : VehicleSubscriptionAlertsResult()
}

class VehicleSubscriptionAlertsContract : ActivityResultContract<VehicleSubscriptionAlertsArguments?, VehicleSubscriptionAlertsResult>() {
    override fun createIntent(
        context: Context,
        input: VehicleSubscriptionAlertsArguments?,
    ): Intent =
        Intent(context, VehicleSubscriptionAlertActivity::class.java).apply {
            putParcelableArrayListExtra(
                ToyotaConstants.VEHICLE_SUBSCRIPTION_ALERTS,
                input?.vehicleAlerts,
            )
            putExtra(ToyotaConstants.SELECTED_SUBSCRIPTION_NAMES, input?.subscriptions)
        }

    override fun parseResult(
        resultCode: Int,
        intent: Intent?,
    ): VehicleSubscriptionAlertsResult =
        when (resultCode) {
            Activity.RESULT_OK -> VehicleSubscriptionAlertsResult.Success
            else -> VehicleSubscriptionAlertsResult.Failure
        }
}
