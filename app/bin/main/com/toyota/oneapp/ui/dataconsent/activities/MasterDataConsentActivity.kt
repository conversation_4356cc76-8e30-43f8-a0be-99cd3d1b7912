package com.toyota.oneapp.ui.dataconsent.activities

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.navigation.fragment.NavHostFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.ActivityMasterDataConsentBinding
import com.toyota.oneapp.model.reconsent.ReConsentData
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModelNavigationEvent
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.dataconsent.viewmodels.MDCEvent
import com.toyota.oneapp.ui.dataconsent.viewmodels.MasterDataConsentViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MasterDataConsentActivity : UiBaseActivity() {
    private lateinit var binding: ActivityMasterDataConsentBinding
    private val masterDataConsentViewModel: MasterDataConsentViewModel by viewModels()
    private lateinit var navHostFragment: NavHostFragment
    private var reConsentData: ReConsentData? = null
    private var vehicle: VehicleInfo? = null

    @Inject lateinit var analyticsLogger: AnalyticsLogger

    companion object {
        const val EXTRA_RE_CONSENT_DATA = "EXTRA_RE_CONSENT_DATA"
        const val EXTRA_DECLINE_CONSENT_SUCCESS = "EXTRA_DECLINE_CONSENT_SUCCESS"
        const val EXTRA_VEHICLE = "EXTRA_SELECTED_VIN"
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(masterDataConsentViewModel)
        reConsentData = intent.getParcelableExtra(EXTRA_RE_CONSENT_DATA)
        vehicle = intent.getParcelableExtra(EXTRA_VEHICLE)

        // binding
        binding = DataBindingUtil.setContentView(this, R.layout.activity_master_data_consent)
        binding.lifecycleOwner = this

        // nav-graph
        navHostFragment = supportFragmentManager.findFragmentById(R.id.mdc_nav_host_fragment) as NavHostFragment
        val graph =
            navHostFragment.navController.navInflater.inflate(
                R.navigation.master_data_consent_nav_graph,
            )
        navHostFragment.navController.graph = graph

        // Events
        observeEvents()
    }

    private fun observeEvents() {
        masterDataConsentViewModel.navigationEvent.observe(this) { it ->
            when (it) {
                MDCEvent.ACTION_SEND_RESULT -> {
                    sendResultData()
                }
                MDCEvent.ACTION_DECLINE_SUCCESS_SEND_RESULT -> {
                    sendResultData(declineSuccess = true)
                }
                MDCEvent.ACTION_MASTER_DATA_CONSENT_TO_DECLINE_CONFIRMATION -> {
                    navHostFragment.navController.navigate(
                        R.id.action_masterDataConsent_to_declineDataConsent,
                    )
                }
                MDCEvent.ACTION_DECLINE_CONFIRMATION_TO_MASTER_DATA_CONSENT -> {
                    navigateDeclineConfirmationToMasterDataConsent()
                }
                MDCEvent.ACTION_DECLINE_CONFIRMATION_TO_WIFI_ERROR -> {
                    navigateDeclineConfirmationToWifiError()
                }
                MDCEvent.ACTION_WAIVE_SUBSCRIPTION -> {
                    // waive consent
                    vehicle?.let {
                        masterDataConsentViewModel.declineMasterConsent(it)
                    }
                }
                MDCEvent.ACTION_WIFI_ERROR_TO_MASTER_DATA_CONSENT -> {
                    navigateWifiErrorToMasterDataConsent()
                }
                MDCEvent.ACTION_MASTER_DATA_CONSENT_ACCEPT_CLICKED -> {
                    vehicle?.let {
                        masterDataConsentViewModel.acceptMasterDataConsent(it)
                    }
                }
            }
        }
        masterDataConsentViewModel.progressEvents.observe(
            this,
            Observer {
                when (it) {
                    is BaseViewModelNavigationEvent.ShowProgress -> showProgressDialog()
                    is BaseViewModelNavigationEvent.HideProgress -> hideProgressDialog()
                    else -> {}
                }
            },
        )

        masterDataConsentViewModel.dataConsentDescription.observe(
            this,
            Observer { combinedDataConsent ->
                var mdcBody: String? = null
                var declineMdcTitle: String? = null
                var declineMdcBody: String? = null

                combinedDataConsent?.description?.let {
                    mdcBody = it.declinePayload?.body
                    declineMdcTitle = it.declinePayload?.title
                    declineMdcBody = it.declinePayload?.body
                }
                if (mdcBody?.isNotEmpty() == true && declineMdcTitle?.isNotEmpty() == true && declineMdcBody?.isNotEmpty() == true) {
                    if (navHostFragment.navController.currentDestination?.id != R.id.action_previewDataConsent_to_masterDataConsent) {
                        navHostFragment.navController.navigate(
                            R.id.action_previewDataConsent_to_masterDataConsent,
                        )
                    }
                }
            },
        )
    }

    // Handle Navigation
    fun navigateToMasterDataConsent() {
        analyticsLogger.logEvent(AnalyticsEvent.MASTER_CONSENT_CONTINUE_CTA)
        vehicle?.let { vehicleInfo ->
            reConsentData?.consentId?.let {
                masterDataConsentViewModel.fetchCombinedDataConsents(vehicleInfo, consentId = it)
            }
        }
    }

    fun navigateDeclineConfirmationToMasterDataConsent() {
        navHostFragment.navController.navigate(R.id.action_declineDataConsent_to_masterDataConsent)
    }

    private fun navigateDeclineConfirmationToWifiError() {
        navHostFragment.navController.navigate(R.id.action_declineDataConsent_to_wifiErrorConsent)
    }

    private fun navigateWifiErrorToMasterDataConsent() {
        navHostFragment.navController.navigate(R.id.action_wifiError_to_masterDataConsent)
    }

    // Send Result Data
    private fun sendResultData(declineSuccess: Boolean = false) {
        val intent = Intent()
        intent.putExtra(EXTRA_RE_CONSENT_DATA, reConsentData)
        intent.putExtra(EXTRA_DECLINE_CONSENT_SUCCESS, declineSuccess)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    // Disable back press
    override fun onBackPressed() {
        // back press not allowed
    }
}
