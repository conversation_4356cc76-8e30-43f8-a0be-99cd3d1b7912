package com.toyota.oneapp.ui.dataconsent.viewmodels

import android.view.View
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class EditDataConsentsViewModel
    @Inject
    constructor(
        private val languageManager: LanguageManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        companion object {
            private const val FILE_PATH = "file:///android_asset/tac/"
            private const val FILE_NAME = "/terms_and_conditions.html"

            private const val PR_CODE = "en_PR"
            private const val PR_ES_CODE = "es_PR"
            private const val CA_FR_CODE = "fr_CA"
            private const val CA_CODE = "en_CA"
            private const val US_CODE = "en_US"
        }

        fun getTermsUrl(vehicleRegion: String) = FILE_PATH + getLocaleString(vehicleRegion) + FILE_NAME

        private fun getLocaleString(vehicleRegion: String): String {
            val langCode = languageManager.getCurrentLanguage()
            return when (vehicleRegion) {
                ToyotaConstants.REGION_PR -> {
                    if ("es".equals(langCode, true)) {
                        PR_ES_CODE
                    } else {
                        PR_CODE
                    }
                }
                ToyotaConstants.REGION_CA -> {
                    if ("fr".equals(langCode, true)) {
                        CA_FR_CODE
                    } else {
                        CA_CODE
                    }
                }
                else -> US_CODE
            }
        }

        val showAppLinkText = MutableLiveData<Boolean>()

        var vehicleInfo: VehicleInfo? = null
            set(value) {
                field = value
                showAppLinkText.value = value?.isFeatureEnabled(Feature.XCAPP)
            }

        fun onAppLicenseClick(view: View) {
            vehicleInfo?.let {
                analyticsLogger.logEvent(AnalyticsEvent.Users_DataPrivacyPortal_AppLicenseAgr)
                ToyUtil.openBrowser(
                    view.context,
                    getTermsUrl(
                        it.region
                            ?: ToyotaConstants.REGION_US,
                    ),
                    view.context.getString(R.string.AccountSettings_App_License_Agreement),
                )
            }
        }

        fun onTermsClick(view: View) {
            vehicleInfo?.let {
                analyticsLogger.logEvent(AnalyticsEvent.Users_DataPrivacyPortal_CSTermsOfUse)
                ToyUtil.openCustomChromeTab(
                    view.context,
                    ToyUtil.getVehicleTerms(
                        it.brand,
                        it.isCY17,
                        it.region,
                    ),
                )
            }
        }

        fun onPrivacyLinkClick(view: View) {
            vehicleInfo?.let {
                analyticsLogger.logEvent(AnalyticsEvent.Users_DataPrivacyPortal_CSPrivacyNotice)
                ToyUtil.openCustomChromeTab(
                    view.context,
                    ToyUtil.getVehiclePrivacy(it.brand, it.region),
                )
            }
        }
    }
