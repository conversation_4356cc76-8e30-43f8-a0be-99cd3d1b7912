package com.toyota.oneapp.ui.dataconsent.viewmodels

import androidx.databinding.ObservableBoolean
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.account.AcknowledgeConsentRequest
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.combineddataconsent.CombinedDataConsentPayload
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
open class BannerDataConsentViewModel
    @Inject
    constructor(
        protected val preferenceModel: OneAppPreferenceModel,
        protected val repository: CombinedDataConsentRepository,
        protected val dateUtil: DateUtil,
        protected val applicationData: ApplicationData,
    ) : BaseViewModel() {
        var vehicle: VehicleInfo? = null
        var eligibleConsent: String = ""
        var isSubscriptionFlow: Boolean = false
        private var dataConsentPayload: CombinedDataConsentPayload? = null
        private var _dataConsentDescription = MutableLiveData<CombineDataConsent?>()

        private var _acceptedOrDeclinedConsents = SingleLiveEvent<List<ConsentRequestItem>?>()
        val acceptedOrDeclinedConsents: LiveData<List<ConsentRequestItem>?>
            get() = _acceptedOrDeclinedConsents

        private var _consent = MutableLiveData<CombineDataConsent>()
        val consentData get() = _consent

        private var _onDeclined = MutableLiveData<CombineDataConsent>()
        val onDeclined get() = _onDeclined

        private var _onSuccess = MutableLiveData<String>()
        val onSuccess get() = _onSuccess

        var showDecline = ObservableBoolean(false)

        fun fetchCombinedDataConsents(dataConsent: CombineDataConsent?) {
            if (dataConsent != null) {
                _consent.postValue(dataConsent)
            } else {
                vehicle?.let {
                    viewModelScope.launch {
                        showProgress()
                        val resource =
                            repository.getCombinedDataConsent(
                                vin = it.vin,
                                brand = it.brand,
                                gen = it.generation,
                                region = it.region,
                                productCodes = arrayListOf(),
                                eligibleConsent = eligibleConsent,
                                flowType = CombinedDataConsentRepository.DataConsentFlowType.BANNER.value,
                            )
                        hideProgress()
                        when (resource) {
                            is Resource.Success -> {
                                dataConsentPayload = resource.data?.payload
                                resource.data?.payload?.eligibleConsents?.firstOrNull().let {
                                    _consent.postValue(it)
                                }
                            }
                            is Resource.Failure -> {
                                _dataConsentDescription.postValue(null)
                            }
                            else -> {}
                        }
                    }
                }
            }
        }

        fun onAcceptClicked() {
            if (isSubscriptionFlow && _consent.value != null) {
                _acceptedOrDeclinedConsents.value =
                    consentRequestItems(
                        consentData.value,
                        ToyotaConstants.CONSENT_ACCEPTED,
                    )
            } else {
                updateConsent(ToyotaConstants.CONSENT_ACCEPTED)
            }
        }

        fun onDeclineClicked() {
            _onDeclined.postValue(consentData.value)
        }

        fun onConfirmDeclineClicked() {
            if (isSubscriptionFlow) {
                _acceptedOrDeclinedConsents.value =
                    consentRequestItems(
                        consentData.value,
                        ToyotaConstants.CONSENT_DECLINED,
                    )
            } else {
                updateConsent(ToyotaConstants.CONSENT_DECLINED)
            }
        }

        private fun updateConsent(status: String) {
            vehicle?.let { vehicle ->
                viewModelScope.launch {
                    showProgress()
                    val resource =
                        repository.acknowledgeConsent(
                            vin = vehicle.vin,
                            brand = vehicle.brand,
                            region = vehicle.region ?: "",
                            eligibleConsent = eligibleConsent,
                            body = getAcknowledgeConsentBody(vehicle, consentData.value, status),
                        )
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            _onSuccess.postValue(status)
                        }
                        is Resource.Failure -> {
                            showErrorMessage(resource.error?.message)
                        }
                        else -> {}
                    }
                }
            }
        }

        private fun getAcknowledgeConsentBody(
            vehicle: VehicleInfo,
            consentItems: CombineDataConsent?,
            status: String,
        ): AcknowledgeConsentRequest =
            AcknowledgeConsentRequest(
                vin = vehicle.vin,
                guid = preferenceModel.getGuid(),
                consents = consentRequestItems(consentItems, status),
            )

        companion object {
            const val TAG = "CombinedDataConsentViewModel"
        }

        private fun consentRequestItems(
            consentItems: CombineDataConsent?,
            status: String,
        ): List<ConsentRequestItem> =
            listOf(
                ConsentRequestItem(
                    consentId = consentItems?.consentId ?: "",
                    versionId = consentItems?.version ?: "",
                    status = status,
                    category = consentItems?.category ?: "",
                ),
            )

        fun showDeclineScreen(show: Boolean) {
            showDecline.set(show)
        }
    }
