package com.toyota.oneapp.ui.dataconsent.activities

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityBannerConsentSuccesDeclineBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.dataconsent.viewmodels.BannerConsentSuccessDeclineViewModel
import com.toyota.oneapp.util.IntentUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
open class BannerConsentSuccessDeclineActivity : UiBaseActivity() {
    companion object {
        const val TAG = "BannerConsentSuccessDeclineActivity"
        private const val EXTRA_CONSENT_ACKNOWLEDGE = "EXTRA_CONSENT_ACKNOWLEDGE"

        fun getIntent(
            context: Context,
            bannerConsentSuccessDecline: BannerConsentSuccessDecline,
        ): Intent =
            Intent(context, BannerConsentSuccessDeclineActivity::class.java).apply {
                putExtra(EXTRA_CONSENT_ACKNOWLEDGE, bannerConsentSuccessDecline)
            }

        enum class BannerConsentSuccessDecline(
            val titleResId: Int,
            val descriptionLineResId: Int,
            val imageResId: Int,
        ) {
            MARKETING_OPT_IN_ACCEPTED(
                R.string.Marketing_Consent_Acknowledge_Header,
                R.string.Marketing_Consent_Accepted_Description,
                R.drawable.ic_subscription_success,
            ),
            MARKETING_OPT_IN_DECLINED(
                R.string.Marketing_Consent_Acknowledge_Header,
                R.string.Marketing_Consent_Declined_Description,
                R.drawable.ic_decline_marketing,
            ),
            AMBER_ALERT_ACCEPTED(
                R.string.Amber_Alert_Accepted_Header,
                R.string.Amber_Alert_Accepted_Description,
                R.drawable.ic_accept_amber_alert,
            ),
            AMBER_ALERT_DECLINED(
                R.string.Amber_Alert_Declined_Header,
                R.string.Amber_Alert_Declined_Description,
                R.drawable.ic_decline_amber_alert,
            ),
        }
    }

    protected open val viewModel: BannerConsentSuccessDeclineViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        val dataBinding =
            DataBindingUtil.setContentView<ActivityBannerConsentSuccesDeclineBinding>(
                this,
                R.layout.activity_banner_consent_succes_decline,
            )
        dataBinding.lifecycleOwner = this
        dataBinding.executePendingBindings()
        observeBaseEvents(viewModel)
        viewModel.setData(
            intent.getSerializableExtra(EXTRA_CONSENT_ACKNOWLEDGE) as BannerConsentSuccessDecline,
        )
        viewModel.backToDashboard.observe(this) {
            backToDashboard()
        }
        viewModel.data.observe(this) {
            dataBinding.image.setImageResource(it.imageResId)
            dataBinding.header.setText(it.titleResId)
            dataBinding.body.setText(it.descriptionLineResId)
        }
        dataBinding.backToDashboard.setOnClickListener {
            viewModel.onBackToDashboardClicked()
        }
    }

    private fun backToDashboard() {
        startActivity(IntentUtil.getOADashBoardIntent(context = this, isDashboardRefresh = true))
        finish()
    }

    override fun onBackPressed() {
        super.onBackPressed()
        backToDashboard()
    }
}
