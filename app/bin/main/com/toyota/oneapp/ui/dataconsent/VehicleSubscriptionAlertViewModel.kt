package com.toyota.oneapp.ui.dataconsent

import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.subscription.VehicleSubscriptionAlert
import com.toyota.oneapp.network.api.manager.SubscriptionAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.CancelSubscriptionStatus
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class VehicleSubscriptionAlertViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val subscriptionManager: SubscriptionAPIManager,
    ) : BaseViewModel() {
        private var vehicleAlerts: ArrayList<VehicleSubscriptionAlert> = arrayListOf()
        var selectedSubscriptionNames: String = ""
        var currentIndex = 0
        val apiCallStatus: MutableLiveData<CancelSubscriptionStatus> = MutableLiveData()
        val populateUI: MutableLiveData<VehicleSubscriptionAlert> = MutableLiveData()

        fun populateSubscriptionAlert(
            selectedSubscriptionNames: String?,
            vehicleAlerts: ArrayList<VehicleSubscriptionAlert>?,
        ) {
            this.vehicleAlerts = vehicleAlerts ?: arrayListOf()
            this.selectedSubscriptionNames = selectedSubscriptionNames ?: ""
            iterateThroughList()
        }

        private fun iterateThroughList() {
            if (vehicleAlerts.size - 1 >= currentIndex) {
                populateUI.postValue(vehicleAlerts[currentIndex])
                currentIndex++
            } else {
                apiCallStatus.postValue(CancelSubscriptionStatus())
            }
        }

        fun onAccepted(category: String) {
            applicationData.getSelectedVehicle()?.let {
                showProgress()
                subscriptionManager.sendGpsOffSetRequest(
                    it.vin,
                    category,
                    object : BaseCallback<BaseResponse>() {
                        override fun onComplete() {
                            hideProgress()
                            iterateThroughList()
                        }
                    },
                )
            }
        }
    }
