package com.toyota.oneapp.ui.dataconsent.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.setFragmentResult
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentServiceDeactivationBinding
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.enable.adapter.EnableTrialSubscriptionAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ConsentsDeclineConfirmationDialogFragment :
    DialogFragment(),
    EnableTrialSubscriptionAdapter.OnItemClickListener {
    companion object {
        const val TAG = "ConsentsDeclineConfirmationDialogFragment"
        private const val PAID_SUBSCRIPTIONS = "PAID_SUBSCRIPTIONS"
        private const val TRIAL_SUBSCRIPTIONS = "TRIAL_SUBSCRIPTIONS"
        private const val VEHICLE_INFO = "VEHICLE_INFO"
        const val REQUEST_KEY_CONSENT_DECLINE_CONFIRMATION_DIALOG = "REQUEST_KEY_CONSENT_DECLINE_CONFIRMATION_DIALOG"
        const val EXTRA_IS_CONFIRM = "EXTRA_IS_CONFIRM"

        fun getInstance(
            paidSubscriptions: Array<SubscriptionV2>,
            trialSubscriptions: Array<SubscriptionV2>,
            vehicleInfo: VehicleInfo,
        ): ConsentsDeclineConfirmationDialogFragment {
            val fragment = ConsentsDeclineConfirmationDialogFragment()
            fragment.arguments =
                bundleOf(
                    PAID_SUBSCRIPTIONS to paidSubscriptions,
                    TRIAL_SUBSCRIPTIONS to trialSubscriptions,
                    VEHICLE_INFO to vehicleInfo,
                )
            return fragment
        }
    }

    private lateinit var paidSubscriptions: Array<SubscriptionV2>
    private lateinit var trialSubscriptions: Array<SubscriptionV2>
    private lateinit var vehicleInfo: VehicleInfo

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, R.style.AppTheme_FullScreenDialog)
    }

    override fun onStart() {
        super.onStart()
        val width = ViewGroup.LayoutParams.MATCH_PARENT
        val height = ViewGroup.LayoutParams.MATCH_PARENT
        dialog?.window?.setLayout(width, height)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        val binding = FragmentServiceDeactivationBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        initializeExtras()
        populateView(binding)
        addListeners(binding)
        return binding.root
    }

    private fun initializeExtras() {
        paidSubscriptions = requireArguments().getParcelableArray(PAID_SUBSCRIPTIONS) as Array<SubscriptionV2>
        trialSubscriptions = requireArguments().getParcelableArray(TRIAL_SUBSCRIPTIONS) as Array<SubscriptionV2>
        vehicleInfo = requireArguments().getParcelable(VEHICLE_INFO)!! // did null check already before passing into arguments.
    }

    private fun populateView(binding: FragmentServiceDeactivationBinding) {
        if (trialSubscriptions.isNotEmpty()) {
            binding.tvTrialService.visibility = View.VISIBLE
            val trialAdapter =
                EnableTrialSubscriptionAdapter(
                    trialSubscriptions.toList(),
                    vehicleInfo,
                    this,
                )
            binding.rvTrialSubscriptions.run {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = trialAdapter
                addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
            }
        }

        if (paidSubscriptions.isNotEmpty()) {
            binding.tvPaidServices.visibility = View.VISIBLE
            val paidAdapter =
                EnableTrialSubscriptionAdapter(
                    paidSubscriptions.toList(),
                    vehicleInfo,
                    this,
                )
            binding.rvPaidSubscriptions.run {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = paidAdapter
                addItemDecoration(DividerItemDecoration(requireContext(), LinearLayout.VERTICAL))
            }
        }
    }

    private fun addListeners(binding: FragmentServiceDeactivationBinding) {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }
        binding.btnConfirm.setOnClickListener {
            onConfirm()
        }
    }

    private fun onConfirm() {
        setFragmentResult(
            REQUEST_KEY_CONSENT_DECLINE_CONFIRMATION_DIALOG,
            bundleOf(
                EXTRA_IS_CONFIRM to true,
            ),
        )
        dismiss()
    }

    override fun onSubscriptionServiceItemClick(subscription: SubscriptionV2) {
        // Not implemented- Click is not allowed in this design
    }
}
