package com.toyota.oneapp.ui.dataconsent.fragments

import android.os.Bundle
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentPreviewDataConsentBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.dataconsent.activities.MasterDataConsentActivity

class PreviewDataConsentFragment : BaseDataBindingFragment<FragmentPreviewDataConsentBinding>() {
    override fun onViewBound(
        binding: FragmentPreviewDataConsentBinding,
        savedInstance: Bundle?,
    ) {
        binding.mdcContinue.setOnClickListener {
            (activity as MasterDataConsentActivity).navigateToMasterDataConsent()
        }
    }

    override fun getLayout(): Int = R.layout.fragment_preview_data_consent
}
