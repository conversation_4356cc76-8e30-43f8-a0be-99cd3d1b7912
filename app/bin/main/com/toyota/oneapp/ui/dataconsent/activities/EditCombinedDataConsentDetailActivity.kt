package com.toyota.oneapp.ui.dataconsent.activities

import android.app.Activity
import android.content.Intent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.extensions.setTextViewHTML
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.ui.dataconsent.viewmodels.CombinedDataConsentItemViewModel
import com.toyota.oneapp.ui.dataconsent.viewmodels.EditCombinedDataConsentViewModel
import com.toyota.oneapp.ui.widget.AddressView
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.ToyotaConstants.Companion.ALL_DATA_CONSENT_ITEMS
import com.toyota.oneapp.util.ToyotaConstants.Companion.DATA_CONSENT_KEY
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EditCombinedDataConsentDetailActivity : CombinedDataConsentActivity() {
    private var dataConsent: CombineDataConsent? = null
    override val viewModel: EditCombinedDataConsentViewModel by viewModels()
    private var allConsents: List<CombineDataConsent>? = null

    override fun fetchDataConsents() {
        dataConsent = intent.getParcelableExtra(DATA_CONSENT_KEY)
        allConsents = intent.getParcelableArrayListExtra(ALL_DATA_CONSENT_ITEMS)
        dataConsent?.let {
            viewModel.updateDataConsentItems(listOf(it))
        }
    }

    override fun initViews() {
        super.initViews()
        hideShowConfirmButton(false)
        viewModel.onConsentDeclined.observe(
            this,
            Observer {
                setResultBack()
            },
        )
        viewModel.paymentRecords.observe(
            this,
            Observer {
                if (it != null) showAddressView(it)
            },
        )
        viewModel.consentUpdatedData.observe(
            this,
            Observer {
                startActivity(
                    IntentUtil.getOADashBoardIntent(context = this, isDashboardRefresh = true),
                )
            },
        )
    }

    override fun enableDisableConfirmButton(enabled: Boolean) {
        // not need to show the confirm and continue button in this screen
    }

    override fun hideShowConfirmButton(show: Boolean) {
        binding.dataConsentConfirmButton.isVisible = false
    }

    override fun onAcceptConsent(consent: CombineDataConsent) {
        dataConsent?.consentStatus = ToyotaConstants.CONSENT_ACCEPTED
        setResultBack()
    }

    override fun onConfirmDeclineConsent(consent: CombineDataConsent) {
        dataConsent
            ?.apply {
                consentStatus = ToyotaConstants.CONSENT_DECLINED
            }?.also {
                viewModel.onDataConsentDecline(it)
            }
    }

    private fun setResultBack() {
        val intent =
            Intent().apply {
                putExtra(DATA_CONSENT_KEY, dataConsent)
            }
        setResult(RESULT_OK, intent)
        finish()
    }

    private val getResult =
        registerForActivityResult(StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) setResultBack()
        }

    /*override fun showDeclineDialog(
            itemViewModel: CombinedDataConsentItemViewModel,
            updateButtonCheckListener: () -> Unit
    ) {
        if (itemViewModel.item.masterConsent) {
            showMasterConsentDeclineView(itemViewModel, updateButtonCheckListener)
        } else
            super.showDeclineDialog(itemViewModel, updateButtonCheckListener)
    }*/

    private fun showMasterConsentDeclineView(
        itemViewModel: CombinedDataConsentItemViewModel,
        updateButtonCheckListener: () -> Unit,
    ) {
        val declinePayload = itemViewModel.item.description?.declinePayload
        findViewById<View>(R.id.layout_decline_master_consent_view).visibility = View.VISIBLE
        findViewById<TextView>(R.id.layout_master_consent_decline_title).text = declinePayload?.title
        findViewById<TextView>(R.id.layout_master_consent_decline_body).setTextViewHTML(
            html = declinePayload?.body ?: "",
            item = itemViewModel.item,
            listener = this,
        )
        findViewById<AddressView>(R.id.layout_master_consent_address_view).isVisible = false
        if (viewModel.vehicle?.isPaidServicesAvailable == true) viewModel.checkRefund()
        findViewById<View>(R.id.decline_confirm_and_continue_button).setOnClickListener {
            allConsents?.forEach { consent ->
                consent.consentStatus = ToyotaConstants.CONSENT_DECLINED
            }
            viewModel.updateConsentWhenMasterConsentIsDeclined(allConsents)
        }
        GlideUtil.loadImage(
            this,
            viewModel.vehicle?.image,
            GlideUtil.CropTransparentTransform(),
            R.drawable.image_not_found,
            findViewById<ImageView>(R.id.iv_car_image),
        )
    }

    private fun showAddressView(paymentRecord: PaymentRecord) {
        findViewById<AddressView>(R.id.layout_master_consent_address_view).address =
            object : AddressView.Address {
                override var line1: String? = paymentRecord.creditCardAddress1
                override var line2: String? = null
                override var city: String? = paymentRecord.creditCardCity
                override var stateCode: String? = paymentRecord.creditCardState
                override var zipCode: String? = paymentRecord.creditCardPostalCode
                override var countryCode: String? = paymentRecord.creditCardCountry
            }
        findViewById<AddressView>(R.id.layout_master_consent_address_view).isVisible = true
    }

    override fun onBackPressed() {
        if (findViewById<View>(R.id.layout_decline_master_consent_view).visibility == View.VISIBLE) {
            findViewById<View>(R.id.layout_decline_master_consent_view).visibility = View.GONE
        } else {
            super.onBackPressed()
        }
    }
}
