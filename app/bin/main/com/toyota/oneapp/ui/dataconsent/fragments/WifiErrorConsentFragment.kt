package com.toyota.oneapp.ui.dataconsent.fragments

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityActiveWifiSubscriptionExistBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.dataconsent.viewmodels.MasterDataConsentViewModel
import com.toyota.oneapp.ui.garage.wifiSubscription.ActiveWifiSubscriptionExistActivity
import com.toyota.oneapp.ui.garage.wifiSubscription.ActiveWifiSubscriptionExistViewModel
import com.toyota.oneapp.util.ToyUtil

class WifiErrorConsentFragment : BaseDataBindingFragment<ActivityActiveWifiSubscriptionExistBinding>() {
    private val activeWifiSubscriptionExistViewModel: ActiveWifiSubscriptionExistViewModel by activityViewModels()
    private val masterDataConsentViewModel: MasterDataConsentViewModel by activityViewModels()

    override fun onViewBound(
        binding: ActivityActiveWifiSubscriptionExistBinding,
        savedInstance: Bundle?,
    ) {
        activeWifiSubscriptionExistViewModel.showAppBar.set(true)
        binding.contactSupportCta.text = resources.getString(R.string.contact_at_t_support)
        binding.contactSupportCta.setOnClickListener {
            activeWifiSubscriptionExistViewModel.applicationData.getSelectedVehicle()?.let {
                val intent = Intent()
                val supportNumber =
                    ToyUtil.getPhoneNO(
                        activityContext,
                        intent.getStringExtra(ActiveWifiSubscriptionExistActivity.VEHICLE_REGION),
                        it.brand,
                    )
                ToyUtil.phoneCall(activityContext, supportNumber)
            }
        }

        binding.toolbar.setOnClickListener {
            masterDataConsentViewModel.onWifiErrorBackActionClicked()
        }
    }

    override fun getLayout(): Int = R.layout.activity_active_wifi_subscription_exist
}
