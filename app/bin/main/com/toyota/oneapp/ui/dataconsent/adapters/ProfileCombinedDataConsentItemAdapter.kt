package com.toyota.oneapp.ui.dataconsent.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.DataBindingAdapter
import com.toyota.oneapp.databinding.ItemDataConsentProfileBinding
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.util.DateUtil
import java.util.concurrent.Executor

class ProfileCombinedDataConsentItemAdapter(
    executor: Executor,
    private val onDataConsentClick: (CombineDataConsent) -> Unit,
) : DataBindingAdapter<CombineDataConsent, ItemDataConsentProfileBinding>(
        executor,
        ConsentDataItemUtils(),
    ) {
    private var editing: Boolean = false
    var dateUtil: DateUtil? = null

    override fun createBinding(
        parent: ViewGroup,
        viewType: Int,
    ): ItemDataConsentProfileBinding =
        DataBindingUtil.inflate(
            LayoutInflater.from(parent.context),
            R.layout.item_data_consent_profile,
            parent,
            false,
        )

    override fun bind(
        binding: ItemDataConsentProfileBinding,
        position: Int,
    ) {
        val item = getItem(position)
        binding.root.isEnabled =
            if (editing) {
                item.consentEditable && !item.masterConsent
            } else {
                true
            }
        binding.root.setOnClickListener {
            onDataConsentClick.invoke(
                item.apply {
                    showButtons = editing
                },
            )
        }
        binding.tvDataConsentTitle.text = item.name
        val context = binding.tvStatus.context
        binding.tvStatus.text = item.getStatusText(context)
        val color =
            if (item.isAccepted) {
                R.color.mediumGreen
            } else {
                R.color.pale_red
            }
        binding.tvStatus.setTextColor(context.resources.getColor(color))
        binding.tvConsentUpdateDate.isInvisible = editing
        binding.tvConsentUpdateDate.text = item.getConsentUpdatedDate(context, dateUtil)
        binding.ivArrow.contentDescription = item.name
        binding.ivArrow.isVisible = editing
        binding.blurView.isVisible = item.consentEditable == false && editing
        binding.blurView.isSaveEnabled = item.consentEditable == false
    }

    fun makeEditable(editing: Boolean) {
        this.editing = editing
        notifyDataSetChanged()
    }
}

class ConsentDataItemUtils : DiffUtil.ItemCallback<CombineDataConsent>() {
    override fun areItemsTheSame(
        oldItem: CombineDataConsent,
        newItem: CombineDataConsent,
    ): Boolean = oldItem.consentId == newItem.consentId

    override fun areContentsTheSame(
        oldItem: CombineDataConsent,
        newItem: CombineDataConsent,
    ): Boolean =
        oldItem.consentId == newItem.consentId &&
            oldItem.consentAcknowledgedDate == newItem.consentAcknowledgedDate &&
            oldItem.statusValue == newItem.statusValue &&
            oldItem.consentEditable == newItem.consentEditable
}
