package com.toyota.oneapp.ui.dataconsent.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentResultListener
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityCombinedDataConsentBinding
import com.toyota.oneapp.extensions.HTMLTextClickListener
import com.toyota.oneapp.extensions.setTextViewHTML
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.subscription.SubscriptionGetPayload
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.AddedVehiclesActivity
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.dataconsent.adapters.CombinedDataConsentAdapter
import com.toyota.oneapp.ui.dataconsent.fragments.ConsentsDeclineConfirmationDialogFragment
import com.toyota.oneapp.ui.dataconsent.viewmodels.CombinedDataConsentItemViewModel
import com.toyota.oneapp.ui.dataconsent.viewmodels.CombinedDataConsentViewModel
import com.toyota.oneapp.ui.garage.subscriptionV2.enabletrialsubscription.declinetrialconfirmation.DeclineTrialConfirmationDialogFragment
import com.toyota.oneapp.ui.garage.wifiSubscription.ActiveWifiSubscriptionExistActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import java.io.Serializable
import javax.inject.Inject

@AndroidEntryPoint
open class CombinedDataConsentActivity :
    UiBaseActivity(),
    CombinedDataConsentAdapter.CombinedDataConsentListener,
    HTMLTextClickListener {
    companion object {
        const val TAG = "CombinedDataConsentActivity"
        private const val EXTRA_ELIGIBLE_CONSENTS = "EXTRA_ELIGIBLE_CONSENTS"
        private const val EXTRA_IS_GET_CONSENT_INFORMATION_AS_RESULT = "EXTRA_IS_GET_SELECTED_PAYMENT_INFORMATION_AS_RESULT"

        fun getIntent(
            context: Context,
            vehicle: VehicleInfo,
            isPaidFlow: Boolean,
            trialSubscriptions: Array<SubscriptionV2>?,
            subscriptionGetPayload: SubscriptionGetPayload? = null,
            eligibleConsents: CombinedDataConsentRepository.DataConsentFlag? = null,
            isGetConsentInfoAsResult: Boolean = false,
        ): Intent =
            getIntent(
                context,
                vehicle,
                isPaidFlow,
                null,
                trialSubscriptions,
                subscriptionGetPayload,
                eligibleConsents,
                isGetConsentInfoAsResult,
            )

        fun getIntent(
            context: Context,
            vehicle: VehicleInfo,
            isPaidFlow: Boolean,
            paidSubscriptions: Array<SubscriptionV2>?,
            trialSubscriptions: Array<SubscriptionV2>?,
            subscriptionGetPayload: SubscriptionGetPayload? = null,
            eligibleConsents: CombinedDataConsentRepository.DataConsentFlag? = null,
            isGetConsentInfoAsResult: Boolean = false,
        ): Intent {
            val intent =
                Intent(context, CombinedDataConsentActivity::class.java).apply {
                    putExtra(ToyotaConstants.VEHICLE, vehicle)
                    putExtra(ToyotaConstants.PAID_FLOW, isPaidFlow)
                    putExtra(ToyotaConstants.PAID_SUBSCRIPTIONS, paidSubscriptions)
                    putExtra(ToyotaConstants.TRIAL_SUBSCRIPTIONS, trialSubscriptions)
                    putExtra(ToyotaConstants.SUBSCRIPTION_GET_PAYLOAD, subscriptionGetPayload)
                    eligibleConsents?.let { putExtra(EXTRA_ELIGIBLE_CONSENTS, it.name) }
                    putExtra(EXTRA_IS_GET_CONSENT_INFORMATION_AS_RESULT, isGetConsentInfoAsResult)
                }
            return intent
        }
    }

    protected open val viewModel: CombinedDataConsentViewModel by viewModels()

    private var eligibleConsents: CombinedDataConsentRepository.DataConsentFlag? = null
    private val consentItems = mutableListOf<CombineDataConsent>()
    private lateinit var adapter: CombinedDataConsentAdapter
    private var showButton = false
    private var consentDeclineConfirmationDialogFragment: ConsentsDeclineConfirmationDialogFragment? = null

    @Inject
    lateinit var preferenceModel: OneAppPreferenceModel

    lateinit var binding: ActivityCombinedDataConsentBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = ActivityCombinedDataConsentBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setSupportActionBar(binding.combinedDataConsentToolbar)
        initData()
        initViews()
        initRecyclerView()
        initViewModelObservers()
        observeBaseEvents(viewModel)
        fetchDataConsents()
        registerForConsentResultListener()
    }

    protected open fun initData() {
        viewModel.vehicle = intent.getParcelableExtra(ToyotaConstants.VEHICLE)
        viewModel.paidFlow = intent.getBooleanExtra(ToyotaConstants.PAID_FLOW, false)
        viewModel.subscriptionGetPayload =
            intent.getParcelableExtra(
                ToyotaConstants.SUBSCRIPTION_GET_PAYLOAD,
            )
        eligibleConsents =
            intent.getStringExtra(EXTRA_ELIGIBLE_CONSENTS)?.let {
                CombinedDataConsentRepository.DataConsentFlag.valueOf(
                    it,
                )
            }
        viewModel.isGetConsentInfoAsResult =
            intent.getBooleanExtra(
                EXTRA_IS_GET_CONSENT_INFORMATION_AS_RESULT,
                false,
            )
        viewModel.paidSubscriptions =
            intent
                .getParcelableArrayExtra(
                    ToyotaConstants.PAID_SUBSCRIPTIONS,
                )?.map { it as SubscriptionV2 }
                ?.toTypedArray()
        viewModel.trialSubscriptions =
            intent
                .getParcelableArrayExtra(
                    ToyotaConstants.TRIAL_SUBSCRIPTIONS,
                )?.map { it as SubscriptionV2 }
                ?.toTypedArray()
    }

    protected open fun initViews() {
        binding.dataConsentConfirmButton.setOnClickListener {
            viewModel.onConfirmAndContinueClick()
        }
    }

    protected open fun fetchDataConsents() {
        if (eligibleConsents != null) {
            viewModel.fetchCombinedDataConsents(
                eligibleConsents!!,
                CombinedDataConsentRepository.DataConsentFlowType.BANNER,
            )
        } else {
            viewModel.fetchCombinedDataConsents()
        }
    }

    private fun initRecyclerView() {
        binding.combinedDataConsentRecyclerview.layoutManager = LinearLayoutManager(this)
        binding.combinedDataConsentRecyclerview.addItemDecoration(
            DividerItemDecoration(this, DividerItemDecoration.VERTICAL),
        )
        adapter =
            CombinedDataConsentAdapter(
                consentItems,
                this@CombinedDataConsentActivity,
                this@CombinedDataConsentActivity,
            )
        binding.combinedDataConsentRecyclerview.adapter = adapter
    }

    private fun initViewModelObservers() {
        viewModel.run {
            consentItemData.observe(
                this@CombinedDataConsentActivity,
                Observer {
                    updateDataConsentList(it)
                },
            )
            showConfirmButton.observe(
                this@CombinedDataConsentActivity,
                Observer {
                    showButton = it
                    enableDisableConfirmButton(showButton)
                },
            )
            onConsentsUpdated.observe(
                this@CombinedDataConsentActivity,
                Observer {
                    val intent =
                        Intent(
                            this@CombinedDataConsentActivity,
                            AddedVehiclesActivity::class.java,
                        ).apply {
                            putExtra("IsWaiveFirstConsent", !viewModel.masterConsentStatus)
                            putExtra(ToyotaConstants.BRAND, viewModel.vehicle?.brand)
                            putExtra(ToyotaConstants.VIN_CAPS, viewModel.vehicle?.vin)
                        }
                    startActivity(intent)
                    finish()
                },
            )

            acceptedOrDeclinedConsents.observe(
                this@CombinedDataConsentActivity,
                Observer {
                    if (it != null) {
                        setResult(
                            RESULT_OK,
                            intent.putExtra(ToyotaConstants.CONSENTS, it as Serializable),
                        )
                    } else {
                        setResult(Activity.RESULT_CANCELED)
                    }
                    finish()
                },
            )

            activeWiFiExist.observe(
                this@CombinedDataConsentActivity,
                Observer {
                    startActivity(
                        ActiveWifiSubscriptionExistActivity.getIntent(
                            this@CombinedDataConsentActivity,
                            trialSubscriptions =
                                intent
                                    .getParcelableArrayExtra(
                                        ToyotaConstants.TRIAL_SUBSCRIPTIONS,
                                    )?.map { it as SubscriptionV2 }
                                    ?.toTypedArray(),
                            vehicleGeneration = viewModel.vehicle?.generation.orEmpty(),
                            vehicleRegion = viewModel.vehicle?.region.orEmpty(),
                            isOnBoarding =
                                intent.getBooleanExtra(
                                    ToyotaConstants.IS_ON_BOARDING_FLOW,
                                    false,
                                ),
                        ),
                    )
                    finish()
                },
            )
        }
    }

    private fun updateDataConsentList(list: List<CombineDataConsent>) {
        consentItems.clear()
        consentItems.addAll(list)
        adapter.notifyDataSetChanged()
    }

    override fun onBackPressed() {
        if (findViewById<View>(R.id.combined_data_consent_decline_view).visibility == View.VISIBLE) {
            findViewById<View>(R.id.combined_data_consent_decline_view).visibility = View.GONE
            hideShowConfirmButton(true)
        } else {
            super.onBackPressed()
        }
    }

    override fun showLinkMessage(message: String) {
        showMessageDialog(message)
    }

    override fun showDeclineDialog(
        combinedDataConsentItemViewModel: CombinedDataConsentItemViewModel,
        updateButtonCheckListener: () -> Unit,
    ) {
        if (viewModel.paidSubscriptions != null && combinedDataConsentItemViewModel.item.masterConsent) {
            purchasedDeclineDialog()
        } else {
            declineDialog(combinedDataConsentItemViewModel, updateButtonCheckListener)
        }
    }

    private fun purchasedDeclineDialog() {
        consentDeclineConfirmationDialogFragment?.dismiss()
        consentDeclineConfirmationDialogFragment =
            viewModel.trialSubscriptions?.let { trialSubscriptions ->
                viewModel.paidSubscriptions?.let { paidSubscriptions ->
                    viewModel.vehicle?.let { vehicleInfo ->
                        ConsentsDeclineConfirmationDialogFragment
                            .getInstance(
                                trialSubscriptions = trialSubscriptions,
                                paidSubscriptions = paidSubscriptions,
                                vehicleInfo = vehicleInfo,
                            ).also {
                                it.show(
                                    supportFragmentManager,
                                    ConsentsDeclineConfirmationDialogFragment.TAG,
                                )
                            }
                    }
                }
            }
    }

    private fun declineDialog(
        viewModel: CombinedDataConsentItemViewModel,
        updateButtonCheckListener: () -> Unit,
    ) {
        val declinePayload = viewModel.item.description?.declinePayload
        findViewById<View>(R.id.combined_data_consent_decline_view).visibility = View.VISIBLE
        hideShowConfirmButton(false)
        findViewById<TextView>(R.id.layout_data_consent_decline_title).text = declinePayload?.title
        findViewById<View>(R.id.layout_data_consent_decline_body).setTextViewHTML(
            html = declinePayload?.body ?: "",
            item = viewModel.item,
            listener = this,
        )
        findViewById<TextView>(R.id.layout_data_consent_decline_positive_button).text = declinePayload?.positiveButtonText
        findViewById<TextView>(R.id.layout_data_consent_decline_positive_button).setOnClickListener {
            findViewById<View>(R.id.combined_data_consent_decline_view).visibility = View.GONE
            updateButtonCheckListener.invoke()
            hideShowConfirmButton(true)
        }
        findViewById<TextView>(R.id.layout_data_consent_decline_negative_button).text = declinePayload?.negativeButtonText
        findViewById<TextView>(R.id.layout_data_consent_decline_negative_button).setOnClickListener {
            findViewById<View>(R.id.combined_data_consent_decline_view).visibility = View.GONE
            hideShowConfirmButton(true)
        }
    }

    protected open fun enableDisableConfirmButton(enabled: Boolean) {
        binding.dataConsentConfirmButton.isEnabled = enabled
    }

    protected open fun hideShowConfirmButton(show: Boolean) {
        binding.dataConsentConfirmButton.isVisible = show
    }

    override fun onAcceptConsent(consent: CombineDataConsent) {
        viewModel.submitConsents(consent, true)
    }

    override fun onConfirmDeclineConsent(consent: CombineDataConsent) {
        viewModel.submitConsents(consent, false)
    }

    override fun showMasterConsentNotEditable() {
        MaterialAlertDialogBuilder(activityContext)
            .setMessage(
                getString(R.string.Privacy_Portal_Master_Consent_UnEditable_Description),
            ).setPositiveButton(R.string.Common_ok, null)
            .show()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
            }
        }
        return true
    }

    private fun registerForConsentResultListener() {
        supportFragmentManager.setFragmentResultListener(
            ConsentsDeclineConfirmationDialogFragment.REQUEST_KEY_CONSENT_DECLINE_CONFIRMATION_DIALOG,
            this,
            FragmentResultListener { _, result ->
                val isConfirm =
                    result.getBoolean(
                        DeclineTrialConfirmationDialogFragment.EXTRA_IS_CONFIRM,
                        false,
                    )
                if (isConfirm) {
                    setResult(
                        RESULT_CANCELED,
                        intent.putExtra(ToyotaConstants.CONSENTS, consentItems as Serializable),
                    )
                    finish()
                }
            },
        )
    }
}
