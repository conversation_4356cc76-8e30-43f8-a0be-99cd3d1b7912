package com.toyota.oneapp.ui.dataconsent.viewmodels

import androidx.databinding.ObservableBoolean
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.model.account.UpdateDataConsentRequest
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.combineddataconsent.CombinedDataConsentPayload
import com.toyota.oneapp.model.subscription.CancellationDataPayload
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscription.SubscriptionPreviewDetailV2
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.LCFSAPIManager
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants.*
import com.toyota.oneapp.util.ToyotaConstants.Companion.CONSENT_DECLINED
import com.toyota.oneapp.util.ToyotaConstants.Companion.FALSE
import com.toyota.oneapp.util.ToyotaConstants.Companion.TRUE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class EditCombinedDataConsentViewModel
    @Inject
    constructor(
        preferenceModel: OneAppPreferenceModel,
        accountManager: AccountManager,
        repository: CombinedDataConsentRepository,
        private val dispatcherProvider: DispatcherProvider,
        dateUtil: DateUtil,
        applicationData: ApplicationData,
        lcfsApiManager: LCFSAPIManager,
        analyticsLogger: AnalyticsLogger,
    ) : CombinedDataConsentViewModel(
            preferenceModel,
            accountManager,
            repository,
            dateUtil,
            applicationData,
            lcfsApiManager,
            analyticsLogger,
        ) {
        var masterDataConsentInitialState = false
        private val _consentUpdatedData = SingleLiveEvent<Boolean>()
        val consentUpdatedData get() = _consentUpdatedData
        var isEditing: Boolean = false
        private val _onConsentDeclined = SingleLiveEvent<Boolean>()
        val onConsentDeclined get() = _onConsentDeclined
        private val refundEligible = ObservableBoolean()
        private val _paymentRecords = SingleLiveEvent<PaymentRecord>()
        val paymentRecords get() = _paymentRecords
        private val _masterConsentDeclined = SingleLiveEvent<VehicleInfo>()
        val masterConsentDeclined get() = _masterConsentDeclined

        override fun updateDateConsentsListFromResponse(payload: CombinedDataConsentPayload) {
            consentItems =
                if (BuildConfig.MAKETING_CARDS_LCFS_ENABLED.not()) {
                    payload.acknowledgedConsents?.filter { it.category != LCFS_CONSENT }
                } else {
                    payload.acknowledgedConsents
                }
            masterDataConsentInitialState = masterConsentAccepted
            consentItems?.forEach {
                it.editable = TRUE
                if (it.masterConsent) {
                    it.consentEditable = true
                    it.masterConsentUnEditable = vehicle?.isFeatureEnabled(Feature.EDIT_MASTER_CONSENT) == false
                } // need to remove this once the backend changes is ready
            }
            _consentItemData.value = consentItems
        }

        fun onEditClick() {
            isEditing = true
            makeConsentsEditable()
        }

        private val masterConsentAccepted
            get() = consentItems?.find { it.masterConsent }?.isAccepted ?: false

        private fun makeConsentsEditable() {
            viewModelScope.launch {
                val editable = if (masterConsentAccepted) TRUE else FALSE
                consentItems?.forEach {
                    if (it.masterConsent) {
                        it.editable = TRUE
                    } else {
                        it.editable = editable
                        if (editable == FALSE) it.consentStatus = CONSENT_DECLINED
                    }
                }
                _consentItemData.postValue(consentItems)
            }
        }

        fun onSaveClick() {
            isEditing = false
            updateConsentAndWaiveSubscriptions(consentItems, !masterConsentAccepted)
        }

        fun updateConsentWhenMasterConsentIsDeclined(consentItems: List<CombineDataConsent>?) {
            updateConsentAndWaiveSubscriptions(consentItems, true)
        }

        private fun makeConsentDisable() {
            viewModelScope.launch(dispatcherProvider.default()) {
                _consentItemData.postValue(consentItems)
            }
        }

        fun onConsentStatusChange(consent: CombineDataConsent?) {
            viewModelScope.launch {
                var isChanged = false
                consent?.let { dataConsent ->
                    consentItems?.forEach {
                        if (it.consentId == dataConsent.consentId) {
                            isChanged = it.consentStatus != dataConsent.consentStatus
                            it.consentStatus = dataConsent.consentStatus
                        }
                    }
                }
                if (isChanged) {
                    makeConsentsEditable()
                }
            }
        }

        override fun onConsentsUpdatedSuccessfully() {
            makeConsentDisable()
        }

        private fun updateConsentAndWaiveSubscriptions(
            consentItems: List<CombineDataConsent>?,
            waive: Boolean,
        ) {
            vehicle?.let {
                viewModelScope.launch {
                    showProgress()
                    val waiveResource =
                        if (waive) {
                            async {
                                repository.createOrWaive17CYPlusSubscription(
                                    vin = it.vin,
                                    brand = it.brand,
                                    region = it.region,
                                    generation = it.generation,
                                    asiCode = it.asiCode,
                                    hwtType = it.hwType,
                                    subscriptionRequest = getSubscriptionRequest(waive, consentItems),
                                )
                            }
                        } else {
                            async {
                                repository.updateDataConsents(
                                    vin = it.vin,
                                    brand = it.brand,
                                    region = it.region,
                                    generation = it.generation,
                                    dateTime = System.currentTimeMillis(),
                                    body = getUpdateConsentBody(it, consentItems),
                                )
                            }
                        }
                    val resources = awaitAll(waiveResource)
                    hideProgress()
                    resources.let {
                        when {
                            waive ->
                                onMasterConsentDeclined(it[0])
                            else ->
                                onOtherConsentsUpdated(it[0])
                        }
                    }
                }
            }
        }

        private fun onMasterConsentDeclined(resource: Resource<BaseResponse?>) {
            when (resource) {
                is Resource.Success -> {
                    _masterConsentDeclined.value = vehicle
                }
                is Resource.Failure -> {
                    showErrorMessage(resource.message)
                }
                else -> {}
            }
        }

        private fun onOtherConsentsUpdated(resource: Resource<BaseResponse?>) {
            when (resource) {
                is Resource.Success -> {
                    _consentUpdatedData.value = true
                    makeConsentDisable()
                    fetchCombinedDataConsents()
                }
                is Resource.Failure -> {
                    showErrorMessage(resource.message)
                }
                else -> {}
            }
        }

        private fun getUpdateConsentBody(
            vehicle: VehicleInfo,
            consentItems: List<CombineDataConsent>?,
        ): UpdateDataConsentRequest =
            UpdateDataConsentRequest(
                vin = vehicle.vin,
                subscriberGuid = preferenceModel.getGuid(),
                dataConsent = DataConsent(FALSE, FALSE, FALSE, FALSE),
                consents = consentRequestItems(consentItems),
            )

        fun onDataConsentDecline(dataConsent: CombineDataConsent) {
            _onConsentDeclined.call()
        }

        fun checkRefund() {
            viewModelScope.launch {
                vehicle?.let { vehicleInfo ->
                    showProgress()
                    val resource =
                        repository.getPreviewRefund(
                            guid = preferenceModel.getGuid(),
                            vin = vehicleInfo.vin,
                            subscriptionIds = vehicleInfo.subscriptions.map { sub -> sub.subscriptionID },
                        )
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            onRefundPreviewSuccess(resource.data?.payload)
                        }
                        is Resource.Failure -> {
                            showErrorMessage(resource.message)
                        }
                        else -> {}
                    }
                }
            }
        }

        private fun onRefundPreviewSuccess(payload: CancellationDataPayload?) {
            payload?.let { dataPayload ->
                if (dataPayload.previewResult?.creditMemos?.any { creditMemo ->
                        creditMemo.creditMemoItems?.any {
                            it.refundEligibilityStatus ==
                                true
                        } ==
                            true
                    } ==
                    true
                ) {
                    paymentRecords.value = dataPayload.records
                    return
                }
            }
            paymentRecords.call()
        }

        val allConsentItems
            get() = ArrayList<CombineDataConsent>(consentItems?.toMutableList() ?: emptyList())

        private fun getSubscriptionRequest(
            waive: Boolean,
            waivedConsentItems: List<CombineDataConsent>?,
        ): SubscriptionPreviewDetailV2 =
            SubscriptionPreviewDetailV2().apply {
                accessToken = ""
                paymentMethodId = null
                dataConsent =
                    DataConsent().apply {
                        can300 = FALSE
                        serviceConnect = FALSE
                        ubi = FALSE
                        dealerContact = FALSE
                    }
                consents = consentRequestItems(waivedConsentItems)
                waiver = waive
            }
    }
