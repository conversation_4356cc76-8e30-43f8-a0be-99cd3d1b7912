package com.toyota.oneapp.ui.dataconsent.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.databinding.Observable
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.AMBER_ALERT_ACCEPT
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.AMBER_ALERT_DECLINE
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.ActivityBannerDataConsentBinding
import com.toyota.oneapp.extensions.HTMLTextClickListener
import com.toyota.oneapp.extensions.setTextViewHTML
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.dataconsent.viewmodels.BannerDataConsentViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import java.io.Serializable
import javax.inject.Inject

@AndroidEntryPoint
open class BannerDataConsentActivity :
    UiBaseActivity(),
    HTMLTextClickListener {
    companion object {
        const val TAG = "BannerDataConsentActivity"
        private const val EXTRA_ELIGIBLE_CONSENTS = "EXTRA_ELIGIBLE_CONSENTS"
        private const val EXTRA_DATA_CONSENT = "EXTRA_DATA_CONSENT"
        private const val EXTRA_IS_SUBSCRIPTION_FLOW = "EXTRA_IS_SUBSCRIPTION_FLOW"
        private const val MARKETING_CONSENT = "marketing"
        private const val AMBER_ALERT_CONSENT = "amber_alerts"

        fun getIntent(
            context: Context,
            vehicle: VehicleInfo?,
            eligibleConsents: String,
            isSubscriptionFlow: Boolean = false,
            dataConsent: CombineDataConsent? = null,
        ): Intent =
            Intent(context, BannerDataConsentActivity::class.java).apply {
                putExtra(ToyotaConstants.VEHICLE, vehicle)
                putExtra(EXTRA_ELIGIBLE_CONSENTS, eligibleConsents)
                putExtra(EXTRA_DATA_CONSENT, dataConsent)
                putExtra(EXTRA_IS_SUBSCRIPTION_FLOW, isSubscriptionFlow)
            }
    }

    protected open val viewModel: BannerDataConsentViewModel by viewModels()

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    private lateinit var binding: ActivityBannerDataConsentBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_banner_data_consent,
            )
        binding.lifecycleOwner = this
        binding.executePendingBindings()
        performActivitySetup(binding.toolbar)
        observeBaseEvents(viewModel)

        initData()
        initViewModelObservers()
    }

    protected open fun initData() {
        viewModel.vehicle = intent.getParcelableExtra(ToyotaConstants.VEHICLE)
        viewModel.eligibleConsent = intent.getStringExtra(EXTRA_ELIGIBLE_CONSENTS) ?: ""
        viewModel.isSubscriptionFlow = intent.getBooleanExtra(EXTRA_IS_SUBSCRIPTION_FLOW, false)
        viewModel.fetchCombinedDataConsents(intent.getParcelableExtra(EXTRA_DATA_CONSENT))
    }

    private fun initViewModelObservers() {
        viewModel.run {
            consentData.observe(this@BannerDataConsentActivity) {
                binding.itemDataConsentBody.setTextViewHTML(
                    it?.description?.body
                        ?: "",
                    it,
                    this@BannerDataConsentActivity,
                )
            }
            onDeclined.observe(this@BannerDataConsentActivity) {
                showDeclineDialog(it)
            }
            onSuccess.observe(this@BannerDataConsentActivity) {
                val resources =
                    when (it) {
                        ToyotaConstants.CONSENT_ACCEPTED -> {
                            when (viewModel.eligibleConsent) {
                                MARKETING_CONSENT -> {
                                    BannerConsentSuccessDeclineActivity.Companion.BannerConsentSuccessDecline.MARKETING_OPT_IN_ACCEPTED
                                }

                                AMBER_ALERT_CONSENT -> {
                                    analyticsLogger.logStringEvent(AMBER_ALERT_ACCEPT)
                                    BannerConsentSuccessDeclineActivity.Companion.BannerConsentSuccessDecline.AMBER_ALERT_ACCEPTED
                                }

                                else -> {
                                    BannerConsentSuccessDeclineActivity.Companion.BannerConsentSuccessDecline.MARKETING_OPT_IN_ACCEPTED
                                }
                            }
                        }
                        ToyotaConstants.CONSENT_DECLINED -> {
                            when (viewModel.eligibleConsent) {
                                MARKETING_CONSENT -> {
                                    BannerConsentSuccessDeclineActivity.Companion.BannerConsentSuccessDecline.MARKETING_OPT_IN_DECLINED
                                }
                                AMBER_ALERT_CONSENT -> {
                                    analyticsLogger.logStringEvent(AMBER_ALERT_DECLINE)
                                    BannerConsentSuccessDeclineActivity.Companion.BannerConsentSuccessDecline.AMBER_ALERT_DECLINED
                                }

                                else -> {
                                    BannerConsentSuccessDeclineActivity.Companion.BannerConsentSuccessDecline.MARKETING_OPT_IN_DECLINED
                                }
                            }
                        }
                        else -> BannerConsentSuccessDeclineActivity.Companion.BannerConsentSuccessDecline.MARKETING_OPT_IN_ACCEPTED
                    }
                startActivity(
                    BannerConsentSuccessDeclineActivity.getIntent(
                        this@BannerDataConsentActivity,
                        resources,
                    ),
                )
            }
            acceptedOrDeclinedConsents.observe(this@BannerDataConsentActivity) {
                if (it != null) {
                    setResult(
                        RESULT_OK,
                        intent.putExtra(ToyotaConstants.CONSENTS, it as Serializable),
                    )
                } else {
                    setResult(Activity.RESULT_CANCELED)
                }
                finish()
            }
            consentData.observe(this@BannerDataConsentActivity) {
                binding.toolbar.title = it.name
                binding.itemDataConsentTitle.text = it.name
                binding.itemDataConsentPositiveButton.text = it.description?.positiveButtonText
                binding.itemDataConsentNegativeButton.text = it.description?.negativeButtonText
            }
            showDecline.addOnPropertyChangedCallback(
                object : Observable.OnPropertyChangedCallback() {
                    override fun onPropertyChanged(
                        sender: Observable?,
                        propertyId: Int,
                    ) {
                        val isVisible = showDecline.get()
                        binding.itemDataConsentBody.isVisible = !isVisible
                        binding.itemDataConsentPositiveButton.isVisible = !isVisible
                        binding.itemDataConsentNegativeButton.isVisible = !isVisible
                        binding.bannerDataConsentDeclineView.layoutDataConsentDeclineRoot.isVisible = isVisible
                    }
                },
            )
        }
        binding.itemDataConsentPositiveButton.setOnClickListener {
            viewModel.onAcceptClicked()
        }
        binding.itemDataConsentNegativeButton.setOnClickListener {
            viewModel.onDeclineClicked()
        }
    }

    override fun onBackPressed() {
        if (viewModel.showDecline.get()) {
            viewModel.showDeclineScreen(false)
        } else {
            super.onBackPressed()
        }
    }

    override fun showLinkMessage(message: String) {
        viewModel.showMessageDialog(message)
    }

    private fun showDeclineDialog(consentData: CombineDataConsent) {
        viewModel.showDeclineScreen(true)
        val declinePayload = consentData.description?.declinePayload
        binding.bannerDataConsentDeclineView.apply {
            layoutDataConsentDeclineTitle.text = declinePayload?.title
            layoutDataConsentDeclineBody.setTextViewHTML(
                html = declinePayload?.body ?: "",
                item = consentData,
                listener = this@BannerDataConsentActivity,
            )
            layoutDataConsentDeclinePositiveButton.text = declinePayload?.positiveButtonText
            layoutDataConsentDeclinePositiveButton.setOnClickListener {
                viewModel.onConfirmDeclineClicked()
            }
            layoutDataConsentDeclineNegativeButton.text = declinePayload?.negativeButtonText
            layoutDataConsentDeclineNegativeButton.setOnClickListener {
                viewModel.showDeclineScreen(false)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
            }
        }
        return true
    }
}
