package com.toyota.oneapp.ui.dataconsent.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.account.AccountManager
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.model.RemoteUser
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.combineddataconsent.CombinedDataConsentPayload
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.combineddataconsent.DataConsentStatus
import com.toyota.oneapp.model.subscription.*
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.LCFSAPIManager
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.ToyotaConstants.Companion.FALSE
import com.toyota.oneapp.util.ToyotaConstants.Companion.TRUE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject
import kotlin.collections.set

@HiltViewModel
open class CombinedDataConsentViewModel
    @Inject
    constructor(
        protected val preferenceModel: OneAppPreferenceModel,
        private val accountManager: AccountManager,
        protected val repository: CombinedDataConsentRepository,
        protected val dateUtil: DateUtil,
        protected val applicationData: ApplicationData,
        private var lcfsApiManager: LCFSAPIManager,
        protected val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        var paidFlow: Boolean = false
        var vehicle: VehicleInfo? = null
        var subscriptionGetPayload: SubscriptionGetPayload? = null
        var isGetConsentInfoAsResult = false
        var paidSubscriptions: Array<SubscriptionV2>? = null
        var trialSubscriptions: Array<SubscriptionV2>? = null
        protected open var consentItems: List<CombineDataConsent>? = null
        private val consents: HashMap<String, Boolean> = HashMap()
        protected open val _consentItemData = SingleLiveEvent<List<CombineDataConsent>>()
        protected open val _acceptedOrDeclinedConsents = SingleLiveEvent<List<ConsentRequestItem>?>()
        private val _showConfirmButton = SingleLiveEvent<Boolean>()
        private val _onConsentsUpdated = SingleLiveEvent<Boolean>()
        private var _dataConsentDescription = MutableLiveData<CombineDataConsent?>()
        private var _customerConsent = MutableLiveData<Any?>()
        private var lcfsConsentRequestItem: ConsentRequestItem? = null
        private var _activeWiFiExist = SingleLiveEvent<Unit>()

        val dataConsentDescription get() = _dataConsentDescription
        val customerConsent get() = _customerConsent
        val consentItemData get() = _consentItemData
        val showConfirmButton get() = _showConfirmButton
        val onConsentsUpdated get() = _onConsentsUpdated
        val acceptedOrDeclinedConsents get() = _acceptedOrDeclinedConsents
        var masterConsentStatus: Boolean = false
        val activeWiFiExist get() = _activeWiFiExist

        fun submitConsents(
            consent: CombineDataConsent,
            consentValue: Boolean,
        ) {
            if (consent.masterConsent) {
                masterConsentStatus = consentValue
                if (consentValue) {
                    enableAllConsentsOnMasterConsentAccept()
                    consents[consent.consentId] = true
                    if (consents.size == consentItems?.size) _showConfirmButton.value = true
                } else {
                    disableOtherConsentWhenMasterConsentDecline()
                }
                return
            } else {
                consents[consent.consentId] = consentValue
                if (consents.size == consentItems?.size) _showConfirmButton.value = true
            }
        }

        private fun disableOtherConsentWhenMasterConsentDecline() {
            consentItems?.forEach {
                if (!it.masterConsent) {
                    it.editable = FALSE
                }
                it.consentStatus = DataConsentStatus.DECLINED.value
                consents[it.consentId] = false
            }
            _consentItemData.value = consentItems!!
            _showConfirmButton.value = true
        }

        private fun enableAllConsentsOnMasterConsentAccept() {
            consentItems?.forEach {
                it.editable = TRUE
            }
            _consentItemData.value = consentItems!!
        }

        fun fetchCombinedDataConsents(
            dataConsentFlag: CombinedDataConsentRepository.DataConsentFlag = CombinedDataConsentRepository.DataConsentFlag.NONE,
            dataConsentFlowType: CombinedDataConsentRepository.DataConsentFlowType = CombinedDataConsentRepository.DataConsentFlowType.NONE,
        ) {
            vehicle?.let {
                viewModelScope.launch {
                    showProgress()
                    val resource =
                        repository.getCombinedDataConsent(
                            vin = it.vin,
                            brand = it.brand,
                            gen = it.generation,
                            region = it.region,
                            productCodes = subscriptionGetPayload?.externalSubscriptions?.map { it.productCode },
                            eligibleConsent = dataConsentFlag.value,
                            flowType = dataConsentFlowType.value,
                        )
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            resource.data?.payload?.let {
                                updateDateConsentsListFromResponse(it)
                                it.eligibleConsents
                                    ?.singleOrNull { dataConsent ->
                                        dataConsent.category == LCFS_CONSENT
                                    }?.let { description ->
                                        lcfsConsentRequestItem =
                                            ConsentRequestItem(
                                                consentId = description.consentId,
                                                status = description.consentStatus,
                                                versionId = description.version,
                                                category = description.category,
                                            )
                                        _dataConsentDescription.postValue(description)
                                    }
                            }
                        }
                        is Resource.Failure -> {
                            _dataConsentDescription.postValue(null)
                        }
                        else -> {}
                    }
                }
            }
        }

        fun sendLcfsDataConsent() {
            vehicle?.let {
                viewModelScope.launch {
                    lcfsConsentRequestItem?.let { _lcfsConsentRequestItem ->
                        showProgress()
                        lcfsApiManager.sendLCFSCustomerConsent(
                            vin = it.vin,
                            guid = preferenceModel.getGuid(),
                            status = LCFS_ACCEPTED,
                            lcfsConsentRequestItem = _lcfsConsentRequestItem,
                            callback =
                                object : BaseCallback<BaseResponse>() {
                                    override fun onSuccess(response: BaseResponse) {
                                        super.onSuccess(response)
                                        hideProgress()
                                        _customerConsent.postValue(response)
                                    }

                                    override fun onFailError(
                                        httpCode: Int,
                                        errorMsg: String?,
                                    ) {
                                        super.onFailError(httpCode, errorMsg)
                                        hideProgress()
                                        _customerConsent.postValue(errorMsg)
                                    }
                                },
                        )
                    }
                }
            }
        }

        /**
         * Check If Master Consent is available (In some cases we will not get Master consent we will only get partner consent like WIFI)
         * If Master consent is available - Make only Master consent editable and other consent as not editable.
         * If Master consent is not available - Make all consent as Editable.
         */
        protected open fun updateDateConsentsListFromResponse(payload: CombinedDataConsentPayload) {
            val isMasterConsentAvailable =
                payload.eligibleConsents?.firstOrNull { it.masterConsent }?.let { true }
                    ?: false
            payload.eligibleConsents?.forEach {
                it.editable =
                    if (isMasterConsentAvailable) {
                        if (it.masterConsent) TRUE else FALSE
                    } else {
                        TRUE
                    }

                it.showButtons = true
            }
            updateDataConsentItems(payload.eligibleConsents)
        }

        fun updateDataConsentItems(list: List<CombineDataConsent>?) {
            consentItems =
                if (BuildConfig.MAKETING_CARDS_LCFS_ENABLED.not()) {
                    list?.filter { it.category != LCFS_CONSENT }
                } else {
                    list
                }
            _consentItemData.value = consentItems
        }

        fun onConfirmAndContinueClick() {
            consentItems?.forEach {
                if (DataConsentStatus.ACCEPTED.value == it.consentStatus) {
                    analyticsLogger.logStringEvent("USER_ACCEPTED_" + it.category)
                } else {
                    analyticsLogger.logStringEvent("USER_DECLINED" + it.category)
                }
            }
            if (isGetConsentInfoAsResult || (paidFlow && masterConsentStatus)) {
                consentItems?.forEach {
                    it.versionId = it.version
                    it.status = it.consentStatus
                    it.category = it.category
                }
                _acceptedOrDeclinedConsents.value = consentRequestItems(consentItems)
            } else {
                if (paidFlow && applicationData.getSelectedVehicle()?.isCY17 == true && applicationData.savedVehicles.isEmpty()) {
                    _acceptedOrDeclinedConsents.value = null
                } else {
                    createOrWaive17CYPlusSubscription()
                }
            }
        }

        protected open fun createOrWaive17CYPlusSubscription() {
            vehicle?.let {
                viewModelScope.launch {
                    showProgress()
                    val resource =
                        repository.createOrWaive17CYPlusSubscription(
                            vin = it.vin,
                            brand = it.brand,
                            region = it.region,
                            generation = it.generation,
                            asiCode = it.asiCode,
                            hwtType = it.hwType,
                            subscriptionRequest = getSubscriptionRequest(!masterConsentStatus),
                        )
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            onConsentsUpdatedSuccessfully()
                        }
                        is Resource.Failure -> {
                            if (ToyotaConstants.WAIVE_SUBSCRIPTION_ATT_ACTIVE_WIFI_ERROR_CODE.equals(
                                    resource.responseCode,
                                    true,
                                )
                            ) {
                                _activeWiFiExist.call()
                            } else {
                                showErrorMessage(resource.message)
                            }
                        }
                        else -> {}
                    }
                }
            }
        }

        protected open fun onConsentsUpdatedSuccessfully() {
            if (paidFlow && applicationData.savedVehicles.isEmpty()) {
                _acceptedOrDeclinedConsents.value = arrayListOf()
            } else {
                _onConsentsUpdated.value = true
            }
        }

        protected open fun getSubscriptionRequest(waive: Boolean): SubscriptionPreviewDetailV2 =
            SubscriptionPreviewDetailV2().apply {
                waiver = waive
                subscriptionGetPayload?.let { payload ->
                    val list = ArrayList<PreviewSubscriptionItemV2>()
                    for (item in payload.subscriptions.filter {
                        it.type.equals(
                            ToyotaConstants.TRIAL,
                            true,
                        )
                    }) {
                        val previewSubscriptionItemV2 =
                            PreviewSubscriptionItemV2(
                                autoRenew = item.isAutoRenew,
                                isCPOProduct = item.isCPOProduct,
                                isPPOProduct = item.isPPOProduct,
                                isService = item.isService,
                                packageID = item.packageID,
                            )
                        list.add(previewSubscriptionItemV2)
                    }
                    isCPOEligible = payload.isCPOEligible
                    isPPOEligible = payload.isPPOEligible
                    isService = payload.isServiceConnect
                    paymentToken = payload.accessToken
                    accessToken = payload.accessToken
                    if (ToyUtil.isRemoteCapable(vehicle?.capabilityItems) && !waive) {
                        remoteUser =
                            RemoteUser().apply {
                                remoteUserGuid = preferenceModel.getGuid()
                            }
                    }
                    subscriptions = list

                    var externalSubscriptionsList = ArrayList<PreviewSubscriptionItem?>()
                    payload.externalSubscriptions.forEach { item ->
                        val subscriptionItem = PreviewSubscriptionItem()
                        subscriptionItem.isCPOProduct = item.isCPOProduct
                        subscriptionItem.isPPOProduct = item.isPPOProduct
                        subscriptionItem.isService = item.isService
                        subscriptionItem.packageID = item.packageID
                        subscriptionItem.isAutoRenew = item.isAutoRenew
                        externalSubscriptionsList.add(subscriptionItem)
                    }
                    externalSubscriptions = externalSubscriptionsList
                }

                productsTotalAmount = ProductsTotalAmount("USD", 0.0)
                productsAmount = ProductsAmount("USD", 0.0)
                taxAmount = TaxAmount("USD", 0.0)
                consentItems?.forEach {
                    it.versionId = it.version
                    it.status = it.consentStatus
                    it.category = it.category
                }
                consents = consentRequestItems(consentItems)
                dataConsent =
                    DataConsent().apply {
                        can300 = FALSE
                        serviceConnect = FALSE
                        ubi = FALSE
                        dealerContact = FALSE
                    }
            }

        fun logOut() {
            accountManager.logout()
        }

        companion object {
            const val TAG = "CombinedDataConsentViewModel"
            const val LCFS_CONSENT = "LCFS_CONSENT"
            private const val LCFS_ACCEPTED = "Accepted"
        }

        fun consentRequestItems(consentItems: List<CombineDataConsent>?): List<ConsentRequestItem>? =
            consentItems?.map {
                ConsentRequestItem(
                    consentId = it.consentId,
                    versionId = it.version,
                    status = it.consentStatus,
                    category = it.category,
                )
            }
    }
