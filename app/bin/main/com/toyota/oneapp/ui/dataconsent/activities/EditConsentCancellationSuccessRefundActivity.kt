package com.toyota.oneapp.ui.dataconsent.activities

import android.app.Activity
import android.os.Bundle
import com.toyota.oneapp.R
import com.toyota.oneapp.ui.garage.subscriptionCancellation.CancellationSuccessRefundActivity
import com.toyota.oneapp.ui.garage.subscriptionCancellation.CancellationSuccessRefundViwModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EditConsentCancellationSuccessRefundActivity : CancellationSuccessRefundActivity() {
    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        viewModel.state.postValue(
            CancellationSuccessRefundViwModel.State(
                title = R.string.ManagePaidSubscription_cancellation_succeeded,
                description = R.string.Cancellation_Success_Refund_disclaimer,
            ),
        )
    }

    override fun onValid(isValid: Boolean) {
        dataBinding.cancellationSuccessConfirm.isEnabled = viewModel.paymentRecord == null || isValid
    }

    override fun onPaymentRecordUpdated() {
        setResult(Activity.RESULT_OK)
        finish()
    }
}
