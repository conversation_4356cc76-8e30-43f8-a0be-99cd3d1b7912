package com.toyota.oneapp.ui.dataconsent.viewmodels

import androidx.databinding.ObservableField
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.model.account.AcknowledgeConsentRequest
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.reconsent.ReConsentData
import com.toyota.oneapp.model.reconsent.ReConsentState
import com.toyota.oneapp.model.subscription.SubscriptionPreviewDetailV2
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.network.api.repository.ReConsentRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

enum class MDCEvent {
    ACTION_MASTER_DATA_CONSENT_TO_DECLINE_CONFIRMATION,
    ACTION_DECLINE_CONFIRMATION_TO_MASTER_DATA_CONSENT,
    ACTION_DECLINE_CONFIRMATION_TO_WIFI_ERROR,
    ACTION_SEND_RESULT,
    ACTION_DECLINE_SUCCESS_SEND_RESULT,
    ACTION_WAIVE_SUBSCRIPTION,
    ACTION_WIFI_ERROR_TO_MASTER_DATA_CONSENT,
    ACTION_MASTER_DATA_CONSENT_ACCEPT_CLICKED,
}

@HiltViewModel
class MasterDataConsentViewModel
    @Inject
    constructor(
        private val repository: ReConsentRepository,
        private val combineDataConsentRepository: CombinedDataConsentRepository,
        private val preferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel() {
        private var _reConsentData = MutableLiveData<ReConsentData>()
        val reConsentData: LiveData<ReConsentData> get() = _reConsentData

        private var _navigationEvent = MutableLiveData<MDCEvent>()
        val navigationEvent: LiveData<MDCEvent> get() = _navigationEvent

        private var _dataConsentDescription = MutableLiveData<CombineDataConsent?>()
        val dataConsentDescription: LiveData<CombineDataConsent?> get() = _dataConsentDescription

        private var _mdc = MutableLiveData<CombineDataConsent>()
        val mdc get() = _mdc
        val declineConsentTitle: ObservableField<String> = ObservableField("")
        val declineConsentBody: ObservableField<String> = ObservableField("")

        private var acknowledgedConsents: List<CombineDataConsent>? = null

        private val _reConsentState = MutableStateFlow<ReConsentState>(value = ReConsentState.Idle)
        val reConsentState = _reConsentState.asStateFlow()

        private var _vehicleInfo: VehicleInfo? = null
        val vehicle get() = _vehicleInfo

        fun fetchReConsentEligibility(
            vehicleInfo: VehicleInfo,
            hasVehicle: Boolean = false,
            errorMessage: String = "",
        ) {
            _vehicleInfo = vehicleInfo
            if (vehicleInfo.generation != null) {
                viewModelScope.launch {
                    showProgress()
                    _reConsentState.value = ReConsentState.Loading
                    val resource =
                        repository.getReConsent(
                            vin = vehicleInfo.vin,
                            vinBrand = vehicleInfo.brand,
                            generation = vehicleInfo.generation,
                            vinRegion = vehicleInfo.region,
                        )
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            resource.data?.payload?.reconsent?.let {
                                val response =
                                    ReConsentData(
                                        hasVehicle = hasVehicle,
                                        errorMessage = errorMessage,
                                        consentId = it.consentId,
                                        reConsentEnabled = it.eligible,
                                    )
                                _reConsentData.value = response
                                _reConsentState.value = ReConsentState.Success(response)
                            } ?: kotlin.run {
                                _reConsentData.value =
                                    ReConsentData(
                                        hasVehicle = hasVehicle,
                                        errorMessage = errorMessage,
                                    )
                                _reConsentState.value = ReConsentState.Error
                            }
                        }
                        else -> {
                            _reConsentData.value =
                                ReConsentData(
                                    hasVehicle = hasVehicle,
                                    errorMessage = errorMessage,
                                )
                            _reConsentState.value = ReConsentState.Error
                        }
                    }
                }
            } else {
                _reConsentData.value =
                    ReConsentData(
                        hasVehicle = hasVehicle,
                        errorMessage = errorMessage,
                    )
                _reConsentState.value = ReConsentState.Error
            }
        }

        fun resetReConsentState() {
            _reConsentState.value = ReConsentState.Idle
        }

        fun fetchCombinedDataConsents(
            vehicleInfo: VehicleInfo,
            dataConsentFlag: CombinedDataConsentRepository.DataConsentFlag = CombinedDataConsentRepository.DataConsentFlag.NONE,
            dataConsentFlowType: CombinedDataConsentRepository.DataConsentFlowType = CombinedDataConsentRepository.DataConsentFlowType.NONE,
            consentId: String,
        ) {
            _vehicleInfo = vehicleInfo
            viewModelScope.launch {
                showProgress()
                val resource =
                    combineDataConsentRepository.getCombinedDataConsent(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        gen = vehicleInfo.generation,
                        region = vehicleInfo.region,
                        eligibleConsent = dataConsentFlag.value,
                        flowType = dataConsentFlowType.value,
                    )
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.payload?.let {
                            it.eligibleConsents?.singleOrNull { dataConsent -> dataConsent.consentId == consentId }?.let { description ->
                                _dataConsentDescription.postValue(description)
                                _mdc.value = description
                                declineConsentTitle.set(
                                    description.description?.declinePayload?.title ?: "",
                                )
                                declineConsentBody.set(
                                    description.description?.declinePayload?.body ?: "",
                                )
                            }
                            acknowledgedConsents = it.acknowledgedConsents
                        }
                    }
                    is Resource.Failure -> {
                        _dataConsentDescription.postValue(null)
                    }
                    else -> {}
                }
            }
        }

        fun acceptMasterDataConsent(vehicleInfo: VehicleInfo) {
            _vehicleInfo = vehicleInfo
            viewModelScope.launch {
                showProgress()
                val consents =
                    listOf(
                        ConsentRequestItem(
                            consentId = dataConsentDescription.value?.consentId ?: "",
                            versionId = dataConsentDescription.value?.version ?: "",
                            status = ToyotaConstants.CONSENT_ACCEPTED,
                            category = dataConsentDescription.value?.category ?: "",
                        ),
                    )

                val body =
                    AcknowledgeConsentRequest(
                        vin = vehicleInfo.vin,
                        guid = preferenceModel.getGuid(),
                        eventType = "reconsent",
                        consents = consents,
                    )
                val resource =
                    combineDataConsentRepository.acknowledgeConsent(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        region = vehicleInfo.region ?: "",
                        eligibleConsent = "",
                        body = body,
                    )
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        _navigationEvent.value = MDCEvent.ACTION_SEND_RESULT
                    }

                    is Resource.Failure -> {
                        showErrorMessage(resource.error?.message)
                    }

                    else -> {
                    }
                }
            }
        }

        fun declineMasterConsent(vehicleInfo: VehicleInfo) {
            _vehicleInfo = vehicleInfo
            viewModelScope.launch {
                showProgress()
                val body =
                    SubscriptionPreviewDetailV2().apply {
                        waiver = true

                        val filteredConsents =
                            acknowledgedConsents?.filter {
                                it.declineMasterConsentEligible == true
                            }
                        consents =
                            filteredConsents?.map {
                                ConsentRequestItem(
                                    consentId = it.consentId,
                                    versionId = it.version,
                                    status = ToyotaConstants.CONSENT_DECLINED,
                                    category = it.category,
                                )
                            }
                        dataConsent =
                            DataConsent().apply {
                                can300 = ToyotaConstants.FALSE
                                serviceConnect = ToyotaConstants.FALSE
                                ubi = ToyotaConstants.FALSE
                                dealerContact = ToyotaConstants.FALSE
                            }
                    }
                val resource =
                    combineDataConsentRepository.createOrWaive17CYPlusSubscription(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        region = vehicleInfo.region,
                        generation = vehicleInfo.generation,
                        asiCode = vehicleInfo.asiCode,
                        hwtType = vehicleInfo.hwType,
                        subscriptionRequest = body,
                    )
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        _navigationEvent.value = MDCEvent.ACTION_DECLINE_SUCCESS_SEND_RESULT
                    }
                    is Resource.Failure -> {
                        if (ToyotaConstants.WAIVE_SUBSCRIPTION_ATT_ACTIVE_WIFI_ERROR_CODE.equals(
                                resource.responseCode,
                                true,
                            )
                        ) {
                            _navigationEvent.value = MDCEvent.ACTION_DECLINE_CONFIRMATION_TO_WIFI_ERROR
                        } else {
                            showErrorMessage(resource.message)
                        }
                    }
                    else -> {}
                }
            }
        }

        fun onMasterDataConsentAcceptClicked() {
            _navigationEvent.value = MDCEvent.ACTION_MASTER_DATA_CONSENT_ACCEPT_CLICKED
        }

        fun onMasterDataConsentDeclineClicked() {
            _navigationEvent.value = MDCEvent.ACTION_MASTER_DATA_CONSENT_TO_DECLINE_CONFIRMATION
        }

        fun onDeclineConsentConfirmClicked() {
            _navigationEvent.value = MDCEvent.ACTION_WAIVE_SUBSCRIPTION
        }

        fun onDeclineConsentCancelClicked() {
            _navigationEvent.value = MDCEvent.ACTION_DECLINE_CONFIRMATION_TO_MASTER_DATA_CONSENT
        }

        fun onWifiErrorBackActionClicked() {
            _navigationEvent.value = MDCEvent.ACTION_WIFI_ERROR_TO_MASTER_DATA_CONSENT
        }
    }
