package com.toyota.oneapp.ui.dataconsent

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.LocaleList
import android.view.MenuItem
import android.widget.CompoundButton
import androidx.activity.viewModels
import androidx.appcompat.widget.Toolbar
import androidx.core.os.bundleOf
import androidx.core.text.HtmlCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityDataConsentLcfsDetailBinding
import com.toyota.oneapp.extensions.HTMLTextClickListener
import com.toyota.oneapp.extensions.applyUnderline
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.dataconsent.viewmodels.CombinedDataConsentViewModel
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * Created by Mohan Uppaluri on 2020-10-04.
 */
@AndroidEntryPoint
class DataConsentLCFSDetailActivity :
    UiBaseActivity(),
    HTMLTextClickListener {
    private val viewModel: CombinedDataConsentViewModel by viewModels()
    private lateinit var activityDataConsentLcfsDetailBinding: ActivityDataConsentLcfsDetailBinding

    @set:Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @set:Inject
    lateinit var applicationData: ApplicationData

    override fun applyOverrideConfiguration(overrideConfiguration: Configuration) {
        val region = AppLanguageUtils.sVehicleRegion
        if (region != null) {
            val language = AppLanguageUtils.getLanguageByRegion(region)
            val locale = AppLanguageUtils.getLocaleFromCode("$language-$region")
            overrideConfiguration.setLocales(LocaleList(locale))
        }
        super.applyOverrideConfiguration(overrideConfiguration)
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)

        initBinding()
        initData()
        initViews()
        initViewModelObservers()
        initListeners()
        observeBaseEvents(viewModel)
        fetchDataConsents()
    }

    private fun initBinding() {
        activityDataConsentLcfsDetailBinding =
            DataBindingUtil.setContentView(
                this,
                R.layout.activity_data_consent_lcfs_detail,
            )
        activityDataConsentLcfsDetailBinding.combinedDataConsentViewModel = viewModel
        activityDataConsentLcfsDetailBinding.lifecycleOwner = this
        GlideUtil.loadImage(
            this,
            intent.getStringExtra(LCFS_IMAGE),
            GlideUtil.CropTransparentTransform(),
            R.drawable.lcfs_banner_new,
            activityDataConsentLcfsDetailBinding.ivDataConsentLogo,
        )
    }

    private fun initData() {
        viewModel.vehicle = intent.getParcelableExtra(ToyotaConstants.VEHICLE)
    }

    private fun fetchDataConsents() {
        viewModel.fetchCombinedDataConsents(
            CombinedDataConsentRepository.DataConsentFlag.LCFS_ELIGIBLE_CONSENT,
            CombinedDataConsentRepository.DataConsentFlowType.BANNER,
        )
    }

    private fun initViewModelObservers() {
        viewModel.run {
            dataConsentDescription.observe(
                this@DataConsentLCFSDetailActivity,
                Observer {
                    it?.description?.body?.let { description ->
                        activityDataConsentLcfsDetailBinding.chkDataConsentLcfsDescription.text =
                            HtmlCompat.fromHtml(
                                description,
                                HtmlCompat.FROM_HTML_MODE_LEGACY,
                            )
                    } ?: run {
                        showErrorToast(getString(R.string.Notification_default_error))
                    }
                },
            )

            consentItemData.observe(
                this@DataConsentLCFSDetailActivity,
                Observer {
                    activityDataConsentLcfsDetailBinding.tvDataConsentlcfsProgramOverview.isVisible = true
                },
            )

            customerConsent.observe(
                this@DataConsentLCFSDetailActivity,
                Observer {
                    handleCustomerConsent(it)
                },
            )
        }
    }

    private fun initViews() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)

        activityDataConsentLcfsDetailBinding.tvDataConsentlcfsProgramOverview.applyUnderline()
        activityDataConsentLcfsDetailBinding.tvDataConsentlcfsFaq.applyUnderline()
    }

    private fun initListeners() {
        activityDataConsentLcfsDetailBinding.chkDataConsentLcfsDescription.setOnCheckedChangeListener {
                _: CompoundButton?,
                isChecked: Boolean,
            ->
            activityDataConsentLcfsDetailBinding.btnDataConsentAgree.isEnabled =
                isChecked
        }
        activityDataConsentLcfsDetailBinding.tvDataConsentlcfsProgramOverview.setOnClickListener {
            val intent = Intent(this, DataConsentLCFSProgramOverviewActivity::class.java)
            startActivity(intent)
        }

        activityDataConsentLcfsDetailBinding.btnDataConsentAgree.setOnClickListener {
            onAcceptClick()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressed()
        }
        return true
    }

    private fun onAcceptClick() {
        analyticsLogger.logEvent(AnalyticsEvent.LCFS_CONFIRM)
        viewModel.sendLcfsDataConsent()
    }

    private fun handleCustomerConsent(response: Any?) {
        when (response) {
            is BaseResponse -> {
                showSuccessToast(getString(R.string.clean_charge_enroll_success_message))
                Handler().postDelayed({
                    val intent = Intent()
                    intent.putExtra(RESULT_ACCEPTED, true)
                    setResult(RESULT_OK, intent)
                    finish()
                }, 1000)
            }
            else -> showErrorToast(getString(R.string.Notification_default_error))
        }
    }

    companion object {
        private const val RESULT_ACCEPTED = "accepted"
        private const val DASHBOARD_CONSENT = "dashboardConsent"
        private const val LCFS_IMAGE = "LCFS_IMAGE"

        fun getIntent(
            context: Context,
            vehicleInfo: VehicleInfo,
            dashboardConsent: Boolean,
            lcfsImage: String? = "",
        ) = Intent(
            context,
            DataConsentLCFSDetailActivity::class.java,
        ).apply {
            putExtras(
                bundleOf(
                    DASHBOARD_CONSENT to dashboardConsent,
                    ToyotaConstants.VEHICLE to vehicleInfo,
                    LCFS_IMAGE to lcfsImage,
                ),
            )
        }
    }

    override fun showLinkMessage(message: String) {
        showMessageDialog(message)
    }
}
