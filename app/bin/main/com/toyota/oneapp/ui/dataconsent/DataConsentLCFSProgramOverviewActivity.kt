package com.toyota.oneapp.ui.dataconsent

import android.content.res.Configuration
import android.os.Bundle
import android.os.LocaleList
import android.view.MenuItem
import androidx.appcompat.widget.Toolbar
import com.toyota.oneapp.R
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.AppLanguageUtils
import dagger.hilt.android.AndroidEntryPoint

/**
 * Created by <PERSON> on 2020-10-04.
 */
@AndroidEntryPoint
class DataConsentLCFSProgramOverviewActivity : UiBaseActivity() {
    override fun applyOverrideConfiguration(overrideConfiguration: Configuration) {
        val region = AppLanguageUtils.sVehicleRegion
        if (region != null) {
            val language = AppLanguageUtils.getLanguageByRegion(region)
            val locale = AppLanguageUtils.getLocaleFromCode("$language-$region")
            overrideConfiguration.setLocales(LocaleList(locale))
        }
        super.applyOverrideConfiguration(overrideConfiguration)
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_data_consent_lcfs_program_overview)

        initViews()
    }

    private fun initViews() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressed()
        }
        return true
    }
}
