/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.ui

import android.content.Context
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.accountsettings.SecuritySettingsViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class BiometricSettingViewModel
    @Inject
    constructor(
        private val preferenceModel: OneAppPreferenceModel,
        val analyticsLogger: AnalyticsLogger,
    ) : SecuritySettingsViewModel(preferenceModel) {
        var isBiometricSettingEnabled = ObservableBoolean(false)
        val isKeepMeSignInEnabled = ObservableBoolean(false)
        val biometricSettingViewModelNavigationEvent = SingleLiveEvent<BiometricSettingViewModelNavigationEvent>()
        val biometricTimeText = MutableLiveData<String>()

        init {
            isBiometricSettingEnabled.set(true)
            isKeepMeSignInEnabled.set(true)
        }

        fun onBioInit(context: Context) {
            biometricTimeText.postValue(getSharePreferenceData(context))
        }

        fun onBiometricSwitchSettingChanged(checked: Boolean) {
            analyticsLogger.logEvent(AnalyticsEvent.USER_ENABLE_SIGNIN_BIOMETRIC_CTA)
            isBiometricSettingEnabled.set(checked)
        }

        fun onKeepMeSignInSettingChanged(checked: Boolean) {
            analyticsLogger.logEvent(AnalyticsEvent.USER_ENABLE_ALWAYS_SIGNEDIN_CTA)
            isKeepMeSignInEnabled.set(checked)
        }

        fun onContinueClicked() {
            preferenceModel.setBiometricSettingsShown(preferenceModel.getGuid())
            preferenceModel.setBiometricEnabled(isBiometricSettingEnabled.get())
            val keepMeLogin =
                if (isKeepMeSignInEnabled.get()) {
                    preferenceModel.getGuid()
                } else {
                    ""
                }
            preferenceModel.setKeepMeLogin(keepMeLogin)
            biometricSettingViewModelNavigationEvent.postValue(
                BiometricSettingViewModelNavigationEvent.Continue,
            )
            setSessionTime(preferenceModel.getBiometricSettingsTime())
        }

        fun onRequiredAuthSettingClicked() {
            biometricSettingViewModelNavigationEvent.postValue(
                BiometricSettingViewModelNavigationEvent.RequiredAuthSetting,
            )
        }
    }

sealed class BiometricSettingViewModelNavigationEvent {
    object Continue : BiometricSettingViewModelNavigationEvent()

    object RequiredAuthSetting : BiometricSettingViewModelNavigationEvent()
}
