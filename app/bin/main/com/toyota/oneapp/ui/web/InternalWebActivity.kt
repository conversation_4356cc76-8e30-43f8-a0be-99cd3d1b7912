package com.toyota.oneapp.ui.web

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.webkit.GeolocationPermissions
import android.webkit.JsResult
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityWebviewBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class InternalWebActivity : UiBaseActivity() {
    private lateinit var binding: ActivityWebviewBinding

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil
                .setContentView<ActivityWebviewBinding>(
                    this,
                    R.layout.activity_webview,
                ).apply {
                    lifecycleOwner = this@InternalWebActivity
                    executePendingBindings()
                    performActivitySetup(toolbar)
                }
        initRefreshLayout()
        init(intent.getStringExtra(URL), intent.getBooleanExtra(IS_ALL_SETTINGS_REQUIRED, true))
    }

    private fun initRefreshLayout() {
        binding.internalWebviewSwiperefresh.setColorSchemeColors(
            ContextCompat.getColor(this, R.color.colorAccent),
        )
        binding.internalWebviewSwiperefresh.setOnRefreshListener {
            binding.internalWebviewSwiperefresh.postDelayed(
                { binding.internalWebviewSwiperefresh.isRefreshing = false },
                2000,
            )
            intent.getStringExtra("URL")?.let { binding.internalWebview.loadUrl(it) }
        }
    }

    private fun init(
        url: String?,
        isAllSettingsRequired: Boolean,
    ) {
        val settings = binding.internalWebview.settings
        val webView = binding.internalWebview
        settings.displayZoomControls = false
        settings.javaScriptEnabled = true
        settings.domStorageEnabled = true
        settings.cacheMode = WebSettings.LOAD_NO_CACHE
        webView.webViewClient = mWebViewClient

        // Some websites are causing ANR if loaded with all these settings
        if (isAllSettingsRequired) {
            settings.apply {
                builtInZoomControls = true
                setSupportZoom(true)
                loadWithOverviewMode = true
                useWideViewPort = true
                setGeolocationEnabled(true)
                allowFileAccess = true
                allowContentAccess = true
                databaseEnabled = true
                loadsImagesAutomatically = true
                javaScriptCanOpenWindowsAutomatically = true
                mediaPlaybackRequiresUserGesture = false
            }
            webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            webView.scrollBarStyle = WebView.SCROLLBARS_OUTSIDE_OVERLAY
            webView.setInitialScale(1)
            webView.webChromeClient =
                object : WebChromeClient() {
                    override fun onGeolocationPermissionsShowPrompt(
                        origin: String,
                        callback: GeolocationPermissions.Callback,
                    ) {
                        callback.invoke(origin, true, false)
                    }

                    override fun onJsAlert(
                        view: WebView,
                        url: String,
                        message: String,
                        result: JsResult,
                    ): Boolean = super.onJsAlert(view, url, message, result)

                    override fun onProgressChanged(
                        view: WebView,
                        newProgress: Int,
                    ) {
                        if (newProgress == 100) {
                            binding.internalWebProgressbar.progress = 0
                        } else {
                            binding.internalWebProgressbar.progress = newProgress % 100
                        }
                        super.onProgressChanged(view, newProgress)
                    }

                    override fun onReceivedTitle(
                        view: WebView,
                        title: String,
                    ) {
                        super.onReceivedTitle(view, title)
                        val pageTitle = intent.getStringExtra("TITLE")
                        if (!pageTitle.isNullOrEmpty()) {
                            binding.toolbarWebTitleTxt.text = pageTitle
                        } else {
                            binding.toolbarWebTitleTxt.text = title
                        }
                    }
                }
        }
        url?.let {
            webView.loadUrl(it)
        }
    }

    override fun onBackPressed() {
        if (binding.internalWebview.canGoBack()) {
            binding.internalWebview.goBack()
        } else {
            super.onBackPressed()
        }
    }

    private val mWebViewClient: WebViewClient =
        object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView,
                request: WebResourceRequest,
            ): Boolean = shouldOverrideURL(this@InternalWebActivity, view, request.url.toString())
        }

    companion object {
        private const val URL = "URL"
        private const val IS_ALL_SETTINGS_REQUIRED = "IS_ALL_SETTINGS_REQUIRED"

        private fun shouldOverrideURL(
            context: Context,
            view: WebView,
            url: String?,
        ): Boolean {
            if (url == null) return false
            try {
                if (!url.startsWith("http://") && !url.startsWith("https://")) {
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                    context.startActivity(intent)
                    return true
                }
            } catch (e: Exception) {
                return true
            }
            view.loadUrl(url)
            return true
        }
    }
}
