package com.toyota.oneapp.ui.widget.validator

import android.text.Editable
import android.widget.EditText
import com.google.android.material.textfield.TextInputLayout
import com.toyota.oneapp.R
import java.util.regex.Pattern

class ZipCodeValidator(
    private val textInputLayout: TextInputLayout,
    private val editText: EditText,
    callback: Validator.Callback? = null,
) : AbstractTextInputValidator(textInputLayout, callback) {
    override fun validate(showError: <PERSON>olean): Boolean =
        PATTERN.matcher(editText.text).matches().also {
            if (showError) {
                textInputLayout.error =
                    if (it) {
                        null
                    } else {
                        textInputLayout.context.getText(R.string.AccountSettings_not_valid_zip)
                    }
            }
        }

    override fun afterTextChanged(s: Editable?) {
        if (s?.length ?: 0 > 5 && s?.contains('-') != true) {
            s?.insert(5, "-")
        } else if (s?.length ?: 0 == 6 && s?.last() == '-') {
            s.delete(5, 6)
        }
        super.afterTextChanged(s)
    }

    companion object {
        private val PATTERN = Pattern.compile("^\\d{5}$|^\\d{5}-\\d{4}$")
    }
}
