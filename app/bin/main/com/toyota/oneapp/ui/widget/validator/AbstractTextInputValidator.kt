package com.toyota.oneapp.ui.widget.validator

import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.annotation.CallSuper
import com.google.android.material.textfield.TextInputLayout

abstract class AbstractTextInputValidator(
    private val textInputLayout: TextInputLayout,
    private val callback: Validator.Callback?,
) : Validator,
    TextWatcher,
    View.OnFocusChangeListener {
    override fun onFocusChange(
        v: View?,
        hasFocus: <PERSON>olean,
    ) {
        if (!hasFocus) {
            validate(true)
        }
    }

    override fun beforeTextChanged(
        s: CharSequence?,
        start: Int,
        count: Int,
        after: Int,
    ) {
        textInputLayout.error = null
    }

    override fun onTextChanged(
        s: CharSequence?,
        start: Int,
        before: Int,
        count: Int,
    ) {
        // Do nothing
    }

    @CallSuper
    override fun afterTextChanged(s: Editable?) {
        callback?.invalidateValidation()
    }
}
