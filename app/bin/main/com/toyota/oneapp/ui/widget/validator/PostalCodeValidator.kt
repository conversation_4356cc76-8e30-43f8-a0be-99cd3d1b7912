package com.toyota.oneapp.ui.widget.validator

import android.text.TextUtils
import android.widget.EditText
import com.google.android.material.textfield.TextInputLayout
import com.toyota.oneapp.R
import java.util.regex.Pattern

class PostalCodeValidator(
    private val textInputLayout: TextInputLayout,
    private val editText: EditText,
    callback: Validator.Callback? = null,
) : AbstractTextInputValidator(textInputLayout, callback) {
    override fun validate(showError: <PERSON>olean): Boolean =
        PATTERN.matcher(editText.text).matches().also {
            var result = editText.text.toString().trim()
            if (!result.contains(" ") && result.length == 6) {
                result =
                    String.format(
                        "%s %s",
                        result.subSequence(0, 3).toString(),
                        result.subSequence(3, 6),
                    )
            }
            if (showError) {
                textInputLayout.error =
                    if (it) {
                        null
                    } else {
                        textInputLayout.context.getText(R.string.personal_info_postal_code_not_valid)
                    }
            }

            if (!TextUtils.equals(result, null)) {
                editText.removeTextChangedListener(this)
                editText.setText(result)
                editText.addTextChangedListener(this)
                editText.setSelection(editText.text.length)
            }
        }

    companion object {
        private val PATTERN = Pattern.compile("^(?!.*[DFIOQU])[A-VXY][0-9][A-Z] ?[0-9][A-Z][0-9]$")
    }
}
