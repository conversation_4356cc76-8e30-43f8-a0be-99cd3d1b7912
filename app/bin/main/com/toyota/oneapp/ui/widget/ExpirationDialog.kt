package com.toyota.oneapp.ui.widget

import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.widget.NumberPicker
import androidx.fragment.app.DialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.toyota.oneapp.R
import java.util.*

class ExpirationDialog : DialogFragment() {
    interface OnSetExpirationListener {
        fun onExpiration(
            month: Int,
            year: Int,
        )
    }

    var expirationListener: OnSetExpirationListener? = null

    var initialMonth: Int? = null
    var initialYear: Int? = null

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val calendar = Calendar.getInstance()

        val view = View.inflate(context, R.layout.dialog_expiration_date, null)
        val yearPicker = view.findViewById<NumberPicker>(R.id.year)
        yearPicker.wrapSelectorWheel = false
        yearPicker.minValue = calendar.get(Calendar.YEAR)
        yearPicker.maxValue = calendar.get(Calendar.YEAR) + 20
        initialYear?.let {
            yearPicker.value = it
        }

        val monthPicker = view.findViewById<NumberPicker>(R.id.month)
        monthPicker.wrapSelectorWheel = false
        monthPicker.minValue = 1
        monthPicker.maxValue = 12
        monthPicker.value = initialMonth ?: calendar.get(Calendar.MONTH) + 1

        return MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.PaymentPage_Expiration_Date)
            .setView(view)
            .setPositiveButton(R.string.done_label) { _, _ ->
                expirationListener?.onExpiration(monthPicker.value, yearPicker.value)
            }.create()
    }
}
