package com.toyota.oneapp.ui.widget.validator

import android.text.Editable
import android.widget.EditText
import com.google.android.material.textfield.TextInputLayout
import com.toyota.oneapp.R
import com.toyota.oneapp.util.ToyotaConstants.Companion.MX_ZIPCODE_LENGTH
import com.toyota.oneapp.util.ToyotaConstants.Companion.MX_ZIPCODE_MAX_LENGTH
import java.util.regex.Pattern

class MXZipCodeValidator(
    private val textInputLayout: TextInputLayout,
    private val editText: EditText,
    callback: Validator.Callback? = null,
) : AbstractTextInputValidator(textInputLayout, callback) {
    override fun validate(showError: Boolean): Boolean =
        PATTERN.matcher(editText.text).matches().also {
            if (showError) {
                textInputLayout.error =
                    if (it) {
                        null
                    } else {
                        textInputLayout.context.getText(R.string.AccountSettings_not_valid_zip)
                    }
            }
        }

    override fun afterTextChanged(s: Editable?) {
        s?.run {
            when {
                length > MX_ZIPCODE_MAX_LENGTH && contains('-') -> {
                    insert(MX_ZIPCODE_MAX_LENGTH, "-")
                }
                length == MX_ZIPCODE_LENGTH && last() == '-' -> {
                    delete(MX_ZIPCODE_MAX_LENGTH, MX_ZIPCODE_LENGTH)
                }
                else -> { // Not Implemented
                }
            }
        }
        super.afterTextChanged(s)
    }

    companion object {
        private val PATTERN = Pattern.compile("^\\d{5}$")
    }
}
