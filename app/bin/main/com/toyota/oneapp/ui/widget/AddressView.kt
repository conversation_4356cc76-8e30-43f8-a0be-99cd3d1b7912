package com.toyota.oneapp.ui.widget

import android.content.Context
import android.content.res.Resources
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.inputmethod.EditorInfo
import android.widget.ArrayAdapter
import androidx.annotation.StringRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.AddressBinding
import com.toyota.oneapp.model.account.CustomerAddress
import com.toyota.oneapp.ui.widget.validator.MXZipCodeValidator
import com.toyota.oneapp.ui.widget.validator.NonBlankValidator
import com.toyota.oneapp.ui.widget.validator.PostalCodeValidator
import com.toyota.oneapp.ui.widget.validator.Validator
import com.toyota.oneapp.ui.widget.validator.ZipCodeValidator
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.ToyotaConstants.Companion.REGION_MX
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

/**
 * Displays a form to view and edit address data.
 *
 * Use #onValidListener to get a callback if the form is valid or not.
 *
 * @attr ref R.styleable#AddressView_showLine2
 * @attr ref R.styleable#AddressView_line1Hint
 * @attr ref R.styleable#AddressView_line2Hint
 */
class AddressView :
    ConstraintLayout,
    Validator.Callback {
    private lateinit var binding: AddressBinding

    private var countryCodes: Array<String>
    private lateinit var stateCodes: Array<String>

    private val usZipValidator: ZipCodeValidator
    private val caPostalValidator: PostalCodeValidator
    private val mxZipValidator: MXZipCodeValidator
    private val nonBlankValidator: NonBlankValidator

    private val validators = mutableSetOf<Validator>()

    private var onValidListener: OnValidListener? = null

    var address: Address =
        object : Address {
            override var line1: String? = null
            override var line2: String? = null
            override var city: String? = null
            override var stateCode: String? = null
            override var zipCode: String? = null
            override var countryCode: String? = null
        }
        set(value) {
            value.countryCode?.let {
                setSelectedCountry(it)
            }
            setSelectedState(value.stateCode)
            binding.line1.setText(value.line1)
            binding.line2.setText(value.line2)
            binding.city.setText(value.city)
            binding.zipCode.setText(value.zipCode)
            field = value
            binding.executePendingBindings()
            invalidateValidation()
        }

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(
        context,
        attrs,
        R.attr.addressFormStyle,
    )

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    ) {
        if (isInEditMode) {
            inflate(context, R.layout.address, this)
        } else {
            binding =
                DataBindingUtil.inflate(
                    LayoutInflater.from(context),
                    R.layout.address,
                    this,
                    true,
                )
        }

        val a =
            context.theme.obtainStyledAttributes(
                attrs,
                R.styleable.AddressView,
                defStyleAttr,
                0,
            )

        binding.line2Layout.isVisible = a.getBoolean(R.styleable.AddressView_showLine2, false)
        binding.line2.setText(address.line2)
        binding.zipCode.setText(address.zipCode)
        a.getString(R.styleable.AddressView_line1Hint)?.let { binding.line1Layout.hint = it }
        a.getString(R.styleable.AddressView_line2Hint)?.let { binding.line2Layout.hint = it }

        usZipValidator = ZipCodeValidator(binding.zipCodeLayout, binding.zipCode, this)
        caPostalValidator = PostalCodeValidator(binding.zipCodeLayout, binding.zipCode, this)
        mxZipValidator = MXZipCodeValidator(binding.zipCodeLayout, binding.zipCode, this)
        nonBlankValidator =
            NonBlankValidator(
                binding.zipCodeLayout,
                binding.zipCode,
                R.string.AccountSettings_not_valid_zip,
                this,
            )

        countryCodes = resources.getStringArray(R.array.country_codes)

        if (!isInEditMode) {
            binding.country.setAdapter(
                ArrayAdapter(
                    context,
                    R.layout.item_state,
                    resources.getTextArray(R.array.country_names),
                ),
            )
        }

        resetCountryDependents(countryCodes.first())

        binding.country.setOnItemClickListener { _, _, position, _ ->
            countryCodes[position].let {
                if (address.countryCode != it) {
                    address.countryCode = it
                    binding.countryLayout.error = null
                    resetCountryDependents(it)
                    invalidateValidation()
                }
            }
        }

        binding.state.setOnItemClickListener { _, _, position, _ ->
            address.stateCode = stateCodes[position]
            binding.stateLayout.error = null
            invalidateValidation()
        }

        validators +=
            NonBlankValidator(
                binding.line1Layout,
                binding.line1,
                R.string.AccountSettings_street_address_cant_be_blank,
                this,
            ).also {
                binding.line1.addTextChangedListener(it)
                binding.line1.onFocusChangeListener = it
                DataBindingAdapters.setRegexFilter(binding.line1, context.getString(R.string.address_regex))
            }
        binding.line1.doAfterTextChanged {
            address.line1 = it?.toString()
        }
        validators +=
            NonBlankValidator(
                binding.cityLayout,
                binding.city,
                R.string.AccountSettings_city_cant_be_blank,
                this,
            ).also {
                binding.city.addTextChangedListener(it)
                binding.city.onFocusChangeListener = it
                binding.city.setText(address.city)
                DataBindingAdapters.setRegexFilter(binding.city, context.getString(R.string.address_city_regex))
            }
        binding.city.doAfterTextChanged {
            address.city = it?.toString()
        }
        validators +=
            object : Validator {
                override fun validate(showError: Boolean): Boolean =
                    (address.countryCode != null).also {
                        if (showError && !it) {
                            binding.countryLayout.error =
                                binding.countryLayout.context.getText(
                                    R.string.AccountSettings_country_cant_be_blank,
                                )
                        }
                    }
            }
        validators +=
            object : Validator {
                override fun validate(showError: Boolean): Boolean =
                    (address.stateCode != null).also {
                        if (showError && !it) {
                            binding.stateLayout.error =
                                binding.stateLayout.context.getText(
                                    R.string.AccountSettings_state_cant_be_blank,
                                )
                        }
                    }
            }
        a.recycle()
    }

    fun setAddress(customerAddress: CustomerAddress) {
        address =
            object : Address {
                override var line1: String? = customerAddress.address
                override var line2: String? = null
                override var city: String? = customerAddress.city
                override var stateCode: String? = customerAddress.state
                override var zipCode: String? = customerAddress.zipCode
                override var countryCode: String? = customerAddress.country
            }
        binding.line1.setText(customerAddress.address)
        binding.city.setText(customerAddress.city)
        binding.zipCode.setText(customerAddress.zipCode)
        customerAddress.country?.let {
            setSelectedCountry(it)
        }
        setSelectedState(customerAddress.state)
        binding.executePendingBindings()
    }

    fun setOnValidListener(listener: OnValidListener?) {
        onValidListener = listener
        invalidateValidation()
    }

    private fun setSelectedState(code: String?) {
        stateCodes.indexOf(code).let { index ->
            if (index == -1) {
                binding.state.setText(code, false)
            } else {
                address.stateCode = code
                binding.state.setText(binding.state.adapter.getItem(index) as? CharSequence, false)
            }
        }
    }

    fun setSelectedCountry(isCanadian: Boolean) {
        setSelectedCountry(if (isCanadian) Country.CA.countryCode else Country.US.countryCode)
    }

    private fun setSelectedCountry(code: String) {
        if (address.countryCode != code) {
            countryCodes
                .indexOf(code)
                .let {
                    if (it == -1) {
                    /*
                    Special cases: Payment methods added via WebView are returned in full text form
                    instead of country codes used throughout CTP. I.E. "United States" instead of
                    "US" or "Canada" instead of "CA". Attempt to match some of these cases to our
                    known forms otherwise, just use the raw value sent to us. ("Denmark")
                     */
                        if (code == "United States") {
                            return@let countryCodes.indexOf(Country.US.countryCode)
                        } else {
                            val size = binding.country.adapter.count - 1
                            for (index in 0..size) {
                                var a = binding.country.adapter.getItem(index)
                                if (binding.country.adapter
                                        .getItem(index)
                                        .toString() == code
                                ) {
                                    return@let index
                                }
                            }
                        }
                    }
                    it
                }.let {
                    binding.country.setText(
                        if (it != -1) binding.country.adapter.getItem(it) as? CharSequence else null ?: code,
                        false,
                    )
                    address.countryCode =
                        (if (it == -1) code else countryCodes[it])
                            .also(this@AddressView::resetCountryDependents)
                }
        }
    }

    fun validate(): Boolean = validators.all { it.validate(true) }

    override fun invalidateValidation() {
        onValidListener?.onValid(validators.all { it.validate(false) })
    }

    private fun resetStates(country: Country) {
        binding.stateLayout.hint = resources.getText(country.stateCodeHintRes)
        stateCodes = country.stateCodes.invoke(resources)

        if (!isInEditMode) {
            binding.state.setAdapter(
                country.stateValues.invoke(resources)?.let {
                    ArrayAdapter(
                        context,
                        R.layout.item_state,
                        it,
                    )
                },
            )
        }
        binding.state.setText(null, false)
        address.stateCode = null
    }

    private fun resetZipCode(country: Country) {
        binding.zipCodeLayout.hint = resources.getText(country.zipCodeHintRes)
        binding.zipCode.run {
            inputType = country.zipCodeInputType
            text = null
        }

        binding.zipCode.removeTextChangedListener(usZipValidator)
        binding.zipCode.removeTextChangedListener(caPostalValidator)
        binding.zipCode.removeTextChangedListener(mxZipValidator)
        binding.zipCode.removeTextChangedListener(nonBlankValidator)

        validators.removeAll(
            arrayOf(usZipValidator, caPostalValidator, mxZipValidator, nonBlankValidator),
        )

        when (country) {
            Country.US -> usZipValidator
            Country.CA -> caPostalValidator
            Country.MX -> mxZipValidator
            Country.OTHER -> nonBlankValidator
        }.let {
            binding.zipCode.addTextChangedListener(it)
            binding.zipCode.onFocusChangeListener = it
            validators.add(it)
        }

        binding.zipCode.doAfterTextChanged {
            address.zipCode = it.toString()
        }
    }

    private fun resetCountryDependents(countryCode: String) {
        (Country.values().firstOrNull { it.countryCode == countryCode } ?: Country.OTHER).let {
            resetStates(it)
            resetZipCode(it)
        }
    }

    private enum class Country(
        val countryCode: String,
        val stateCodes: ((Resources) -> Array<String>),
        val stateValues: ((Resources) -> Array<CharSequence>?),
        @StringRes val zipCodeHintRes: Int,
        @StringRes val stateCodeHintRes: Int,
        val zipCodeInputType: Int,
    ) {
        US(
            "US",
            { it.getStringArray(R.array.state_codes) },
            {
                it.getTextArray(
                    when {
                        AppLanguageUtils.isSpanish() -> R.array.state_full_simple_names_es
                        AppLanguageUtils.isFrench() -> R.array.state_full_simple_names_fr
                        else -> R.array.state_full_simple_names
                    },
                )
            },
            R.string.Consents_zip_code,
            R.string.BillingAddress_state,
            EditorInfo.TYPE_NUMBER_FLAG_SIGNED,
        ),
        CA(
            "CA",
            { it.getStringArray(R.array.canada_state_codes) },
            {
                it.getTextArray(
                    when {
                        AppLanguageUtils.isSpanish() -> R.array.canada_state_full_simple_names_es
                        AppLanguageUtils.isFrench() -> R.array.canada_state_full_simple_names_fr
                        else -> R.array.canada_state_full_simple_names
                    },
                )
            },
            R.string.personal_info_postal_code,
            R.string.BillingAddress_province,
            EditorInfo.TYPE_CLASS_TEXT.or(EditorInfo.TYPE_TEXT_FLAG_CAP_CHARACTERS),
        ),
        MX(
            REGION_MX,
            { it.getStringArray(R.array.mexico_state_codes) },
            {
                it.getTextArray(
                    when {
                        AppLanguageUtils.isSpanish() -> R.array.mx_state_full_simple_names_es
                        else -> R.array.mx_state_full_simple_names
                    },
                )
            },
            R.string.Consents_zip_code,
            R.string.BillingAddress_state,
            EditorInfo.TYPE_NUMBER_FLAG_SIGNED,
        ),
        OTHER(
            "",
            { emptyArray() },
            { null },
            R.string.Consents_zip_code,
            R.string.BillingAddress_state,
            EditorInfo.TYPE_CLASS_TEXT,
        ),
    }

    interface OnValidListener {
        fun onValid(isValid: Boolean)
    }

    interface Address {
        var line1: String?
        var line2: String?
        var city: String?
        var stateCode: String?
        var zipCode: String?
        var countryCode: String?
    }
}

fun AddressView.Address.toCustomerAddress(type: String): CustomerAddress =
    CustomerAddress(
        type,
        line1 ?: "",
        city,
        stateCode,
        zipCode,
        countryCode ?: "US",
    )
