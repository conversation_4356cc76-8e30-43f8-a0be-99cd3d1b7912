package com.toyota.oneapp.ui.widget

import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.Interpolator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.*
import kotlin.math.max

/**
 * CirclePagerIndicatorDecoration is an ItemDecoration that can be applied both vertically and
 * horizontally.
 *
 * A builder pattern is implemented to customize padding, length width and color of the
 * decoration
 */
class CircleIndicatorDecoration() : ItemDecoration() {
    private var colorActive: Int
    private var colorInactive: Int
    private var indicatorWidth: Float
    private var indicatorLength: Float
    private var indicatorPadding: Float
    private var orientation: Int

    private val mInterpolator: Interpolator = AccelerateDecelerateInterpolator()
    private val mPaint: Paint = Paint()

    init {
        mPaint.strokeWidth = DEFAULT_INDICATOR_STROKE_WIDTH
        mPaint.style = Paint.Style.FILL_AND_STROKE
        mPaint.isAntiAlias = true
        this.colorActive = DEFAULT_COLOR_ACTIVE
        this.colorInactive = DEFAULT_COLOR_INACTIVE
        this.indicatorWidth = DEFAULT_INDICATOR_WIDTH
        this.indicatorLength = DEFAULT_INDICATOR_LENGTH
        this.indicatorPadding = DEFAULT_INDICATOR_PADDING + indicatorLength
        this.orientation = DEFAULT_ORIENTATION
    }

    constructor(
        colorActive: Int,
        colorInactive: Int,
        indicatorWidth: Float,
        indicatorLength: Float,
        indicatorPadding: Float,
        orientation: Int,
    ) : this() {
        this.colorActive = colorActive
        this.colorInactive = colorInactive
        this.indicatorWidth = indicatorWidth
        this.indicatorLength = indicatorLength
        this.indicatorPadding = indicatorPadding
        this.orientation = orientation
    }

    internal class Builder() {
        private var colorActive = DEFAULT_COLOR_ACTIVE
        private var colorInactive = DEFAULT_COLOR_INACTIVE
        private var indicatorWidth = DEFAULT_INDICATOR_WIDTH
        private var indicatorLength = DEFAULT_INDICATOR_LENGTH
        private var indicatorPadding = DEFAULT_INDICATOR_PADDING + indicatorLength
        private var orientation = DEFAULT_ORIENTATION

        constructor(decoration: CircleIndicatorDecoration) : this() {
            this.colorActive = decoration.colorActive
            this.colorInactive = decoration.colorInactive
            this.indicatorWidth = decoration.indicatorWidth
            this.indicatorLength = decoration.indicatorLength
            this.indicatorPadding = decoration.indicatorPadding + indicatorLength
            this.orientation = decoration.orientation
        }

        fun setColorActive(colorActive: Int): Builder {
            this.colorActive = colorActive
            return this
        }

        fun setColorInactive(colorInactive: Int): Builder {
            this.colorInactive = colorInactive
            return this
        }

        fun setIndicatorWidth(indicatorWidth: Float): Builder {
            this.indicatorWidth = indicatorWidth * DP
            return this
        }

        fun setIndicatorLength(indicatorLength: Float): Builder {
            this.indicatorLength = indicatorLength * DP
            return this
        }

        fun setIndicatorPadding(indicatorPadding: Float): Builder {
            this.indicatorPadding = indicatorPadding * DP + indicatorLength
            return this
        }

        fun setOrientation(orientation: Int): Builder {
            this.orientation = orientation
            return this
        }

        fun build(): CircleIndicatorDecoration =
            CircleIndicatorDecoration(
                colorActive,
                colorInactive,
                indicatorWidth,
                indicatorLength,
                indicatorPadding,
                orientation,
            )
    }

    override fun onDrawOver(
        c: Canvas,
        parent: RecyclerView,
        state: State,
    ) {
        super.onDrawOver(c, parent, state)

        // if orientation is not explicitly set, we will look for the orientation of the view and
        // implement accordingly. If we are not using a LinearLayoutManager and cannot know the
        // orientation of the view, will default to horizontal
        if (orientation == -1 && parent.layoutManager is LinearLayoutManager) {
            orientation = (parent.layoutManager as LinearLayoutManager).orientation
        }

        val itemCount = parent.adapter!!.itemCount

        // center vertically, calculate height and subtract half from center
        val totalLength = indicatorLength * itemCount
        val paddingBetweenItems = max(0, itemCount - 1) * indicatorPadding
        val indicatorLengthWithPadding = totalLength + paddingBetweenItems
        val indicatorStart =
            if (orientation == VERTICAL) {
                (parent.height - indicatorLengthWithPadding) / 2F
            } else {
                (parent.width - indicatorLengthWithPadding) / 2F
            }

        // center horizontally in the allotted space
        val indicatorPos =
            if (orientation == VERTICAL) {
                parent.width - indicatorWidth
            } else {
                parent.height - indicatorWidth / 2F
            }

        // find active page (which should be highlighted)
        val layoutManager = parent.layoutManager as LinearLayoutManager?
        val activePosition = layoutManager!!.findFirstVisibleItemPosition()
        if (activePosition == NO_POSITION) {
            return
        }

        // find offset of active page (if the user is scrolling)
        val activeChild: View? = layoutManager.findViewByPosition(activePosition)
        val animStart: Int? = activeChild?.top
        val animEnd: Int? = activeChild?.height

        // on swipe the active item will be positioned from [-(height|width), 0]
        // interpolate offset for smooth animation
        val progress: Float =
            mInterpolator
                .getInterpolation((animStart?.times(-1) ?: 0) / animEnd?.toFloat()!!)

        val x =
            if (orientation == VERTICAL) {
                indicatorPos
            } else {
                indicatorStart
            }
        val y =
            if (orientation == VERTICAL) {
                indicatorStart
            } else {
                indicatorPos
            }
        drawInactiveIndicators(c, x, y, itemCount)
        drawHighlights(c, x, y, activePosition, progress)
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: State,
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        outRect.bottom = indicatorLength.toInt()
    }

    private fun drawInactiveIndicators(
        c: Canvas,
        indicatorX: Float,
        indicatorY: Float,
        itemCount: Int,
    ) {
        mPaint.color = DEFAULT_COLOR_INACTIVE

        // Length of item indicator including padding
        val itemLength = indicatorLength + indicatorPadding
        var x = indicatorX
        var y = indicatorY
        for (i in 0 until itemCount) {
            c.drawCircle(x, y, indicatorLength / 2f, mPaint)
            if (orientation == VERTICAL) {
                y += itemLength
            } else {
                x += itemLength
            }
        }
    }

    private fun drawHighlights(
        c: Canvas,
        indicatorX: Float,
        indicatorY: Float,
        highlightPosition: Int,
        progress: Float,
    ) {
        mPaint.color = colorActive

        // Length of item indicator including padding
        val itemLength = indicatorLength + indicatorPadding
        val indicatorStart =
            if (orientation == VERTICAL) {
                indicatorY
            } else {
                indicatorX
            }
        if (progress == 0f) {
            val highlightStart = indicatorStart + itemLength * highlightPosition
            val x: Float
            val y: Float

            if (orientation == VERTICAL) {
                x = indicatorX
                y = highlightStart
            } else {
                x = highlightStart
                y = indicatorY
            }

            c.drawCircle(x, y, indicatorLength / 2f, mPaint)
        } else {
            val highlightStart = indicatorStart + itemLength * highlightPosition
            // calculate highlight as animation progresses
            val partialLength = indicatorLength * progress + indicatorPadding * progress
            val x: Float
            val y: Float
            if (orientation == VERTICAL) {
                x = indicatorX
                y = highlightStart + partialLength
            } else {
                x = highlightStart + partialLength
                y = indicatorY
            }
            c.drawCircle(x, y, indicatorLength / 2f, mPaint)
        }
    }

    companion object {
        private const val DEFAULT_ORIENTATION = -1
        private val DP: Float = Resources.getSystem().displayMetrics.density
        private val DEFAULT_COLOR_ACTIVE = Color.parseColor("#262626")
        private val DEFAULT_COLOR_INACTIVE = Color.parseColor("#DEDEDE")
        private val DEFAULT_INDICATOR_WIDTH = (DP * 22)
        private val DEFAULT_INDICATOR_STROKE_WIDTH = DP * 4
        private val DEFAULT_INDICATOR_LENGTH = DP * 4
        private val DEFAULT_INDICATOR_PADDING = DP * 8
    }
}
