package com.toyota.oneapp.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentActivity
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.CreditCardBinding
import com.toyota.oneapp.ui.widget.validator.NonBlankValidator
import com.toyota.oneapp.ui.widget.validator.Validator
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class CreditCardView : ConstraintLayout {
    private val binding: CreditCardBinding

    private val validators: Array<Validator>
    private var creditCard: CreditCard? = null

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(
        context,
        attrs,
        R.attr.addressFormStyle,
    )

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    ) {
        binding =
            DataBindingUtil.inflate(
                LayoutInflater.from(context),
                R.layout.credit_card,
                this,
                true,
            )

        validators =
            arrayOf(
                NonBlankValidator(binding.cvvLayout, binding.cvv, R.string.required, null),
            )

        binding.number.isCursorVisible = false

        binding.expiration.setOnClickListener {
            (context as? FragmentActivity)?.let {
                ExpirationDialog()
                    .apply {
                        initialYear = creditCard?.expirationYear
                        initialMonth = creditCard?.expirationMonth

                        expirationListener =
                            object : ExpirationDialog.OnSetExpirationListener {
                                override fun onExpiration(
                                    month: Int,
                                    year: Int,
                                ) {
                                    creditCard?.apply {
                                        expirationMonth = month
                                        expirationYear = year
                                    }
                                    binding.invalidateAll()
                                    binding.executePendingBindings()
                                }
                            }
                    }.show(it.supportFragmentManager, null)
            }
        }
    }

    fun setCreditCard(creditCard: CreditCard?) {
        this.creditCard = creditCard

        creditCard?.apply {
            binding.name.setText(creditCard.name)
            binding.number.setText(creditCard.number)
            binding.cvv.setText(creditCard.cvv)
            DataBindingAdapters.setExpiration(binding.expiration, creditCard.expirationMonth ?: 0, creditCard.expirationYear ?: 0)
        }

        binding.executePendingBindings()
    }

    fun validate() = validators.all { it.validate(true) }

    interface CreditCard {
        var name: String?
        var number: String?
        var expirationMonth: Int?
        var expirationYear: Int?
        var cvv: String?
    }
}
