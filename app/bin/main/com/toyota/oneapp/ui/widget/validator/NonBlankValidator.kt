package com.toyota.oneapp.ui.widget.validator

import android.widget.EditText
import androidx.annotation.StringRes
import com.google.android.material.textfield.TextInputLayout

class NonBlankValidator(
    private val textInputLayout: TextInputLayout,
    private val editText: EditText,
    @StringRes private val errorRes: Int,
    callback: Validator.Callback? = null,
) : AbstractTextInputValidator(textInputLayout, callback) {
    override fun validate(showError: Boolean): Boolean =
        editText.text.isNullOrBlank().not().also {
            if (showError && !it) {
                textInputLayout.error = textInputLayout.context.getText(errorRes)
            }
        }
}
