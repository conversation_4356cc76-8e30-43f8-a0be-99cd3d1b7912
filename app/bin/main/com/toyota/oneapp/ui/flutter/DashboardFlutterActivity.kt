package com.toyota.oneapp.ui.flutter

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Bundle
import android.widget.FrameLayout
import androidx.appcompat.app.AlertDialog
import androidx.compose.material.ExperimentalMaterialApi
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.FragmentManager
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.material.snackbar.Snackbar
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.component.receiver.NetworkStateReceiver
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BasePresenter
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ProgressDialogFragment
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import io.flutter.embedding.android.FlutterFragment
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

const val DASHBOARD_ENGINE_ID = "DASHBOARD_ENGINE_ID"
private const val TAG_FLUTTER_ENTRANCE_FRAGMENT = "oneapp_flutter_entrance_fragment"
const val GO_TO_DASHBOARD = "GO_TO_DASHBOARD"
const val GO_TO_APPOINTMENT_DETAIL = "GO_TO_APPOINTMENT_DETAIL"
const val GO_TO_CLIMATE_DETAIL = "GO_TO_CLIMATE_DETAIL"
const val GO_TO_GUEST_DRIVER_DETAIL = "GO_TO_GUEST_DRIVER_DETAIL"
const val Flutter_Appointment_Detail = "Flutter_Appointment_Detail"
const val Flutter_Station = "Flutter_Station"
const val Flutter_Dealer = "Flutter_Dealer"
const val Flutter_Rentals = "Flutter_Rentals"
const val Flutter_Drive_Pulse_Trips = "Flutter_Drive_Pulse_Trips"
const val Flutter_Select_Preferred_Dealer = "Flutter_Select_Preferred_Dealer"
const val Flutter_Preferred_Dealer = "Flutter_Preferred_Dealer"
const val Flutter_Vehicle_announcements = "Flutter_Vehicle_announcements"
const val Flutter_Vehicle_health_Detail = "Flutter_Vehicle_health_Detail"
const val Flutter_SelectPreferred_Dealer_for_Health = "Flutter_SelectPreferred_Dealer_for_Health"
const val GO_TO_WALLET_HOME = "GO_TO_WALLET_HOME"
const val GO_TO_FIND_STATIONS = "GO_TO_FIND_STATIONS"
const val GO_TO_EV_CHARGE_INFO_PAGE = "EV_CHARGE_INFO_PAGE"
const val GO_TO_VEHICLE_CHARGE_MANAGEMENT_SCREEN = "VEHICLE_CHARGE_MANAGEMENT_SCREEN"
const val GO_TO_EV_CHARGE_MANAGEMENT_PAGE = "DRIVER_CHARGE_MANAGEMENT_PAGE"
const val GO_TO_EV_ACTIVE_CHARGE_SESSION_PAGE = "EV_ACTIVE_CHARGE_SESSION_PAGE"
const val GO_TO_MAINTENANCE_SCHEDULE = "MAINTANANCE_SCHEDULE"
const val GO_TO_SERVICE_HISTORY = "SERVICE_HISTORY"
const val GO_TO_APPOINTMENTS = "APPOINTMENTS"
const val GO_TO_VEHICLE_INFO_SOFTWARE_UPDATE = "VEHICLE_INFO_SOFTWARE_UPDATE"
const val GO_TO_EV_SWAP = "VEHICLE_ANNOUNCEMENT_EV_SWAP_OVERVIEW"
const val GO_TO_CLEAN_ASSIST_DETAIL = "CLEAN_ASSIST_DETAIL_PAGE"
const val GO_TO_ACCOUNT_OPTIONS = "GO_TO_ACCOUNT_OPTIONS"
const val GO_TO_PAYMENTS = "GO_TO_PAYMENTS"
const val GO_TO_REQUEST_OTP = "GO_TO_REQUEST_OTP"
const val GO_TO_GLOVE_BOX = "GO_TO_GLOVE_BOX"
const val LINK_ACCOUNT = "LINK_ACCOUNT"
const val OPEN_ONLINE_POLICY = "OPEN_ONLINE_POLICY"
const val OPEN_ELECTRONIC_AGREEMENT = "OPEN_ELECTRONIC_AGREEMENT"
const val ACCOUNTS = "account"
const val REQUEST_OTP = "otp"

@OptIn(ExperimentalMaterialApi::class)
@AndroidEntryPoint
class DashboardFlutterActivity :
    UiBaseActivity(),
    NetworkStateReceiver.NetworkStateChangeListener,
    BasePresenter.BaseView {
    private lateinit var snackBarView: Snackbar
    private var progressDialogFragment: ProgressDialogFragment? = null
    private lateinit var netWorkStateReceiver: NetworkStateReceiver
    private var filter = IntentFilter()
    private var isOffline = false

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var oneAppPreferenceModel: OneAppPreferenceModel

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    override fun onCreate(savedInstance: Bundle?) {
        LogTool.d("****", "onCreate")
        super.onCreate(savedInstance)
        setContentView(R.layout.activity_flutter_entrance)

        WindowInsetsControllerCompat(window, window.decorView).isAppearanceLightStatusBars = !oneAppPreferenceModel.isDarkModeEnabled()

        findViewById<FrameLayout>(R.id.flutterEntranceFragmentContainer).setBackgroundColor(
            getColor(
                if (oneAppPreferenceModel.isDarkModeEnabled()) {
                    org.forgerock.android.auth.ui.R.color.Black
                } else {
                    R.color.white
                },
            ),
        )

        initProgressFragment()
        initNetworkStatusSnackBar()

        val fragmentManager: FragmentManager = supportFragmentManager
        flutterFragment = fragmentManager.findFragmentByTag(TAG_FLUTTER_ENTRANCE_FRAGMENT) as FlutterMainFragment?

        if (flutterFragment == null) {
            val newFlutterFragment = CacheBuilder().build<FlutterMainFragment>()
            flutterFragment = newFlutterFragment
            fragmentManager
                .beginTransaction()
                .add(
                    R.id.flutterEntranceFragmentContainer,
                    newFlutterFragment,
                    TAG_FLUTTER_ENTRANCE_FRAGMENT,
                ).commit()
        }
        if (intent.extras != null) {
            flutterFragment?.extraBundle?.putAll(intent.extras)
        }
        flutterFragment?.intentUri = intent.data
    }

    override fun onResume() {
        super.onResume()
        LogTool.d("****", "onResume")
        netWorkStateReceiver = NetworkStateReceiver(this@DashboardFlutterActivity)
        filter.addCategory(Intent.CATEGORY_DEFAULT)
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION)
        filter.addAction(ToyotaConstants.CHECK_NETWORK_ACTION)
        registerReceiver(netWorkStateReceiver, filter, RECEIVER_NOT_EXPORTED)
        LocalBroadcastManager.getInstance(this).registerReceiver(netWorkStateReceiver, filter)
    }

    override fun onStart() {
        super.onStart()
        LogTool.d("****", "onStart")
    }

    override fun onRestart() {
        super.onRestart()
        LogTool.d("****", "onRestart")
        if (launchRemotePark) {
            analyticsLogger.logEvent(AnalyticsEvent.REMOTE_PARKING_CARD_STOP)
            launchRemotePark = false
        }
    }

    override fun onStop() {
        super.onStop()
        LogTool.d("****", "onStop")
    }

    override fun onPause() {
        super.onPause()
        LogTool.d("****", "onPause")
        LocalBroadcastManager.getInstance(this).unregisterReceiver(netWorkStateReceiver)
        unregisterReceiver(netWorkStateReceiver)
    }

    override fun onDestroy() {
        LogTool.d("****", "onDestory")
        progressDialogFragment = null
        try {
            super.onDestroy()
        } catch (e: Exception) {
            LogTool.d("****", "Error $e: ")
        }
    }

    override fun onPostResume() {
        try {
            super.onPostResume()
            flutterFragment?.onPostResume()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        LogTool.d("****", "onNewIntent")
        flutterFragment?.onNewIntent(intent)
    }

    override fun onBackPressed() {
        super.onBackPressed()
        flutterFragment?.onBackPressed()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (permissions.isNotEmpty() && grantResults.isNotEmpty()) { // TODO: Upgrade targetSdk to 33 and remove this condition
            flutterFragment?.onRequestPermissionsResult(
                requestCode,
                permissions,
                grantResults,
            )
        }
    }

    override fun onUserLeaveHint() {
        LogTool.d("****", "onUserLeaveHint")
        flutterFragment?.onUserLeaveHint()
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        LogTool.d("****", "onTrimMemory")
        flutterFragment?.onTrimMemory(level)
    }

    companion object {
        const val REFRESH_DASHBOARD = "DashboardRefresh"
        const val UPDATE_DK_VEHICLE = "updateDkVehicle"
        var flutterFragment: FlutterMainFragment? = null
        var launchRemotePark = false

        @JvmStatic
        fun createIntent(
            context: Context,
            bundle: Bundle? = null,
            screen: String,
        ): Intent {
            val intent = Intent(context, DashboardFlutterActivity::class.java)
            intent.putExtra(screen, true)
            if (bundle != null) {
                intent.putExtras(bundle)
            }
            return intent
        }
    }

    override fun onNetworkStateChanged(
        isConnected: Boolean,
        isFromAPICheck: Boolean,
    ) {
        if (isOffline == isConnected) {
            isOffline = !isConnected
        }
        if (isConnected) {
            removeSnackBar()
        } else {
            showNoNetworkDialog()
        }
    }

    private fun initProgressFragment() {
        progressDialogFragment = ProgressDialogFragment()
    }

    private fun initNetworkStatusSnackBar() {
        snackBarView =
            Snackbar.make(
                findViewById(android.R.id.content),
                resources.getString(R.string.no_internet_connection_snac_msg),
                Snackbar.LENGTH_INDEFINITE,
            )
    }

    private fun showCustomDialog(
        message: String?,
        callback: (() -> Unit)? = null,
    ): AlertDialog {
        val msg =
            if (message?.isNotEmpty() == true && !message.isBlank()) {
                message
            } else {
                getString(R.string.generic_error)
            }

        return DialogUtil.showDialog(
            this,
            null,
            msg,
            getString(R.string.Common_ok),
            null,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    callback?.invoke()
                }

                override fun onCancelClick() {
                }
            },
            true,
        )
    }

    override fun showProgressDialog() {
        try {
            progressDialogFragment?.showNow(supportFragmentManager, "ProgressDialog")
        } catch (e: Exception) {
            LogTool.d("Progress Dialog Exception", e.toString())
        }
    }

    override fun hideProgressDialog() {
        try {
            progressDialogFragment?.dismiss()
        } catch (e: Exception) {
            LogTool.d("Progress Dialog Exception", e.toString())
        }
    }

    override fun getActivityContext(): Context = this@DashboardFlutterActivity

    override fun showUnsupportedDemoDialog() {
        DialogUtil.showDialog(
            this,
            null,
            getString(R.string.common_not_support_in_demo),
            getString(
                R.string.Common_ok,
            ),
        )
    }

    override fun showGenericErrorMessage() {
        showMessage()
    }

    override fun showDialog(message: String?) {
        showCustomDialog(message)
    }

    override fun showMessage(errorMsg: String?) {
        if (!errorMsg.isNullOrBlank()) {
            showDialog(errorMsg)
        } else {
            showMessage()
        }
    }

    override fun showProxyDetectedAlert() {
        DialogUtil
            .showDialog(
                this,
                getString(R.string.VPN_Detected),
                getString(R.string.VPN_Message),
                getString(R.string.ManagePaidSubscription_Try_Again),
                null,
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        onResume()
                    }

                    override fun onCancelClick() {}
                },
                false,
            ).setCanceledOnTouchOutside(false)
    }
}

@OptIn(ExperimentalMaterialApi::class)
class CacheBuilder :
    FlutterFragment.CachedEngineFragmentBuilder(
        FlutterMainFragment::class.java,
        DASHBOARD_ENGINE_ID,
    )
