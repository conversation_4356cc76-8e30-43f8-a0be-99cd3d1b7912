package com.toyota.oneapp.ui.flutter

data class TFSServiceConfig(
    val evBaseURL: String,
    val oneAppBaseURL: String,
    val tfsAuthenticateLexusURL: String,
    val tfsAuthenticateToyotaURL: String,
    val tfsAuthorizeLexusURL: String,
    val tfsAuthorizeToyotaURL: String,
    val tfsEEFLexusURL: String,
    val tfsEEFToyotaURL: String,
    val tfsAddBankURL: String,
    val tfsAddBankZuoraURL: String,
    val tfsAddBankZuoraHost: String,
    val evxApiKey: String,
    val oneAppApiKey: String,
    val tfsFrClientSecret: String,
    val tfsAddBankPageId: String,
)
