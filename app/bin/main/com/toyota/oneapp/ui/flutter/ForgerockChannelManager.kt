package com.toyota.oneapp.ui.flutter

import android.app.Activity
import android.location.Location
import android.text.TextUtils
import com.google.gson.Gson
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.component.IDPHelper
import com.toyota.oneapp.model.pref.SharePreferenceHelper
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.ToyotaConstants
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.forgerock.android.auth.exception.AuthenticationRequiredException
import org.forgerock.android.auth.ui.FRNative
import org.forgerock.android.auth.ui.FRNativeResponse
import org.forgerock.android.auth.ui.FRNativeResultListener
import java.util.Locale
import kotlin.coroutines.CoroutineContext

/**
 * Created by Daniel on 2021/12/11.
 */

const val FR_METHOD_CHANNEL = "FR_METHOD_CHANNEL"
const val FR_EVENT_CALLBACK_CHANNEL = "FR_TOKEN_EVENT_CHANNEL"
const val FR_REFRESH_TOKEN_EVENT_CHANNEL = "FR_REFRESH_TOKEN_EVENT_CHANNEL"

class ForgerockChannelManager(
    private val logoutCallback: (() -> Unit)? = null,
    val applicationData: ApplicationData,
    val oneAppPreferenceModel: OneAppPreferenceModel,
) : MethodChannel.MethodCallHandler {
    private var parentJob = Job()
    private val coroutineContext: CoroutineContext
        get() = parentJob + Dispatchers.Main
    private val scope = CoroutineScope(coroutineContext)

    private lateinit var channel: MethodChannel

    var activity: Activity? = null

    private var tokenReceiverEvent: EventChannel.EventSink? = null

    private var refreshTokenReceiverEvent: EventChannel.EventSink? = null

    fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        initChannel(flutterEngine)
    }

    private fun initChannel(flutterEngine: FlutterEngine) {
        channel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, FR_METHOD_CHANNEL)
        channel.setMethodCallHandler(this)

        val tokenChannel =
            EventChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                FR_EVENT_CALLBACK_CHANNEL,
            )
        tokenChannel.setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(
                    o: Any?,
                    eventSink: EventChannel.EventSink?,
                ) {
                    tokenReceiverEvent = eventSink
                }

                override fun onCancel(o: Any?) {}
            },
        )

        val refreshTokenChannel =
            EventChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                FR_REFRESH_TOKEN_EVENT_CHANNEL,
            )
        refreshTokenChannel.setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(
                    o: Any?,
                    eventSink: EventChannel.EventSink?,
                ) {
                    refreshTokenReceiverEvent = eventSink
                }

                override fun onCancel(o: Any?) {}
            },
        )
    }

    private fun parsingTokenResponse(result: FRNativeResponse): String =
        if (result.success && result.accessToken != null) {
            Gson().toJson(
                FRTokensResponse(
                    success = true,
                    payload =
                        FRTokensPayload(
                            idToken = result.accessToken?.idToken,
                            accessToken = result.accessToken?.accessToken,
                            refreshToken = result.accessToken?.refreshToken,
                            tokenType = result.accessToken?.tokenType,
                            expiration = result.accessToken?.expiration?.time,
                            expiresIn = result.accessToken?.expiresIn,
                        ),
                ),
            )
        } else {
            val isInvalidGrant = result.message?.contains("invalid_grant", ignoreCase = true) == true
            Gson().toJson(
                FRTokensResponse(
                    success = false,
                    isInvalidGrant = isInvalidGrant,
                    errorMsg = result.message,
                ),
            )
        }

    private fun handleTokenResponse(
        result: FRNativeResponse,
        isRefreshToken: Boolean,
    ) {
        val string = parsingTokenResponse(result)
        if (isRefreshToken) {
            refreshTokenReceiverEvent?.success(string)
        } else {
            tokenReceiverEvent?.success(string)
        }
    }

    override fun onMethodCall(
        call: MethodCall,
        resultCall: MethodChannel.Result,
    ) {
        if (activity == null) return
        try {
            when (call.method) {
                "setDebug" -> {
                    val debug: Boolean? = call.argument("debug")
                    FRNative.setDebug(debug ?: false)
                    resultCall.success(null)
                }
                "setBrand" -> {
                    val brand: String = call.argument("brand") ?: "T"
                    FRNative.brand = brand
                    resultCall.success(null)
                }
                "setLocale" -> {
                    val localeString: String? = call.argument("localeString")
                    FRNative.nativeLocale = FRPluginUtils.getLocaleFromCode(localeString ?: "en-US")
                    SharePreferenceHelper
                        .getLanguagePreference()
                        .setLanguage(FRNative.nativeLocale.language)
                    val regionItem = AppLanguageUtils.getRegion(FRNative.nativeLocale.country)
                    if (regionItem != null) {
                        SharePreferenceHelper.getLanguagePreference().setRegion(regionItem)
                    }
                    resultCall.success(null)
                }
                "setPushToken" -> {
                    val pushToken: String? = call.argument("pushToken")
                    FRNative.pushTokenId = pushToken
                    resultCall.success(null)
                }
                "setBiometricSupport" -> {
                    val biometricSupport: Boolean = call.argument("biometricSupport") ?: false
                    FRNative.biometricSupport = biometricSupport
                    resultCall.success(null)
                }
                "setCurrentPosition" -> {
                    val lat: Double? = call.argument("lat")
                    val lon: Double? = call.argument("lon")
                    val location = Location("")
                    location.latitude = lat ?: 0.0
                    location.longitude = lon ?: 0.0
                    FRNative.currentLocation = location
                    resultCall.success(null)
                }
                "signIn" -> {
                    FRNative.logout()
                    FRNative.login(
                        activity!!,
                        object : FRNativeResultListener {
                            override fun onResult(result: FRNativeResponse) {
                                handleTokenResponse(result, false)
                            }
                        },
                    )
                    resultCall.success(null)
                }
                "signUp" -> {
                    FRNative.logout()
                    FRNative.register(
                        activity!!,
                        object : FRNativeResultListener {
                            override fun onResult(result: FRNativeResponse) {
                                handleTokenResponse(result, false)
                            }
                        },
                    )
                    resultCall.success(null)
                }
                "logout" -> {
                    logoutCallback?.invoke()
                    resultCall.success(null)
                }
                "refreshToken" -> {
                    if (isRefreshTokenAvailable()) {
                        scope.launch(Dispatchers.IO) {
                            try {
                                val result =
                                    FRNative.refreshTokenRetryTimes(
                                        false,
                                        IDPHelper.RETRY_TIMES,
                                    )
                                withContext(Dispatchers.Main) {
                                    resultCall.success(result?.let { parsingTokenResponse(it) })
                                }
                            } catch (ex: AuthenticationRequiredException) {
                                withContext(Dispatchers.Main) {
                                    IDPData.getInstance(oneAppPreferenceModel).clearToken()
                                    logoutCallback?.invoke()
                                    resultCall.success(null)
                                }
                            }
                        }
                    } else {
                        IDPData.getInstance(oneAppPreferenceModel).clearToken()
                        logoutCallback?.invoke()
                        resultCall.success(null)
                    }
                }
                else -> {
                    resultCall.success(null)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun onAttachedToActivity(activity: Activity?) {
        this.activity = activity
        if (activity != null) {
            FRNative.initResultLauncher(activity)
        }
    }

    fun onDetachedFromEngine() {
        scope.cancel()
        channel.setMethodCallHandler(null)
    }

    private fun isRefreshTokenAvailable(): Boolean {
        val refreshToken = IDPData.getInstance(oneAppPreferenceModel).refreshToken
        return !TextUtils.isEmpty(refreshToken) && !IDPData.getInstance(oneAppPreferenceModel).isRefreshTokenExpired()
    }
}

data class FRTokensResponse(
    val success: Boolean,
    val isInvalidGrant: Boolean = false,
    val payload: FRTokensPayload? = null,
    val errorMsg: String? = null,
)

data class FRTokensPayload(
    val idToken: String? = null,
    val accessToken: String? = null,
    val refreshToken: String? = null,
    val tokenType: String? = null,
    val expiration: Long? = null,
    val expiresIn: Long? = null,
)

data class TFSTokensPayload(
    val id_token: String? = null,
    val access_token: String? = null,
    val refresh_token: String? = null,
    val token_type: String? = null,
    val expires_in: Long? = null,
)

object FRPluginUtils {
    private val US_ENGLISH = Locale("EN", "US", "")
    private val US_SPANISH = Locale("ES", "US", "")
    private val TDPR_ENGLISH = Locale("EN", "PR", "")
    private val TDPR_SPANISH = Locale("ES", "PR", "")
    private val CANADA_ENGLISH = Locale("EN", "CA", "")
    private val CANADA_FRANCE = Locale("FR", "CA", "")
    private val MEXICO_ENGLISH =
        Locale("EN", ToyotaConstants.REGION_MX, ToyotaConstants.EMPTY_STRING)
    private val MEXICO_SPANISH =
        Locale("ES", ToyotaConstants.REGION_MX, ToyotaConstants.EMPTY_STRING)

    fun getLocaleFromCode(code: String): Locale =
        when {
            code.equals(getStringFromLocale(US_ENGLISH), ignoreCase = true) -> {
                US_ENGLISH
            }
            code.equals(getStringFromLocale(US_SPANISH), ignoreCase = true) -> {
                US_SPANISH
            }
            code.equals(getStringFromLocale(TDPR_ENGLISH), ignoreCase = true) -> {
                TDPR_ENGLISH
            }
            code.equals(getStringFromLocale(TDPR_SPANISH), ignoreCase = true) -> {
                TDPR_SPANISH
            }
            code.equals(getStringFromLocale(CANADA_ENGLISH), ignoreCase = true) -> {
                CANADA_ENGLISH
            }
            code.equals(getStringFromLocale(CANADA_FRANCE), ignoreCase = true) -> {
                CANADA_FRANCE
            }
            code.equals(getStringFromLocale(MEXICO_ENGLISH), ignoreCase = true) -> {
                MEXICO_ENGLISH
            } code.equals(getStringFromLocale(MEXICO_SPANISH), ignoreCase = true) -> {
                MEXICO_SPANISH
            }
            else -> {
                US_ENGLISH
            }
        }

    private fun getStringFromLocale(locale: Locale): String {
        val country = if (TextUtils.isEmpty(locale.country)) "US" else locale.country
        return locale.language.lowercase(Locale.US) + "-" + country.uppercase(Locale.US)
    }
}
