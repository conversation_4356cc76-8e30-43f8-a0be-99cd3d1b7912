package com.toyota.oneapp.ui.flutter

const val ENROLLMENT_FIELD = "isEnrolled"
const val IS_KEYEXIST_FIELD = "isKeyExist"
const val IS_CONNECTED_FIELD = "isConnected"
const val IS_OWNER_KEY_TYPE = "isOwnerKeyType"
const val IS_SETUPCOMPLETE_FIELD = "isSetUpComplete"
const val SHARE_USER_COUNTER_FIELD = "sharedUserCount"
const val DOWNLOAD_PROGRESS = "downloadProgressPercentage"
const val IS_DOWNLOAD_PROGRESS = "isDownloadInProgress"
const val CURRENT_VIN_DOWNLOAD = "downloadingVIN"
const val IS_ERROR = "isError"
const val IS_SECONDARY_VEHICLE = "isSecondaryVehicle"
const val NATIVE_DASHBOARD_METHOD_CHANNEL = "oneapp_call_native_method_dashboard"
const val NATIVE_APPOINTMENT_METHOD_CHANNEL = "oneapp_call_native_method_appointment"
const val NATIVE_METHOD_CHANNEL = "oneapp_call_native_method"
const val APPTENTIVE_METHOD_CHANNEL = "oneapp_apptentive_method"
const val DIGITAL_KEY_SET_INVOKE_METHOD = "setDigitalKeyStatus"
const val DIGITAL_KEY_UPDATE_INVOKE_METHOD = "updateDigitalKeyCard"
const val DIGITAL_KEY_STATUS_PARAM = "dkStatus"
const val VEHICLE_LIST_INVOKE_METHOD = "vehicleList"
const val SELECTED_VIN_INVOKE_METHOD = "selectedVin"
const val FINANCE_ACCOUNT_INVOKE_METHOD = "financeAccountDetail"
const val FINANCE_ACCOUNT_UPDATE_INVOKE_METHOD = "financeAccountDetailUpdate"
const val MARKETING_CONSENT_INVOKE_METHOD = "marketingConsentStatus"
const val ACCOUNT_BADGE_INVOKE_METHOD = "accountBadgeStatus"
const val BIOMETRIC_STATUS_INVOKE_METHOD = "biometricStatus"
const val KEEPME_SIGNED_INVOKE_METHOD = "keepMeSignedStatus"
const val UPDATE_LOCALE_CHANNEL = "update_locale_channel"
const val APPOINTMENTLIST_UPDATE_INVOKE_METHOD = "refresh_appointment_list"
const val SCHEUDLE_MAINTENANCE_INVOKE_METHOD = "scheduleMaintenance"
const val SUBSCRIPTIONS_INVOKE_METHOD = "showfluttersubscriptionpage"
const val UPDATE_ELIGIBLE_PARAM = "isUpdateEligible"
const val MAPDETAILS_EXISTS_PARAM = "mapDetailExists"
const val DYNAMIC_NAV_ACTION_PARAM = "action"
const val DYNAMIC_NAV_PCOLINK_PARAM = "pcoLink"
const val CONTACT_DEALER_INVOKE_METHOD = "contactDealer"
const val PREFERRED_DEALER_INVOKE_METHOD = "preferredDealer"
const val VEHICLE_SOFTWARE_UPDATE_INVOKE_METHOD = "vehiclesoftwareupdate"
const val PERCENTAGE_05 = 0.05
const val PERCENTAGE_010 = 0.10
const val PERCENTAGE_033 = 0.33
const val PERCENTAGE_066 = 0.66
const val PERCENTAGE_100 = 1.0
