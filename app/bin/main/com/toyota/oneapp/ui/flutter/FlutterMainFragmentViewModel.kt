package com.toyota.oneapp.ui.flutter

import androidx.compose.material.ExperimentalMaterialApi
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.ctp.v1.ProfileServiceServer.UpdateUserProfileResponse
import com.toyota.oneapp.apptentive.ApptentiveService
import com.toyota.oneapp.model.account.PhotoResponse
import com.toyota.oneapp.model.dashboard.card.MyDestinationsItem
import com.toyota.oneapp.model.dealer.Dealer
import com.toyota.oneapp.model.poi.Address
import com.toyota.oneapp.model.poi.Coordinates
import com.toyota.oneapp.model.poi.SendPOIToCarRequest
import com.toyota.oneapp.model.poi.SendToCarLocation
import com.toyota.oneapp.model.poi.SharePOIRequest
import com.toyota.oneapp.model.poi.SharePOIResponse
import com.toyota.oneapp.model.poi.isSameAs
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.network.api.repository.LocationRepository
import com.toyota.oneapp.network.api.repository.SoftwareUpdate20TMRepository
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseFragmentViewModel
import com.toyota.oneapp.util.LocationUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.grpc.stub.StreamObserver
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import toyotaone.commonlib.location.UserLocationProvider
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@OptIn(ExperimentalMaterialApi::class)
@HiltViewModel
class FlutterMainFragmentViewModel
    @Inject
    constructor(
        private val accountApiManager: AccountAPIManager,
        private val userProfileAPIManager: UserProfileAPIManager,
        private val preferenceModel: OneAppPreferenceModel,
        private val locationRepository: LocationRepository,
        private val softwareUpdate20TMRepository: SoftwareUpdate20TMRepository,
        apptentiveService: ApptentiveService,
    ) : BaseFragmentViewModel(),
        ApptentiveService by apptentiveService {
        private val TAG = "FlutterMainFragmentViewModel"

        /**** Mutable Live Objects ***/
        private lateinit var mUserLocationProvider: UserLocationProvider

        private val mMyDestinationsItem = MutableLiveData<MyDestinationsItem>()
        val myDestinationsItem: LiveData<MyDestinationsItem> get() =
            mMyDestinationsItem

        fun getProfilePicture() {
            accountApiManager.sendGetPhotoRequest(
                preferenceModel.getGuid(),
                object : BaseCallback<PhotoResponse>() {
                    override fun onSuccess(response: PhotoResponse) {
                        FlutterMainFragment.methodChannelResult?.success(
                            response.payload ?: ToyotaConstants.EMPTY_STRING,
                        )
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        LogTool.e(
                            TAG,
                            "$httpCode: $errorMsg",
                        )
                    }

                    // Gets invoked when no photo is found
                    override fun onRedirect(location: String?) {
                        // Posting an empty string to update image in UI to remove potentially
                        // cached image
                        // Surround with try/catch to prevent IllegalStateException: Reply already submitted, which occurs intermittently
                        try {
                            FlutterMainFragment.methodChannelResult?.success(
                                ToyotaConstants.EMPTY_STRING,
                            )
                        } catch (ex: IllegalStateException) {
                            ex.printStackTrace()
                        }
                    }
                },
            )
        }

        fun initialize(userLocationProvider: UserLocationProvider) {
            mUserLocationProvider = userLocationProvider
        }

        fun sendPoiToCar(
            result: MethodChannel.Result,
            poi: SharePOIResponse,
        ) {
            val coordinates = Coordinates(poi.location.latitude, poi.location.longitude)
            val poiRequest =
                SendPOIToCarRequest(
                    preferenceModel.getGuid(),
                    poi.name ?: ToyotaConstants.EMPTY_STRING,
                    SendToCarLocation(
                        poi.location.latitude,
                        poi.location.longitude,
                        "google_maps",
                        poi.name ?: ToyotaConstants.EMPTY_STRING,
                        coordinates,
                        poi.formattedAddress ?: ToyotaConstants.EMPTY_STRING,
                        poi.address ?: Address(),
                        poi.locationType,
                        coordinates,
                        poi.placeId,
                        poi.phoneNumber ?: ToyotaConstants.EMPTY_STRING,
                    ),
                )
            viewModelScope.launch {
                when (val response = locationRepository.sendPOIToCar(poiRequest)) {
                    is Resource.Success -> {
                        val map: Map<String, String> = mapOf("outcome" to "success")
                        result.success(Gson().toJson(map))
                    }
                    is Resource.Failure -> {
                        result.error("genericError", response.error?.message, null)
                    }
                    else -> {
                        // No op
                    }
                }
            }
        }

        fun removeFavorites(
            result: MethodChannel.Result,
            poi: SharePOIResponse,
        ) {
            userProfileAPIManager.getUserProfile(
                object :
                    StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse?) {
                        if (value?.userProfile == null) {
                            result.error("genericError", "Couldn't retrieve user profile", null)
                            return
                        }
                        val poiLocation = poi.toLocationDetails()
                        val favList = LocationUtil.getLocationDetails(value.userProfile).favList

                        val isRemoved =
                            favList.removeIf {
                                it.placeId == poiLocation.placeId
                            }

                        if (isRemoved) {
                            userProfileAPIManager.updateFavouriteLocation(
                                favList,
                                object : StreamObserver<UpdateUserProfileResponse> {
                                    override fun onNext(value: UpdateUserProfileResponse?) {
                                        result.success(true)
                                    }

                                    override fun onError(t: Throwable?) {
                                        result.error("genericError", t?.message, null)
                                    }

                                    override fun onCompleted() {
                                        // No op
                                    }
                                },
                            )
                        }
                    }

                    override fun onError(t: Throwable?) {
                        result.error("genericError", t?.message, null)
                    }

                    override fun onCompleted() {
                        // No op
                    }
                },
            )
        }

        fun addFavoritePoi(
            result: MethodChannel.Result,
            poi: SharePOIResponse,
        ) {
            userProfileAPIManager.getUserProfile(
                object :
                    StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse?) {
                        if (value?.userProfile == null) {
                            result.error("genericError", "Couldn't retrieve user profile", null)
                            return
                        }
                        val poiLocation = poi.toLocationDetails()
                        val favList = LocationUtil.getLocationDetails(value.userProfile).favList

                        // Update fav if already exists
                        favList.stream().filter { location ->
                            if (location.isSameAs(poiLocation)) {
                                favList.remove(poiLocation)
                            } else {
                                false
                            }
                        }

                        poiLocation.refreshDate = System.currentTimeMillis() / 1000
                        poiLocation.timeStamp = System.currentTimeMillis() / 1000
                        favList.add(poiLocation)

                        userProfileAPIManager.updateFavouriteLocation(
                            favList,
                            object : StreamObserver<UpdateUserProfileResponse> {
                                override fun onNext(value: UpdateUserProfileResponse?) {
                                    result.success(true)
                                }

                                override fun onError(t: Throwable?) {
                                    result.error("genericError", t?.message, null)
                                }

                                override fun onCompleted() {
                                    // No op
                                }
                            },
                        )
                    }

                    override fun onError(t: Throwable?) {
                        result.error("genericError", t?.message, null)
                    }

                    override fun onCompleted() {
                        // No op
                    }
                },
            )
        }

        fun loadFavorites(result: MethodChannel.Result) {
            userProfileAPIManager.getUserProfile(
                object : StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse?) {
                        if (value?.userProfile == null) {
                            result.success(false)
                            return
                        }

                        if (LocationUtil.getLocationDetails(value.userProfile).favList.isNullOrEmpty()) {
                            result.success(false)
                            return
                        }

                        result.success(true)
                    }

                    override fun onError(t: Throwable?) {
                        result.error("genericError", t?.message, null)
                    }

                    override fun onCompleted() {
                        // No op
                    }
                },
            )
        }

        fun mapPlaceIDsToFav(
            result: MethodChannel.Result,
            places: ArrayList<String>?,
        ) {
            if (places == null) {
                result.success(ToyotaConstants.EMPTY_STRING)
                return
            }

            userProfileAPIManager.getUserProfile(
                object : StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse?) {
                        if (value?.userProfile == null) {
                            result.success(ToyotaConstants.EMPTY_STRING)
                            return
                        }

                        if (LocationUtil.getLocationDetails(value.userProfile).favList.isNullOrEmpty()) {
                            result.success(ToyotaConstants.EMPTY_STRING)
                            return
                        }

                        val favList = LocationUtil.getLocationDetails(value.userProfile).favList
                        val map: Map<String, Boolean> =
                            places.associateWith { placeId ->
                                favList.any { location ->
                                    location.placeId == placeId
                                }
                            }

                        result.success(Gson().toJson(map))
                    }

                    override fun onError(t: Throwable?) {
                        result.error("genericError", t?.message, null)
                    }

                    override fun onCompleted() {
                        // No op
                    }
                },
            )
        }

        fun getLocationDetails() {
            showProgressDialog()
            userProfileAPIManager.getUserProfile(
                object :
                    StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse) {
                        mMyDestinationsItem.postValue(
                            LocationUtil.getLocationDetails(value.userProfile),
                        )
                    }

                    override fun onError(t: Throwable) {
                        dismissProgressDialog()
                    }

                    override fun onCompleted() {
                        dismissProgressDialog()
                    }
                },
            )
        }

        fun lookUpPoi(
            methodCall: MethodCall,
            result: MethodChannel.Result,
            callback: (result: MethodChannel.Result, poi: SharePOIResponse) -> Unit,
        ) {
            val sharedText = methodCall.argument<String>("text")
            if (sharedText.isNullOrEmpty()) {
                result.error("textError", "Required argument 'text' not found.", null)
                return
            }
            viewModelScope.launch {
                val response =
                    locationRepository.getPOIDataFromSharedText(
                        SharePOIRequest(
                            "android",
                            sharedText,
                        ),
                    )

                when (response) {
                    is Resource.Success -> {
                        response.data?.payload?.let { poi ->
                            callback(result, poi)
                        }
                    }
                    is Resource.Failure -> {
                        result.error("textError", response.error?.message, null)
                    }
                    is Resource.Loading -> {
                        // No op
                    }
                    else -> {
                        result.error("genericError", "No POI found, and no error found.", null)
                    }
                }
            }
        }

        fun savePreferredDealerToUserProfile(
            result: MethodChannel.Result,
            methodCall: MethodCall,
        ) {
            val dealerInfoJson = methodCall.argument<String>("dealerInfo")
            val dealerInfo = Gson().fromJson<Dealer>(dealerInfoJson, Dealer::class.java)

            userProfileAPIManager.updatePreferredDealer(
                methodCall.argument<String>("vin").orEmpty(),
                dealerInfo,
                object : StreamObserver<UpdateUserProfileResponse> {
                    override fun onNext(value: UpdateUserProfileResponse?) {
                        result.success(true)
                    }

                    override fun onCompleted() {
                    }

                    override fun onError(t: Throwable?) {
                        result.error(t?.localizedMessage ?: "", t?.message, t)
                    }
                },
            )
        }

        suspend fun getUpdateNotificationStatus(vin: String): Int =
            withContext(Dispatchers.IO) {
                val resource = softwareUpdate20TMRepository.getSoftwareUpdateNotification(vin)
                var status = 0
                if (resource is Resource.Success) {
                    status = resource.data?.payload?.notificationStatus ?: 0
                }
                return@withContext status
            }
    }
