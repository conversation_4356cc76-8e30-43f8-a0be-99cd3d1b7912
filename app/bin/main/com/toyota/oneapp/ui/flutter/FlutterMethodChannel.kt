package com.toyota.oneapp.ui.flutter

class FlutterMethodChannel {
    companion object {
        const val CallDeviceTokenApi = "callNativeDeviceTokenApi"
        const val GetOneAppAccessToken = "getOneAppAccessToken"
        const val GetProxyAddress = "getProxyAddress"
        const val GetOneAppEnv = "getOneAppEnv"
        const val GetOneAppLocale = "getOneAppLocale"
        const val OneAppFinish = "oneAppFinish"
        const val OneAppLogEvent = "oneAppLogEvent"
        const val OneAppDataDogLog = "oneAppDataDogLog"
        const val VehicleList = "vehicleList"
        const val SelectedVin = "selectedVin"
        const val OneAppBottomBarTap = "oneAppBottomBarTap"
        const val FcmDeviceId = "fcmDeviceId"
        const val Account = "account"
        const val ScheduleService = "scheduleService"
        const val Notification = "notification"
        const val RemoteActivate = "remoteActivate"
        const val Subscription = "subscription"
        const val AddVehicle = "addVehicle"
        const val Remove21MMVehicle = "remove21MMVehicle"
        const val Connect = "connect"
        const val Xcapp_List = "xcapp_list"
        const val DriverCompanion = "driverCompanion"
        const val AccountInfoPayload = "accountInfoPayload"
        const val MarketingConsent = "MARKETING_CONSENT"
        const val MyDestination = "myDestination"
        const val MyMusic = "myMusic"
        const val InsureConnectConsent = "insureConnectConsent"
        const val InsureConnectOffers = "insureConnectOffers"
        const val PaymentMethods = "paymentMethods"
        const val GetProfilePicture = "getProfilePicture"
        const val KeepMeSignedInBioMetricsStatus = "keepMeSignedInBioMetricsStatus"
        const val HowToUseDK = "howToUseDK"
        const val ShowLCFSDataConsent = "showLCFSDataConsent"
        const val RemotePark = "remotePark"
        const val GetOneAppAttToken = "getOneAppAttToken"
        const val SendToCar = "sendToCar"
        const val LoadFavorites = "loadFavorites"
        const val AddFavorite = "addFavorite"
        const val IsFavorite = "isFavorite"
        const val RemoveFavorite = "removeFavorite"
        const val AppointmentDetail = "serviceAppointmentDetail"
        const val PrivacyPortal = "privacyPortal"
        const val AccountLinking = "smsAccountLinking"
        const val SmsRegistration = "smsRegistration"
        const val RemoteAccess = "remoteAccess"
        const val PinRest = "smsPinRest"
        const val VehicleFeatureOverrides = "getDebugVehicleFeatureOverrides"
        const val VehicleHealthReport = "healthReport"
        const val howtoVideos = "howToVideos"
        const val deeplinkDynamicNav = "dynamicNavigation"
        const val dynamicNavigation = "announcementDynamicNavigation"
        const val getUserProfileName = "update_profileName"
        const val savePreferredDealerToUserProfile = "save_preferredDealer_User_profile"
        const val appUpdateRequired = "appUpdateRequired"
        const val isBleEnabledCheck = "isBluetoothOn"
        const val vehicleSoftwareUpdate = "vehicleSoftwareUpdate"
        const val back = "back"
        const val tfsOtpSuccess = "tfsOtpSuccess"
        const val setStoredUnLinkUser = "setStoredUnLinkUser"
        const val backToDashboard = "backToDashboard"
        const val backAndStoreCleanAssistEnroll = "caEnroll"
        const val setStoreChargeSessionId = "setStoreChargeSessionId"
        const val setStoreChargePartner = "setStoreChargePartner"
        const val activateByCode = "activateByCode"
        const val engageServiceAppointmentSuccess = "engageServiceAppointmentSuccess"
    }
}
