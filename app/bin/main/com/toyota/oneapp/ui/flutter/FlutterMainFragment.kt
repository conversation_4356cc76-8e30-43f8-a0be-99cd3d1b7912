/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.ui.flutter

import android.app.Activity
import android.app.Dialog
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.material.ExperimentalMaterialApi
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import apptentive.com.android.feedback.Apptentive
import apptentive.com.android.feedback.ApptentiveActivityInfo
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.AppFeatureFlags
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.component.IDPHelper
import com.toyota.oneapp.component.receiver.BluetoothStateReceiver
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.fcm.ToyotaFCMService
import com.toyota.oneapp.features.dashboard.util.ApptentiveEvent
import com.toyota.oneapp.features.pay.tfs.dataaccess.servermodel.AuthenticateResponseModel
import com.toyota.oneapp.features.pay.tfs.domain.model.AccountModel
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.garage.HowToVideo
import com.toyota.oneapp.model.pref.SingletonPreferenceModel
import com.toyota.oneapp.model.remote.SendDeviceStatusRequest
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.api.manager.RemoteServiceAPIManager
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.LogoutCallback
import com.toyota.oneapp.network.dataprovider.NetworkDataProvider
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.accountsettings.LegalSelectVehicleActivity
import com.toyota.oneapp.ui.accountsettings.ResetPinActivity
import com.toyota.oneapp.ui.dataconsent.DataConsentLCFSDetailActivity
import com.toyota.oneapp.ui.destinations.DestinationsActivity
import com.toyota.oneapp.ui.destinations.destination.DestinationsFragment
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity.Companion.REFRESH_DASHBOARD
import com.toyota.oneapp.ui.garage.HowToVideosActivity
import com.toyota.oneapp.ui.garage.vehicleUpdate21mm.VehicleSoftware21MMParentActivity
import com.toyota.oneapp.ui.newdashboard.DashboardActivity
import com.toyota.oneapp.ui.newdashboard.VehicleHealthReportActivity
import com.toyota.oneapp.ui.newdashboard.kNavItem
import com.toyota.oneapp.ui.payment.PaymentMethodsActivity
import com.toyota.oneapp.ui.scheduledmaint.ScheduleMaintenanceMainActivity
import com.toyota.oneapp.ui.vinscan.QRScanActivity
import com.toyota.oneapp.util.AdUtil
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.FlutterOneAppUtil
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import io.flutter.embedding.android.FlutterFragment
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.StandardMessageCodec
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.eventbus.RxBus
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.permission.PermissionPageUtil
import javax.inject.Inject

/**
 * Created by Daniel on 4/21/21.
 */
private const val DASHBOARD_PAGE = "DASHBOARD_PAGE"
private const val VEHICLE_SERVICE_APPOINTMENT_INITIAL_PAGE = "VEHICLE_SERVICE_APPOINTMENT_INITIAL_PAGE"
private const val VEHICLE_CLIMATE_PAGE = "VEHICLE_CLIMATE_PAGE"
private const val VEHICLE_DRIVER_ALERT_PAGE = "VEHICLE_DRIVER_ALERT_PAGE"
private const val FX_DETAIL_LANDING = "FX_DETAIL_LANDING"
private const val VEHICLE_TRIP_PAGE = "VEHICLE_TRIP_PAGE"
private const val FX_SELECTPREFERDEALERPAGE = "FX_SELECTPREFERDEALERPAGE"
private const val FX_VEHICLEDEALERDETAILPAGE = "FX_VEHICLEDEALERDETAILPAGE"
private const val VEHICLE_ANNOUNCEMENTS = "VEHICLE_ANNOUNCEMENTS"
private const val VEHICLE_HEALTH_DETAIL_PAGE = "VEHICLE_HEALTH_DETAIL_PAGE"
private const val PREFERRED_SERVICE_DEALER = "PREFERRED_SERVICE_DEALER"
private const val WALLET_HOME = "WALLET_HOME"
private const val VEHICLE_SEARCH_CHARGE_STATION_LOCATION = "VEHICLE_SEARCH_CHARGE_STATION_LOCATION"
private const val EV_CHARGE_INFO_PAGE = "EV_CHARGE_INFO_PAGE"
private const val VEHICLE_CHARGE_MANAGEMENT_SCREEN = "VEHICLE_CHARGE_MANAGEMENT_SCREEN"
private const val EV_VEHICLE_CHARGE_MANAGEMENT_PAGE = "DRIVER_CHARGE_MANAGEMENT_PAGE"
private const val MAINTENANCE_SCHEDULE = "MAINTANANCE_SCHEDULE"
private const val SERVICE_HISTORY = "SERVICE_HISTORY"
private const val APPOINTMENTS = "APPOINTMENTS"
private const val ACCOUNT_OPTIONS = "FINANCE_OPTIONS_PAGE"
private const val FINANCE_CREATE_PAYMENT_PAGE = "FINANCE_CREATE_PAYMENT_PAGE"
private const val FINANCE_ACCESS_ACCOUNT_PAGE = "FINANCE_ACCESS_ACCOUNT_PAGE"
private const val FINANCE_LINK_ACCOUNT_PAGE = "FINANCE_LINK_ACCOUNT_PAGE"
private const val GLOVE_BOX_PAGE = "GLOVE_BOX_PAGE"
private const val FINANCE_ONLINE_AGREEMENT = "FINANCE_ONLINE_AGREEMENT"
private const val FINANCE_ELECTRONIC_AGREEMENT = "FINANCE_ELECTRONIC_AGREEMENT"

@ExperimentalMaterialApi
@AndroidEntryPoint
class FlutterMainFragment :
    FlutterFragment(),
    ApptentiveActivityInfo {
    private val tag = "FlutterMainFragment"
    // TODO: Clean all method channels post V2 development excludes nativeToFlutterNavigationChannel & tokenChannel.

    companion object {
        var methodChannelResult: MethodChannel.Result? = null
    }

    val extraBundle = Bundle()
    var intentUri: Uri? = null

    @Inject
    lateinit var oneAppPreferenceModel: OneAppPreferenceModel

    lateinit var disposable: Disposable
    lateinit var flutterBroadcast: FlutterBroadcast
    private var forgerockChannelManager: ForgerockChannelManager? = null
    private var pushTokenChannel: BasicMessageChannel<Any>? = null
    private var pushMessageChannel: BasicMessageChannel<Any>? = null
    private var pushMessageClickChannel: BasicMessageChannel<Any>? = null
    private var routeChannel: BasicMessageChannel<Any>? = null
    private var tokenChannel: BasicMessageChannel<Any>? = null
    private var tfsTokenChannel: BasicMessageChannel<Any>? = null
    private var nativeToFlutterNavigationChannel: BasicMessageChannel<Any>? = null
    private var vehicleListPayloadChannel: BasicMessageChannel<Any>? = null
    var isFromTFSScreen: Boolean = false

    private var sharedPreferences: SharedPreferences? = null

    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    lateinit var idpData: IDPData

    @Inject
    lateinit var singletonPreferenceModel: SingletonPreferenceModel

    @Inject
    lateinit var accountApiManager: AccountAPIManager

    @Inject
    lateinit var digitalMopKeyMopUtils: DigitalMopKeyUtils

    @Inject
    lateinit var digitalKeyLocalData: DigitalKeyLocalData

    @Inject
    lateinit var networkDataProvider: NetworkDataProvider

    @Inject
    lateinit var userProfileAPIManager: UserProfileAPIManager

    @Inject
    lateinit var regionManager: RegionManager

    @Inject
    lateinit var remoteManager: RemoteServiceAPIManager

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    @Inject
    lateinit var adUtil: AdUtil

    @Inject
    lateinit var idpHelper: IDPHelper

    @Inject
    lateinit var appFeatureFlags: AppFeatureFlags

    private val viewModel: FlutterMainFragmentViewModel by viewModels()

    private val reqLcfs = 101
    private var mProgressDialog: Dialog? = null

    private fun notificationContract(context: Context): ActivityResultLauncher<Intent> =
        (context as ComponentActivity).registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val resultIntent = result.data
                val category = resultIntent?.getStringExtra(ToyotaFCMService.CATEGORY)
                val map = resultIntent?.getStringExtra(ToyotaFCMService.MAP)

                when (category) {
                    ToyotaFCMService.TM_INSTALL_CATEGORY,
                    ToyotaFCMService.TM_RELEASE_CATEGORY,
                    ToyotaFCMService.TM_UPDATE_CATEGORY,
                    -> {
                        routeChannel?.send(
                            Gson().toJson(
                                FlutterNavigationEntity(
                                    DASHBOARD_PAGE,
                                    false,
                                    routeData =
                                        DeeplinkPayload(
                                            isDashboardRefresh = false,
                                            isDeepLink = true,
                                            category = category,
                                            vin = map ?: "",
                                        ),
                                ),
                            ),
                        )
                    }

                    ToyotaFCMService.OTA_UPDATES_21MM -> {
                        map?.let { mapString: String ->
                            showProgressDialog()
                            try {
                                val jsonMap = JSONObject(mapString)
                                val vin = jsonMap[ToyotaConstants.VIN]
                                redirectOtaFlow(vin.toString())
                            } catch (exception: JSONException) {
                                dismissProgressDialog()
                                LogTool.e(tag, "Couldn't parse map for $category. Map: $map")
                            }
                        }
                    }
                }
            }
        }

    private fun destinationsContract(context: Context): ActivityResultLauncher<Bundle> =
        (context as ComponentActivity).registerForActivityResult(DestinationsActivity.Contract()) {
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        registerFlutterBroadcast()
        ToyUtil.setTransparentStatusBar(requireActivity())
        listenForEvent()
        if (savedInstanceState != null) {
            extraBundle.putAll(savedInstanceState)
        }

        handleIntent(extraBundle)
        sharedPreferences =
            activity?.getSharedPreferences(
                "PVL_LOCATION_POINTS",
                Context.MODE_PRIVATE,
            )

        // Initialize Digitalkey
        mProgressDialog = DialogUtil.createProgressDialog(activity)
        R.style.AppTheme
        activity?.let {
            it.window?.statusBarColor = it.getColor(com.toyota.one_ui.R.color.oneUiColorSecondaryBase)
            it.window?.decorView?.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        }

        viewModel.myDestinationsItem.observe(this) {
            val bundle = Bundle()
            bundle.putString(DestinationsFragment.PREFERENCE_TYPE, DestinationsFragment.SEARCH_ALL)
            bundle.putString(DestinationsFragment.EXTRAS_MY_DESTINATIONS, Gson().toJson(it))

            launchDestinations(context, bundle)
        }
        BluetoothStateReceiver.register(context.applicationContext)
    }

    override fun getApptentiveActivityInfo(): Activity = requireActivity()

    override fun onResume() {
        super.onResume()
        Apptentive.registerApptentiveActivityInfoCallback(this)
    }

    private fun showProgressDialog() {
        mProgressDialog?.show()
    }

    private fun dismissProgressDialog() {
        mProgressDialog?.dismiss()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        extraBundle.clear()
        if (intent.data != null) {
            intentUri = intent.data
        }
        if (intent.extras != null) {
            extraBundle.putAll(intent.extras)
        }
        handleIntent(extraBundle)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putAll(extraBundle)
    }

    private fun handleIntent(bundle: Bundle?) {
        val pushCategory = bundle?.getString(ToyotaFCMService.CATEGORY)
        val map = bundle?.getString(ToyotaFCMService.MAP)
        val pushToDashboard = bundle?.getBoolean(GO_TO_DASHBOARD) ?: false
        val pushToAppointmentDetail = bundle?.getBoolean(GO_TO_APPOINTMENT_DETAIL) ?: false
        val pushToClimateDetail = bundle?.getBoolean(GO_TO_CLIMATE_DETAIL) ?: false
        val pushToGuestDriverDetail = bundle?.getBoolean(GO_TO_GUEST_DRIVER_DETAIL) ?: false
        val pushToStation = bundle?.getBoolean(Flutter_Station) ?: false
        val pushToDealer = bundle?.getBoolean(Flutter_Dealer) ?: false
        val pushToRental = bundle?.getBoolean(Flutter_Rentals) ?: false
        val pushToMaintenance = bundle?.getBoolean(GO_TO_MAINTENANCE_SCHEDULE) ?: false
        val pushToServiceHistory = bundle?.getBoolean(GO_TO_SERVICE_HISTORY) ?: false
        val pushToAppointments = bundle?.getBoolean(GO_TO_APPOINTMENTS) ?: false

        val pushToDrivePulseTrips = bundle?.getBoolean(Flutter_Drive_Pulse_Trips) ?: false
        val pushToSelectPreferredDealer = bundle?.getBoolean(Flutter_Select_Preferred_Dealer) ?: false
        val pushToPreferredDealer = bundle?.getBoolean(Flutter_Preferred_Dealer) ?: false
        val pushToVehicleAnnouncements = bundle?.getBoolean(Flutter_Vehicle_announcements) ?: false
        val pushToVehicleHealthDetail = bundle?.getBoolean(Flutter_Vehicle_health_Detail) ?: false
        val pushToSelectPreferredDealerForHealth =
            bundle?.getBoolean(
                Flutter_SelectPreferred_Dealer_for_Health,
            ) ?: false
        val pushToWalletHome = bundle?.getBoolean(GO_TO_WALLET_HOME) ?: false
        val pushToFindStations = bundle?.getBoolean(GO_TO_FIND_STATIONS) ?: false
        val pushToEvChargeInfo = bundle?.getBoolean(GO_TO_EV_CHARGE_INFO_PAGE) ?: false
        val pushToChargeManagement = bundle?.getBoolean(GO_TO_VEHICLE_CHARGE_MANAGEMENT_SCREEN) ?: false
        val pushToEVChargeManagement = bundle?.getBoolean(GO_TO_EV_CHARGE_MANAGEMENT_PAGE) ?: false
        val pushToAccountOptions = bundle?.getBoolean(GO_TO_ACCOUNT_OPTIONS) ?: false
        val pushToPayment = bundle?.getBoolean(GO_TO_PAYMENTS) ?: false
        val pushToGloveBox = bundle?.getBoolean(GO_TO_GLOVE_BOX) ?: false
        val requestOtp = bundle?.getBoolean(GO_TO_REQUEST_OTP) ?: false
        val pushToLinkAccount = bundle?.getBoolean(LINK_ACCOUNT) ?: false
        val pushToOnlinePolicies = bundle?.getBoolean(OPEN_ONLINE_POLICY) ?: false
        val pushToElectronicAgreement = bundle?.getBoolean(OPEN_ELECTRONIC_AGREEMENT) ?: false
        val isLogout = bundle?.getBoolean(IDPHelper.KEY_LOGOUT) ?: false
        val isContainDashboardRefresh = bundle?.containsKey(REFRESH_DASHBOARD)
        val isDashboardRefresh = bundle?.getBoolean(REFRESH_DASHBOARD)
        val isNewVehicleAdded =
            bundle?.getBoolean(DashboardActivity.DASHBOARD_NEW_VEHICLE_ADDED_KEY) ?: false
        val isDeepLinkRoute = bundle?.getBoolean(ToyotaConstants.IS_DEEP_LINK_ROUTING) ?: false
        val accountDetails =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                bundle?.let { it.getSerializable(ACCOUNTS, AccountModel::class.java) ?: {} }
            } else {
                @Suppress("DEPRECATION")
                bundle?.getSerializable(ACCOUNTS)?.let {
                    it as AccountModel
                }
            }
        val authenticateResponseModel =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                bundle?.let { it.getSerializable(REQUEST_OTP, AuthenticateResponseModel::class.java) ?: {} }
            } else {
                @Suppress("DEPRECATION")
                bundle?.getSerializable(REQUEST_OTP)?.let {
                    it as AuthenticateResponseModel
                }
            }
        val flutterData = bundle?.getSerializable(ToyotaConstants.FLUTTER_DATA)

        val shortcutAction =
            if (!applicationData.threeDShortAction.isNullOrEmpty()) {
                applicationData.threeDShortAction
            } else {
                null
            }
        val pushToVehicleSoftWareUpdate = bundle?.getBoolean(GO_TO_VEHICLE_INFO_SOFTWARE_UPDATE) ?: false
        val pushToEVSwap = bundle?.getBoolean(GO_TO_EV_SWAP) ?: false
        val pushToCleanAssistDetail = bundle?.getBoolean(GO_TO_CLEAN_ASSIST_DETAIL) ?: false

        // FuelWidget Navigation Actions Start
        if (pushToFindStations) {
            passTokenToFlutter {
                pushToFlutterScreen(VEHICLE_SEARCH_CHARGE_STATION_LOCATION, flutterData ?: {})
            }
        }
        if (pushToEvChargeInfo) {
            passTokenToFlutter {
                pushToFlutterScreen(EV_CHARGE_INFO_PAGE, flutterData ?: {})
            }
        }
        if (pushToChargeManagement) {
            passTokenToFlutter {
                pushToFlutterScreen(VEHICLE_CHARGE_MANAGEMENT_SCREEN, flutterData ?: {})
            }
        }
        if (pushToEVChargeManagement) {
            passTokenToFlutter {
                pushToFlutterScreen(EV_VEHICLE_CHARGE_MANAGEMENT_PAGE, flutterData ?: {})
            }
        }

        // FuelWidget Navigation Actions End

        // Update Dashboard with latest Secondary vehicle added.
        if (pushToAppointmentDetail) {
            passTokenToFlutter {
                pushToFlutterScreen(VEHICLE_SERVICE_APPOINTMENT_INITIAL_PAGE, flutterData ?: {})
            }
        } else if (pushToStation) {
            passTokenToFlutter {
                pushToFlutterScreen(VEHICLE_SEARCH_CHARGE_STATION_LOCATION, flutterData ?: {})
            }
        } else if (pushToDealer) {
            passTokenToFlutter {
                pushToFlutterScreen("", flutterData ?: {})
            }
        } else if (pushToRental) {
            passTokenToFlutter {
                pushToFlutterScreen(FX_DETAIL_LANDING, flutterData ?: {})
            }
        } else if (pushToDrivePulseTrips) {
            passTokenToFlutter {
                pushToFlutterScreen(VEHICLE_TRIP_PAGE, flutterData ?: {})
            }
        } else if (pushToClimateDetail) {
            passTokenToFlutter {
                pushToFlutterScreen(VEHICLE_CLIMATE_PAGE, flutterData ?: {})
            }
        } else if (pushToGuestDriverDetail) {
            passTokenToFlutter {
                pushToFlutterScreen(VEHICLE_DRIVER_ALERT_PAGE, flutterData ?: {})
            }
        } else if (pushToSelectPreferredDealer) {
            passTokenToFlutter {
                pushToFlutterScreen(FX_SELECTPREFERDEALERPAGE, flutterData ?: {})
            }
        } else if (pushToPreferredDealer) {
            passTokenToFlutter {
                pushToFlutterScreen(FX_VEHICLEDEALERDETAILPAGE, flutterData ?: {})
            }
        } else if (pushToVehicleAnnouncements) {
            passTokenToFlutter {
                pushToFlutterScreen(VEHICLE_ANNOUNCEMENTS, flutterData ?: {})
            }
        } else if (pushToVehicleHealthDetail) {
            passTokenToFlutter {
                pushToFlutterScreen(VEHICLE_HEALTH_DETAIL_PAGE, flutterData ?: {})
            }
        } else if (pushToSelectPreferredDealerForHealth) {
            passTokenToFlutter {
                pushToFlutterScreen(PREFERRED_SERVICE_DEALER, flutterData ?: {})
            }
        } else if (pushToMaintenance) {
            passTokenToFlutter {
                pushToFlutterScreen(MAINTENANCE_SCHEDULE, flutterData ?: {})
            }
        } else if (pushToServiceHistory) {
            passTokenToFlutter {
                pushToFlutterScreen(SERVICE_HISTORY, flutterData ?: {})
            }
        } else if (pushToAppointments) {
            passTokenToFlutter {
                pushToFlutterScreen(APPOINTMENTS, flutterData ?: {})
            }
            vehicleListPayloadChannel?.send(Gson().toJson(applicationData.getVehicleList() ?: ""))
        } else if (pushToGloveBox) {
            passTokenToFlutter {
                pushToFlutterScreen(GLOVE_BOX_PAGE, flutterData ?: {})
            }
        } else if (isLogout) {
            signOut()
        } else if (isDeepLinkRoute) {
            val branchReferringParam = bundle?.getString(ToyotaConstants.BRANCH_REFERRING_PARAM)
            passTokenToFlutter {
                pushToFlutterDashboard(
                    true,
                    isDeepLink = true,
                    shortCutAction = shortcutAction,
                    branchReferringParam = branchReferringParam,
                )
            }
        } else if (!shortcutAction.isNullOrBlank()) {
            if (shortcutAction == Intent.ACTION_SEND &&
                bundle?.getString(ToyotaConstants.DASHBOARD_ROUTE) ==
                getString(
                    R.string.deep_link_share_poi,
                )
            ) {
                startActivity(Intent(context, DestinationsActivity::class.java).putExtras(bundle))
            }
            applicationData.threeDShortAction = ""
            passTokenToFlutter {
                pushToFlutterDashboard(
                    true,
                    shortCutAction = shortcutAction,
                )
            }
        } else if (pushToDashboard || isNewVehicleAdded) {
            if (isContainDashboardRefresh == true) { // Passed the refresh value false
                pushToFlutterDashboard(isDashboardRefresh ?: true)
            } else {
                FlutterOneAppUtil.sendBasicChannelMessage(
                    engineId = DASHBOARD_ENGINE_ID,
                    channelName = UPDATE_LOCALE_CHANNEL,
                    param = AppLanguageUtils.getCurrentLocaleString(),
                )
                passTokenToFlutter {
                    vehicleListPayloadChannel?.send(
                        Gson().toJson(applicationData.getVehicleList() ?: ""),
                    )
                }
            }
        } else if (pushCategory != null) {
            handlePushCategory(pushCategory, map)
        } else if (pushToWalletHome) {
            passTokenToFlutter {
                pushToFlutterScreen(
                    WALLET_HOME,
                    flutterData ?: {},
                )
            }
        } else if (pushToVehicleSoftWareUpdate) {
            passTokenToFlutter {
                pushToFlutterScreen(GO_TO_VEHICLE_INFO_SOFTWARE_UPDATE, flutterData ?: {})
            }
        } else if (pushToAccountOptions) {
            isFromTFSScreen = true
            passTokenToFlutter {
                passTFSTokenToFlutter {
                    pushToFlutterScreen(
                        ACCOUNT_OPTIONS,
                        accountDetails ?: {},
                    )
                }
            }
        } else if (pushToPayment) {
            isFromTFSScreen = true
            passTokenToFlutter {
                passTFSTokenToFlutter {
                    pushToFlutterScreen(
                        FINANCE_CREATE_PAYMENT_PAGE,
                        accountDetails ?: {},
                    )
                }
            }
        } else if (requestOtp) {
            passTokenToFlutter {
                passTFSTokenToFlutter {
                    pushToFlutterScreen(
                        FINANCE_ACCESS_ACCOUNT_PAGE,
                        authenticateResponseModel ?: {},
                    )
                }
            }
        } else if (pushToLinkAccount) {
            passTokenToFlutter {
                passTFSTokenToFlutter {
                    pushToFlutterScreen(
                        FINANCE_LINK_ACCOUNT_PAGE,
                        flutterData ?: {},
                    )
                }
            }
        } else if (pushToOnlinePolicies) {
            passTokenToFlutter {
                passTFSTokenToFlutter {
                    pushToFlutterScreen(
                        FINANCE_ONLINE_AGREEMENT,
                        flutterData ?: {},
                    )
                }
            }
        } else if (pushToElectronicAgreement) {
            passTokenToFlutter {
                passTFSTokenToFlutter {
                    pushToFlutterScreen(
                        FINANCE_ELECTRONIC_AGREEMENT,
                        flutterData ?: {},
                    )
                }
            }
        } else if (pushToEVSwap) {
            passTokenToFlutter {
                pushToFlutterScreen(GO_TO_EV_SWAP, flutterData ?: {})
            }
        } else if (pushToCleanAssistDetail) {
            passTokenToFlutter {
                pushToFlutterScreen(GO_TO_CLEAN_ASSIST_DETAIL, flutterData ?: {})
            }
        }
    }

    private fun handlePushCategory(
        category: String?,
        map: String?,
    ) {
        when (category) {
            ToyotaFCMService.FLEX_RENTAL -> {
                val result =
                    if (map == null) {
                        val json = JSONObject()
                        json.put("category", category)
                        json.toString()
                    } else {
                        val json = JSONObject(map)
                        json.put("category", category)
                        json.toString()
                    }
                pushMessageClickChannel?.send(result)
            }

            ToyotaFCMService.TM_INSTALL_CATEGORY,
            ToyotaFCMService.TM_RELEASE_CATEGORY,
            ToyotaFCMService.TM_UPDATE_CATEGORY,
            -> {
                routeChannel?.send(
                    Gson().toJson(
                        FlutterNavigationEntity(
                            DASHBOARD_PAGE,
                            false,
                            routeData =
                                DeeplinkPayload(
                                    isDashboardRefresh = false,
                                    isDeepLink = true,
                                    category = category,
                                    vin = map ?: "",
                                ),
                        ),
                    ),
                )
            }

            ToyotaFCMService.OTA_UPDATES_21MM -> {
                map?.let { it ->
                    showProgressDialog()
                    try {
                        val jsonMap = JSONObject(it)
                        val vin = jsonMap[ToyotaConstants.VIN]
                        redirectOtaFlow(vin.toString())
                    } catch (exception: JSONException) {
                        dismissProgressDialog()
                        LogTool.e(tag, "Couldn't parse map for $category. Map: $map")
                    }
                }
            }
        }
    }

    private fun redirectOtaFlow(vin: String) {
        lifecycleScope.launch {
            // getUpdateNotificationStatus is a suspending function
            val status = viewModel.getUpdateNotificationStatus(vin)
            if (status == 0) {
                dismissProgressDialog()
                return@launch // Nowhere to redirect for unused status value
            }
            if (status == ToyotaConstants.SOFTWARE_UPDATE_AVAILABLE_21MM) {
                val softwareUpdateIntent =
                    Intent(context, VehicleSoftware21MMParentActivity::class.java)
                softwareUpdateIntent.putExtra(ToyotaConstants.VIN, vin)
                startActivity(softwareUpdateIntent)
            } else {
                sendToVehicleSoftwareUpdate(status)
            }
            dismissProgressDialog()
        }
    }

    private fun sendToVehicleSoftwareUpdate(status: Int) {
        val arguments: HashMap<String, Int> = HashMap()
        flutterEngine?.dartExecutor?.binaryMessenger?.let { binaryMessenger ->
            arguments["softwareUpdateStatus"] = status
            val channel = MethodChannel(binaryMessenger, NATIVE_DASHBOARD_METHOD_CHANNEL)
            channel.invokeMethod(VEHICLE_SOFTWARE_UPDATE_INVOKE_METHOD, arguments)
        }
    }

    private fun registerFlutterBroadcast() {
        flutterBroadcast = FlutterBroadcast()
        val intentFilter = IntentFilter()
        intentFilter.addAction(ToyotaConstants.FLUTTER_FCM_ACTION)
        intentFilter.addAction(ToyotaConstants.FLUTTER_FCM_TOKEN)
        LocalBroadcastManager
            .getInstance(requireActivity().applicationContext)
            .registerReceiver(flutterBroadcast, intentFilter)
    }

    private fun unregisterFlutterBroadcast() {
        if (::flutterBroadcast.isInitialized) {
            LocalBroadcastManager
                .getInstance(requireActivity().applicationContext)
                .unregisterReceiver(flutterBroadcast)
        }
    }

    private fun listenForEvent() {
        disposable =
            RxBus.get().toFlowable().subscribe { event ->
                when (event) {
                    is LaunchVehicleTabEvent -> openFlutterVehicleTab()
                    is LaunchMobilityTabEvent -> openFlutterMobilityTab()
                    is LaunchVehicleSwitcherEvent -> openFlutterSwitcherPage()
                    is LaunchAssistTabEvent -> openAssistTab()
                    is LaunchFindTabEvent -> openFindtab()
                    is LaunchLoginPageEvent -> signOut()
                }
            }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::disposable.isInitialized) {
            disposable.dispose()
        }
        unregisterFlutterBroadcast()
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        initMethodChannel(flutterEngine)
        initMessageChannel(flutterEngine)
        initFcmMessageChannel(flutterEngine)
        initApptentiveMethodChannel(flutterEngine)
        forgerockChannelManager =
            ForgerockChannelManager(
                logoutCallback = {
                    signOut()
                },
                applicationData = applicationData,
                oneAppPreferenceModel = oneAppPreferenceModel,
            )
        forgerockChannelManager?.configureFlutterEngine(flutterEngine)
        forgerockChannelManager?.onAttachedToActivity(activity)
    }

    override fun cleanUpFlutterEngine(flutterEngine: FlutterEngine) {
        super.cleanUpFlutterEngine(flutterEngine)
        forgerockChannelManager?.onDetachedFromEngine()
    }

    private fun initApptentiveMethodChannel(flutterEngine: FlutterEngine) {
        val mMethodChannel =
            MethodChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                APPTENTIVE_METHOD_CHANNEL,
            )

        mMethodChannel.setMethodCallHandler { methodCall: MethodCall, result: MethodChannel.Result ->
            if (methodCall.method == FlutterMethodChannel.engageServiceAppointmentSuccess) {
                lifecycleScope.launch {
                    delay(4000L)
                    viewModel.engageEvent(ApptentiveEvent.SCHEDULE_APPOINTMENT_SUCCESS)
                }
            }
        }
    }

    @OptIn(ExperimentalMaterialApi::class)
    private fun initMethodChannel(flutterEngine: FlutterEngine) {
        val mMethodChannel =
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, NATIVE_METHOD_CHANNEL)
        mMethodChannel.setMethodCallHandler { methodCall: MethodCall, result: MethodChannel.Result ->
            when (methodCall.method) {
                FlutterMethodChannel.CallDeviceTokenApi -> {
                    sendRegisterDeviceRequest()
                    result.success(true)
                }

                FlutterMethodChannel.GetOneAppAccessToken -> refreshAccessToken(result)
                FlutterMethodChannel.GetOneAppAttToken ->
                    result.success(
                        networkDataProvider.attToken(),
                    )

                FlutterMethodChannel.GetProxyAddress -> proxyAddress(result)
                FlutterMethodChannel.GetOneAppEnv -> result.success(BuildConfig.FLAVOR_environment)
                FlutterMethodChannel.GetOneAppLocale ->
                    result.success(
                        AppLanguageUtils.getCurrentLocaleString(),
                    )

                FlutterMethodChannel.OneAppFinish -> {
                    onBackPressed()
                    result.success(true)
                }

                FlutterMethodChannel.OneAppLogEvent -> onAppLog(methodCall, result)
                FlutterMethodChannel.OneAppDataDogLog -> dataDogLog(methodCall, result)
                FlutterMethodChannel.VehicleList -> vinList(methodCall, result)
                FlutterMethodChannel.SelectedVin -> selectedVin(methodCall, result)
                FlutterMethodChannel.OneAppBottomBarTap -> result.success(true)
                FlutterMethodChannel.FcmDeviceId -> {
                    result.success(oneAppPreferenceModel.getDeviceToken())
                }

                FlutterMethodChannel.Account -> {
                    startActivity(
                        Intent(context, DashboardActivity::class.java)
                            .putExtra(kNavItem, DashboardActivity.NavItemType.ACCOUNT),
                    )
                    result.success(true)
                }

                FlutterMethodChannel.ScheduleService -> {
                    val odometerValue = methodCall.argument<String>("odometerValue")
                    if (odometerValue != null && applicationData.getSelectedVehicle() != null) {
                        applicationData.mileage[applicationData.getSelectedVehicle()!!.vin] =
                            odometerValue.toInt()
                    }
                    startActivity(Intent(context, ScheduleMaintenanceMainActivity::class.java))
                    result.success(true)
                }

                FlutterMethodChannel.PrivacyPortal -> {
                    result.success(navigateToPrivacyPortal())
                }

                FlutterMethodChannel.Notification -> {
                    notificationContract(context).launch(
                        Intent(context, DashboardActivity::class.java)
                            .putExtra(kNavItem, DashboardActivity.NavItemType.ALERTS),
                    )
                    result.success(true)
                }

                FlutterMethodChannel.AccountInfoPayload -> accountInfoPayload(methodCall, result)
                FlutterMethodChannel.MyDestination -> {
                    viewModel.getLocationDetails()
                    result.success(true)
                }

                FlutterMethodChannel.PaymentMethods -> {
                    startActivity(Intent(context, PaymentMethodsActivity::class.java))
                    result.success(true)
                }

                FlutterMethodChannel.GetProfilePicture -> {
                    viewModel.getProfilePicture()
                    methodChannelResult = result
                }

                FlutterMethodChannel.KeepMeSignedInBioMetricsStatus ->
                    keepMeSignedInBioMetricsStatus(
                        methodCall,
                        result,
                    )

                FlutterMethodChannel.ShowLCFSDataConsent -> showLCFSDataConsent(methodCall, result)
                FlutterMethodChannel.SendToCar -> {
                    viewModel.lookUpPoi(
                        methodCall,
                        result,
                    ) { res, poi -> viewModel.sendPoiToCar(res, poi) }
                }

                FlutterMethodChannel.AddFavorite -> {
                    viewModel.lookUpPoi(methodCall, result) { res, poi ->
                        viewModel.addFavoritePoi(res, poi)
                    }
                }

                FlutterMethodChannel.LoadFavorites -> {
                    viewModel.loadFavorites(result)
                }

                FlutterMethodChannel.IsFavorite -> {
                    viewModel.mapPlaceIDsToFav(
                        result,
                        methodCall.argument<ArrayList<String>>("placeIds"),
                    )
                }

                FlutterMethodChannel.RemoveFavorite -> {
                    viewModel.lookUpPoi(methodCall, result) { res, poi ->
                        viewModel.removeFavorites(res, poi)
                    }
                }

                FlutterMethodChannel.PinRest -> {
                    result.success(navigateToPinReset())
                }

                FlutterMethodChannel.VehicleFeatureOverrides -> {
                    if (BuildConfig.DEBUG) {
                        val vin = methodCall.argument<String>("vin")
                        vin?.let {
                            result.success(
                                appFeatureFlags.getFeatureOverrides(it),
                            )
                        }
                    } else {
                        result.success(HashMap<String, Int>())
                    }
                }

                FlutterMethodChannel.VehicleHealthReport -> {
                    val i =
                        VehicleHealthReportActivity.getIntent(
                            activity,
                            applicationData.getSelectedVehicle(),
                        )
                    startActivity(i)
                    result.success(true)
                }

                FlutterMethodChannel.isBleEnabledCheck -> {
                    result.success(isBluetoothEnable())
                }

                FlutterMethodChannel.howtoVideos -> {
                    val hwtovideosPayload = methodCall.argument<String>("howToVideosPayload")
                    val gson = Gson()
                    val howToVideos =
                        gson.fromJson<ArrayList<HowToVideo>>(
                            hwtovideosPayload,
                            object :
                                TypeToken<ArrayList<HowToVideo>>() {}.type,
                        )

                    analyticsLogger.logEventWithParameter(
                        AnalyticsEventParam.GARAGE_SELECT,
                        AnalyticsEventParam.GARAGE_HOW_TO_VIDEOS,
                    )
                    startActivity(
                        Intent(activity, HowToVideosActivity::class.java)
                            .putParcelableArrayListExtra(
                                HowToVideosActivity.EXTRA_HOW_TO_VIDOES,
                                howToVideos,
                            ),
                    )
                    result.success(true)
                }

                FlutterMethodChannel.getUserProfileName -> {
                    var profileName = ToyotaConstants.EMPTY_STRING
                    idpData.getUserProfileName().let { userProfileName ->
                        profileName = userProfileName
                    }
                    result.success(profileName)
                }

                FlutterMethodChannel.savePreferredDealerToUserProfile -> {
                    viewModel.savePreferredDealerToUserProfile(result, methodCall)
                }

                FlutterMethodChannel.appUpdateRequired -> {
                    startActivity(
                        Intent(Intent.ACTION_VIEW, Uri.parse(ToyotaConstants.APP_GOOGLE_PLAY_URL)),
                    )
                }

                FlutterMethodChannel.vehicleSoftwareUpdate -> {
                    startActivity(Intent(context, VehicleSoftware21MMParentActivity::class.java))
                }

                FlutterMethodChannel.back -> {
                    if (isFromTFSScreen) {
                        activity?.let {
                            it.setResult(Activity.RESULT_OK)
                            isFromTFSScreen = false
                            it.finish()
                        }
                    } else {
                        activity?.finish()
                    }
                }

                FlutterMethodChannel.backToDashboard -> {
                    activity?.let {
                        it.setResult(Activity.RESULT_OK, Intent())
                        it.finish()
                    }
                }

                FlutterMethodChannel.setStoreChargeSessionId -> {
                    val chargeSessionId = methodCall.argument<String>("sessionId")
                    chargeSessionId?.let {
                        oneAppPreferenceModel.setStoreChargeSessionId(it)
                    }
                    result.success(true)
                }

                FlutterMethodChannel.setStoreChargePartner -> {
                    val chargePartnerId = methodCall.argument<String>("partner")
                    chargePartnerId?.let {
                        oneAppPreferenceModel.setStoreChargePartner(it)
                    }
                    result.success(true)
                }

                FlutterMethodChannel.tfsOtpSuccess -> {
                    val tfsIdToken = methodCall.argument<String>("tfsSession")
                    LogTool.d("tfsOtpSuccess", "tfsOtpSuccess--$tfsIdToken")
                    tfsIdToken?.let {
                        oneAppPreferenceModel.setAuthenticateIdToken(it)
                    }
                    activity?.let {
                        it.setResult(Activity.RESULT_OK)
                        it.finish()
                    }
                    result.success(true)
                }

                FlutterMethodChannel.setStoredUnLinkUser -> {
                    val vin = methodCall.argument<String>("vin")
                    val unLinkUser = methodCall.argument<Boolean>("value")
                    unLinkUser?.let {
                        oneAppPreferenceModel.setTfsUnLinkUser(it)
                        if (it) {
                            oneAppPreferenceModel.setTFSAccessToken(ToyotaConstants.EMPTY_STRING)
                            oneAppPreferenceModel.setTFSRefreshToken(ToyotaConstants.EMPTY_STRING)
                            oneAppPreferenceModel.setTFSIdToken(ToyotaConstants.EMPTY_STRING)
                            oneAppPreferenceModel.setTFSExpireIn(0)
                        }
                    }
                    activity?.let {
                        it.setResult(Activity.RESULT_OK)
                        it.finish()
                    }
                    result.success(true)
                }

                FlutterMethodChannel.backAndStoreCleanAssistEnroll -> {
                    val caEnrollStatus = methodCall.argument<Boolean>("caEnrollStatus")
                    LogTool.d("caEnrollStatus", "caEnrollStatus--$caEnrollStatus")
                    caEnrollStatus?.let {
                        applicationData.getSelectedVehicle()?.let { vehicle ->
                            oneAppPreferenceModel.setAnnouncementCAEnroll(it, vehicle.vin)
                        }
                    }
                    activity?.finish()
                    result.success(true)
                }

                FlutterMethodChannel.activateByCode -> {
                    navigateToActivateByCode()
                    result.success(true)
                }
            }
        }
    }

    private fun isBluetoothEnable(): Boolean =
        with(context) {
            getSystemService(Context.BLUETOOTH_SERVICE)?.run {
                (this as BluetoothManager).run {
                    this.adapter.isEnabled
                }
            } ?: kotlin.run {
                return false
            }
        }

    private fun dataDogLog(
        methodCall: MethodCall,
        result: MethodChannel.Result,
    ) {
        val category = methodCall.argument<String>("category")
        val name = methodCall.argument<String>("name")
        val logLevel = methodCall.argument<String>("logLevel")
        name?.let {
            // DigitalMopKeyUtils.appendLog(it, isDataDogRequired = true, category = category, logLevel = logLevel)
        }
    }

    private fun showLCFSDataConsent(
        methodCall: MethodCall,
        result: MethodChannel.Result,
    ) {
        val lcfsImageUrl = methodCall.argument<String>("lcfsImageUrl")
        activity?.let {
            applicationData.getSelectedVehicle()?.let { it1 ->
                it.startActivityForResult(
                    DataConsentLCFSDetailActivity.getIntent(
                        it,
                        it1,
                        true,
                        lcfsImageUrl,
                    ),
                    reqLcfs,
                )
            }
        }
        result.success(true)
    }

    private fun sendRegisterDeviceRequest() {
        adUtil.getAdvertisingId(
            object : AdUtil.AdUtilCallback {
                override fun onComplete(
                    adId: String?,
                    success: Boolean,
                ) {
                    val sendDeviceStatusRequest =
                        SendDeviceStatusRequest(
                            oneAppPreferenceModel.getGuid(),
                            oneAppPreferenceModel.getDeviceToken(),
                            adId,
                        )
                    remoteManager.sendDeviceStatus(sendDeviceStatusRequest, BaseCallback())
                }
            },
        )
    }

    private fun keepMeSignedInBioMetricsStatus(
        methodCall: MethodCall,
        result: MethodChannel.Result,
    ) {
        val keepLoginValue = methodCall.argument<Boolean>("keepLoginValue")
        val bioMetricsValue = methodCall.argument<Boolean>("bioMetricsValue")
        if (keepLoginValue == true) {
            oneAppPreferenceModel.setKeepMeLogin(oneAppPreferenceModel.getGuid())
        }
        oneAppPreferenceModel.setBiometricEnabled(bioMetricsValue == true)
        result.success(true)
    }

    private fun accountInfoPayload(
        methodCall: MethodCall,
        result: MethodChannel.Result,
    ) {
        val accountPayload = methodCall.argument<String>("accountPayload")
        val accountInfoData =
            Gson().fromJson<AccountInfoSubscriber>(
                accountPayload.toString(),
                object : TypeToken<AccountInfoSubscriber>() {}.type,
            )
        oneAppPreferenceModel.setAccountInfoSubscriber(accountInfoData)
        result.success(true)
    }

    private fun selectedVin(
        methodCall: MethodCall,
        result: MethodChannel.Result,
    ) {
        val vin = methodCall.argument<String>("selectedVinData")
        applicationData.setSelectedVehicle(
            applicationData.getVehicleList()?.single { it -> it.vin == vin },
        )
        applicationData.generationCode = applicationData.getSelectedVehicle()?.generation ?: ""
        result.success(true)
    }

    private fun vinList(
        methodCall: MethodCall,
        result: MethodChannel.Result,
    ) {
        val vehicleListStr = methodCall.argument<String>("vehicleListData")
        val gson = Gson()
        val vehicleList =
            gson.fromJson<ArrayList<VehicleInfo>>(
                vehicleListStr,
                object :
                    TypeToken<ArrayList<VehicleInfo>>() {}.type,
            )
        applicationData.setVehicleList(vehicleList)
        result.success(true)
    }

    private fun onAppLog(
        methodCall: MethodCall,
        result: MethodChannel.Result,
    ) {
        val name = methodCall.argument<String>("name")
        val param = methodCall.argument<Map<String, String>>("param")
        val category = methodCall.argument<String>("category")
        val eventName =
            name?.let {
                if (it.length > 40) {
                    it.substring(0, 40)
                } else {
                    it
                }
            } ?: ""
        when (category) {
            "FL_SCR" -> {
                val bundle =
                    Bundle().apply {
                        putString("fl_screen_name", eventName)
                        putString("fl_screen_class", eventName)
                    }
                FirebaseAnalytics
                    .getInstance(context)
                    .logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
            }

            "FL_EVT" -> {
                val bundle =
                    Bundle().apply {
                        param?.forEach { (t, u) ->
                            putString(t, u)
                        }
                    }
                FirebaseAnalytics.getInstance(context).logEvent(eventName, bundle)
            }
        }
        result.success(true)
    }

    private fun proxyAddress(result: MethodChannel.Result) {
        var proxyAddress: String? = null
        try {
            val proxy = System.getProperty("http.proxyHost")
            val port = System.getProperty("http.proxyPort")
            proxyAddress =
                if (proxy == null && port == null) {
                    null
                } else {
                    "$proxy:$port"
                }
        } catch (e: Exception) {
        }
        result.success(proxyAddress)
    }

    private fun launchDestinations(
        context: Context,
        bundle: Bundle,
    ) {
        destinationsContract(context).launch(bundle)
    }

    fun sendMarketingDataConsent(consentStaus: String?) {
        val arguments: HashMap<String, Any> = HashMap()
        flutterEngine?.dartExecutor?.binaryMessenger?.let { binaryMessenger ->
            if (!consentStaus.isNullOrEmpty()) {
                arguments["consentStatus"] = consentStaus
                val channel =
                    MethodChannel(
                        binaryMessenger,
                        NATIVE_METHOD_CHANNEL,
                    )
                channel.invokeMethod(
                    MARKETING_CONSENT_INVOKE_METHOD,
                    arguments,
                    object : MethodChannel.Result {
                        override fun success(result: Any?) {
                        }

                        override fun error(
                            s: String,
                            s1: String?,
                            o: Any?,
                        ) {
                        }

                        override fun notImplemented() {}
                    },
                )
            }
        }
    }

    fun sendChannel(
        channelName: String,
        methodName: String,
        arguments: HashMap<String, Boolean>,
    ) {
        flutterEngine?.dartExecutor?.binaryMessenger?.let { binaryMessenger ->
            val channel =
                MethodChannel(
                    binaryMessenger,
                    channelName,
                )
            channel.invokeMethod(
                methodName,
                arguments,
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                    }

                    override fun error(
                        errorCode: String,
                        errorMessage: String?,
                        errorDetails: Any?,
                    ) {
                    }

                    override fun notImplemented() {
                    }
                },
            )
        }
    }

    fun updateAppointmentList() {
        flutterEngine?.dartExecutor?.binaryMessenger?.let { binaryMessenger ->
            val arguments: HashMap<String, String> = HashMap()
            val channel =
                MethodChannel(
                    binaryMessenger,
                    NATIVE_APPOINTMENT_METHOD_CHANNEL,
                )
            channel.invokeMethod(
                APPOINTMENTLIST_UPDATE_INVOKE_METHOD,
                arguments,
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                    }

                    override fun error(
                        s: String,
                        s1: String?,
                        o: Any?,
                    ) {
                    }

                    override fun notImplemented() {
                    }
                },
            )
        }
    }

    private fun openFlutterSwitcherPage() {
        flutterEngine?.dartExecutor?.binaryMessenger?.let { binaryMessenger ->
            val channel = MethodChannel(binaryMessenger, NATIVE_METHOD_CHANNEL)
            channel.invokeMethod("vehicleSwitcherPage", null)
        }
    }

    private fun openAssistTab() {
        flutterEngine?.dartExecutor?.binaryMessenger?.let { binaryMessenger ->
            val channel = MethodChannel(binaryMessenger, NATIVE_METHOD_CHANNEL)
            channel.invokeMethod(
                "assistTab",
                true,
            ) // pass argument as true if want to close bottom sheet in flutter
        }
    }

    private fun openFindtab() {
        flutterEngine?.dartExecutor?.binaryMessenger?.let { binaryMessenger ->
            val channel = MethodChannel(binaryMessenger, NATIVE_METHOD_CHANNEL)
            channel.invokeMethod(
                "findTab",
                true,
            ) // pass argument as true if want to close bottom sheet in flutter
        }
    }

    private fun pushToFlutterScreen(
        flutterScreen: String,
        routeData: Any,
    ) {
        nativeToFlutterNavigationChannel?.send(
            Gson().toJson(
                FlutterNavigationEntity(
                    flutterScreen,
                    vin = applicationData.getSelectedVehicle()?.vin,
                    vinList = Gson().toJson(applicationData.getVehicleList() ?: ""),
                    routeData = routeData,
                    isDarkTheme = oneAppPreferenceModel.isDarkModeEnabled(),
                ),
            ),
        )
    }

    private fun pushToFlutterDashboard(
        isDashboardRefresh: Boolean,
        isDeepLink: Boolean = false,
        shortCutAction: String? = null,
        branchReferringParam: String? = null,
        category: String? = null,
    ) {
        routeChannel?.send(
            Gson().toJson(
                FlutterNavigationEntity(
                    DASHBOARD_PAGE,
                    routeData =
                        DeeplinkPayload(
                            isDashboardRefresh = isDashboardRefresh,
                            isDeepLink = isDeepLink,
                            shortcutAction = shortCutAction,
                            branchReferringParam = branchReferringParam,
                            category = category,
                        ),
                ),
            ),
        )
    }

    private fun openFlutterVehicleTab() {
        flutterEngine?.dartExecutor?.binaryMessenger?.let { binaryMessenger ->
            val channel = MethodChannel(binaryMessenger, NATIVE_METHOD_CHANNEL)
            channel.invokeMethod(
                "dashboardTab",
                true,
            ) // pass argument as true if want to close bottom sheet in flutter
        }
    }

    private fun openFlutterMobilityTab() {
        flutterEngine?.dartExecutor?.binaryMessenger?.let { binaryMessenger ->
            val channel = MethodChannel(binaryMessenger, NATIVE_METHOD_CHANNEL)
            channel.invokeMethod("mobilityTab", null)
        }
    }

    private fun initMessageChannel(flutterEngine: FlutterEngine) {
        routeChannel =
            BasicMessageChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                "oneapp_page_path",
                StandardMessageCodec.INSTANCE,
            )
        tokenChannel =
            BasicMessageChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                "oneapp_token_json",
                StandardMessageCodec.INSTANCE,
            )
        tfsTokenChannel =
            BasicMessageChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                "tfs_token_json",
                StandardMessageCodec.INSTANCE,
            )
        nativeToFlutterNavigationChannel =
            BasicMessageChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                "native_flutter_navigation_channel",
                StandardMessageCodec.INSTANCE,
            )
        vehicleListPayloadChannel =
            BasicMessageChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                "oneapp_vehicle_list_json",
                StandardMessageCodec.INSTANCE,
            )
    }

    private fun initFcmMessageChannel(flutterEngine: FlutterEngine) {
        pushTokenChannel =
            BasicMessageChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                "oneapp_receive_push_token",
                StandardMessageCodec.INSTANCE,
            )
        pushMessageChannel =
            BasicMessageChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                "oneapp_init_fcm_json",
                StandardMessageCodec.INSTANCE,
            )
        pushMessageClickChannel =
            BasicMessageChannel(
                flutterEngine.dartExecutor.binaryMessenger,
                "oneapp_push_click_json",
                StandardMessageCodec.INSTANCE,
            )
    }

    private fun passTokenToFlutter(callback: () -> Unit) {
        analyticsLogger.logEvent(AnalyticsEvent.FLUTTER_DASHBOARD_TOKEN_INITIATED)
        LogTool.d(tag, AnalyticsEvent.FLUTTER_DASHBOARD_TOKEN_INITIATED.eventName)
        val frTokensResponseStr =
            Gson().toJson(
                FRTokensResponse(
                    success = true,
                    payload =
                        FRTokensPayload(
                            idToken = IDPData.instance?.idToken,
                            accessToken = IDPData.instance?.accessToken,
                            refreshToken = IDPData.instance?.refreshToken,
                            expiration = IDPData.instance?.accessTokenExpireTime,
                        ),
                ),
            )
        tokenChannel?.send(frTokensResponseStr) { _ ->
            LogTool.d(tag, AnalyticsEvent.FLUTTER_DASHBOARD_CALL_BACK.eventName)
            analyticsLogger.logEvent(AnalyticsEvent.FLUTTER_DASHBOARD_CALL_BACK)
            requireActivity().runOnUiThread {
                callback.invoke()
            }
        }
    }

    private fun passTFSTokenToFlutter(callback: () -> Unit) {
        val tfsIdToken = oneAppPreferenceModel.getTFSIdToken()
        val frTokensResponseStr =
            Gson().toJson(
                TFSTokensPayload(
                    id_token = tfsIdToken.ifEmpty { null },
                    access_token = oneAppPreferenceModel.getTFSAccessToken(),
                    refresh_token = oneAppPreferenceModel.getTFSRefreshToken(),
                    expires_in = oneAppPreferenceModel.getTFSExpireIn().toLong(),
                ),
            )
        tfsTokenChannel?.send(frTokensResponseStr) { _ ->
            requireActivity().runOnUiThread {
                callback.invoke()
            }
        }
    }

    private fun refreshAccessToken(result: MethodChannel.Result) {
        result.success(idpHelper.getAccessToken())
    }

    private fun signOut() {
        accountApiManager.logout(
            oneAppPreferenceModel.getGuid(),
            oneAppPreferenceModel.getDeviceToken(),
            object : LogoutCallback() {
                override fun onLogoutAPISuccess() {
                    (activity as? DashboardFlutterActivity)?.performLogout()
                }

                override fun onLogoutAPIError(errorMsg: String?) {
                    (activity as? DashboardFlutterActivity)?.performLogout()
                }
            },
        )
    }

    fun showForceAllowPermissionDialog(string: String = getString(R.string.Common_permission_msg)) {
        DialogUtil.showDialog(
            activity,
            null,
            string,
            getString(R.string.Common_ok),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    PermissionPageUtil.intentToAppSettings(activity)
                }

                override fun onCancelClick() {}
            },
            false,
        )
    }

    private fun navigateToPrivacyPortal(): Boolean {
        if (applicationData.getVehicleList()?.isEmpty() == true) {
            return true // Remain in empty dashboard
        }

        val intent =
            if (applicationData.getVehicleList()?.size == 1) {
                IntentUtil.getEditConsentIntent(
                    context,
                    applicationData.getSelectedVehicle(),
                    regionManager,
                )
            } else {
                Intent(requireContext(), LegalSelectVehicleActivity::class.java)
            }
        intent.flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
        startActivity(intent)
        return true
    }

    private fun navigateToPinReset(): Boolean {
        val intent = Intent(requireActivity(), ResetPinActivity::class.java)
        startActivity(intent)
        return true
    }

    private fun navigateToActivateByCode() {
        val intent = Intent(activity, QRScanActivity::class.java)
        intent.putExtra(QRScanActivity.SHOW_QR_CODE, false) // Set to false to show manual code entry
        startActivity(intent)
    }

    inner class FlutterBroadcast : BroadcastReceiver() {
        override fun onReceive(
            context: Context?,
            intent: Intent?,
        ) {
            val map = intent?.getStringExtra(ToyotaFCMService.MAP)
            if (map != null) {
                if (intent.action == ToyotaConstants.FLUTTER_FCM_ACTION) {
                    pushMessageChannel?.send(map)
                } else if (intent.action == ToyotaConstants.FLUTTER_FCM_TOKEN) {
                    pushTokenChannel?.send(map)
                }
            }
        }
    }
}
