package com.toyota.oneapp.ui.flutter

/**
 *
 * {
"routeName" : "xxx",
"showBottomBar" : false,
"routeData" : {
"logout" : true
}
}
 */

data class DeeplinkPayload(
    val logout: Boolean = false,
    val canClearBiometric: Boolean = false,
    val isDeepLink: Boolean = false,
    val shortcutAction: String? = null,
    val branchReferringParam: String? = null,
    val category: String? = null,
    val subcategory: String? = null,
    val vin: String? = null,
    val isDashboardRefresh: Boolean? = true,
)
