package com.toyota.oneapp.ui.privacyportal

import android.view.View
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent

interface PrivacyPortalMasterConsentListener {
    fun onMasterConsentClick(
        view: View,
        dataConsent: CombineDataConsent,
    )
}

interface PrivacyPortalConsentItemClickListener {
    fun onConsentItemClick(
        view: View,
        dataConsent: CombineDataConsent,
    )
}

interface PrivacyPortalConsentManageListener {
    fun onDeclineConsentClick(
        view: View,
        dataConsent: CombineDataConsent,
    )

    fun onAcceptConsentClick(
        view: View,
        dataConsent: CombineDataConsent,
    )
}
