package com.toyota.oneapp.ui.privacyportal.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.DataBindingAdapter
import com.toyota.oneapp.databinding.ItemConnectServiceConsentsBinding
import com.toyota.oneapp.model.combineddataconsent.SubscriptionProduct
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import java.util.concurrent.Executor

class PrivacyPortalConnectedServiceItemAdapter(
    executor: Executor,
    private val onProductItemClick: (SubscriptionProduct) -> Unit,
) : DataBindingAdapter<SubscriptionProduct, ItemConnectServiceConsentsBinding>(
        executor = executor,
        diffCallback = ConnectedServiceProductItemDiffUtil(),
    ) {
    override fun createBinding(
        parent: ViewGroup,
        viewType: Int,
    ): ItemConnectServiceConsentsBinding =
        DataBindingUtil.inflate(
            LayoutInflater.from(parent.context),
            R.layout.item_connect_service_consents,
            parent,
            false,
        )

    override fun bind(
        binding: ItemConnectServiceConsentsBinding,
        position: Int,
    ) {
        val item = getItem(position)
        binding.root.setOnClickListener { onProductItemClick.invoke(item) }
        DataBindingAdapters.loadImage(
            binding.imageServiceConnect,
            item.imageUrl,
            binding.imageServiceConnect.context.getDrawable(R.drawable.appauth_96dp),
        )
        binding.imageServiceConnect.contentDescription = item.name
        binding.textServiceConnectTitle.text = item.name
        binding.textServiceConnectSubtitle.text = item.name
    }
}

class ConnectedServiceProductItemDiffUtil : DiffUtil.ItemCallback<SubscriptionProduct>() {
    override fun areItemsTheSame(
        oldItem: SubscriptionProduct,
        newItem: SubscriptionProduct,
    ): Boolean = oldItem == newItem

    override fun areContentsTheSame(
        oldItem: SubscriptionProduct,
        newItem: SubscriptionProduct,
    ): Boolean = oldItem == newItem
}
