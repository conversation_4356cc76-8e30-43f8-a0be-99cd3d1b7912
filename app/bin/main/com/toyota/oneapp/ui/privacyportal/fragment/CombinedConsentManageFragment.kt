package com.toyota.oneapp.ui.privacyportal.fragment

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentCombinedDataConsentManageBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.privacyportal.ConsentTextHtmlUtils
import com.toyota.oneapp.ui.privacyportal.viewmodel.PrivacyPortalDataConsentViewModel
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil

@AndroidEntryPoint
class CombinedConsentManageFragment : BaseDataBindingFragment<FragmentCombinedDataConsentManageBinding>() {
    private val viewModel: PrivacyPortalDataConsentViewModel by activityViewModels()
    val args: CombinedConsentManageFragmentArgs by navArgs()

    override fun onViewBound(
        binding: FragmentCombinedDataConsentManageBinding,
        savedInstance: Bundle?,
    ) {
        addListeners()
        showDetails()
    }

    private fun addListeners() {
        viewDataBinding.toolbar.setNavigationOnClickListener { findNavController().popBackStack() }
        viewModel.run {
            declinePageNavigationData.observe(viewLifecycleOwner) {
                findNavController().navigate(
                    CombinedConsentManageFragmentDirections.actionToConsentDeclineFragment(it),
                )
            }
            consentAcceptSuccess.observe(viewLifecycleOwner) {
                if (TssConsentConfirmationFragment.A11_CONSENT_TYPE == args.dataConsent?.consentId) {
                    findNavController().navigate(
                        CombinedConsentManageFragmentDirections.actionToTssConsentDeclineConfirmation(
                            true,
                        ),
                    )
                } else {
                    findNavController().popBackStack(R.id.dataPrivacyPortalFragment, false)
                }
            }
            masterConsentNotEditableData.observe(viewLifecycleOwner) {
                MaterialAlertDialogBuilder(activityContext)
                    .setMessage(
                        getString(R.string.Privacy_Portal_Master_Consent_UnEditable_Description),
                    ).setPositiveButton(R.string.Common_ok, null)
                    .show()
            }
        }
    }

    private fun showDetails() {
        args.dataConsent?.let {
            val manageDetails = it.manageSection
            ConsentTextHtmlUtils()
                .setTextViewHTML(
                    text = viewDataBinding.combinedDataConsentBody,
                    item = it,
                    html = manageDetails?.body ?: "",
                    onDialogSchemeClick = { message -> showMessageDialog(message) },
                )

            viewDataBinding.toolbar.title = it.manageSection?.pageTitle
            viewDataBinding.combinedDataConsentTitle.text = it.name
            DataBindingAdapters.setIsVisible(viewDataBinding.buttonLayout, !it.hideButtons)

            viewDataBinding.combinedDataConsentPositiveButton.run {
                text = it.manageSection?.positiveButtonText
                isEnabled = it.isDeclined
                setOnClickListener { v -> viewModel.onAcceptConsentClick(v, it) }
            }
            viewDataBinding.combinedDataConsentNegativeButton.run {
                text = it.manageSection?.negativeButtonText
                isEnabled = it.isAccepted
                setOnClickListener { v -> viewModel.onDeclineConsentClick(v, it) }
            }
        }
    }

    override fun getLayout(): Int = R.layout.fragment_combined_data_consent_manage

    fun showMessageDialog(message: String) {
        DialogUtil.showDialog(
            requireActivity(),
            null,
            message,
            getString(R.string.ok_label),
        )
    }
}
