package com.toyota.oneapp.ui.privacyportal.fragment

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.oneapp.R
import com.toyota.oneapp.core.IoThreadExecutor
import com.toyota.oneapp.databinding.FragmentPpSubscriptionDetailBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.privacyportal.adapter.ConsentMoreDetailSharedInfoAdapter
import com.toyota.oneapp.ui.privacyportal.viewmodel.PPSubscriptionDetailViewModel
import com.toyota.oneapp.ui.privacyportal.viewmodel.PrivacyPortalDataConsentViewModel
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import java.util.concurrent.Executor
import javax.inject.Inject

@AndroidEntryPoint
class PPSubscriptionDetailFragment : BaseDataBindingFragment<FragmentPpSubscriptionDetailBinding>() {
    @Inject
    @IoThreadExecutor
    lateinit var executor: Executor
    val args: PPSubscriptionDetailFragmentArgs by navArgs()
    private val dataConsentViewModel: PrivacyPortalDataConsentViewModel by activityViewModels()
    private val viewModel: PPSubscriptionDetailViewModel by activityViewModels()

    override fun onViewBound(
        binding: FragmentPpSubscriptionDetailBinding,
        savedInstance: Bundle?,
    ) {
        showSharedData()
        showDetails()
    }

    private fun showDetails() {
        val subscriptionProduct = args.subscriptionProduct

        viewDataBinding.run {
            subscriptionProduct?.let {
                viewModel.showCustomLinkText(
                    it,
                    viewDataBinding.combinedConsentIncludeBottom.declineConsentDescText,
                )
            }

            toolbar.setTitle(subscriptionProduct?.dataShared?.pageTitle)
            toolbar.setNavigationOnClickListener {
                findNavController().popBackStack()
            }

            subscriptionProduct?.imageUrl?.let {
                DataBindingAdapters.loadImage(combinedConsentInclude.subscriptionImage, it, null)
                combinedConsentInclude.subscriptionImage.contentDescription = subscriptionProduct.name
            }

            combinedConsentInclude.subscriptionNameText.text = subscriptionProduct?.dataShared?.title
            combinedConsentInclude.subscriptionDescText.text = subscriptionProduct?.dataShared?.description

            val shareItemAdapter =
                ConsentMoreDetailSharedInfoAdapter(executor).also {
                    it.submitList(subscriptionProduct?.dataShared?.objects)
                }
            DataBindingAdapters.setAdapter(combinedConsentInclude.subscriptionSharedRecyclerView, shareItemAdapter)

            combinedConsentIncludeBottom.declineConsentTitleText.text = subscriptionProduct?.declineConsent?.title
        }
        viewModel.run {
            manageConsentData.observe(viewLifecycleOwner) {
                findNavController().navigate(
                    PPSubscriptionDetailFragmentDirections.actionToConsentDeclineFragment(
                        args.dataConsent,
                    ),
                )
            }
            manageSubscriptionData.observe(viewLifecycleOwner) {
                dataConsentViewModel.navigateToManageSubscription()
            }
        }
    }

    override fun getLayout(): Int = R.layout.fragment_pp_subscription_detail

    private fun showSharedData() {
        val fragment =
            SubscriptionMoreDetailFragment().apply {
                arguments =
                    Bundle().apply {
                        putParcelable(ToyotaConstants.PP_SUBSCRIPTION_PRODUCT, args.subscriptionProduct)
                    }
            }
        childFragmentManager
            .beginTransaction()
            .replace(R.id.more_shared_container, fragment, "SHARED_DATA")
            .commitNow()
    }
}
