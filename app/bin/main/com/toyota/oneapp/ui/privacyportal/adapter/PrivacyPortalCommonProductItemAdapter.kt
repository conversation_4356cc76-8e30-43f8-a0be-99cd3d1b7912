package com.toyota.oneapp.ui.privacyportal.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.DiffUtil
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.DataBindingAdapter
import com.toyota.oneapp.databinding.ItemPpCombinedConsentBinding
import com.toyota.oneapp.databinding.ItemPpConsentProductHeaderBinding
import com.toyota.oneapp.databinding.ItemPpDeclineProductBinding
import com.toyota.oneapp.databinding.ItemPpDeclineProductTwoTextBinding
import com.toyota.oneapp.model.combineddataconsent.CombinedConsentBaseItem
import com.toyota.oneapp.model.combineddataconsent.ConsentProductHeaderItem
import com.toyota.oneapp.model.combineddataconsent.ConsentProductUIItem
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import java.util.concurrent.Executor

class PrivacyPortalDeclineProductItemAdapter(
    executor: Executor,
) : DataBindingAdapter<CombinedConsentBaseItem, ViewDataBinding>(
        executor = executor,
        diffCallback = DeclineProductItemDiffUtil(),
    ) {
    override fun createBinding(
        parent: ViewGroup,
        viewType: Int,
    ): ViewDataBinding =
        when (viewType) {
            0 ->
                DataBindingUtil.inflate<ItemPpConsentProductHeaderBinding>(
                    LayoutInflater.from(parent.context),
                    R.layout.item_pp_consent_product_header,
                    parent,
                    false,
                )
            1 ->
                DataBindingUtil.inflate<ItemPpCombinedConsentBinding>(
                    LayoutInflater.from(parent.context),
                    R.layout.item_pp_decline_product,
                    parent,
                    false,
                )
            2 ->
                DataBindingUtil.inflate<ItemPpCombinedConsentBinding>(
                    LayoutInflater.from(parent.context),
                    R.layout.item_pp_decline_product_two_text,
                    parent,
                    false,
                )
            else -> throw RuntimeException("Not a valid view type")
        }

    override fun bind(
        binding: ViewDataBinding,
        position: Int,
    ) {
        when (binding) {
            is ItemPpConsentProductHeaderBinding -> {
                val item = getItem(position) as ConsentProductHeaderItem
                binding.sectionTitleText.isVisible = item.title.isNotNullOrEmpty()
                binding.sectionTitleText.text = item.title
                binding.titleText.isVisible = item.subTitle.isNotNullOrEmpty()
                binding.titleText.text = item.subTitle
            }
            is ItemPpDeclineProductBinding -> {
                val item = getItem(position) as ConsentProductUIItem
                DataBindingAdapters.loadImage(binding.consentImage, item.imageUrl, binding.root.resources.getDrawable(R.drawable.logo))
                binding.consentNameText.text = item.name
                binding.consentAlertText.isVisible = item.alertMessage.isNotNullOrEmpty()
                binding.consentAlertText.text = item.alertMessage
            }
            is ItemPpDeclineProductTwoTextBinding -> {
                val item = getItem(position) as ConsentProductUIItem
                DataBindingAdapters.loadImage(binding.consentImage, item.imageUrl, binding.root.resources.getDrawable(R.drawable.logo))
                binding.consentNameText.text = item.name
                binding.consentDescText.isVisible = item.description.isNotNullOrEmpty()
                binding.consentDescText.text = item.description
            }
        }
    }

    override fun getItemViewType(position: Int): Int =
        when (getItem(position)) {
            is ConsentProductHeaderItem -> 0
            is ConsentProductUIItem -> {
                val item = getItem(position) as ConsentProductUIItem
                if (item.description?.isEmpty() == true) 1 else 2
            }
            else -> -1
        }
}

class DeclineProductItemDiffUtil : DiffUtil.ItemCallback<CombinedConsentBaseItem>() {
    override fun areItemsTheSame(
        oldItem: CombinedConsentBaseItem,
        newItem: CombinedConsentBaseItem,
    ): Boolean = oldItem == newItem

    override fun areContentsTheSame(
        oldItem: CombinedConsentBaseItem,
        newItem: CombinedConsentBaseItem,
    ): Boolean =
        if (oldItem is ConsentProductHeaderItem && newItem is ConsentProductHeaderItem) {
            oldItem == newItem
        } else if (oldItem is ConsentProductUIItem && newItem is ConsentProductUIItem) {
            oldItem == newItem
        } else {
            false
        }
}
