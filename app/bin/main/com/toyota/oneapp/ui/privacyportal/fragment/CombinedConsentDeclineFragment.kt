package com.toyota.oneapp.ui.privacyportal.fragment

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.R
import com.toyota.oneapp.core.IoThreadExecutor
import com.toyota.oneapp.databinding.FragmentCombinedConsentDeclineBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.privacyportal.ConsentTextHtmlUtils
import com.toyota.oneapp.ui.privacyportal.adapter.PrivacyPortalDeclineProductItemAdapter
import com.toyota.oneapp.ui.privacyportal.viewmodel.PrivacyPortalDataConsentViewModel
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import java.util.concurrent.Executor
import javax.inject.Inject

@AndroidEntryPoint
class CombinedConsentDeclineFragment : BaseDataBindingFragment<FragmentCombinedConsentDeclineBinding>() {
    private val args: CombinedConsentDeclineFragmentArgs by navArgs()
    private val dataConsentViewModel: PrivacyPortalDataConsentViewModel by activityViewModels()

    @Inject
    @IoThreadExecutor
    lateinit var ioExecutor: Executor

    lateinit var adapter: PrivacyPortalDeclineProductItemAdapter

    override fun onViewBound(
        binding: FragmentCombinedConsentDeclineBinding,
        savedInstance: Bundle?,
    ) {
        binding.toolbar.setNavigationOnClickListener { findNavController().popBackStack() }
        observeBaseEvents(dataConsentViewModel)
        initAdapter()
        showDetails()
    }

    private fun initAdapter() {
        adapter =
            PrivacyPortalDeclineProductItemAdapter(ioExecutor)
    }

    private fun showDetails() {
        args.dataConsent?.let { consent ->
            val declinePayload = consent.manageSection?.declinePayload

            viewDataBinding.toolbar.title = declinePayload?.pageTitle

            viewDataBinding.declineImage.run {
                DataBindingAdapters.setIsVisible(this, consent.masterConsent)
                contentDescription = declinePayload?.pageTitle
            }

            viewDataBinding.tvConfirmationCentered.run {
                DataBindingAdapters.setIsVisible(this, consent.masterConsent)
                text = declinePayload?.title
            }

            viewDataBinding.tvConfirmationStart.run {
                DataBindingAdapters.setIsVisible(this, !consent.masterConsent)
                text = declinePayload?.title
            }

            viewDataBinding.tvImportantTitle.run {
                DataBindingAdapters.setIsVisible(this, declinePayload?.subTitleHeader.isNotNullOrEmpty())
                text = declinePayload?.subTitleHeader
            }

            viewDataBinding.consentSubtitleDescription.run {
                DataBindingAdapters.setIsVisible(this, declinePayload?.subTitleHeaderDesc.isNotNullOrEmpty())
                text = declinePayload?.subTitleHeaderDesc
            }

            adapter.submitList(declinePayload?.getAllServices)
            DataBindingAdapters.setAdapter(viewDataBinding.recyclerView, adapter)

            viewDataBinding.ppCombinedDataConsentButtonLayout.dataConsentPositiveButton.text =
                declinePayload?.positiveButtonText

            ConsentTextHtmlUtils()
                .setTextViewHTML(
                    text = viewDataBinding.consentDeclineBody,
                    item = consent,
                    html = declinePayload?.body?.replace("\n", "<br>") ?: "",
                    onDialogSchemeClick = { message -> showMessageDialog(message) },
                )

            viewDataBinding.ppCombinedDataConsentButtonLayout.dataConsentPositiveButton.setOnClickListener {
                dataConsentViewModel.declineConsent(consent, it.context)
            }
        }
        dataConsentViewModel.run {
            hideDeclineData.observe(viewLifecycleOwner) {
                if (TssConsentConfirmationFragment.A11_CONSENT_TYPE == args.dataConsent?.consentId) {
                    findNavController().navigate(
                        CombinedConsentDeclineFragmentDirections.actionToTssConsentDeclineConfirmation(
                            false,
                        ),
                    )
                } else {
                    findNavController().popBackStack(R.id.dataPrivacyPortalFragment, false)
                }
            }
        }
    }

    override fun getLayout(): Int = R.layout.fragment_combined_consent_decline

    fun showMessageDialog(message: String) {
        DialogUtil.showDialog(
            requireActivity(),
            null,
            message,
            getString(R.string.ok_label),
        )
    }
}
