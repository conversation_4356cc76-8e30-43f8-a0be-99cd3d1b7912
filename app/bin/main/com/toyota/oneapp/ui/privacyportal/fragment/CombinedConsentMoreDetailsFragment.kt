package com.toyota.oneapp.ui.privacyportal.fragment

import android.os.Bundle
import com.toyota.oneapp.R
import com.toyota.oneapp.core.IoThreadExecutor
import com.toyota.oneapp.databinding.FragmentPpConsentMoreDetailsBinding
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.privacyportal.adapter.ConsentMoreDetailItemAdapter
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import java.util.concurrent.Executor
import javax.inject.Inject

@AndroidEntryPoint
open class CombinedConsentMoreDetailsFragment : BaseDataBindingFragment<FragmentPpConsentMoreDetailsBinding>() {
    @Inject
    @IoThreadExecutor
    lateinit var executor: Executor
    private var dataConsent: CombineDataConsent? = null

    override fun onViewBound(
        binding: FragmentPpConsentMoreDetailsBinding,
        savedInstance: Bundle?,
    ) {
        dataConsent = arguments?.getParcelable(ToyotaConstants.DATA_CONSENT_KEY)
        showDetails()
    }

    protected open fun showDetails() {
        val adapter = ConsentMoreDetailItemAdapter(executor)
        DataBindingAdapters.setAdapter(viewDataBinding.rvMoreDetails, adapter)

        dataConsent?.detailSection?.let {
            viewDataBinding.tvMoreDetailsTitle.text = it.dataShared?.title
            adapter.submitList(it.dataShared?.items)
        }
    }

    override fun getLayout(): Int = R.layout.fragment_pp_consent_more_details
}
