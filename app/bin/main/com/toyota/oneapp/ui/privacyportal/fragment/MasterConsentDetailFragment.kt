package com.toyota.oneapp.ui.privacyportal.fragment

import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.oneapp.model.combineddataconsent.SubscriptionProduct
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MasterConsentDetailFragment : CombinedConsentDetailsFragment() {
    private val privacyPortalArgs: MasterConsentDetailFragmentArgs by navArgs()

    override fun showDetails() {
        DataBindingAdapters.setIsVisible(viewDataBinding.ppDataConsentInclude.root, true)

        viewDataBinding.run {
            privacyPortalArgs.dataConsent?.let { consent ->
                val consentDetail = privacyPortalArgs.dataConsent?.detailSection
                val dataConsent = privacyPortalArgs.dataConsent

                initView(consentDetail, dataConsent)

                connectedServiceAdapter.submitList(
                    privacyPortalArgs.dataConsent
                        ?.detailSection
                        ?.serviceSubscriptions
                        ?.productDetails,
                )

                consent.detailSection?.let { detailSection ->
                    otherProductAdapter.submitList(detailSection.otherProducts?.products)
                    partnerProductAdapter.submitList(detailSection.partnerProducts?.products)
                }
                showVideo(consent)
            }
        }
    }

    override fun showSharedData() {
        // not need to show the shared data here as showing the subscriptions data
    }

    override fun onProductItemClick(subscriptionProduct: SubscriptionProduct) {
        findNavController().navigate(
            MasterConsentDetailFragmentDirections.actionSubscriptionDetailFragment(
                dataConsent = privacyPortalArgs.dataConsent,
                subscriptionProduct = subscriptionProduct,
            ),
        )
    }
}
