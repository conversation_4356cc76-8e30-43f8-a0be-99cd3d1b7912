package com.toyota.oneapp.ui.privacyportal.fragment

import android.os.Bundle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentTssConsentConfirmationBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class TssConsentConfirmationFragment : BaseDataBindingFragment<FragmentTssConsentConfirmationBinding>() {
    private val consentArgs: TssConsentConfirmationFragmentArgs by navArgs()

    override fun onViewBound(
        binding: FragmentTssConsentConfirmationBinding,
        savedInstance: Bundle?,
    ) {
        if (consentArgs.isOptInConsent) {
            binding.apply {
                dataSmsTssDeclineMes.text = getString(R.string.dataconsent_tss_optin_message)
                dataSmsTssDeclineTit.text = getString(R.string.consent_accepted)
                ivDataSmsTssDecline.setImageResource(R.drawable.tss_consent_accepted)
            }
        }
        viewDataBinding.confirmDataConsent.setOnClickListener {
            findNavController().popBackStack(R.id.dataPrivacyPortalFragment, false)
        }
    }

    override fun getLayout(): Int = R.layout.fragment_tss_consent_confirmation

    companion object {
        const val A11_CONSENT_TYPE = "A11"
    }
}
