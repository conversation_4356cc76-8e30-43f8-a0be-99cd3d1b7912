package com.toyota.oneapp.ui.privacyportal.fragment

import android.net.Uri
import android.os.Bundle
import android.webkit.URLUtil
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentImageVideoViewBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ImageVideoViewFragment : BaseDataBindingFragment<FragmentImageVideoViewBinding>() {
    private var imageUrl: String? = null
    private var videoUrl: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initData()
    }

    override fun onViewBound(
        binding: FragmentImageVideoViewBinding,
        savedInstance: Bundle?,
    ) {
        binding.imageView.isVisible = videoUrl.isNullOrEmpty()
        imageUrl?.let {
            DataBindingAdapters.loadImage(binding.imageView, it)
            binding.imageView.contentDescription = it
        }

        binding.videoView.isVisible = videoUrl.isNotNullOrEmpty()
        binding.videoView.setMediaController(null)

        binding.loading.isVisible = videoUrl.isNotNullOrEmpty()
    }

    private fun initData() {
        arguments?.let {
            imageUrl = it.getString(IMAGE_URL)
            videoUrl = it.getString(VIDEO_URL)
        }
    }

    override fun getLayout(): Int = R.layout.fragment_image_video_view

    private fun initializePlayer() {
        getMedia(videoUrl)?.let { videoUri ->
            viewDataBinding.run {
                videoView.setVideoURI(videoUri)
                videoView.setOnPreparedListener {
                    it.isLooping = true
                    videoView.start()
                    lifecycleScope.launch {
                        loading.isVisible = false
                        delay(50)
                    }
                }
                videoView.setOnCompletionListener {
                    // Return the video position to the start.
                    videoView.seekTo(0)
                }
            }
        }
    }

    private fun getMedia(mediaName: String?): Uri? =
        if (URLUtil.isValidUrl(mediaName)) {
            Uri.parse(mediaName)
        } else {
            null
        }

    override fun onStart() {
        super.onStart()
        initializePlayer()
    }

    override fun onPause() {
        super.onPause()
        viewDataBinding.videoView.pause()
    }

    override fun onStop() {
        super.onStop()
        releasePlayer()
    }

    private fun releasePlayer() {
        viewDataBinding.videoView.stopPlayback()
    }

    companion object {
        const val IMAGE_URL = "image_url"
        const val VIDEO_URL = "video_url"

        fun newInstance(
            imageUrl: String?,
            videoUrl: String?,
        ): ImageVideoViewFragment =
            ImageVideoViewFragment().apply {
                arguments =
                    Bundle().apply {
                        putString(IMAGE_URL, imageUrl)
                        putString(VIDEO_URL, videoUrl)
                    }
            }
    }
}
