package com.toyota.oneapp.ui.privacyportal.viewmodel

import android.content.Context
import android.view.View
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.model.account.UpdateDataConsentRequest
import com.toyota.oneapp.model.combineddataconsent.*
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent.Companion.CONSENT_TYPE_MARKETING
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent.Companion.CONSENT_TYPE_PARTNER
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent.Companion.CONSENT_TYPE_PRIMARY
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent.Companion.CONSENT_TYPE_SMS_CONSENT
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent.Companion.CONSENT_TYPE_TERMS_OF_USE
import com.toyota.oneapp.model.lcfs.LCFSEligibilityResponse
import com.toyota.oneapp.model.subscription.CancelSubscriptionResponse
import com.toyota.oneapp.model.subscription.SubscriptionGetPayload
import com.toyota.oneapp.model.subscription.SubscriptionPreviewDetailV2
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.CombinedDataConsentRepository
import com.toyota.oneapp.network.api.repository.SubscriptionV2Repository
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.privacyportal.PrivacyPortalConsentItemClickListener
import com.toyota.oneapp.ui.privacyportal.PrivacyPortalConsentManageListener
import com.toyota.oneapp.ui.privacyportal.PrivacyPortalMasterConsentListener
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class PrivacyPortalDataConsentViewModel
    @Inject
    constructor(
        private val preferenceModel: OneAppPreferenceModel,
        private val repository: CombinedDataConsentRepository,
        private val subscriptionRepository: SubscriptionV2Repository,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel(),
        PrivacyPortalMasterConsentListener,
        PrivacyPortalConsentItemClickListener,
        PrivacyPortalConsentManageListener {
        var vehicle: VehicleInfo? = null
        var lcfsEligible = CombinedDataConsentRepository.DataConsentFlag.NONE
        private var dataConsentPayload: CombinedDataConsentPayload? = null
        private val _masterConsentLiveData = MutableLiveData<CombineDataConsent>()
        var subscriptionGetPayload: SubscriptionGetPayload? = null
        val masterConsentLiveData: LiveData<CombineDataConsent> get() = _masterConsentLiveData
        private val _privacyConsents = MutableLiveData<PrivacyConsents>()
        val privacyConsents: LiveData<PrivacyConsents> get() = _privacyConsents
        private val _allConsents = MutableLiveData<List<CombinedConsentBaseItem>>()
        val allConsentItems: LiveData<List<CombinedConsentBaseItem>> get() = _allConsents
        val successMessageData = SingleLiveEvent<DeclineSuccessData?>()
        val hideDeclineData = SingleLiveEvent<Unit>()
        val consentAcceptSuccess = SingleLiveEvent<Unit>()
        val activeWiFiExist = SingleLiveEvent<Unit>()
        var masterConsent: CombineDataConsent? = null
            set(value) {
                if (field == value) return
                field = value
            }
        private val _manageSubscriptionNavigation = SingleLiveEvent<Unit>()
        val manageSubscriptionNavigationData: LiveData<Unit> get() = _manageSubscriptionNavigation

        fun getDataConsents() {
            if (vehicle?.isFeatureEnabled(Feature.LCFS) == true) {
                getVehicleLcfsEligibility()
            } else {
                fetchCombinedDataConsents()
            }
        }

        private fun fetchCombinedDataConsents() {
            vehicle?.let {
                viewModelScope.launch {
                    showProgress()
                    val resource =
                        repository.getPrivacyPortalDataConsent(
                            vin = it.vin,
                            brand = it.brand,
                            gen = it.generation ?: "",
                            region = it.region,
                            productCodes = subscriptionGetPayload?.externalSubscriptions?.map { it.productCode },
                            eligibleConsent = lcfsEligible.value,
                        )
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            resource.data?.payload?.let { payload ->
                                dataConsentPayload = payload
                                payload.privacyConsents?.let {
                                    _privacyConsents.value = it
                                    updateDataConsents(it)
                                }
                                initMasterConsent()
                            }
                        }
                        is Resource.Failure -> {
                            showErrorMessage(resource.message)
                        }
                        else -> {}
                    }
                }
            }
        }

        private fun getVehicleLcfsEligibility() {
            vehicle?.let {
                viewModelScope.launch {
                    showProgress()
                    val resource = repository.getLCFSEligibility(vin = it.vin, brand = it.brand)
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            resource.data?.payload?.let { payload ->
                                if (payload.lcfsEligible == false &&
                                    payload.showLcfsBanner == false &&
                                    (
                                        payload.lcfsOptIn == null ||
                                            LCFSEligibilityResponse.OptInStatus.Out == payload.lcfsOptIn
                                    )
                                ) {
                                    fetchCombinedDataConsents()
                                } else {
                                    lcfsEligible = CombinedDataConsentRepository.DataConsentFlag.LCFS_ELIGIBLE_CONSENT
                                    fetchCombinedDataConsents()
                                }
                            }
                        }
                        is Resource.Failure -> {
                            fetchCombinedDataConsents()
                        }
                        else -> {}
                    }
                }
            }
        }

        private fun initMasterConsent() {
            masterConsent =
                dataConsentPayload?.acknowledgedConsents?.find { it.masterConsent }?.apply {
                    masterConsentUnEditable = vehicle?.isFeatureEnabled(Feature.EDIT_MASTER_CONSENT) == false
                    videoUrl = dataConsentPayload?.privacyConsents?.animationUrl
                }
            _masterConsentLiveData.value = masterConsent
        }

        private fun updateDataConsents(privacyConsents: PrivacyConsents) {
            val ackConsents = dataConsentPayload?.acknowledgedConsents?.groupBy { it.consentId }
            ackConsents?.let { consentMap ->
                val consentItems = mutableListOf<CombinedConsentBaseItem>()
                privacyConsents.dataConsents?.let {
                    consentItems.addAll(getConsentItems(it, consentMap, CONSENT_TYPE_PRIMARY))
                }
                privacyConsents.partnerConsents?.let {
                    consentItems.addAll(getConsentItems(it, consentMap, CONSENT_TYPE_PARTNER))
                }
                privacyConsents.marketingConsents?.let {
                    consentItems.addAll(getConsentItems(it, consentMap, CONSENT_TYPE_MARKETING))
                }
                privacyConsents.termsOfUse?.let {
                    consentItems.addAll(getConsentItems(it, consentMap, CONSENT_TYPE_TERMS_OF_USE))
                }
                privacyConsents.smsConsents?.let {
                    consentItems.addAll(getConsentItems(it, consentMap, CONSENT_TYPE_SMS_CONSENT))
                }
                _allConsents.value = consentItems
            }
        }

        private fun getConsentItems(
            privacyConsents: PrivacyDataConsents,
            ackConsents: Map<String, List<CombineDataConsent>>,
            consentType: String,
        ): List<CombinedConsentBaseItem> {
            val consentItems = mutableListOf<CombinedConsentBaseItem>()
            if (!privacyConsents.consents.isNullOrEmpty()) {
                consentItems.add(
                    CombinedConsentHeaderItem(
                        title =
                            privacyConsents.title
                                ?: "",
                        description = privacyConsents.subTitle ?: "",
                    ),
                )
                privacyConsents.consents?.forEach { consent ->
                    ackConsents[consent.consentId]?.get(0)?.let {
                        consentItems.add(
                            it.apply {
                                name = consent.name
                                descriptionTex = consent.description
                                imageUrl = consent.imageUrl
                                videoUrl = consent.animationUrl
                                this.consentType = consentType
                            },
                        )
                    }
                }
            }
            return consentItems
        }

        fun declineConsent(
            dataConsent: CombineDataConsent,
            context: Context,
        ) {
            vehicle?.let { vehicleInfo ->
                viewModelScope.launch {
                    showProgress()
                    updateConsentsStatus(dataConsent, ToyotaConstants.CONSENT_DECLINED)
                    val waive = dataConsent.masterConsent
                    val paidExist = vehicleInfo.isPaidServicesAvailable && dataConsent.masterConsent

                    // cancelling subscriptions if master consent is declined and paid service available
                    val refundPreviewResource =
                        if (paidExist) {
                            val subscriptionIds = arrayListOf<String>()
                            subscriptionIds.addAll(vehicleInfo.subscriptions.map { it.subscriptionID })
                            subscriptionRepository.cancelSubscriptions(
                                vehicleInfo = vehicleInfo,
                                subscriptionIds = subscriptionIds,
                            )
                        } else {
                            null
                        }
                    // waiving all services if master consent is declined
                    val updateResource =
                        if (waive) {
                            repository.createOrWaive17CYPlusSubscription(
                                vin = vehicleInfo.vin,
                                brand = vehicleInfo.brand,
                                region = vehicleInfo.region,
                                generation = vehicleInfo.generation,
                                asiCode = vehicleInfo.asiCode,
                                hwtType = vehicleInfo.hwType,
                                subscriptionRequest = getSubscriptionRequest(waive, filteredConsents),
                            )
                        } else {
                            repository.updateDataConsents(
                                vin = vehicleInfo.vin,
                                brand = vehicleInfo.brand,
                                region = vehicleInfo.region,
                                generation = vehicleInfo.generation,
                                dateTime = System.currentTimeMillis(),
                                body =
                                    getUpdateConsentBody(
                                        vehicleInfo,
                                        dataConsentPayload?.acknowledgedConsents?.filter { it.consentId == dataConsent.consentId },
                                    ),
                            )
                        }
                    hideProgress()
                    if (dataConsent.masterConsent) {
                        successData =
                            DeclineSuccessData.ShowToast(
                                context.getString(R.string.Cancellation_Request_In_Progress_note),
                            )
                    }
                    when {
                        paidExist -> {
                            onCancelSubscriptionsSuccess(refundPreviewResource, context)
                            onConsentsUpdated(updateResource)
                        }
                        else -> {
                            dataConsent.category?.let { dataConsentCategory ->
                                analyticsLogger.logStringEvent(
                                    AnalyticsEventParam.PRIVACY_PORTAL_CONSENT_DECLINE,
                                    AnalyticsEventParam.PRIVACY_PORTAL_CONSENT_CATEGORY to dataConsentCategory,
                                )
                            }
                            onConsentsUpdated(updateResource)
                        }
                    }
                }
            }
        }

        private val filteredConsents
            get() =
                dataConsentPayload?.acknowledgedConsents?.filter {
                    it.declineMasterConsentEligible == true
                }

        /**
         * Updating the consent's status before posting the data to server
         */
        private fun updateConsentsStatus(
            dataConsent: CombineDataConsent,
            status: String,
        ) {
            if (dataConsent.masterConsent) {
                dataConsentPayload?.acknowledgedConsents?.forEach {
                    it.consentStatus = status
                }
            } else {
                dataConsentPayload
                    ?.acknowledgedConsents
                    ?.find {
                        it.consentId == dataConsent.consentId
                    }?.consentStatus = status
            }
        }

        private var successData: DeclineSuccessData? = null

        /**
         * Calculating the refund amount if applicable
         */
        private fun onCancelSubscriptionsSuccess(
            resource: Resource<CancelSubscriptionResponse?>?,
            context: Context,
        ) {
            when (resource) {
                is Resource.Success -> {
                    val response = resource.data
                    response?.payload?.refundResponse?.amount.let { refundAmount ->
                        if (refundAmount != null && refundAmount > 0.0) {
                            successData =
                                DeclineSuccessData.ShowBottomSheet(
                                    String.format(
                                        context.getString(
                                            R.string.ManagePaidSubscription_cancellation_description,
                                        ),
                                        preferenceModel
                                            .getAccountInfoSubscriber()
                                            ?.customerEmails
                                            ?.get(0)
                                            ?.emailAddress ?: "",
                                    ),
                                )
                            return
                        }
                    }
                }
                else -> {
                    // not need to show any message
                }
            }
        }

        private fun onConsentsUpdated(resource: Resource<BaseResponse?>) {
            when (resource) {
                is Resource.Success -> {
                    successMessageData.value = successData
                    hideDeclineData.call()
                    fetchCombinedDataConsents()
                    successData = null
                }
                is Resource.Failure -> {
                    if (ToyotaConstants.WAIVE_SUBSCRIPTION_ATT_ACTIVE_WIFI_ERROR_CODE.equals(
                            resource.responseCode,
                            true,
                        )
                    ) {
                        activeWiFiExist.call()
                        fetchCombinedDataConsents()
                    } else {
                        showErrorMessage(resource.message)
                    }
                }
                else -> {}
            }
        }

        private fun getUpdateConsentBody(
            vehicle: VehicleInfo,
            consentItems: List<CombineDataConsent>?,
        ): UpdateDataConsentRequest =
            UpdateDataConsentRequest(
                vin = vehicle.vin,
                subscriberGuid = preferenceModel.getGuid(),
                dataConsent =
                    dataConsentPayload?.dataConsent ?: DataConsent(
                        ToyotaConstants.FALSE,
                        ToyotaConstants.FALSE,
                        ToyotaConstants.FALSE,
                        ToyotaConstants.FALSE,
                    ),
                consents = consentRequestItems(consentItems),
            )

        private fun consentRequestItems(consentItems: List<CombineDataConsent>?): List<ConsentRequestItem>? =
            consentItems?.map {
                ConsentRequestItem(
                    consentId = it.consentId,
                    versionId = it.version,
                    status = it.consentStatus,
                    category = it.category,
                )
            }

        /**
         * Making API call when some other consent is accepted
         */
        private fun onDataConsentAccepted(dataConsent: CombineDataConsent) {
            vehicle?.let { vehicleInfo ->
                viewModelScope.launch {
                    showProgress()
                    updateConsentsStatus(dataConsent, ToyotaConstants.CONSENT_ACCEPTED)
                    val resource =
                        repository.updateDataConsents(
                            vin = vehicleInfo.vin,
                            brand = vehicleInfo.brand,
                            region = vehicleInfo.region,
                            generation = vehicleInfo.generation,
                            dateTime = System.currentTimeMillis(),
                            body =
                                getUpdateConsentBody(
                                    vehicleInfo,
                                    dataConsentPayload?.acknowledgedConsents?.filter { it.consentId == dataConsent.consentId },
                                ),
                        )
                    hideProgress()
                    when (resource) {
                        is Resource.Success -> {
                            dataConsent.category?.let { dataConsentName ->
                                analyticsLogger.logStringEvent(
                                    AnalyticsEventParam.PRIVACY_PORTAL_CONSENT_ACCEPT,
                                    AnalyticsEventParam.PRIVACY_PORTAL_CONSENT_CATEGORY to dataConsentName,
                                )
                            }
                            consentAcceptSuccess.call()
                            fetchCombinedDataConsents()
                        }
                        is Resource.Failure -> {
                            showErrorMessage(resource.error?.message)
                        }
                        else -> {}
                    }
                }
            }
        }

        private fun getSubscriptionRequest(
            waive: Boolean,
            waivedConsentItems: List<CombineDataConsent>?,
        ): SubscriptionPreviewDetailV2 =
            SubscriptionPreviewDetailV2().apply {
                accessToken = ""
                paymentMethodId = null
                dataConsent =
                    DataConsent().apply {
                        can300 = ToyotaConstants.FALSE
                        serviceConnect = ToyotaConstants.FALSE
                        ubi = ToyotaConstants.FALSE
                        dealerContact = ToyotaConstants.FALSE
                    }
                consents = consentRequestItems(waivedConsentItems)
                waiver = waive
            }

        val masterConsentNotEditableData = SingleLiveEvent<Unit>()
        val consentNotEditableData = SingleLiveEvent<Unit>()
        val consentItemClickData = SingleLiveEvent<CombineDataConsent>()
        val declinePageNavigationData = SingleLiveEvent<CombineDataConsent>()
        val masterConsentDeclinedDialogData = SingleLiveEvent<Unit>()

        override fun onMasterConsentClick(
            view: View,
            dataConsent: CombineDataConsent,
        ) {
            // In new flow no separate view for master consent
        }

        override fun onConsentItemClick(
            view: View,
            dataConsent: CombineDataConsent,
        ) {
            masterConsent?.let { master ->
                when {
                    !dataConsent.consentEditable -> consentItemClickData.value = dataConsent
                    master.isDeclined && dataConsent.masterConsent -> masterConsentDeclinedDialogData.call()
                    master.isDeclined && dataConsent.declineMasterConsentEligible == true -> consentNotEditableData.call()
                    else -> consentItemClickData.value = dataConsent
                }
            } ?: if (dataConsent.consentEditable) {
                consentItemClickData.value = dataConsent
            } else {
            }
        }

        override fun onDeclineConsentClick(
            view: View,
            dataConsent: CombineDataConsent,
        ) {
            if (dataConsent.masterConsent && masterConsent?.masterConsentUnEditable == true) {
                masterConsentNotEditableData.call()
                return
            }
            if (dataConsent.isAccepted) declinePageNavigationData.value = dataConsent
        }

        override fun onAcceptConsentClick(
            view: View,
            dataConsent: CombineDataConsent,
        ) {
            if (dataConsent.isDeclined) onDataConsentAccepted(dataConsent)
        }

        fun onAcceptConsentClick(dataConsent: CombineDataConsent) {
            if (dataConsent.isDeclined) onDataConsentAccepted(dataConsent)
        }

        fun navigateToManageSubscription() {
            _manageSubscriptionNavigation.call()
        }
    }

sealed class DeclineSuccessData(
    val message: String,
) {
    class ShowToast(
        message: String,
    ) : DeclineSuccessData(message)

    class ShowBottomSheet(
        message: String,
    ) : DeclineSuccessData(message)
}
