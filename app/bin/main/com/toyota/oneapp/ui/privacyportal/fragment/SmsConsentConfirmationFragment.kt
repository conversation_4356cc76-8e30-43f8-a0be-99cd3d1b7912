package com.toyota.oneapp.ui.privacyportal.fragment

import android.os.Bundle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentSmsConsentConfirmationBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SmsConsentConfirmationFragment : BaseDataBindingFragment<FragmentSmsConsentConfirmationBinding>() {
    private val consentText: SmsConsentConfirmationFragmentArgs by navArgs()

    override fun onViewBound(
        binding: FragmentSmsConsentConfirmationBinding,
        savedInstance: Bundle?,
    ) {
        binding.dataSmsConsentText.text = consentText.optInOptOutConsentText
        binding.confirmDataConsent.setOnClickListener {
            findNavController().popBackStack(R.id.dataPrivacyPortalFragment, false)
        }
    }

    override fun getLayout() = R.layout.fragment_sms_consent_confirmation
}
