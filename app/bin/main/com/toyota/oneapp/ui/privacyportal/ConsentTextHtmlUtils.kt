package com.toyota.oneapp.ui.privacyportal

import android.text.Html
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.URLSpan
import android.view.View
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent

class ConsentTextHtmlUtils {
    fun setTextViewHTML(
        text: TextView,
        html: String,
        item: CombineDataConsent,
        onDialogSchemeClick: (String) -> Unit,
    ) {
        val sequence: Spanned = Html.fromHtml(html, Html.FROM_HTML_MODE_LEGACY)
        val strBuilder = SpannableStringBuilder(sequence)
        val urls =
            strBuilder.getSpans(0, sequence.length, URLSpan::class.java)

        for (span in urls) {
            val start = strBuilder.getSpanStart(span)
            val end = strBuilder.getSpanEnd(span)
            val flags = strBuilder.getSpanFlags(span)
            val clickableSpan: URLSpan =
                object : URLSpan(span.url) {
                    override fun onClick(widget: View) {
                        val message = getMessageFromScheme(span.url, item)
                        if (message != null) {
                            onDialogSchemeClick.invoke(message)
                        } else {
                            super.onClick(widget)
                        }
                    }
                }

            strBuilder.setSpan(clickableSpan, start, end, flags)
            strBuilder.removeSpan(span)
        }

        text.text = strBuilder
        text.movementMethod = LinkMovementMethod.getInstance()
    }

    @VisibleForTesting
    fun getMessageFromScheme(
        scheme: String,
        item: CombineDataConsent,
    ): String? {
        val dialogs = item.manageSection?.dialogs
        val foundDialog = dialogs?.find { it.scheme == scheme }

        foundDialog?.let {
            return it.body
        }
        return null
    }

    fun setSubscriptionDeclineText(
        text: TextView,
        html: String,
        onCustomLinkClick: (String) -> Unit,
    ) {
        val sequence: Spanned = Html.fromHtml(html, Html.FROM_HTML_MODE_LEGACY)
        val strBuilder = SpannableStringBuilder(sequence)
        val urls =
            strBuilder.getSpans(0, sequence.length, URLSpan::class.java)

        for (span in urls) {
            val start = strBuilder.getSpanStart(span)
            val end = strBuilder.getSpanEnd(span)
            val flags = strBuilder.getSpanFlags(span)
            val clickableSpan: URLSpan =
                object : URLSpan(span.url) {
                    override fun onClick(widget: View) {
                        onCustomLinkClick.invoke(span.url)
                    }
                }

            strBuilder.setSpan(clickableSpan, start, end, flags)
            strBuilder.removeSpan(span)
        }

        text.text = strBuilder
        text.movementMethod = LinkMovementMethod.getInstance()
    }
}
