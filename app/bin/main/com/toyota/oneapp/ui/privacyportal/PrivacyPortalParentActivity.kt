package com.toyota.oneapp.ui.privacyportal

import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.NavGraph
import androidx.navigation.fragment.NavHostFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityPrivacyPortalParentBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.ui.garage.wifiSubscription.ActiveWifiSubscriptionExistActivity
import com.toyota.oneapp.ui.privacyportal.viewmodel.PrivacyPortalDataConsentViewModel
import com.toyota.oneapp.util.IntentUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class PrivacyPortalParentActivity : DataBindingBaseActivity<ActivityPrivacyPortalParentBinding>() {
    private lateinit var navGraph: NavGraph
    private var vehicleInfo: VehicleInfo? = null

    @Inject
    lateinit var applicationData: ApplicationData
    private val viewModel: PrivacyPortalDataConsentViewModel by viewModels()

    override fun initViews(savedInstance: Bundle?) {
        navigateToParticularScreen()
        viewModel.apply {
            activeWiFiExist.observe(
                this@PrivacyPortalParentActivity,
                Observer {
                    startActivity(
                        ActiveWifiSubscriptionExistActivity.getIntent(
                            this@PrivacyPortalParentActivity,
                            vehicleGeneration = viewModel.vehicle?.generation.orEmpty(),
                            vehicleRegion = viewModel.vehicle?.region.orEmpty(),
                        ),
                    )
                    navigateToParticularScreen()
                },
            )
            manageSubscriptionNavigationData.observe(
                this@PrivacyPortalParentActivity,
                Observer {
                    navigateToManageSubscriptionFlow()
                },
            )
        }
    }

    private fun navigateToParticularScreen() {
        (supportFragmentManager.findFragmentById(binding.privacyPortalNavHost.id) as NavHostFragment).navController.let {
            val graphInflater = it.navInflater
            navGraph = graphInflater.inflate(R.navigation.privacy_portal_nav_graph)
            vehicleInfo = intent.getParcelableExtra<VehicleInfo>(ToyotaConstants.VEHICLE)
            val vehicleDataArgs = Bundle()
            vehicleDataArgs.putParcelable(ToyotaConstants.VEHICLE, vehicleInfo)
            it.setGraph(navGraph, vehicleDataArgs)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_privacy_portal_parent

    private fun navigateToManageSubscriptionFlow() {
        vehicleInfo?.let { vehicle ->
            applicationData.setSelectedVehicle(
                applicationData.getVehicleList()?.find { it.vin == vehicle.vin },
            )
            applicationData.getSelectedVehicle()?.let {
                val intent =
                    IntentUtil.getManageSubscriptionIntent(
                        context = this,
                        vehicle = it,
                        isAddVehicleFlow = false,
                    )
                startActivity(intent)
            }
        }
    }
}
