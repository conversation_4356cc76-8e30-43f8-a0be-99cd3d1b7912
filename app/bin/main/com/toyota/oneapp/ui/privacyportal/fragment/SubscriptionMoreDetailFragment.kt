package com.toyota.oneapp.ui.privacyportal.fragment

import com.toyota.oneapp.model.combineddataconsent.SubscriptionProduct
import com.toyota.oneapp.ui.privacyportal.adapter.ConsentMoreDetailItemAdapter
import com.toyota.oneapp.util.ToyotaConstants.Companion.PP_SUBSCRIPTION_PRODUCT
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SubscriptionMoreDetailFragment : CombinedConsentMoreDetailsFragment() {
    private val subscriptionProduct by lazy {
        arguments?.getParcelable<SubscriptionProduct>(
            PP_SUBSCRIPTION_PRODUCT,
        )
    }

    override fun showDetails() {
        super.showDetails()
        subscriptionProduct?.let {
            viewDataBinding.tvMoreDetailsTitle.text = it.moreDetails?.title

            val adapter =
                ConsentMoreDetailItemAdapter(executor).also { adapter ->
                    adapter.submitList(it.moreDetails?.pageItem)
                }
            DataBindingAdapters.setAdapter(viewDataBinding.rvMoreDetails, adapter)
        }
    }
}
