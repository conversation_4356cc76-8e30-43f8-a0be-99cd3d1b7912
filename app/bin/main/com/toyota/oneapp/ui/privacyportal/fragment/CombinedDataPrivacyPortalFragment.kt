package com.toyota.oneapp.ui.privacyportal.fragment

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.core.IoThreadExecutor
import com.toyota.oneapp.databinding.FragmentCombinedDataPrivacyPortalBinding
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.dataconsent.viewmodels.EditDataConsentsViewModel
import com.toyota.oneapp.ui.newdashboard.viewmodel.VehicleListViewModel
import com.toyota.oneapp.ui.privacyportal.adapter.PrivacyPortalConsentItemAdapter
import com.toyota.oneapp.ui.privacyportal.viewmodel.DeclineSuccessData
import com.toyota.oneapp.ui.privacyportal.viewmodel.PrivacyPortalDataConsentViewModel
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.ToyotaConstants.Companion.DECLINE_CONSENT_SUCCESS_MESSAGE
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import java.util.concurrent.Executor
import javax.inject.Inject

@AndroidEntryPoint
class CombinedDataPrivacyPortalFragment :
    BaseDataBindingFragment<FragmentCombinedDataPrivacyPortalBinding>() {
    @Inject
    lateinit var applicationData: ApplicationData

    @Inject
    @IoThreadExecutor
    lateinit var executor: Executor
    private var vehicleInfo: VehicleInfo? = null
    private val dataConsentViewModel: PrivacyPortalDataConsentViewModel by activityViewModels()
    private val editDataConsentsViewModel: EditDataConsentsViewModel by viewModels()
    private val vehicleListViewModel: VehicleListViewModel by viewModels()
    private var isViewCreated = false
    private lateinit var adapter: PrivacyPortalConsentItemAdapter

    override fun onViewBound(
        binding: FragmentCombinedDataPrivacyPortalBinding,
        savedInstance: Bundle?,
    ) {
        observeBaseEvents(dataConsentViewModel)
        initViews()
    }

    private fun initViews() {
        val bundle = requireActivity().intent.extras
        vehicleInfo = bundle?.getParcelable(ToyotaConstants.VEHICLE)
        showHeaderInfo()
        initObservers()
        /**
         * this is added to avoid making api call when popping other fragments
         */
        if (!isViewCreated) {
            dataConsentViewModel.getDataConsents()
            isViewCreated = true
        }

        adapter = PrivacyPortalConsentItemAdapter(executor, dataConsentViewModel)
        DataBindingAdapters.setAdapter(
            viewDataBinding.consentItems,
            adapter,
        )

        dataConsentViewModel.privacyConsents.observe(this) { data ->
            viewDataBinding.consentItems.isVisible = data != null

            DataBindingAdapters.setIsVisible(viewDataBinding.topContentLayout.root, data != null)
            DataBindingAdapters.loadImage(
                viewDataBinding.topContentLayout.headerImage,
                data.staticData?.imageUrl,
                resources.getDrawable(R.drawable.ic_privacy_portal),
            )
            viewDataBinding.topContentLayout.headerDetails.text = data.staticData?.title
            viewDataBinding.topContentLayout.headerDescription.text = data.staticData?.description

            viewDataBinding.consentText.isVisible = data?.staticData?.footerText != null
            viewDataBinding.consentText.text = data?.staticData?.footerText
        }

        editDataConsentsViewModel.showAppLinkText.observe(this) {
            viewDataBinding.termsConditionsLayout.appLicenseText.isVisible = it ?: false
            viewDataBinding.termsConditionsLayout.tcDivider2.isVisible = it ?: false
        }
        viewDataBinding.termsConditionsLayout.appLicenseText.setOnClickListener {
            editDataConsentsViewModel.onAppLicenseClick(it)
        }
        viewDataBinding.termsConditionsLayout.privacyTex.setOnClickListener {
            editDataConsentsViewModel.onPrivacyLinkClick(it)
        }
        viewDataBinding.termsConditionsLayout.termsText.setOnClickListener {
            editDataConsentsViewModel.onTermsClick(it)
        }
    }

    private fun showHeaderInfo() {
        viewDataBinding.let {
            it.toolbar.apply {
                setNavigationOnClickListener { requireActivity().onBackPressed() }
                title =
                    getString(
                        R.string.Common_vehicle_name_format,
                        vehicleInfo?.modelYear
                            ?: "",
                        vehicleInfo?.modelDescription ?: "",
                    )
            }
        }
    }

    private fun initObservers() {
        dataConsentViewModel.run {
            vehicle = vehicleInfo
            allConsentItems.observe(
                viewLifecycleOwner,
                Observer {
                    adapter.submitList(it)
                },
            )

            consentNotEditableData.observe(
                viewLifecycleOwner,
                Observer {
                    showConsentNotEditableAlert()
                },
            )
            consentItemClickData.observe(
                viewLifecycleOwner,
                Observer {
                    if (it.masterConsent) {
                        findNavController().navigate(
                            CombinedDataPrivacyPortalFragmentDirections
                                .actionDataPrivacyPortalFragmentToMasterConsentFragment(it),
                        )
                    } else {
                        findNavController().navigate(
                            CombinedDataPrivacyPortalFragmentDirections
                                .actionDataPrivacyPortalFragmentToConsentDetailFragment(it),
                        )
                    }
                },
            )
            masterConsentDeclinedDialogData.observe(
                viewLifecycleOwner,
                Observer {
                    showMasterConsentDeclinedDialog()
                },
            )

            successMessageData.observe(
                viewLifecycleOwner,
                Observer {
                    when (it) {
                        is DeclineSuccessData.ShowBottomSheet -> showSuccessMessage(it.message)
                        is DeclineSuccessData.ShowToast -> showSuccessToastMessage(it.message)
                        else -> {}
                    }
                },
            )
        }
        editDataConsentsViewModel.vehicleInfo = vehicleInfo
    }

    private fun showSuccessMessage(message: String) {
        val fragment =
            CombinedDeclineSuccessBottomSheetFragment().apply {
                arguments =
                    Bundle().apply {
                        putString(DECLINE_CONSENT_SUCCESS_MESSAGE, message)
                    }
            }
        fragment.show(childFragmentManager, "SUCCESS")
        vehicleListViewModel.getVehiclesList()
        findNavController().currentBackStackEntry?.savedStateHandle?.set(
            DECLINE_CONSENT_SUCCESS_MESSAGE,
            "",
        )
    }

    private fun showMasterConsentDeclinedDialog() {
        DialogUtil.showDialog(
            requireActivity(),
            null,
            getString(R.string.pp_master_consent_declined_dialog_message),
            getString(R.string.pp_add_service),
            getString(toyotaone.commonlib.R.string.cancel_label),
            object : OnCusDialogInterface {
                override fun onCancelClick() {
                    // not need to do any thing just dismiss the dialog
                }

                override fun onConfirmClick() {
                    dataConsentViewModel.navigateToManageSubscription()
                }
            },
            false,
        )
    }

    private fun showConsentNotEditableAlert() {
        DialogUtil.showDialog(
            requireActivity(),
            null,
            getString(R.string.pp_other_consent_declined_dialog_message),
            getString(R.string.pp_add_service),
            getString(toyotaone.commonlib.R.string.cancel_label),
            object : OnCusDialogInterface {
                override fun onCancelClick() {
                    // not need to do any thing just dismiss the dialog
                }

                override fun onConfirmClick() {
                    dataConsentViewModel.navigateToManageSubscription()
                }
            },
            false,
        )
    }

    override fun getLayout(): Int = R.layout.fragment_combined_data_privacy_portal
}
