package com.toyota.oneapp.ui.privacyportal.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.DataBindingAdapter
import com.toyota.oneapp.databinding.ItemPpConsentDetailProductBinding
import com.toyota.oneapp.model.combineddataconsent.ProductDetail
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import java.util.concurrent.Executor

class PrivacyPortalConsentDetailProductItemAdapter(
    executor: Executor,
) : DataBindingAdapter<ProductDetail, ItemPpConsentDetailProductBinding>(
        executor = executor,
        diffCallback = ConsentProductItemDiffUtil(),
    ) {
    override fun createBinding(
        parent: ViewGroup,
        viewType: Int,
    ): ItemPpConsentDetailProductBinding =
        DataBindingUtil.inflate(
            LayoutInflater.from(parent.context),
            R.layout.item_pp_consent_detail_product,
            parent,
            false,
        )

    override fun bind(
        binding: ItemPpConsentDetailProductBinding,
        position: Int,
    ) {
        val productDetail = getItem(position)
        DataBindingAdapters.loadImage(
            binding.consentImage,
            productDetail.imageUrl,
        )
        binding.consentNameText.text = productDetail.name
        binding.lastItemView.isInvisible = productDetail.isLastItem
    }
}

class ConsentProductItemDiffUtil : DiffUtil.ItemCallback<ProductDetail>() {
    override fun areItemsTheSame(
        oldItem: ProductDetail,
        newItem: ProductDetail,
    ): Boolean = oldItem == newItem

    override fun areContentsTheSame(
        oldItem: ProductDetail,
        newItem: ProductDetail,
    ): Boolean = oldItem == newItem
}
