package com.toyota.oneapp.ui.privacyportal.viewmodel

import android.widget.TextView
import com.toyota.oneapp.model.combineddataconsent.SubscriptionProduct
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.privacyportal.ConsentTextHtmlUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@HiltViewModel
class PPSubscriptionDetailViewModel
    @Inject
    constructor() : BaseViewModel() {
        val manageConsentData = SingleLiveEvent<Unit>()
        val manageSubscriptionData = SingleLiveEvent<Unit>()

        fun showCustomLinkText(
            subscriptionProduct: SubscriptionProduct,
            descText: TextView,
        ) {
            val cancelKey =
                subscriptionProduct.declineConsent?.cancelKey.let {
                    if (it.isNullOrEmpty()) CANCEL_MASTER_CONSENT_KEY else it
                }
            val declineKey =
                subscriptionProduct.declineConsent?.declineKey.let {
                    if (it.isNullOrEmpty()) CONNECTED_SERVICES_MASTER_CONSENT_KEY else it
                }
            val cancelText = String.format(CANCEL_MASTER_CONSENT_VALUE, cancelKey)
            val masterConsentText = String.format(CONNECTED_SERVICES_MASTER_CONSENT_VALUE, declineKey)
            subscriptionProduct.apply {
                declineConsent
                    ?.apply {
                        description =
                            description
                                ?.replace(cancelKey, cancelText, true)
                                ?.replace(declineKey, masterConsentText, true)
                                ?.replace("\n", "<br>")
                    }?.also {
                        ConsentTextHtmlUtils().setSubscriptionDeclineText(
                            descText,
                            it.description
                                ?: "",
                        ) { scheme ->
                            onCustomLinkClick(scheme)
                        }
                    }
            }
        }

        private fun onCustomLinkClick(scheme: String) {
            when (scheme) {
                MANAGE_CONSENT ->
                    manageConsentData.call()
                MANAGE_SUBSCRIPTION ->
                    manageSubscriptionData.call()
                else ->
                    LogTool.d("onCustomLinkClick", "Not a right click")
            }
        }

        companion object {
            const val MANAGE_CONSENT = "manageConsent"
            const val MANAGE_SUBSCRIPTION = "manageSubscription"
            const val CONNECTED_SERVICES_MASTER_CONSENT_KEY = "<CONNECTED_SERVICES_MASTER_CONSENT_KEY>"
            const val CONNECTED_SERVICES_MASTER_CONSENT_VALUE = "<a href=\"$MANAGE_CONSENT\"><b>%s</b></a>"
            const val CANCEL_MASTER_CONSENT_KEY = "<CANCEL_MASTER_CONSENT_KEY>"
            const val CANCEL_MASTER_CONSENT_VALUE = "<a href=\"$MANAGE_SUBSCRIPTION\"><b>%s</b></a>"
        }
    }
