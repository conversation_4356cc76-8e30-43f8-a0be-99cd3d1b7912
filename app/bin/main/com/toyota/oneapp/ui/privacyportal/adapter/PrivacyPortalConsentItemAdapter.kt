package com.toyota.oneapp.ui.privacyportal.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.DiffUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.DataBindingAdapter
import com.toyota.oneapp.databinding.ItemPpCombinedConsentBinding
import com.toyota.oneapp.databinding.ItemPpConsentHeaderBinding
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.combineddataconsent.CombinedConsentBaseItem
import com.toyota.oneapp.model.combineddataconsent.CombinedConsentHeaderItem
import com.toyota.oneapp.ui.privacyportal.PrivacyPortalConsentItemClickListener
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import java.util.concurrent.Executor

class PrivacyPortalConsentItemAdapter(
    executor: Executor,
    private val itemClickListener: PrivacyPortalConsentItemClickListener,
) : DataBindingAdapter<CombinedConsentBaseItem, ViewDataBinding>(
        executor = executor,
        diffCallback = ConsentItemDiffUtil(),
    ) {
    override fun createBinding(
        parent: ViewGroup,
        viewType: Int,
    ): ViewDataBinding =
        when (viewType) {
            0 ->
                DataBindingUtil.inflate<ItemPpConsentHeaderBinding>(
                    LayoutInflater.from(parent.context),
                    R.layout.item_pp_consent_header,
                    parent,
                    false,
                )
            1 ->
                DataBindingUtil
                    .inflate<ItemPpCombinedConsentBinding>(
                        LayoutInflater.from(parent.context),
                        R.layout.item_pp_combined_consent,
                        parent,
                        false,
                    ).apply {
                    }
            else -> throw RuntimeException("Not a valid view type")
        }

    override fun bind(
        binding: ViewDataBinding,
        position: Int,
    ) {
        when (binding) {
            is ItemPpConsentHeaderBinding -> {
                val consentHeader = getItem(position) as CombinedConsentHeaderItem
                binding.consentTypeNameText.text = consentHeader.title
                binding.consentTypeDescText.text = consentHeader.description
                DataBindingAdapters.setIsVisible(
                    binding.consentTypeDescText,
                    consentHeader.description?.isNotEmpty()
                        ?: true,
                )
            }
            is ItemPpCombinedConsentBinding -> {
                val consentItem = getItem(position) as CombineDataConsent
                val context = binding.root.context
                binding.root.setOnClickListener { itemClickListener.onConsentItemClick(binding.root, consentItem) }
                DataBindingAdapters.loadImage(binding.consentImage, consentItem.imageUrl, context.resources.getDrawable(R.drawable.logo))
                DataBindingAdapters.setIsVisible(binding.consentImage, consentItem.showImage)
                binding.consentNameText.text = consentItem.name
                DataBindingAdapters.setIsVisible(
                    binding.consentDescText,
                    consentItem.descriptionTex?.isNotEmpty()
                        ?: true,
                )
                binding.consentDescText.text = consentItem.descriptionTex
                DataBindingAdapters.setIsVisible(binding.consentStatusText, consentItem.showStatus)
                binding.consentStatusText.text = consentItem.getStatusText(context)
                binding.consentStatusText.setTextColor(context.getColor(consentItem.colorResId))
            }
        }
    }

    override fun getItemViewType(position: Int): Int =
        when (getItem(position)) {
            is CombinedConsentHeaderItem -> 0
            is CombineDataConsent -> 1
            else -> -1
        }
}

class ConsentItemDiffUtil : DiffUtil.ItemCallback<CombinedConsentBaseItem>() {
    override fun areItemsTheSame(
        oldItem: CombinedConsentBaseItem,
        newItem: CombinedConsentBaseItem,
    ): Boolean = oldItem == newItem

    override fun areContentsTheSame(
        oldItem: CombinedConsentBaseItem,
        newItem: CombinedConsentBaseItem,
    ): Boolean =
        if (oldItem is CombineDataConsent && newItem is CombineDataConsent) {
            oldItem == newItem
        } else if (oldItem is CombinedConsentHeaderItem && newItem is CombinedConsentHeaderItem) {
            oldItem == newItem
        } else {
            false
        }
}
