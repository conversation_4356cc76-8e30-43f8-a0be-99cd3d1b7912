package com.toyota.oneapp.ui.privacyportal.fragment

import android.os.Bundle
import android.text.Html
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.core.IoThreadExecutor
import com.toyota.oneapp.databinding.FragmentCombinedConsentDetailsBinding
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent
import com.toyota.oneapp.model.combineddataconsent.DetailSection
import com.toyota.oneapp.model.combineddataconsent.SubscriptionProduct
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.privacyportal.adapter.PrivacyPortalConnectedServiceItemAdapter
import com.toyota.oneapp.ui.privacyportal.adapter.PrivacyPortalConsentDetailProductItemAdapter
import com.toyota.oneapp.ui.privacyportal.viewmodel.PrivacyPortalDataConsentViewModel
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import java.util.concurrent.Executor
import javax.inject.Inject

@AndroidEntryPoint
open class CombinedConsentDetailsFragment : BaseDataBindingFragment<FragmentCombinedConsentDetailsBinding>() {
    private val args: CombinedConsentDetailsFragmentArgs by navArgs()
    private val dataConsentViewModel: PrivacyPortalDataConsentViewModel by activityViewModels()

    @IoThreadExecutor
    @Inject
    lateinit var executor: Executor

    @Inject
    lateinit var dateUtil: DateUtil

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    lateinit var connectedServiceAdapter: PrivacyPortalConnectedServiceItemAdapter
    lateinit var otherProductAdapter: PrivacyPortalConsentDetailProductItemAdapter
    lateinit var partnerProductAdapter: PrivacyPortalConsentDetailProductItemAdapter

    override fun onViewBound(
        binding: FragmentCombinedConsentDetailsBinding,
        savedInstance: Bundle?,
    ) {
        addListeners()
        initAdapters()
        showDetails()
        showSharedData()
        dataConsentViewModel.run {
            consentAcceptSuccess.observe(this@CombinedConsentDetailsFragment) {
                findNavController().navigate(
                    CombinedConsentDetailsFragmentDirections
                        .actionToSmsconsentConfirmationFragment(
                            args.dataConsent
                                ?.manageSection
                                ?.declinePayload
                                ?.optInTitle,
                        ),
                )
            }
            hideDeclineData.observe(this@CombinedConsentDetailsFragment) {
                findNavController().navigate(
                    CombinedConsentDetailsFragmentDirections
                        .actionToSmsconsentConfirmationFragment(
                            args.dataConsent
                                ?.manageSection
                                ?.declinePayload
                                ?.optOutTitle,
                        ),
                )
            }
        }
    }

    private fun addListeners() {
        viewDataBinding.masterConsentsToolBar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        viewDataBinding.include3.dataConsentPositiveButton.setOnClickListener {
            val consent = args.dataConsent
            consent?.category?.let { dataConsentCategory ->
                analyticsLogger.logStringEvent(
                    AnalyticsEventParam.PRIVACY_PORTAL_CONSENT_DETAIL,
                    AnalyticsEventParam.PRIVACY_PORTAL_CONSENT_CATEGORY to dataConsentCategory,
                )
            }
            activity?.let {
                if (consent?.category == CombineDataConsent.CONSENT_TYPE_SMS_CONSENT) {
                    if (consent.isAccepted) {
                        dataConsentViewModel.declineConsent(consent, it)
                    } else {
                        dataConsentViewModel.onAcceptConsentClick(consent)
                    }
                } else {
                    findNavController().navigate(
                        CombinedConsentDetailsFragmentDirections
                            .actionToConsentManageServiceFragment(args.dataConsent),
                    )
                }
            }
        }
    }

    protected open fun showDetails() {
        DataBindingAdapters.setIsVisible(viewDataBinding.ppDataConsentInclude.root, false)

        val consentDetail = args.dataConsent?.detailSection
        val dataConsent = args.dataConsent

        initView(consentDetail, dataConsent)

        showVideo(args.dataConsent)
    }

    protected fun initView(
        consentDetail: DetailSection?,
        dataConsent: CombineDataConsent?,
    ) {
        viewDataBinding.masterConsentsToolBar.title = consentDetail?.pageTitle
        viewDataBinding.combinedConsentInclude.consentNameText.text = consentDetail?.title
        viewDataBinding.combinedConsentInclude.consentDateText.text = dataConsent?.getNewConsentDateByStatus(requireContext(), dateUtil)
        viewDataBinding.combinedConsentInclude.consentDescText.text = Html.fromHtml(consentDetail?.getHtmlDescription())
        DataBindingAdapters.setIsVisible(
            viewDataBinding.combinedConsentInclude.moreDetailImage,
            consentDetail?.dataShared?.moreDetails != null,
        )
        DataBindingAdapters.loadImage(
            viewDataBinding.combinedConsentInclude.moreDetailImage,
            consentDetail?.dataShared?.moreDetails?.expandImage,
            resources.getDrawable(R.drawable.ic_add_payment_method),
        )
        DataBindingAdapters.setIsVisible(
            viewDataBinding.combinedConsentInclude.moreDetailText,
            consentDetail?.dataShared?.moreDetails != null,
        )
        viewDataBinding.combinedConsentInclude.moreDetailText.text = consentDetail?.dataShared?.moreDetails?.name

        viewDataBinding.ppDataConsentInclude.subscriptionsTitleText.text = consentDetail?.serviceSubscriptions?.title
        viewDataBinding.ppDataConsentInclude.otherConsentsTitleText.text = consentDetail?.otherProducts?.sectionSubTitle
        viewDataBinding.ppDataConsentInclude.partnerConsentsTitleText.text = consentDetail?.partnerProducts?.sectionSubTitle
        DataBindingAdapters.setAdapter(viewDataBinding.ppDataConsentInclude.connectedServiceList, connectedServiceAdapter)
        DataBindingAdapters.setAdapter(viewDataBinding.ppDataConsentInclude.otherRecyclerView, otherProductAdapter)
        DataBindingAdapters.setAdapter(viewDataBinding.ppDataConsentInclude.partnerRecyclerView, partnerProductAdapter)

        DataBindingAdapters.setIsVisible(viewDataBinding.include3.root, dataConsent?.showManageConsentButton ?: false)
        viewDataBinding.include3.dataConsentPositiveButton.text = dataConsent?.manageServiceText
    }

    override fun getLayout(): Int = R.layout.fragment_combined_consent_details

    protected open fun initAdapters() {
        connectedServiceAdapter =
            PrivacyPortalConnectedServiceItemAdapter(executor) {
                onProductItemClick(it)
            }
        otherProductAdapter = PrivacyPortalConsentDetailProductItemAdapter(executor)
        partnerProductAdapter = PrivacyPortalConsentDetailProductItemAdapter(executor)
    }

    protected open fun showSharedData() {
        val fragment =
            CombinedConsentMoreDetailsFragment().apply {
                arguments =
                    Bundle().apply {
                        putParcelable(ToyotaConstants.DATA_CONSENT_KEY, args.dataConsent)
                    }
            }
        childFragmentManager
            .beginTransaction()
            .replace(R.id.shared_container, fragment, "SHARED_DATA")
            .commitNow()
    }

    protected fun showVideo(dataConsent: CombineDataConsent?) {
        dataConsent?.detailSection?.let {
            if (it.imageUrl.isNullOrBlank() &&
                it.videoUrl.isNullOrBlank()
            ) {
                viewDataBinding.combinedConsentInclude.videoContainer.isVisible = false
            }
            val fragment = ImageVideoViewFragment.newInstance(it.imageUrl, it.videoUrl)
            childFragmentManager
                .beginTransaction()
                .replace(R.id.video_container, fragment, "VIDEO_CONTAINER")
                .commitNow()
        }
    }

    protected open fun onProductItemClick(subscriptionProduct: SubscriptionProduct) {}
}
