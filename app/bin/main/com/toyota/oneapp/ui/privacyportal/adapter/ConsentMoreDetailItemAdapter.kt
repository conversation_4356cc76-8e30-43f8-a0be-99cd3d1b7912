package com.toyota.oneapp.ui.privacyportal.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.DataBindingAdapter
import com.toyota.oneapp.databinding.ItemPpMoreDetailPageInfoBinding
import com.toyota.oneapp.model.combineddataconsent.ConsentMoreDetailItemUIData
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import java.util.concurrent.Executor

class ConsentMoreDetailItemAdapter(
    executor: Executor,
) : DataBindingAdapter<ConsentMoreDetailItemUIData, ItemPpMoreDetailPageInfoBinding>(
        executor = executor,
        diffCallback = MoreDetailItemDiffUtil(),
    ) {
    override fun createBinding(
        parent: ViewGroup,
        viewType: Int,
    ): ItemPpMoreDetailPageInfoBinding {
        val binding =
            DataBindingUtil.inflate<ItemPpMoreDetailPageInfoBinding>(
                LayoutInflater.from(parent.context),
                R.layout.item_pp_more_detail_page_info,
                parent,
                false,
            )
        return binding
    }

    override fun bind(
        binding: ItemPpMoreDetailPageInfoBinding,
        position: Int,
    ) {
        val item = getItem(position)
        binding.titleText.text = item.title
        binding.descriptionText.isVisible = !item.description.isNullOrEmpty()
        binding.descriptionText.text = item.description
        val adapter =
            ConsentMoreDetailSharedInfoAdapter(executor).also {
                it.submitList(item.items)
            }
        DataBindingAdapters.setAdapter(binding.recyclerView, adapter)
    }

    private class MoreDetailItemDiffUtil : DiffUtil.ItemCallback<ConsentMoreDetailItemUIData>() {
        override fun areItemsTheSame(
            oldItem: ConsentMoreDetailItemUIData,
            newItem: ConsentMoreDetailItemUIData,
        ): Boolean = oldItem == newItem

        override fun areContentsTheSame(
            oldItem: ConsentMoreDetailItemUIData,
            newItem: ConsentMoreDetailItemUIData,
        ): Boolean = oldItem == newItem
    }
}
