package com.toyota.oneapp.ui.privacyportal.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.DataBindingAdapter
import com.toyota.oneapp.databinding.ItemPpConsentMoreDetailInfoBinding
import java.util.concurrent.Executor

class ConsentMoreDetailSharedInfoAdapter(
    executor: Executor,
) : DataBindingAdapter<String, ItemPpConsentMoreDetailInfoBinding>(executor, ItemDiffUtils()) {
    class ItemDiffUtils : DiffUtil.ItemCallback<String>() {
        override fun areItemsTheSame(
            oldItem: String,
            newItem: String,
        ): Boolean = oldItem == newItem

        override fun areContentsTheSame(
            oldItem: String,
            newItem: String,
        ): Boolean = oldItem == newItem
    }

    override fun createBinding(
        parent: ViewGroup,
        viewType: Int,
    ): ItemPpConsentMoreDetailInfoBinding =
        DataBindingUtil.inflate(
            LayoutInflater.from(parent.context),
            R.layout.item_pp_consent_more_detail_info,
            parent,
            false,
        )

    override fun bind(
        binding: ItemPpConsentMoreDetailInfoBinding,
        position: Int,
    ) {
        binding.item = getItem(position)
    }
}
