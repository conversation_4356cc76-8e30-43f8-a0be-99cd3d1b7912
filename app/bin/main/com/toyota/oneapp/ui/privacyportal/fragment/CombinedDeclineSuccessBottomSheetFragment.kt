package com.toyota.oneapp.ui.privacyportal.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentPrivacyPortalDeclineSuccessBinding
import com.toyota.oneapp.ui.BaseBottomSheetDialogFragment
import com.toyota.oneapp.util.ToyotaConstants.Companion.DECLINE_CONSENT_SUCCESS_MESSAGE
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CombinedDeclineSuccessBottomSheetFragment : BaseBottomSheetDialogFragment() {
    private lateinit var binding: FragmentPrivacyPortalDeclineSuccessBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding =
            DataBindingUtil.inflate(
                inflater,
                R.layout.fragment_privacy_portal_decline_success,
                container,
                false,
            )
        binding.message = arguments?.getString(DECLINE_CONSENT_SUCCESS_MESSAGE)
        isCancelable = false
        return binding.root
    }

    override fun getTheme(): Int = R.style.BottomSheetDialog

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        binding.closeButton.setOnClickListener { dismissDialog() }
        binding.doneButton.setOnClickListener { dismissDialog() }
    }

    private fun dismissDialog() {
        dismiss()
    }
}
