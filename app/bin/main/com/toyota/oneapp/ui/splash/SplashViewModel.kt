package com.toyota.oneapp.ui.splash

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.core.BuildWrapper
import com.toyota.oneapp.fcm.ToyotaFCMService
import com.toyota.oneapp.model.AppDetailsResponse
import com.toyota.oneapp.model.AppVersionBanner
import com.toyota.oneapp.model.remote.OneAppPayload
import com.toyota.oneapp.network.api.manager.AppDetailsAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.CommonError
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class SplashViewModel
    @Inject
    constructor(
        private val appDetailsManager: AppDetailsAPIManager,
        private val applicationData: ApplicationData,
        private val buildWrapper: <PERSON>uildWrapper,
    ) : BaseViewModel() {
        val splashNavigationEvents = SingleLiveEvent<SplashNavigationEvent>()
        private var appDetailsResponse: AppDetailsResponse? = null

        private val _navigateToEmptyDashboard = MutableLiveData<Boolean>()
        val navigateToEmptyDashboard: LiveData<Boolean>
            get() = _navigateToEmptyDashboard

        private val _navigateToTMUpdateOrInstall = MutableLiveData<Boolean>()
        val navigateToTMUpdateOrInstall: LiveData<Boolean>
            get() = _navigateToTMUpdateOrInstall

        private val _navigateToOADashboardActivity = MutableLiveData<Boolean>()
        val navigateToOADashboardActivity: LiveData<Boolean>
            get() = _navigateToOADashboardActivity

        private val _navigateToSpecialOffersGetQuoteActivity = MutableLiveData<Boolean>()
        val navigateToSpecialOffersGetQuoteActivity: LiveData<Boolean>
            get() = _navigateToSpecialOffersGetQuoteActivity

        private val _navigateToMapDirections = MutableLiveData<Boolean>()
        val navigateToMapDirections: LiveData<Boolean>
            get() = _navigateToMapDirections

        private val _navigateToResetPinActitivity = MutableLiveData<Boolean>()
        val navigateToResetPinActitivity: LiveData<Boolean>
            get() = _navigateToResetPinActitivity

        fun navigateOnFCMClick(notificationCategory: String) {
            if (applicationData.appSessionActive) {
                if (applicationData.getVehicleList()?.isEmpty() == true) {
                    _navigateToEmptyDashboard.value = true
                } else {
                    notificationCategory.let {
                        if (ToyotaFCMService.UBI.equals(it, true)) {
                            _navigateToSpecialOffersGetQuoteActivity.value = true
                        } else if (ToyotaFCMService.NAVI_SEND_TO_PHONE.equals(it, true)) {
                            _navigateToMapDirections.value = true
                        } else if (ToyotaFCMService.TM_UPDATE_CATEGORY.equals(it, true) ||
                            ToyotaFCMService.TM_INSTALL_CATEGORY.equals(it, true) ||
                            ToyotaFCMService.TM_RELEASE_CATEGORY.equals(it, true)
                        ) {
                            _navigateToTMUpdateOrInstall.value = true
                        } else if (OneAppPayload.PIN_UPDATE.equals(it, true)) {
                            _navigateToResetPinActitivity.value = true
                        } else {
                            _navigateToOADashboardActivity.value = true
                        }
                    }
                }
            }
        }

        fun checkAppDetails() {
            appDetailsManager.sendFeedbackRequest(
                object : BaseCallback<AppDetailsResponse?>() {
                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        println("Test Cases $httpCode $errorMsg")
                        when {
                            httpCode == CommonError.HTTP_SSL_EXCEPTION -> showProxyDetectedAlert()
                            !buildWrapper.isProdEnv() ->
                                splashNavigationEvents.postValue(
                                    SplashNavigationEvent.ShowLoginScreen,
                                )
                            else ->
                                splashNavigationEvents.postValue(
                                    SplashNavigationEvent.ShowDowntimeAlert,
                                )
                        }
                    }

                    override fun onSuccess(response: AppDetailsResponse?) {
                        handleAppDetailsResponse(response)
                    }
                },
            )
        }

        private fun handleAppDetailsResponse(response: AppDetailsResponse?) {
            val appVersion = buildWrapper.getAppVersion()
            appDetailsResponse = response
            val mandatoryVersion = response?.payload?.mandatoryVersion ?: appVersion
            val appStoreVersion = response?.payload?.appStoreVersion ?: appVersion
            val appVersionBanner = response?.payload?.appVersionBanner
            when {
                appVersion == appStoreVersion -> upcomingDowntime()
                appVersion < mandatoryVersion ->
                    splashNavigationEvents.postValue(
                        SplashNavigationEvent.UpdateApp(appVersionBanner, true),
                    )
                appVersion < appStoreVersion ->
                    splashNavigationEvents.postValue(
                        SplashNavigationEvent.UpdateApp(appVersionBanner),
                    )
                else -> upcomingDowntime()
            }
        }

        private fun upcomingDowntime() {
            val event =
                appDetailsResponse?.payload?.upcomingDowntime?.firstOrNull()?.let {
                    SplashNavigationEvent.ShowUpcomingDowntime(it.reason, it.features?.firstOrNull())
                } ?: SplashNavigationEvent.ShowLoginScreen
            splashNavigationEvents.postValue(event)
        }
    }

sealed class SplashNavigationEvent {
    data class UpdateApp(
        val appVersionBanner: AppVersionBanner?,
        val mandatoryUpdate: Boolean = false,
    ) : SplashNavigationEvent()

    data class ShowUpcomingDowntime(
        val reason: String?,
        val message: String?,
    ) : SplashNavigationEvent()

    object ShowLoginScreen : SplashNavigationEvent()

    object ShowDowntimeAlert : SplashNavigationEvent()
}
