package com.toyota.oneapp.ui.splash

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.drawable.Icon
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.View
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import apptentive.com.android.util.isNotNullOrEmpty
import com.google.common.base.Strings
import com.google.gson.Gson
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.ActivitySplashBinding
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.fcm.ToyotaFCMService
import com.toyota.oneapp.model.AppVersionBanner
import com.toyota.oneapp.model.pref.SingletonPreferenceModel
import com.toyota.oneapp.model.remote.OneAppPayload
import com.toyota.oneapp.ui.LoginActivity
import com.toyota.oneapp.ui.accountsettings.ResetPinActivity
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.ui.ubi.SpecialOfferGetQuoteActivity
import com.toyota.oneapp.util.FeatureFlagUtil
import com.toyota.oneapp.util.IntentUtil.getOADashBoardIntent
import com.toyota.oneapp.util.RootUtil
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.ToyotaConstants.Companion.BRANCH_CLICKED_BRANCH_LINK_KEY
import com.toyota.oneapp.util.ToyotaConstants.Companion.BRANCH_DEEPLINK_PATH_KEY
import com.toyota.oneapp.util.ToyotaConstants.Companion.BRANCH_REFERRING_PARAM
import com.toyota.oneapp.util.ToyotaConstants.Companion.DASHBOARD_POI_DATA
import com.toyota.oneapp.util.ToyotaConstants.Companion.DASHBOARD_ROUTE
import com.toyota.oneapp.util.ToyotaConstants.Companion.DEEP_LINK_ROUTING
import com.toyota.oneapp.util.ToyotaConstants.Companion.IS_DEEP_LINK_ROUTING
import com.toyota.oneapp.util.ToyotaConstants.Companion.SHORTCUT_DOOR_LOCK_ACTION
import com.toyota.oneapp.util.ToyotaConstants.Companion.SHORTCUT_DOOR_UNLOCK_ACTION
import com.toyota.oneapp.util.ToyotaConstants.Companion.SHORTCUT_START_ENGINE_ACTION
import dagger.hilt.android.AndroidEntryPoint
import io.branch.referral.Branch
import io.branch.referral.BranchError
import org.json.JSONObject
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject
import kotlin.system.exitProcess

@AndroidEntryPoint
@SuppressLint("CustomSplashScreen")
class SplashActivity : UiBaseActivity() {
    private lateinit var binding: ActivitySplashBinding
    private val viewModel: SplashViewModel by viewModels()

    private val branchListener = BranchListener()
    private var isBranchInitialized: Boolean = false
    private var isDeepLinkPending: Boolean = false
    private var branchReferringParam: JSONObject? = null
    private var isNavigateToLoginScreen: Boolean = false
    private var isOfflineChecking = false
    private var isMopInitialized = false

    @Inject
    lateinit var singletonPreferenceModel: SingletonPreferenceModel

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger
    private val mDkMopReceiver: DkMopReceiver by lazy { DkMopReceiver() }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding =
            DataBindingUtil
                .setContentView<ActivitySplashBinding>(
                    this,
                    R.layout.activity_splash,
                ).apply {
                    lifecycleOwner = this@SplashActivity
                    executePendingBindings()
                }

        intent.getStringExtra(ToyotaFCMService.CATEGORY)?.let {
            viewModel.navigateOnFCMClick(it)
        }

        observeFCMNavigationEvents()
        observeBaseEvents(viewModel)
        enableShortcuts()
        if (!isTaskRoot) {
            val intent = intent
            if (intent != null) {
                val action = intent.action
                if (intent.hasCategory(Intent.CATEGORY_LAUNCHER) && Intent.ACTION_MAIN == action) {
                    finish()
                }
            }
        }

        viewModel.splashNavigationEvents.observe(this) {
            when (it) {
                is SplashNavigationEvent.UpdateApp ->
                    updateApp(
                        it.appVersionBanner,
                        it.mandatoryUpdate,
                    )
                is SplashNavigationEvent.ShowUpcomingDowntime ->
                    showUpcomingDowntime(
                        it.reason,
                        it.message,
                    )
                is SplashNavigationEvent.ShowLoginScreen -> showLoginScreen()
                is SplashNavigationEvent.ShowDowntimeAlert -> showDowntimeAlert()
            }
        }

        if (RootUtil.isDeviceRooted(this)) {
            analyticsLogger.logEvent(AnalyticsEvent.ROOTED_DEVICE)
        }
    }

    override fun onStart() {
        super.onStart()
        Branch
            .sessionBuilder(this)
            .withCallback(branchListener)
            .withData(this.intent?.data)
            .init()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        this.intent = intent
        Branch.sessionBuilder(this).withCallback(branchListener).reInit()
    }

    override fun onResume() {
        super.onResume()
        activityContext.registerReceiver(mDkMopReceiver, DigitalMopKeyUtils.DK_FILTER, Context.RECEIVER_EXPORTED)
        when {
            !ToyUtil.hasNetwork(this) -> {
                if (!isOfflineChecking || digitalMopKeyUtils.getCurrentProcess().success) {
                    showNoNetworkDialogue()
                }
            }
            else -> {
                isOfflineChecking = false
                when {
                    !BuildConfig.DEBUG && RootUtil.isDeviceRooted(this) ->
                        DialogUtil
                            .showDialog(
                                this,
                                String.format(
                                    applicationContext.getString(R.string.application_rooted_title),
                                    getString(R.string.app_name),
                                ),
                                applicationContext.getString(R.string.application_rooted_description),
                                applicationContext.getString(R.string.Common_ok),
                                null,
                                object : OnCusDialogInterface {
                                    override fun onConfirmClick() {
                                        finish()
                                        android.os.Process.killProcess(android.os.Process.myPid())
                                        exitProcess(1)
                                    }

                                    override fun onCancelClick() {}
                                },
                                false,
                            ).setCanceledOnTouchOutside(false)
                    else -> viewModel.checkAppDetails()
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        activityContext.unregisterReceiver(mDkMopReceiver)
    }

    private fun observeFCMNavigationEvents() {
        viewModel.navigateToEmptyDashboard.observe(this) {
            val emptyDashboardIntent = getOADashBoardIntent(this, false, false)
            emptyDashboardIntent.putExtra(
                ToyotaFCMService.NOTIFICATION_ID,
                intent.getStringExtra(ToyotaFCMService.NOTIFICATION_ID),
            )
            startActivity(emptyDashboardIntent)
            finish()
        }

        viewModel.navigateToSpecialOffersGetQuoteActivity.observe(this) {
            val specialOfferIntent = Intent(this, SpecialOfferGetQuoteActivity::class.java)
            specialOfferIntent.putExtra(
                ToyotaFCMService.CATEGORY,
                intent.getStringExtra(ToyotaFCMService.CATEGORY),
            )
            specialOfferIntent.putExtra(
                ToyotaFCMService.VIN,
                intent.getStringExtra(ToyotaFCMService.VIN),
            )
            specialOfferIntent.putExtra(
                ToyotaFCMService.STATUS,
                intent.getStringExtra(ToyotaFCMService.STATUS),
            )
            val payload = intent.getStringExtra(ToyotaFCMService.PAYLOAD).orEmpty()
            var imageURL: String =
                payload
                    .split(",".toRegex())
                    .dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                    .get(0)
                    .split(":".toRegex(), limit = 2)
                    .toTypedArray()
                    .get(1)
            imageURL = imageURL.substring(1, imageURL.length - 1).replace("\\", "")
            specialOfferIntent.putExtra("img", imageURL)
            var quoteURL: String =
                payload
                    .split(",".toRegex())
                    .dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                    .get(3)
                    .split(":".toRegex(), limit = 2)
                    .toTypedArray()
                    .get(1)
            quoteURL = quoteURL.substring(1, quoteURL.length - 1).replace("\\", "")
            specialOfferIntent.putExtra(ToyotaFCMService.QUOTE_URL, quoteURL)
            specialOfferIntent.putExtra(
                ToyotaFCMService.NOTIFICATION_ID,
                intent.getStringExtra(ToyotaFCMService.NOTIFICATION_ID),
            )
            startActivity(specialOfferIntent)
            finish()
        }

        viewModel.navigateToMapDirections.observe(this) {
            val oneAppPayload =
                Gson().fromJson<OneAppPayload>(
                    intent.getStringExtra(ToyotaFCMService.MAP),
                    OneAppPayload::class.java,
                )
            oneAppPayload.destinationInfo?.coordinate?.let {
                val latitude = it.lat
                val longitude = it.lon

                if (latitude.isNotNullOrEmpty() && longitude.isNotNullOrEmpty()) {
                    val navString =
                        String.format(
                            getString(R.string.maps_directions_url),
                            Strings.nullToEmpty(latitude),
                            Strings.nullToEmpty(longitude),
                        )
                    val navIntent =
                        Intent(Intent.ACTION_VIEW, Uri.parse(navString)).setPackage(
                            getString(R.string.google_map_package),
                        )
                    startActivity(navIntent)
                    finish()
                }
            }
        }

        viewModel.navigateToTMUpdateOrInstall.observe(this) {
            val tmUpdateInstallIntent = getOADashBoardIntent(this, false, false)
            tmUpdateInstallIntent.putExtra(
                ToyotaFCMService.CATEGORY,
                intent.getStringExtra(ToyotaFCMService.CATEGORY),
            )
            tmUpdateInstallIntent.putExtra(
                ToyotaFCMService.VIN,
                intent.getStringExtra(ToyotaFCMService.VIN),
            )
            tmUpdateInstallIntent.putExtra(
                ToyotaFCMService.STATUS,
                intent.getStringExtra(ToyotaFCMService.STATUS),
            )
            tmUpdateInstallIntent.putExtra(
                ToyotaFCMService.NOTIFICATION_ID,
                intent.getStringExtra(ToyotaFCMService.NOTIFICATION_ID),
            )
            startActivity(tmUpdateInstallIntent)
            finish()
        }

        viewModel.navigateToResetPinActitivity.observe(this) {
            val resetPinIntent = Intent(this, ResetPinActivity::class.java)
            startActivity(resetPinIntent)
            finish()
        }

        viewModel.navigateToOADashboardActivity.observe(this) {
            val oaDashboardIntent = getOADashBoardIntent(this, false, false)
            oaDashboardIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            oaDashboardIntent.putExtra(
                ToyotaFCMService.CATEGORY,
                intent.getStringExtra(ToyotaFCMService.CATEGORY),
            )
            oaDashboardIntent.putExtra(
                ToyotaFCMService.VIN,
                intent.getStringExtra(ToyotaFCMService.VIN),
            )
            oaDashboardIntent.putExtra(
                ToyotaFCMService.STATUS,
                intent.getStringExtra(ToyotaFCMService.STATUS),
            )
            oaDashboardIntent.putExtra(
                ToyotaFCMService.PAYLOAD,
                intent.getStringExtra(ToyotaFCMService.PAYLOAD),
            )
            oaDashboardIntent.putExtra(
                ToyotaFCMService.SUBCATEGORY,
                intent.getStringExtra(ToyotaFCMService.SUBCATEGORY),
            )
            oaDashboardIntent.putExtra(
                ToyotaFCMService.MAP,
                intent.getStringExtra(ToyotaFCMService.MAP),
            )
            oaDashboardIntent.putExtra(
                ToyotaFCMService.NOTIFICATION_ID,
                intent.getStringExtra(ToyotaFCMService.NOTIFICATION_ID),
            )
            startActivity(oaDashboardIntent)
            finish()
        }
    }

    private fun showNoNetworkDialogue() {
        isOfflineChecking = false
        DialogUtil
            .showDialog(
                this,
                "",
                applicationContext.getString(R.string.Common_connection_error),
                applicationContext.getString(R.string.Splash_network_settings),
                applicationContext.getString(R.string.Common_close),
                object :
                    OnCusDialogInterface {
                    override fun onConfirmClick() {
                        val intent = Intent(Settings.ACTION_WIRELESS_SETTINGS)
                        startActivity(intent)
                    }

                    override fun onCancelClick() {
                        finish()
                        android.os.Process.killProcess(android.os.Process.myPid())
                        exitProcess(1)
                    }
                },
                false,
            ).setCanceledOnTouchOutside(false)
    }

    fun showLoginScreen() {
        isNavigateToLoginScreen = true
        navigateToLoginScreenAfterConditionsMeet()
    }

    private fun navigateToLoginScreenAfterConditionsMeet() {
        if (isNavigateToLoginScreen) {
            if (isBranchInitialized) {
                startWithDeepLink()
            } else {
                isDeepLinkPending = true
            }
        } else {
            val loginIntent = Intent(this@SplashActivity, LoginActivity::class.java)
            startActivityWithoutAnim(loginIntent)
            finish()
        }
    }

    private fun startWithDeepLink() {
        val isDeepLinkRouting =
            branchReferringParam?.optBoolean(
                BRANCH_CLICKED_BRANCH_LINK_KEY,
            )
                ?: false

        val loginIntent =
            Intent(this@SplashActivity, FeatureFlagUtil.loginActivity).putExtras(
                intent,
            )
        if (isDeepLinkRouting) {
            loginIntent.putExtra(IS_DEEP_LINK_ROUTING, isDeepLinkRouting)
            loginIntent.putExtra(
                BRANCH_REFERRING_PARAM,
                branchReferringParam?.toString().orEmpty(),
            )
        }
        loginIntent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT or
            Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK

        if (intent.action == Intent.ACTION_SEND && intent.type == "text/plain") {
            loginIntent.putExtra(
                DEEP_LINK_ROUTING,
                isDeepLinkRouting,
            )
            loginIntent.putExtra(
                DASHBOARD_ROUTE,
                getString(R.string.deep_link_share_poi),
            )
            loginIntent.putExtra(
                DASHBOARD_POI_DATA,
                intent.getStringExtra(Intent.EXTRA_TEXT),
            )
        }

        startActivityWithoutAnim(loginIntent)
        finish()
    }

    private fun updateApp(
        appVersionBanner: AppVersionBanner?,
        mandatoryUpdate: Boolean,
    ) {
        binding.appUpdateScreen.visibility = View.VISIBLE
        loadAppUpdateUi(appVersionBanner, mandatoryUpdate)
    }

    private fun showUpcomingDowntime(
        reason: String?,
        message: String?,
    ) {
        if (reason.equals("appOutage", true)) {
            DialogUtil
                .showDialog(
                    this,
                    getString(R.string.Splash_down_time),
                    message,
                    getString(
                        R.string.Common_okay,
                    ),
                    null,
                    object :
                        OnCusDialogInterface {
                        override fun onConfirmClick() {
                            finish()
                            android.os.Process.killProcess(android.os.Process.myPid())
                            exitProcess(1)
                        }

                        override fun onCancelClick() {}
                    },
                    false,
                ).setCanceledOnTouchOutside(false)
        } else {
            DialogUtil
                .showDialog(
                    this,
                    null,
                    message,
                    applicationContext.getString(
                        R.string.Common_okay,
                    ),
                    null,
                    object :
                        OnCusDialogInterface {
                        override fun onConfirmClick() {
                            showLoginScreen()
                        }

                        override fun onCancelClick() {
                        }
                    },
                    false,
                ).setCanceledOnTouchOutside(false)
        }
    }

    private fun showDowntimeAlert() {
        DialogUtil
            .showDialog(
                this,
                applicationContext.getString(R.string.Splash_down_time),
                applicationContext.getString(
                    R.string.Splash_outage,
                ),
                applicationContext.getString(R.string.Common_okay),
                null,
                object :
                    OnCusDialogInterface {
                    override fun onConfirmClick() {
                        finish()
                        android.os.Process.killProcess(android.os.Process.myPid())
                        exitProcess(1)
                    }

                    override fun onCancelClick() {}
                },
                false,
            ).setCanceledOnTouchOutside(false)
    }

    private fun enableShortcuts() {
        val shortcutManager = getSystemService(ShortcutManager::class.java)
        val shortcutUnlock =
            ShortcutInfo
                .Builder(this, "doorUnlock")
                .setShortLabel(getString(R.string.shortcut_door_unlock))
                .setLongLabel(getString(R.string.shortcut_door_unlock))
                .setIcon(Icon.createWithResource(this, R.drawable.unlock_carousel))
                .setIntent(
                    Intent(this, SplashActivity::class.java).setAction(SHORTCUT_DOOR_UNLOCK_ACTION),
                ).setRank(3)
                .build()
        val shortcutLock =
            ShortcutInfo
                .Builder(this, "doorLock")
                .setShortLabel(getString(R.string.shortcut_door_lock))
                .setLongLabel(getString(R.string.shortcut_door_lock))
                .setIcon(Icon.createWithResource(this, R.drawable.lock_carousel))
                .setIntent(
                    Intent(this, SplashActivity::class.java).setAction(SHORTCUT_DOOR_LOCK_ACTION),
                ).setRank(2)
                .build()
        val shortcutEngineStart =
            ShortcutInfo
                .Builder(this, "startEngine")
                .setShortLabel(getString(R.string.shortcut_engine))
                .setLongLabel(getString(R.string.shortcut_engine))
                .setIcon(Icon.createWithResource(this, R.drawable.power_carousel))
                .setIntent(
                    Intent(this, SplashActivity::class.java).setAction(
                        SHORTCUT_START_ENGINE_ACTION,
                    ),
                ).setRank(1)
                .build()
        try {
            shortcutManager.addDynamicShortcuts(
                listOf(shortcutEngineStart, shortcutLock, shortcutUnlock),
            )
        } catch (ignored: Throwable) {
            LogTool.d("Handle Dynamic Short CUT Crash", ignored.localizedMessage)
        }
    }

    private fun loadAppUpdateUi(
        appVersionBanner: AppVersionBanner?,
        mandatoryUpdate: Boolean,
    ) {
        binding.headerText.text = appVersionBanner?.header ?: getString(
            R.string.Splash_update_now_title,
        )
        binding.bodyText.text = appVersionBanner?.banner ?: getString(
            R.string.Splash_update_now_message,
        )
        if (mandatoryUpdate) {
            binding.btnLater.visibility = View.GONE
        } else {
            binding.btnLater.visibility = View.VISIBLE
            binding.btnLater.text =
                if (appVersionBanner?.later.isNullOrBlank()) {
                    getString(
                        R.string.Common_later,
                    )
                } else {
                    appVersionBanner?.later
                }
        }

        binding.btnLater.setOnClickListener {
            showLoginScreen()
        }

        binding.btnUpdateNow.text =
            if (appVersionBanner?.updateNow.isNullOrBlank()) {
                getString(
                    R.string.Common_update_now,
                )
            } else {
                appVersionBanner?.updateNow
            }
        binding.btnUpdateNow.setOnClickListener {
            startActivity(
                Intent(Intent.ACTION_VIEW, Uri.parse(ToyotaConstants.APP_GOOGLE_PLAY_URL)),
            )
        }
    }

    private inner class BranchListener : Branch.BranchReferralInitListener {
        override fun onInitFinished(
            referringParams: JSONObject?,
            error: BranchError?,
        ) {
            isBranchInitialized = true
            branchReferringParam = referringParams

            if (branchReferringParam?.optString(BRANCH_DEEPLINK_PATH_KEY).isNotNullOrEmpty()) {
                isDeepLinkPending = true
            }

            if (isDeepLinkPending) {
                isDeepLinkPending = false
                startWithDeepLink()
            }
        }
    }

    // DigitalKey
    inner class DkMopReceiver : BroadcastReceiver() {
        override fun onReceive(
            context: Context,
            intent: Intent,
        ) {
            DigitalMopKeyUtils.appendLog("DigitalMop Received Operation  ${intent.action}")
            when (intent.action) {
                DigitalMopKeyUtils.ACTION_INITIALIZE, DigitalMopKeyUtils.ACTION_KEYINFO_CHANGE -> {
                    if (!isMopInitialized && !ToyUtil.hasNetwork(context)) {
                        isMopInitialized = true
                        showNoNetworkDialogue()
                    }
                }
            }
        }
    }
}
