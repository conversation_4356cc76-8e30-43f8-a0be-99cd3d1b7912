package com.toyota.oneapp.ui.vehiclehealth.viewmodel

import android.content.Context
import android.graphics.Typeface
import android.text.SpannableString
import android.text.Spanned
import android.text.style.StyleSpan
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.model.HealthHistoryItem
import com.toyota.oneapp.model.HealthHistoryResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.vehiclehealth.HealthReportAdapter
import com.toyota.oneapp.ui.vehiclehealth.repository.HealthReportRepository
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.HealthReportNavigationEvent.EmailSent
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.HealthReportNavigationEvent.ReportRequestError
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.HealthReportNavigationEvent.SendEmailPrompt
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.HealthReportNavigationEvent.SendEmailRequestError
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.ReportType.DIAGNOSTIC_REPORT
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.ReportType.HEALTH_REPORT
import com.toyota.oneapp.util.ToyUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class HealthReportViewModel
    @Inject
    constructor(
        private val healthReportRepository: HealthReportRepository,
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : BaseViewModel(),
        HealthReportClickListener {
        val healthListAdapter = HealthReportAdapter(this)

        private val _healthList = MutableLiveData<List<HealthHistoryItem?>?>()
        val healthList: LiveData<List<HealthHistoryItem?>?> = _healthList

        /** Reactive field used to indicate navigation events linked to Health Report Nav Components */
        private val _healthReportNavigationEvents = SingleLiveEvent<HealthReportNavigationEvent>()
        val healthReportNavigationEvents: LiveData<HealthReportNavigationEvent> = _healthReportNavigationEvents

        /** Reactive field used to indicate when to show the reports are empty layout.
         *  We are using this instead of only checking if the result list is empty to create a better
         *  user experience*/
        private val _reportsAreEmpty = SingleLiveEvent<Boolean>()
        val reportsAreEmpty: LiveData<Boolean> = _reportsAreEmpty

        val vin: String get() = applicationData.getSelectedVehicle()?.vin.orEmpty()

        /** This variable comes from intent data that allows us to know if the user is
         * asking to access Health Reports or Diagnostic Reports. This flag alters the viewmodel's
         * behavior accordingly. It must always be instantiated, but as a fallback, it defaults to
         * HEALTH_REPORT, in order to do damage control for the user experience*/
        private var _reportType: ReportType = HEALTH_REPORT

        /** Field that holds the item selected by the user */
        private lateinit var _selectedHealthHistoryItem: HealthHistoryItem

        override fun onHealthHistoryItemClicked(item: HealthHistoryItem) {
            _selectedHealthHistoryItem = item
            _healthReportNavigationEvents.postValue(SendEmailPrompt)
        }

        /** Method used to properly instantiate the ViewModel, defining _reportType and the ViewModel's
         * behavior. */
        fun initialize(reportType: ReportType) {
            _reportType = reportType

            when (_reportType) {
                HEALTH_REPORT -> sendHealthReportRequest()

                DIAGNOSTIC_REPORT -> sendDiagnosticReportRequest()
            }.exhaustive
        }

        fun onSendEmailClicked() {
            sendEmailClickedAnalyticsEvent()
            sendEmailRequest()
        }

        fun getBottomSheetTitleText(context: Context): String {
            if (!::_selectedHealthHistoryItem.isInitialized) {
                return ""
            }

            val format = context.getString(R.string.VehicleHealth_email_prompt_title_format)
            val monthString = ToyUtil.monthNumToText(context, _selectedHealthHistoryItem.month)
            val yearString = _selectedHealthHistoryItem.year
            val dateString = "$monthString $yearString"

            return String.format(format, dateString)
        }

        fun getBottomSheetInfoText(context: Context): SpannableString {
            if (!::_selectedHealthHistoryItem.isInitialized) {
                return SpannableString("")
            }

            val format = context.getString(R.string.VehicleHealth_email_details_format)
            val vehicleModel = applicationData.getSelectedVehicle()?.modelName
            val userEmail =
                oneAppPreferenceModel
                    .getAccountInfoSubscriber()
                    ?.customerEmails
                    ?.get(0)
                    ?.emailAddress ?: ""
            val monthString = ToyUtil.monthNumToText(context, _selectedHealthHistoryItem.month)
            val yearString = _selectedHealthHistoryItem.year
            val dateString = "$monthString $yearString"
            val infoText = String.format(format, vehicleModel, dateString, userEmail)
            val spannableString = SpannableString(infoText)
            val start = infoText.indexOf(userEmail)
            val end = start + userEmail.length
            val styleSpan = StyleSpan(Typeface.BOLD)

            spannableString.setSpan(styleSpan, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)

            return spannableString
        }

        private fun sendDiagnosticReportRequest() =
            sendReportRequest { vin: String ->
                healthReportRepository.getDiagnosticHistory(vin)
            }

        private fun sendHealthReportRequest() =
            sendReportRequest { vin: String ->
                healthReportRepository.getHealthHistory(vin)
            }

        private fun sendEmailRequest() {
            viewModelScope.launch {
                showProgress()
                val resource =
                    healthReportRepository
                        .requestEmailReport(
                            vin,
                            _reportType.requestField,
                            _selectedHealthHistoryItem,
                        )
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        val userEmail =
                            oneAppPreferenceModel
                                .getAccountInfoSubscriber()
                                ?.customerEmails
                                ?.get(
                                    0,
                                )?.emailAddress ?: ""
                        val emailSentNavEvent = EmailSent(_selectedHealthHistoryItem, userEmail)
                        _healthReportNavigationEvents.postValue(emailSentNavEvent)
                        analyticsLogger.logEvent(AnalyticsEvent.VHR_HISTORY_EMAIL_REPORT_SUCCESSFUL)
                    }

                    is Resource.Failure -> {
                        _healthReportNavigationEvents.postValue(SendEmailRequestError(resource.message))
                        analyticsLogger.logEvent(AnalyticsEvent.VHR_HISTORY_EMAIL_REPORT_UNSUCCESSFUL)
                    }

                    else -> {
                        // do nothing
                    }
                }.exhaustive
            }
        }

        /**
         * Generalized version of sending Vehicle Health or Diagnostic Report request.
         * Given both async methods require the same parameter, we have generalized the method to
         * accommodate for both suspend functions.
         */
        private fun sendReportRequest(requestMethod: suspend (String) -> Resource<HealthHistoryResponse?>) {
            viewModelScope.launch {
                showProgress()
                val resource = requestMethod.invoke(vin)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        resource.data?.payload.let {
                            _healthList.postValue(it ?: emptyList())
                            _reportsAreEmpty.postValue(it?.isEmpty() ?: true)
                        }
                        sendAnalyticsEventForSuccess()
                    }

                    is Resource.Failure -> {
                        _healthReportNavigationEvents.postValue(ReportRequestError(resource.message))
                        _reportsAreEmpty.postValue(true)
                        sendAnalyticsEventForFailure()
                    }

                    else -> {
                        // do nothing
                    }
                }.exhaustive
            }
        }

        private fun sendAnalyticsEventForSuccess() {
            when (_reportType) {
                HEALTH_REPORT -> {
                    analyticsLogger.logEvent(AnalyticsEvent.VHR_HISTORY_GET_HEALTH_REPORTS_SUCCESSFUL)
                }

                DIAGNOSTIC_REPORT -> {
                    analyticsLogger.logEvent(
                        AnalyticsEvent.VHR_HISTORY_GET_DIAGNOSTIC_REPORTS_SUCCESSFUL,
                    )
                }
            }.exhaustive
        }

        private fun sendAnalyticsEventForFailure() {
            when (_reportType) {
                HEALTH_REPORT -> {
                    analyticsLogger.logEvent(AnalyticsEvent.VHR_HISTORY_GET_HEALTH_REPORTS_UNSUCCESSFUL)
                }

                DIAGNOSTIC_REPORT -> {
                    analyticsLogger.logEvent(
                        AnalyticsEvent.VHR_HISTORY_GET_DIAGNOSTIC_REPORTS_UNSUCCESSFUL,
                    )
                }
            }.exhaustive
        }

        private fun sendEmailClickedAnalyticsEvent() {
            when (_reportType) {
                HEALTH_REPORT ->
                    analyticsLogger.logEvent(AnalyticsEvent.EMAIL_HEALTH_REPORT_CLICK)
                DIAGNOSTIC_REPORT ->
                    analyticsLogger.logEvent(AnalyticsEvent.EMAIL_DIAGNOSTIC_REPORT_CLICK)
            }.exhaustive
        }

        sealed class HealthReportNavigationEvent {
            object SendEmailPrompt : HealthReportNavigationEvent()

            data class EmailSent(
                val selectedItem: HealthHistoryItem,
                val userEmail: String,
            ) : HealthReportNavigationEvent()

            data class ReportRequestError(
                val errorMessage: String,
            ) : HealthReportNavigationEvent()

            data class SendEmailRequestError(
                val errorMessage: String,
            ) : HealthReportNavigationEvent()
        }

        enum class ReportType(
            val requestField: String,
        ) {
            HEALTH_REPORT("VHR"),
            DIAGNOSTIC_REPORT("VDR"),
        }
    }

interface HealthReportClickListener {
    fun onHealthHistoryItemClicked(item: HealthHistoryItem)
}
