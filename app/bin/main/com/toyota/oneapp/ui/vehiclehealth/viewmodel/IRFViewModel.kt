package com.toyota.oneapp.ui.vehiclehealth.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.extensions.clearAndAdd
import com.toyota.oneapp.extensions.notifyObservers
import com.toyota.oneapp.model.vehiclehealth.IRFInfoItem
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.vehiclehealth.IRFClickListener
import com.toyota.oneapp.ui.vehiclehealth.IRFListAdapter
import com.toyota.oneapp.ui.vehiclehealth.IRFSearchResultAdapter
import com.toyota.oneapp.ui.vehiclehealth.SearchResultClickListener
import com.toyota.oneapp.ui.vehiclehealth.repository.IRFRepository
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationEvent.IRFAccessGranted
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationEvent.IRFAccessRevoked
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationEvent.IRFListError
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationEvent.IRFLocationGrant
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationEvent.IRFLocationRevoke
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationEvent.IRFSearchError
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationEvent.SearchForServiceLocations
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationProgressEvent.HideProgress
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationProgressEvent.ShowProgress
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class IRFViewModel
    @Inject
    constructor(
        private val irfRepository: IRFRepository,
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
    ) : BaseViewModel(),
        IRFClickListener,
        SearchResultClickListener {
        companion object {
            private const val ACCESS_GRANT = "GRANT"
            private const val ACCESS_REVOKE = "REVOKE"
        }

        val diagnosticAccessAdapter = IRFListAdapter(this)
        val searchResultAdapter = IRFSearchResultAdapter(this)

        /**
         * Reactive field used to indicate any navigation events linked to IRF Navigation Components.
         * */
        private val _irfNavigationEvents = SingleLiveEvent<IRFNavigationEvent>()
        val irfNavigationEvents: LiveData<IRFNavigationEvent> = _irfNavigationEvents

        /**
         * Event related to progress events specific to this viewModel.
         * Used to show and hide a progressbar inside the view rather than the progressbar
         * which disallows the user from interacting with the entire screen.
         * **/
        private val _irfNavigationProgressEvents = SingleLiveEvent<IRFNavigationProgressEvent>()
        val irfNavigationProgressEvents: LiveData<IRFNavigationProgressEvent> =
            _irfNavigationProgressEvents

        /** Reactive object holding an IRF facility object when it is selected **/
        private val _selectedIRFInfoItem = MutableLiveData<IRFInfoItem>()
        val selectedIRFInfoItem: LiveData<IRFInfoItem> = _selectedIRFInfoItem

        /**
         * List of IRFs who have access granted. As of now, we are only consuming a single IRF item.
         * The infrastructure was initially set up to handle a list of IRFs, so they are being held in
         * a list and passed to a RecyclerView accordingly.
         *
         * @date: 04/15/2021
         * **/
        private val _accessGrantedIrfList = MutableLiveData<List<IRFInfoItem?>?>()
        val accessGrantedIrfList: LiveData<List<IRFInfoItem?>?> = _accessGrantedIrfList

        /**
         * Boolean value determining if the user's query yields no results.
         * This extra boolean is being held rather than simply
         * checking if accessGrantedIrfList.isEmpty(), because we use this field to determine if the
         * layout for no results found will be displayed or not, which has similar but not identical
         * logic
         * **/
        private val _noResultsFound = MutableLiveData<Boolean>()
        val noResultsFound: LiveData<Boolean> = _noResultsFound

        /**
         * MutableLiveData holding user input for search.
         * This field is public and has no LiveData counterpart because it is being used in
         * two-way binding.
         * **/
        val searchQuery = MutableLiveData<String>()

        /** Transformed address of IRF to be displayed in separate lines **/
        val formattedAddress = selectedIRFInfoItem.map { it.getMultiLineAddress() }

        /**
         * A field holding all search results filtered by the user input
         * Whenever the searchQuery is updated, this field, acting as an
         * observer gets notified of the query change and filters the searchResults
         * we already have from the API based on the new query.
         * **/
        val filteredResults =
            searchQuery.map { query ->
                _searchResults
                    .filter { item ->
                        item?.name?.contains(query, ignoreCase = true) == true
                    }.also {
                        if (_irfNavigationProgressEvents.value != ShowProgress && query.length >= 3) {
                            _noResultsFound.value = it.isEmpty()
                        }
                    }
            }

        val grantRevokeAccessButtonText: String
            get() =
                "${
                    grantRevoke.lowercase(Locale.ROOT)
                        .replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.ROOT) else it.toString() }
                } Access"

        val showGrantAccessTerms: Boolean get() = !grantRevoke.equals(ACCESS_REVOKE, ignoreCase = true)

        val vehicleMake: String get() = applicationData.getSelectedVehicle()?.make.orEmpty()

        val vehicleModel: String get() = applicationData.getSelectedVehicle()?.modelName.orEmpty()

        /**
         * String where we pass "grant" or "revoke" via the navArgs in the Activity
         * When the user views details of a specific IRF, this variable will determine if the user
         * will be able to grant or revoke access to said IRF.
         * This variable changes based on user navigation, so it must ALWAYS be defined in
         * NavigationComponent Arguments and instantiated thusly. If it is not instantiated as such,
         * a runtime error will be thrown forcing developers to implement properly.
         * **/
        lateinit var grantRevoke: String

        /**
         * This field holds the last searchResults retrieved from the API.
         * We use this extra field in the event that the user inputs the same exact query.
         * In this event, rather than making an extra API call, we display the same results to the user
         * to avoid using unnecessary network resources.
         * **/
        private val _searchResultBuffer = mutableListOf<IRFInfoItem?>()

        /**
         * Field holding the search results retrieved from the API. This list will be filtered
         * by user query that will be displayed on the UI.
         * **/
        private val _searchResults = mutableListOf<IRFInfoItem?>()

        private val vin: String get() = applicationData.getSelectedVehicle()?.vin.orEmpty()

        /**
         * A string holding the last query used to make an API call. This field is used to
         * cross reference with the current user input to determine if we should make another API call
         * or simply display the list of items held in the searchResultBuffer/
         * **/
        private var _lastQuery: String? = null

        override fun onSearchResultClicked(item: IRFInfoItem) {
            _selectedIRFInfoItem.value = item
            _irfNavigationEvents.postValue(IRFLocationGrant)
        }

        override fun onServiceLocationClicked(item: IRFInfoItem) {
            _selectedIRFInfoItem.value = item
            _irfNavigationEvents.postValue(IRFLocationRevoke)
        }

        fun onAddServiceLocationClicked() {
            _irfNavigationEvents.postValue(SearchForServiceLocations)
        }

        fun onGrantRevokeAccessClicked() {
            _searchResults.clear()
            if (grantRevoke.equals(ACCESS_GRANT, ignoreCase = true)) {
                analyticsLogger.logEvent(AnalyticsEvent.HEALTH_AND_DIAGNOSTIC_ACCESS_GRANT_CLICK)
                _selectedIRFInfoItem.value?.let {
                    grantIrfAccessRequest(it)
                }
            } else if (grantRevoke.equals(ACCESS_REVOKE, ignoreCase = true)) {
                analyticsLogger.logEvent(AnalyticsEvent.HEALTH_AND_DIAGNOSTIC_ACCESS_REVOKE_CLICK)
                _selectedIRFInfoItem.value?.let {
                    revokeIrfAccessRequest(it)
                }
            }
        }

        /**
         * Method called when the user clicks on the search button on the keyboard.
         * If the user has not input a search query, we do nothing.
         * If the query length is 3, the API is already searching for results so we need not do anything
         * If the user's query's length is smaller than 3, we search based on said query.
         * If it is longer, the query must not start with the previous query. This covers
         * the scenario where a user would paste over the previous input and force a search.
         * Whether or not we search based on the user query, we always
         * hide the keyboard when the is invoked
         * **/
        fun onSearchClicked() {
            searchQuery.value?.let { query ->
                if (query.isBlank() || query.length == 3) {
                    return@let
                }

                if (query.length > 3 && _lastQuery?.let { !query.startsWith(it) } == true) {
                    sendIrfSearchRequest(query)
                    return@let
                }
            }

            hideSoftKeyboard()
        }

        fun resetSearch() {
            // Clearing search results to avoid flickering of past results before the EditText empties
            _searchResults.clear()
            searchQuery.value = ""
            _irfNavigationProgressEvents.postValue(HideProgress)
        }

        fun getGrantAccessInfoText(infoTextFormat: String) = String.format(infoTextFormat, _selectedIRFInfoItem.value?.name)

        fun onSearchTextChanged(text: CharSequence) {
            if (text.isEmpty()) {
                _searchResults.clear()
                _noResultsFound.postValue(false)
                return
            }

            if (text.length < 3) {
                return
            }

            val searchQuery = text.toString()
            if (searchQuery.length >= 3) {
                // Whenever the character length goes to 3 and the user has not searched by this string
                // previously, we make an API call to look up results based on user query
                if (_lastQuery?.let { !searchQuery.startsWith(it) } != false) {
                    sendIrfSearchRequest(searchQuery)
                    _lastQuery = searchQuery
                } else {
                    _searchResults.clearAndAdd(_searchResultBuffer)
                }
            }
        }

        fun sendIrfListRequest() =
            viewModelScope.launch {
                showProgress()
                val resource = irfRepository.getIRFList(vin)
                hideProgress()
                when (resource) {
                    is Resource.Success -> {
                        _accessGrantedIrfList
                            .postValue(resource.data?.payload ?: emptyList())
                        analyticsLogger
                            .logEvent(AnalyticsEvent.VHR_HISTORY_GET_CONSENTED_FACILITIES_SUCCESSFUL)
                    }

                    is Resource.Failure -> {
                        _irfNavigationEvents
                            .postValue(IRFListError(resource.message))
                        analyticsLogger
                            .logEvent(AnalyticsEvent.VHR_HISTORY_GET_CONSENTED_FACILITIES_UNSUCCESSFUL)
                    }

                    else -> {
                        // do nothing
                    }
                }
            }

        private fun sendIrfSearchRequest(query: String) =
            viewModelScope.launch {
                _searchResults.clear()
                _searchResultBuffer.clear()
                _noResultsFound.postValue(false)
                showProgressInRecyclerView()
                val resource = irfRepository.searchForIRF(vin, query)
                hideProgressInRecyclerView()
                when (resource) {
                    is Resource.Success -> {
                        val resultList = resource.data?.payload ?: emptyList()
                        resultList.let { result ->
                            _searchResults.clearAndAdd(result)
                            _searchResultBuffer.clearAndAdd(result)
                        }

                        analyticsLogger.logEvent(AnalyticsEvent.VHR_HISTORY_SEARCH_FACILITIES_SUCCESSFUL)
                        // notify LiveData observing the query that the results have changed
                        // so that the new results get filtered by the query
                        searchQuery.notifyObservers()
                    }

                    is Resource.Failure -> {
                        _irfNavigationEvents.postValue(IRFSearchError(resource.message))
                        analyticsLogger.logEvent(AnalyticsEvent.VHR_HISTORY_SEARCH_FACILITIES_UNSUCCESSFUL)
                    }

                    else -> {
                        // do nothing
                    }
                }
            }

        private fun grantIrfAccessRequest(irfInfoItem: IRFInfoItem) =
            viewModelScope.launch {
                showProgress()
                val resource = irfRepository.grantIRFAccess(irfInfoItem, vin)
                when (resource) {
                    is Resource.Success -> {
                        _irfNavigationEvents.postValue(IRFAccessGranted(true))
                        searchQuery.value = ""
                        analyticsLogger.logEvent(AnalyticsEvent.VHR_HISTORY_CONSENT_FACILITY_SUCCESSFUL)
                    }

                    is Resource.Failure -> {
                        hideProgress()
                        _irfNavigationEvents
                            .postValue(IRFAccessGranted(false, resource.message))
                        analyticsLogger
                            .logEvent(AnalyticsEvent.VHR_HISTORY_CONSENT_FACILITY_UNSUCCESSFUL)
                    }

                    else -> {
                        // do nothing
                    }
                }.exhaustive
            }

        private fun revokeIrfAccessRequest(irfInfoItem: IRFInfoItem) =
            viewModelScope.launch {
                showProgress()
                val resource = irfRepository.revokeIRFAccess(irfInfoItem, vin)
                when (resource) {
                    is Resource.Success -> {
                        _irfNavigationEvents.postValue(IRFAccessRevoked(true))
                        analyticsLogger
                            .logEvent(AnalyticsEvent.VHR_HISTORY_REVOKE_CONSENTED_FACILITY_SUCCESSFUL)
                    }

                    is Resource.Failure -> {
                        hideProgress()
                        _irfNavigationEvents
                            .postValue(IRFAccessRevoked(false, resource.message))
                        analyticsLogger
                            .logEvent(AnalyticsEvent.VHR_HISTORY_REVOKE_CONSENTED_FACILITY_UNSUCCESSFUL)
                    }

                    else -> {
                        // do nothing
                    }
                }.exhaustive
            }

        private fun showProgressInRecyclerView() {
            _irfNavigationProgressEvents.postValue(ShowProgress)
        }

        private fun hideProgressInRecyclerView() {
            _irfNavigationProgressEvents.postValue(HideProgress)
        }

        sealed class IRFNavigationEvent {
            object SearchForServiceLocations : IRFNavigationEvent()

            object IRFLocationGrant : IRFNavigationEvent()

            object IRFLocationRevoke : IRFNavigationEvent()

            data class IRFListError(
                val errorMessage: String?,
            ) : IRFNavigationEvent()

            data class IRFSearchError(
                val errorMessage: String?,
            ) : IRFNavigationEvent()

            data class IRFAccessGranted(
                val apiSuccessful: Boolean,
                val message: String? = null,
            ) : IRFNavigationEvent()

            data class IRFAccessRevoked(
                val apiSuccessful: Boolean,
                val message: String? = null,
            ) : IRFNavigationEvent()
        }

        sealed class IRFNavigationProgressEvent {
            object ShowProgress : IRFNavigationProgressEvent()

            object HideProgress : IRFNavigationProgressEvent()
        }
    }
