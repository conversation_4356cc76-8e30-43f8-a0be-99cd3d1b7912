package com.toyota.oneapp.ui.vehiclehealth

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemDiagnosticAccessBinding
import com.toyota.oneapp.extensions.clearAndAdd
import com.toyota.oneapp.model.vehiclehealth.IRFInfoItem
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class IRFListAdapter(
    val irfClickListener: IRFClickListener,
) : RecyclerView.Adapter<IRFListAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<IRFInfoItem> {
    private val irfItemList = mutableListOf<IRFInfoItem>()

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding: ItemDiagnosticAccessBinding =
            DataBindingUtil.inflate(
                LayoutInflater.from(parent.context),
                R.layout.item_diagnostic_access,
                parent,
                false,
            )

        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.bind(irfItemList[position])
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun setData(data: List<IRFInfoItem>?) {
        if (data == null) {
            return
        }
        irfItemList.clearAndAdd(data)
        notifyDataSetChanged()
    }

    override fun getItemCount() = irfItemList.size

    inner class ViewHolder(
        val binding: ItemDiagnosticAccessBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: IRFInfoItem) {
            binding.irfNameText.text = item.name
            val addressString = item.getMultiLineAddress()
            binding.irfAddressText.text = addressString
            binding.irfDaysRemainingText.text = item.validUntil
            binding.root.setOnClickListener {
                irfClickListener.onServiceLocationClicked(item)
            }
        }
    }
}

interface IRFClickListener {
    fun onServiceLocationClicked(item: IRFInfoItem)
}
