package com.toyota.oneapp.ui.vehiclehealth.ui

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentIrfListBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class IRFListFragment : BaseDataBindingFragment<FragmentIrfListBinding>() {
    private val mViewModel: IRFViewModel by activityViewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewModel.sendIrfListRequest()
    }

    override fun onViewBound(
        binding: FragmentIrfListBinding,
        savedInstance: Bundle?,
    ) {
        binding.lifecycleOwner = viewLifecycleOwner
        binding.apply {
            mViewModel.accessGrantedIrfList.observe(viewLifecycleOwner) { data ->
                binding.noAccessGrantedLayout.isVisible = data.isNullOrEmpty()
                binding.diagnosticAccessListLayout.isVisible = !data.isNullOrEmpty()
                binding.diagnosticAccessRecyclerView.adapter = mViewModel.diagnosticAccessAdapter
                DataBindingAdapters.setRecyclerViewAdapterData(binding.diagnosticAccessRecyclerView, data, emptyList())
            }
            binding.diagnosticAccessInfoText.setText(
                getString(R.string.VehicleHealth_diagnostic_access_info_format, mViewModel.vehicleMake, mViewModel.vehicleModel),
            )
        }
        observeBaseEvents(mViewModel)
    }

    override fun getLayout() = R.layout.fragment_irf_list
}
