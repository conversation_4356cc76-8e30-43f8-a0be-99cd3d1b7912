package com.toyota.oneapp.ui.vehiclehealth

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemHistoryReportBinding
import com.toyota.oneapp.extensions.clearAndAdd
import com.toyota.oneapp.model.HealthHistoryItem
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportClickListener
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class HealthReportAdapter(
    val healthReportClickListener: HealthReportClickListener,
) : RecyclerView.Adapter<HealthReportAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<HealthHistoryItem> {
    private val historyItemList = mutableListOf<HealthHistoryItem>()

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding: ItemHistoryReportBinding =
            DataBindingUtil.inflate(
                LayoutInflater.from(parent.context),
                R.layout.item_history_report,
                parent,
                false,
            )

        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.bind(historyItemList[position])
    }

    override fun setData(data: List<HealthHistoryItem>?) {
        if (data == null) {
            return
        }
        historyItemList.clearAndAdd(data)
        notifyDataSetChanged()
    }

    override fun getItemCount() = historyItemList.size

    inner class ViewHolder(
        val binding: ItemHistoryReportBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: HealthHistoryItem) {
            binding.dateText.text = item.displayTime
            binding.root.setOnClickListener {
                healthReportClickListener.onHealthHistoryItemClicked(
                    item,
                )
            }
        }
    }
}
