package com.toyota.oneapp.ui.vehiclehealth.ui

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.BottomSheetHealthReportEmailBinding
import com.toyota.oneapp.ui.baseClasses.BaseBottomSheetDialogFragment
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BottomSheetHealthReportEmail : BaseBottomSheetDialogFragment<BottomSheetHealthReportEmailBinding>() {
    val viewModel: HealthReportViewModel by activityViewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
    }

    companion object {
        const val TAG = "BottomSheetHealthReportEmail"
    }

    override fun getLayout(): Int = R.layout.bottom_sheet_health_report_email

    override fun onViewBound(
        binding: BottomSheetHealthReportEmailBinding,
        savedInstance: Bundle?,
    ) {
        binding.viewModel = viewModel
    }
}
