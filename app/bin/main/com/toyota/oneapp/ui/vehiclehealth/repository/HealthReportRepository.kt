package com.toyota.oneapp.ui.vehiclehealth.repository

import com.toyota.oneapp.model.EmailReportRequest
import com.toyota.oneapp.model.HealthHistoryItem
import com.toyota.oneapp.model.HealthHistoryResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.HealthReportCoroutineServiceApi
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class HealthReportRepository
    @Inject
    constructor(
        private val client: HealthReportCoroutineServiceApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext) {
        suspend fun getHealthHistory(vin: String): Resource<HealthHistoryResponse?> = makeApiCall { client.getHealthHistoryResponse(vin) }

        suspend fun getDiagnosticHistory(vin: String): Resource<HealthHistoryResponse?> =
            makeApiCall { client.getHealthDiagnosticsResponse(vin) }

        suspend fun requestEmailReport(
            vin: String,
            reportType: String,
            reportItem: HealthHistoryItem,
        ): Resource<BaseResponse?> {
            val body = EmailReportRequest(reportItem.month, reportItem.year, reportType)

            return makeApiCall { client.sendReportEmail(vin, body) }
        }
    }
