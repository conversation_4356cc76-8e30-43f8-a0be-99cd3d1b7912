package com.toyota.oneapp.ui.vehiclehealth.ui

import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityHealthReportBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.HealthReportNavigationEvent.*
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.ReportType
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.ReportType.DIAGNOSTIC_REPORT
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.HealthReportViewModel.ReportType.HEALTH_REPORT
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

@AndroidEntryPoint
class HealthReportActivity : DataBindingBaseActivity<ActivityHealthReportBinding>() {
    companion object {
        const val REPORT_TYPE = "REPORT_TYPE"
    }

    private val reportType by lazy { initReportType() }
    private val mViewModel: HealthReportViewModel by viewModels()

    override fun initViews(savedInstance: Bundle?) {
        binding.apply {
            lifecycleOwner = this@HealthReportActivity

            toolbar.let {
                setSupportActionBar(it)
                it.setNavigationOnClickListener { onBackPressed() }
            }

            mViewModel.let {
                observeBaseEvents(it)
            }
        }

        mViewModel.initialize(reportType)

        mViewModel.reportsAreEmpty.observe(this) { data ->
            DataBindingAdapters.setIsVisible(binding.noReportsMessage, data)
        }

        binding.reportRecyclerview.run {
            adapter = mViewModel.healthListAdapter
        }
        mViewModel.healthList.observe(this) { data ->
            DataBindingAdapters.setRecyclerViewAdapterData(binding.reportRecyclerview, data, emptyList())
        }

        initActivityByHealthReportType(reportType)
        initNavigationEvents()
    }

    override fun getLayoutId() = R.layout.activity_health_report

    private fun initActivityByHealthReportType(reportType: ReportType) {
        title =
            when (reportType) {
                HEALTH_REPORT -> getString(R.string.VehicleHealth_report_history)

                DIAGNOSTIC_REPORT -> getString(R.string.VehicleHealth_diagnostic_history)
            }.exhaustive
    }

    private fun initReportType() = (intent.getSerializableExtra(REPORT_TYPE) ?: HEALTH_REPORT) as ReportType

    private fun initNavigationEvents() {
        mViewModel.healthReportNavigationEvents.observe(
            this,
            Observer { event ->
                when (event) {
                    is SendEmailPrompt -> {
                        BottomSheetHealthReportEmail()
                            .show(supportFragmentManager, BottomSheetHealthReportEmail.TAG)
                    }

                    is EmailSent -> {
                        val format = getString(R.string.VehicleHealth_email_success_message_format)
                        val monthString = ToyUtil.monthNumToText(this, event.selectedItem.month)
                        val yearString = event.selectedItem.year
                        val dateString = "$monthString $yearString"
                        val successMessage = String.format(format, dateString, event.userEmail)

                        dismissEmailBottomSheet()
                        showSuccessToast(successMessage)
                    }

                    is SendEmailRequestError -> {
                        dismissEmailBottomSheet()
                        showErrorToast(event.errorMessage)
                    }

                    is ReportRequestError -> showReportRequestErrorDialog(event.errorMessage)
                }.exhaustive
            },
        )
    }

    private fun dismissEmailBottomSheet() {
        val bottomSheetHealthReportEmail =
            supportFragmentManager.findFragmentByTag(BottomSheetHealthReportEmail.TAG)
        (bottomSheetHealthReportEmail as BottomSheetDialogFragment)
            .dismissAllowingStateLoss()
    }

    private fun showReportRequestErrorDialog(errorMessage: String) {
        val cancel = getString(R.string.return_text)
        DialogUtil.showDialog(
            this,
            null,
            errorMessage,
            null,
            cancel,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    // do nothing
                }

                override fun onCancelClick() {
                    onBackPressed()
                }
            },
            false,
        )
    }
}
