package com.toyota.oneapp.ui.vehiclehealth

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemDiagnosticSearchResultBinding
import com.toyota.oneapp.model.vehiclehealth.IRFInfoItem
import com.toyota.oneapp.util.dataBinding.BindableRecyclerViewAdapter

class IRFSearchResultAdapter(
    val searchResultClickListener: SearchResultClickListener,
) : RecyclerView.Adapter<IRFSearchResultAdapter.ViewHolder>(),
    BindableRecyclerViewAdapter<IRFInfoItem> {
    private val searchResultItemList = mutableListOf<IRFInfoItem>()

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val binding: ItemDiagnosticSearchResultBinding =
            DataBindingUtil.inflate(
                LayoutInflater.from(parent.context),
                R.layout.item_diagnostic_search_result,
                parent,
                false,
            )

        return ViewHolder(binding)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.bind(searchResultItemList[position])
    }

    override fun setData(data: List<IRFInfoItem>?) {
        if (data == null) {
            return
        }
        searchResultItemList.clear()
        searchResultItemList.addAll(data)
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = searchResultItemList.size

    inner class ViewHolder(
        val binding: ItemDiagnosticSearchResultBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: IRFInfoItem) {
            binding.irfNameText.text = item.name
            val addressString = item.getMultiLineAddress()
            binding.irfAddressText.text = addressString

            binding.root.setOnClickListener { searchResultClickListener.onSearchResultClicked(item) }
        }
    }
}

interface SearchResultClickListener {
    fun onSearchResultClicked(item: IRFInfoItem)
}
