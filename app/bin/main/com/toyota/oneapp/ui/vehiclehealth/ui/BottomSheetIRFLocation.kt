package com.toyota.oneapp.ui.vehiclehealth.ui

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.navArgs
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.BottomSheetIrfLocationBinding
import com.toyota.oneapp.ui.baseClasses.BaseBottomSheetDialogFragment
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BottomSheetIRFLocation : BaseBottomSheetDialogFragment<BottomSheetIrfLocationBinding>() {
    val mViewModel: IRFViewModel by activityViewModels()
    val args: BottomSheetIRFLocationArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(mViewModel)
    }

    override fun getLayout(): Int = R.layout.bottom_sheet_irf_location

    override fun onViewBound(
        binding: BottomSheetIrfLocationBinding,
        savedInstance: Bundle?,
    ) {
        mViewModel.grantRevoke = args.grantRevoke
        mViewModel.selectedIRFInfoItem.observe(viewLifecycleOwner) {
            viewDataBinding.irfNameTextView.text = it.name
        }
        mViewModel.formattedAddress.observe(viewLifecycleOwner) {
            viewDataBinding.irfAddressTextView.text = it
        }
        DataBindingAdapters.setIsGone(
            viewDataBinding.grantAccessTermsTextView,
            !mViewModel.showGrantAccessTerms,
        )
        viewDataBinding.grantAccessButton.text = mViewModel.grantRevokeAccessButtonText
        viewDataBinding.grantAccessButton.setOnClickListener {
            mViewModel.onGrantRevokeAccessClicked()
        }
    }

    /**
     * Overriding setupDialog to expand the bottomsheet completely, as, when we display
     * the "Grant Access" text, it takes up a large part of the screen.
     */
    @SuppressLint("RestrictedApi")
    override fun setupDialog(
        dialog: Dialog,
        style: Int,
    ) {
        super.setupDialog(dialog, style)
        val rootView = View.inflate(context, getLayout(), null)
        dialog.setContentView(rootView)
        val bottomSheet = dialog.window?.findViewById(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout
        val behaviour = BottomSheetBehavior.from(bottomSheet)
        behaviour.state = BottomSheetBehavior.STATE_EXPANDED
    }
}
