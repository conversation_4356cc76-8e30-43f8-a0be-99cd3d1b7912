package com.toyota.oneapp.ui.vehiclehealth.repository

import com.toyota.oneapp.model.vehiclehealth.IRFApprovalGrantRequest
import com.toyota.oneapp.model.vehiclehealth.IRFApprovalRevokeRequest
import com.toyota.oneapp.model.vehiclehealth.IRFInfoItem
import com.toyota.oneapp.model.vehiclehealth.IRFResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.IRFCoroutineServiceAPI
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class IRFRepository
    @Inject
    constructor(
        private val client: IRFCoroutineServiceAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext) {
        suspend fun getIRFList(vin: String): Resource<IRFResponse?> = makeApiCall { client.getGrantedIRFs(vin) }

        suspend fun searchForIRF(
            vin: String,
            query: String,
        ): Resource<IRFResponse?> = makeApiCall { client.searchForIRFsByName(vin, query) }

        suspend fun grantIRFAccess(
            irfInfoItem: IRFInfoItem,
            vin: String,
        ): Resource<BaseResponse?> {
            val grantRequest = IRFApprovalGrantRequest(irfInfoItem)
            return makeApiCall {
                client.grantIRFAccess(irfInfoItem.id, vin, grantRequest)
            }
        }

        suspend fun revokeIRFAccess(
            irfInfoItem: IRFInfoItem,
            vin: String,
        ): Resource<BaseResponse?> =
            makeApiCall {
                client.revokeIRFAccess(irfInfoItem.id, vin, IRFApprovalRevokeRequest())
            }
    }
