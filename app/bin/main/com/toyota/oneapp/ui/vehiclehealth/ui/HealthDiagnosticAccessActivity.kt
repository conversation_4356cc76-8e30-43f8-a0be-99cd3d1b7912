package com.toyota.oneapp.ui.vehiclehealth.ui

import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.NavController
import androidx.navigation.NavGraph
import androidx.navigation.fragment.NavHostFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ActivityDiagnosticAccessBinding
import com.toyota.oneapp.extensions.KotlinExtensions.exhaustive
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel.IRFNavigationEvent.*
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface

@AndroidEntryPoint
class HealthDiagnosticAccessActivity : DataBindingBaseActivity<ActivityDiagnosticAccessBinding>() {
    private val mViewModel: IRFViewModel by viewModels()
    private lateinit var navGraph: NavGraph

    override fun initViews(savedInstance: Bundle?) {
        binding.apply {
            toolbar.let {
                setSupportActionBar(it)
                it.setNavigationOnClickListener { onBackPressed() }
            }
        }

        observeBaseEvents(mViewModel)
        initNavigationComponents()
    }

    override fun getLayoutId() = R.layout.activity_diagnostic_access

    private fun initNavigationComponents() {
        val navHostFragment =
            (supportFragmentManager.findFragmentById(binding.navHost.id) as NavHostFragment)

        navHostFragment.navController.let {
            val graphInflater = it.navInflater
            navGraph = graphInflater.inflate(R.navigation.diagnostic_access_nav_graph)
            initOnDestinationChangedListener(it)
            observeNavigationEvents(it)
        }
    }

    private fun observeNavigationEvents(navController: NavController) {
        mViewModel.irfNavigationEvents.observe(
            this@HealthDiagnosticAccessActivity,
            Observer { event ->
                when (event) {
                    is SearchForServiceLocations ->
                        navController.navigate(
                            IRFListFragmentDirections.actionIrfListFragmentToIrfSearchFragment(),
                        )
                    is IRFLocationGrant ->
                        navController.navigate(
                            IRFSearchFragmentDirections.actionIrfSearchToGrantAccessBottomSheet(),
                        )
                    is IRFLocationRevoke ->
                        navController.navigate(
                            IRFListFragmentDirections.actionIrfListFragmentToGrantIrfAccessBottomSheet(),
                        )
                    is IRFAccessGranted -> {
                        if (event.apiSuccessful) {
                            navController.navigate(
                                BottomSheetIRFLocationDirections.actionGrantIrfAccessBottomSheetToIrfListFragment(),
                            )
                            showSuccessToast(getString(R.string.irf_access_granted_success))
                        } else {
                            showErrorToast(event.message)
                        }
                    }
                    is IRFAccessRevoked -> {
                        if (event.apiSuccessful) {
                            navController.navigate(
                                BottomSheetIRFLocationDirections.actionGrantIrfAccessBottomSheetToIrfListFragment(),
                            )
                            showSuccessToast(getString(R.string.irf_access_revoked_success))
                        } else {
                            showErrorToast(event.message)
                        }
                    }

                    is IRFSearchError -> {
                        showErrorToast(event.errorMessage)
                    }
                    is IRFListError -> {
                        showIRFListErrorDialog(event.errorMessage)
                    }
                }.exhaustive
            },
        )
    }

    private fun showIRFListErrorDialog(errorMessage: String?) {
        val message = errorMessage ?: getString(R.string.VehicleHealth_server_not_reached_error)
        DialogUtil.showDialog(
            this,
            null,
            message,
            null,
            getString(R.string.return_text),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    finish()
                }

                override fun onCancelClick() {
                    finish()
                }
            },
            false,
        )
    }

    private fun initOnDestinationChangedListener(navController: NavController) {
        navController.addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                R.id.irfListFragment ->
                    binding.toolbar.title =
                        getString(
                            R.string.VehicleHealth_diagnostic_access,
                        )

                R.id.irfSearchFragment ->
                    binding.toolbar.title =
                        getString(
                            R.string.VehicleHealth_add_repair_facility,
                        )
            }
        }
    }
}
