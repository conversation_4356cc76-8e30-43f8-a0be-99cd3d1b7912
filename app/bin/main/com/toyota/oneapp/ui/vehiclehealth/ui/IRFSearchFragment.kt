package com.toyota.oneapp.ui.vehiclehealth.ui

import android.os.Bundle
import android.view.inputmethod.EditorInfo
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentIrfSearchBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.vehiclehealth.viewmodel.IRFViewModel
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class IRFSearchFragment : BaseDataBindingFragment<FragmentIrfSearchBinding>() {
    private val mViewModel: IRFViewModel by activityViewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        mViewModel.resetSearch()
    }

    override fun onViewBound(
        binding: FragmentIrfSearchBinding,
        savedInstance: Bundle?,
    ) {
        binding.apply {
            lifecycleOwner = viewLifecycleOwner

            searchEt.doOnTextChanged { text, _, _, _ ->
                text?.let { mViewModel.onSearchTextChanged(it) }
            }
            clearTextButton.setOnClickListener { mViewModel.resetSearch() }

            DataBindingAdapters.setAdapter(searchResultsRecyclerView, mViewModel.searchResultAdapter)

            mViewModel.searchQuery.observe(viewLifecycleOwner) { data -> searchEt.setText(data) }
            mViewModel.filteredResults.observe(
                viewLifecycleOwner,
            ) { data -> DataBindingAdapters.setRecyclerViewAdapterData(searchResultsRecyclerView, data, emptyList()) }
            mViewModel.noResultsFound.observe(viewLifecycleOwner) { data -> DataBindingAdapters.setIsVisible(llLocationError, data) }

            observeBaseEvents(mViewModel)
            observeProgressEvents(mViewModel, this)
            hideKeyboardOnSearchClick()
        }
    }

    override fun getLayout() = R.layout.fragment_irf_search

    private fun observeProgressEvents(
        viewModel: IRFViewModel,
        binding: FragmentIrfSearchBinding,
    ) {
        viewModel.run {
            irfNavigationProgressEvents.observe(
                viewLifecycleOwner,
                Observer { event ->
                    binding.run {
//                        when (event) {
//                        is ShowProgress -> progressBar.visibility = View.VISIBLE
//                        is HideProgress -> progressBar.visibility = View.GONE
//                        }
                    }
                },
            )
        }
    }

    private fun hideKeyboardOnSearchClick() {
        viewDataBinding.searchEt.setOnEditorActionListener { _, i, _ ->
            if (i == EditorInfo.IME_ACTION_SEARCH) {
                mViewModel.onSearchClicked()
                return@setOnEditorActionListener true
            }

            return@setOnEditorActionListener false
        }
    }
}
