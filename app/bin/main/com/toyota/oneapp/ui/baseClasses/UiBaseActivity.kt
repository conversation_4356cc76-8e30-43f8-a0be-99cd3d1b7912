package com.toyota.oneapp.ui.baseClasses

import android.view.MenuItem
import androidx.appcompat.widget.Toolbar
import com.toyota.oneapp.ui.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
abstract class UiBaseActivity : BaseActivity() {
    protected fun performActivitySetup(toolbar: Toolbar) {
        setSupportActionBar(toolbar)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
} // UiBaseActivity abstract class
