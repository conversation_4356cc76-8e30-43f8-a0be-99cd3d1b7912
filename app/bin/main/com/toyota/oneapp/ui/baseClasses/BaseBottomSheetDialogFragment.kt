package com.toyota.oneapp.ui.baseClasses

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.ui.BaseActivity
import com.toyota.oneapp.ui.BaseViewModel

abstract class BaseBottomSheetDialogFragment<B : ViewDataBinding> :
    BottomSheetDialogFragment(),
    DataBindingFragmentLifeCycle<B> {
    open lateinit var viewDataBinding: B

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? = bindView(inflater, container, savedInstanceState)

    override fun bindView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstance: Bundle?,
    ): View? {
        viewDataBinding =
            DataBindingUtil.inflate(inflater, getLayout(), container, false)
        viewDataBinding.lifecycleOwner = viewLifecycleOwner
        return viewDataBinding.root
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        onViewBound(viewDataBinding, savedInstanceState)
    }

    override fun getTheme() = R.style.AppBottomSheetDialogTheme

    fun observeBaseEvents(vm: BaseViewModel) {
        (requireActivity() as? BaseActivity)?.observeBaseEvents(vm)
    }
}
