package com.toyota.oneapp.ui.baseClasses

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.R
import com.toyota.oneapp.util.dataBinding.Event
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.toast.ToastUtil

sealed class DialogData(
    protected val textResId: Int,
) {
    abstract fun showDialog(activity: AppCompatActivity)
} // DialogData sealed class // AlertDialogData class

class ConfirmationDialogData(
    textResId: Int,
    private val confirmTextResId: Int,
    private val confirmAction: () -> Unit,
) : DialogData(textResId) {
    override fun showDialog(activity: AppCompatActivity) {
        DialogUtil.showMessageDialog(
            activity,
            null,
            activity.getString(textResId),
            activity.getString(confirmTextResId),
            activity.getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    confirmAction()
                }

                override fun onCancelClick() {
                    // Nothing, in this case.
                }
            },
            false,
        )
    }
} // ConfirmationDialogData class

class ToastDialogData(
    textResId: Int,
) : DialogData(textResId) {
    override fun showDialog(activity: AppCompatActivity) {
        ToastUtil.show(activity, activity.getString(textResId), R.drawable.toast_check)
    }

    // Convenience function for use in unit tests.
    override fun equals(other: Any?): Boolean {
        val otherObject = (other as? ToastDialogData) ?: return false
        return textResId == otherObject.textResId
    }
} // ToastDialogData class

interface DialogEventCreator {
    val dialogEvent: LiveData<Event<DialogData>>
} // DialogEventCreator interface

class DialogEventCreatorDelegate : DialogEventCreator {
    private val mutableDialogEvent: MutableLiveData<Event<DialogData>> = MutableLiveData()

    override val dialogEvent: LiveData<Event<DialogData>> = mutableDialogEvent

    fun postDialog(dialogData: DialogData) {
        mutableDialogEvent.postValue(Event(dialogData))
    }
} // DialogEventCreatorDelegate class

interface DialogEventObserver {
    fun observeDialog(
        activity: AppCompatActivity,
        dialogEventCreator: DialogEventCreator,
    )
} // DialogEventObserver interface

class DialogEventObserverDelegate : DialogEventObserver {
    override fun observeDialog(
        activity: AppCompatActivity,
        dialogEventCreator: DialogEventCreator,
    ) {
        dialogEventCreator.dialogEvent.observe(activity, Event.Observer { it.showDialog(activity) })
    }
} // DialogEventObserverDelegate class
