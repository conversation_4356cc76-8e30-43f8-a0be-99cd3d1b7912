package com.toyota.oneapp.ui.baseClasses

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding

/**
 * Base Data Binding Activity to use data binding features with minimal implementation
 */
abstract class DataBindingBaseActivity<VB : ViewDataBinding> : UiBaseActivity() {
    protected open lateinit var binding: VB

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        binding = DataBindingUtil.setContentView(this, getLayoutId())
        binding.lifecycleOwner = this
        initViews(savedInstance)
    }

    protected abstract fun getLayoutId(): Int

    protected abstract fun initViews(savedInstance: Bundle?)
}
