package com.toyota.oneapp.ui.baseClasses

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.databinding.ViewDataBinding

interface DataBindingFragmentLifeCycle<B : ViewDataBinding> {
    /**
     * This method is used to initialize the view data binding in fragments and return the root view.
     * Call this method from onCreateView() method of the base fragment
     */
    fun bindView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstance: Bundle?,
    ): View?

    /**
     * This method should be used to start any operation in fragments.
     * Call this method from onViewCreated() method of base fragment
     */
    fun onViewBound(
        binding: B,
        savedInstance: Bundle?,
    )

    /**
     * This method returns the layout id of the fragment
     * @return [LayoutRes]
     */
    @LayoutRes
    fun getLayout(): Int
}
