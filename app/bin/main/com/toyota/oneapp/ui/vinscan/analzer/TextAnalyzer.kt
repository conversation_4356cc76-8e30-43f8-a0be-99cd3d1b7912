package com.toyota.oneapp.ui.vinscan.analzer

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.graphics.RectF
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import com.toyota.oneapp.ui.vinscan.view.BarcodeBoxView
import com.toyota.oneapp.util.StringUtil.isAlphaNumeric

class TextAnalyzer(
    private val context: Context,
    private val barcodeBoxView: BarcodeBoxView,
    private val previewViewWidth: Float,
    private val previewViewHeight: Float,
    private val onScanResult: (String?) -> Unit,
) : ImageAnalysis.Analyzer {
    /**
     * This parameters will handle preview box scaling
     */
    private var scaleX = 1f
    private var scaleY = 1f

    private fun translateX(x: Float) = x * scaleX

    private fun translateY(y: Float) = y * scaleY

    private fun adjustBoundingRect(rect: Rect) =
        RectF(
            translateX(rect.left.toFloat()),
            translateY(rect.top.toFloat()),
            translateX(rect.right.toFloat()),
            translateY(rect.bottom.toFloat()),
        )

    @SuppressLint("UnsafeOptInUsageError")
    override fun analyze(image: ImageProxy) {
        val img = image.image
        if (img != null) {
            // Update scale factors
            scaleX = previewViewWidth / img.height.toFloat()
            scaleY = previewViewHeight / img.width.toFloat()

            val inputImage = InputImage.fromMediaImage(img, image.imageInfo.rotationDegrees)

            // Process image searching for barcodes

            val textScanner = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)

            textScanner
                .process(inputImage)
                .addOnSuccessListener { visionText ->

                    val resultText = visionText.text
                    for (block in visionText.textBlocks) {
                        val blockText = block.text
                        val blockCornerPoints = block.cornerPoints
                        val blockFrame = block.boundingBox
                        for (line in block.lines) {
                            val lineText = line.text
                            val lineCornerPoints = line.cornerPoints
                            val lineFrame = line.boundingBox
                            for (element in line.elements) {
                                val elementText = element.text
                                val elementCornerPoints = element.cornerPoints
                                val elementFrame = element.boundingBox

                                if (elementText.isNotBlank() && elementText.isAlphaNumeric() && elementText.length == 17) {
                                    // Handle received barcodes...
                                    onScanResult(elementText)
                                    image.close()
                                }

                                // Update bounding rect
                                element.boundingBox?.let { rect ->
                                    barcodeBoxView.setRect(
                                        adjustBoundingRect(
                                            rect,
                                        ),
                                    )
                                }
                            }
                        }
                    }
                }.addOnFailureListener { }
                .addOnCompleteListener {
                    image.close()
                }
        }
    }
}
