package com.toyota.oneapp.ui.vinscan

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.util.Constants.BAR_CODE_HEIGHT_DOUBLE
import com.toyota.oneapp.features.core.util.Constants.BAR_CODE_OFFSET_DOUBLE
import com.toyota.oneapp.features.core.util.Constants.BAR_CODE_WIDTH_DOUBLE
import com.toyota.oneapp.features.core.util.Constants.BAR_CODE_ZERO_FIVE_DOUBLE
import com.toyota.oneapp.features.core.util.Constants.INT_12

@Composable
fun TransparentFrameLayout(
    modifier: Modifier,
    width: Dp,
    height: Dp,
    offsetX: Dp = 0.dp,
    offsetY: Dp,
    cornerRadius: Float = 30f,
) {
    val xOffsetInPx: Float
    val yOffsetInPx: Float
    val widthInPx: Float
    val heightInPx: Float

    with(LocalDensity.current) {
        xOffsetInPx = offsetX.toPx()
        yOffsetInPx = offsetY.toPx()
        widthInPx = width.toPx()
        heightInPx = height.toPx()
    }

    Canvas(modifier = modifier) {
        val canvasWidth = size.width

        with(drawContext.canvas.nativeCanvas) {
            val checkPoint = saveLayer(null, null)

            // Destination
            drawRect(Color(0xA3000000))

            // Source
            drawRoundRect(
                topLeft =
                    Offset(
                        x = if (xOffsetInPx == 0F) (canvasWidth - widthInPx) / 2 else xOffsetInPx,
                        y = yOffsetInPx,
                    ),
                size = Size(widthInPx, heightInPx),
                cornerRadius = CornerRadius(x = cornerRadius, y = cornerRadius),
                color = Color.Transparent,
                blendMode = BlendMode.Clear,
            )
            restoreToCount(checkPoint)
        }
    }
}

@Composable
fun AddBarcodeView() {
    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        val pair = getScreenConfiguration()
        val screenHeight = pair.second
        val screenWidth = pair.first
        val width = screenWidth / BAR_CODE_WIDTH_DOUBLE
        val height = screenHeight / BAR_CODE_HEIGHT_DOUBLE
        val yOffset = screenHeight / BAR_CODE_OFFSET_DOUBLE

        var diameter = screenWidth
        if (screenHeight < screenWidth) {
            diameter = screenHeight
        }
        diameter -= (BAR_CODE_ZERO_FIVE_DOUBLE * diameter).toInt()
        val xOffset = screenWidth / INT_12
        TransparentFrameLayout(
            modifier = Modifier.fillMaxSize(),
            width = width.dp,
            height = height.dp,
            offsetY = yOffset.dp,
            offsetX = xOffset.dp,
        )
    }
}

@Composable
fun getScreenConfiguration(): Pair<Int, Int> {
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp
    val screenWidth = configuration.screenWidthDp
    return Pair(screenWidth, screenHeight)
}
