package com.toyota.oneapp.ui.vinscan

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.component.QRScanHelper
import com.toyota.oneapp.component.VprsSyncHelper
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class QRScanViewModel
    @Inject
    constructor(
        private val qrScanHelper: QRScanHelper,
        private val vprsSyncHelper: VprsSyncHelper,
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
    ) : BaseViewModel() {
        private val mQrScanResult = MutableLiveData<QRScanHelper.ResultType>()
        val qrScanResult: LiveData<QRScanHelper.ResultType> get() = mQrScanResult

        fun processUserCode(
            activity: AppCompatActivity,
            userCode: String,
        ) {
            qrScanHelper.processUserCode(activity, userCode) { resultType ->
                mQrScanResult.postValue(resultType)
            }
        }

        fun syncVprs() = vprsSyncHelper.sync()

        fun logEvent(analyticsEvent: AnalyticsEvent) {
            analyticsLogger.logEvent(analyticsEvent)
        }

        fun resetSelectedVehicle() {
            applicationData.handleAddVehicleBackPressed()
        }
    }
