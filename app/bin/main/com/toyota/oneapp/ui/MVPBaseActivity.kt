package com.toyota.oneapp.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityManager
import android.app.Dialog
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.graphics.Color
import android.net.ConnectivityManager
import android.os.Bundle
import android.os.Handler
import android.os.LocaleList
import android.os.Looper
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.app.NotificationManagerCompat
import androidx.work.WorkManager
import com.evernote.android.state.StateSaver
import com.google.android.material.snackbar.BaseTransientBottomBar
import com.google.android.material.snackbar.Snackbar
import com.google.android.play.core.splitcompat.SplitCompat
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.component.IDPHelper
import com.toyota.oneapp.component.receiver.NetworkStateReceiver
import com.toyota.oneapp.crashlytics.FirebaseCrashlyticsLogger
import com.toyota.oneapp.digitalkey.AUTHENTIC_FAIL
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.fcm.ToyotaFCMService.NOTIFICATION_ID
import com.toyota.oneapp.util.*
import com.toyota.oneapp.util.ToyotaConstants.Companion.CHECK_NETWORK_ACTION
import com.toyota.oneapp.worker.AutoLaunchSetupWorker
import com.toyota.oneapp.xcapp.manager.XcappManagerProvider
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.mvpcore.EXMVPBaseAppCompatActivity
import toyotaone.commonlib.mvpcore.EXMVPBasePresenter
import toyotaone.commonlib.permission.PermissionPageUtil
import toyotaone.commonlib.toast.ToastUtil
import toyotaone.commonlib.wear.WearAPIType
import toyotaone.commonlib.wear.WearConstants
import toyotaone.commonlib.wear.WearResponse
import java.io.File
import java.security.Signature
import javax.inject.Inject

abstract class MVPBaseActivity<P : EXMVPBasePresenter<*>> :
    EXMVPBaseAppCompatActivity<P>(),
    BasePresenter.BaseView,
    NetworkStateReceiver.NetworkStateChangeListener {
    lateinit var digitalMopKeyUtils: DigitalMopKeyUtils
    private lateinit var firebaseCrashlyticsLogger: FirebaseCrashlyticsLogger
    protected var dispatchTouchEventEnable = true
    private var fingerPrintDialog: Dialog? = null
    private var noNetworkDialog: Dialog? = null
    private var progressDialogFragment: ProgressDialogFragment? = null

    //    private var showLexus = "L".equals(BuildConfig.APP_BRAND, true)
    private lateinit var netWorkStateReceiver: NetworkStateReceiver
    private var filter = IntentFilter()

    val MY_PREFS_NAME = "ToyotaPrefsFile"
    private lateinit var snackBarView: Snackbar
    private var isDashboard = false
    private var offlineDialog: AlertDialog? = null
    private var isOfflineMode = false

    @Inject
    lateinit var appData: ApplicationData

    override fun attachBaseContext(newBase: Context) {
        super.attachBaseContext(newBase)
        applyOverrideConfiguration(
            Configuration().apply {
                setLocales(
                    LocaleList(AppLanguageUtils.getCurrentLocale()),
                )
            },
        )
        SplitCompat.installActivity(this)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        cancelNotification(intent)
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        digitalMopKeyUtils = DigitalMopKeyUtils.getInstance(this)
        initProgressFragment()

        adjustFontScale(resources.configuration)

        try {
            val sharedPreferences =
                getSharedPreferences(
                    "google_bug_154855417",
                    Context.MODE_PRIVATE,
                )
            if (!sharedPreferences.contains("fixed")) {
                val corruptedZoomTables = File(filesDir, "ZoomTables.data")
                val corruptedSavedClientParameters = File(filesDir, "SavedClientParameters.data.cs")
                val corruptedClientParametersData =
                    File(
                        filesDir,
                        "DATA_ServerControlledParametersManager.data.v1.$packageName",
                    )
                corruptedZoomTables.delete()
                corruptedSavedClientParameters.delete()
                corruptedClientParametersData.delete()
                sharedPreferences.edit().putBoolean("fixed", true).apply()
            }
        } catch (exception: Exception) {
        }
        presenter
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        if (presenter != null) {
            StateSaver.restoreInstanceState(presenter, savedInstance)
        }

        val shortcutAction = intent.action
        if (!shortcutAction.isNullOrEmpty() &&
            !shortcutAction.equals(
                "android.intent.action.MAIN",
                false,
            )
        ) {
            appData.threeDShortAction = shortcutAction
        }

        initNetworkStatusSnackBar()
        cancelNotification(intent)
    }

    fun xCappInitilizationFinshed() {
        // lazy init xcappManager
        if (XcappManagerProvider.isXcappInitialized()) {
            try {
                XcappManagerProvider.xcappManager
            } catch (ex: Exception) {
                firebaseCrashlyticsLogger.logMessage("Lazy init XcappManager ${ex.message}")
            }
        }
    }

    private fun cancelNotification(intent: Intent?) {
        val notificationID = intent?.getIntExtra(NOTIFICATION_ID, -1) ?: -1
        if (notificationID != -1) {
            NotificationManagerCompat.from(this).cancel(notificationID)
        }
    }

    private fun initProgressFragment() {
        progressDialogFragment = ProgressDialogFragment()
    }

    @SuppressLint("WrongConstant")
    private fun initNetworkStatusSnackBar() {
        snackBarView =
            Snackbar.make(
                findViewById(android.R.id.content),
                resources.getString(R.string.Common_network_without_internet_error),
                Snackbar.LENGTH_INDEFINITE,
            )

        snackBarView.animationMode = BaseTransientBottomBar.ANIMATION_MODE_FADE
        snackBarView.setAction(resources.getString(R.string.Common_dismiss)) { snackBarView.dismiss() }
        snackBarView.setActionTextColor(Color.WHITE)
    }

    private fun adjustFontScale(configuration: Configuration) {
        configuration.fontScale = 1.0.toFloat()
        val metrics = resources.displayMetrics
        val wm = getSystemService(WINDOW_SERVICE) as WindowManager
        wm.defaultDisplay.getMetrics(metrics)
        metrics.scaledDensity = configuration.fontScale * metrics.density
        baseContext.resources.updateConfiguration(configuration, metrics)
    }

    override fun onResume() {
        super.onResume()
        netWorkStateReceiver = NetworkStateReceiver(this@MVPBaseActivity)
        filter.addCategory(Intent.CATEGORY_DEFAULT)
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION)
        filter.addAction(CHECK_NETWORK_ACTION)
        filter.addAction(DigitalMopKeyUtils.ACTION_REMOTE_AUTH)
        registerReceiver(netWorkStateReceiver, filter, RECEIVER_EXPORTED)
        // TODO: LocalBroadcastManager is deprecated: https://developer.android.com/jetpack/androidx/releases/localbroadcastmanager
//        LocalBroadcastManager.getInstance(this).registerReceiver(netWorkStateReceiver, filter)
    }

    override fun showProxyDetectedAlert() {
        DialogUtil
            .showDialog(
                this,
                getString(R.string.VPN_Detected),
                getString(R.string.VPN_Message),
                getString(R.string.ManagePaidSubscription_Try_Again),
                null,
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        onResume()
                    }

                    override fun onCancelClick() {}
                },
                false,
            ).setCanceledOnTouchOutside(false)
    }

    override fun onPause() {
        super.onPause()
//        LocalBroadcastManager.getInstance(this).unregisterReceiver(netWorkStateReceiver)
        unregisterReceiver(netWorkStateReceiver)
    }

    override fun onDestroy() {
        progressDialogFragment = null
        super.onDestroy()
        onApplicationKilled()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (presenter != null) {
            StateSaver.saveInstanceState(presenter!!, outState)
        }
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        if (presenter != null) {
            StateSaver.restoreInstanceState(presenter, savedInstanceState)
        }
    }

    fun startActivityWithoutAnim(intent: Intent?) {
        super.startActivity(intent)
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (dispatchTouchEventEnable) {
            if (ev.action == MotionEvent.ACTION_DOWN) {
                val v = currentFocus
                if (shouldHideInput(v, ev)) {
                    val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.hideSoftInputFromWindow(v?.windowToken, 0)
                }
                return super.dispatchTouchEvent(ev)
            }
            return if (window.superDispatchTouchEvent(ev)) {
                true
            } else {
                onTouchEvent(ev)
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    private fun shouldHideInput(
        v: View?,
        event: MotionEvent,
    ): Boolean {
        if (v != null && v is EditText) {
            val leftTop = intArrayOf(0, 0)
            v.getLocationInWindow(leftTop)
            val left = leftTop[0]
            val top = leftTop[1]
            val bottom = top + v.height
            val right = left + v.width
            if (event.x > left &&
                event.x < right &&
                event.y > top &&
                event.y < bottom
            ) {
                return false
            } else {
                v.isFocusable = false
                v.isFocusableInTouchMode = true
                return true
            }
        }
        return false
    }

    override fun showMessage(errorMsg: String?) {
        if (!errorMsg.isNullOrBlank()) {
            showDialog(errorMsg)
        } else {
            showMessage()
        }
    }

    fun showMessage() {
        showDialog(getString(R.string.generic_error))
    }

    protected fun showNoNetworkDialog() {
        if (noNetworkDialog != null) {
            noNetworkDialog?.dismiss()
            noNetworkDialog = null
        }
        hideProgressDialog()
        noNetworkDialog = showCustomDialog(getString(R.string.Login_no_internet_connection))
    }

    private fun showBiometricDialog(callback: (isSuccess: Boolean, exception: Throwable?) -> Unit) {
        BiometryUtil.showBiometryDialog(
            this,
            getString(R.string.Biometry_dialog_title),
            "",
            getString(R.string.Common_cancel),
            object : BiometryUtil.BiometryCallback {
                override fun onSuccess(signature: Signature?) {
                    callback.invoke(true, null)
                }

                override fun onFailure() {
                    callback.invoke(false, RuntimeException(AUTHENTIC_FAIL))
                }

                override fun onLockedFailure() {
                    callback.invoke(false, RuntimeException(AUTHENTIC_FAIL))
                }

                override fun onCancelled() {
                    callback.invoke(false, RuntimeException(AUTHENTIC_FAIL))
                }

                override fun onError(errorCode: Int) {
                    callback.invoke(false, RuntimeException(AUTHENTIC_FAIL))
                }
            },
        )
    }

    fun showNoNetworkToast() {
        hideProgressDialog()
        // showToast(getString(R.string.Login_no_internet_connection))
    }

    fun showForceAllowPermissionDialog() {
        DialogUtil.showDialog(
            this,
            null,
            getString(R.string.Common_permission_msg),
            getString(R.string.Common_ok),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    PermissionPageUtil.intentToAppSettings(this@MVPBaseActivity)
                }

                override fun onCancelClick() {}
            },
            false,
        )
    }

    override fun showGenericErrorMessage() {
        showMessage()
    }

    override fun showDialog(message: String?) {
        showCustomDialog(message ?: getString(R.string.generic_error))
    }

    fun showDialog(
        message: String?,
        callback: (() -> Unit),
    ) {
        showCustomDialog(message ?: getString(R.string.generic_error), callback)
    }

    private fun showCustomDialog(
        message: String?,
        callback: (() -> Unit)? = null,
    ): AlertDialog {
        val msg =
            if (message?.isNotEmpty() == true && message.isNotBlank()) {
                message
            } else {
                getString(R.string.generic_error)
            }

        return DialogUtil.showDialog(
            this,
            null,
            msg,
            getString(R.string.Common_ok),
            null,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    callback?.invoke()
                }

                override fun onCancelClick() {
                }
            },
            true,
        )
    }

    fun showCustomMessage(
        message: String,
        yes: String,
        no: String,
        listener: OnCusDialogInterface,
    ) {
        DialogUtil.showDialog(this, null, message, yes, no, listener, false)
    }

    fun hideSoftKeyboard() {
        val inputMethodManager =
            getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.hideSoftInputFromWindow(currentFocus?.windowToken, 0)
        currentFocus?.clearFocus()
    }

    fun showSuccessToast(message: String?) {
        ToastUtil.show(this, message, R.drawable.toast_check)
    }

    fun showErrorToast(errorMessage: String?) {
        ToastUtil.show(this, errorMessage, R.drawable.toast_remove)
    }

    fun showNoNetworkToast(errorMessage: String?) {
        ToastUtil.show(this, errorMessage, R.drawable.ic_warning)
    }

    fun showRegularToast(message: String?) {
        ToastUtil.show(this, message, ToastUtil.NO_ICON)
    }

    override fun onNetworkStateChanged(
        isConnected: Boolean,
        isFromAPICheck: Boolean,
    ) {
        if (isConnected) {
            removeSnackBar()
        } else if (!isOfflineMode) {
            if (isFromAPICheck) {
                showNoNetworkDialog()
            } else {
                // Workaround because the snack bar comes in conflict with the network
                // error toast message to be displayed when SSL connection drops
                // TODO: 11/9/21 improve network status snack bar.
                showCustomSnack(R.drawable.ic_warning)
            }
        }
    }

    override fun dkAuthRequired(keyInfoId: String?) {
        DigitalMopKeyUtils.appendLog("DkAuthRequired KeuyInfo Id $keyInfoId")
        keyInfoId?.isNotEmpty()?.run {
            if (this && BiometryUtil.canAuthenticate(this@MVPBaseActivity)) {
                BiometryUtil.showBiometryDialog(
                    this@MVPBaseActivity,
                    getString(R.string.Biometry_dialog_title),
                    getString(R.string.Biometry_login_to_OneApp),
                    getString(R.string.Common_cancel),
                    object : BiometryUtil.BiometryCallback {
                        override fun onSuccess(signature: Signature?) {
                            DigitalMopKeyUtils.getInstance(this@MVPBaseActivity).activateRemoteAuth(
                                keyInfoId,
                            )
                        }

                        override fun onFailure() {
                        }

                        override fun onLockedFailure() {
                        }

                        override fun onCancelled() {
                        }

                        override fun onError(errorCode: Int) {
                        }
                    },
                )
            }
        }
    }

    /**
     * Part of the workaround involves not initializing the snackbar on DKChatActivity
     * however, this method can be invoked either way, which causes a crash.
     * As such, we are ensuring that the snackbar is initialized before performing operations on it
     * The same applies for showCustomSnack()
     */
    protected fun removeSnackBar() {
        if (!::snackBarView.isInitialized) return
        Handler(Looper.getMainLooper()).postDelayed({
            snackBarView.dismiss()
        }, 200)
    }

    private fun showCustomSnack(icons: Int) {
        if (!::snackBarView.isInitialized) {
            return
        }

        val layout = snackBarView.view
        layout.apply {
            findViewById<TextView>(com.google.android.material.R.id.snackbar_text).apply {
                setCompoundDrawablesWithIntrinsicBounds(icons, 0, 0, 0)
                compoundDrawablePadding = resources.getDimensionPixelOffset(R.dimen.lineSpace_big)
            }
        }
        val params = layout.layoutParams as FrameLayout.LayoutParams
        params.gravity = Gravity.TOP
        layout.layoutParams = params
        snackBarView.show()
    }

    override fun showUnsupportedDemoDialog() {
        DialogUtil.showDialog(
            this,
            null,
            getString(R.string.common_not_support_in_demo),
            getString(
                R.string.Common_ok,
            ),
        )
    }

    override fun showProgressDialog() {
        try {
            progressDialogFragment?.showNow(supportFragmentManager, "ProgressDialog")
        } catch (e: Exception) {
            LogTool.d("Progress Dialog Exception", e.toString())
        }
    }

    override fun hideProgressDialog() {
        try {
            progressDialogFragment?.dismiss()
        } catch (e: Exception) {
            LogTool.d("Progress Dialog Exception", e.toString())
        }
    }

    override fun setLexusGeneration(
        isLexus: Boolean,
        forceUpdate: Boolean,
    ) {
//        showLexus = isLexus
//        RxBus.get().post(VehicleSkinType(isLexus, forceUpdate))
    }

    override fun getActivityContext(): Context = this@MVPBaseActivity

    private fun onApplicationKilled() {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val taskList = activityManager.runningAppProcesses
        val list = taskList?.filter { it.processName == BuildConfig.APPLICATION_ID } ?: emptyList()
        if (list.isEmpty() || list.firstOrNull()?.importance != ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
            prepareForAutoLaunch()
            appData.appSessionActive = false
        }
    }

    private fun prepareForAutoLaunch() {
        // Auto-launch is set only whn the app is killed while a user is signed in
        if (!appData.appSessionActive) {
            return
        }

        val adapter = (getSystemService(BLUETOOTH_SERVICE) as BluetoothManager).adapter

        if (adapter.isEnabled && appData.hasAutoLaunchPermission) {
            WorkManager.getInstance(this).enqueue(AutoLaunchSetupWorker.newInstance())
        }
    }

    protected fun goToLogin(
        forLogout: Boolean = false,
        forBackPressed: Boolean = false,
    ) {
        val intent =
            Intent(this, FeatureFlagUtil.loginActivity)
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        if (forLogout) {
            intent.putExtras(
                Bundle().apply {
                    putBoolean(IDPHelper.KEY_LOGOUT, true)
                },
            )
        } else if (forBackPressed) {
            intent.putExtras(
                Bundle().apply {
                    putBoolean("IsBackPressed", true)
                },
            )
        }
        startActivityWithoutAnim(intent)
        finish()
    }

    protected fun forceUserToLogin() {
        val intent =
            Intent(this, LoginActivity::class.java)
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.putExtras(
            Bundle().apply {
                putBoolean(IDPHelper.KEY_SHOW_GENERIC_ERROR, true)
                putBoolean(IDPHelper.KEY_LOGOUT, true)
            },
        )
        startActivityWithoutAnim(intent)
        finish()
    }

    // TODO Check for other users of "go to dashboard"
    protected open fun goToDashboard() {
        startActivityWithoutAnim(IntentUtil.getOADashBoardIntent(context = this))
        finish()
    }

    protected open fun goToAddVehicle() {
        val intent =
            Intent(this, AddVehicleActivity::class.java)
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivityWithoutAnim(intent)
        finish()
    }

    open fun performLogout() {
        if (presenter != null) {
            presenter.logout()
        }
        WearUtil.sendToWear(
            WearConstants.BUS_OUTBOUND_PATH,
            WearConstants.BUS_OUTBOUND_DATA_KEY,
            WearResponse(
                WearAPIType.LOGOUT,
                null,
                null,
                getString(R.string.SmartWatch_Please_Login),
            ).toJsonString(),
        )
        goToLogin(forLogout = true)
    }
}
