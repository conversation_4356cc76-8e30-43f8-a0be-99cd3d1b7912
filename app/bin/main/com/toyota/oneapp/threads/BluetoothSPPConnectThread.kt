package com.toyota.oneapp.threads

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothServerSocket
import android.bluetooth.BluetoothSocket
import android.os.Build
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.ctp.v1.VehicleLogsService
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import io.grpc.stub.StreamObserver
import toyotaone.commonlib.log.LogTool
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.nio.ByteBuffer
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject

class BluetoothSPPConnectThread
    @Inject
    constructor(
        private val btAdapter: BluetoothAdapter,
        private val userProfileAPIManager: UserProfileAPIManager,
        private val analyticsLogger: AnalyticsLogger,
    ) : Thread() {
        companion object {
            // Constants that indicate the current connection state
            private const val STATE_NONE = 0 // we're doing nothing
            private const val STATE_LISTEN = 1 // now listening for incoming connections
            private const val STATE_CONNECTED = 2 // now connected to a remote device
            private const val MAX_COMMAND_SIZE = 6 // SPP commands don't have a payload
        }

        var currentState: AtomicInteger = AtomicInteger(STATE_NONE)
        private val toyotaAcceptThread: AcceptThread by lazy {
            AcceptThread(
                UUID.fromString(
                    ToyotaConstants.UP_GATT_SERVICE_UUID_TOYOTA,
                ),
            )
        }
        private val lexusAcceptThread: AcceptThread by lazy {
            AcceptThread(
                UUID.fromString(ToyotaConstants.UP_GATT_SERVICE_UUID_LEXUS),
            )
        }

        private val subaruAcceptThread: AcceptThread by lazy {
            AcceptThread(
                UUID.fromString(ToyotaConstants.UP_GATT_SERVICE_UUID_SUBARU),
            )
        }

        private var transferThread: TransferThread? = null

        override fun run() {
            name = "BluetoothSppConnectThread"
            startAcceptingConnections()
        }

        override fun destroy() {
            LogTool.i(ToyotaConstants.BLUETOOTH_TAG, "END $name")
        }

        @SuppressLint("MissingPermission") // Service only runs after permission check
        private inner class AcceptThread(
            uuid: UUID,
        ) : Thread() {
            private val brand =
                when (uuid.toString()) {
                    ToyotaConstants.UP_GATT_SERVICE_UUID_TOYOTA -> Brand.TOYOTA.brandName
                    ToyotaConstants.UP_GATT_SERVICE_UUID_LEXUS -> Brand.LEXUS.brandName
                    ToyotaConstants.UP_GATT_SERVICE_UUID_SUBARU -> Brand.SUBARU.brandName
                    else -> ToyotaConstants.EMPTY_STRING
                }

            private val serverSocket: BluetoothServerSocket? =
                try {
                    btAdapter.listenUsingRfcommWithServiceRecord("$brand User Profile", uuid)
                } catch (e: Exception) {
                    when (e) {
                        is IOException -> {
                            LogTool.e(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "Bluetooth not available, or insufficient permissions, or channel in use.",
                                e,
                            )
                        }
                        is SecurityException -> {
                            LogTool.e(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "Might not have permissions",
                                e,
                            )
                            // TODO: If Android 12, check and request Bluetooth runtime permissions (BLUETOOTH_CONNECT)
                        }
                        else -> {
                            LogTool.e(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "Unexpected exception",
                                e,
                            )
                        }
                    }
                    null
                }

            override fun run() {
                // Name of the Accept thread identified by brand
                name = "$brand AcceptThread"
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Waiting for $brand SPP connections")
                var shouldAcceptConnections = currentState.get() == STATE_LISTEN
                if (serverSocket == null) {
                    return
                }
                while (shouldAcceptConnections) {
                    val socket: BluetoothSocket? =
                        try {
                            serverSocket.accept()
                        } catch (e: Exception) {
                            LogTool.e(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "Could not accept $brand connection",
                                e,
                            )
                            when (e) {
                                is IOException -> {
                                    shouldAcceptConnections = false
                                    try {
                                        serverSocket.close()
                                    } catch (e: Exception) {
                                        LogTool.e(
                                            ToyotaConstants.BLUETOOTH_TAG,
                                            """Could not close the connect socket
                            | ${e.message} 
                            | ${e.cause}
                                            """.trimMargin(),
                                            e,
                                        )
                                    }
                                }
                                is SecurityException -> {
                                    LogTool.e(
                                        ToyotaConstants.BLUETOOTH_TAG,
                                        "Might not have permissions",
                                        e,
                                    )
                                    // TODO: If Android 12, check and request Bluetooth runtime permissions (BLUETOOTH_CONNECT)
                                }
                                else -> {
                                    LogTool.e(
                                        ToyotaConstants.BLUETOOTH_TAG,
                                        "Unexpected exception",
                                        e,
                                    )
                                }
                            }

                            null
                        }

                    socket?.run {
                        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "accepted $brand connection")
                        connected(this)
                    }

                    if (currentState.get() != STATE_LISTEN) {
                        shouldAcceptConnections = false
                        serverSocket.close()
                    }
                }
                LogTool.i(ToyotaConstants.BLUETOOTH_TAG, "END $name")
            }

            fun cancel() {
                try {
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Closing open $brand server socket")
                    serverSocket?.close()
                } catch (e: Exception) {
                    LogTool.e(
                        ToyotaConstants.BLUETOOTH_TAG,
                        """Could not close the connect socket
                    | ${e.message} 
                    | ${e.cause}
                        """.trimMargin(),
                        e,
                    )
                    when (e) {
                        is IOException -> {}
                        is SecurityException -> {
                            // TODO: If Android 12, check and request Bluetooth runtime permissions (BLUETOOTH_CONNECT)
                        }
                        else -> {
                            LogTool.e(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "Unexpected exception",
                                e,
                            )
                        }
                    }
                }
            }
        }

        // Thread for listening to incoming commands
        private inner class TransferThread(
            private val socket: BluetoothSocket,
        ) : Thread() {
            override fun run() {
                startRead()
            }

            @SuppressLint("MissingPermission") // Service only runs after permission check
            private fun startRead() {
                LogTool.d(
                    ToyotaConstants.BLUETOOTH_TAG,
                    """Connected
                |Started listening to SPP connection from device
                |address: ${socket.remoteDevice.address}
                |name: ${socket.remoteDevice.name}
                    """.trimMargin(),
                )
                while (STATE_CONNECTED == currentState.get()) {
                    val input =
                        try {
                            val bytes = ByteArray(MAX_COMMAND_SIZE)
                            socket.inputStream.read(bytes, 0, MAX_COMMAND_SIZE)
                            bytes
                        } catch (e: Exception) {
                            currentState.set(STATE_NONE)
                            when (e) {
                                is IOException -> {
                                    LogTool.e(
                                        ToyotaConstants.BLUETOOTH_TAG,
                                        "Connection lost",
                                        e,
                                    )
                                }
                                is SecurityException -> {
                                    LogTool.e(
                                        ToyotaConstants.BLUETOOTH_TAG,
                                        "Couldn't start read",
                                        e,
                                    )
                                    // TODO: If Android 12, check and request Bluetooth runtime permissions (BLUETOOTH_CONNECT)
                                }
                                else -> {
                                    LogTool.e(
                                        ToyotaConstants.BLUETOOTH_TAG,
                                        "Unexpected exception",
                                        e,
                                    )
                                }
                            }

                            ByteArray(0)
                        }

                    if (input.size == MAX_COMMAND_SIZE) {
                        handleCommand(input)
                    }
                }
                socket.close()
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Connection ended")
                startAcceptingConnections()
            }

            @Throws(IOException::class)
            private fun transferThroughSPP(packet: ByteArray) {
                LogTool.d(
                    ToyotaConstants.BLUETOOTH_TAG,
                    """SPP starting to write:
            |${ToyUtil.bytesToHexString(packet)}
                    """.trimMargin(),
                )
                sleep(200)
                try {
                    socket.outputStream.write(packet)
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Data written")
                    socket.outputStream.flush()
                } catch (e: Exception) {
                    LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Failed to write data", e)
                }
            }

            fun cancel() {
                try {
                    socket.inputStream?.let {
                        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Closing input stream")
                        socket.inputStream.close()
                        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Stream closed")
                    }
                    socket.outputStream?.let {
                        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Closing output stream")
                        socket.outputStream.close()
                        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Stream closed")
                    }

                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Closing socket")
                    socket.close()
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Socket closed")
                } catch (closeException: Exception) {
                    when (closeException) {
                        is IOException -> {
                            LogTool.d(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "Could not close the BT client socket",
                                closeException,
                            )
                        }
                        is SecurityException -> {
                            // TODO: If Android 12, check and request Bluetooth runtime permissions (BLUETOOTH_CONNECT)
                        }
                        else -> {
                            LogTool.e(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "Unexpected exception",
                                closeException,
                            )
                        }
                    }
                }
            }

            private fun handleCommand(input: ByteArray) {
                // Taking 5th and 6th byte. Size (first 4 bytes) and payload are not relevant to SPP
                val command = input.copyOfRange(4, 6)
                val commandString = "0x" + ToyUtil.bytesToHexString(command)
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Handling command: $commandString")
                when (commandString) {
                    ToyotaConstants.UP_COMMAND_PACKET_1 -> {
                        analyticsLogger.logEvent(AnalyticsEvent.BLUETOOTH_SEND_PACKET1_BT)
                        getBasicProfile(command, 1)
                    }

                    ToyotaConstants.UP_COMMAND_PACKET_2 -> {
                        analyticsLogger.logEvent(AnalyticsEvent.BLUETOOTH_SEND_PACKET2_BT)
                        getBasicProfile(command, 2)
                    }

                    ToyotaConstants.AD_COMMAND_REQUEST -> {
                        analyticsLogger.logEvent(AnalyticsEvent.BLUETOOTH_SEND_APP_DATA_REPORT_BT)
                        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "App Data requested")
                        sendAppData(command)
                    }

                    else -> {
                        LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Unknown Command: $commandString")
                    }
                }
            }

            private fun sendAppData(command: ByteArray) {
                val androidAttributes =
                    VehicleLogsService.SmartphoneInformation.AndroidAttributes
                        .newBuilder()
                        .setOsArchitecture(System.getProperty("os.arch"))
                        .setKernelName(System.getProperty("os.name"))
                        .setKernelVersion(System.getProperty("os.version"))
                        .setBuildVersion(Build.DISPLAY)
                        .setSmartphoneModel(Build.MODEL)
                        .setAndroidVersion(Build.VERSION.RELEASE)
                        .setSmartphoneManufacturer(Build.MANUFACTURER)
                        .setProduct(Build.PRODUCT)

                LogTool.i(
                    ToyotaConstants.BLUETOOTH_TAG,
                    """
                    Phone information collected
                    OS architecture : ${androidAttributes.osArchitecture} 
                    (Kernel) name : ${androidAttributes.kernelName}
                    (Kernel) version : ${androidAttributes.kernelVersion}
                    Build Version : ${androidAttributes.buildVersion}
                    Smartphone Model : ${androidAttributes.smartphoneModel}
                    Android Version : ${androidAttributes.androidVersion}
                    Phone Manufacturer : ${androidAttributes.smartphoneManufacturer}
                    Product : ${androidAttributes.product}
                    """.trimIndent(),
                )

                val appData =
                    VehicleLogsService.SmartphoneInformation
                        .newBuilder()
                        .setAndroidAttributes(androidAttributes)
                        .build()
                        .toByteArray()

                val payloadSize =
                    if (appData.isNotEmpty()) {
                        appData.size + 1
                    } else {
                        2
                    }
                val byteArrayOutputStream = ByteArrayOutputStream()
                // Adding payload size to packet
                byteArrayOutputStream.write(ByteBuffer.allocate(4).putInt(payloadSize).array())
                // Adding command to packet
                byteArrayOutputStream.write(command)
                // Adding error/success flag
                byteArrayOutputStream.write(if (appData.isNotEmpty()) 0x00 else 0xFF)
                // Adding data
                if (appData.isNotEmpty()) {
                    byteArrayOutputStream.write(appData) // Writes an array of bytes
                } else {
                    byteArrayOutputStream.write(0x01) // Writes a byte
                }
                transferThroughSPP(byteArrayOutputStream.toByteArray())
            }

            private fun getBasicProfile(
                command: ByteArray,
                packetId: Int,
            ) {
                LogTool.d(
                    ToyotaConstants.BLUETOOTH_TAG,
                    "Getting packet for command: 0x${ToyUtil.bytesToHexString(command)}",
                )

                if (packetId == 2 && userProfileAPIManager.hasCachedProfile()) {
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Using cached packet 2")
                    handlePayload(
                        command,
                        userProfileAPIManager.getCachedPacket2().toByteArray(),
                        packetId,
                    )
                    return
                }

                userProfileAPIManager.getBasicProfile(
                    object : StreamObserver<ProfileServiceServer.GetBaseProfileResponse> {
                        override fun onNext(value: ProfileServiceServer.GetBaseProfileResponse) {
                            // packet1 and packet 2
                            val payload = if (packetId == 1) value.packet1 else value.packet2

                            LogTool.d(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "packet $packetId: " +
                                    payload.toStringUtf8(),
                            )

                            handlePayload(command, payload.toByteArray(), packetId)
                        }

                        override fun onError(t: Throwable) {
                            LogTool.d(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "Failed to download profile: " +
                                    t.message,
                            )
                        }

                        override fun onCompleted() {}
                    },
                )
            }

            private fun handlePayload(
                command: ByteArray,
                payload: ByteArray?,
                packetId: Int,
            ) {
                if (payload == null) {
                    return
                }

                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Payload $packetId size : ${payload.size}")
                val byteArrayOutputStream = ByteArrayOutputStream()
                try {
                    byteArrayOutputStream.write(
                        ByteBuffer
                            .allocate(4)
                            .putInt(payload.size)
                            .array(),
                    )
                    byteArrayOutputStream.write(command)
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Packet $packetId header : ${ToyUtil.bytesToHexString(
                            byteArrayOutputStream.toByteArray(),
                        )}",
                    )
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Packet $packetId payload : ${ToyUtil.bytesToHexString(payload)}",
                    )
                    byteArrayOutputStream.write(payload)
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Packet $packetId: ${ToyUtil.bytesToHexString(
                            byteArrayOutputStream.toByteArray(),
                        )}",
                    )
                } catch (e: Exception) {
                    LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Failed to write output byte stream", e)
                }

                transferThroughSPP(byteArrayOutputStream.toByteArray())
            }
        }

        private fun startAcceptingConnections() {
            currentState.set(STATE_LISTEN)
            transferThread?.cancel()
            try {
                if (ToyUtil.isSubaru()) {
                    subaruAcceptThread.start()
                } else {
                    toyotaAcceptThread.start()
                    lexusAcceptThread.start()
                }
            } catch (e: Exception) {
                LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Failed to start accepting connections", e)
            }
        }

        private fun connected(socket: BluetoothSocket) {
            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Stopping acceptance of new connections")
            // Stop listening for connections
            try {
                if (ToyUtil.isSubaru()) {
                    subaruAcceptThread.cancel()
                } else {
                    toyotaAcceptThread.cancel()
                    lexusAcceptThread.cancel()
                }
            } catch (e: Exception) {
                if (e is SecurityException) {
                    LogTool.e(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Failed to stop accept threads",
                        e,
                    )
                    // TODO: If Android 12, check and request Bluetooth runtime permissions (BLUETOOTH_CONNECT)
                }
            }

            currentState.set(STATE_CONNECTED)
            transferThread?.cancel() // Stopping previous connection if it exists.
            // Start listening to input stream
            try {
                transferThread = TransferThread(socket)
                transferThread?.start()
            } catch (e: Exception) {
                if (e is SecurityException) {
                    LogTool.e(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Failed to stop accept threads",
                        e,
                    )
                    // TODO: If Android 12, check and request Bluetooth runtime permissions (BLUETOOTH_CONNECT)
                }
            }
        }

        fun closeSppConnection() {
            LogTool.i(ToyotaConstants.BLUETOOTH_TAG, "Cancelling BT threads")
            currentState.set(STATE_NONE)

            try {
                if (ToyUtil.isSubaru()) {
                    subaruAcceptThread.cancel()
                } else {
                    toyotaAcceptThread.cancel()
                    lexusAcceptThread.cancel()
                }
                transferThread?.cancel()
            } catch (e: Exception) {
                if (e is SecurityException) {
                    LogTool.e(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Failed to stop accept threads",
                        e,
                    )
                    // TODO: If Android 12, check and request Bluetooth runtime permissions (BLUETOOTH_CONNECT)
                }
            }
        }
    }
