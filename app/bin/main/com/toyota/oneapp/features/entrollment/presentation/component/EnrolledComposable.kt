/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.entrollment.presentation.component

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.DrawableImage
import com.toyota.oneapp.features.core.composable.OACallOut2TextViewResizable
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun EnrolledComposable(
    modifier: Modifier = Modifier,
    @StringRes textId: Int,
) {
    Row(modifier = modifier) {
        OACallOut2TextViewResizable(
            text = stringResource(textId),
            color = AppTheme.colors.primaryButton02,
        )
        DrawableImage(
            modifier =
                Modifier.padding(
                    top = 8.dp,
                    start = 4.dp,
                ),
            drawableId = R.drawable.ic_enrolled,
            contentDescriptionId = null,
        )
    }
}

@Preview
@Composable
private fun EnrolledComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
    ) {
        EnrolledComposable(textId = R.string.registered)
    }
}
