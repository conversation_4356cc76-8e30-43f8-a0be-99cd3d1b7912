/*
 *  Created by sudhan.ram on 22/10/24, 11:32 am
 *  Copyright (c) 2024 . All rights reserved.
 *  Last modified 22/10/24, 11:32 am
 *
 */

package com.toyota.oneapp.features.chargeschedule.util

import kotlinx.coroutines.flow.Flow

private const val API_TIMEOUT_SEC = 180

suspend fun <T> Flow<T?>.collectAndRetry(
    startTime: Long,
    onRetry: suspend () -> Unit,
    onTimeout: () -> Unit,
    onSuccess: (T) -> Unit,
) {
    collect { response ->
        if (response == null) {
            val currentTime = System.currentTimeMillis()
            val elapsedTime = (currentTime - startTime) / 1000
            if (elapsedTime < API_TIMEOUT_SEC) {
                onRetry()
            } else {
                onTimeout()
            }
        } else {
            onSuccess(response)
        }
    }
}
