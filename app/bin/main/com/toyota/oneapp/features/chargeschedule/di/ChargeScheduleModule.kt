package com.toyota.oneapp.features.chargeschedule.di

import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleLogic
import com.toyota.oneapp.features.chargeschedule.application.ChargeScheduleUseCase
import com.toyota.oneapp.features.chargeschedule.dataaccess.repository.ChargeScheduleDefaultRepo
import com.toyota.oneapp.features.chargeschedule.domain.repo.ChargeScheduleRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class ChargeScheduleModule {
    @Binds
    abstract fun bindChargeScheduleRepo(repo: ChargeScheduleDefaultRepo): ChargeScheduleRepo

    @Binds
    abstract fun bindChargeScheduleUseCase(logic: ChargeScheduleLogic): ChargeScheduleUseCase
}
