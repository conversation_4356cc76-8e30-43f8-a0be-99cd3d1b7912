/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model

sealed interface PlugAndChargeEnrollmentButtonsModel {
    val primaryButton: PlugAndChargeButtonModel.Primary
    val secondaryButton: PlugAndChargeButtonModel.Secondary?

    data class Single(
        override val primaryButton: PlugAndChargeButtonModel.Primary,
    ) : PlugAndChargeEnrollmentButtonsModel {
        override val secondaryButton: PlugAndChargeButtonModel.Secondary? = null
    }

    data class Dual(
        override val primaryButton: PlugAndChargeButtonModel.Primary,
        override val secondaryButton: PlugAndChargeButtonModel.Secondary,
    ) : PlugAndChargeEnrollmentButtonsModel
}
