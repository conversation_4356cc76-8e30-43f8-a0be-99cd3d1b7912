/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.repository

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus

interface PlugAndChargeEnrollmentRepository {
    suspend fun initiateEnrollment(): Result<Unit>

    suspend fun getStatusModel(): Result<PlugAndChargeEnrollmentStatus>
}
