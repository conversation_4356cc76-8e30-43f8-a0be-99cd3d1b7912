/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder

import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PlugAndChargeEnrollmentStateHolderImpl
    @Inject
    constructor() : PlugAndChargeEnrollmentStateHolder {
        private val mutableState: MutableStateFlow<PlugAndChargeEnrollmentState> =
            MutableStateFlow(PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn(false))

        override val state = mutableState.asStateFlow()

        override fun update(newState: PlugAndChargeEnrollmentState) {
            mutableState.update { newState }
        }
    }
