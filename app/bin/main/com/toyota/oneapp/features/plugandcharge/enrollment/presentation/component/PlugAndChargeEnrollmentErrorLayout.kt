/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.features.core.composable.image.RedStatusComposable
import com.toyota.oneapp.features.core.composable.spacer.VerticalSpacer
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentUiEvent
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeErrorModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState

@Composable
fun PlugAndChargeEnrollmentErrorLayout(
    modifier: Modifier = Modifier,
    model: PlugAndChargeErrorModel,
    onEvent: (PlugAndChargeEnrollmentUiEvent) -> Unit,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        RedStatusComposable()
        VerticalSpacer()
        PlugAndChargeEnrollmentErrorTextsComposable(
            titleId = model.titleId,
            descriptionId = model.descriptionId,
        )
        VerticalSpacer(size = 32)
        PlugAndChargeEnrollmentButtonsComposable(
            model = model.buttons,
            onEvent = onEvent,
        )
    }
}

@Composable
@Preview
private fun PlugAndChargeEnrollmentErrorContentComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode, modifier = Modifier.wrapContentSize()) {
        PlugAndChargeEnrollmentErrorLayout(
            model =
                PlugAndChargeEnrollmentState
                    .ContainsError
                    .PowerYourVehicleOn(false)
                    .errorModel,
            onEvent = {},
        )
    }
}
