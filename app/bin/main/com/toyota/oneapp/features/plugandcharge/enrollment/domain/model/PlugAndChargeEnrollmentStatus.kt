/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.model

sealed interface PlugAndChargeEnrollmentStatus {
    data class Enrolled(val isToggleOn: Boolean) : PlugAndChargeEnrollmentStatus

    sealed interface NotEnrolled : PlugAndChargeEnrollmentStatus {
        data class Incomplete(val progress: Int) : NotEnrolled

        data object NotStarted : NotEnrolled

        data object EnrollmentNotPossible : NotEnrolled
    }
}
