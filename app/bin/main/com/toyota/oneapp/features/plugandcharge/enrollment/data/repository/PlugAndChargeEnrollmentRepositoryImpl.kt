/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.data.repository

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.plugandcharge.enrollment.data.api.PlugAndChargeEnrollmentApi
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeStartEnrollmentRequestDto
import com.toyota.oneapp.features.plugandcharge.enrollment.data.mapper.PlugAndChargeEnrollmentStatusMapper
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.repository.PlugAndChargeEnrollmentRepository
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.NetworkStatus
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class PlugAndChargeEnrollmentRepositoryImpl
    @Inject
    constructor(
        errorParser: <PERSON>rror<PERSON>essageParser,
        ioContext: CoroutineContext,
        private val api: PlugAndChargeEnrollmentApi,
        private val statusMapper: PlugAndChargeEnrollmentStatusMapper,
        private val applicationData: ApplicationData,
    ) : BaseRepository(
            errorParser = errorParser,
            ioContext = ioContext,
        ),
        PlugAndChargeEnrollmentRepository {
        override suspend fun initiateEnrollment(): Result<Unit> {
            val vehicle = applicationData.getSelectedVehicle() ?: return getGenericFailure()
            val vin = vehicle.vin ?: return getGenericFailure()
            val region = vehicle.region ?: return getGenericFailure()

            val status =
                makeApiCall {
                    api.initiateEnrollment(
                        vin = vin,
                        region = region,
                        brand = vehicle.brand,
                        body =
                            PlugAndChargeStartEnrollmentRequestDto(
                                partners = listOf(TESLA_PARTNER),
                            ),
                    )
                }.status
            return if (status == NetworkStatus.SUCCESS) {
                Result.success(Unit)
            } else {
                getGenericFailure()
            }
        }

        override suspend fun getStatusModel(): Result<PlugAndChargeEnrollmentStatus> {
            val vin =
                applicationData.getSelectedVehicle()?.vin
                    ?: return getGenericFailure()

            return makeApiCall {
                api.getPlugAndChargeEnrollmentStatus(vin)
            }.data?.payload?.let { dto ->
                Result.success(statusMapper.map(dto))
            } ?: getGenericFailure()
        }

        private fun <T> getGenericFailure(): Result<T> = Result.failure(Exception())

        companion object {
            private const val TESLA_PARTNER = "Tesla"
        }
    }
