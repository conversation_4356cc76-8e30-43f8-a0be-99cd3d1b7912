/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model

import androidx.annotation.StringRes

sealed interface PlugAndChargeButtonModel {
    val textId: Int

    data class Primary(
        @StringRes override val textId: Int,
        val uiEvent: PlugAndChargeEnrollmentUiEvent?,
    ) : PlugAndChargeButtonModel

    data class Secondary(
        @StringRes override val textId: Int,
        val uiEvent: PlugAndChargeEnrollmentUiEvent,
    ) : PlugAndChargeButtonModel
}
