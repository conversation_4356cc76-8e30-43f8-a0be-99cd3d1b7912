/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component

import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.composable.CircleLoading
import com.toyota.oneapp.features.core.composable.image.GreenCheckComposable
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeInstallationProgress

private const val CONTENT_SIZE = 96
private const val LOADING_ANIMATION_DURATION_MILLIS = 1000

@Composable
fun PlugAndChargeInstallationProgressComposable(progress: PlugAndChargeInstallationProgress) {
    val size = CONTENT_SIZE.dp
    when (progress) {
        PlugAndChargeInstallationProgress.IN_PROGRESS ->
            CircleLoading(
                indicatorSize = size,
                animationDuration = LOADING_ANIMATION_DURATION_MILLIS,
            )

        PlugAndChargeInstallationProgress.DONE ->
            GreenCheckComposable(
                modifier = Modifier.size(size),
            )
    }
}

@Preview
@Composable
private fun PlugAndChargeInstallationProgressPreview(
    @PreviewParameter(PlugAndChargePreviewParameterProvider::class)
    param: PlugAndChargePreviewParameter,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = param.themeMode,
    ) {
        PlugAndChargeInstallationProgressComposable(progress = param.progress)
    }
}

data class PlugAndChargePreviewParameter(
    val themeMode: ThemeMode,
    val progress: PlugAndChargeInstallationProgress,
)

private class PlugAndChargePreviewParameterProvider :
    PreviewParameterProvider<PlugAndChargePreviewParameter> {
    override val values =
        sequenceOf(
            PlugAndChargePreviewParameter(
                themeMode = ThemeMode.Light,
                progress = PlugAndChargeInstallationProgress.IN_PROGRESS,
            ),
            PlugAndChargePreviewParameter(
                themeMode = ThemeMode.Dark,
                progress = PlugAndChargeInstallationProgress.IN_PROGRESS,
            ),
            PlugAndChargePreviewParameter(
                themeMode = ThemeMode.Light,
                progress = PlugAndChargeInstallationProgress.DONE,
            ),
            PlugAndChargePreviewParameter(
                themeMode = ThemeMode.Dark,
                progress = PlugAndChargeInstallationProgress.DONE,
            ),
        )
}
