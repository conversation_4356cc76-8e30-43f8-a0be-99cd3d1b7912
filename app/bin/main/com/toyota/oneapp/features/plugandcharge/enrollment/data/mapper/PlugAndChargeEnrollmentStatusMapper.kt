/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.data.mapper

import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeEnrollmentStatusDto
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus

fun interface PlugAndChargeEnrollmentStatusMapper {
    fun map(dto: PlugAndChargeEnrollmentStatusDto): PlugAndChargeEnrollmentStatus
}
