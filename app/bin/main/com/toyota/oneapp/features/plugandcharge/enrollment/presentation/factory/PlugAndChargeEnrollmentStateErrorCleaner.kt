/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.factory

import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState

fun interface PlugAndChargeEnrollmentStateErrorCleaner {
    operator fun invoke(state: PlugAndChargeEnrollmentState): PlugAndChargeEnrollmentState
}
