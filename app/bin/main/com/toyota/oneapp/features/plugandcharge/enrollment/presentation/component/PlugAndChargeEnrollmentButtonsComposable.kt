/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.features.core.composable.OAButton
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.spacer.VerticalSpacer
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentButtonsModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentUiEvent
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState

@Composable
fun PlugAndChargeEnrollmentButtonsComposable(
    modifier: Modifier = Modifier,
    model: PlugAndChargeEnrollmentButtonsModel,
    onEvent: (PlugAndChargeEnrollmentUiEvent) -> Unit,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        model.run {
            secondaryButton?.let { safeSecondaryButton ->
                OAButtonTextView(
                    modifier = Modifier.clickable { onEvent(safeSecondaryButton.uiEvent) },
                    text = stringResource(safeSecondaryButton.textId),
                    color = AppTheme.colors.tertiary03,
                )
                VerticalSpacer()
            }
            OAButton(
                text = stringResource(primaryButton.textId),
                click = { primaryButton.uiEvent?.let(onEvent) },
            )
        }
    }
}

@Composable
@Preview
private fun PlugAndChargeEnrollmentButtonsComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        PlugAndChargeEnrollmentButtonsComposable(
            model = PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn(false).buttonsModel,
            onEvent = {},
        )
    }
}
