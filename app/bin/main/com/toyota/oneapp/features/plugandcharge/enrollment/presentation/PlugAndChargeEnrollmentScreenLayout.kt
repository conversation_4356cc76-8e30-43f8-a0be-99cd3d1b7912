/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OAFullScreenBottomSheetLayout
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component.PlugAndChargeEnrollmentButtonsComposable
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component.PlugAndChargeEnrollmentProgressContentComposable
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentButtonsModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentProgressModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentUiEvent
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState

private const val SPACE_HEIGHT_PERCENT = 0.15f

@Composable
fun PlugAndChargeEnrollmentScreenLayout(
    progressModel: PlugAndChargeEnrollmentProgressModel,
    buttonsModel: PlugAndChargeEnrollmentButtonsModel,
    onEvent: (PlugAndChargeEnrollmentUiEvent) -> Unit,
) {
    val backgroundColor = AppTheme.colors.tertiary15
    val maxSizeModifier = Modifier.fillMaxSize()
    OAFullScreenBottomSheetLayout(
        modifier =
            maxSizeModifier.padding(
                vertical = 8.dp,
                horizontal = 16.dp,
            ),
        backgroundColor = backgroundColor,
        testTagId = AccessibilityId.PLUG_AND_CHARGE_ENROLLMENT,
        onBack = {
            onEvent(PlugAndChargeEnrollmentUiEvent.ON_ARROW_BACK_CLICK)
        },
        screenTitle = stringResource(R.string.plug_and_charge_installation),
    ) {
        Box(modifier = maxSizeModifier.padding(bottom = 8.dp)) {
            Column(modifier = Modifier.align(Alignment.TopCenter)) {
                Spacer(modifier = Modifier.fillMaxHeight(SPACE_HEIGHT_PERCENT))
                PlugAndChargeEnrollmentProgressContentComposable(
                    model = progressModel,
                )
            }
            PlugAndChargeEnrollmentButtonsComposable(
                modifier = Modifier.align(Alignment.BottomCenter),
                model = buttonsModel,
                onEvent = onEvent,
            )
        }
    }
}

@Preview
@Composable
private fun PlugAndChargeEnrollmentScreenPreview(
    @PreviewParameter(PlugAndChargeEnrollmentPreviewParamProvider::class)
    param: PlugAndChargeEnrollmentPreviewParam,
) {
    ContentPreview(
        themeMode = param.themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        PlugAndChargeEnrollmentScreenLayout(
            progressModel = param.state.progressModel,
            buttonsModel = param.state.buttonsModel,
            onEvent = {},
        )
    }
}

data class PlugAndChargeEnrollmentPreviewParam(
    val themeMode: ThemeMode,
    val state: PlugAndChargeEnrollmentState,
)

private class PlugAndChargeEnrollmentPreviewParamProvider :
    PreviewParameterProvider<PlugAndChargeEnrollmentPreviewParam> {
    override val values =
        sequenceOf(
            PlugAndChargeEnrollmentPreviewParam(
                ThemeMode.Light,
                PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn(isShowingError = false),
            ),
            PlugAndChargeEnrollmentPreviewParam(
                ThemeMode.Dark,
                PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn(isShowingError = false),
            ),
            PlugAndChargeEnrollmentPreviewParam(
                ThemeMode.Light,
                PlugAndChargeEnrollmentState.ContainsError.ConnectingToVehicle(isShowingError = false),
            ),
            PlugAndChargeEnrollmentPreviewParam(
                ThemeMode.Dark,
                PlugAndChargeEnrollmentState.ContainsError.ConnectingToVehicle(isShowingError = false),
            ),
            PlugAndChargeEnrollmentPreviewParam(
                ThemeMode.Light,
                PlugAndChargeEnrollmentState.ContainsError.ActivatingPlugAndCharge(isShowingError = false),
            ),
            PlugAndChargeEnrollmentPreviewParam(
                ThemeMode.Dark,
                PlugAndChargeEnrollmentState.ContainsError.ActivatingPlugAndCharge(isShowingError = false),
            ),
            PlugAndChargeEnrollmentPreviewParam(
                ThemeMode.Light,
                PlugAndChargeEnrollmentState.InstallationComplete,
            ),
            PlugAndChargeEnrollmentPreviewParam(
                ThemeMode.Dark,
                PlugAndChargeEnrollmentState.InstallationComplete,
            ),
        )
}
