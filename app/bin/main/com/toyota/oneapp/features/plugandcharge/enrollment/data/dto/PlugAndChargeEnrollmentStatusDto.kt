/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.data.dto

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.certificate.CertificateDetailsDto

data class PlugAndChargeEnrollmentStatusDto(
    @SerializedName("toggle_status") val toggleStatus: Boolean?,
    @SerializedName("cert_details") val certificates: List<CertificateDetailsDto>?,
)
