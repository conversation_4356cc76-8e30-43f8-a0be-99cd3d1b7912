/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.repository.PlugAndChargeEnrollmentRepository
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeEnrollmentStatusUseCase
import javax.inject.Inject

class GetPlugAndChargeEnrollmentStatusLogic
    @Inject
    constructor(
        private val repository: PlugAndChargeEnrollmentRepository,
    ) : GetPlugAndChargeEnrollmentStatusUseCase {
        override suspend fun invoke(): Result<PlugAndChargeEnrollmentStatus> = repository.getStatusModel()
    }
