/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.core.ApplicationScope
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.factory.PlugAndChargeEnrollmentStateErrorCleaner
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentUiAction
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentUiEvent
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.StartPlugAndChargeEnrollmentProcess
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolder
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class PlugAndChargeEnrollmentViewModel
    @Inject
    constructor(
        private val startPlugAndChargeEnrollment: StartPlugAndChargeEnrollmentProcess,
        private val getStateWithoutError: PlugAndChargeEnrollmentStateErrorCleaner,
        private val dispatcherProvider: DispatcherProvider,
        private val stateHolder: PlugAndChargeEnrollmentStateHolder,
        @ApplicationScope private val applicationCoroutineScope: CoroutineScope,
    ) : BaseViewModel<Unit, PlugAndChargeEnrollmentUiEvent>() {
        val enrollmentState: StateFlow<PlugAndChargeEnrollmentState> = stateHolder.state

        private val mutableUiAction = Channel<PlugAndChargeEnrollmentUiAction>()
        val uiAction = mutableUiAction.receiveAsFlow()

        override fun defaultState(): Unit = Unit

        override fun onEvent(event: PlugAndChargeEnrollmentUiEvent) {
            when (event) {
                PlugAndChargeEnrollmentUiEvent.ON_CONFIRM_ENROLLMENT_CLICK -> startEnrollment()
                PlugAndChargeEnrollmentUiEvent.ON_CONFIRM_SKIP_CLICK ->
                    closeErrorBottomSheet(shouldEmitBack = true)

                PlugAndChargeEnrollmentUiEvent.ON_RETRY_CONFIRM_CLICK ->
                    closeErrorBottomSheet(shouldStartEnrollment = true)

                PlugAndChargeEnrollmentUiEvent.ON_SKIP_CLICK ->
                    stateHolder.update(PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn(true))

                PlugAndChargeEnrollmentUiEvent.ON_CANCEL_SKIP_CLICK -> closeErrorBottomSheet()

                PlugAndChargeEnrollmentUiEvent.ON_BOTTOM_SHEET_ERROR_DISMISS,
                PlugAndChargeEnrollmentUiEvent.ON_CLOSE_PLUG_AND_CHARGE_CLICK,
                PlugAndChargeEnrollmentUiEvent.ON_SYSTEM_BACK_CLICK,
                PlugAndChargeEnrollmentUiEvent.ON_ARROW_BACK_CLICK,
                -> handleBack()
            }
        }

        private fun startEnrollment() {
            applicationCoroutineScope.launch {
                startPlugAndChargeEnrollment()
            }
        }

        private fun handleBack() {
            when (val currentState = enrollmentState.value) {
                is PlugAndChargeEnrollmentState.ContainsError -> handleErrorBack(currentState)
                PlugAndChargeEnrollmentState.InstallationComplete -> {
                    close()
                    emitBackToPreviousScreen()
                }
            }
        }

        private fun handleErrorBack(currentState: PlugAndChargeEnrollmentState.ContainsError) {
            if (currentState.isShowingError) {
                if (currentState is PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn) {
                    closeErrorBottomSheet()
                } else {
                    closeErrorBottomSheet(shouldEmitBack = true)
                }
            } else {
                emitBackToPreviousScreen()
            }
            close()
        }

        private fun close() {
            delayThenExecute((BOTTOM_SHEET_DISAPPEAR_ANIMATION_TIME_MILLIS * 1.5).toLong())
        }

        private fun closeErrorBottomSheet(
            shouldEmitBack: Boolean = false,
            shouldStartEnrollment: Boolean = false,
        ) {
            viewModelScope.launch {
                mutableUiAction.send(PlugAndChargeEnrollmentUiAction.CLOSE_ERROR_BOTTOM_SHEET)
                delayThenExecute(BOTTOM_SHEET_DISAPPEAR_ANIMATION_TIME_MILLIS) {
                    stateHolder.update(getStateWithoutError(enrollmentState.value))
                    when {
                        shouldStartEnrollment -> startEnrollment()
                        shouldEmitBack -> emitBackToPreviousScreen()
                    }
                }
            }
        }

        private fun delayThenExecute(
            delayTime: Long,
            action: () -> Unit = {},
        ) {
            viewModelScope.launch(dispatcherProvider.default()) {
                delay(delayTime)
                action()
            }
        }

        private fun emitBackToPreviousScreen() {
            viewModelScope.launch {
                mutableUiAction.send(PlugAndChargeEnrollmentUiAction.BACK_TO_PREVIOUS_SCREEN)
            }
        }

        companion object {
            private const val BOTTOM_SHEET_DISAPPEAR_ANIMATION_TIME_MILLIS = 200L
        }
    }
