/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentUiEvent
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeErrorModel

@Composable
@OptIn(ExperimentalMaterial3Api::class)
fun PlugAndChargeEnrollmentErrorBottomSheet(
    sheetState: SheetState,
    errorModel: PlugAndChargeErrorModel,
    onEvent: (PlugAndChargeEnrollmentUiEvent) -> Unit,
) {
    ModalBottomSheet(
        containerColor = AppTheme.colors.tile03,
        sheetState = sheetState,
        dragHandle = {},
        onDismissRequest = {
            onEvent(PlugAndChargeEnrollmentUiEvent.ON_BOTTOM_SHEET_ERROR_DISMISS)
        },
    ) {
        PlugAndChargeEnrollmentErrorLayout(
            modifier = Modifier.padding(24.dp),
            model = errorModel,
            onEvent = onEvent,
        )
    }
}
