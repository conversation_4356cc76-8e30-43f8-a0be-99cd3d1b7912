/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.factory

import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import javax.inject.Inject

class PlugAndChargeEnrollmentStateErrorCleanerImpl
    @Inject
    constructor() :
    PlugAndChargeEnrollmentStateErrorCleaner {
        override fun invoke(state: PlugAndChargeEnrollmentState): PlugAndChargeEnrollmentState = state.withoutError()

        private fun PlugAndChargeEnrollmentState.withoutError(): PlugAndChargeEnrollmentState =
            when (this) {
                is PlugAndChargeEnrollmentState.ContainsError.ActivatingPlugAndCharge ->
                    copy(isShowingError = false)

                is PlugAndChargeEnrollmentState.ContainsError.ConnectingToVehicle ->
                    copy(isShowingError = false)
                is PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn ->
                    copy(isShowingError = false)
                is PlugAndChargeEnrollmentState.InstallationComplete -> this
            }
    }
