/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.data.mapper.impl

import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeEnrollmentStatusDto
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.certificate.CertificateDetailsDto
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.certificate.CertificateTypeDto
import com.toyota.oneapp.features.plugandcharge.enrollment.data.mapper.PlugAndChargeEnrollmentStatusMapper
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import javax.inject.Inject

class PlugAndChargeEnrollmentStatusMapperImpl
    @Inject
    constructor() : PlugAndChargeEnrollmentStatusMapper {
        override fun map(dto: PlugAndChargeEnrollmentStatusDto): PlugAndChargeEnrollmentStatus =
            dto.run {
                certificates
                    ?.findType(CertificateTypeDto.PROVISION)
                    ?.let { provisionCert ->
                        val isProvisionCertActive = provisionCert.isActive ?: false
                        if (!isProvisionCertActive) {
                            PlugAndChargeEnrollmentStatus.NotEnrolled.EnrollmentNotPossible
                        } else {
                            certificates
                                .findType(CertificateTypeDto.CONTRACT)
                                ?.toEnrollmentContractStatus(toggleStatus)
                        }
                    } ?: PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted
            }

        private fun CertificateDetailsDto.toEnrollmentContractStatus(toggleStatus: Boolean?): PlugAndChargeEnrollmentStatus =
            if (isActive == true) {
                PlugAndChargeEnrollmentStatus.Enrolled(
                    isToggleOn = toggleStatus ?: false,
                )
            } else {
                PlugAndChargeEnrollmentStatus.NotEnrolled.Incomplete(
                    progress = progress ?: 0,
                )
            }

        private fun List<CertificateDetailsDto>.findType(type: CertificateTypeDto): CertificateDetailsDto? = find { it.type == type }
    }
