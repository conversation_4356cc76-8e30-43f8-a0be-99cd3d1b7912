/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.spacer.VerticalSpacer
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun PlugAndChargeEnrollmentErrorTextsComposable(
    modifier: Modifier = Modifier,
    @StringRes titleId: Int,
    @StringRes descriptionId: Int,
) {
    Column(modifier = modifier) {
        OASubHeadLine1TextView(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(titleId),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary00,
        )
        VerticalSpacer(size = 8)
        OACallOut1TextView(
            text = stringResource(descriptionId),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary05,
        )
    }
}

@Composable
@Preview
private fun PlugAndChargeEnrollmentErrorTextsPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        PlugAndChargeEnrollmentErrorTextsComposable(
            titleId = R.string.pnc_enrollment_connection_error_title,
            descriptionId = R.string.pnc_enrollment_connection_error_description,
        )
    }
}
