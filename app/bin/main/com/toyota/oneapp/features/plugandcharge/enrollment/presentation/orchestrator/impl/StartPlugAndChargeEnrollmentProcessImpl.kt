/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.impl

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeEnrollmentStatusUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.PostPlugAndChargeEnrollmentUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.StartPlugAndChargeEnrollmentProcess
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolder
import javax.inject.Inject

class StartPlugAndChargeEnrollmentProcessImpl
    @Inject
    constructor(
        private val postPlugAndChargeEnrollment: PostPlugAndChargeEnrollmentUseCase,
        private val getPlugAndChargeEnrollmentStatus: GetPlugAndChargeEnrollmentStatusUseCase,
        private val stateHolder: PlugAndChargeEnrollmentStateHolder,
    ) : StartPlugAndChargeEnrollmentProcess {
        private val defaultConnectingToVehicleState =
            PlugAndChargeEnrollmentState
                .ContainsError
                .ConnectingToVehicle(false)

        private val defaultActivatingPlugAndCharge =
            PlugAndChargeEnrollmentState
                .ContainsError
                .ActivatingPlugAndCharge(false)

        override suspend fun invoke() {
            stateHolder.run {
                update(defaultConnectingToVehicleState)
                postPlugAndChargeEnrollment().onSuccess {
                    update(defaultActivatingPlugAndCharge)
                    getPlugAndChargeEnrollmentStatus().onSuccess {
                        update(PlugAndChargeEnrollmentState.InstallationComplete)
                    }.onFailure {
                        update(defaultActivatingPlugAndCharge.copy(isShowingError = true))
                    }
                }.onFailure {
                    update(defaultConnectingToVehicleState.copy(isShowingError = true))
                }
            }
        }
    }
