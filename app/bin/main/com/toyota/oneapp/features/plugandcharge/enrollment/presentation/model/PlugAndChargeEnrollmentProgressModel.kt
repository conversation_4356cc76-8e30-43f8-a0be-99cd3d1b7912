/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model

import androidx.annotation.StringRes
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeInstallationProgress

data class PlugAndChargeEnrollmentProgressModel(
    @StringRes val titleId: Int,
    @StringRes val descriptionId: Int,
    val installationProgress: PlugAndChargeInstallationProgress?,
)
