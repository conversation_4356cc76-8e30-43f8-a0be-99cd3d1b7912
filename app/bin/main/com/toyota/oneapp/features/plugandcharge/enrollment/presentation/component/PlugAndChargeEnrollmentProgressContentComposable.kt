/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.spacer.VerticalSpacer
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeInstallationProgress
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentProgressModel

@Composable
fun PlugAndChargeEnrollmentProgressContentComposable(
    modifier: Modifier = Modifier,
    model: PlugAndChargeEnrollmentProgressModel,
) {
    model.run {
        Column(
            modifier = modifier,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            PlugAndChargeEnrollmentProgressTitleLineComposable(titleId)
            installationProgress?.let {
                VerticalSpacer(size = 48)
                PlugAndChargeInstallationProgressComposable(it)
            }
            VerticalSpacer(size = 56)
            Text(
                text = stringResource(descriptionId),
                color = AppTheme.colors.tertiary00,
                textAlign = TextAlign.Center,
                style = AppTheme.fontStyles.body3,
            )
        }
    }
}

@Composable
@Preview
private fun PlugAndChargeProgressContentComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        PlugAndChargeEnrollmentProgressContentComposable(
            model =
                PlugAndChargeEnrollmentProgressModel(
                    titleId = R.string.pnc_enrollment_activating_title,
                    descriptionId = R.string.pnc_enrollment_connecting_description,
                    installationProgress = PlugAndChargeInstallationProgress.IN_PROGRESS,
                ),
        )
    }
}
