/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder

import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import kotlinx.coroutines.flow.StateFlow

interface PlugAndChargeEnrollmentStateHolder {
    val state: StateFlow<PlugAndChargeEnrollmentState>

    fun update(newState: PlugAndChargeEnrollmentState)
}
