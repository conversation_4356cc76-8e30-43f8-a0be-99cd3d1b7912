/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.di

import com.toyota.oneapp.features.plugandcharge.enrollment.data.mapper.PlugAndChargeEnrollmentStatusMapper
import com.toyota.oneapp.features.plugandcharge.enrollment.data.mapper.impl.PlugAndChargeEnrollmentStatusMapperImpl
import com.toyota.oneapp.features.plugandcharge.enrollment.data.repository.PlugAndChargeEnrollmentRepositoryImpl
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic.GetPlugAndChargeEnrollmentStatusLogic
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic.PostPlugAndChargeEnrollmentLogic
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.repository.PlugAndChargeEnrollmentRepository
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeEnrollmentStatusUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.PostPlugAndChargeEnrollmentUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.factory.PlugAndChargeEnrollmentStateErrorCleaner
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.factory.PlugAndChargeEnrollmentStateErrorCleanerImpl
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.StartPlugAndChargeEnrollmentProcess
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.orchestrator.impl.StartPlugAndChargeEnrollmentProcessImpl
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolder
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolderImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
interface PlugAndChargeEnrollmentModule {
    @Binds
    fun bindPostPlugAndChargeEnrollmentUseCase(
        postPlugAndChargeEnrollmentLogic: PostPlugAndChargeEnrollmentLogic,
    ): PostPlugAndChargeEnrollmentUseCase

    @Binds
    fun bindGetPlugAndChargeEnrollmentStatusUseCase(
        getPlugAndChargeEnrollmentStatusLogic: GetPlugAndChargeEnrollmentStatusLogic,
    ): GetPlugAndChargeEnrollmentStatusUseCase

    @Binds
    fun bindPlugAndChargeEnrollmentRepository(
        plugAndChargeEnrollmentRepositoryImpl: PlugAndChargeEnrollmentRepositoryImpl,
    ): PlugAndChargeEnrollmentRepository

    @Binds
    fun bindPlugAndChargeEnrollmentStatusMapper(
        plugAndChargeEnrollmentStatusMapperImpl: PlugAndChargeEnrollmentStatusMapperImpl,
    ): PlugAndChargeEnrollmentStatusMapper

    @Binds
    fun bindStartPlugAndChargeEnrollmentProcess(
        startPlugAndChargeEnrollmentProcessImpl: StartPlugAndChargeEnrollmentProcessImpl,
    ): StartPlugAndChargeEnrollmentProcess

    @Binds
    fun bindPlugAndChargeEnrollmentStateErrorCleaner(
        plugAndChargeEnrollmentStateErrorCleanerImpl: PlugAndChargeEnrollmentStateErrorCleanerImpl,
    ): PlugAndChargeEnrollmentStateErrorCleaner

    @Binds
    fun bindPlugAndChargeEnrollmentStateHolder(
        plugAndChargeEnrollmentStateHolderImpl: PlugAndChargeEnrollmentStateHolderImpl,
    ): PlugAndChargeEnrollmentStateHolder
}
