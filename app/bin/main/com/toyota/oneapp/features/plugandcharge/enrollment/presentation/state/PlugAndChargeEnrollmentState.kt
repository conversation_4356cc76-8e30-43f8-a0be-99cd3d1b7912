/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state

import com.toyota.oneapp.R
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeInstallationProgress
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeButtonModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentButtonsModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentProgressModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentUiEvent
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeErrorModel

sealed class PlugAndChargeEnrollmentState(
    val progressModel: PlugAndChargeEnrollmentProgressModel,
    val buttonsModel: PlugAndChargeEnrollmentButtonsModel,
) {
    sealed class ContainsError(
        open val isShowingError: Boolean,
        val errorModel: PlugAndChargeErrorModel,
        progressModel: PlugAndChargeEnrollmentProgressModel,
        buttonsModel: PlugAndChargeEnrollmentButtonsModel,
    ) : PlugAndChargeEnrollmentState(progressModel, buttonsModel) {
        data class PowerYourVehicleOn(override val isShowingError: Boolean) : ContainsError(
            progressModel = powerYourVehicleOnProgressModel,
            buttonsModel = powerYourVehicleOnButtonsModel,
            errorModel = powerYourVehicleOnErrorModel,
            isShowingError = isShowingError,
        )

        data class ConnectingToVehicle(override val isShowingError: Boolean) : ContainsError(
            progressModel = connectingToVehicleProgressModel,
            buttonsModel = connectingToVehicleButtonsModel,
            errorModel = connectingToVehicleErrorModel,
            isShowingError = isShowingError,
        )

        data class ActivatingPlugAndCharge(override val isShowingError: Boolean) : ContainsError(
            progressModel = activatingPlugAndChargeProgressModel,
            buttonsModel = activatingPlugAndChargeButtonsModel,
            errorModel = connectingToVehicleErrorModel,
            isShowingError = isShowingError,
        )
    }

    data object InstallationComplete : PlugAndChargeEnrollmentState(
        progressModel = installationCompleteProgressModel,
        buttonsModel = installationCompleteButtonsModel,
    )
}

private val powerYourVehicleOnProgressModel =
    PlugAndChargeEnrollmentProgressModel(
        titleId = R.string.pnc_enrollment_power_vehicle_title,
        descriptionId = R.string.pnc_enrollment_power_vehicle_description,
        installationProgress = null,
    )

private val powerYourVehicleOnButtonsModel =
    PlugAndChargeEnrollmentButtonsModel.Dual(
        primaryButton =
            PlugAndChargeButtonModel.Primary(
                textId = R.string.Common_confirm,
                uiEvent = PlugAndChargeEnrollmentUiEvent.ON_CONFIRM_ENROLLMENT_CLICK,
            ),
        secondaryButton =
            PlugAndChargeButtonModel.Secondary(
                textId = R.string.evgo_skip_cta,
                uiEvent = PlugAndChargeEnrollmentUiEvent.ON_SKIP_CLICK,
            ),
    )

private val powerYourVehicleOnErrorModel =
    PlugAndChargeErrorModel(
        titleId = R.string.pnc_enrollment_skip_installation_error_title,
        descriptionId = R.string.pnc_enrollment_skip_installation_error_description,
        buttons =
            PlugAndChargeEnrollmentButtonsModel.Dual(
                secondaryButton =
                    PlugAndChargeButtonModel.Secondary(
                        textId = R.string.Common_cancel,
                        uiEvent = PlugAndChargeEnrollmentUiEvent.ON_CANCEL_SKIP_CLICK,
                    ),
                primaryButton =
                    PlugAndChargeButtonModel.Primary(
                        textId = R.string.Common_confirm,
                        uiEvent = PlugAndChargeEnrollmentUiEvent.ON_CONFIRM_SKIP_CLICK,
                    ),
            ),
    )

private val connectingToVehicleProgressModel =
    PlugAndChargeEnrollmentProgressModel(
        titleId = R.string.pnc_enrollment_connecting_title,
        descriptionId = R.string.pnc_enrollment_connecting_description,
        installationProgress = PlugAndChargeInstallationProgress.IN_PROGRESS,
    )

private val connectingToVehicleErrorModel =
    PlugAndChargeErrorModel(
        titleId = R.string.pnc_enrollment_connection_error_title,
        descriptionId = R.string.pnc_enrollment_connection_error_description,
        buttons =
            PlugAndChargeEnrollmentButtonsModel.Dual(
                primaryButton =
                    PlugAndChargeButtonModel.Primary(
                        textId = R.string.error_retry,
                        uiEvent = PlugAndChargeEnrollmentUiEvent.ON_RETRY_CONFIRM_CLICK,
                    ),
                secondaryButton =
                    PlugAndChargeButtonModel.Secondary(
                        textId = R.string.Common_cancel,
                        uiEvent = PlugAndChargeEnrollmentUiEvent.ON_BOTTOM_SHEET_ERROR_DISMISS,
                    ),
            ),
    )

private val connectingToVehicleButtonsModel =
    PlugAndChargeEnrollmentButtonsModel.Single(
        primaryButton =
            PlugAndChargeButtonModel.Primary(
                textId = R.string.Common_confirm,
                uiEvent = null,
            ),
    )

private val activatingPlugAndChargeProgressModel =
    PlugAndChargeEnrollmentProgressModel(
        titleId = R.string.pnc_enrollment_activating_title,
        descriptionId = R.string.pnc_enrollment_connecting_description,
        installationProgress = PlugAndChargeInstallationProgress.IN_PROGRESS,
    )

private val activatingPlugAndChargeButtonsModel =
    PlugAndChargeEnrollmentButtonsModel.Single(
        primaryButton =
            PlugAndChargeButtonModel.Primary(
                textId = R.string.Common_confirm,
                uiEvent = null,
            ),
    )

private val installationCompleteProgressModel =
    PlugAndChargeEnrollmentProgressModel(
        titleId = R.string.pnc_enrollment_completed_title,
        descriptionId = R.string.pnc_enrollment_completed_description,
        installationProgress = PlugAndChargeInstallationProgress.DONE,
    )

private val installationCompleteButtonsModel =
    PlugAndChargeEnrollmentButtonsModel.Single(
        primaryButton =
            PlugAndChargeButtonModel.Primary(
                textId = R.string.Common_close,
                uiEvent = PlugAndChargeEnrollmentUiEvent.ON_CLOSE_PLUG_AND_CHARGE_CLICK,
            ),
    )
