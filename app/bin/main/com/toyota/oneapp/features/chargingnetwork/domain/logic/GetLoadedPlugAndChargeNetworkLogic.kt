/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedPlugAndChargeNetworkUseCase
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.GetPlugAndChargeEnrollmentStatusUseCase
import javax.inject.Inject

class GetLoadedPlugAndChargeNetworkLogic
    @Inject
    constructor(
        private val getPlugAndChargeEnrolledStatus: GetPlugAndChargeEnrollmentStatusUseCase,
    ) : GetLoadedPlugAndChargeNetworkUseCase {
        override suspend fun invoke(isChargingNetworksDataConsentAccepted: Boolean): PlugAndChargeNetworkModel.Loaded? =
            if (isChargingNetworksDataConsentAccepted) {
                getPlugAndChargeEnrolledStatus().getOrNull()?.toPlugAndChargeNetworkModel()
            } else {
                PlugAndChargeNetworkModel.Loaded.DataConsentNotAccepted
            }

        private fun PlugAndChargeEnrollmentStatus.toPlugAndChargeNetworkModel(): PlugAndChargeNetworkModel.Loaded.DataConsentAccepted =
            PlugAndChargeNetworkModel.Loaded.DataConsentAccepted(
                isEnrolled = this is PlugAndChargeEnrollmentStatus.Enrolled,
            )
    }
