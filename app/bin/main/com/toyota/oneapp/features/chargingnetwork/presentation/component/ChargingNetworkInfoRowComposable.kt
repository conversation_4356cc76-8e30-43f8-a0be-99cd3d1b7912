/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun ChargingNetworkInfoRowComposable(
    modifier: Modifier = Modifier,
    title: String,
    statusComponent: @Composable () -> Unit,
    descriptionComponent: @Composable (() -> Unit)? = null,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .weight(
                        weight = 1f,
                        fill = false,
                    ),
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_ev_charge_point),
                contentDescription = title,
                colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
                modifier = Modifier.size(24.dp),
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(
                verticalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                OASubHeadLine1TextView(
                    text = title,
                    color = AppTheme.colors.tertiary03,
                )
                descriptionComponent?.let { safeDescriptionComponent ->
                    safeDescriptionComponent()
                }
            }
        }

        Spacer(modifier = Modifier.width(16.dp))

        statusComponent()
    }
}

@Preview
@Composable
private fun ChargingNetworkInfoRowPreview(
    @PreviewParameter(OAThemePreviewProvider::class) themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        ChargingNetworkInfoRowComposable(
            title = "Charging Networks",
            statusComponent = { Text("Status component") },
        )
    }
}
