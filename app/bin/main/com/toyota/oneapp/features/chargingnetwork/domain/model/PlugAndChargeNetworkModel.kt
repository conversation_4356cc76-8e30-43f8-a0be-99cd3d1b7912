/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.model

sealed interface PlugAndChargeNetworkModel {
    sealed interface Loaded : PlugAndChargeNetworkModel {
        data class DataConsentAccepted(val isEnrolled: Boolean) : Loaded

        data object DataConsentNotAccepted : Loaded
    }

    data object Loading : PlugAndChargeNetworkModel
}
