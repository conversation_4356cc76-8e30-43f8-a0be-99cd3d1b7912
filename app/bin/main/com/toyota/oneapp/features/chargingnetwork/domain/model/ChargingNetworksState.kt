/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.model

sealed interface ChargingNetworksState {
    data class Loaded(
        val withDataConsent: DataConsentNetworksModel?,
        val plugAndChargeNetwork: PlugAndChargeNetworkModel?,
    ) : ChargingNetworksState

    data class Loading(
        val enabledChargingNetworksAmount: Int,
    ) : ChargingNetworksState
}
