/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsFeatureFlagEnabledForChargingNetworkUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsLocalFeatureFlagEnabledForChargingNetworkUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsRemoteFeatureFlagEnabledForChargingNetworkUseCase
import javax.inject.Inject

class IsFeatureFlagEnabledForChargingNetworkLogic
    @Inject
    constructor(
        private val isLocalFlagEnabled: IsLocalFeatureFlagEnabledForChargingNetworkUseCase,
        private val isRemoteFlagEnable: IsRemoteFeatureFlagEnabledForChargingNetworkUseCase,
    ) : IsFeatureFlagEnabledForChargingNetworkUseCase {
        override fun invoke(chargingNetworkType: ChargingNetworkType): Boolean =
            isLocalFlagEnabled(chargingNetworkType) && isRemoteFlagEnable(chargingNetworkType)
    }
