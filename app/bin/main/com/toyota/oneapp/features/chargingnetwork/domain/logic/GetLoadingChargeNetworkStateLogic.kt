/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksTypesUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadingChargeNetworkStateUseCase
import javax.inject.Inject

class GetLoadingChargeNetworkStateLogic
    @Inject
    constructor(
        private val getEnabledChargingNetworkTypes: GetEnabledChargingNetworksTypesUseCase,
    ) : GetLoadingChargeNetworkStateUseCase {
        override fun invoke(): ChargingNetworksState = getEnabledChargingNetworkTypes().toLoadingState()

        private fun List<ChargingNetworkType>.toLoadingState(): ChargingNetworksState.Loading =
            ChargingNetworksState.Loading(
                enabledChargingNetworksAmount =
                    listOf(
                        containsPlugAndCharge(),
                        containsDataConsent(),
                    ).count { it },
            )

        private fun List<ChargingNetworkType>.containsPlugAndCharge(): Boolean =
            contains(
                ChargingNetworkType.PLUG_AND_CHARGE,
            )

        private fun List<ChargingNetworkType>.containsDataConsent(): Boolean =
            contains(ChargingNetworkType.IONNA) || contains(ChargingNetworkType.TESLA)
    }
