/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.usecase

import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel

fun interface GetLoadedPlugAndChargeNetworkUseCase {
    suspend operator fun invoke(isChargingNetworksDataConsentAccepted: Boolean): PlugAndChargeNetworkModel?
}
