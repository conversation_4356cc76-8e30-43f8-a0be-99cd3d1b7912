/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.mapper

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import javax.inject.Inject

class PlugAndChargeEnrollmentNotificationUiMapperImpl
    @Inject
    constructor() :
    PlugAndChargeEnrollmentNotificationUiMapper {
        override fun map(
            stateNotified: PlugAndChargeEnrollmentState,
            currentChargingNetworksState: ChargingNetworksState,
        ): ChargingNetworksState =
            if (
                stateNotified == PlugAndChargeEnrollmentState.InstallationComplete &&
                currentChargingNetworksState is ChargingNetworksState.Loaded
            ) {
                currentChargingNetworksState.toActivatedPlugAndCharge()
            } else {
                currentChargingNetworksState
            }

        private fun ChargingNetworksState.Loaded.toActivatedPlugAndCharge(): ChargingNetworksState.Loaded =
            copy(
                plugAndChargeNetwork =
                    PlugAndChargeNetworkModel.Loaded.DataConsentAccepted(
                        isEnrolled = true,
                    ),
            )
    }
