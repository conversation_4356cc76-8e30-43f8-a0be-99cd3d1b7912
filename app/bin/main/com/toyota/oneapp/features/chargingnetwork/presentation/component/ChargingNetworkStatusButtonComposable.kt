/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABorderButton
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun ChargingNetworkStatusButton(
    modifier: Modifier = Modifier,
    @StringRes stringResourceId: Int,
    onClick: () -> Unit,
) {
    OABorderButton(
        modifier = modifier.wrapContentSize(),
        onClick = onClick,
        text = stringResource(stringResourceId),
        accessibilityID = "",
    )
}

@Composable
@Preview
private fun ChargingNetworkStatusButtonPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode) {
        ChargingNetworkStatusButton(
            stringResourceId = R.string.register,
            onClick = {},
        )
    }
}
