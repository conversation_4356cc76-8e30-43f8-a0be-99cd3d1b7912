/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetDisabledChargingNetworkNamesUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsFeatureFlagEnabledForChargingNetworkUseCase
import javax.inject.Inject

class GetDisabledChargingNetworkNamesLogic
    @Inject
    constructor(
        private val isFeatureFlagEnabledForChargingNetwork: IsFeatureFlagEnabledForChargingNetworkUseCase,
    ) : GetDisabledChargingNetworkNamesUseCase {
        override fun invoke(): List<String> =
            ChargingNetworkType.entries
                .filterNot(isFeatureFlagEnabledForChargingNetwork::invoke)
                .map { it.name.lowercase() }
    }
