/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.utils

import androidx.annotation.StringRes
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType

@StringRes
internal fun ChargingNetworkType.toStringResource(): Int =
    when (this) {
        ChargingNetworkType.IONNA -> R.string.ionna
        ChargingNetworkType.TESLA -> R.string.tesla
        ChargingNetworkType.PLUG_AND_CHARGE -> R.string.plug_and_charge
    }
