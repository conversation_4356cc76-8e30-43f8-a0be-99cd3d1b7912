/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsLocalFeatureFlagEnabledForChargingNetworkUseCase
import javax.inject.Inject

class IsLocalFeatureFlagEnabledForChargingNetworkLogic
    @Inject
    constructor(
        private val isLocalFlagEnabledForIonna: Boolean,
        private val isLocalFlagEnabledForTesla: Boolean,
        private val isLocalFlagEnabledForPlugAndCharge: Boolean,
    ) : IsLocalFeatureFlagEnabledForChargingNetworkUseCase {
        override fun invoke(chargingNetworkType: ChargingNetworkType): Boolean =
            when (chargingNetworkType) {
                ChargingNetworkType.IONNA -> isLocalFlagEnabledForIonna
                ChargingNetworkType.TESLA -> isLocalFlagEnabledForTesla
                ChargingNetworkType.PLUG_AND_CHARGE -> isLocalFlagEnabledForPlugAndCharge
            }
    }
