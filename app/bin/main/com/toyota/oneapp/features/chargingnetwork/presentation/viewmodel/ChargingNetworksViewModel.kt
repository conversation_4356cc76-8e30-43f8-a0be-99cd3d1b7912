/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksUseCase
import com.toyota.oneapp.features.chargingnetwork.presentation.mapper.PlugAndChargeEnrollmentNotificationUiMapper
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolder
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChargingNetworksViewModel
    @Inject
    constructor(
        private val getEnabledChargingNetworks: GetEnabledChargingNetworksUseCase,
        private val plugAndChargeEnrollmentStateHolder: PlugAndChargeEnrollmentStateHolder,
        private val plugAndChargeEnrollmentNotificationUiMapper: PlugAndChargeEnrollmentNotificationUiMapper,
    ) : BaseViewModel<ChargingNetworksState, Unit>() {
        init {
            observeEnabledChargingNetworks()
            observePlugAndChargeEnrollmentState()
        }

        private fun observeEnabledChargingNetworks() {
            getEnabledChargingNetworks().onEach { newState ->
                state.update { newState }
            }.launchIn(viewModelScope)
        }

        private fun observePlugAndChargeEnrollmentState() {
            viewModelScope.launch {
                plugAndChargeEnrollmentStateHolder.state.collect { plugAndChargeState ->
                    state.update {
                        plugAndChargeEnrollmentNotificationUiMapper.map(
                            stateNotified = plugAndChargeState,
                            currentChargingNetworksState = it,
                        )
                    }
                }
            }
        }

        override fun defaultState(): ChargingNetworksState =
            ChargingNetworksState.Loading(
                enabledChargingNetworksAmount = 0,
            )

        override fun onEvent(event: Unit) = Unit
    }
