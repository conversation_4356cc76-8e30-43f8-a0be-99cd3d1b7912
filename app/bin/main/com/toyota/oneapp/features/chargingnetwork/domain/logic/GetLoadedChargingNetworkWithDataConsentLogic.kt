/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.DataConsentNetworksModel
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetChargingNetworkDataConsentsUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedChargingNetworkWithDataConsentUseCase
import com.toyota.oneapp.features.dataconsent.domain.usecase.GetDataConsentStatusResultUseCase
import javax.inject.Inject

class GetLoadedChargingNetworkWithDataConsentLogic
    @Inject
    constructor(
        private val getChargingNetworkDataConsents: GetChargingNetworkDataConsentsUseCase,
        private val getDataConsentStatusResult: GetDataConsentStatusResultUseCase,
    ) : GetLoadedChargingNetworkWithDataConsentUseCase {
        override suspend fun invoke(): DataConsentNetworksModel? {
            val strategies = getChargingNetworkDataConsents()
            return strategies.firstOrNull()?.let { strategy ->
                getDataConsentStatusResult(strategy).getOrNull()?.let { status ->
                    DataConsentNetworksModel(
                        dataConsentStrategies = strategies,
                        status = status,
                    )
                }
            }
        }
    }
