/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun PlugAndChargeNetworkRowComposable(
    modifier: Modifier = Modifier,
    model: PlugAndChargeNetworkModel,
    onActivateClick: () -> Unit,
) {
    when (model) {
        is PlugAndChargeNetworkModel.Loaded ->
            LoadedPlugAndChargeNetworkRowComposable(
                modifier = modifier,
                model = model,
                onActivateClick = onActivateClick,
            )

        PlugAndChargeNetworkModel.Loading -> ChargingNetworkShimmerRowComposable()
    }
}

@Composable
@Preview
private fun PlugAndChargeNetworkRowComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeNetworkRowComposable(
            model = PlugAndChargeNetworkModel.Loaded.DataConsentAccepted(true),
            onActivateClick = {},
        )
    }
}

@Composable
@Preview
private fun PlugAndChargeLoadingComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        PlugAndChargeNetworkRowComposable(
            model = PlugAndChargeNetworkModel.Loading,
            onActivateClick = {},
        )
    }
}
