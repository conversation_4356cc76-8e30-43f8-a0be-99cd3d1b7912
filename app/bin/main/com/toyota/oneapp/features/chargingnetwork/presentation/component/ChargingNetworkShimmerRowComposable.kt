/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.composable.ChargeInfoCommonCardShimmer
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun ChargingNetworkShimmerRowComposable(modifier: Modifier = Modifier) {
    ChargeInfoCommonCardShimmer(
        modifier = modifier,
        elevation = 0.dp,
        shape = 0.dp,
    )
}

@Composable
@Preview
private fun ChargingNetworkShimmerRowPreview(
    @PreviewParameter(OAThemePreviewProvider::class) themeMode: ThemeMode,
) {
    ContentPreview(
        modifier = Modifier.wrapContentSize(),
        themeMode = themeMode,
    ) {
        ChargingNetworkShimmerRowComposable()
    }
}
