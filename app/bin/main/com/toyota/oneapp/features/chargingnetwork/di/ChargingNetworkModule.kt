/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.di

import com.toyota.oneapp.features.chargingnetwork.domain.logic.GetChargingNetworkDataConsentsLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.GetChargingNetworksMapLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.GetDisabledChargingNetworkNamesLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.GetEnabledChargingNetworksLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.GetEnabledChargingNetworksTypesLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.GetLoadedChargingNetworkWithDataConsentLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.GetLoadedEnabledChargingNetworksLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.GetLoadedPlugAndChargeNetworkLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.GetLoadingChargeNetworkStateLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.IsFeatureFlagEnabledForChargingNetworkLogic
import com.toyota.oneapp.features.chargingnetwork.domain.logic.IsRemoteFeatureFlagEnabledForChargingNetworkLogic
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetChargingNetworkDataConsentsUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetChargingNetworksMapUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetDisabledChargingNetworkNamesUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksTypesUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedChargingNetworkWithDataConsentUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedEnabledChargingNetworksUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedPlugAndChargeNetworkUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadingChargeNetworkStateUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsFeatureFlagEnabledForChargingNetworkUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsRemoteFeatureFlagEnabledForChargingNetworkUseCase
import com.toyota.oneapp.features.chargingnetwork.presentation.mapper.PlugAndChargeEnrollmentNotificationUiMapper
import com.toyota.oneapp.features.chargingnetwork.presentation.mapper.PlugAndChargeEnrollmentNotificationUiMapperImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
interface ChargingNetworkModule {
    @Binds
    fun bindGetEnabledChargingNetworksTypesUseCase(logic: GetEnabledChargingNetworksTypesLogic): GetEnabledChargingNetworksTypesUseCase

    @Binds
    fun bindIsRemoteFeatureFlagEnabledForChargingNetworkUseCase(
        logic: IsRemoteFeatureFlagEnabledForChargingNetworkLogic,
    ): IsRemoteFeatureFlagEnabledForChargingNetworkUseCase

    @Binds
    fun bindIsFeatureFlagEnabledForChargingNetworkUseCase(
        logic: IsFeatureFlagEnabledForChargingNetworkLogic,
    ): IsFeatureFlagEnabledForChargingNetworkUseCase

    @Binds
    fun bindGetLoadedEnabledChargingNetworksUseCase(logic: GetLoadedEnabledChargingNetworksLogic): GetLoadedEnabledChargingNetworksUseCase

    @Binds
    fun bindGetLoadingChargeNetworkStateUseCas(logic: GetLoadingChargeNetworkStateLogic): GetLoadingChargeNetworkStateUseCase

    @Binds
    fun bindGetChargingNetworkDataConsentsUseCase(logic: GetChargingNetworkDataConsentsLogic): GetChargingNetworkDataConsentsUseCase

    @Binds
    fun bindGetLoadedChargingNetworkWithDataConsentUseCase(
        logic: GetLoadedChargingNetworkWithDataConsentLogic,
    ): GetLoadedChargingNetworkWithDataConsentUseCase

    @Binds
    fun bindGetLoadedPlugAndChargeNetworkUseCase(logic: GetLoadedPlugAndChargeNetworkLogic): GetLoadedPlugAndChargeNetworkUseCase

    @Binds
    fun bindGetChargingNetworksMapUseCase(logic: GetChargingNetworksMapLogic): GetChargingNetworksMapUseCase

    @Binds
    fun bindGetEnabledChargingNetworksUseCase(logic: GetEnabledChargingNetworksLogic): GetEnabledChargingNetworksUseCase

    @Binds
    fun bindGetDisabledChargingNetworkNamesUseCase(logic: GetDisabledChargingNetworkNamesLogic): GetDisabledChargingNetworkNamesUseCase

    @Binds
    fun bindPlugAndChargeEnrollmentNotificationUiMapper(
        impl: PlugAndChargeEnrollmentNotificationUiMapperImpl,
    ): PlugAndChargeEnrollmentNotificationUiMapper
}
