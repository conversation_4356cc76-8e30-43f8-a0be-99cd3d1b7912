/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetChargingNetworksMapUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksTypesUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsFeatureFlagEnabledForChargingNetworkUseCase
import com.toyota.oneapp.features.dataconsent.domain.usecase.IsVehicleFeatureEnabledUseCase
import com.toyota.oneapp.model.vehicle.Feature
import javax.inject.Inject

class GetEnabledChargingNetworksTypesLogic
    @Inject
    constructor(
        private val isVehicleFeatureEnabled: IsVehicleFeatureEnabledUseCase,
        private val isFeatureFlagEnabledForChargingNetwork: IsFeatureFlagEnabledForChargingNetworkUseCase,
        getChargingNetworks: GetChargingNetworksMapUseCase,
    ) : GetEnabledChargingNetworksTypesUseCase {
        private val chargingNetworksMap: Map<Feature, ChargingNetworkType> =
            getChargingNetworks()

        override fun invoke(): List<ChargingNetworkType> =
            chargingNetworksMap.filter { (feature: Feature, chargingNetworkType: ChargingNetworkType) ->
                isVehicleFeatureEnabled(feature) &&
                    isFeatureFlagEnabledForChargingNetwork(chargingNetworkType)
            }.values.toList()
    }
