/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.component

import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.entrollment.presentation.component.EnrolledComposable

@Composable
fun LoadedPlugAndChargeNetworkRowComposable(
    modifier: Modifier = Modifier,
    model: PlugAndChargeNetworkModel.Loaded,
    onActivateClick: () -> Unit,
) {
    ChargingNetworkInfoRowComposable(
        modifier = modifier,
        title = stringResource(id = R.string.plug_and_charge),
        statusComponent = {
            val activateResourceId = R.string.activate
            when (model) {
                is PlugAndChargeNetworkModel.Loaded.DataConsentAccepted ->
                    if (model.isEnrolled) {
                        EnrolledComposable(
                            textId = R.string.activated,
                        )
                    } else {
                        ChargingNetworkStatusButton(
                            stringResourceId = activateResourceId,
                            onClick = onActivateClick,
                        )
                    }
                PlugAndChargeNetworkModel.Loaded.DataConsentNotAccepted ->
                    ChargingNetworkStatusButton(
                        stringResourceId = activateResourceId,
                        onClick = {
                            // Sice we don't have the UI for disabled state yet, we will use the
                            // same button as the activated state. But without any action.
                        },
                    )
            }
        },
    )
}

@Composable
@Preview
private fun LoadedPlugAndChargeNetworkRowComposablePreview(
    @PreviewParameter(LoadedPlugAndChargeNetworkRowPreviewProvider::class)
    parameter: LoadedPlugAndChargeNetworkRowPreviewParameter,
) {
    ContentPreview(
        themeMode = parameter.themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        LoadedPlugAndChargeNetworkRowComposable(
            model = parameter.state,
            onActivateClick = {},
        )
    }
}

private class LoadedPlugAndChargeNetworkRowPreviewProvider :
    PreviewParameterProvider<LoadedPlugAndChargeNetworkRowPreviewParameter> {
    override val values: Sequence<LoadedPlugAndChargeNetworkRowPreviewParameter> =
        sequenceOf(
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Dark,
                state = PlugAndChargeNetworkModel.Loaded.DataConsentNotAccepted,
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Dark,
                state = PlugAndChargeNetworkModel.Loaded.DataConsentAccepted(true),
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Dark,
                state = PlugAndChargeNetworkModel.Loaded.DataConsentAccepted(false),
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Light,
                state = PlugAndChargeNetworkModel.Loaded.DataConsentNotAccepted,
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Light,
                state = PlugAndChargeNetworkModel.Loaded.DataConsentAccepted(true),
            ),
            LoadedPlugAndChargeNetworkRowPreviewParameter(
                themeMode = ThemeMode.Light,
                state = PlugAndChargeNetworkModel.Loaded.DataConsentAccepted(false),
            ),
        )
}

private data class LoadedPlugAndChargeNetworkRowPreviewParameter(
    val themeMode: ThemeMode,
    val state: PlugAndChargeNetworkModel.Loaded,
)
