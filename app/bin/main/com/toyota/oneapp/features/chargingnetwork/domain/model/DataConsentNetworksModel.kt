/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.model

import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy

data class DataConsentNetworksModel(
    val status: DataConsentStatus,
    val dataConsentStrategies: List<DataConsentStrategy>,
)
