/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetChargingNetworkDataConsentsUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksTypesUseCase
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import javax.inject.Inject

class GetChargingNetworkDataConsentsLogic
    @Inject
    constructor(
        private val getEnabledChargingNetworksTypes: GetEnabledChargingNetworksTypesUseCase,
    ) : GetChargingNetworkDataConsentsUseCase {
        override fun invoke(): List<DataConsentStrategy> {
            val enabledTypes = getEnabledChargingNetworksTypes()
            return when {
                enabledTypes.containsIonna() && enabledTypes.containsTesla() ->
                    listOf(
                        DataConsentStrategy.IONNA,
                        DataConsentStrategy.TESLA,
                    )

                enabledTypes.containsIonna() -> listOf(DataConsentStrategy.IONNA)

                enabledTypes.containsTesla() -> listOf(DataConsentStrategy.TESLA)

                else -> emptyList()
            }
        }

        private fun List<ChargingNetworkType>.containsIonna(): Boolean = contains(ChargingNetworkType.IONNA)

        private fun List<ChargingNetworkType>.containsTesla(): Boolean = contains(ChargingNetworkType.TESLA)
    }
