/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedEnabledChargingNetworksUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadingChargeNetworkStateUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class GetEnabledChargingNetworksLogic
    @Inject
    constructor(
        private val getLoadingEnabledChargingNetworks: GetLoadingChargeNetworkStateUseCase,
        private val getLoadedEnabledChargingNetworks: GetLoadedEnabledChargingNetworksUseCase,
    ) : GetEnabledChargingNetworksUseCase {
        override fun invoke(): Flow<ChargingNetworksState> =
            flow {
                emit(getLoadingEnabledChargingNetworks())
                emitAll(getLoadedEnabledChargingNetworks())
            }
    }
