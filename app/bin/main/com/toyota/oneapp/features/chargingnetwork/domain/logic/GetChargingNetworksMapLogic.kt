/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetChargingNetworksMapUseCase
import com.toyota.oneapp.model.vehicle.Feature
import javax.inject.Inject

class GetChargingNetworksMapLogic
    @Inject
    constructor() : GetChargingNetworksMapUseCase {
        override operator fun invoke(): Map<Feature, ChargingNetworkType> =
            mapOf(
                Feature.IONNA to ChargingNetworkType.IONNA,
                Feature.TESLA to ChargingNetworkType.TESLA,
                Feature.PLUG_AND_CHARGE to ChargingNetworkType.PLUG_AND_CHARGE,
            )
    }
