/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.domain.logic

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksTypesUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedChargingNetworkWithDataConsentUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedEnabledChargingNetworksUseCase
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetLoadedPlugAndChargeNetworkUseCase
import com.toyota.oneapp.features.dataconsent.domain.model.isAccepted
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class GetLoadedEnabledChargingNetworksLogic
    @Inject
    constructor(
        private val getEnabledChargingNetworkTypes: GetEnabledChargingNetworksTypesUseCase,
        private val getLoadedChargingNetworkWithDataConsent: GetLoadedChargingNetworkWithDataConsentUseCase,
        private val getLoadedPlugAndChargeNetwork: GetLoadedPlugAndChargeNetworkUseCase,
    ) : GetLoadedEnabledChargingNetworksUseCase {
        override fun invoke(): Flow<ChargingNetworksState.Loaded> =
            flow {
                val dataConsent = getLoadedChargingNetworkWithDataConsent()
                val enabledTypes = getEnabledChargingNetworkTypes()
                if (enabledTypes.contains(ChargingNetworkType.PLUG_AND_CHARGE)) {
                    emit(
                        ChargingNetworksState.Loaded(
                            withDataConsent = dataConsent,
                            plugAndChargeNetwork = PlugAndChargeNetworkModel.Loading,
                        ),
                    )
                    emit(
                        ChargingNetworksState.Loaded(
                            withDataConsent = dataConsent,
                            plugAndChargeNetwork =
                                dataConsent?.status?.let {
                                    getLoadedPlugAndChargeNetwork(it.isAccepted())
                                },
                        ),
                    )
                } else {
                    emit(
                        ChargingNetworksState.Loaded(
                            withDataConsent = dataConsent,
                            plugAndChargeNetwork = null,
                        ),
                    )
                }
            }
    }
