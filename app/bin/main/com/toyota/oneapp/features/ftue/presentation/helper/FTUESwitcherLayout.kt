package com.toyota.oneapp.features.ftue.presentation.helper

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.ftue.presentation.FTUEViewModel
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w

@Composable
fun SwitcherLayout(
    layoutIndex: Int,
    onNextStep: () -> Unit,
    url: String,
    finishFTUE: () -> Unit,
    viewModel: FTUEViewModel,
) {
    val switcherLayoutIndex = layoutIndex + 1
    Box(
        Modifier
            .background(color = Color.White)
            .height(560.h()),
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(modifier = Modifier.height(29.h()))
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier =
                    Modifier
                        .padding(end = 14.w(), start = 16.w())
                        .fillMaxWidth(),
            ) {
                Box(
                    modifier =
                        Modifier
                            .width(48.w())
                            .height(48.h())
                            .clip(CircleShape)
                            .background(AppTheme.colors.tertiary12)
                            .wrapContentSize(Alignment.Center)
                            .clickable(onClick = finishFTUE),
                ) {
                    Icon(
                        modifier =
                            Modifier
                                .layoutId(AccessibilityId.FTUE_CLOSE_ICON_CTA),
                        painter = painterResource(id = R.drawable.ic_close),
                        contentDescription = stringResource(id = R.string.ftueCloseIconDescription),
                        tint = AppTheme.colors.button02a,
                    )
                }
                OACallOut1TextView(
                    text = "$switcherLayoutIndex ${stringResource(id = R.string.ftueIndexSize)}",
                    color = AppTheme.colors.tertiary05,
                    textAlign = TextAlign.End,
                    modifier =
                        Modifier
                            .width(63.w())
                            .layoutId(AccessibilityId.FTUE_INDEX_TEXT),
                )
            }
            Spacer(Modifier.height(20.h()))
            Box(
                Modifier
                    .size(270.w(), 202.h())
                    .layoutId(AccessibilityId.FTUE_VIDEO),
            ) {
                VideoPlayer(url)
            }
            OASubHeadLine3TextView(
                text = stringResource(id = R.string.switcherLayoutTitle),
                color = AppTheme.colors.tertiary03,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .padding(horizontal = 16.w())
                        .layoutId(AccessibilityId.FTUE_TITLE_TEXT),
            )
            OACallOut1TextView(
                text = stringResource(id = R.string.switcherLayoutNote),
                color = AppTheme.colors.tertiary05,
                modifier =
                    Modifier
                        .padding(horizontal = 16.w(), vertical = 8.h())
                        .layoutId(AccessibilityId.FTUE_NOTE_TEXT),
                textAlign = TextAlign.Center,
            )
            Spacer(Modifier.height(27.h()))
            Box(modifier = Modifier.fillMaxSize()) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.align(Alignment.BottomCenter),
                ) {
                    PrimaryButton02(
                        text = stringResource(id = R.string.ftueNext),
                        modifier =
                            Modifier
                                .wrapContentWidth()
                                .wrapContentHeight()
                                .align(Alignment.CenterHorizontally)
                                .layoutId(AccessibilityId.FTUE_NEXT_BUTTON_CTA),
                        click = {
                            viewModel.logEventWithParameter(
                                AnalyticsEventParam.FTUE_SWITCHER_NEXT_CTA,
                            )
                            onNextStep()
                        },
                    )
                    Spacer(Modifier.height(32.h()))
                }
            }
        }
    }
}
