package com.toyota.oneapp.features.ftue.presentation.helper

import android.net.Uri
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView
import androidx.media3.ui.PlayerView.SHOW_BUFFERING_WHEN_PLAYING
import com.toyota.oneapp.R

@Composable
@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
fun VideoPlayer(
    videoUrl: String,
    isRevisitScreen: Boolean = false,
) {
    val context = LocalContext.current

    val exoPlayer =
        remember {
            ExoPlayer.Builder(context).build()
        }

    DisposableEffect(Unit) {
        val defaultDataSourceFactory = DefaultDataSource.Factory(context)
        val dataSourceFactory: DataSource.Factory =
            DefaultDataSource.Factory(
                context,
                defaultDataSourceFactory,
            )
        val source =
            ProgressiveMediaSource.Factory(dataSourceFactory)
                .createMediaSource(MediaItem.fromUri(Uri.parse(videoUrl)))

        exoPlayer.setMediaSource(source)
        exoPlayer.prepare()

        onDispose {
            exoPlayer.release()
        }
    }

    exoPlayer.playWhenReady = true
    exoPlayer.videoScalingMode = C.VIDEO_SCALING_MODE_SCALE_TO_FIT
    exoPlayer.repeatMode = Player.REPEAT_MODE_ONE

    val playerView =
        remember {
            PlayerView(context).apply {
                hideController()
                useController = false
                resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FILL
                player = exoPlayer
                setBackgroundColor(resources.getColor(R.color.white))
                setShutterBackgroundColor(
                    resources.getColor(
                        if (isRevisitScreen) com.toyota.one_ui.R.color.oneUiColorPrimaryBackGround else R.color.white,
                    ),
                )
                setShowBuffering(SHOW_BUFFERING_WHEN_PLAYING)
            }
        }

    DisposableEffect(
        AndroidView(factory = {
            playerView
        }),
    ) {
        onDispose {
            exoPlayer.stop()
            exoPlayer.release()
        }
    }
}
