package com.toyota.oneapp.features.ftue.presentation

import android.os.Bundle
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.ftue.application.FTUEState
import com.toyota.oneapp.features.ftue.presentation.extension.appendToVideoBaseUrl
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w
import com.toyota.oneapp.features.ftue.presentation.helper.FTUEHelper
import com.toyota.oneapp.features.ftue.presentation.helper.VideoPlayer
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel

@Composable
fun FTUERevisitScreen(
    oneAppPreferenceModel: OneAppPreferenceModel,
    extras: Bundle?,
) {
    val viewModel = hiltViewModel<FTUEViewModel>()

    val fTUEState = viewModel.fTUEState.collectAsState()

    val context = LocalContext.current

    val finishFTUE: () -> Unit = {
        FTUEHelper.finishFTUE(context, oneAppPreferenceModel, extras)
    }

    if (fTUEState.value is FTUEState.Success) {
        val skipData = (fTUEState.value as FTUEState.Success).data.ftueInitials.skipLayout

        val skipLayout = stringResource(id = skipData).appendToVideoBaseUrl()

        Box(
            Modifier
                .fillMaxSize()
                .background(AppTheme.colors.tertiary12),
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Box(
                    modifier =
                        Modifier
                            .padding(start = 20.w(), top = 20.h())
                            .width(48.w())
                            .height(48.h())
                            .clip(CircleShape)
                            .background(AppTheme.colors.tertiary12)
                            .wrapContentSize(Alignment.Center)
                            .align(Alignment.Start)
                            .clickable(onClick = finishFTUE),
                ) {
                    Icon(
                        modifier =
                            Modifier
                                .layoutId(AccessibilityId.FTUE_CLOSE_ICON_CTA),
                        painter = painterResource(id = R.drawable.ic_close),
                        contentDescription = stringResource(id = R.string.ftueCloseIconDescription),
                        tint = AppTheme.colors.button02a,
                    )
                }
                Box(
                    Modifier
                        .padding(top = 48.dp, bottom = 136.dp)
                        .size(178.dp)
                        .layoutId(AccessibilityId.FTUE_VIDEO),
                ) {
                    VideoPlayer(skipLayout, isRevisitScreen = true)
                }
                OASubHeadLine3TextView(
                    text = stringResource(id = R.string.revisitTitle),
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .padding(bottom = 20.h(), start = 38.w(), end = 38.w())
                            .layoutId(AccessibilityId.FTUE_TITLE_TEXT),
                    textAlign = TextAlign.Center,
                )
                OACallOut1TextView(
                    text = stringResource(id = R.string.revisitNote),
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .padding(bottom = 16.h(), start = 28.dp, end = 28.dp)
                            .layoutId(AccessibilityId.FTUE_NOTE_TEXT),
                    textAlign = TextAlign.Center,
                )
            }
            PrimaryButton02(
                text = stringResource(id = R.string.ftueCommonOk),
                modifier =
                    Modifier
                        .wrapContentWidth()
                        .wrapContentHeight()
                        .align(Alignment.BottomCenter)
                        .layoutId(AccessibilityId.FTUE_OK_BUTTON_CTA)
                        .padding(bottom = 48.h()),
                click = {
                    finishFTUE()
                },
            )
        }
    }
}
