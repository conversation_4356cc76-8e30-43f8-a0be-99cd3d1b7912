package com.toyota.oneapp.features.ftue.presentation

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.navigation.compose.rememberNavController
import com.toyota.oneapp.features.ftue.presentation.helper.FTUENavigationGraph
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class FTUEActivity : UiBaseActivity() {
    @Inject
    lateinit var oneAppPreferenceModel: OneAppPreferenceModel

    companion object {
        const val USER_COMPLETED_TOUR = 101
        const val USER_INITIATED_TOUR = "userInitiatedTour"

        @JvmStatic
        fun createIntent(
            context: Context,
            bundle: Bundle? = null,
        ): Intent {
            val intent = Intent(context, FTUEActivity::class.java)
            if (bundle != null) {
                intent.addFlags(
                    Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK,
                )
                intent.putExtras(bundle)
            }
            return intent
        }
    }

    @SuppressLint("UnusedMaterialScaffoldPaddingParameter")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val userInitiatedTour = intent.extras?.getBoolean(USER_INITIATED_TOUR, false) ?: false

        setContent {
            val navHostController = rememberNavController()
            FTUENavigationGraph(
                navController = navHostController,
                oneAppPreferenceModel = oneAppPreferenceModel,
                userInitiatedTour = userInitiatedTour,
                extras = intent.extras,
            )
        }
    }
}
