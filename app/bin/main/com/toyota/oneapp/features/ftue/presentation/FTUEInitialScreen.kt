package com.toyota.oneapp.features.ftue.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAClickableButtonTextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.ftue.application.FTUEState
import com.toyota.oneapp.features.ftue.presentation.extension.appendToImageBaseUrl
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w
import com.toyota.oneapp.features.ftue.presentation.helper.FTUEImageLoader

@Composable
fun FTUEInitialScreen(
    navHostController: NavHostController,
    userInitiatedTour: Boolean,
) {
    val viewModel = hiltViewModel<FTUEViewModel>()

    val fTUEState = viewModel.fTUEState.collectAsState()

    val finishFTUE: () -> Unit = {
        navHostController.navigate(OAScreen.FTUERevisitScreen.route)
    }

    when (fTUEState.value) {
        is FTUEState.Success -> {
            val carouselData = (fTUEState.value as FTUEState.Success).data.ftueInitials.carouselLayout

            val carouselLayout = stringResource(id = carouselData).appendToImageBaseUrl()

            val annotatedString =
                buildAnnotatedString {
                    withStyle(
                        style = SpanStyle(fontWeight = FontWeight.Bold),
                    ) {
                        append(stringResource(id = R.string.carouselFTUENote_highlight))
                    }
                    append(" ") // SPACE_CHARACTER
                    withStyle(
                        style = SpanStyle(fontWeight = FontWeight.Normal),
                    ) {
                        append(stringResource(id = R.string.carouselFTUENote))
                    }
                }

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Bottom,
            ) {
                Spacer(modifier = Modifier.height(20.h()))
                Box(
                    modifier =
                        Modifier
                            .padding(start = 20.w())
                            .width(48.w())
                            .height(48.h())
                            .clip(CircleShape)
                            .background(AppTheme.colors.tertiary12)
                            .wrapContentSize(Alignment.Center)
                            .align(Alignment.Start)
                            .clickable(onClick = finishFTUE),
                ) {
                    Icon(
                        modifier =
                            Modifier
                                .layoutId(AccessibilityId.FTUE_CLOSE_ICON_CTA),
                        painter = painterResource(id = R.drawable.ic_close),
                        contentDescription = stringResource(id = R.string.ftueCloseIconDescription),
                        tint = AppTheme.colors.button02a,
                    )
                }
                Box(
                    modifier =
                        Modifier
                            .height(396.h())
                            .fillMaxWidth()
                            .layoutId(AccessibilityId.FTUE_IMAGE),
                ) {
                    FTUEImageLoader(
                        carouselLayout,
                        contentDescription = stringResource(id = R.string.ftueCarouselImage),
                    )
                }
                Spacer(
                    Modifier.height(
                        dimensionResource(id = R.dimen.ftue_initial_screen_text_top_padding),
                    ),
                )
                OASubHeadLine3TextView(
                    text =
                        stringResource(
                            id = if (userInitiatedTour) R.string.seeWhatsNew else R.string.WeJustGotAnUpgrade,
                        ),
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .padding(
                                horizontal = 27.w(),
                            )
                            .layoutId(AccessibilityId.FTUE_TITLE_TEXT),
                    textAlign = TextAlign.Center,
                )
                Spacer(Modifier.height(20.h()))
                Box(Modifier.height(100.h())) {
                    OACallOut1TextView(
                        text = annotatedString,
                        color = AppTheme.colors.tertiary05,
                        modifier =
                            Modifier
                                .padding(horizontal = 28.w())
                                .layoutId(
                                    AccessibilityId.FTUE_NOTE_TEXT,
                                ),
                        textAlign = TextAlign.Center,
                    )
                }
                Box(
                    modifier =
                        Modifier.fillMaxSize()
                            .padding(top = 9.dp),
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.align(Alignment.BottomCenter),
                    ) {
                        if (!userInitiatedTour) {
                            OAClickableButtonTextView(
                                text = stringResource(id = R.string.skipForNow),
                                color = AppTheme.colors.tertiary03,
                                modifier =
                                    Modifier
                                        .padding(top = 16.dp)
                                        .layoutId(AccessibilityId.FTUE_SKIP_TEXT_CTA),
                                onClick = {
                                    viewModel.logEventWithParameter(
                                        AnalyticsEventParam.FTUE_SKIP_FOR_NOW_CTA,
                                    )
                                    navHostController.navigate(OAScreen.SkipForNowScreen.route)
                                },
                            )
                        }
                        Spacer(modifier = Modifier.height(22.h()))
                        PrimaryButton02(
                            text =
                                stringResource(
                                    id = if (userInitiatedTour) R.string.startTour else R.string.seeWhatsNew,
                                ),
                            modifier =
                                Modifier
                                    .wrapContentWidth()
                                    .wrapContentHeight()
                                    .align(Alignment.CenterHorizontally)
                                    .layoutId(AccessibilityId.FTUE_SEENEW_BUTTON_CTA),
                            click = {
                                viewModel.logEventWithParameter(
                                    AnalyticsEventParam.FTUE_WHATS_NEW_CTA,
                                )
                                navHostController.navigate(OAScreen.FTUE.route)
                            },
                        )
                        Spacer(Modifier.height(48.h()))
                    }
                }
            }
        }
        is FTUEState.Loading -> {
            Text(stringResource(id = R.string.shimmer_loading))
        }
        else -> {
            Text(stringResource(id = R.string.no_data_available))
        }
    }
}
