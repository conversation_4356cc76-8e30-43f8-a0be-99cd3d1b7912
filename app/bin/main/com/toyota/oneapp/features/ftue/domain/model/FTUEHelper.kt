package com.toyota.oneapp.features.ftue.domain.model

import com.toyota.oneapp.features.ftue.constants.FTUELayouts

data class FTUE(val item: FTUELayouts, val urlPath: Int, val isVideoLayout: Boolean = false)

data class FTUEInitials(val homeLayout: Int, val carouselLayout: Int, val skipLayout: Int)

data class FTUEData(val ftueInitials: FTUEInitials, val ftue: ArrayList<ArrayList<FTUE>>)

data class TabLayoutResource(
    val title: Int,
    val note: Int,
    val ctaText: Int,
    val analyticsEventParam: String,
)
