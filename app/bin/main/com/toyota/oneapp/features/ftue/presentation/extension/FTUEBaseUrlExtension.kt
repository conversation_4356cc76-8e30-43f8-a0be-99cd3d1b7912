package com.toyota.oneapp.features.ftue.presentation.extension

import com.toyota.oneapp.BuildConfig

fun String.appendToImageBaseUrl(): String {
    val trimmedBaseUrl = BuildConfig.FTUE_IMAGE_BASE_URL.trimEnd('/')
    val trimmedString = this.trimStart('/')
    return "$trimmedBaseUrl/$trimmedString"
}

fun String.appendToVideoBaseUrl(): String {
    val trimmedBaseUrl = BuildConfig.FTUE_VIDEO_BASE_URL.trimEnd('/')
    val trimmedString = this.trimStart('/')
    return "$trimmedBaseUrl/$trimmedString"
}
