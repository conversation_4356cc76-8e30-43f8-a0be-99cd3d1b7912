package com.toyota.oneapp.features.ftue.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.features.ftue.application.FTUEState
import com.toyota.oneapp.features.ftue.application.FTUEUseCase
import com.toyota.oneapp.ui.BaseFragmentViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FTUEViewModel
    @Inject
    constructor(
        private val fTUEUseCase: FTUEUseCase,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseFragmentViewModel() {
        private val _fTUEState =
            MutableStateFlow<FTUEState>(value = FTUEState.Loading)
        val fTUEState = _fTUEState.asStateFlow()

        init {
            getFTUELayouts()
        }

        fun getFTUELayouts() {
            viewModelScope.launch {
                fTUEUseCase.fetchToolTips().collect {
                    _fTUEState.value = FTUEState.Success(it)
                }
            }
        }

        fun logEventWithParameter(event: String) {
            analyticsLogger.logEventWithParameter(AnalyticsEvent.FTUE.eventName, event)
        }
    }
