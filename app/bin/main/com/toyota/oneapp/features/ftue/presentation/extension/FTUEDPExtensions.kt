package com.toyota.oneapp.features.ftue.presentation.extension

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun Int.h(): Dp {
    val configuration = LocalConfiguration.current

    val screenHeight = configuration.screenHeightDp.dp
    return (screenHeight.value * (this.toFloat() / 812.0)).dp
}

@Composable
fun Int.w(): Dp {
    val configuration = LocalConfiguration.current

    val screenWidth = configuration.screenWidthDp.dp
    return (screenWidth.value * (this.toFloat() / 375.0)).dp
}
