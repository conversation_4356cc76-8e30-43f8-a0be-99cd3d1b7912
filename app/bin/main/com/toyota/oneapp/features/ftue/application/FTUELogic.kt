/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.ftue.application

import com.toyota.oneapp.R
import com.toyota.oneapp.core.BuildWrapper
import com.toyota.oneapp.features.ftue.constants.FTUELayouts
import com.toyota.oneapp.features.ftue.domain.model.FTUE
import com.toyota.oneapp.features.ftue.domain.model.FTUEData
import com.toyota.oneapp.features.ftue.domain.model.FTUEInitials
import com.toyota.oneapp.util.Brand
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class FTUELogic
    @Inject
    constructor(private val buildWrapper: BuildWrapper) : FTUEUseCase() {
        override suspend fun fetchToolTips(): Flow<FTUEData> {
            return flow {
                // Individual components
                val vehicleName =
                    FTUE(
                        item = FTUELayouts.VEHICLE_NAME,
                        urlPath = vehicleNameURL(),
                    )
                val vehicleNameWithDottedLines =
                    FTUE(
                        item = FTUELayouts.VEHICLE_NAME_DOTTED_LINES,
                        urlPath = R.string.FTUEVehicleNameWithDottedLines,
                    )
                val vehicleSwitcherLayout =
                    FTUE(
                        item = FTUELayouts.VEHICLE_SWITCHER,
                        urlPath = vehicleSwitcherUrl(),
                        isVideoLayout = true,
                    )
                val vehicleRemote =
                    FTUE(
                        item = FTUELayouts.VEHICLE_REMOTE,
                        urlPath = vehicleRemoteUrl(),
                    )
                val vehicleRemoteWithDottedLines =
                    FTUE(
                        item = FTUELayouts.VEHICLE_REMOTE_DOTTED_LINES,
                        urlPath = R.string.FTUERemoteWithDottedLines,
                    )
                val vehicleRemoteLayout =
                    FTUE(
                        item = FTUELayouts.VEHICLE_REMOTE_LAYOUT,
                        urlPath = vehicleRemoteLayoutUrl(),
                        isVideoLayout = true,
                    )
                val vehicleStatus =
                    FTUE(
                        item = FTUELayouts.VEHICLE_STATUS,
                        urlPath = vehicleStatusUrl(),
                    )
                val vehicleStatusWithDottedLines =
                    FTUE(
                        item = FTUELayouts.VEHICLE_STATUS_DOTTED_LINES,
                        urlPath = R.string.FTUEStatusWithDottedLines,
                    )
                val vehicleStatusLayout =
                    FTUE(
                        item = FTUELayouts.VEHICLE_STATUS_LAYOUT,
                        urlPath = vehicleStatusLayoutUrl(),
                        isVideoLayout = true,
                    )
                val vehicleHealth =
                    FTUE(
                        item = FTUELayouts.VEHICLE_HEALTH,
                        urlPath = vehicleHealthUrl(),
                    )
                val vehicleHealthWithDottedLines =
                    FTUE(
                        item = FTUELayouts.VEHICLE_HEALTH_DOTTED_LINES,
                        urlPath = R.string.FTUEHealthWithDottedLines,
                    )
                val vehicleHealthLayout =
                    FTUE(
                        item = FTUELayouts.VEHICLE_HEALTH_LAYOUT,
                        urlPath = vehicleHealthLayoutUrl(),
                        isVideoLayout = true,
                    )
                val vehicleInfo =
                    FTUE(
                        item = FTUELayouts.VEHICLE_INFO,
                        urlPath = vehicleInfoUrl(),
                    )
                val vehicleInfoLayout =
                    FTUE(
                        item = FTUELayouts.VEHICLE_INFO_LAYOUT,
                        urlPath = vehicleInfoLayoutUrl(),
                        isVideoLayout = true,
                    )

                // Set of individual components
                val vehicleSwitcherFTUE =
                    arrayListOf(
                        vehicleName,
                        vehicleNameWithDottedLines,
                        vehicleSwitcherLayout,
                    )
                val vehicleRemoteFTUE =
                    arrayListOf(
                        vehicleRemote,
                        vehicleRemoteWithDottedLines,
                        vehicleRemoteLayout,
                    )
                val vehicleStatusFTUE =
                    arrayListOf(
                        vehicleStatus,
                        vehicleStatusWithDottedLines,
                        vehicleStatusLayout,
                    )
                val vehicleHealthFTUE =
                    arrayListOf(
                        vehicleHealth,
                        vehicleHealthWithDottedLines,
                        vehicleHealthLayout,
                    )
                val vehicleInfoFTUE = arrayListOf(vehicleInfo, vehicleInfoLayout)

                // Tooltip lists
                val toolTipLists =
                    arrayListOf(
                        vehicleSwitcherFTUE,
                        vehicleRemoteFTUE,
                        vehicleStatusFTUE,
                        vehicleHealthFTUE,
                        vehicleInfoFTUE,
                    )

                val homeLayout =
                    when (buildWrapper.appBrand()) {
                        Brand.TOYOTA.appBrand -> {
                            R.string.FTUEToyotaHome
                        }

                        Brand.LEXUS.appBrand -> {
                            R.string.FTUELexusHome
                        }

                        Brand.SUBARU.appBrand -> {
                            R.string.FTUESubaruHome
                        }

                        else -> {
                            R.string.FTUEToyotaHome
                        }
                    }
                val carouselLayout =
                    when (buildWrapper.appBrand()) {
                        Brand.TOYOTA.appBrand -> {
                            R.string.FTUEToyotaCarousel
                        }

                        Brand.LEXUS.appBrand -> {
                            R.string.FTUELexusCarousel
                        }

                        Brand.SUBARU.appBrand -> {
                            R.string.FTUESubaruCarousel
                        }

                        else -> {
                            R.string.FTUEToyotaCarousel
                        }
                    }
                val ftueInitials =
                    FTUEInitials(
                        homeLayout = homeLayout,
                        carouselLayout = carouselLayout,
                        skipLayout = R.string.FTUESkipLayout,
                    )

                val ftue = FTUEData(ftueInitials = ftueInitials, ftue = toolTipLists)

                emit(ftue)
            }
        }

        private fun vehicleNameURL(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaVehicleName
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusVehicleName
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruVehicleName
                }

                else -> {
                    R.string.FTUEToyotaVehicleName
                }
            }
        }

        private fun vehicleSwitcherUrl(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaSwitcherLayout
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusSwitcherLayout
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruSwitcherLayout
                }

                else -> {
                    R.string.FTUEToyotaSwitcherLayout
                }
            }
        }

        private fun vehicleRemoteUrl(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaRemote
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusRemote
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruRemote
                }

                else -> {
                    R.string.FTUEToyotaRemote
                }
            }
        }

        private fun vehicleRemoteLayoutUrl(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaRemoteLayout
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusRemoteLayout
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruRemoteLayout
                }

                else -> {
                    R.string.FTUEToyotaRemoteLayout
                }
            }
        }

        private fun vehicleStatusUrl(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaStatus
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusStatus
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruStatus
                }

                else -> {
                    R.string.FTUEToyotaStatus
                }
            }
        }

        private fun vehicleStatusLayoutUrl(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaStatusLayout
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusStatusLayout
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruStatusLayout
                }

                else -> {
                    R.string.FTUEToyotaStatusLayout
                }
            }
        }

        private fun vehicleHealthUrl(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaHealth
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusHealth
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruHealth
                }

                else -> {
                    R.string.FTUEToyotaHealth
                }
            }
        }

        private fun vehicleHealthLayoutUrl(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaHealthLayout
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusHealthLayout
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruHealthLayout
                }

                else -> {
                    R.string.FTUEToyotaHealthLayout
                }
            }
        }

        private fun vehicleInfoUrl(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaInfo
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusInfo
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruInfo
                }

                else -> {
                    R.string.FTUEToyotaInfo
                }
            }
        }

        private fun vehicleInfoLayoutUrl(): Int {
            return when (buildWrapper.appBrand()) {
                Brand.TOYOTA.appBrand -> {
                    R.string.FTUEToyotaInfoLayout
                }

                Brand.LEXUS.appBrand -> {
                    R.string.FTUELexusInfoLayout
                }

                Brand.SUBARU.appBrand -> {
                    R.string.FTUESubaruInfoLayout
                }

                else -> {
                    R.string.FTUEToyotaInfoLayout
                }
            }
        }
    }
