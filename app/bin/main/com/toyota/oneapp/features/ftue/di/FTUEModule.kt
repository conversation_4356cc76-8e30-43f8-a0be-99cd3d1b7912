package com.toyota.oneapp.features.ftue.di

import com.toyota.oneapp.features.ftue.application.FTUELogic
import com.toyota.oneapp.features.ftue.application.FTUEUseCase
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class FTUEModule {
    @Binds
    abstract fun provideFTUELogic(fTUELogic: FTUELogic): FTUEUseCase
}
