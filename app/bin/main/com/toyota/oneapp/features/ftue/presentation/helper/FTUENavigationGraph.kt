package com.toyota.oneapp.features.ftue.presentation.helper

import android.os.Bundle
import androidx.compose.foundation.background
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.ftue.presentation.FTUEInitialScreen
import com.toyota.oneapp.features.ftue.presentation.FTUERevisitScreen
import com.toyota.oneapp.features.ftue.presentation.FTUEToolTipScreen
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel

@Composable
fun FTUENavigationGraph(
    navController: NavHostController,
    startDestination: String = OAScreen.FTUEInitialScreen.route,
    oneAppPreferenceModel: OneAppPreferenceModel,
    userInitiatedTour: Boolean,
    extras: Bundle?,
) {
    NavHost(
        navController,
        startDestination = startDestination,
        modifier = Modifier.background(AppTheme.colors.tertiary15),
    ) {
        composable(route = OAScreen.FTUEInitialScreen.route) {
            FTUEInitialScreen(
                navHostController = navController,
                userInitiatedTour = userInitiatedTour,
            )
        }
        composable(route = OAScreen.FTUE.route) {
            FTUEToolTipScreen(
                navHostController = navController,
            )
        }
        composable(route = OAScreen.SkipForNowScreen.route) {
            SkipForNowScreen(
                oneAppPreferenceModel = oneAppPreferenceModel,
                extras = extras,
            )
        }
        composable(route = OAScreen.FTUERevisitScreen.route) {
            FTUERevisitScreen(
                oneAppPreferenceModel = oneAppPreferenceModel,
                extras = extras,
            )
        }
    }
}
