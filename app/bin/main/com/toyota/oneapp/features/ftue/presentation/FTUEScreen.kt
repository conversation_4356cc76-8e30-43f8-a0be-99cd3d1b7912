package com.toyota.oneapp.features.ftue.presentation

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.ftue.application.FTUEState
import com.toyota.oneapp.features.ftue.constants.FTUELayouts
import com.toyota.oneapp.features.ftue.domain.model.FTUE
import com.toyota.oneapp.features.ftue.domain.model.TabLayoutResource
import com.toyota.oneapp.features.ftue.presentation.extension.appendToImageBaseUrl
import com.toyota.oneapp.features.ftue.presentation.extension.appendToVideoBaseUrl
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w
import com.toyota.oneapp.features.ftue.presentation.helper.FTUEImageLoader
import com.toyota.oneapp.features.ftue.presentation.helper.FTUETabLayout
import com.toyota.oneapp.features.ftue.presentation.helper.SwitcherLayout
import kotlinx.coroutines.delay

@Composable
fun FTUEToolTipScreen(navHostController: NavHostController) {
    val currentStep = remember { mutableStateOf(1) }

    Box(modifier = Modifier.fillMaxSize()) {
        when (currentStep.value) {
            1 -> {
                BaseToolTip()
                DelayTransition(currentStep, delayMillis = 1000L) {
                    currentStep.value = 2
                }
            }
            2 -> ToolTip(navHostController)
        }
    }
}

@Composable
fun BaseToolTip() {
    val viewModel = hiltViewModel<FTUEViewModel>()

    val fTUEState = viewModel.fTUEState.collectAsState()

    if (fTUEState.value is FTUEState.Success) {
        val homeData = (fTUEState.value as FTUEState.Success).data.ftueInitials.homeLayout

        val homeLayout = stringResource(id = homeData).appendToImageBaseUrl()

        FTUEImageLoader(homeLayout, contentDescription = stringResource(id = R.string.ftueImage))
    }
}

@Composable
fun GreyLayout() {
    BaseToolTip()
    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.6f)),
    )
}

@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun ToolTip(navHostController: NavHostController) {
    val viewModel = hiltViewModel<FTUEViewModel>()

    val fTUEState = viewModel.fTUEState.collectAsState()

    val toolTipLists: ArrayList<ArrayList<FTUE>> = arrayListOf()

    if (fTUEState.value is FTUEState.Success) {
        toolTipLists.addAll((fTUEState.value as FTUEState.Success).data.ftue)
    }

    val currentStepIndex = remember { mutableStateOf(0) }
    val currentFTUEStepSubIndex = remember { mutableStateOf(0) }
    val isVehicleInfoLayout = remember { mutableStateOf(false) }

    val finishFTUE: () -> Unit = {
        navHostController.navigate(OAScreen.FTUERevisitScreen.route)
    }

    // Function to move to the next FTUE
    val moveToNextStep: () -> Unit = {
        if (isVehicleInfoLayout.value) {
            finishFTUE()
        } else {
            if (currentFTUEStepSubIndex.value < toolTipLists[currentStepIndex.value].size - 1) {
                currentFTUEStepSubIndex.value += 1
            } else {
                currentStepIndex.value = (currentStepIndex.value + 1) % toolTipLists.size
                currentFTUEStepSubIndex.value = 0
            }
        }
    }

    LaunchedEffect(currentStepIndex.value) {
        while (true) {
            val currentList = toolTipLists[currentStepIndex.value]
            if (!currentList[currentFTUEStepSubIndex.value].isVideoLayout) {
                delay(200L)
                moveToNextStep()
            } else {
                break
            }
        }
    }

    GreyLayout()
    Box(modifier = Modifier.fillMaxSize()) {
        val currentFTUEList = toolTipLists[currentStepIndex.value]
        for (index in 0..currentFTUEStepSubIndex.value) {
            val currentFTUEStep = currentFTUEList[index]

            if (currentFTUEStep.isVideoLayout) {
                if (currentFTUEStep.item == FTUELayouts.VEHICLE_SWITCHER) {
                    Box(
                        Modifier
                            .padding(start = 16.w(), end = 16.w(), top = 202.h(), bottom = 50.h())
                            .clip(RoundedCornerShape(30.dp)),
                    ) {
                        SwitcherLayout(
                            currentStepIndex.value,
                            moveToNextStep,
                            stringResource(id = currentFTUEStep.urlPath).appendToVideoBaseUrl(),
                            finishFTUE,
                            viewModel,
                        )
                    }
                } else {
                    var tabLayoutResource =
                        TabLayoutResource(
                            title = R.string.remoteConnect,
                            note = R.string.remoteConnectNote,
                            ctaText = R.string.Common_next,
                            analyticsEventParam = AnalyticsEventParam.FTUE_REMOTE_NEXT_CTA,
                        )

                    when (currentFTUEStep.item) {
                        FTUELayouts.VEHICLE_INFO_LAYOUT -> {
                            isVehicleInfoLayout.value = true
                            tabLayoutResource =
                                TabLayoutResource(
                                    title = R.string.vehicleInfo,
                                    note = R.string.vehicleInfoNote,
                                    ctaText = R.string.ftueInfoDone,
                                    analyticsEventParam = AnalyticsEventParam.FTUE_VEHICLE_INFO_DONE_CTA,
                                )
                        }
                        FTUELayouts.VEHICLE_STATUS_LAYOUT -> {
                            tabLayoutResource =
                                TabLayoutResource(
                                    title = R.string.vehicleStatus,
                                    note = R.string.vehicleStatusNote,
                                    ctaText = R.string.ftueNext,
                                    analyticsEventParam = AnalyticsEventParam.FTUE_STATUS_NEXT_CTA,
                                )
                        }
                        FTUELayouts.VEHICLE_HEALTH_LAYOUT -> {
                            tabLayoutResource =
                                TabLayoutResource(
                                    title = R.string.health,
                                    note = R.string.healthNote,
                                    ctaText = R.string.ftueNext,
                                    analyticsEventParam = AnalyticsEventParam.FTUE_HEALTH_NEXT_CTA,
                                )
                        }
                        FTUELayouts.VEHICLE_REMOTE_LAYOUT -> {
                            tabLayoutResource =
                                TabLayoutResource(
                                    title = R.string.remoteConnect,
                                    note = R.string.remoteConnectNote,
                                    ctaText = R.string.ftueNext,
                                    analyticsEventParam = AnalyticsEventParam.FTUE_REMOTE_NEXT_CTA,
                                )
                        }
                        else -> {}
                    }
                    Box(
                        Modifier
                            .padding(
                                start = 16.w(),
                                end = 16.w(),
                                top = 277.h(),
                                bottom =
                                    dimensionResource(
                                        id = R.dimen.ftue_screen_bottom_padding,
                                    ),
                            )
                            .clip(RoundedCornerShape(15.dp)),
                    ) {
                        FTUETabLayout(
                            viewModel,
                            currentStepIndex.value,
                            moveToNextStep,
                            stringResource(id = currentFTUEStep.urlPath).appendToVideoBaseUrl(),
                            tabLayoutResource,
                            finishFTUE,
                        )
                    }
                }
            } else {
                Box(modifier = Modifier.layoutId(AccessibilityId.FTUE_IMAGE)) {
                    FTUEImageLoader(
                        stringResource(id = currentFTUEStep.urlPath).appendToImageBaseUrl(),
                        contentDescription = stringResource(id = R.string.ftueImage),
                    )
                }
            }
        }
    }
}

@Composable
fun DelayTransition(
    currentStep: MutableState<Int>,
    delayMillis: Long,
    action: () -> Unit,
) {
    LaunchedEffect(currentStep.value) {
        delay(delayMillis)
        action()
    }
}
