package com.toyota.oneapp.features.ftue.presentation.helper

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.ftue.domain.model.TabLayoutResource
import com.toyota.oneapp.features.ftue.presentation.FTUEViewModel
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w

@Composable
fun FTUETabLayout(
    viewModel: FTUEViewModel,
    layoutIndex: Int,
    onNextStep: () -> Unit,
    url: String,
    tabLayoutResource: TabLayoutResource,
    finishFTUE: () -> Unit,
) {
    val tabLayoutIndex = layoutIndex + 1

    val buttonCTAText = stringResource(id = tabLayoutResource.ctaText)

    val next = stringResource(id = R.string.Common_next)

    val buttonCtaAccessibility =
        if (buttonCTAText.contains(
                next,
            )
        ) {
            AccessibilityId.FTUE_NEXT_BUTTON_CTA
        } else {
            AccessibilityId.FTUE_DONE_BUTTON_CTA
        }

    Box(
        Modifier
            .background(color = Color.White)
            .height(500.h()),
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(Modifier.height(dimensionResource(id = R.dimen.ftue_tab_text_top_padding)))
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier =
                    Modifier
                        .padding(horizontal = 16.w())
                        .fillMaxWidth(),
            ) {
                Box(
                    modifier =
                        Modifier
                            .width(48.w())
                            .height(48.h())
                            .clip(CircleShape)
                            .background(AppTheme.colors.tertiary12)
                            .wrapContentSize(Alignment.Center)
                            .clickable(onClick = finishFTUE),
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_close),
                        contentDescription = stringResource(id = R.string.ftueCloseIconDescription),
                        tint = AppTheme.colors.button02a,
                        modifier = Modifier.layoutId(AccessibilityId.FTUE_CLOSE_ICON_CTA),
                    )
                }
                OASubHeadLine3TextView(
                    text = stringResource(id = tabLayoutResource.title),
                    color = AppTheme.colors.tertiary03,
                    textAlign = TextAlign.Center,
                    modifier =
                        Modifier
                            .padding(top = 10.h(), start = 15.w(), end = 15.w())
                            .width(185.w())
                            .layoutId(AccessibilityId.FTUE_TITLE_TEXT),
                )
                OACallOut1TextView(
                    text = "$tabLayoutIndex ${stringResource(id = R.string.ftueIndexSize)}",
                    color = AppTheme.colors.tertiary05,
                    textAlign = TextAlign.End,
                    modifier =
                        Modifier
                            .width(73.w())
                            .layoutId(AccessibilityId.FTUE_INDEX_TEXT),
                )
            }
            Spacer(Modifier.height(dimensionResource(id = R.dimen.ftue_tab_text_top_padding)))
            OACallOut1TextView(
                text = stringResource(id = tabLayoutResource.note),
                color = AppTheme.colors.tertiary05,
                modifier =
                    Modifier
                        .padding(bottom = 4.h(), start = 16.w(), end = 16.w())
                        .layoutId(AccessibilityId.FTUE_NOTE_TEXT),
                textAlign = TextAlign.Center,
            )
            Box(
                Modifier
                    .size(287.w(), 240.h())
                    .layoutId(AccessibilityId.FTUE_VIDEO),
            ) {
                VideoPlayer(url)
            }
            Spacer(modifier = Modifier.height(6.h()))
            Box(modifier = Modifier.fillMaxSize()) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.align(Alignment.BottomCenter),
                ) {
                    PrimaryButton02(
                        text = buttonCTAText,
                        modifier =
                            Modifier
                                .wrapContentWidth()
                                .wrapContentHeight()
                                .align(Alignment.CenterHorizontally)
                                .layoutId(buttonCtaAccessibility),
                        click = {
                            onNextStep()
                            viewModel.logEventWithParameter(tabLayoutResource.analyticsEventParam)
                        },
                    )
                    Spacer(Modifier.height(32.h()))
                }
            }
        }
    }
}
