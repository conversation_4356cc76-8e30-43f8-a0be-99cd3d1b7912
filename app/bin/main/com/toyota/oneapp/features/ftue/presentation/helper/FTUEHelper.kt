package com.toyota.oneapp.features.ftue.presentation.helper

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.features.ftue.presentation.FTUEActivity
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel

object FTUEHelper {
    fun finishFTUE(
        context: Context,
        oneAppPreferenceModel: OneAppPreferenceModel,
        extras: Bundle? = null,
    ) {
        if (oneAppPreferenceModel.isFTUECompleted()) {
            val resultIntent = Intent()
            (context as FTUEActivity).setResult(FTUEActivity.USER_COMPLETED_TOUR, resultIntent)
            context.finish()
        } else {
            context.startActivity(
                Intent(context, OADashboardActivity::class.java).apply {
                    extras?.let { this.putExtras(it) }
                },
            )
            oneAppPreferenceModel.firstTUECompleted()
            (context as FTUEActivity).finish()
        }
    }
}
