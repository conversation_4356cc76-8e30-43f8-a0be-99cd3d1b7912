/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeInfoItem
import com.toyota.oneapp.features.chargehistory.domain.model.DateModel
import com.toyota.oneapp.features.chargehistory.domain.model.PriceDetail
import com.toyota.oneapp.features.chargehistory.presentation.AddressCard
import com.toyota.oneapp.features.chargehistory.presentation.InfoItem
import com.toyota.oneapp.features.chargehistory.presentation.TotalAmountCard
import com.toyota.oneapp.features.publiccharging.domain.model.CdrDetailModel
import com.toyota.oneapp.features.publiccharging.domain.model.toInfoItemsList
import com.toyota.oneapp.ui.components.OADateLayout
import com.toyota.oneapp.ui.components.OAFullScreenBottomSheetLayout
import com.toyota.oneapp.ui.theme.AppTheme
import com.toyota.oneapp.util.AccessibilityId
import com.toyota.oneapp.util.testTagID

@Composable
fun CdrDetailScreen(
    cdrDetailModel: CdrDetailModel?,
    navController: NavHostController,
    modifier: Modifier = Modifier,
) {
    CdrDetailContent(
        cdrDetailModel = cdrDetailModel,
        modifier = modifier,
        onBack = { navController.popBackStack() },
    )
}

@Composable
fun CdrDetailContent(
    cdrDetailModel: CdrDetailModel?,
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
) {
    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tile01,
        screenTitle = stringResource(id = R.string.charge_details),
        testTagId = AccessibilityId.ID_CHARGE_DETAILS_BACK_BTN,
        onBack = { onBack() },
        modifier =
            modifier
                .padding(vertical = 8.dp, horizontal = 16.dp),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(vertical = 16.dp),
        ) {
            cdrDetailModel?.dateModel?.let {
                OADateLayout(
                    dateModel = it,
                    modifier =
                        Modifier
                            .width(56.dp)
                            .height(53.dp)
                            .align(Alignment.CenterHorizontally)
                            .testTagID(AccessibilityId.ID_CHARGE_SESSION_DATE_LAYOUT)
                            .background(
                                color = AppTheme.colors.secondary01,
                                shape = RoundedCornerShape(10.dp),
                            ),
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Convert CdrDetailModel to ChargeSessionModel-like structure for AddressCard
            val addressCardModel = cdrDetailModel?.let { model ->
                object {
                    val iconRes = model.iconRes
                    val locationType = model.locationType
                    val address = model.address
                    val displayDate = model.displayDate
                    val timeRange = model.timeRange
                    val duration = model.duration
                }
            }

            AddressCard(uiModel = addressCardModel)

            Spacer(modifier = Modifier.height(8.dp))

            // Display CDR info items
            val chargeInfoItemList = cdrDetailModel?.toInfoItemsList() ?: emptyList()
            chargeInfoItemList.forEachIndexed { index, infoItem ->
                InfoItem(
                    infoItem = infoItem,
                    canShowDivider = index != chargeInfoItemList.size - 1,
                ) {
                    // No find out more action for CDR details
                }
            }

            // Display total amount if available
            cdrDetailModel?.priceDetail?.let {
                TotalAmountCard(
                    value = it.totalAmount,
                    modifier = Modifier.padding(top = 8.dp),
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun CdrDetailContentPreview() {
    val cdrDetailModel =
        CdrDetailModel(
            iconRes = R.drawable.ic_ev_public,
            locationType = "Public",
            address = "ChargePoint Station, 123 Main St, Frisco, TX 75036",
            displayDate = "Today",
            dateModel = DateModel("15", "Jan", "15, Jan 2025"),
            timeRange = "2:30 PM - 3:45 PM",
            duration = "1 hr 15 min",
            detailedDuration = "1 hr 15 min 30 sec",
            totalEnergy = "25.450 kWh",
            averagePower = "20.5 kW",
            maxPower = "22.0 kW",
            priceDetail =
                PriceDetail(
                    digits = "",
                    totalAmount = "$12.50",
                ),
            authMethod = "RFID",
            connectorType = "DCFast",
            operatorName = "ChargePoint",
        )
    
    AppTheme {
        CdrDetailContent(
            cdrDetailModel = cdrDetailModel,
            onBack = {},
        )
    }
}
