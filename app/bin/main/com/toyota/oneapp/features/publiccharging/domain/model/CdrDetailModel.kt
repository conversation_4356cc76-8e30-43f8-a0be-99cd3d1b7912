/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.domain.model

import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeInfoItem
import com.toyota.oneapp.features.chargehistory.domain.model.DateModel
import com.toyota.oneapp.features.chargehistory.domain.model.PriceDetail
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CdrSessionResponse
import com.toyota.oneapp.util.AccessibilityId
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

@Parcelize
data class CdrDetailModel(
    @DrawableRes val iconRes: Int?,
    val locationType: String,
    val address: String,
    val displayDate: String,
    val dateModel: DateModel?,
    val timeRange: String,
    val duration: String,
    val detailedDuration: String,
    val totalEnergy: String,
    val averagePower: String,
    val maxPower: String,
    val priceDetail: PriceDetail?,
    val authMethod: String?,
    val connectorType: String?,
    val operatorName: String?,
) : Parcelable

fun CdrSessionResponse.toCdrDetailModel(): CdrDetailModel? {
    val cdrDetails = this.payload?.cdrDetails ?: return null
    val cdrData = cdrDetails.data ?: return null
    val location = cdrData.location
    val evse = location?.evses?.firstOrNull()
    val connector = evse?.connectors?.firstOrNull()
    
    // Parse dates
    val startDateTime = cdrData.startDateTime?.let { parseDateTime(it) }
    val stopDateTime = cdrData.stopDateTime?.let { parseDateTime(it) }
    
    // Calculate duration
    val duration = calculateDuration(startDateTime, stopDateTime)
    val timeRange = formatTimeRange(startDateTime, stopDateTime)
    
    // Format date
    val (displayDate, dateModel) = formatDisplayDate(startDateTime)
    
    // Format address
    val address = formatAddress(location)
    
    // Format energy and power values
    val totalEnergy = cdrData.totalEnergy?.let { "${String.format("%.3f", it)} kWh" } ?: "N/A"
    val averagePower = cdrData.averagePower?.let { "${String.format("%.1f", it)} kW" } ?: "N/A"
    val maxPower = cdrData.maxPower?.let { "${String.format("%.1f", it)} kW" } ?: "N/A"
    
    // Format price
    val priceDetail = if (cdrData.totalCost != null && cdrData.totalCost >= 0.50) {
        PriceDetail(
            digits = "", // CDR doesn't have payment method digits
            totalAmount = cdrData.formattedTotalCost
        )
    } else {
        PriceDetail(
            digits = "",
            totalAmount = "FREE"
        )
    }
    
    return CdrDetailModel(
        iconRes = R.drawable.ic_ev_public, // Use public charging icon
        locationType = "Public",
        address = address,
        displayDate = displayDate,
        dateModel = dateModel,
        timeRange = timeRange,
        duration = duration.first,
        detailedDuration = duration.second,
        totalEnergy = totalEnergy,
        averagePower = averagePower,
        maxPower = maxPower,
        priceDetail = priceDetail,
        authMethod = cdrData.authMethod,
        connectorType = connector?.friendlyName,
        operatorName = location?.operator?.name,
    )
}

fun CdrDetailModel.toInfoItemsList(): List<ChargeInfoItem> {
    val infoItemList = arrayListOf<ChargeInfoItem>()
    
    infoItemList.addAll(
        listOf(
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.guest_time,
                value = timeRange,
                otherAccessibility = AccessibilityId.ID_TIME_INFO_ITEM,
            ),
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.duration,
                value = duration,
                otherAccessibility = AccessibilityId.ID_DURATION_INFO_ITEM,
            ),
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.energy_used,
                value = totalEnergy,
                otherAccessibility = AccessibilityId.ID_ENERGY_USED_INFO_ITEM,
            ),
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.average_power,
                value = averagePower,
                otherAccessibility = "ID_AVERAGE_POWER_INFO_ITEM",
            ),
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.max_power,
                value = maxPower,
                otherAccessibility = "ID_MAX_POWER_INFO_ITEM",
            ),
        ),
    )
    
    // Add connector type if available
    connectorType?.let {
        infoItemList.add(
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.connector_type,
                value = it,
                otherAccessibility = "ID_CONNECTOR_TYPE_INFO_ITEM",
            )
        )
    }
    
    // Add operator name if available
    operatorName?.let {
        infoItemList.add(
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.operator,
                value = it,
                otherAccessibility = "ID_OPERATOR_INFO_ITEM",
            )
        )
    }
    
    return infoItemList
}

private fun parseDateTime(dateTimeString: String): Date? {
    return try {
        val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        format.parse(dateTimeString)
    } catch (e: Exception) {
        try {
            val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
            format.parse(dateTimeString)
        } catch (e2: Exception) {
            null
        }
    }
}

private fun calculateDuration(startDate: Date?, endDate: Date?): Pair<String, String> {
    if (startDate == null || endDate == null) {
        return Pair("N/A", "N/A")
    }
    
    val durationMs = endDate.time - startDate.time
    val hours = TimeUnit.MILLISECONDS.toHours(durationMs)
    val minutes = TimeUnit.MILLISECONDS.toMinutes(durationMs) % 60
    val seconds = TimeUnit.MILLISECONDS.toSeconds(durationMs) % 60
    
    val shortDuration = if (hours > 0) {
        "${hours} hr ${minutes} min"
    } else {
        "${minutes} min"
    }
    
    val detailedDuration = if (hours > 0) {
        "${hours} hr ${minutes} min ${seconds} sec"
    } else {
        "${minutes} min ${seconds} sec"
    }
    
    return Pair(shortDuration, detailedDuration)
}

private fun formatTimeRange(startDate: Date?, endDate: Date?): String {
    if (startDate == null || endDate == null) {
        return "N/A"
    }
    
    val timeFormat = SimpleDateFormat("h:mm a", Locale.getDefault())
    val startTime = timeFormat.format(startDate)
    val endTime = timeFormat.format(endDate)
    
    return "$startTime - $endTime"
}

private fun formatDisplayDate(date: Date?): Pair<String, DateModel?> {
    if (date == null) {
        return Pair("N/A", null)
    }
    
    val dayFormat = SimpleDateFormat("dd", Locale.getDefault())
    val monthFormat = SimpleDateFormat("MMM", Locale.getDefault())
    val fullDateFormat = SimpleDateFormat("dd, MMM yyyy", Locale.getDefault())
    
    val day = dayFormat.format(date)
    val month = monthFormat.format(date)
    val fullDate = fullDateFormat.format(date)
    
    val dateModel = DateModel(day, month, fullDate)
    
    // Determine display date (Today, Yesterday, or actual date)
    val today = Date()
    val yesterday = Date(today.time - TimeUnit.DAYS.toMillis(1))
    
    val displayDate = when {
        isSameDay(date, today) -> "Today"
        isSameDay(date, yesterday) -> "Yesterday"
        else -> fullDate
    }
    
    return Pair(displayDate, dateModel)
}

private fun isSameDay(date1: Date, date2: Date): Boolean {
    val cal1 = java.util.Calendar.getInstance().apply { time = date1 }
    val cal2 = java.util.Calendar.getInstance().apply { time = date2 }
    
    return cal1.get(java.util.Calendar.YEAR) == cal2.get(java.util.Calendar.YEAR) &&
            cal1.get(java.util.Calendar.DAY_OF_YEAR) == cal2.get(java.util.Calendar.DAY_OF_YEAR)
}

private fun formatAddress(location: com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CdrLocation?): String {
    if (location == null) return "Unknown Location"
    
    val parts = mutableListOf<String>()
    
    location.name?.let { parts.add(it) }
    location.address?.let { parts.add(it) }
    location.city?.let { parts.add(it) }
    location.province?.let { parts.add(it) }
    location.postalCode?.let { parts.add(it) }
    
    return if (parts.isNotEmpty()) {
        parts.joinToString(", ")
    } else {
        "Unknown Location"
    }
}
