package com.toyota.oneapp.features.vehicleswitcher.presentation

import android.content.Context
import com.toyota.oneapp.model.vehicle.VehicleInfo

sealed class VehicleSwitcherEvent {
    data class MakeDefault(val vin: String) : VehicleSwitcherEvent()

    object RemoveVehicle : VehicleSwitcherEvent()

    data class AddVehicle(val context: Context) : VehicleSwitcherEvent()

    data class SelectVehicle(val vehicleInfo: VehicleInfo) : VehicleSwitcherEvent()

    data class CheckSubscription(val vehicleInfo: VehicleInfo) : VehicleSwitcherEvent()
}
