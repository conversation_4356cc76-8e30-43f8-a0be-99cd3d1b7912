@file:OptIn(ExperimentalMaterialApi::class)

package com.toyota.oneapp.features.vehicleswitcher.presentation

import android.content.Intent
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.ButtonElevation
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.GetButtonHeightAsPerDeviceHeight
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.AddVehicleActivity
import com.toyota.oneapp.ui.dataconsent.viewmodels.MasterDataConsentViewModel

@ExperimentalMaterialApi
@Composable
fun SelectButton(
    backgroundColor: Color,
    textColor: Color,
    vehicleInfo: VehicleInfo,
    isCurrentVehicleSelected: Boolean,
    masterDataConsentViewModel: MasterDataConsentViewModel = hiltViewModel(),
) {
    val height = GetButtonHeightAsPerDeviceHeight(52.dp)

    Button(
        onClick = {
            if (!isCurrentVehicleSelected) {
                masterDataConsentViewModel.fetchReConsentEligibility(vehicleInfo)
            }
        },
        contentPadding =
            PaddingValues(
                start = 66.dp,
                end = 66.dp,
            ),
        enabled = !isCurrentVehicleSelected,
        modifier =
            Modifier.height(height)
                .clip(shape = RoundedCornerShape(50.dp))
                .background(color = backgroundColor)
                .testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_SELECT_BUTTON),
        colors =
            ButtonDefaults.buttonColors(
                backgroundColor = backgroundColor,
                contentColor = textColor,
                disabledBackgroundColor = backgroundColor,
            ),
        elevation = showElevation(),
    ) {
        OAButtonTextView(
            stringResource(R.string.select_vehicle),
            color = textColor,
        )
    }
}

@Composable
fun showElevation(): ButtonElevation {
    return ButtonDefaults.elevation(
        defaultElevation = 0.dp,
        disabledElevation = 0.dp,
    )
}

@ExperimentalMaterialApi
@Composable
fun MakeDefaultButton(
    vin: String,
    viewModel: VehicleSwitcherViewModel,
) {
    Button(
        onClick = {
            viewModel.onEvent(
                VehicleSwitcherEvent.MakeDefault(
                    vin = vin,
                ),
            )
        },
        modifier =
            Modifier
                .padding(horizontal = 4.dp, vertical = 4.dp)
                .testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_MAKE_DEFAULT_BUTTON)
                .height(42.dp),
        colors =
            ButtonDefaults.buttonColors(
                backgroundColor = AppTheme.colors.tertiary15,
                contentColor = AppTheme.colors.tertiary03,
            ),
        elevation =
            ButtonDefaults.elevation(
                defaultElevation = 0.dp,
                disabledElevation = 0.dp,
            ),
        border = BorderStroke(3.dp, color = AppTheme.colors.tertiary10),
        shape = RoundedCornerShape(100.dp),
    ) {
        OAButtonTextView(
            text = stringResource(R.string.vehicle_switcher_make_default),
            color = AppTheme.colors.tertiary03,
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
fun DefaultButton() {
    Box(
        contentAlignment = Alignment.Center,
        modifier =
            Modifier
                .clip(shape = RoundedCornerShape(100.dp))
                .background(color = AppTheme.colors.success02)
                .height(42.dp)
                .testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_DEFAULT_TEXT),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.padding(horizontal = 16.dp),
        ) {
            Image(
                modifier =
                    Modifier
                        .width(30.dp)
                        .height(20.dp),
                painter = painterResource(R.drawable.ic_tick),
                contentDescription = stringResource(R.string.tickIconDescription),
            )
            OAButtonTextView(
                text = stringResource(R.string.vehicle_switcher_default),
                color = AppTheme.colors.tertiary03,
                textAlign = TextAlign.Center,
            )
        }
    }
}

@ExperimentalMaterialApi
@Composable
fun AddVehicleIcon(viewModel: VehicleSwitcherViewModel) {
    val context = LocalContext.current
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier.fillMaxWidth(),
    ) {
        Box(
            modifier =
                Modifier
                    .width(50.dp)
                    .height(50.dp),
        )
        OASubHeadLine3TextView(
            text = stringResource(R.string.vehicles),
            textAlign = TextAlign.Center,
            color = AppTheme.colors.tertiary03,
            modifier = Modifier.testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_VEHICLE_TITLE_TEXT),
        )
        Image(
            modifier =
                Modifier
                    .width(64.dp)
                    .height(64.dp)
                    .testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_ADD_VEHICLE_BUTTON)
                    .padding(end = 16.dp)
                    .clickable {
                        viewModel.onEvent(VehicleSwitcherEvent.AddVehicle(context = context))
                        context.startActivity(Intent(context, AddVehicleActivity::class.java))
                    },
            painter = painterResource(R.drawable.ic_add_vehicle),
            contentDescription = stringResource(R.string.addVehicleContentDescription),
        )
    }
}
