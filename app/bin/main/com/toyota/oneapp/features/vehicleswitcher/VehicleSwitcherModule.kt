package com.toyota.oneapp.features.vehicleswitcher

import com.toyota.oneapp.features.vehicleswitcher.application.VehicleSwitcherLogic
import com.toyota.oneapp.features.vehicleswitcher.application.VehicleSwitcherUseCase
import com.toyota.oneapp.features.vehicleswitcher.dataaccess.repository.VehicleSwitcherDefaultRepository
import com.toyota.oneapp.features.vehicleswitcher.domain.respository.VehicleSwitcherRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class VehicleSwitcherModule {
    @Binds
    abstract fun provideVehicleSwitcherRepository(
        vehicleSwitcherDefaultRepository: VehicleSwitcherDefaultRepository,
    ): VehicleSwitcherRepository

    @Binds
    abstract fun provideVehicleSwitcherUseCase(vehicleSwitcherLogic: VehicleSwitcherLogic): VehicleSwitcherUseCase
}
