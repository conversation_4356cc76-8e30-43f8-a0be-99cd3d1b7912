/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

@file:OptIn(ExperimentalMaterialApi::class)

package com.toyota.oneapp.features.vehicleswitcher.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImagePainter
import coil.compose.SubcomposeAsyncImage
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.AddSpacingAsPerDeviceHeight
import com.toyota.oneapp.features.core.composable.CircleLoading
import com.toyota.oneapp.features.core.composable.GetButtonHeightAsPerDeviceHeight
import com.toyota.oneapp.features.core.composable.LoadVehicleImage
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.OATextTitle2ResizableTextView
import com.toyota.oneapp.features.core.composable.VehicleImageNotFound
import com.toyota.oneapp.features.core.composable.dynamicvin.DynamicVinComponent
import com.toyota.oneapp.features.core.composable.dynamicvin.TestTagIdPrefix.TestTagIdPrefixVehicleSwitcher
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@OptIn(
    ExperimentalMaterialApi::class,
)
@Composable
fun VehicleViewPager(
    pagerState: PagerState,
    vehicleList: MutableList<VehicleInfo>,
    viewModel: VehicleSwitcherViewModel,
    scaffoldState: ScaffoldState,
    isSecondaryVehicle: Boolean,
    onYCoordinatePositioned: (Dp) -> Unit,
) {
    LaunchedEffect(pagerState) {
        pagerState.scrollToPage(viewModel.getSelectedVehicleIndex())
    }

    HorizontalPager(
        verticalAlignment = Alignment.Top,
        state = pagerState,
        modifier =
            Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) { pageIndex ->
        val vehicleData = vehicleList[pageIndex]

        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            LoadVehicleImage(
                url = vehicleData.image,
                accessibilityId = AccessibilityId.ID_VEHICLE_SWITCHER_DISPLAY_IMAGE,
                contentDescription = R.string.switcherVehicleImageDescription,
                showImageOnSmallDevices = true,
            )
            AddSpacingAsPerDeviceHeight(defaultSpacing = 10.dp)

            if (!isSecondaryVehicle) {
                if (isPreferredVehicle(vehicleData.preferred)) {
                    DefaultButton()
                } else {
                    MakeDefaultButton(vehicleData.vin, viewModel)
                }
            }
            AddSpacingAsPerDeviceHeight(defaultSpacing = 20.dp)

            Box(
                contentAlignment = Alignment.Center,
            ) {
                OASubHeadLine1TextView(
                    text = vehicleData.modelYear ?: " - ",
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .align(Alignment.Center)
                            .testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_MODEL_YEAR_TEXT),
                )
            }
            AddSpacingAsPerDeviceHeight(defaultSpacing = 8.dp)

            val height = GetButtonHeightAsPerDeviceHeight(50.dp)
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(height),
                contentAlignment = Alignment.Center,
            ) {
                // Todo Need to confirm about "-" for null values
                OATextTitle2ResizableTextView(
                    text = vehicleData.modelName ?: " - ",
                    color = AppTheme.colors.tertiary03,
                    modifier =
                        Modifier
                            .padding(horizontal = 10.dp)
                            .testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_MODEL_NAME_TEXT),
                    textAlign = TextAlign.Center,
                    maxLines = 2,
                    lineHeight = 44.sp,
                )
            }
            AddSpacingAsPerDeviceHeight(defaultSpacing = 8.dp)

            DynamicVinComponent(
                scaffoldState = scaffoldState,
                vin = vehicleData.vin,
                onYCoordinatePositioned = onYCoordinatePositioned,
                testTagIdPrefix = TestTagIdPrefixVehicleSwitcher,
            )

            AddSpacingForVehicleIndicator()
        }
    }
}

@ExperimentalMaterialApi
@Composable
fun VehicleIndicator(
    selectedIndex: Int,
    scope: CoroutineScope,
    pagerState: PagerState,
    vehicleList: MutableList<VehicleInfo>,
    indicatorSize: Dp = 10.dp,
) {
    val totalVehicles = vehicleList.size

    val listState = rememberLazyListState()

    val widthInPx = LocalDensity.current.run { indicatorSize.toPx() }

    val currentItem by remember {
        derivedStateOf {
            pagerState.currentPage
        }
    }

    LaunchedEffect(key1 = currentItem) {
        val viewportSize = listState.layoutInfo.viewportSize
        listState.animateScrollToItem(
            currentItem,
            (widthInPx / 2 - viewportSize.width / 2).toInt(),
        )
    }

    Box {
        Row {
            LazyRow(
                state = listState,
                modifier =
                    Modifier
                        .padding(5.dp)
                        .fillMaxWidth()
                        .wrapContentHeight(),
                horizontalArrangement = Arrangement.Center,
            ) {
                items(totalVehicles) { index ->
                    if (index == selectedIndex) {
                        Box(
                            modifier =
                                Modifier
                                    .size(70.dp)
                                    .clip(CircleShape)
                                    .background(color = AppTheme.colors.success02)
                                    .clickable {
                                        scope.launch {
                                            pagerState.animateScrollToPage(page = index)
                                        }
                                    },
                            contentAlignment = Alignment.Center,
                        ) {
                            LoadIndicatorVehicleImage(vehicleList[index].image)
                        }
                    } else {
                        Box(
                            modifier =
                                Modifier
                                    .size(70.dp)
                                    .clip(CircleShape)
                                    .clickable {
                                        scope.launch {
                                            pagerState.animateScrollToPage(page = index)
                                        }
                                    },
                            contentAlignment = Alignment.Center,
                        ) {
                            LoadIndicatorVehicleImage(vehicleList[index].image)
                        }
                    }

                    if (index != totalVehicles - 1) {
                        Spacer(modifier = Modifier.padding(horizontal = 10.dp))
                    }
                }
            }
        }
        Row(
            modifier =
                Modifier
                    .padding(5.dp)
                    .fillMaxWidth()
                    .height(70.dp)
                    .wrapContentHeight(),
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Spacer(
                Modifier
                    .width(70.dp)
                    .fillMaxHeight()
                    .clip(CircleShape)
                    .background(
                        brush =
                            Brush.horizontalGradient(
                                colors =
                                    listOf(
                                        AppTheme.colors.tertiary15,
                                        Color.Transparent,
                                    ),
                            ),
                    ),
            )
            Spacer(
                Modifier
                    .width(70.dp)
                    .fillMaxHeight()
                    .clip(CircleShape)
                    .background(
                        brush =
                            Brush.horizontalGradient(
                                colors =
                                    listOf(
                                        Color.Transparent,
                                        AppTheme.colors.tertiary15,
                                    ),
                            ),
                    ),
            )
        }
    }
}

@Composable
private fun LoadIndicatorVehicleImage(url: String) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier.testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_INDICATOR_IMAGE),
    ) {
        val contentDescription = R.string.switcherIndicatorImageDescription
        if (url.contains(Constants.ImageNotFound, ignoreCase = true)) {
            VehicleImageNotFound(contentDescription = contentDescription)
        } else {
            SubcomposeAsyncImage(
                model = url,
                contentDescription = stringResource(contentDescription),
            ) {
                when (painter.state) {
                    is AsyncImagePainter.State.Loading -> {
                        Box(contentAlignment = Alignment.Center) {
                            CircleLoading(indicatorSize = 20.dp)
                        }
                    }

                    is AsyncImagePainter.State.Error -> {
                        VehicleImageNotFound(contentDescription = contentDescription)
                    }

                    else -> {
                        SetIndicatorImage(painter)
                    }
                }
            }
        }
    }
}

@Composable
private fun SetIndicatorImage(painter: AsyncImagePainter) {
    Image(
        painter = painter,
        contentDescription =
            stringResource(
                R.string.switcherIndicatorImageDescription,
            ),
        modifier = Modifier.fillMaxSize(),
    )
}

@Composable
fun AddSpacingForVehicleIndicator() {
    val configuration = LocalConfiguration.current
    val screenHeightDp = configuration.screenHeightDp.dp
    val spacing =
        when {
            screenHeightDp > 707.dp -> 50.dp
            screenHeightDp in 569.dp..707.dp -> 15.dp
            else -> 5.dp
        }
    Spacer(modifier = Modifier.height(spacing))
}

@Composable
fun AddSpacingForVehicleImage() {
    val configuration = LocalConfiguration.current
    val screenHeightDp = configuration.screenHeightDp.dp
    val spacing =
        when {
            screenHeightDp > 707.dp -> 10.dp
            else -> 2.dp
        }
    Spacer(modifier = Modifier.height(spacing))
}
