package com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel

import com.toyota.oneapp.features.vehicleswitcher.domain.model.RemoveVehicleUIModel

data class RemoveVehicleResponse(
    val status: Status?,
)

data class Status(
    val messages: List<Message>?,
)

data class Message(
    val description: String?,
    val detailedDescription: String?,
    val responseCode: String?,
)

fun RemoveVehicleResponse.toUIModel(): RemoveVehicleUIModel {
    return RemoveVehicleUIModel(
        description = status?.messages?.first()?.description,
        detailedDescription = status?.messages?.first()?.detailedDescription,
        responseCode = status?.messages?.first()?.responseCode,
    )
}
