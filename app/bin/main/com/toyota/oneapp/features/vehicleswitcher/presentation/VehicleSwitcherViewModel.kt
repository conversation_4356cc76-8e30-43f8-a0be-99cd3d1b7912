package com.toyota.oneapp.features.vehicleswitcher.presentation

import android.content.BroadcastReceiver
import android.content.Context
import android.content.IntentFilter
import androidx.compose.material.ExperimentalMaterialApi
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.VEHICLE_SWITCHER_ADD_VEHICLE
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.VEHICLE_SWITCHER_FETCH_SUBSCRIPTION
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.VEHICLE_SWITCHER_SELECT_VEHICLE
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.application.DigitalKeyDownloadLogic
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyRevokeStatus
import com.toyota.oneapp.features.vehicleswitcher.application.LoadingState
import com.toyota.oneapp.features.vehicleswitcher.application.SubscriptionState
import com.toyota.oneapp.features.vehicleswitcher.application.VehicleSwitcherLogic
import com.toyota.oneapp.features.vehicleswitcher.application.VehicleSwitcherState
import com.toyota.oneapp.features.vehicleswitcher.domain.model.VehicleConfirmation
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@ExperimentalMaterialApi
@HiltViewModel
class VehicleSwitcherViewModel
    @Inject
    constructor(
        private val vehicleSwitcherLogic: VehicleSwitcherLogic,
        val applicationData: ApplicationData,
        val oneAppPreferenceModel: OneAppPreferenceModel,
        private val digitalKeyMopKeyUtils: DigitalMopKeyUtils,
        private val digitalKeyDownloadLogic: DigitalKeyDownloadLogic,
        private val digitalKeyLocalData: DigitalKeyLocalData,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        var selectedVehicle: VehicleInfo = VehicleInfo()

        private val _loadingState =
            MutableStateFlow<LoadingState>(
                value = LoadingState.Idle,
            )
        val loadingState = _loadingState.asStateFlow()

        private val _vehicleSwitcherState =
            MutableStateFlow<VehicleSwitcherState>(
                value = VehicleSwitcherState.Loading,
            )
        val vehicleSwitcherState = _vehicleSwitcherState.asStateFlow()

        private val _subscriptionState =
            MutableStateFlow<SubscriptionState>(
                value = SubscriptionState.Loading,
            )
        val subscriptionState = _subscriptionState.asStateFlow()

        private var deviceId = digitalKeyLocalData.getDeviceId()

        private var phoneNumber = digitalKeyLocalData.getPhoneNumber()

        private val _dkRevoke = MutableStateFlow(false)
        val dkRevoke = _dkRevoke.asStateFlow()

        private fun updateDkRevoke(newValue: Boolean) {
            _dkRevoke.value = newValue
        }

        private val _isRemoveVehicleEnabledState =
            MutableStateFlow(
                value = true,
            )
        val isRemoveVehicleEnabledState = _isRemoveVehicleEnabledState.asStateFlow()

        companion object {
            private val TAG = VehicleSwitcherViewModel::class.java.name
        }

        init {
            viewModelScope.launch {
                applicationData.getVehicleListState().collectLatest {
                    _vehicleSwitcherState.value =
                        VehicleSwitcherState.Success(
                            it,
                        )
                }
            }
        }

        fun getVinKeyInfo(): DKLib.MyKeyInfo? {
            applicationData.getSelectedVehicle()?.let {
                return digitalKeyDownloadLogic.getKeyInfo(it.vin)
            }
            return null
        }

        fun getSelectedVehicleIndex(): Int {
            var selectedIndex = applicationData.getVehicleList()?.indexOfFirst { it.vin == applicationData.getSelectedVehicle()?.vin }

            // use default vehicle index, if the selected vehicle is null
            if (selectedIndex == null || selectedIndex == -1) {
                selectedIndex = applicationData.getVehicleList()?.indexOfFirst { it.preferred == 1 }
            }

            if (selectedIndex == null || selectedIndex == -1) selectedIndex = 0
            return selectedIndex
        }

        fun updateDefaultVehicle(vin: String) {
            val requestBody =
                VehicleConfirmation(
                    guid = oneAppPreferenceModel.getGuid(),
                    preference = 1,
                    vin = vin,
                )
            viewModelScope.launch {
                try {
                    _loadingState.value = LoadingState.Loading
                    vehicleSwitcherLogic
                        .updatePreferredVehicle(vin = vin, body = requestBody)
                        .collect {
                            _vehicleSwitcherState.value = it
                            _loadingState.value = LoadingState.Idle
                        }
                } catch (e: Exception) {
                    _vehicleSwitcherState.value =
                        VehicleSwitcherState.Error(
                            errorMessage = e.message,
                            errorCode = null,
                        )
                    _loadingState.value = LoadingState.Idle
                }
            }
        }

        fun removeVehicle() {
            viewModelScope.launch {
                _loadingState.value = LoadingState.Loading
                updateDkRevoke(false)
                try {
                    vehicleSwitcherLogic
                        .removeVehicle(
                            selectedVehicle = selectedVehicle,
                        ).collect {
                            _loadingState.value =
                                when (it) {
                                    is VehicleSwitcherState.Success -> {
                                        _vehicleSwitcherState.value = it
                                        LoadingState.RemoveVehicleSuccess
                                    }

                                    is VehicleSwitcherState.Error -> {
                                        LoadingState.RemoveVehicleError(
                                            errorCode = it.errorCode,
                                            errorMessage = it.errorMessage,
                                        )
                                    }

                                    else -> {
                                        LoadingState.Idle
                                    }
                                }
                        }
                } catch (e: Exception) {
                    _loadingState.value = LoadingState.Idle
                }
            }
        }

        private fun revokeMopKey(
            keyInfoId: String,
            type: String,
            isOwner: Boolean = false,
        ) {
            _loadingState.value = LoadingState.Loading
            applicationData.getSelectedVehicle()?.let {
                viewModelScope.launch {
                    if (isOwner) {
                        digitalKeyLocalData.setIsTmpKeyInfoId(it.vin, keyInfoId)
                        digitalKeyMopKeyUtils.deactivateKey(keyInfoId)
                    } else {
                        digitalKeyDownloadLogic
                            .deleteDigitalKeyToken(
                                keyInfoId,
                                phoneNumber,
                                it.vin,
                                deviceId,
                                type,
                                inviteId = null,
                            ).collect { resource ->
                                _loadingState.value = LoadingState.Idle
                                resource.error?.let { status ->
                                    if (status.code == 204) {
                                        handleRevokeSuccess(
                                            type,
                                            keyInfoId,
                                            it,
                                        )
                                        // update this value to true so we can remove vehicle after dk removed.
                                        updateDkRevoke(true)
                                    } else {
                                        handleRevokeFailure()
                                    }
                                }
                            }
                    }
                }
            }
        }

        fun removeOwnerKeyFromCTP(
            type: String,
            inviteId: String,
        ) {
            viewModelScope.launch {
                applicationData.getSelectedVehicle()?.let {
                    digitalKeyDownloadLogic
                        .deleteDigitalKeyToken(
                            digitalKeyLocalData.getTmpKeyInfoId(it.vin),
                            phoneNumber,
                            it.vin,
                            deviceId,
                            type,
                            inviteId,
                        ).collect { resource ->
                            _loadingState.value = LoadingState.Idle
                            resource.error?.let { status ->
                                if (status.code == 204 || status.code == 400) {
                                    digitalKeyLocalData.setInviteSize(it.vin, 0)
                                    vehicleSwitcherLogic.removeKeyCurrentVehicle()
                                    updateDkRevoke(true)
                                } else {
                                    updateDkRevoke(false)
                                }
                            }
                            digitalKeyLocalData.removeTmpKeyInfoId(it.vin)
                        }
                }
            }
        }

        private fun handleRevokeFailure() {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.DK_REVOKE_KEY_FAILED,
            )
            hideProgress()
            DigitalMopKeyUtils.appendLog(
                "RevokeUser Failed",
                DigitalMopKeyUtils.DK_REVOKE_TAG,
                isDataDogRequired = true,
                isError = true,
            )
            // update this value to true for failure as well even if issue in dk removal we can remove vehicle.
            updateDkRevoke(true)
        }

        private fun handleRevokeSuccess(
            type: String,
            keyInfoId: String?,
            vehicleInfo: VehicleInfo,
        ) {
            when (type) {
                DigitalKeyRevokeStatus.PRIMARY.type -> {
                    digitalKeyLocalData.setInviteSize(vehicleInfo.vin, 0)
                    vehicleSwitcherLogic.removeKeyCurrentVehicle()
                    digitalKeyMopKeyUtils.syncAllKeys(oneAppPreferenceModel)
                }
                DigitalKeyRevokeStatus.PRIMARY_LUK.type -> {
                    keyInfoId?.let {
                        digitalKeyMopKeyUtils.deactivateKey(it)
                        digitalKeyMopKeyUtils.syncKey(
                            it,
                            oneAppPreferenceModel = oneAppPreferenceModel,
                        )
                    }
                }
            }
        }

        fun removePersonaKey(isOwner: Boolean = false) {
            digitalKeyMopKeyUtils.registerVehicleCancel()
            val keyInfo = getVinKeyInfo()
            keyInfo?.run {
                when (keyKind) {
                    DKLib.KeyKind.OWNER -> {
                        revokeMopKey(
                            keyInfoId,
                            DigitalKeyRevokeStatus.PRIMARY.type,
                            isOwner = isOwner,
                        )
                    }
                    DKLib.KeyKind.LIMITED -> {
                        revokeMopKey(
                            keyInfoId,
                            DigitalKeyRevokeStatus.PRIMARY_LUK.type,
                            isOwner = isOwner,
                        )
                    }
                    else -> {}
                }
            }
        }

        fun registerReceiver(
            context: Context,
            broadcastReceiver: BroadcastReceiver,
        ) {
            context.registerReceiver(
                broadcastReceiver,
                IntentFilter(DigitalMopKeyUtils.DK_FILTER),
                Context.RECEIVER_EXPORTED,
            )
        }

        fun unregisterReceiver(
            context: Context,
            broadcastReceiver: BroadcastReceiver,
        ) {
            try {
                context.unregisterReceiver(broadcastReceiver)
            } catch (e: IllegalArgumentException) {
                LogTool.e(TAG, "Receiver not registered", e)
            }
        }

        fun selectVehicle(vehicleData: VehicleInfo) {
            vehicleSwitcherLogic.selectVehicle(vehicleData = vehicleData)
            _vehicleSwitcherState.value =
                VehicleSwitcherState.Success(
                    applicationData.getVehicleList(),
                    resetTab = true,
                )
        }

        fun checkSubscriptionAvailableOrNot(selectedVehicleInfo: VehicleInfo) {
            selectedVehicle = selectedVehicleInfo
            viewModelScope.launch {
                try {
                    _loadingState.value = LoadingState.Loading
                    vehicleSwitcherLogic
                        .getSubscriptions(selectedVehicleInfo)
                        .collect {
                            _subscriptionState.value = it
                            _loadingState.value = LoadingState.Idle
                            vehicleSwitcherLogic.logFirebaseEventWithParameter(
                                VEHICLE_SWITCHER_FETCH_SUBSCRIPTION,
                            )
                        }
                } catch (e: Exception) {
                    _subscriptionState.value =
                        SubscriptionState.Error(
                            errorMessage = e.message,
                            errorCode = null,
                        )
                    _loadingState.value = LoadingState.Idle
                }
            }
        }

        fun isRemoveVehicleEnabled(vehicleInfo: VehicleInfo) {
            _isRemoveVehicleEnabledState.value =
                vehicleSwitcherLogic.isRemoveVehicleEnabled(
                    vehicleInfo,
                )
        }

        fun updateButtonSheetState() {
            _subscriptionState.value = SubscriptionState.Idle
        }

        fun showLoading() {
            _loadingState.value = LoadingState.Loading
        }

        fun dismissLoading() {
            _loadingState.value = LoadingState.Idle
        }

        fun isKeyAvailable(): Boolean {
            val keyInfo = getVinKeyInfo()
            return keyInfo?.run {
                (keyInfo.keyStatus == DKLib.KeyStatus.INSTALLED || keyInfo.keyStatus == DKLib.KeyStatus.REGISTERED) &&
                    keyInfo.keyKind == DKLib.KeyKind.OWNER
            } ?: run {
                false
            }
        }

        fun onEvent(event: VehicleSwitcherEvent) {
            when (event) {
                is VehicleSwitcherEvent.MakeDefault -> {
                    updateDefaultVehicle(vin = event.vin)
                }
                is VehicleSwitcherEvent.SelectVehicle -> {
                    selectVehicle(event.vehicleInfo)
                    vehicleSwitcherLogic.logFirebaseEventWithParameter(VEHICLE_SWITCHER_SELECT_VEHICLE)
                }
                is VehicleSwitcherEvent.AddVehicle -> {
                    vehicleSwitcherLogic.logFirebaseEventWithParameter(VEHICLE_SWITCHER_ADD_VEHICLE)
                }
                is VehicleSwitcherEvent.RemoveVehicle -> {
                    removeVehicle()
                }
                is VehicleSwitcherEvent.CheckSubscription -> {
                    checkSubscriptionAvailableOrNot(
                        event.vehicleInfo,
                    )
                }
            }
        }
    }
