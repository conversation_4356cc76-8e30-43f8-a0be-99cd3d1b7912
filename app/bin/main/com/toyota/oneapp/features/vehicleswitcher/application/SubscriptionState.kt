package com.toyota.oneapp.features.vehicleswitcher.application

import com.toyota.oneapp.features.vehicleswitcher.domain.model.SubscriptionsStatus

sealed class SubscriptionState {
    object Idle : SubscriptionState()

    object Loading : SubscriptionState()

    class Success(val data: SubscriptionsStatus) : SubscriptionState()

    class Error(val errorCode: String?, val errorMessage: String?) : SubscriptionState()
}
