package com.toyota.oneapp.features.vehicleswitcher.dataaccess.service

import com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel.ConfirmVehicleResponse
import com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel.RemoveVehicleResponse
import com.toyota.oneapp.features.vehicleswitcher.domain.model.RemoveVehicleInfo
import com.toyota.oneapp.features.vehicleswitcher.domain.model.VehicleConfirmation
import retrofit2.Response
import retrofit2.http.*

interface VehicleSwitcherApi {
    @POST("/oneapi/v1/preferred/vehicle")
    suspend fun confirmVehicle(
        @Header("VIN") vin: String?,
        @Body requestBody: VehicleConfirmation?,
    ): Response<ConfirmVehicleResponse?>

    @Headers("CONTENT-TYPE: application/json")
    @PUT("/oneapi/v1/vehicle-association/vehicle/sold")
    suspend fun removeVehicle(
        @Header("X-BRAND") brand: String,
        @Header("DATETIME") dateTime: String,
        @Body body: RemoveVehicleInfo,
        @Header("VIN") vin: String,
    ): Response<RemoveVehicleResponse?>
}
