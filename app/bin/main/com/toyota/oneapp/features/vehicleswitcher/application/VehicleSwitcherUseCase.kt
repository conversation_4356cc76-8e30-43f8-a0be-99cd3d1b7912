package com.toyota.oneapp.features.vehicleswitcher.application

import com.toyota.oneapp.features.vehicleswitcher.domain.model.Subscription
import com.toyota.oneapp.features.vehicleswitcher.domain.model.SubscriptionsStatus
import com.toyota.oneapp.features.vehicleswitcher.domain.model.TwentyOneMMRemoveVehicleResponseModel
import com.toyota.oneapp.features.vehicleswitcher.domain.model.VehicleConfirmation
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

abstract class VehicleSwitcherUseCase {
    abstract fun removeVehicle(selectedVehicle: VehicleInfo): Flow<VehicleSwitcherState>

    abstract fun selectVehicle(vehicleData: VehicleInfo?)

    abstract fun getSubscriptions(vehicleInfo: VehicleInfo): Flow<SubscriptionState>

    abstract suspend fun updatePreferredVehicle(
        vin: String,
        body: VehicleConfirmation,
    ): Flow<VehicleSwitcherState>

    abstract suspend fun remove21MMVehicle(selectedVin: String): Flow<TwentyOneMMRemoveVehicleResponseModel?>

    abstract suspend fun updateVehicleStatusAfterMakeDefault(vin: String): MutableList<VehicleInfo>?

    abstract suspend fun isActiveSubscriptionAvailable(subscriptionResponse: Subscription): Flow<SubscriptionsStatus>

    abstract suspend fun updateVehicleListAfterRemove(selectedVehicle: VehicleInfo): Flow<MutableList<VehicleInfo>?>

    abstract fun logFirebaseEventWithParameter(event: String)

    abstract suspend fun getVehicleList(selectedVehicle: VehicleInfo): Flow<MutableList<VehicleInfo>?>

    abstract fun removeKeyCurrentVehicle()

    abstract fun isRemoveVehicleEnabled(vehicleInfo: VehicleInfo): Boolean
}
