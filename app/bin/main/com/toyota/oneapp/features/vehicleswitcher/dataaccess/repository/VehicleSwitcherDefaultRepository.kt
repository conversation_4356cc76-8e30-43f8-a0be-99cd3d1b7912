package com.toyota.oneapp.features.vehicleswitcher.dataaccess.repository

import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel.ConfirmVehicleResponse
import com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel.RemoveVehicleResponse
import com.toyota.oneapp.features.vehicleswitcher.dataaccess.service.VehicleSwitcherApi
import com.toyota.oneapp.features.vehicleswitcher.domain.model.RemoveVehicleRequestModel
import com.toyota.oneapp.features.vehicleswitcher.domain.model.VehicleConfirmation
import com.toyota.oneapp.features.vehicleswitcher.domain.respository.VehicleSwitcherRepository
import com.toyota.oneapp.model.subscriptionV2.VehicleSubscriptionResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.cy17plus.CY17PlusCoroutineServiceApi
import com.toyota.oneapp.network.interceptor.GrpcInterceptor
import com.toyota.oneapp.util.ToyotaConstants
import io.grpc.ManagedChannel
import io.grpc.okhttp.OkHttpChannelBuilder
import toyotaone.commonlib.log.LogTool
import java.io.ByteArrayInputStream
import java.security.KeyStore
import java.security.cert.CertificateFactory
import javax.inject.Inject
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManagerFactory
import kotlin.coroutines.CoroutineContext

class VehicleSwitcherDefaultRepository
    @Inject
    constructor(
        val service: VehicleSwitcherApi,
        private val cY17PlusService: CY17PlusCoroutineServiceApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : VehicleSwitcherRepository(errorParser, ioContext) {
        override suspend fun confirmPreferredVehicle(
            vin: String,
            vehicleConfirmationModel: VehicleConfirmation,
        ): Resource<ConfirmVehicleResponse?> {
            return makeApiCall {
                service.confirmVehicle(
                    vin = vin,
                    requestBody = vehicleConfirmationModel,
                )
            }
        }

        override suspend fun removeVehicle(body: RemoveVehicleRequestModel): Resource<RemoveVehicleResponse?> {
            return makeApiCall {
                service.removeVehicle(
                    vin = body.removeVehicle.vin,
                    brand = body.xBrand,
                    dateTime = body.dateTime,
                    body = body.removeVehicle,
                )
            }
        }

        override suspend fun fetchVehicleSubscriptions(vehicle: VehicleInfo): Resource<VehicleSubscriptionResponse?> {
            return makeApiCall {
                cY17PlusService.getVehicleSubscriptions(
                    vin = vehicle.vin,
                    brand = vehicle.brand,
                    region = vehicle.region,
                    generation = vehicle.generation,
                    asiCode = vehicle.asiCode,
                    hwType = vehicle.hwType,
                    dateTime = System.currentTimeMillis(),
                )
            }
        }

        override fun generateRPCManagedChannel(
            url: String?,
            port: Int,
            certData: ByteArray,
            grpcInterceptor: GrpcInterceptor,
        ): ManagedChannel? {
            val channelBuilder: OkHttpChannelBuilder = OkHttpChannelBuilder.forAddress(url, port)
            channelBuilder.intercept(grpcInterceptor)
            if (!BuildConfig.IS_CERTIFICATION_REQUIRED.toBoolean()) {
                return channelBuilder.build()
            }
            var sslSocketFactory: SSLSocketFactory? = null
            try {
                val inputStream = ByteArrayInputStream(certData)
                val keyStore =
                    KeyStore.getInstance(KeyStore.getDefaultType())
                keyStore.load(null, null)
                val cf =
                    CertificateFactory.getInstance(ToyotaConstants.GRPC_CERT_TYPE)
                keyStore.setCertificateEntry(
                    ToyotaConstants.GRPC_CERT_ALIAS,
                    cf.generateCertificate(inputStream),
                )
                inputStream.close()
                val keyManagerFactory =
                    KeyManagerFactory.getInstance(ToyotaConstants.KEY_MANAGER_ALGO)
                keyManagerFactory.init(keyStore, ToyotaConstants.EMPTY_STRING.toCharArray())
                val trustManagerFactory =
                    TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
                trustManagerFactory.init(keyStore)
                val sslContext = SSLContext.getInstance(ToyotaConstants.SSL_PROTOCOL)
                sslContext.init(
                    keyManagerFactory.keyManagers,
                    trustManagerFactory.trustManagers,
                    null,
                )
                sslSocketFactory = sslContext.socketFactory
            } catch (e: Exception) {
                LogTool.d(VehicleSwitcherDefaultRepository::class.java.simpleName, e.message)
            }

            return sslSocketFactory?.let {
                channelBuilder.sslSocketFactory(it).build()
            }
        }
    }
