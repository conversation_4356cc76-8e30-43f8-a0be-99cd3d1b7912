package com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel

import com.toyota.oneapp.features.vehicleswitcher.domain.model.MakeDefaultResponse

data class ConfirmVehicleResponse(
    val payload: String?,
    val status: String?,
    val code: Int?,
    val version: Int?,
    val message: String?,
    val errors: List<String>?,
)

fun ConfirmVehicleResponse.toUIModel(): MakeDefaultResponse {
    return MakeDefaultResponse(
        payload = payload,
        status = status,
    )
}
