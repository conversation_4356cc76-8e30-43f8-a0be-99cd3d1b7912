package com.toyota.oneapp.features.vehicleswitcher.application

import com.toyota.ctp.v1.VehicleRegistrationServiceGrpc
import com.toyota.ctp.v1.VehicleRegistrationServiceOuterClass
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.features.dashboard.dashboard.domain.repository.DashboardRepository
import com.toyota.oneapp.features.vehicleswitcher.application.VehicleSwitcherState.Success
import com.toyota.oneapp.features.vehicleswitcher.domain.model.RemoveVehicleInfo
import com.toyota.oneapp.features.vehicleswitcher.domain.model.RemoveVehicleRequestModel
import com.toyota.oneapp.features.vehicleswitcher.domain.model.Subscription
import com.toyota.oneapp.features.vehicleswitcher.domain.model.SubscriptionsStatus
import com.toyota.oneapp.features.vehicleswitcher.domain.model.TwentyOneMMRemoveVehicleResponseModel
import com.toyota.oneapp.features.vehicleswitcher.domain.model.VehicleConfirmation
import com.toyota.oneapp.features.vehicleswitcher.domain.respository.VehicleSwitcherRepository
import com.toyota.oneapp.features.vehicleswitcher.presentation.utils.PreferredVehicleStatus
import com.toyota.oneapp.features.vehicleswitcher.presentation.utils.SubscriptionStatus
import com.toyota.oneapp.features.vehicleswitcher.presentation.utils.SubscriptionTerm
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.subscriptionV2.toUIModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.NetworkStatus
import com.toyota.oneapp.network.dataprovider.NetworkDataProvider
import com.toyota.oneapp.network.interceptor.GrpcInterceptor
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants
import io.grpc.stub.StreamObserver
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flow
import toyotaone.commonlib.toast.ToastUtil.getContext
import javax.inject.Inject

class VehicleSwitcherLogic
    @Inject
    constructor(
        private val repository: VehicleSwitcherRepository,
        private val dashboardRepository: DashboardRepository,
        private val applicationData: ApplicationData,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val grpcInterceptor: GrpcInterceptor,
        private val networkDataProvider: NetworkDataProvider,
        private val analyticsLogger: AnalyticsLogger,
        private val sharedDataSource: SharedDataSource,
        private val digitalKeyMopKeyUtils: DigitalMopKeyUtils,
    ) : VehicleSwitcherUseCase() {
        private fun getRemoveVehicleModel(selectedVehicle: VehicleInfo) =
            RemoveVehicleRequestModel(
                xBrand = selectedVehicle.brand,
                dateTime = System.currentTimeMillis().toString(),
                removeVehicle =
                    RemoveVehicleInfo(
                        vin = selectedVehicle.vin,
                        guid = oneAppPreferenceModel.getGuid(),
                        generation = selectedVehicle.generation,
                        status = "SOLD",
                    ),
            )

        override fun removeVehicle(selectedVehicle: VehicleInfo): Flow<VehicleSwitcherState> {
            if (selectedVehicle.is21MMVehicle) {
                return getVehicleSwitcherStateFor21MM(selectedVehicle)
            } else {
                return getVehicleSwitcherState(selectedVehicle)
            }
        }

        private fun isLMEXPhase1(selectedVehicle: VehicleInfo): Boolean {
            // LMEX phase 1 will not have Drive connect capability
            val isDriveConnectCapable =
                selectedVehicle.vehicleCapabilities
                    .map { it.lowercase() }
                    .contains("drive connect")
            return selectedVehicle.isLMEX && !isDriveConnectCapable
        }

        private fun getVehicleSwitcherStateFor21MM(selectedVehicle: VehicleInfo): Flow<VehicleSwitcherState> {
            if (isLMEXPhase1(selectedVehicle)) {
                return getVehicleSwitcherState(selectedVehicle)
            }
            return flow {
                remove21MMVehicle(selectedVin = selectedVehicle.vin).collect {
                    it?.value.let {
                        updateVehicleListAfterRemove(
                            selectedVehicle = selectedVehicle,
                        ).collect { updatedVehicleList ->
                            if (updatedVehicleList.isNullOrEmpty()) {
                                selectVehicle(null)
                            }
                            updatedVehicleList?.let {
                                logFirebaseEventWithParameter(
                                    AnalyticsEventParam.VEHICLE_SWITCHER_REMOVE_VEHICLE,
                                )
                                emit(Success(updatedVehicleList))
                            } ?: run {
                                emit(
                                    VehicleSwitcherState.Error(
                                        errorMessage =
                                            getContext().getString(
                                                R.string.maintenance_error_msg,
                                            ),
                                        errorCode = null,
                                    ),
                                )
                            }
                        }
                    }
                    if (it?.error != null) {
                        emit(VehicleSwitcherState.Error(errorMessage = it.error, errorCode = null))
                    }
                }
            }
        }

        private fun getVehicleSwitcherState(selectedVehicle: VehicleInfo): Flow<VehicleSwitcherState> {
            val removeVehicleModel = getRemoveVehicleModel(selectedVehicle)
            return flow {
                val response =
                    repository.removeVehicle(
                        body = removeVehicleModel,
                    )
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        updateVehicleListAfterRemove(
                            selectedVehicle = selectedVehicle,
                        ).collect { updatedVehicleList ->
                            logFirebaseEventWithParameter(
                                AnalyticsEventParam.VEHICLE_SWITCHER_REMOVE_VEHICLE,
                            )
                            if (updatedVehicleList.isNullOrEmpty()) {
                                selectVehicle(null)
                            }
                            emit(Success(updatedVehicleList))
                        }
                    } else if (it.status == NetworkStatus.FAILED) {
                        emit(
                            VehicleSwitcherState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.message,
                            ),
                        )
                    }
                }
            }
        }

        override fun selectVehicle(vehicleData: VehicleInfo?) {
            applicationData.setSelectedVehicle(vehicleData)
            sharedDataSource.resetDataSource()
            sharedDataSource.resetMarketingBanner()
        }

        override fun getSubscriptions(vehicleInfo: VehicleInfo): Flow<SubscriptionState> {
            if (!vehicleInfo.isConnectedVehicle) {
                return flow {
                    emit(SubscriptionState.Success(SubscriptionsStatus(false, false)))
                }
            }
            return flow {
                val response = repository.fetchVehicleSubscriptions(vehicleInfo)

                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        it.data?.toUIModel()?.let { uiModel ->
                            isActiveSubscriptionAvailable(uiModel).collect { isActiveSubscriptionAvailable ->
                                if (isActiveSubscriptionAvailable.paidSubscriptionsStatus && !vehicleInfo.isCY17) {
                                    isActiveSubscriptionAvailable.isYearlySubscriptionsRemoveOpenStatue =
                                        isActiveSubscriptionAvailable.isYearlySubscriptionsRemoveStatus
                                }
                                emit(SubscriptionState.Success(isActiveSubscriptionAvailable))
                            }
                        }
                    } else if (it.status == NetworkStatus.FAILED) {
                        emit(
                            SubscriptionState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.message,
                            ),
                        )
                    }
                }
            }
        }

        override suspend fun updatePreferredVehicle(
            vin: String,
            body: VehicleConfirmation,
        ): Flow<VehicleSwitcherState> =
            flow {
                val response = repository.confirmPreferredVehicle(vin, body)
                response.let {
                    if (it.status == NetworkStatus.SUCCESS && it.data != null) {
                        logFirebaseEventWithParameter(AnalyticsEventParam.VEHICLE_SWITCHER_MAKE_DEFAULT)
                        emit(
                            Success(
                                updateVehicleStatusAfterMakeDefault(
                                    vin,
                                ),
                                resetTab = true,
                            ),
                        )
                    } else if (it.status == NetworkStatus.FAILED) {
                        emit(
                            VehicleSwitcherState.Error(
                                errorCode = it.responseCode,
                                errorMessage = it.error?.message,
                            ),
                        )
                    }
                }
            }

        override suspend fun remove21MMVehicle(selectedVin: String): Flow<TwentyOneMMRemoveVehicleResponseModel?> {
            val deregisterVehicleRequest =
                VehicleRegistrationServiceOuterClass.DeregisterVehicleRequest
                    .newBuilder()
                    .apply {
                        vin = selectedVin
                        userId = oneAppPreferenceModel.getGuid()
                    }.build()

            val channel =
                repository.generateRPCManagedChannel(
                    BuildConfig.VEHICLE_REGISTRATION_BASE_URL,
                    Integer.valueOf(BuildConfig.USER_PROFILE_PORT),
                    networkDataProvider.rawCertData(),
                    grpcInterceptor,
                )
            return callbackFlow {
                VehicleRegistrationServiceGrpc.newStub(channel).deregisterVehicle(
                    deregisterVehicleRequest,
                    object : StreamObserver<VehicleRegistrationServiceOuterClass.DeregisterVehicleResponse> {
                        override fun onNext(value: VehicleRegistrationServiceOuterClass.DeregisterVehicleResponse) {
                            trySend(TwentyOneMMRemoveVehicleResponseModel(value = value, error = null))
                        }

                        override fun onError(t: Throwable) {
                            trySend(
                                TwentyOneMMRemoveVehicleResponseModel(
                                    value = null,
                                    error = t.message,
                                ),
                            )
                        }

                        override fun onCompleted() {
                            channel?.shutdown()
                        }
                    },
                )
                awaitClose { channel?.shutdown() }
            }
        }

        override suspend fun updateVehicleStatusAfterMakeDefault(vin: String): MutableList<VehicleInfo>? {
            val vehicleInfo: ArrayList<VehicleInfo>? = applicationData.getVehicleList()

            vehicleInfo?.forEachIndexed { _, vehicle ->
                if (vehicle.vin == vin) {
                    vehicle.preferred = PreferredVehicleStatus.PREFERRED_VEHICLE.preferredVehicleStatus
                    applicationData.setSelectedVehicle(vehicle)
                } else {
                    vehicle.preferred = PreferredVehicleStatus.NOT_PREFERRED_VEHICLE.preferredVehicleStatus
                }
            }

            applicationData.setVehicleList(vehicleInfo)

            return vehicleInfo?.toMutableList()
        }

        override suspend fun isActiveSubscriptionAvailable(subscriptionResponse: Subscription): Flow<SubscriptionsStatus> =
            flow {
                val trialList: List<SubscriptionV2> = subscriptionResponse.trialSubscriptions
                val paidList: List<SubscriptionV2> = subscriptionResponse.paidSubscriptions
                val subscriptionsStatus = SubscriptionsStatus()

                if (trialList.isNotEmpty()) {
                    trialList.forEach { trialSubscriptions ->
                        if (trialSubscriptions.status != null &&
                            trialSubscriptions.status.lowercase() == SubscriptionStatus.Active.status
                        ) {
                            subscriptionsStatus.trialSubscriptionsStatus = true
                        }
                    }
                }

                if (paidList.isNotEmpty()) {
                    paidList.forEach { paidSubscriptions ->
                        if (paidSubscriptions.status != null && paidSubscriptions.status.lowercase() == SubscriptionStatus.Active.status) {
                            subscriptionsStatus.paidSubscriptionsStatus = true
                        }
                        paidSubscriptions.subscriptionTerm?.let {
                            if (it.uppercase() == SubscriptionTerm.Yearly.term) {
                                subscriptionsStatus.isYearlySubscriptionsRemoveStatus = true
                            }
                        }
                    }
                }

                emit(subscriptionsStatus)
            }

        override suspend fun updateVehicleListAfterRemove(selectedVehicle: VehicleInfo): Flow<MutableList<VehicleInfo>?> =
            flow {
                getVehicleList(selectedVehicle).collect { vehicleInfo ->
                    vehicleInfo?.let { value -> emit(value.toMutableList()) } ?: run {
                        emit(null)
                    }
                }
            }

        override fun logFirebaseEventWithParameter(event: String) {
            analyticsLogger.logEventWithParameter(AnalyticsEventParam.VEHICLE_SWITCHER, event)
        }

        override suspend fun getVehicleList(selectedVehicle: VehicleInfo): Flow<MutableList<VehicleInfo>?> =
            flow {
                val response = dashboardRepository.vehiclesList()
                response.let {
                    if (response.status == NetworkStatus.SUCCESS && it.data != null) {
                        applicationData.setVehicleList(it.data?.payload)
                        if (selectedVehicle.vin == applicationData.getSelectedVehicle()?.vin) {
                            applicationData.getVehicleList()?.forEach { vehicleInfo ->
                                if (vehicleInfo.preferred == PreferredVehicleStatus.PREFERRED_VEHICLE.preferredVehicleStatus) {
                                    selectVehicle(vehicleInfo)
                                }
                            }
                        }
                        emit(it.data?.payload?.toMutableList())
                    } else if (response.status == NetworkStatus.FAILED) {
                        emit(null)
                    }
                }
            }

        override fun removeKeyCurrentVehicle() {
            digitalKeyMopKeyUtils.deactivateKeyCancel()
            digitalKeyMopKeyUtils.removeKey(
                applicationData.getSelectedVehicle()?.vin
                    ?: ToyotaConstants.EMPTY_STRING,
                oneAppPreferenceModel,
            )
        }

        override fun isRemoveVehicleEnabled(vehicleInfo: VehicleInfo): Boolean = !vehicleInfo.isRemoteOnlyUser
    }
