package com.toyota.oneapp.features.vehicleswitcher.presentation

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation.ConnectedVehicleViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyDownloadViewModel
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import kotlinx.coroutines.launch

@ExperimentalMaterialApi
@Composable
fun VehicleSwitcherBottomSheet(
    viewModel: VehicleSwitcherViewModel = (LocalContext.current as OADashboardActivity).vehicleSwitcherViewModel,
    connectedVehicleViewModel: ConnectedVehicleViewModel =
        hiltViewModel<ConnectedVehicleViewModel>(
            LocalContext.current as OADashboardActivity,
        ),
    digitalKeyDownloadViewModel: DigitalKeyDownloadViewModel = hiltViewModel<DigitalKeyDownloadViewModel>(),
) {
    // TODO When back press should not show white screen
    val coroutineScope =
        rememberCoroutineScope()
    val vehicleSwitcherBottomSheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Expanded,
            skipHalfExpanded = true,
        )

    BackHandler(vehicleSwitcherBottomSheetState.isVisible) {
        coroutineScope.launch { vehicleSwitcherBottomSheetState.hide() }
    }
    ModalBottomSheetLayout(
        modifier =
            Modifier
                .fillMaxHeight()
                .fillMaxSize(),
        sheetBackgroundColor = AppTheme.colors.tertiary15,
        sheetState = vehicleSwitcherBottomSheetState,
        sheetContent = {
            VehicleSwitcherBottomSheetScreen(
                vehicleSwitcherBottomSheetState,
                viewModel,
                connectedVehicleViewModel,
                digitalKeyDownloadViewModel,
            )
        },
    ) {}
}
