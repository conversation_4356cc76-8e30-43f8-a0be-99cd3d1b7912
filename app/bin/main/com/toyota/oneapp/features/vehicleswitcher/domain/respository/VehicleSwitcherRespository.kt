package com.toyota.oneapp.features.vehicleswitcher.domain.respository

import com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel.ConfirmVehicleResponse
import com.toyota.oneapp.features.vehicleswitcher.dataaccess.servermodel.RemoveVehicleResponse
import com.toyota.oneapp.features.vehicleswitcher.domain.model.RemoveVehicleRequestModel
import com.toyota.oneapp.features.vehicleswitcher.domain.model.VehicleConfirmation
import com.toyota.oneapp.model.subscriptionV2.VehicleSubscriptionResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.interceptor.GrpcInterceptor
import io.grpc.ManagedChannel
import kotlin.coroutines.CoroutineContext

abstract class VehicleSwitcherRepository(
    errorParser: ErrorMessageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun confirmPreferredVehicle(
        vin: String,
        vehicleConfirmationModel: VehicleConfirmation,
    ): Resource<ConfirmVehicleResponse?>

    abstract suspend fun removeVehicle(body: RemoveVehicleRequestModel): Resource<RemoveVehicleResponse?>

    abstract suspend fun fetchVehicleSubscriptions(vehicle: VehicleInfo): Resource<VehicleSubscriptionResponse?>

    abstract fun generateRPCManagedChannel(
        url: String?,
        port: Int,
        certData: ByteArray,
        grpcInterceptor: GrpcInterceptor,
    ): ManagedChannel?
}
