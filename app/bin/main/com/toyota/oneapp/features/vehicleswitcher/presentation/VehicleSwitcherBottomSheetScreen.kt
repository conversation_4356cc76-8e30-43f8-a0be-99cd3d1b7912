@file:OptIn(ExperimentalMaterialApi::class)

package com.toyota.oneapp.features.vehicleswitcher.presentation

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import com.toyota.oneapp.R
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.DEACTIVATE_RESULT
import com.toyota.oneapp.features.core.composable.*
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation.ConnectedVehicleViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyRevokeStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyDownloadViewModel
import com.toyota.oneapp.features.vehiclestatus.presentation.OnLifecycleEvent
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@ExperimentalMaterialApi
@Composable
fun VehicleSwitcherBottomSheetScreen(
    vehicleSwitcherBottomSheetState: ModalBottomSheetState,
    viewModel: VehicleSwitcherViewModel,
    connectedVehicleViewModel: ConnectedVehicleViewModel,
    digitalKeyDownloadViewModel: DigitalKeyDownloadViewModel = hiltViewModel<DigitalKeyDownloadViewModel>(),
) {
    val coroutineScope = rememberCoroutineScope()
    val removeVehicleBottomSheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            confirmValueChange = { false },
        )

    BackHandler(
        enabled = (vehicleSwitcherBottomSheetState.isVisible || removeVehicleBottomSheetState.isVisible),
    ) {
        coroutineScope.launch {
            if (!removeVehicleBottomSheetState.isVisible) {
                vehicleSwitcherBottomSheetState.hide()
            } else {
                removeVehicleBottomSheetState.hide()
            }
        }
    }

    VehicleSwitcherScreen(
        state = removeVehicleBottomSheetState,
        vehicleSwitcherBottomSheetState = vehicleSwitcherBottomSheetState,
        viewModel = viewModel,
        connectedVehicleViewModel = connectedVehicleViewModel,
        digitalKeyDownloadViewModel = digitalKeyDownloadViewModel,
    )
}

@OptIn(ExperimentalMaterialApi::class)
private fun removeDigitalKey(
    viewModel: VehicleSwitcherViewModel,
    context: Context,
) {
    val keyInfo = viewModel.getVinKeyInfo()
    if (keyInfo?.bleConnectFlg == true &&
        keyInfo.keyKind == DKLib.KeyKind.OWNER &&
        keyInfo.isVehicleConnectAccept
    ) {
        val mDialogView =
            LayoutInflater.from(context).inflate(
                R.layout.fragment_digital_key_remove_info_dialog,
                null,
            )
        val mBuilder =
            android.app.AlertDialog
                .Builder(context)
                .setView(mDialogView)
        val mAlertDialog = mBuilder.show()
        mDialogView.findViewById<View>(R.id.finish_button).setOnClickListener {
            mAlertDialog.dismiss()
            viewModel.removePersonaKey(isOwner = true)
        }
        mDialogView.findViewById<View>(R.id.back_btn).setOnClickListener {
            mAlertDialog.dismiss()
        }
    } else {
        viewModel.removePersonaKey(isOwner = keyInfo?.keyKind == DKLib.KeyKind.OWNER)
    }
}

@ExperimentalMaterialApi
@Composable
fun RemoveVehicleBottomSheet(
    modalSheetState: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
    isActiveSubscription: Boolean = false,
    viewModel: VehicleSwitcherViewModel,
    digitalKeyDownloadViewModel: DigitalKeyDownloadViewModel = hiltViewModel(),
) {
    val context = LocalContext.current

    val dkRevoke by viewModel.dkRevoke.collectAsState()

    if (dkRevoke) {
        viewModel.removeVehicle()
    }

    fun handleRemoteDeactivate(intent: Intent) {
        val result = intent.getBooleanExtra(DEACTIVATE_RESULT, false)
        if (result) {
            viewModel.removeOwnerKeyFromCTP(DigitalKeyRevokeStatus.PRIMARY.type, "")
        }
    }

    val broadcastReceiver =
        object : BroadcastReceiver() {
            override fun onReceive(
                context: Context?,
                intent: Intent?,
            ) {
                DigitalMopKeyUtils.appendLog("handleDigitalKeyMopEvents()  $intent")
                when (intent?.action) {
                    DigitalMopKeyUtils.ACTION_REMOTE_DEACTIVATE -> {
                        handleRemoteDeactivate(intent)
                    }
                }
            }
        }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> {
                viewModel.registerReceiver(
                    context = context,
                    broadcastReceiver = broadcastReceiver,
                )
            }
            Lifecycle.Event.ON_STOP -> {
                viewModel.unregisterReceiver(
                    context = context,
                    broadcastReceiver = broadcastReceiver,
                )
            }
            else -> {}
        }
    }

    Column(
        Modifier
            .fillMaxWidth()
            .background(AppTheme.colors.tertiary15),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(24.dp))
        Box(
            modifier =
                Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(color = AppTheme.colors.primary02),
            contentAlignment = Alignment.Center,
        ) {
            Image(
                modifier = Modifier.align(Alignment.Center),
                painter = painterResource(R.drawable.ic_popup_car),
                contentDescription = stringResource(R.string.removeVehiclePopupIconDescription),
            )
        }
        Spacer(modifier = Modifier.height(16.dp))
        OASubHeadLine1TextView(
            text = stringResource(R.string.remove_vehicle_text),
            color = AppTheme.colors.tertiary03,
            modifier =
                Modifier.testTagID(
                    AccessibilityId.ID_VEHICLE_SWITCHER_BOTTOM_SHEET_REMOVE_VEHICLE_TITLE_TEXT,
                ),
        )
        Spacer(modifier = Modifier.height(10.dp))
        OACallOut1TextView(
            text = stringResource(R.string.remove_vehicle_confirmation),
            color = AppTheme.colors.tertiary05,
            modifier =
                Modifier
                    .padding(horizontal = 32.dp)
                    .testTagID(
                        AccessibilityId.ID_VEHICLE_SWITCHER_BOTTOM_SHEET_REMOVE_CONFIRMATION_TEXT,
                    ),
            textAlign = TextAlign.Center,
        )
        Spacer(modifier = Modifier.height(30.dp))
        if (isActiveSubscription) {
            Column(
                modifier = Modifier.padding(horizontal = 32.dp),
            ) {
                ActiveSubscriptionsContent(stringResource(R.string.removeVehicleSubscriptions))
                Spacer(modifier = Modifier.height(10.dp))
                ActiveSubscriptionsContent(stringResource(R.string.removeVehicleAllSubscriptions))
                Spacer(modifier = Modifier.height(10.dp))
                ActiveSubscriptionsContent(stringResource(R.string.removeVehicleConfirmation))
                Spacer(modifier = Modifier.height(40.dp))
            }
        }
        Spacer(modifier = Modifier.height(30.dp))
        Button(
            onClick = {
                viewModel.updateButtonSheetState()
                coroutineScope.launch {
                    if (modalSheetState.isVisible) modalSheetState.hide()
                }
            },
            modifier =
                Modifier.testTagID(
                    AccessibilityId.ID_VEHICLE_SWITCHER_BOTTOM_SHEET_CANCEL_BUTTON,
                ),
            colors =
                ButtonDefaults.buttonColors(
                    backgroundColor = AppTheme.colors.tertiary15,
                    contentColor = AppTheme.colors.tertiary05,
                ),
            elevation =
                ButtonDefaults.elevation(
                    defaultElevation = 0.dp,
                    disabledElevation = 0.dp,
                ),
        ) {
            OACallOut1TextView(
                text = stringResource(R.string.cancel),
                color = AppTheme.colors.tertiary05,
            )
        }
        Spacer(modifier = Modifier.height(6.dp))
        ButtonLink1(
            modifier =
                Modifier
                    .clip(shape = RoundedCornerShape(50.dp))
                    .padding(horizontal = 50.dp, vertical = 10.dp)
                    .testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_BOTTOM_SHEET_REMOVE_BUTTON),
            text = stringResource(R.string.remove),
            color = AppTheme.colors.primaryButton01,
            bgColor = AppTheme.colors.primaryButton02,
            shape = RoundedCornerShape(50.dp),
            onClick = {
                viewModel.updateButtonSheetState()
                coroutineScope.launch {
                    if (modalSheetState.isVisible) modalSheetState.hide()
                }
                digitalKeyDownloadViewModel.clearPreferences(
                    digitalKeyDownloadViewModel.vehicleInfo.value?.vin,
                )
                if (viewModel.isKeyAvailable()) {
                    removeDigitalKey(
                        viewModel,
                        context = context,
                    )
                } else {
                    viewModel.onEvent(VehicleSwitcherEvent.RemoveVehicle)
                }
            },
        )
        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
fun ActiveSubscriptionsContent(activeSubscriptions: String) {
    Row(
        verticalAlignment = Alignment.Top,
        horizontalArrangement = Arrangement.Start,
    ) {
        OACallOut1TextView(
            text = stringResource(R.string.removeVehicleDot),
            color = AppTheme.colors.tertiary00,
            textAlign = TextAlign.Start,
        )
        Spacer(modifier = Modifier.width(10.dp))
        OACallOut1TextView(
            text = activeSubscriptions,
            color = AppTheme.colors.tertiary05,
            modifier =
                Modifier.testTagID(
                    AccessibilityId.ID_VEHICLE_SWITCHER_SUBS_REMOVE_CONFIRM_TEXT,
                ),
        )
    }
}
