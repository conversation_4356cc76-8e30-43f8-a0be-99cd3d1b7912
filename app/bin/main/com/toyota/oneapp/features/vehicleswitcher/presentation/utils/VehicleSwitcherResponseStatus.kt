package com.toyota.oneapp.features.vehicleswitcher.presentation.utils

enum class MakeDefaultStatus(val status: String) {
    MAKE_DEFAULT_SUCCESS("Success"),
    MAKE_DEFAULT_FAILURE("Failure"),
}

enum class PreferredVehicleStatus(val preferredVehicleStatus: Int) {
    PREFERRED_VEHICLE(1),
    NOT_PREFERRED_VEHICLE(0),
}

enum class SubscriptionStatus(val status: String) {
    Active("active"),
}

enum class SubscriptionTerm(val term: String) {
    Yearly("YRLY"),
    Monthly("MTH"),
}
