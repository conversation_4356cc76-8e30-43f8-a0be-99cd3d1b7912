/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

@file:OptIn(ExperimentalMaterialApi::class)

package com.toyota.oneapp.features.vehicleswitcher.presentation

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.Scaffold
import androidx.compose.material.ScaffoldState
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.AddSpacingAsPerDeviceHeight
import com.toyota.oneapp.features.core.composable.GetButtonHeightAsPerDeviceHeight
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.VinCopiedSnackbar
import com.toyota.oneapp.features.core.composable.dynamicvin.TestTagIdPrefix.TestTagIdPrefixVehicleSwitcher
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchSecondaryBottomSheetAction
import com.toyota.oneapp.features.dashboard.connectedvehicle.connectedvehicle.presentation.ConnectedVehicleViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.presentation.DigitalKeyDownloadViewModel
import com.toyota.oneapp.features.vehicleswitcher.application.LoadingState
import com.toyota.oneapp.features.vehicleswitcher.application.SubscriptionState
import com.toyota.oneapp.features.vehicleswitcher.application.VehicleSwitcherState
import com.toyota.oneapp.features.vehicleswitcher.presentation.utils.PreferredVehicleStatus
import com.toyota.oneapp.model.reconsent.ReConsentState
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.dataconsent.activities.MasterDataConsentActivity
import com.toyota.oneapp.ui.dataconsent.viewmodels.MasterDataConsentViewModel
import com.toyota.oneapp.ui.garage.unlink.RemoveVehicleActivity
import com.toyota.oneapp.util.ToyotaConstants
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import com.toyota.oneapp.features.core.composable.OAButtonTextView as OAButtonTextView1

@SuppressLint("CoroutineCreationDuringComposition", "UnusedMaterialScaffoldPaddingParameter")
@ExperimentalMaterialApi
@Composable
fun VehicleSwitcherScreen(
    state: ModalBottomSheetState,
    vehicleSwitcherBottomSheetState: ModalBottomSheetState,
    viewModel: VehicleSwitcherViewModel,
    connectedVehicleViewModel: ConnectedVehicleViewModel,
    digitalKeyDownloadViewModel: DigitalKeyDownloadViewModel = hiltViewModel(),
    masterDataConsentViewModel: MasterDataConsentViewModel = hiltViewModel(),
) {
    var vinYCoordinateForSnackbar = MutableStateFlow<Dp>(0.dp)
    val coroutineScope = rememberCoroutineScope()
    val subscriptionState = viewModel.subscriptionState.collectAsState()
    val initialVehiclesSwitcherState = viewModel.loadingState.collectAsState()
    val scaffoldState: ScaffoldState = rememberScaffoldState()

    val context = LocalContext.current
    val launcher =
        rememberLauncherForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) {
            if (it.resultCode == Activity.RESULT_OK) {
                masterDataConsentViewModel.vehicle?.let { vehicleInfo ->
                    viewModel.onEvent(
                        VehicleSwitcherEvent.SelectVehicle(vehicleInfo = vehicleInfo),
                    )
                    coroutineScope.launch {
                        vehicleSwitcherBottomSheetState.hide()
                    }
                }
            }
        }

    val reConsentState = masterDataConsentViewModel.reConsentState.collectAsState()
    LaunchedEffect(key1 = reConsentState.value, block = {
        if (reConsentState.value is ReConsentState.Loading) {
            viewModel.showLoading()
        } else if (reConsentState.value is ReConsentState.Success &&
            (reConsentState.value as ReConsentState.Success).data.reConsentEnabled
        ) {
            viewModel.dismissLoading()
            masterDataConsentViewModel.resetReConsentState()
            masterDataConsentViewModel.vehicle?.let { vehicleInfo ->
                val data = (reConsentState.value as ReConsentState.Success).data
                val intent = Intent(context, MasterDataConsentActivity::class.java)
                intent.putExtra(MasterDataConsentActivity.EXTRA_RE_CONSENT_DATA, data)
                intent.putExtra(MasterDataConsentActivity.EXTRA_VEHICLE, vehicleInfo)
                launcher.launch(intent)
            }
        } else if (reConsentState.value !is ReConsentState.Idle) {
            viewModel.dismissLoading()
            masterDataConsentViewModel.vehicle?.let { vehicleInfo ->
                masterDataConsentViewModel.resetReConsentState()
                viewModel.onEvent(
                    VehicleSwitcherEvent.SelectVehicle(vehicleInfo = vehicleInfo),
                )
                coroutineScope.launch {
                    vehicleSwitcherBottomSheetState.hide()
                }
            }
        } else {
            viewModel.dismissLoading()
        }
    })

    Scaffold(
        content = {
            Box(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                        .background(AppTheme.colors.tertiary15),
            ) {
                SubscriptionBottomSheet(
                    coroutineScope = coroutineScope,
                    subscriptionState = subscriptionState.value,
                    viewModel,
                    digitalKeyDownloadViewModel = digitalKeyDownloadViewModel,
                )
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Spacer(modifier = Modifier.height(8.dp))
                    SwipeIcon(vehicleSwitcherBottomSheetState)
                    AddVehicleIcon(viewModel)
                    AddSpacingAsPerDeviceHeight(defaultSpacing = 16.dp)
                    LoadVehicles(
                        vehicleSwitcherBottomSheetState,
                        viewModel,
                        scaffoldState,
                        connectedVehicleViewModel,
                        onYCoordinatePositioned = { it ->
                            vinYCoordinateForSnackbar.value = it
                        },
                    )
                }
                VinCopiedSnackbar(
                    scaffoldState.snackbarHostState,
                    stringResource(id = R.string.copied),
                    vinYCoordinateForSnackbar = vinYCoordinateForSnackbar,
                    yCoordinateAdjustment = 53.dp,
                    testTagIdPrefix = TestTagIdPrefixVehicleSwitcher,
                )
                when (initialVehiclesSwitcherState.value) {
                    is LoadingState.Loading -> {
                        ShowProgressIndicator(true)
                    }

                    is LoadingState.RemoveVehicleSuccess -> {
                        viewModel.dismissLoading()
                    }

                    is LoadingState.RemoveVehicleError -> {
                        val errorMessage =
                            (
                                initialVehiclesSwitcherState.value
                                    as LoadingState.RemoveVehicleError
                            ).errorMessage
                        ShowErrorToast(errorMessage = errorMessage)
                        viewModel.dismissLoading()
                    }

                    else -> {}
                }
            }
        },
    )
}

@Composable
fun LoadVehicles(
    vehicleSwitcherBottomSheetState: ModalBottomSheetState,
    viewModel: VehicleSwitcherViewModel,
    scaffoldState: ScaffoldState,
    connectedVehicleViewModel: ConnectedVehicleViewModel,
    onYCoordinatePositioned: (Dp) -> Unit,
) {
    val vehicleSwitcherState = viewModel.vehicleSwitcherState.collectAsState()
    val coroutineScope = rememberCoroutineScope()

    if (vehicleSwitcherState.value is VehicleSwitcherState.Success) {
        with(vehicleSwitcherState.value as VehicleSwitcherState.Success) {
            if (resetTab) {
                connectedVehicleViewModel.updateRemoteTab()
            }
            if (!data.isNullOrEmpty()) {
                VehicleList(
                    vehicleList = data.toMutableList(),
                    viewModel = viewModel,
                    scaffoldState = scaffoldState,
                    onYCoordinatePositioned = onYCoordinatePositioned,
                )
            } else {
                coroutineScope.launch {
                    if (vehicleSwitcherBottomSheetState.isVisible) {
                        vehicleSwitcherBottomSheetState.hide()
                    }
                }
            }
        }
    } else if (vehicleSwitcherState.value is VehicleSwitcherState.Error) {
        val errorMessage = (vehicleSwitcherState.value as VehicleSwitcherState.Error).errorMessage
        ShowErrorToast(errorMessage = errorMessage)
    }
}

@Composable
fun AddSpacer() {
    Spacer(modifier = Modifier.height(10.dp))
}

@Composable
fun SwipeIcon(vehicleSwitcherBottomSheetState: ModalBottomSheetState) {
    val coroutineScope = rememberCoroutineScope()
    Image(
        painterResource(R.drawable.ic_dropdown),
        contentDescription = stringResource(R.string.swipeDownIconDescription),
        contentScale = ContentScale.FillWidth,
        modifier =
            Modifier
                .width(30.dp)
                .height(10.dp)
                .testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_PULL_DOWN_BUTTON)
                .clickable {
                    coroutineScope.launch {
                        vehicleSwitcherBottomSheetState.hide()
                    }
                },
        colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
    )
}

@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun VehicleList(
    vehicleList: MutableList<VehicleInfo>,
    viewModel: VehicleSwitcherViewModel,
    scaffoldState: ScaffoldState,
    onYCoordinatePositioned: (Dp) -> Unit,
) {
    val state = rememberPagerState(pageCount = { vehicleList.size })
    val coroutineScope = rememberCoroutineScope()
    val isRemoveVehicleEnabledState by viewModel.isRemoveVehicleEnabledState.collectAsState()
    var currentPage = state.currentPage

    var prevVehicleListSize by remember { mutableStateOf(vehicleList.size) }
    LaunchedEffect(prevVehicleListSize) {
        // update the state for change in vehicle list
        coroutineScope.launch {
            state.animateScrollToPage(page = viewModel.getSelectedVehicleIndex())
        }
    }

    // update the current size.
    if (prevVehicleListSize != vehicleList.size) {
        prevVehicleListSize = vehicleList.size
    }

    // if the currentPage is not updated after scrollToPage, handle to load the last vehicle.
    if (currentPage >= vehicleList.size) {
        currentPage = vehicleList.size - 1
    }

    viewModel.isRemoveVehicleEnabled(vehicleList[currentPage])
    val isCurrentVehicleSelected =
        vehicleList[currentPage].vin == viewModel.applicationData.getSelectedVehicle()?.vin

    val isSecondaryVehicle = vehicleList[currentPage].isDigitalkey

    Box(contentAlignment = Alignment.BottomCenter, modifier = Modifier.fillMaxWidth()) {
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            VehicleViewPager(
                pagerState = state,
                vehicleList = vehicleList,
                viewModel = viewModel,
                scaffoldState = scaffoldState,
                isSecondaryVehicle = isSecondaryVehicle,
                onYCoordinatePositioned = onYCoordinatePositioned,
            )
            VehicleIndicator(
                selectedIndex = state.currentPage,
                scope = coroutineScope,
                pagerState = state,
                vehicleList = vehicleList,
                indicatorSize = 24.dp,
            )
            AddSpacingAsPerDeviceHeight(5.dp)
            OAFootNote1TextView(
                text = stringResource(R.string.switchVehicle_info),
                color = AppTheme.colors.tertiary07,
                textAlign = TextAlign.Center,
                modifier = Modifier.testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_DISCLAIMER_TEXT),
            )
            AddSpacingAsPerDeviceHeight(6.dp)
            if (isSecondaryVehicle) {
                Box {
                    // empty container
                }
            } else {
                if (isRemoveVehicleEnabledState) {
                    val height = GetButtonHeightAsPerDeviceHeight(52.dp)
                    Button(
                        onClick = {
                            val selectedVehicleInfo: VehicleInfo = vehicleList[state.currentPage]
                            viewModel.onEvent(
                                VehicleSwitcherEvent.CheckSubscription(
                                    vehicleInfo = selectedVehicleInfo,
                                ),
                            )
                        },
                        modifier =
                            Modifier
                                .height(height)
                                .testTagID(AccessibilityId.ID_VEHICLE_SWITCHER_REMOVE_VEHICLE_BUTTON),
                        colors =
                            ButtonDefaults.buttonColors(
                                backgroundColor = AppTheme.colors.tertiary15,
                                contentColor = AppTheme.colors.button02a,
                            ),
                        elevation =
                            ButtonDefaults.elevation(
                                defaultElevation = 0.dp,
                                disabledElevation = 0.dp,
                            ),
                    ) {
                        OAButtonTextView1(
                            stringResource(R.string.remove_vehicle_text),
                            color = AppTheme.colors.button02a,
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(4.dp))
            SelectButton(
                backgroundColor =
                    if (isCurrentVehicleSelected) {
                        AppTheme.colors.button02d
                    } else {
                        AppTheme.colors.tertiary03a
                    },
                textColor =
                    if (isCurrentVehicleSelected) {
                        AppTheme.colors.button05a
                    } else {
                        AppTheme.colors.button01a
                    },
                vehicleInfo = vehicleList[currentPage],
                isCurrentVehicleSelected = isCurrentVehicleSelected,
            )
            Box(
                Modifier
                    .width(20.dp)
                    .height(20.dp),
            ) {
            }
        }
    }
}

private fun removeDigitalKey(
    viewModel: VehicleSwitcherViewModel,
    context: Context,
) {
    val keyInfo = viewModel.getVinKeyInfo()
    if (keyInfo?.bleConnectFlg == true &&
        keyInfo.keyKind == DKLib.KeyKind.OWNER &&
        keyInfo.isVehicleConnectAccept
    ) {
        val mDialogView =
            LayoutInflater.from(context).inflate(
                R.layout.fragment_digital_key_remove_info_dialog,
                null,
            )
        val mBuilder =
            android.app.AlertDialog
                .Builder(context)
                .setView(mDialogView)
        val mAlertDialog = mBuilder.show()
        mDialogView.findViewById<View>(R.id.finish_button).setOnClickListener {
            mAlertDialog.dismiss()
            viewModel.removePersonaKey(isOwner = true)
        }
        mDialogView.findViewById<View>(R.id.back_btn).setOnClickListener {
            mAlertDialog.dismiss()
        }
    } else {
        viewModel.removePersonaKey(isOwner = keyInfo?.keyKind == DKLib.KeyKind.OWNER)
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun SubscriptionBottomSheet(
    coroutineScope: CoroutineScope,
    subscriptionState: SubscriptionState,
    viewModel: VehicleSwitcherViewModel,
    digitalKeyDownloadViewModel: DigitalKeyDownloadViewModel = hiltViewModel<DigitalKeyDownloadViewModel>(),
) {
    val context = LocalContext.current
    val bottomSheetState = LocalBottomSheet.current
    bottomSheetState.secondarySheetShape.value =
        RoundedCornerShape(
            topStart = 30.dp,
            topEnd = 30.dp,
        )

    val dkRevoke by viewModel.dkRevoke.collectAsState()

    if (dkRevoke) {
        viewModel.removeVehicle()
    }

    val launcher =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                viewModel.updateButtonSheetState()
                coroutineScope.launch {
                    bottomSheetState.secondarySheetState.value.let {
                        if (it.isVisible) {
                            it.hide()
                        }
                    }
                }
                digitalKeyDownloadViewModel.clearPreferences(
                    digitalKeyDownloadViewModel.vehicleInfo.value?.vin,
                )
                if (viewModel.isKeyAvailable()) {
                    removeDigitalKey(viewModel, context)
                } else {
                    viewModel.onEvent(VehicleSwitcherEvent.RemoveVehicle)
                }
            }
        }
    when (subscriptionState) {
        is SubscriptionState.Success -> {
            viewModel.updateButtonSheetState()
            with((subscriptionState.data)) {
                if (isYearlySubscriptionsRemoveStatus) {
                    if (isYearlySubscriptionsRemoveOpenStatue) {
                        isYearlySubscriptionsRemoveOpenStatue = false
                        LocalContext.current.let {
                            val removeVehicleIntent = Intent(it, RemoveVehicleActivity::class.java)
                            removeVehicleIntent.putExtra(
                                ToyotaConstants.SELECTED_VEHICLE,
                                viewModel.selectedVehicle,
                            )
                            launcher.launch(removeVehicleIntent)
                        }
                    }
                } else {
                    LaunchedEffect(key1 = 0) {
                        coroutineScope.launchSecondaryBottomSheetAction(bottomSheetState) { bottomSheet ->
                            val isActiveSubscription = paidSubscriptionsStatus || trialSubscriptionsStatus
                            RemoveVehicleBottomSheet(
                                bottomSheet,
                                coroutineScope,
                                isActiveSubscription,
                                viewModel,
                                digitalKeyDownloadViewModel,
                            )
                        }
                    }
                }
            }
        }

        is SubscriptionState.Error -> {
            val errorMessage =
                (viewModel.subscriptionState.collectAsState().value as SubscriptionState.Error).errorMessage
            ShowErrorToast(errorMessage = errorMessage)
        }

        is SubscriptionState.Idle -> {}
        else -> {}
    }
}

fun isPreferredVehicle(preferredStatus: Int): Boolean = (preferredStatus == PreferredVehicleStatus.PREFERRED_VEHICLE.preferredVehicleStatus)

@Composable
fun ShowErrorToast(errorMessage: String?) {
    val context = LocalContext.current
    Toast
        .makeText(
            context,
            errorMessage ?: stringResource(R.string.generic_error),
            Toast.LENGTH_LONG,
        ).show()
}
