/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.presentation

import android.content.Intent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.rememberNestedScrollInteropConnection
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.features.chargehistory.application.ChargeHistoryNavigationState
import com.toyota.oneapp.features.chargehistory.application.ChargeHistoryScreenState
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeInfoItem
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeSessionModel
import com.toyota.oneapp.features.chargehistory.domain.model.EmptyChargeHistoryModel
import com.toyota.oneapp.features.chargehistory.navigation.ChargeHistoryRoute
import com.toyota.oneapp.features.chargehistory.navigation.ChargeHistoryRoute.Companion.CHARGE_HISTORY_DETAIL_ARG
import com.toyota.oneapp.features.chargehistory.navigation.ChargeHistoryRoute.Companion.INFO_ITEMS_ARG
import com.toyota.oneapp.features.chargeinfo.domain.model.PriceDetail
import com.toyota.oneapp.features.chargeinfo.presentation.UnknownLocationDialog
import com.toyota.oneapp.features.core.composable.OABadgeIcon
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OAButton
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAFullScreenBottomSheetLayout
import com.toyota.oneapp.features.core.composable.OAMonthPicker
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.core.util.launchSecondaryBottomSheetAction
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.ftue.presentation.extension.w
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthdetail.domain.DateModel
import com.toyota.oneapp.ui.newdashboard.DashboardActivity
import com.toyota.oneapp.ui.newdashboard.kNavItem

@Composable
fun ChargeHistoryScreen(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: ChargeHistoryViewModel = hiltViewModel(),
) {
    val coroutineScope = rememberCoroutineScope()
    val bottomSheet = LocalBottomSheet.current
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(uiState.screenState) {
        if (uiState.screenState is ChargeHistoryScreenState.Init) {
            viewModel.onEvent(ChargeHistoryEvent.InitScreen)
        }
    }

    NavigationResultHandler(
        navigationState = uiState.navigationState,
        onLogEvent = { event ->
            viewModel.onEvent(
                ChargeHistoryEvent.OnLogEvent(
                    group = AnalyticsEvent.VEHICLE_EV_PUB_CHG_GROUP,
                    name = event,
                ),
            )
        },
        onLaunchMonthYearPicker = {
            MonthYearPickerDialog(
                onCancel = { viewModel.resetNavigationState() },
            ) { month, monthStr, year ->
                viewModel.resetNavigationState()
                viewModel.onEvent(
                    ChargeHistoryEvent.OnFilterByMonth(month, monthStr, year),
                )
            }
        },
        onShowUnknownPopup = { sheetShape ->
            viewModel.resetNavigationState()
            coroutineScope.launchSecondaryBottomSheetAction(bottomSheet) {
                bottomSheet.secondarySheetShape.value = sheetShape
                UnknownLocationDialog(it) {
                    context.startActivity(
                        Intent(context, DashboardActivity::class.java)
                            .putExtra(kNavItem, DashboardActivity.NavItemType.ACCOUNT),
                    )
                }
            }
        },
        onLaunchChargeHistoryDetail = { uiModel, infoList ->
            viewModel.resetNavigationState()
            navController.currentBackStackEntry?.savedStateHandle?.set(
                CHARGE_HISTORY_DETAIL_ARG,
                uiModel,
            )
            navController.currentBackStackEntry?.savedStateHandle?.set(INFO_ITEMS_ARG, infoList)
            navController.navigate(ChargeHistoryRoute.ChargeHistoryDetailScreen.route)
        },
    )

    ChargeHistoryListContent(
        uiState = uiState,
        modifier = modifier,
        onBack = { navController.popBackStack() },
        onLaunchMonthYearPicker = {
            viewModel.onEvent(ChargeHistoryEvent.OnLaunchMonthYearPicker)
        },
        onChargingTypeFilter = { isPublicType, isHomeType ->
            viewModel.onEvent(
                ChargeHistoryEvent.OnFilterByChargeType(isPublicType, isHomeType),
            )
        },
        onShowUnknownPopup = {
            viewModel.resetNavigationState()
            viewModel.onEvent(ChargeHistoryEvent.OnShowUnknownLocationInfo)
        },
        onLaunchChargeHistoryDetail = { uiModel ->
            viewModel.onEvent(ChargeHistoryEvent.OnHistoryItemClicked(uiModel))
        },
    )
}

@Composable
private fun ChargeHistoryListContent(
    uiState: ChargeHistoryUIState,
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
    onLaunchMonthYearPicker: () -> Unit,
    onChargingTypeFilter: (isPublicTpye: Boolean, isHomeType: Boolean) -> Unit,
    onShowUnknownPopup: () -> Unit,
    onLaunchChargeHistoryDetail: (uiModel: ChargeSessionModel) -> Unit,
) {
    val context = LocalContext.current

    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tertiary15,
        screenTitle = stringResource(id = R.string.history),
        testTagId = AccessibilityId.ID_HISTORY_LIST_BACK_BTN,
        onBack = { onBack() },
        modifier =
            modifier
                .padding(vertical = 8.dp, horizontal = 16.dp)
                .nestedScroll(rememberNestedScrollInteropConnection()),
    ) {
        ChargeHistoryFilterLayout(
            uiState = uiState,
            onLaunchMonthYearPicker = onLaunchMonthYearPicker,
        ) { isPublicType, isHomeType ->
            onChargingTypeFilter(isPublicType, isHomeType)
        }
        when (uiState.screenState) {
            is ChargeHistoryScreenState.Init -> {
                // don't do anything
            }
            is ChargeHistoryScreenState.Loading -> {
                ShowProgressIndicator(dialogState = true)
            }
            is ChargeHistoryScreenState.ListChargeSessions -> {
                ChargeSessionsList(
                    chargeSessions = uiState.screenState.sessionsList,
                    onShowPopup = onShowUnknownPopup,
                ) { uiModel ->
                    onLaunchChargeHistoryDetail(uiModel)
                }
            }
            is ChargeHistoryScreenState.NoChargeHistory -> {
                NoChargeSessionsScreen(
                    uiModel = uiState.screenState.uiModel,
                ) {
                    context.startActivity(
                        Intent(context, DashboardActivity::class.java)
                            .putExtra(kNavItem, DashboardActivity.NavItemType.ACCOUNT),
                    )
                }
            }
        }
    }
}

@Composable
private fun NavigationResultHandler(
    navigationState: ChargeHistoryNavigationState,
    onLogEvent: (event: String) -> Unit,
    onLaunchMonthYearPicker: @Composable () -> Unit,
    onShowUnknownPopup: (sheetShape: RoundedCornerShape) -> Unit,
    onLaunchChargeHistoryDetail: (
        uiModel: ChargeSessionModel,
        infoList: List<ChargeInfoItem>,
    ) -> Unit,
) {
    when (navigationState) {
        is ChargeHistoryNavigationState.Init -> {
            // don't do anything
        }
        is ChargeHistoryNavigationState.LaunchMonthYearPicker -> {
            onLogEvent(AnalyticsEventParam.CHARGE_HISTORY_DATE_FILTER)
            onLaunchMonthYearPicker()
        }
        is ChargeHistoryNavigationState.ShowUnknownLocationPopup -> {
            onShowUnknownPopup(
                RoundedCornerShape(
                    topStart = 30.dp,
                    topEnd = 30.dp,
                ),
            )
        }
        is ChargeHistoryNavigationState.ChargeDetailNavigation -> {
            onLogEvent(AnalyticsEventParam.CHARGE_HISTORY_DETAILS)
            onLaunchChargeHistoryDetail(
                navigationState.uiModel,
                navigationState.infoList,
            )
        }
    }
}

@Composable
private fun ChargeHistoryFilterLayout(
    uiState: ChargeHistoryUIState,
    modifier: Modifier = Modifier,
    onLaunchMonthYearPicker: () -> Unit,
    onChargingTypeFilter: (isPublicTpye: Boolean, isHomeType: Boolean) -> Unit,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceEvenly,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        FilterButton(
            text = stringResource(R.string.month),
            isSelected = uiState.isFilterByMonth,
            accessibilityID = AccessibilityId.ID_FILTER_BY_MONTH_TAB,
            modifier = Modifier.weight(1.2f),
            content = {
                MonthFilterContent(
                    text = uiState.monthLabel ?: stringResource(R.string.filter_by_month),
                    isSelected = uiState.isFilterByMonth,
                    modifier =
                        Modifier
                            .align(Alignment.Center)
                            .testTagID(AccessibilityId.ID_MONTH_LABEL_TEXT),
                )
            },
        ) {
            onLaunchMonthYearPicker()
        }

        FilterButton(
            text = stringResource(R.string.filter_by_home_type),
            isSelected = uiState.isFilterByHomeType,
            accessibilityID = AccessibilityId.ID_FILTER_BY_HOME_TYPE_TAB,
            modifier = Modifier.weight(1f),
        ) {
            onChargingTypeFilter(false, !uiState.isFilterByHomeType)
        }

        FilterButton(
            text = stringResource(R.string.filter_by_public_type),
            isSelected = uiState.isFilterByPublicType,
            accessibilityID = AccessibilityId.ID_FILTER_BY_PUBLIC_TYPE_TAB,
            modifier = Modifier.weight(1f),
        ) {
            onChargingTypeFilter(!uiState.isFilterByPublicType, false)
        }
    }
}

@Composable
private fun MonthYearPickerDialog(
    modifier: Modifier = Modifier,
    onCancel: () -> Unit,
    onPicked: (month: Int, monthStr: String, year: Int) -> Unit,
) {
    var showDialog by remember { mutableStateOf(true) }

    OAMonthPicker(
        modifier = modifier,
        showDialog = showDialog,
        testTagIds =
            Pair(
                AccessibilityId.ID_MONTH_YEAR_PICKER_OK_BTN,
                AccessibilityId.ID_MONTH_YEAR_PICKER_CLOSE_BTN,
            ),
        onCancel = {
            onCancel()
            showDialog = false
        },
    ) { _month, _monthStr, _year ->
        onPicked(_month, _monthStr, _year)
        showDialog = false
    }
}

@Composable
private fun ChargeSessionsList(
    chargeSessions: List<ChargeSessionModel>,
    modifier: Modifier = Modifier,
    onShowPopup: () -> Unit,
    onLaunchChargeHistoryDetail: (uiModel: ChargeSessionModel) -> Unit,
) {
    LazyColumn(
        modifier =
            modifier
                .fillMaxSize(),
    ) {
        items(chargeSessions) { uiModel ->
            ChargeSessionCard(
                uiModel = uiModel,
                onShowUnknownPopup = { onShowPopup() },
                modifier = Modifier.testTagID(AccessibilityId.ID_CHARGE_SESSION_CARD),
            ) {
                onLaunchChargeHistoryDetail(uiModel)
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun ChargeSessionCard(
    uiModel: ChargeSessionModel,
    modifier: Modifier = Modifier,
    onShowUnknownPopup: () -> Unit,
    onClick: (uiModel: ChargeSessionModel) -> Unit,
) {
    Card(
        onClick = {
            onClick(uiModel)
        },
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tile03,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(vertical = 8.dp),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
        ) {
            ChargeNameLayout(
                iconRes = uiModel.iconRes ?: R.drawable.ic_pin,
                name = uiModel.locationType,
                isEco = uiModel.isEco,
                isUnknown = uiModel.locationType == "Unknown",
                modifier =
                    Modifier
                        .padding(bottom = 12.dp),
            ) { onShowUnknownPopup() }

            ChargeDetailsLayout(
                socBefore = uiModel.socBeforeCharging,
                socAfter = uiModel.socAfterCharging,
                totalCharge = uiModel.totalCharge,
                modifier =
                    Modifier
                        .padding(bottom = 8.dp),
            )

            OtherInfoLayout(
                date = uiModel.displayDate,
                duration = uiModel.duration,
                timeRange = " • ${uiModel.timeRange}",
                priceDetail = uiModel.priceDetail,
            )
        }
    }
}

@Composable
private fun NoChargeSessionsScreen(
    uiModel: EmptyChargeHistoryModel,
    modifier: Modifier = Modifier,
    onGoToAccountSettings: () -> Unit,
) {
    Box(
        modifier =
            modifier
                .background(AppTheme.colors.tertiary15)
                .fillMaxSize()
                .testTagID(uiModel.accessibilityId),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                Modifier
                    .wrapContentSize()
                    .align(Alignment.Center),
        ) {
            OABadgeIcon(imageResId = uiModel.icon)

            Spacer(modifier = Modifier.height(4.dp))

            OABody4TextView(
                text = stringResource(uiModel.title),
                color = AppTheme.colors.tertiary00,
            )

            Spacer(modifier = Modifier.height(4.dp))

            OACallOut1TextView(
                text = stringResource(uiModel.subtitle),
                color = AppTheme.colors.tertiary03,
                textAlign = TextAlign.Center,
            )
        }

        AnimatedVisibility(
            visible = uiModel is EmptyChargeHistoryModel.NoProfileAddress,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(top = 8.dp, bottom = 16.dp, start = 24.dp, end = 24.dp)
                    .align(Alignment.BottomCenter),
        ) {
            OAButton(
                text = stringResource(R.string.go_to_account_settings),
                modifier = Modifier.size(width = 192.w(), height = 52.h()),
                textModifier =
                    Modifier
                        .padding(all = 0.dp)
                        .testTagID(AccessibilityId.ID_NO_HOME_ADDRESS_GO_TO_ACCOUNTS),
                click = {
                    onGoToAccountSettings()
                },
            )
        }
    }
}

@Composable
private fun ChargeHistoryListPreview() {
    ChargeHistoryListContent(
        uiState =
            ChargeHistoryUIState(
                screenState =
                    ChargeHistoryScreenState.ListChargeSessions(
                        listOf(
                            ChargeSessionModel(
                                iconRes = R.drawable.ic_pin,
                                locationType = "Public",
                                isEco = false,
                                ecoDuration = 1,
                                socBeforeCharging = "56%",
                                socAfterCharging = "82%",
                                totalCharge = "2.657",
                                address = "Frisco, Texas 75036, United States",
                                displayDate = "Today",
                                timeRange = "10:00 PM - 02:00 AM",
                                duration = "2 hr 20 min",
                                detailedDuration = "2 hour 20 min 30 sec",
                                dateModel = DateModel("06", "Jan", "06, Jan 2025"),
                                priceDetail =
                                    PriceDetail(
                                        digits = "5678",
                                        totalAmount = "$25.5",
                                    ),
                            ),
                        ),
                    ),
            ),
        onBack = {},
        onLaunchMonthYearPicker = {},
        onChargingTypeFilter = { _, _ -> },
        onShowUnknownPopup = {},
        onLaunchChargeHistoryDetail = {},
    )
}

@Preview
@Composable
private fun ChargeHistoryListThemePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        ChargeHistoryListPreview()
    }
}

@Preview
@Composable
private fun NoProfileAddressPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        NoChargeSessionsScreen(
            uiModel =
                EmptyChargeHistoryModel.NoProfileAddress(
                    R.drawable.ic_location,
                    R.string.no_saved_home_address,
                    R.string.no_saved_home_address_description,
                ),
            modifier = Modifier,
            onGoToAccountSettings = {},
        )
    }
}

@Preview
@Composable
private fun NoChargeSessionsForMonthPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        NoChargeSessionsScreen(
            uiModel =
                EmptyChargeHistoryModel.NoChargeSessionsForMonth(
                    R.drawable.ic_wall_socket_1_1,
                    R.string.no_charging_sessions,
                    R.string.no_charging_sessions_description,
                ),
            modifier = Modifier,
            onGoToAccountSettings = {},
        )
    }
}

@Preview
@Composable
private fun NoChargeHistoryPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        NoChargeSessionsScreen(
            uiModel =
                EmptyChargeHistoryModel.NoChargeHistory(
                    R.drawable.ic_schedule,
                    R.string.no_charging_history,
                    R.string.no_charging_history_description,
                ),
            modifier = Modifier,
            onGoToAccountSettings = {},
        )
    }
}
