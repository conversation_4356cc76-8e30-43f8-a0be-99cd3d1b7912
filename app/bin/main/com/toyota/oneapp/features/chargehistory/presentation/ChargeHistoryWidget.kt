/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeinfo.application.ChargeHistoryCardState
import com.toyota.oneapp.features.chargeinfo.domain.model.ChargeHistoryInfoCardModel
import com.toyota.oneapp.features.chargeinfo.domain.model.PriceDetail
import com.toyota.oneapp.features.chargeinfo.presentation.ChargeInfoViewModel
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OACallOut2TextViewResizable
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.OAIconArrowRight
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.shimmerEffect
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.util.StringUtil.capitalizeWords
import com.toyota.oneapp.util.ToyotaConstants

@Composable
fun ChargeHistoryWidget(
    viewModel: ChargeInfoViewModel,
    modifier: Modifier = Modifier,
    onShowUnknownPopup: () -> Unit,
    onClick: () -> Unit,
) {
    when (val viewState = viewModel.chargeHistoryCardState.collectAsState().value) {
        is ChargeHistoryCardState.Loading -> {
            ChargeHistoryShimmerCard(modifier)
        }

        is ChargeHistoryCardState.ShowLastChargeData -> {
            ChargeHistoryCard(
                uiModel = viewState.uiModel,
                modifier =
                    modifier
                        .clickable { onClick() },
            ) { onShowUnknownPopup() }
        }

        is ChargeHistoryCardState.NoHistory -> {
            NoChargeHistoryCard(
                modifier =
                    modifier
                        .clickable { onClick() },
            )
        }

        else -> {
            // Do nothing
        }
    }
}

@Composable
private fun ChargeHistoryShimmerCard(modifier: Modifier = Modifier) {
    Card(
        backgroundColor = AppTheme.colors.tile03,
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(16.dp),
        ) {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier =
                    Modifier
                        .fillMaxWidth(),
            ) {
                Box(
                    modifier =
                        Modifier
                            .size(32.dp)
                            .clip(CircleShape)
                            .shimmerEffect(),
                )

                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(16.dp)
                            .padding(start = 24.dp, end = 16.dp)
                            .shimmerEffect(),
                )

                Box(
                    modifier =
                        Modifier
                            .size(32.dp)
                            .clip(CircleShape)
                            .shimmerEffect(),
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Card(
                backgroundColor = AppTheme.colors.tile05,
                elevation = 0.dp,
                shape = RoundedCornerShape(8.dp),
                modifier =
                    modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
            ) {
                ChildShimmerCard()
            }
        }
    }
}

@Composable
private fun ChildShimmerCard(modifier: Modifier = Modifier) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(16.dp),
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
        ) {
            Row {
                Box(
                    modifier =
                        Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .shimmerEffect(),
                )

                Box(
                    modifier =
                        Modifier
                            .width(80.dp)
                            .height(16.dp)
                            .padding(horizontal = 16.dp)
                            .shimmerEffect(),
                )
            }

            Box(
                modifier =
                    Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .shimmerEffect(),
            )
        }

        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .width(150.dp)
                        .height(16.dp)
                        .padding(horizontal = 16.dp)
                        .shimmerEffect(),
            )

            Box(
                modifier =
                    Modifier
                        .width(100.dp)
                        .height(24.dp)
                        .padding(horizontal = 16.dp)
                        .shimmerEffect(),
            )
        }

        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Box(
                modifier =
                    Modifier
                        .width(200.dp)
                        .height(16.dp)
                        .padding(horizontal = 16.dp)
                        .shimmerEffect(),
            )

            Box(
                modifier =
                    Modifier
                        .width(100.dp)
                        .height(16.dp)
                        .padding(horizontal = 16.dp)
                        .shimmerEffect(),
            )
        }
    }
}

@Composable
private fun ChargeHistoryCard(
    uiModel: ChargeHistoryInfoCardModel.ChargeHistoryDetailModel,
    modifier: Modifier = Modifier,
    onShowPopup: () -> Unit,
) {
    Card(
        backgroundColor = AppTheme.colors.tile03,
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(16.dp),
        ) {
            HistoryTitleLayout(
                modifier =
                    Modifier
                        .padding(bottom = 16.dp),
            )

            LastChargeCard(
                uiModel = uiModel,
                modifier = Modifier.testTagID(AccessibilityId.ID_LAST_CHARGE_INFO_CARD),
            ) { onShowPopup() }
        }
    }
}

@Composable
private fun HistoryTitleLayout(modifier: Modifier = Modifier) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier =
            modifier
                .fillMaxWidth(),
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Image(
                painter = painterResource(id = R.drawable.ic_schedule),
                contentDescription = "",
                colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
                modifier =
                    Modifier
                        .size(24.dp),
            )

            Spacer(modifier = Modifier.width(16.dp))

            OASubHeadLine1TextView(
                text = stringResource(id = R.string.history),
                color = AppTheme.colors.tertiary03,
            )
        }

        OAIconArrowRight()
    }
}

@Composable
private fun LastChargeCard(
    uiModel: ChargeHistoryInfoCardModel.ChargeHistoryDetailModel,
    modifier: Modifier = Modifier,
    onShowPopup: () -> Unit,
) {
    Card(
        backgroundColor = AppTheme.colors.tile05,
        elevation = 0.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(16.dp),
        ) {
            ChargeNameLayout(
                iconRes = uiModel.iconRes,
                name = uiModel.name,
                isEco = uiModel.isEcho,
                isUnknown = uiModel.name == "Unknown",
                modifier =
                    Modifier
                        .padding(bottom = 12.dp),
            ) { onShowPopup() }

            ChargeDetailsLayout(
                socBefore = uiModel.socBefore,
                socAfter = uiModel.socAfter,
                totalCharge = uiModel.totalCharge,
                modifier =
                    Modifier
                        .padding(bottom = 6.dp),
            )

            OtherInfoLayout(
                date = uiModel.date,
                timeRange = " • ${uiModel.timeRange}",
                duration = uiModel.duration,
                priceDetail = uiModel.priceDetail,
            )
        }
    }
}

@Composable
fun ChargeNameLayout(
    iconRes: Int,
    name: String,
    isEco: Boolean,
    isUnknown: Boolean,
    modifier: Modifier = Modifier,
    onUnknownLocationPopup: () -> Unit,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier =
            modifier
                .fillMaxWidth(),
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (!isUnknown) {
                Image(
                    painter = painterResource(id = iconRes),
                    contentDescription = "",
                    colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
                    modifier =
                        Modifier
                            .size(24.dp),
                )
            }

            OABody4TextView(
                text = name.capitalizeWords(),
                color = AppTheme.colors.tertiary03,
                modifier =
                    Modifier
                        .padding(horizontal = 8.dp)
                        .testTagID(AccessibilityId.ID_LAST_CHARGE_PLACE_TXT),
            )
        }

        if (isEco) {
            Image(
                painter = painterResource(id = R.drawable.ic_eco_badge),
                contentDescription = "",
                modifier =
                    Modifier
                        .size(32.dp),
            )
        } else if (isUnknown) {
            Image(
                painter = painterResource(id = R.drawable.ic_info_secondary),
                contentDescription = "",
                modifier =
                    Modifier
                        .size(32.dp)
                        .testTagID(AccessibilityId.ID_UNKNOWN_LOCATION_INFO_ICON)
                        .clickable { onUnknownLocationPopup() },
            )
        }
    }
}

@Composable
fun ChargeDetailsLayout(
    socBefore: String,
    socAfter: String,
    totalCharge: String,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier =
            modifier
                .fillMaxWidth(),
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            OACallOut1TextView(
                text = socBefore,
                color = AppTheme.colors.tertiary03,
            )

            Image(
                painter = painterResource(id = R.drawable.ic_line_arrow_right),
                contentDescription = "",
                modifier =
                    Modifier
                        .wrapContentSize()
                        .padding(horizontal = 8.dp),
                colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
            )

            OACallOut1TextView(
                text = socAfter,
                color = AppTheme.colors.tertiary03,
            )
        }

        Row(
            verticalAlignment = Alignment.Bottom,
            modifier = Modifier.testTagID(AccessibilityId.ID_LAST_CHARGE_KWHR_TXT),
        ) {
            OABody4TextView(
                text = totalCharge,
                color = AppTheme.colors.tertiary03,
            )
            Spacer(modifier = Modifier.width(2.dp))
            OACallOut1TextView(
                text = stringResource(R.string.unit_kWh),
                color = AppTheme.colors.tertiary03,
            )
        }
    }
}

@Composable
fun OtherInfoLayout(
    date: String,
    duration: String,
    timeRange: String = ToyotaConstants.EMPTY_STRING,
    priceDetail: PriceDetail?,
    modifier: Modifier = Modifier,
) {
    val dateAndTime =
        when (date) {
            "Today" -> {
                stringResource(R.string.common_today)
            }

            "Yesterday" -> {
                stringResource(R.string.common_yesterday)
            }

            else -> {
                date
            }
        }.plus(timeRange)
    Column(
        modifier =
            modifier
                .fillMaxWidth(),
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                modifier
                    .fillMaxWidth(),
        ) {
            OACaption1TextView(
                text = dateAndTime,
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.testTagID(AccessibilityId.ID_LAST_CHARGE_DATE_TXT),
            )

            OACaption1TextView(
                text = duration,
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.testTagID(AccessibilityId.ID_LAST_CHARGE_DURATION_TXT),
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        priceDetail?.let {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier =
                    modifier
                        .fillMaxWidth(),
            ) {
                OACaption1TextView(
                    text = it.digits,
                    color = AppTheme.colors.tertiary03,
                )

                OACaption1TextView(
                    text = it.totalAmount,
                    color = AppTheme.colors.tertiary03,
                )
            }
        }
    }
}

@Composable
private fun NoChargeHistoryCard(modifier: Modifier = Modifier) {
    Card(
        backgroundColor = AppTheme.colors.tile03,
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight(),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .padding(16.dp),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f, fill = false),
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_schedule),
                    contentDescription = "",
                    colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
                    modifier =
                        Modifier
                            .size(24.dp),
                )

                Spacer(modifier = Modifier.width(16.dp))

                Column {
                    OASubHeadLine1TextView(
                        text = stringResource(id = R.string.history),
                        color = AppTheme.colors.tertiary03,
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    OAFootNote1TextView(
                        text = stringResource(id = R.string.no_history_description),
                        color = AppTheme.colors.tertiary03,
                        textAlign = TextAlign.Start,
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            OAIconArrowRight(modifier = Modifier.size(18.dp))
        }
    }
}

@Composable
fun FilterButton(
    text: String,
    accessibilityID: String,
    modifier: Modifier = Modifier,
    isSelected: Boolean = false,
    content: @Composable BoxScope.() -> Unit? = { null },
    onClick: () -> Unit,
) {
    Button(
        onClick = onClick,
        colors =
            ButtonDefaults.buttonColors(
                backgroundColor = if (isSelected) AppTheme.colors.button02a else AppTheme.colors.button05b,
            ),
        shape = RoundedCornerShape(100.dp),
        modifier =
            modifier
                .testTagID(accessibilityID)
                .padding(4.dp),
    ) {
        Box(Modifier.wrapContentWidth()) {
            if (content() == null) {
                OACallOut2TextViewResizable(
                    text = text,
                    color = if (isSelected) AppTheme.colors.button05b else AppTheme.colors.button02a,
                    modifier =
                        Modifier
                            .padding(all = 2.dp)
                            .align(Alignment.Center),
                )
            } else {
                content()
            }
        }
    }
}

@Composable
fun MonthFilterContent(
    text: String,
    isSelected: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.wrapContentSize(),
    ) {
        OACallOut2TextViewResizable(
            text = text,
            color = if (isSelected) AppTheme.colors.button05b else AppTheme.colors.button02a,
            modifier =
                Modifier
                    .padding(all = 2.dp),
        )

        Spacer(modifier = Modifier.width(8.dp))

        Image(
            painter = painterResource(R.drawable.ic_arrow_down),
            contentDescription = "",
            colorFilter =
                ColorFilter.tint(
                    color = if (isSelected) AppTheme.colors.button05b else AppTheme.colors.button02a,
                ),
            modifier = Modifier.size(24.dp),
        )
    }
}

@Preview
@Composable
private fun NoChargeHistoryCardPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        NoChargeHistoryCard()
    }
}
