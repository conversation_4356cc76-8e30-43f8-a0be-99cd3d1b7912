/*
 * *
 *  * Copyright © 2024. Toyota Motors North America Inc
 *  * All rights reserved.
 *
 */

package com.toyota.oneapp.features.chargehistory.domain.model

import android.os.Parcelable
import androidx.annotation.DrawableRes
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargehistory.util.ChargeHistoryUtil.LOCATION_HOME
import com.toyota.oneapp.features.chargeinfo.domain.model.PriceDetail
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthdetail.domain.DateModel
import kotlinx.parcelize.Parcelize

@Parcelize
data class ChargeSessionModel(
    @DrawableRes val iconRes: Int?,
    val locationType: String,
    val isEco: Boolean,
    val ecoDuration: Int,
    val socBeforeCharging: String,
    val socAfterCharging: String,
    val totalCharge: String,
    val address: String,
    val displayDate: String,
    val dateModel: DateModel?,
    val timeRange: String,
    val duration: String,
    val detailedDuration: String,
    val priceDetail: PriceDetail?,
) : Parcelable

fun ChargeSessionModel.toInfoItemsList(): List<ChargeInfoItem> {
    val infoItemList = arrayListOf<ChargeInfoItem>()
    if (locationType.uppercase() == LOCATION_HOME) {
        infoItemList.add(this.toEcoInfoItem())
    }
    infoItemList.addAll(
        listOf(
            ChargeInfoItem.SOCInfoItem(
                socTitle = R.string.charge_level,
                socBefore = socBeforeCharging,
                socAfter = socAfterCharging,
            ),
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.guest_time,
                value = timeRange,
                otherAccessibility = AccessibilityId.ID_TIME_INFO_ITEM,
            ),
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.duration,
                value = duration,
                otherAccessibility = AccessibilityId.ID_DURATION_INFO_ITEM,
            ),
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.energy_used,
                value = "$totalCharge kWh",
                otherAccessibility = AccessibilityId.ID_ENERGY_USED_INFO_ITEM,
            ),
        ),
    )
    priceDetail?.let {
        if (it.digits.isNotEmpty()) {
            infoItemList.add(
                ChargeInfoItem.OtherInfoItem(
                    otherTitle = R.string.charge_info_card,
                    value = "**** ${it.digits}",
                    otherAccessibility = AccessibilityId.ID_CARD_INFO_ITEM,
                ),
            )
        }
    }
    return infoItemList
}

private fun ChargeSessionModel.toEcoInfoItem(): ChargeInfoItem =
    when {
        isEco -> {
            ChargeInfoItem.EcoInfoItem(
                ecoTitle = R.string.eco_charge,
                value = R.string.eco_earned_leaf,
            )
        }
        ecoDuration >= 30 -> {
            ChargeInfoItem.OtherInfoItem(
                otherTitle = R.string.eco_charge,
                value = null,
                valueRes = R.string.eco_pending,
                otherAccessibility = AccessibilityId.ID_ECO_CHARGE_INFO_ITEM,
            )
        }
        else -> {
            ChargeInfoItem.EcoFindOutMoreItem(
                ecoTitle = R.string.eco_charge,
                value = R.string.eco_not_eligible,
            )
        }
    }
