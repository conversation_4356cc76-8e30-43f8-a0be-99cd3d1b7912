/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.chargehistory.application.ChargeHistoryNavigationState
import com.toyota.oneapp.features.chargehistory.application.ChargeHistoryScreenState
import com.toyota.oneapp.features.chargehistory.application.ChargeHistoryUseCase
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeSessionModel
import com.toyota.oneapp.features.chargehistory.domain.model.EmptyChargeHistoryModel
import com.toyota.oneapp.features.chargehistory.domain.model.toInfoItemsList
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.util.getDateNameAsStringFormats
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import java.time.YearMonth
import javax.inject.Inject
import kotlin.coroutines.suspendCoroutine

@HiltViewModel
class ChargeHistoryViewModel
    @Inject
    constructor(
        private val chargeHistoryUseCase: ChargeHistoryUseCase,
        private val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel<ChargeHistoryUIState, ChargeHistoryEvent>() {
        private var isAddressExists = true

        override fun defaultState() = ChargeHistoryUIState()

        fun reset() {
            defaultState()
        }

        fun resetNavigationState() {
            state.update { _state ->
                _state.copy(navigationState = ChargeHistoryNavigationState.Init)
            }
        }

        private fun logAnalytics(
            group: AnalyticsEvent,
            eventName: String,
        ) {
            analyticsLogger.logEventWithParameter(group.eventName, eventName)
        }

        override fun onEvent(event: ChargeHistoryEvent) {
            when (event) {
                is ChargeHistoryEvent.InitScreen -> {
                    initChargeHistoryScreen()
                }
                is ChargeHistoryEvent.OnLogEvent -> {
                    logAnalytics(event.group, event.name)
                }
                is ChargeHistoryEvent.OnHistoryItemClicked -> {
                    val infoList = event.uiModel.toInfoItemsList()
                    state.update { _state ->
                        _state.copy(
                            navigationState =
                                ChargeHistoryNavigationState.ChargeDetailNavigation(
                                    event.uiModel,
                                    infoList,
                                ),
                        )
                    }
                }
                is ChargeHistoryEvent.OnShowUnknownLocationInfo -> {
                    state.update { _state ->
                        _state.copy(
                            navigationState = ChargeHistoryNavigationState.ShowUnknownLocationPopup,
                        )
                    }
                }
                is ChargeHistoryEvent.OnLaunchMonthYearPicker -> {
                    state.update { _state ->
                        _state.copy(
                            navigationState = ChargeHistoryNavigationState.LaunchMonthYearPicker,
                        )
                    }
                }
                is ChargeHistoryEvent.OnFilterByChargeType -> {
                    state.update { _state ->
                        _state.copy(
                            isFilterByPublicType = event.isPublic,
                            isFilterByHomeType = event.isHome,
                            selectedChargeType =
                                when {
                                    event.isHome -> {
                                        ChargeType.HOME
                                    }
                                    event.isPublic -> {
                                        ChargeType.PUBLIC
                                    }
                                    else -> {
                                        ChargeType.NONE
                                    }
                                },
                        )
                    }
                    viewModelScope.launch(dispatcherProvider.main()) {
                        val chargeSessionsDeffered = async { fetchChargeHistoryList() }
                        updateScreenState(chargeSessionsDeffered.await())
                    }
                }
                is ChargeHistoryEvent.OnFilterByMonth -> {
                    state.update { _state ->
                        _state.copy(
                            isFilterByMonth = true,
                            selectedMonth = event.month,
                            selectedYear = event.year,
                            monthLabel = "${event.monthStr.uppercase()} ${event.year}",
                        )
                    }
                    viewModelScope.launch(dispatcherProvider.main()) {
                        val chargeSessionsDeffered = async { fetchChargeHistoryList() }
                        updateScreenState(chargeSessionsDeffered.await())
                    }
                }
            }
        }

        private fun initChargeHistoryScreen() {
            viewModelScope.launch(dispatcherProvider.main()) {
                state.update { _state ->
                    _state.copy(screenState = ChargeHistoryScreenState.Loading)
                }
                val checkAddressExistDeffered = async { checkProfileAddressExists() }
                val chargeSessionsDeffered = async { fetchChargeHistoryList() }

                isAddressExists = checkAddressExistDeffered.await()
                updateScreenState(chargeSessionsDeffered.await())
            }
        }

        private suspend fun fetchChargeHistoryList(): ChargeHistoryScreenState? =
            suspendCoroutine { continuation ->
                viewModelScope.launch(dispatcherProvider.main()) {
                    state.update { _state ->
                        _state.copy(screenState = ChargeHistoryScreenState.Loading)
                    }
                    applicationData.getSelectedVehicleState().value?.let { vehicle ->
                        val (startDate, endDate) =
                            calculateStartDateEndDate(
                                state.value.selectedMonth,
                                state.value.selectedYear,
                            )
                        chargeHistoryUseCase
                            .fetchChargeSessionsList(
                                vin = vehicle.vin,
                                startDate = startDate,
                                endDate = endDate,
                                chargeType = state.value.selectedChargeType.type,
                            ).flowOn(dispatcherProvider.io())
                            .collect {
                                continuation.resumeWith(Result.success(it))
                            }
                    }
                }
            }

        private suspend fun checkProfileAddressExists(): Boolean =
            suspendCoroutine { continuation ->
                viewModelScope.launch(dispatcherProvider.main()) {
                    applicationData.getSelectedVehicleState().value?.let { vehicle ->
                        chargeHistoryUseCase
                            .checkProfileAddressExists(brand = vehicle.brand)
                            .flowOn(dispatcherProvider.io())
                            .collect {
                                continuation.resumeWith(Result.success(it))
                            }
                    }
                }
            }

        private fun updateScreenState(screenState: ChargeHistoryScreenState?) {
            if (screenState == null) {
                state.update { _state ->
                    _state.copy(screenState = constructNoChargeHistory())
                }
                return
            }
            state.update { _state ->
                _state.copy(screenState = screenState)
            }
        }

        private fun constructNoChargeHistory(): ChargeHistoryScreenState.NoChargeHistory {
            val uiModel =
                when {
                    state.value.isFilterByHomeType &&
                        !isAddressExists -> {
                        EmptyChargeHistoryModel.NoProfileAddress(
                            R.drawable.ic_location,
                            R.string.no_saved_home_address,
                            R.string.no_saved_home_address_description,
                        )
                    }
                    state.value.isFilterByMonth -> {
                        EmptyChargeHistoryModel.NoChargeSessionsForMonth(
                            R.drawable.ic_wall_socket_1_1,
                            R.string.no_charging_sessions,
                            R.string.no_charging_sessions_description,
                        )
                    }
                    else -> {
                        EmptyChargeHistoryModel.NoChargeHistory(
                            R.drawable.ic_schedule,
                            R.string.no_charging_history,
                            R.string.no_charging_history_description,
                        )
                    }
                }
            return ChargeHistoryScreenState.NoChargeHistory(uiModel)
        }

        private fun calculateStartDateEndDate(
            selectedMonth: Int,
            selectedYear: Int,
        ): Pair<String, String> {
            if (selectedMonth == 0 || selectedYear == 0) {
                return Pair("", "")
            }

            val yearMonthObj = YearMonth.of(selectedYear, selectedMonth)
            val startDate = "01-$selectedMonth-$selectedYear"
            val endDate = "${yearMonthObj.lengthOfMonth()}-$selectedMonth-$selectedYear"

            return Pair(
                getDateNameAsStringFormats(startDate, "dd-MM-yyyy", "yyyy-MM-dd"),
                getDateNameAsStringFormats(endDate, "dd-MM-yyyy", "yyyy-MM-dd"),
            )
        }
    }

sealed class ChargeHistoryEvent {
    object InitScreen : ChargeHistoryEvent()

    data class OnHistoryItemClicked(
        val uiModel: ChargeSessionModel,
    ) : ChargeHistoryEvent()

    object OnShowUnknownLocationInfo : ChargeHistoryEvent()

    object OnLaunchMonthYearPicker : ChargeHistoryEvent()

    data class OnFilterByChargeType(
        val isPublic: Boolean,
        val isHome: Boolean,
    ) : ChargeHistoryEvent()

    data class OnFilterByMonth(
        val month: Int,
        val monthStr: String,
        val year: Int,
    ) : ChargeHistoryEvent()

    data class OnLogEvent(
        val group: AnalyticsEvent,
        val name: String,
    ) : ChargeHistoryEvent()
}

data class ChargeHistoryUIState(
    val isFilterByPublicType: Boolean = false,
    val isFilterByHomeType: Boolean = false,
    val isFilterByMonth: Boolean = false,
    val selectedChargeType: ChargeType = ChargeType.NONE,
    val selectedMonth: Int = 0,
    val selectedYear: Int = 0,
    val monthLabel: String? = null,
    val navigationState: ChargeHistoryNavigationState = ChargeHistoryNavigationState.Init,
    val screenState: ChargeHistoryScreenState = ChargeHistoryScreenState.Init,
)

enum class ChargeType(
    val type: String,
) {
    HOME("home"),
    PUBLIC("public"),
    NONE(""),
}
