/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.dataaccess.service

import com.toyota.oneapp.features.chargehistory.dataaccess.servermodel.ChargeHistoryListResponse
import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.ChargeHistoryResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header

interface ChargeHistoryAPI {
    @GET("/charging/vehicle/charge-statistics")
    suspend fun fetchChargeHistoryData(
        @Header("X-BRAND") brand: String,
        @Header("X-GENERATION") generation: String,
        @Header("X-VIN") vin: String,
        @Header("X-report-type") reportType: String,
        @Header("X-MONTH") month: String?,
    ): Response<ChargeHistoryResponse?>

    @GET("/charging/vehicle/charge-history")
    suspend fun fetchAllChargeSessions(
        @Header("X-VIN") vin: String,
        @Header("x-start-date") startDate: String,
        @Header("x-end-date") endDate: String,
        @Header("x-charging-type") chargingType: String,
    ): Response<ChargeHistoryListResponse>
}
