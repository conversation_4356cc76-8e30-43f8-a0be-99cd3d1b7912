/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.domain.repo

import com.toyota.oneapp.features.chargehistory.dataaccess.servermodel.ChargeHistoryListResponse
import com.toyota.oneapp.network.Resource

interface ChargeHistoryRepo {
    suspend fun fetchChargeSessions(
        vin: String,
        startDate: String,
        endDate: String,
        chargeType: String,
    ): Resource<ChargeHistoryListResponse?>
}
