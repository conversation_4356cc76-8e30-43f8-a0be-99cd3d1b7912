/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.application

import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargehistory.dataaccess.servermodel.ChargeHistoryListResponse
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeSessionModel
import com.toyota.oneapp.features.chargehistory.domain.repo.ChargeHistoryRepo
import com.toyota.oneapp.features.chargehistory.util.ChargeHistoryUtil
import com.toyota.oneapp.features.chargehistory.util.ChargeHistoryUtil.ADDRESS_TYPE_WORK
import com.toyota.oneapp.features.chargehistory.util.ChargeHistoryUtil.LOCATION_HOME
import com.toyota.oneapp.features.chargehistory.util.ChargeHistoryUtil.LOCATION_PUBLIC
import com.toyota.oneapp.features.chargehistory.util.ChargeHistoryUtil.LOCATION_UNKNOWN
import com.toyota.oneapp.features.chargeinfo.dataaccess.servermodel.ChargeSession
import com.toyota.oneapp.features.chargeinfo.domain.model.toPriceModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoAddress
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoCustomer
import com.toyota.oneapp.features.core.commonapicalls.domain.repository.CommonApiRepository
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthdetail.domain.DateModel
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.StringUtil.capitalizeWords
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.getDateNameAsStringFormats
import com.toyota.oneapp.util.timeRange
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class ChargeHistoryLogic
    @Inject
    constructor(
        private val preferenceModel: OneAppPreferenceModel,
        private val chargeHistoryRepo: ChargeHistoryRepo,
        private val commonApiRepository: CommonApiRepository,
        private val dateUtil: DateUtil,
    ) : ChargeHistoryUseCase {
        override fun fetchChargeSessionsList(
            vin: String,
            startDate: String,
            endDate: String,
            chargeType: String,
        ): Flow<ChargeHistoryScreenState?> =
            flow {
                val response =
                    chargeHistoryRepo.fetchChargeSessions(
                        vin = vin,
                        startDate = startDate,
                        endDate = endDate,
                        chargeType = chargeType,
                    )

                if (response is Resource.Success) {
                    val chargeSessionsList = response.data?.toChargeSessionUIModelList(dateUtil) ?: emptyList()
                    if (chargeSessionsList.isNotEmpty()) {
                        emit(ChargeHistoryScreenState.ListChargeSessions(chargeSessionsList))
                    } else {
                        emit(null)
                    }
                } else {
                    emit(null)
                }
            }

        override fun checkProfileAddressExists(brand: String): Flow<Boolean> =
            flow {
                val response =
                    commonApiRepository.fetchProfileDetails(
                        brand = brand,
                        guid = preferenceModel.getGuid(),
                    )

                if (response is Resource.Success) {
                    emit(
                        response.data
                            ?.payload
                            ?.customer
                            ?.isAddressExist() ?: false,
                    )
                } else {
                    emit(false)
                }
            }
    }

private fun ChargeHistoryListResponse.toChargeSessionUIModelList(dateUtil: DateUtil): List<ChargeSessionModel> {
    val uiModelList = arrayListOf<ChargeSessionModel>()
    chargingSessions?.forEach { chargeSession ->
        val uiModel =
            ChargeSessionModel(
                iconRes = getIconRes(chargeSession),
                locationType = getLocationType(chargeSession),
                isEco = chargeSession.watttimeDetails?.chargeClasification == "echo",
                ecoDuration = getEcoPendingDuration(dateUtil, chargeSession.endTime),
                socBeforeCharging = chargeSession.socBeforeCharging ?: "0%",
                socAfterCharging = chargeSession.socAfterCharging ?: "0%",
                totalCharge = chargeSession.totalChargeKwhr ?: "0",
                address = chargeSession.address.orEmpty(),
                duration =
                    ChargeHistoryUtil.calculateChargingDuration(
                        dateUtil,
                        false,
                        chargeSession.startTime,
                        chargeSession.endTime,
                    ),
                detailedDuration =
                    ChargeHistoryUtil.calculateChargingDuration(
                        dateUtil,
                        true,
                        chargeSession.startTime,
                        chargeSession.endTime,
                    ),
                displayDate = ChargeHistoryUtil.parseDate(dateUtil, chargeSession.endTime),
                dateModel = chargeSession.endTime?.toDateModel(),
                priceDetail = chargeSession.cdrDetails?.toPriceModel(),
                timeRange = chargeSession.startTime.orEmpty().timeRange(chargeSession.endTime.orEmpty()),
            )
        uiModelList.add(uiModel)
    }
    return uiModelList
}

private fun getLocationType(chargeSession: ChargeSession): String {
    val partnerName = chargeSession.cdrDetails?.partnerName.orEmpty()
    val chargeLocationType =
        if (partnerName.isNotEmpty()) {
            partnerName.capitalizeWords()
        } else if (chargeSession.chargeLocationType?.isNotEmpty() == true) {
            chargeSession.chargeLocationType.capitalizeWords()
        } else {
            LOCATION_PUBLIC
        }

    return chargeLocationType
}

private fun getIconRes(chargeSession: ChargeSession): Int? {
    val locationType = getLocationType(chargeSession)
    return when (locationType.uppercase()) {
        LOCATION_HOME -> {
            R.drawable.ic_ev_home
        }
        LOCATION_UNKNOWN -> {
            null
        }
        else -> {
            R.drawable.ic_pin
        }
    }
}

private fun getEcoPendingDuration(
    dateUtil: DateUtil,
    chargeEndTime: String?,
): Int {
    val currentTime = dateUtil.getCurrentTime()
    val ecoDuration =
        ChargeHistoryUtil.calculateChargingDuration(
            dateUtil,
            false,
            chargeEndTime,
            currentTime,
        )
    return try {
        ecoDuration.split(" ")[0].toInt()
    } catch (ex: NumberFormatException) {
        0
    }
}

private fun String.toDateModel(): DateModel =
    DateModel(
        day =
            getDateNameAsStringFormats(
                date = this,
                inputFormat = ToyotaConstants.STANDARD_UTC_DATE_FORMAT,
                outputFormat = "dd",
            ),
        month =
            getDateNameAsStringFormats(
                date = this,
                inputFormat = ToyotaConstants.STANDARD_UTC_DATE_FORMAT,
                outputFormat = "MMM",
            ),
        date =
            getDateNameAsStringFormats(
                date = this,
                inputFormat = ToyotaConstants.STANDARD_UTC_DATE_FORMAT,
                outputFormat = "MMM dd, yyyy",
            ),
    )

private fun ProfileInfoCustomer.isAddressExist(): Boolean {
    var profileAddress: ProfileInfoAddress? = null
    if (addresses != null && addresses.size > 1) {
        profileAddress =
            addresses.firstOrNull { address ->
                address.addressType.uppercase() == ADDRESS_TYPE_WORK
            }
    }
    profileAddress =
        if (addresses.isNullOrEmpty()) {
            null
        } else {
            addresses.first()
        }
    return profileAddress != null
}
