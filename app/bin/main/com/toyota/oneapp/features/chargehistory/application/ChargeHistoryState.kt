/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.application

import com.toyota.oneapp.features.chargehistory.domain.model.ChargeInfoItem
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeSessionModel
import com.toyota.oneapp.features.chargehistory.domain.model.EmptyChargeHistoryModel

sealed class ChargeHistoryScreenState {
    object Init : ChargeHistoryScreenState()

    object Loading : ChargeHistoryScreenState()

    data class ListChargeSessions(
        val sessionsList: List<ChargeSessionModel>,
    ) : ChargeHistoryScreenState()

    data class NoChargeHistory(
        val uiModel: EmptyChargeHistoryModel,
    ) : ChargeHistoryScreenState()
}

sealed class ChargeHistoryNavigationState {
    object Init : ChargeHistoryNavigationState()

    data class ChargeDetailNavigation(
        val uiModel: ChargeSessionModel,
        val infoList: List<ChargeInfoItem>,
    ) : ChargeHistoryNavigationState()

    object LaunchMonthYearPicker : ChargeHistoryNavigationState()

    object ShowUnknownLocationPopup : ChargeHistoryNavigationState()
}
