/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.di

import com.toyota.oneapp.features.chargehistory.application.ChargeHistoryLogic
import com.toyota.oneapp.features.chargehistory.application.ChargeHistoryUseCase
import com.toyota.oneapp.features.chargehistory.dataaccess.repository.ChargeHistoryDefaultRepo
import com.toyota.oneapp.features.chargehistory.domain.repo.ChargeHistoryRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class ChargeHistoryModule {
    @Binds
    abstract fun bindChargeHistoryRepo(repo: ChargeHistoryDefaultRepo): ChargeHistoryRepo

    @Binds
    abstract fun bindChargeHistoryUseCase(logic: ChargeHistoryLogic): ChargeHistoryUseCase
}
