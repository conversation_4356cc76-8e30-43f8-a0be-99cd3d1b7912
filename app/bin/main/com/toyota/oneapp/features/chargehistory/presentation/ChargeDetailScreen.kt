/*
 * *
 *  * Copyright © 2024. Toyota Motors North America Inc
 *  * All rights reserved.
 *
 */

package com.toyota.oneapp.features.chargehistory.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Divider
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeInfoItem
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeSessionModel
import com.toyota.oneapp.features.chargehistory.domain.model.toInfoItemsList
import com.toyota.oneapp.features.chargeinfo.domain.model.PriceDetail
import com.toyota.oneapp.features.chargeinfo.presentation.FindOutMoreScreen
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OADateLayout
import com.toyota.oneapp.features.core.composable.OAFullScreenBottomSheetLayout
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.vehiclehealth.vehiclehealthdetail.domain.DateModel

@Composable
fun ChargeDetailScreen(
    viewModel: ChargeHistoryViewModel,
    uiModel: ChargeSessionModel?,
    chargeInfoItemList: List<ChargeInfoItem>,
    navController: NavHostController,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()
    val bottomSheet = LocalBottomSheet.current

    ChargeDetailContent(
        uiModel = uiModel,
        chargeInfoItemList = chargeInfoItemList,
        modifier = modifier,
        onBack = { navController.popBackStack() },
    ) {
        coroutineScope.launchPrimaryBottomSheetAction(bottomSheet) {
            FindOutMoreScreen(
                bottomSheetState = it,
            ) { group, event ->
                viewModel.onEvent(ChargeHistoryEvent.OnLogEvent(group, event))
            }
        }
    }
}

@Composable
fun ChargeDetailContent(
    uiModel: ChargeSessionModel?,
    chargeInfoItemList: List<ChargeInfoItem>,
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
    onLaunchFindOutMore: () -> Unit,
) {
    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tile01,
        screenTitle = stringResource(id = R.string.charge_details),
        testTagId = AccessibilityId.ID_CHARGE_DETAILS_BACK_BTN,
        onBack = { onBack() },
        modifier =
            modifier
                .padding(vertical = 8.dp, horizontal = 16.dp),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(vertical = 16.dp),
        ) {
            uiModel?.dateModel?.let {
                OADateLayout(
                    dateModel = it,
                    modifier =
                        Modifier
                            .width(56.dp)
                            .height(53.dp)
                            .align(Alignment.CenterHorizontally)
                            .testTagID(AccessibilityId.ID_CHARGE_SESSION_DATE_LAYOUT)
                            .background(
                                color = AppTheme.colors.secondary01,
                                shape = RoundedCornerShape(10.dp),
                            ),
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            AddressCard(uiModel = uiModel)

            Spacer(modifier = Modifier.height(8.dp))

            chargeInfoItemList.forEachIndexed { index, infoItem ->
                InfoItem(
                    infoItem = infoItem,
                    canShowDivider = index != chargeInfoItemList.size - 1,
                ) {
                    onLaunchFindOutMore()
                }
            }

            uiModel?.priceDetail?.let {
                TotalAmountCard(
                    value = it.totalAmount,
                    modifier = Modifier.padding(top = 8.dp),
                )
            }
        }
    }
}

@Composable
private fun AddressCard(
    uiModel: ChargeSessionModel?,
    modifier: Modifier = Modifier,
) {
    Card(
        backgroundColor = AppTheme.colors.tertiary15,
        shape = RoundedCornerShape(8.dp),
        elevation = 4.dp,
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .testTagID(AccessibilityId.ID_CHARGE_SESSION_LOCATION_CARD),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .padding(16.dp),
        ) {
            Surface(
                shape = CircleShape,
                color = AppTheme.colors.success02,
                modifier =
                    Modifier
                        .size(60.dp)
                        .padding(8.dp),
            ) {
                Image(
                    modifier = Modifier.padding(8.dp),
                    painter = painterResource(id = uiModel?.iconRes ?: R.drawable.ic_pin),
                    colorFilter = ColorFilter.tint(AppTheme.colors.secondary01),
                    contentDescription = uiModel?.locationType,
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            Column {
                OABody4TextView(
                    text = uiModel?.locationType.orEmpty(),
                    color = AppTheme.colors.tertiary00,
                )

                OACallOut1TextView(
                    text = uiModel?.address.orEmpty(),
                    color = AppTheme.colors.tertiary03,
                )
            }
        }
    }
}

@Composable
fun InfoItem(
    infoItem: ChargeInfoItem,
    modifier: Modifier = Modifier,
    canShowDivider: Boolean = true,
    onLaunchFindOutMore: () -> Unit,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .testTagID(infoItem.accessibilityId),
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
        ) {
            OABody3TextView(
                text = stringResource(infoItem.title),
                color = AppTheme.colors.tertiary03,
            )

            when (infoItem) {
                is ChargeInfoItem.SOCInfoItem -> {
                    SOCValueWidget(
                        socBefore = infoItem.socBefore,
                        socAfter = infoItem.socAfter,
                    )
                }

                is ChargeInfoItem.EcoInfoItem,
                is ChargeInfoItem.EcoFindOutMoreItem,
                -> {
                    EcoValueWidget(
                        isNotEligible = infoItem.isNotEligible,
                        value = stringResource(infoItem.infoValue),
                        testTagId =
                            if (infoItem.isNotEligible) {
                                AccessibilityId.ID_ECO_NOT_ELIGIBLE_ICON
                            } else {
                                AccessibilityId.ID_ECO_BADGE_ICON
                            },
                    ) {
                        onLaunchFindOutMore()
                    }
                }

                is ChargeInfoItem.OtherInfoItem -> {
                    OACallOut1TextView(
                        text = infoItem.value ?: stringResource(infoItem.valueRes),
                        color = AppTheme.colors.tertiary03,
                    )
                }
            }
        }

        if (canShowDivider) {
            Divider(
                thickness = 1.dp,
                color = AppTheme.colors.tertiary10,
            )
        }
    }
}

@Composable
private fun SOCValueWidget(
    socBefore: String,
    socAfter: String,
    modifier: Modifier = Modifier,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.wrapContentSize(),
    ) {
        OACallOut1TextView(
            text = socBefore,
            color = AppTheme.colors.tertiary03,
        )

        Image(
            painter = painterResource(id = R.drawable.ic_line_arrow_right),
            contentDescription = "",
            modifier =
                Modifier
                    .wrapContentSize()
                    .padding(horizontal = 8.dp),
            colorFilter = ColorFilter.tint(AppTheme.colors.tertiary00),
        )

        OACallOut1TextView(
            text = socAfter,
            color = AppTheme.colors.tertiary03,
        )
    }
}

@Composable
private fun EcoValueWidget(
    isNotEligible: Boolean,
    value: String,
    testTagId: String,
    modifier: Modifier = Modifier,
    onLaunchFindOutMore: () -> Unit,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier =
            modifier
                .wrapContentSize()
                .testTagID(testTagId)
                .clickable {
                    if (isNotEligible) {
                        onLaunchFindOutMore()
                    }
                },
    ) {
        Image(
            painter =
                painterResource(
                    id =
                        if (isNotEligible) {
                            R.drawable.ic_history_info
                        } else {
                            R.drawable.ic_eco_badge
                        },
                ),
            contentDescription = "",
            modifier = Modifier.size(24.dp, 18.dp),
        )

        Spacer(modifier = Modifier.width(4.dp))

        OACallOut1TextView(
            text = value,
            color =
                if (isNotEligible) {
                    AppTheme.colors.secondary01
                } else {
                    AppTheme.colors.tertiary03
                },
        )
    }
}

@Composable
private fun TotalAmountCard(
    value: String,
    modifier: Modifier = Modifier,
) {
    Card(
        elevation = 0.dp,
        backgroundColor = AppTheme.colors.tertiary12,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth(),
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
        ) {
            OASubHeadLine1TextView(
                text = stringResource(R.string.Subscription_total),
                color = AppTheme.colors.tertiary03,
            )

            OASubHeadLine1TextView(
                text = value,
                color = AppTheme.colors.tertiary03,
            )
        }
    }
}

@Composable
private fun ChargeDetailContentPreview() {
    val uiModel =
        ChargeSessionModel(
            iconRes = R.drawable.ic_ev_home,
            locationType = "Home",
            isEco = true,
            ecoDuration = 1,
            socBeforeCharging = "64%",
            socAfterCharging = "67%",
            totalCharge = "1.958",
            address = "Frisco, Texas 75036, United States",
            displayDate = "Yesterday",
            timeRange = "11:00 PM - 01:00 AM",
            duration = "1 hr 15 min",
            detailedDuration = "1 hr 15 min 20 sec",
            dateModel = DateModel("03", "Dec", "03, Dec 2024"),
            priceDetail =
                PriceDetail(
                    digits = "1234",
                    totalAmount = "$10.0",
                ),
        )
    ChargeDetailContent(
        uiModel = uiModel,
        chargeInfoItemList = uiModel.toInfoItemsList(),
        onBack = {},
        onLaunchFindOutMore = {},
    )
}

@Preview
@Composable
private fun ChargeDetailScreenPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode = themeMode) {
        ChargeDetailContentPreview()
    }
}
