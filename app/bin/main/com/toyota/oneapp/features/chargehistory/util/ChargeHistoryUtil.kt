/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.util

import android.text.format.DateUtils
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants
import java.text.SimpleDateFormat
import java.util.Locale

object ChargeHistoryUtil {
    const val LOCATION_HOME = "HOME"
    const val LOCATION_UNKNOWN = "UNKNOWN"
    const val LOCATION_PUBLIC = "Public"
    const val CHARGE_TYPE_ECHO = "echo"
    const val SHORT_DAY = "d"
    const val SHORT_HOUR = "hr"
    const val SHORT_MINUTE = "min"
    const val SHORT_SECOND = "sec"
    const val LESS_THAN_A_SECOND = "Less than a second ago."
    const val HOUR = "hours"
    const val ADDRESS_TYPE_WORK = "WORK"

    fun parseDate(
        dateUtil: DateUtil,
        date: String?,
    ): String {
        val exactDateOutputFormat =
            SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
        dateUtil.getDateFromString(date)?.let {
            return when {
                DateUtils.isToday(it.time) ->
                    "Today"

                DateUtils.isToday(it.time + DateUtils.DAY_IN_MILLIS) ->
                    "Yesterday"

                else -> {
                    exactDateOutputFormat.format(it)
                }
            }
        }
        return ToyotaConstants.EMPTY_STRING
    }

    fun calculateChargingDuration(
        dateUtil: DateUtil,
        showDetailedDuration: Boolean,
        startTime: String?,
        endTime: String?,
    ): String {
        val duration = calculateDurationInMillis(dateUtil, startTime, endTime)

        if (duration != null && duration >= 1000L) {
            val seconds = ((duration / 1000) % 60).toInt()
            val minutes = ((duration / (1000 * 60)) % 60).toInt()
            val hours = ((duration / (1000 * 60 * 60)) % 24).toInt()
            val days = (duration / (1000 * 60 * 60 * 24)).toInt()
            val hoursDisplayText =
                if (showDetailedDuration) {
                    HOUR
                } else {
                    SHORT_HOUR
                }
            val secondsDisplayText =
                if (showDetailedDuration) {
                    " $seconds $SHORT_SECOND"
                } else {
                    ToyotaConstants.EMPTY_STRING
                }

            return when {
                (days == 0) && (hours != 0) -> {
                    "$hours $hoursDisplayText $minutes $SHORT_MINUTE$secondsDisplayText"
                }
                (hours == 0) && (minutes != 0) -> {
                    "$minutes $SHORT_MINUTE$secondsDisplayText"
                }
                (days == 0) -> {
                    "$seconds $SHORT_SECOND"
                }
                else -> {
                    "$days $SHORT_DAY $hours $hoursDisplayText $minutes m"
                }
            }
        } else {
            return LESS_THAN_A_SECOND
        }
    }

    private fun calculateDurationInMillis(
        dateUtil: DateUtil,
        startTime: String?,
        endTime: String?,
    ): Long? {
        val startTimeInMillis = dateUtil.getDateFromString(startTime)?.time
        val endTimeInMillis = dateUtil.getDateFromString(endTime)?.time

        if (startTimeInMillis != null && endTimeInMillis != null) {
            return endTimeInMillis - startTimeInMillis
        }
        return null
    }
}
