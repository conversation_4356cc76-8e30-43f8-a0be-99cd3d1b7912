/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.application

import kotlinx.coroutines.flow.Flow

interface ChargeHistoryUseCase {
    fun fetchChargeSessionsList(
        vin: String,
        startDate: String,
        endDate: String,
        chargeType: String,
    ): Flow<ChargeHistoryScreenState?>

    fun checkProfileAddressExists(brand: String): Flow<Boolean>
}
