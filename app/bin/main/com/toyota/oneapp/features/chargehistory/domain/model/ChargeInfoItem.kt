/*
 * *
 *  * Copyright © 2024. Toyota Motors North America Inc
 *  * All rights reserved.
 *
 */

package com.toyota.oneapp.features.chargehistory.domain.model

import android.os.Parcelable
import androidx.annotation.StringRes
import com.toyota.oneapp.features.core.util.AccessibilityId
import kotlinx.parcelize.Parcelize

@Parcelize
sealed class ChargeInfoItem(
    val accessibilityId: String,
    @StringRes val title: Int,
    @StringRes val infoValue: Int = Int.MIN_VALUE,
    val isNotEligible: Boolean = false,
) : Parcelable {
    data class EcoInfoItem(
        @StringRes val ecoTitle: Int,
        @StringRes val value: Int,
        val ecoAccessibilityId: String = AccessibilityId.ID_ECO_CHARGE_INFO_ITEM,
    ) : ChargeInfoItem(ecoAccessibilityId, ecoTitle, value)

    data class EcoFindOutMoreItem(
        @StringRes val ecoTitle: Int,
        @StringRes val value: Int,
        val ecoFindMoreAccessibilityId: String = AccessibilityId.ID_ECO_CHARGE_INFO_ITEM,
    ) : ChargeInfoItem(ecoFindMoreAccessibilityId, ecoTitle, value, true)

    data class SOCInfoItem(
        @StringRes val socTitle: Int,
        val socBefore: String,
        val socAfter: String,
        val socAccessibilityId: String = AccessibilityId.ID_CHARGE_LEVEL_INFO_ITEM,
    ) : ChargeInfoItem(socAccessibilityId, socTitle)

    data class OtherInfoItem(
        @StringRes val otherTitle: Int,
        val value: String?,
        val valueRes: Int = Int.MIN_VALUE,
        val otherAccessibility: String,
    ) : ChargeInfoItem(otherAccessibility, otherTitle, valueRes)
}
