/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.dataaccess.repository

import com.toyota.oneapp.features.chargehistory.dataaccess.servermodel.ChargeHistoryListResponse
import com.toyota.oneapp.features.chargehistory.dataaccess.service.ChargeHistoryAPI
import com.toyota.oneapp.features.chargehistory.domain.repo.ChargeHistoryRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class ChargeHistoryDefaultRepo
    @Inject
    constructor(
        private val chargeHistoryAPI: ChargeHistoryAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext),
        ChargeHistoryRepo {
        override suspend fun fetchChargeSessions(
            vin: String,
            startDate: String,
            endDate: String,
            chargeType: String,
        ): Resource<ChargeHistoryListResponse?> =
            makeApiCall {
                chargeHistoryAPI.fetchAllChargeSessions(
                    vin = vin,
                    startDate = startDate,
                    endDate = endDate,
                    chargingType = chargeType,
                )
            }
    }
