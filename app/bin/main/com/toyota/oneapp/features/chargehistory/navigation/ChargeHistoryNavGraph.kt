/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargehistory.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeInfoItem
import com.toyota.oneapp.features.chargehistory.domain.model.ChargeSessionModel
import com.toyota.oneapp.features.chargehistory.navigation.ChargeHistoryRoute.Companion.CHARGE_HISTORY_DETAIL_ARG
import com.toyota.oneapp.features.chargehistory.navigation.ChargeHistoryRoute.Companion.INFO_ITEMS_ARG
import com.toyota.oneapp.features.chargehistory.presentation.ChargeDetailScreen
import com.toyota.oneapp.features.chargehistory.presentation.ChargeHistoryScreen
import com.toyota.oneapp.features.chargehistory.presentation.ChargeHistoryViewModel
import com.toyota.oneapp.features.chargeinfo.navigation.ChargeInfoRoute
import com.toyota.oneapp.features.core.navigation.sharedViewModel

fun NavGraphBuilder.chargeHistoryNavGraph(navController: NavHostController) {
    navigation(
        route = ChargeInfoRoute.ChargeHistoryNestedRoute.route,
        startDestination = ChargeHistoryRoute.ChargeHistoryListScreen.route,
    ) {
        composable(route = ChargeHistoryRoute.ChargeHistoryListScreen.route) { entry ->
            val chargeHistoryViewModel =
                entry.sharedViewModel<ChargeHistoryViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )

            ChargeHistoryScreen(
                navController = navController,
                viewModel = chargeHistoryViewModel,
            )
        }
        composable(route = ChargeHistoryRoute.ChargeHistoryDetailScreen.route) { entry ->
            val detailUIModel =
                navController.previousBackStackEntry?.savedStateHandle?.get<ChargeSessionModel>(
                    CHARGE_HISTORY_DETAIL_ARG,
                )
            val infoItems =
                navController.previousBackStackEntry?.savedStateHandle?.get<List<ChargeInfoItem>>(
                    INFO_ITEMS_ARG,
                ) ?: emptyList()
            val chargeHistoryViewModel =
                entry.sharedViewModel<ChargeHistoryViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )

            ChargeDetailScreen(
                viewModel = chargeHistoryViewModel,
                uiModel = detailUIModel,
                chargeInfoItemList = infoItems,
                navController = navController,
            )
        }
    }
}

sealed class ChargeHistoryRoute(
    val route: String,
) {
    companion object {
        const val CHARGE_HISTORY_DETAIL_ARG = "arg_charge_history_detail"
        const val INFO_ITEMS_ARG = "arg_info_items"
    }

    object ChargeHistoryListScreen : ChargeHistoryRoute("charge_history_list")

    object ChargeHistoryDetailScreen : ChargeHistoryRoute("charge_history_detail")
}
