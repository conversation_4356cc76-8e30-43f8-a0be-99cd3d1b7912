/*
 * *
 *  * Copyright © 2024. Toyota Motors North America Inc
 *  * All rights reserved.
 *
 */

package com.toyota.oneapp.features.chargehistory.domain.model

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.toyota.oneapp.features.core.util.AccessibilityId

sealed class EmptyChargeHistoryModel(
    @DrawableRes val icon: Int,
    @StringRes val title: Int,
    @StringRes val subtitle: Int,
    val accessibilityId: String,
) {
    data class NoChargeHistory(
        @DrawableRes val noHistoryIcon: Int,
        @StringRes val noHistoryTitle: Int,
        @StringRes val noHistorySubtitle: Int,
        val noChargeHistoryAccessibilityId: String = AccessibilityId.ID_NO_CHARGE_HISTORY_YET_LAYOUT,
    ) : EmptyChargeHistoryModel(
            noHistoryIcon,
            noHistoryTitle,
            noHistorySubtitle,
            noChargeHistoryAccessibilityId,
        )

    data class NoChargeSessionsForMonth(
        @DrawableRes val noSessionsIcon: Int,
        @StringRes val noSessionsTitle: Int,
        @StringRes val noSessionsSubtitle: Int,
        val noChargeSessionsAccessibilityId: String = AccessibilityId.ID_NO_CHARGE_SESSIONS_LAYOUT,
    ) : EmptyChargeHistoryModel(
            noSessionsIcon,
            noSessionsTitle,
            noSessionsSubtitle,
            noChargeSessionsAccessibilityId,
        )

    data class NoProfileAddress(
        @DrawableRes val noProfileIcon: Int,
        @StringRes val noProfileTitle: Int,
        @StringRes val noProfileSubtitle: Int,
        val noProfileAccessibilityId: String = AccessibilityId.ID_NO_HOME_ADDRESS_LAYOUT,
    ) : EmptyChargeHistoryModel(
            noProfileIcon,
            noProfileTitle,
            noProfileSubtitle,
            noProfileAccessibilityId,
        )
}
