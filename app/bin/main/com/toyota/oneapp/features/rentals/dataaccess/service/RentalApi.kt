package com.toyota.oneapp.features.rentals.dataaccess.service

import com.toyota.oneapp.features.rentals.dataaccess.remotemodel.FxCfaiReservationListResponse
import retrofit2.Response
import retrofit2.http.*

interface RentalApi {
    @GET("/fleet/v2/reservations")
    suspend fun getReservationsList(
        @Header("driverId") driverId: String,
        @Query("rentalType") rentalType: String,
        @Query("status") status: String,
        @Query("sortBy") sortBy: String,
        @Query("sortDir") sortDir: String,
        @Query("offset") offset: String = "0",
        @Query("limit") limit: String = "10",
    ): Response<FxCfaiReservationListResponse?>
}
