package com.toyota.oneapp.features.rentals.dataaccess.repository

import com.toyota.oneapp.features.rentals.dataaccess.remotemodel.FxCfaiReservationListResponse
import com.toyota.oneapp.features.rentals.dataaccess.service.RentalApi
import com.toyota.oneapp.features.rentals.domain.repository.RentalRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class RentalDefaultRepository
    @Inject
    constructor(
        private val rentalApi: RentalApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : RentalRepository(errorParser, ioContext) {
        override suspend fun getReservationsList(
            driverId: String,
            rentalType: String,
            status: String,
            sortBy: String,
            sortDir: String,
        ): Resource<FxCfaiReservationListResponse?> {
            return makeApiCall {
                rentalApi.getReservationsList(
                    driverId = driverId,
                    rentalType = rentalType,
                    status = status,
                    sortDir = sortDir,
                    sortBy = sortBy,
                )
            }
        }
    }
