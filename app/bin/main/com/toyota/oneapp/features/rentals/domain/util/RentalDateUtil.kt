package com.toyota.oneapp.features.rentals.domain.util

import com.toyota.oneapp.features.rentals.domain.model.Reservations
import toyotaone.commonlib.log.LogTool
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

object RentalDateUtil {
    private const val TAG: String = "RentalDateUtil"

    fun getFormattedTime(
        startsAt: String?,
        dealerTimezoneOffset: String?,
    ): Reservations? {
        if (startsAt.isNullOrEmpty() || dealerTimezoneOffset.isNullOrEmpty()) {
            return null
        }
        return try {
            var zonedDateTime = ZonedDateTime.parse(startsAt)
            val offset = getTimezoneMinutesOffset(dealerTimezoneOffset)
            zonedDateTime = zonedDateTime.plusMinutes(offset)
            val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd")
            val timeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("h:mm a")
            val formattedDate: String = dateFormatter.format(zonedDateTime)
            val formattedTime: String = timeFormatter.format(zonedDateTime)

            val upcomingReservationMessage = "see upcoming $formattedDate at $formattedTime"
            LogTool.d(TAG, "UpcomingReservationMessage= $upcomingReservationMessage")
            Reservations(
                month = zonedDateTime.month.toString(),
                date = zonedDateTime.dayOfMonth.toString(),
                formattedDate = upcomingReservationMessage,
            )
        } catch (_: Exception) {
            null
        }
    }

    private fun getTimezoneMinutesOffset(timezone: String): Long {
        return try {
            val offsetString = timezone.replace("GMT", "")
            java.lang.Long.valueOf(offsetString)
        } catch (_: Exception) {
            0
        }
    }
}
