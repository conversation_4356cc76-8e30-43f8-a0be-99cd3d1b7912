package com.toyota.oneapp.features.rentals.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.rentals.application.RentalStateHolder
import com.toyota.oneapp.features.rentals.application.RentalUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class RentalViewModel
    @Inject
    constructor(
        internal val applicationData: ApplicationData,
        private val rentalUseCase: RentalUseCase,
        private val dispatcherProvider: DispatcherProvider,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel<RentalStateHolder, RentalEvent>() {
        override fun defaultState() = RentalStateHolder()

        override fun onEvent(event: RentalEvent) {
            when (event) {
                RentalEvent.DashboardMakeReservation -> {
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.DASHBOARD_CARD.eventName,
                        AnalyticsEventParam.DASHBOARD_NO_VEHICLE_MAKE_RES_CTA,
                    )
                }
                RentalEvent.DashboardUpcomingReservation -> {
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.DASHBOARD_CARD.eventName,
                        AnalyticsEventParam.DASHBOARD_NO_VEHICLE_UPCOMING_RES_CTA,
                    )
                }
            }
        }

        init {
            getReservations()
        }

        private fun getReservations() {
            viewModelScope.launch(dispatcherProvider.main()) {
                rentalUseCase.getReservationsList("UPCOMING").flowOn(dispatcherProvider.io()).collect { response ->
                    state.update {
                        it.copy(
                            state = response.state,
                        )
                    }
                }
            }
        }
    }
