package com.toyota.oneapp.features.rentals.application

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.rentals.dataaccess.remotemodel.toUiModel
import com.toyota.oneapp.features.rentals.domain.repository.RentalRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class RentalLogic
    @Inject
    constructor(
        val repository: RentalRepository,
        val applicationData: ApplicationData,
        private val preferenceModel: OneAppPreferenceModel,
    ) : RentalUseCase {
        override suspend fun getReservationsList(status: String): Flow<RentalStateHolder> {
            return flow {
                val response =
                    repository.getReservationsList(
                        driverId = preferenceModel.getGuid(),
                        status = status,
                    )

                var rentalStateHolder: RentalStateHolder? = null
                try {
                    response.data?.payload?.ShowResrv?.let {
                        if (it.isNotEmpty()) {
                            val uiData = it[0].toUiModel()
                            if (uiData != null) {
                                rentalStateHolder =
                                    RentalStateHolder(
                                        state = RentalState.Success(data = uiData),
                                    )
                            }
                        }
                    }
                } catch (_: Exception) {
                }

                rentalStateHolder?.let {
                    emit(it)
                } ?: emit(
                    RentalStateHolder(
                        state = RentalState.NoUpcomingReservationFound,
                    ),
                )
            }
        }
    }
