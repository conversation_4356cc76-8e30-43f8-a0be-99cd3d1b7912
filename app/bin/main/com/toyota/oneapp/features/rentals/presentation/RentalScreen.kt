package com.toyota.oneapp.features.rentals.presentation

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OAButton
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine2TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine4TextView
import com.toyota.oneapp.features.core.composable.OATabLabel01TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.rentals.application.RentalState
import com.toyota.oneapp.features.rentals.application.RentalStateHolder
import com.toyota.oneapp.ui.flutter.DashboardFlutterActivity
import com.toyota.oneapp.ui.flutter.Flutter_Rentals

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun RentalScreen(
    stateHolder: RentalStateHolder,
    onRentalEvent: (RentalEvent) -> Unit,
) {
    val context = LocalContext.current
    val state = stateHolder.state
    Card(
        backgroundColor = AppTheme.colors.tertiary15,
        elevation = 15.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(top = 24.dp),
    ) {
        Column {
            Row(
                modifier =
                    Modifier.padding(
                        top = 12.dp,
                        start = 16.dp,
                    ),
            ) {
                Surface(
                    shape = CircleShape,
                    color = AppTheme.colors.button02b,
                    modifier =
                        Modifier
                            .size(48.dp),
                ) {
                    Image(
                        modifier =
                            Modifier
                                .padding(12.dp),
                        painter = painterResource(R.drawable.ic_rental),
                        contentDescription = null,
                    )
                }
                Column(
                    modifier =
                        Modifier.padding(
                            top = 12.dp,
                            start = 12.dp,
                        ),
                ) {
                    OABody4TextView(
                        text = context.getString(R.string.rentals),
                        color = AppTheme.colors.tertiary03,
                    )
                }
            }
            Box(
                modifier =
                    Modifier
                        .padding(16.dp)
                        .fillMaxWidth(),
            ) {
                Image(
                    painter = painterResource(id = R.drawable.rental_bg),
                    contentDescription = null,
                    modifier = Modifier.fillMaxWidth(),
                    contentScale = ContentScale.FillWidth,
                )

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    OASubHeadLine2TextView(
                        text = context.getString(R.string.rent_a_toyota),
                        color = colorResource(R.color.black),
                        modifier = Modifier.padding(top = 16.dp),
                    )

                    OACallOut1TextView(
                        text = context.getString(R.string.find_your_fav_vehicle_to_rent),
                        color = colorResource(R.color.black),
                        modifier = Modifier.padding(top = 8.dp),
                    )
                }

                when (state) {
                    is RentalState.Loading -> {
                    }
                    is RentalState.NoUpcomingReservationFound -> {
                        OAButton(
                            text = stringResource(id = R.string.make_a_reservation),
                            bgColor = AppTheme.colors.button02a,
                            modifier =
                                Modifier
                                    .align(Alignment.BottomCenter)
                                    .padding(horizontal = 21.dp, vertical = 16.dp),
                            textColor = AppTheme.colors.primaryButton01,
                            textModifier =
                                Modifier
                                    .padding(vertical = 8.dp, horizontal = 15.dp),
                            click = {
                                onRentalEvent(RentalEvent.DashboardMakeReservation)
                                context.startActivity(
                                    DashboardFlutterActivity.createIntent(
                                        context,
                                        null,
                                        Flutter_Rentals,
                                    ),
                                )
                            },
                        )
                    }
                    is RentalState.Success -> {
                        Card(
                            backgroundColor = AppTheme.colors.tile05,
                            shape = RoundedCornerShape(8.dp),
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .wrapContentHeight()
                                    .align(Alignment.BottomCenter),
                            onClick = {
                                onRentalEvent(RentalEvent.DashboardUpcomingReservation)
                                context.startActivity(
                                    DashboardFlutterActivity.createIntent(
                                        context,
                                        null,
                                        Flutter_Rentals,
                                    ),
                                )
                            },
                        ) {
                            Row(
                                modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .padding(
                                            top = 16.dp,
                                            bottom = 13.dp,
                                            start = 13.dp,
                                            end = 16.dp,
                                        ),
                            ) {
                                Card(
                                    backgroundColor = AppTheme.colors.tile05,
                                    shape = RoundedCornerShape(8.dp),
                                    border = BorderStroke(2.dp, AppTheme.colors.secondary01),
                                    modifier =
                                        Modifier
                                            .width(48.dp)
                                            .wrapContentHeight(),
                                ) {
                                    Column {
                                        OATabLabel01TextView(
                                            text = state.data.month,
                                            color = AppTheme.colors.tertiary15,
                                            textAlign = TextAlign.Center,
                                            modifier =
                                                Modifier
                                                    .fillMaxWidth()
                                                    .align(CenterHorizontally)
                                                    .background(AppTheme.colors.secondary01),
                                        )
                                        OASubHeadLine4TextView(
                                            text = state.data.date,
                                            color = AppTheme.colors.tertiary00,
                                            modifier =
                                                Modifier
                                                    .align(CenterHorizontally)
                                                    .padding(bottom = 4.dp)
                                                    .background(AppTheme.colors.tile05),
                                        )
                                    }
                                }

                                Column(modifier = Modifier.padding(start = 15.dp)) {
                                    OABody4TextView(
                                        text = stringResource(id = R.string.upcoming_reservation),
                                        color = AppTheme.colors.tertiary03,
                                    )
                                    OACallOut1TextView(
                                        text = state.data.formattedDate,
                                        color = AppTheme.colors.tertiary05,
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
