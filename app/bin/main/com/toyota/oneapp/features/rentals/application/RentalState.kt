package com.toyota.oneapp.features.rentals.application

import com.toyota.oneapp.features.rentals.domain.model.Reservations
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.Brand

sealed interface RentalState {
    object Loading : RentalState

    class Success(val data: Reservations) : RentalState

    object NoUpcomingReservationFound : RentalState
}

data class RentalStateHolder(
    val isBrandToyota: Boolean = Brand.currentAppBrand().isToyota(),
    val isRentalEnabled: Boolean = AppLanguageUtils.isAmericanRegion() && Brand.currentAppBrand().isToyota(),
    val state: RentalState = RentalState.Loading,
)
