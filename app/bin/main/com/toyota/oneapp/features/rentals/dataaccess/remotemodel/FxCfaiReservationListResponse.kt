package com.toyota.oneapp.features.rentals.dataaccess.remotemodel

import com.toyota.oneapp.features.rentals.domain.model.Reservations
import com.toyota.oneapp.features.rentals.domain.util.RentalDateUtil

data class FxCfaiReservationListResponse(
    val payload: ReservationPayloadResponse? = null,
    val status: StatusResponse? = null,
)

data class ReservationPayloadResponse(
    val ShowResrv: List<ShowReserveResponse>? = null,
    val pagination: PaginationResponse? = null,
)

data class ShowReserveResponse(
    val createdAt: String? = null,
    val createdBy: String? = null,
    val destinationStationDetails: DestinationStationDetailsResponse? = null,
    val destinationStationId: String? = null,
    val driverId: String? = null,
    val endsAt: String? = null,
    val id: String? = null,
    val organizationId: String? = null,
    val originStationDetails: OriginStationDetailsResponse? = null,
    val originStationId: String? = null,
    val programId: String? = null,
    val referenceNumber: String? = null,
    val rentalType: String? = null,
    val rppFlag: Boolean? = null,
    val rppReservationDetails: RppReservationDetailsResponse? = null,
    val startsAt: String? = null,
    val status: String? = null,
    val type: String? = null,
    val updatedAt: String? = null,
    val updatedBy: String? = null,
)

fun ShowReserveResponse.toUiModel(): Reservations? {
    return RentalDateUtil.getFormattedTime(startsAt, originStationDetails?.timezone)
}

data class DestinationStationDetailsResponse(
    val description: String? = null,
    val formattedAddress: String? = null,
    val id: String? = null,
    val name: String? = null,
    val organizationId: String? = null,
    val supportPhoneNumber: String? = null,
    val timezone: String? = null,
)

data class DailyRateResponse(
    val amount: String? = null,
    val currency: String? = null,
    val units: String? = null,
)

data class HourlyRateResponse(
    val amount: String? = null,
    val currency: String? = null,
    val units: String? = null,
)

data class LateChargeResponse(
    val currency: String? = null,
)

data class MessageResponse(
    val description: String? = null,
    val responseCode: String? = null,
)

data class MonthlyRateResponse(
    val amount: String? = null,
    val currency: String? = null,
    val units: String? = null,
)

data class OriginStationDetailsResponse(
    val description: String? = null,
    val formattedAddress: String? = null,
    val id: String? = null,
    val name: String? = null,
    val organizationId: String? = null,
    val supportPhoneNumber: String? = null,
    val timezone: String? = null,
)

data class PaginationResponse(
    val limit: Int,
    val offset: Int,
    val total: Int,
)

data class PerMileAmountResponse(
    val amount: String? = null,
    val currency: String? = null,
)

data class PricingDetailsResponse(
    val dailyRate: DailyRateResponse,
    val hourlyRate: HourlyRateResponse,
    val lateCharge: LateChargeResponse,
    val monthlyRate: MonthlyRateResponse,
    val perMileAmount: PerMileAmountResponse,
    val ratePlusLate: RatePlusLateResponse,
    val rentalCharge: RentalChargeResponse,
    val rentalDays: String? = null,
    val rentalHours: String? = null,
    val surcharge: SurchargeResponse,
    val taxes: List<TaxesResponse>,
    val totalCharge: TotalChargeResponse,
    val totalExtras: TotalExtrasResponse,
    val totalFreeMiles: String? = null,
    val totalOtherCharges: TotalOtherChargesResponse,
    val totalTaxes: TotalTaxesResponse,
    val weeklyRate: WeeklyRateResponse,
)

data class RatePlusLateResponse(
    val currency: String? = null,
)

data class RentalChargeResponse(
    val amount: String? = null,
    val currency: String? = null,
)

data class RppReservationDetailsResponse(
    val pricingDetails: PricingDetailsResponse,
    val reservationRefNumber: String? = null,
    val status: String? = null,
    val vehicleDetails: VehicleDetailsResponse,
)

data class StatusResponse(
    val messages: List<MessageResponse>,
)

data class SurchargeResponse(
    val amount: String? = null,
    val currency: String? = null,
)

data class TaxesResponse(
    val amount: String? = null,
    val rate: String? = null,
)

data class TotalChargeResponse(
    val amount: String? = null,
    val currency: String? = null,
)

data class TotalExtrasResponse(
    val amount: String? = null,
    val currency: String? = null,
)

data class TotalOtherChargesResponse(
    val currency: String? = null,
)

data class TotalTaxesResponse(
    val amount: String? = null,
    val currency: String? = null,
)

data class VehicleDetailsResponse(
    val cityMPG: String? = null,
    val classDesc: String? = null,
    val hWYMPG: String? = null,
    val largeImgURL: String? = null,
    val luggage: String? = null,
    val mileage: String? = null,
    val pricingEstimate: String? = null,
    val seatingCapacity: String? = null,
    val smallImgURL: String? = null,
    val vehicleClass: String? = null,
    val vehicleGroup: String? = null,
    val vehicleMake: String? = null,
    val vehicleModel: String? = null,
)

data class WeeklyRateResponse(
    val amount: String? = null,
    val currency: String? = null,
    val units: String? = null,
)
