package com.toyota.oneapp.features.rentals.di

import com.toyota.oneapp.features.rentals.application.RentalLogic
import com.toyota.oneapp.features.rentals.application.RentalUseCase
import com.toyota.oneapp.features.rentals.dataaccess.repository.RentalDefaultRepository
import com.toyota.oneapp.features.rentals.domain.repository.RentalRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class RentalModule {
    @Binds
    abstract fun bindRentalRepo(rentalDefaultRepo: RentalDefaultRepository): RentalRepository

    @Binds
    abstract fun bindRentalUseCase(rentalLogic: RentalLogic): RentalUseCase
}
