package com.toyota.oneapp.features.rentals.domain.repository

import com.toyota.oneapp.features.rentals.dataaccess.remotemodel.FxCfaiReservationListResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import kotlin.coroutines.CoroutineContext

abstract class RentalRepository(
    errorParser: ErrorMessageParser,
    ioContext: CoroutineContext,
) : BaseRepository(errorParser, ioContext) {
    abstract suspend fun getReservationsList(
        driverId: String,
        rentalType: String = "RENTAL",
        status: String,
        sortBy: String = "startsAt",
        sortDir: String = "asc",
    ): Resource<FxCfaiReservationListResponse?>
}
