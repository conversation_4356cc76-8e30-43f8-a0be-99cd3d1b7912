package com.toyota.oneapp.features.dataconsent.application

import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.ConsentBody
import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.Consents
import com.toyota.oneapp.features.dataconsent.util.DataConsentConstants.BANNER
import com.toyota.oneapp.features.dataconsent.util.DataConsentConstants.CONSENT_ACCEPTED
import com.toyota.oneapp.features.dataconsent.util.DataConsentConstants.CONSENT_DECLINED
import com.toyota.oneapp.features.dataconsent.util.DataConsentConstants.CONSENT_VERSION_ID
import com.toyota.oneapp.features.dataconsent.util.DataConsentConstants.IONNA_CONSENT_ID
import com.toyota.oneapp.features.dataconsent.util.DataConsentConstants.IONNA_ELIGIBLE_CONSENT
import com.toyota.oneapp.features.dataconsent.util.DataConsentConstants.UPDATE_SUBSCRIPTION
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsentRequest

interface DataConsentProvider {
    fun consentName(): String

    fun consentId(): String

    fun consentRequest(): CombineDataConsentRequest

    fun consentBody(
        accepted: Boolean,
        vin: String,
    ): ConsentBody
}

// TODO: Tesla and Ionna use the same provider, rename this class, remove the interface and
//  refactor network call to remove ionna parameters
// https://toyotaconnected.atlassian.net/browse/OAD01-28069
class IonnaDataConsentProvider : DataConsentProvider {
    override fun consentName() = IONNA_ELIGIBLE_CONSENT

    override fun consentId() = IONNA_CONSENT_ID

    override fun consentRequest() =
        CombineDataConsentRequest(
            products = emptyList(),
            flowType = BANNER,
        )

    override fun consentBody(
        accepted: Boolean,
        vin: String,
    ): ConsentBody {
        val element =
            Consents(
                status = if (accepted) CONSENT_ACCEPTED else CONSENT_DECLINED,
                consentId = consentId(),
                versionId = CONSENT_VERSION_ID,
            )
        return ConsentBody(
            eventType = UPDATE_SUBSCRIPTION,
            vin = vin,
            consents = listOf(element),
        )
    }
}
