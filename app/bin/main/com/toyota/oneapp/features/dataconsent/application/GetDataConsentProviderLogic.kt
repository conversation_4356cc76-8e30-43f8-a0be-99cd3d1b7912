/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.dataconsent.application

import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.dataconsent.domain.usecase.GetDataConsentProviderUseCase
import javax.inject.Inject

class GetDataConsentProviderLogic
    @Inject
    constructor() : GetDataConsentProviderUseCase {
        override fun invoke(strategy: DataConsentStrategy): DataConsentProvider =
            when (strategy) {
                DataConsentStrategy.IONNA,
                DataConsentStrategy.TESLA,
                -> IonnaDataConsentProvider()
            }
    }
