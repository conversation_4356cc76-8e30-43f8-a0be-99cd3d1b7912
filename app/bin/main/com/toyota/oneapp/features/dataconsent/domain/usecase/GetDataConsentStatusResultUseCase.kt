/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.dataconsent.domain.usecase

import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy

fun interface GetDataConsentStatusResultUseCase {
    suspend operator fun invoke(strategy: DataConsentStrategy): Result<DataConsentStatus>
}
