/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.dataconsent.di

import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.features.chargingnetwork.domain.logic.IsLocalFeatureFlagEnabledForChargingNetworkLogic
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.IsLocalFeatureFlagEnabledForChargingNetworkUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
class DataConsentProviderModule {
    @Provides
    @Singleton
    fun provideIsLocalFlagEnabledForDataConsentUseCase(): IsLocalFeatureFlagEnabledForChargingNetworkUseCase =
        IsLocalFeatureFlagEnabledForChargingNetworkLogic(
            isLocalFlagEnabledForIonna = BuildConfig.SHOW_IONNA_CARD,
            isLocalFlagEnabledForTesla = BuildConfig.SHOW_TESLA_CARD,
            isLocalFlagEnabledForPlugAndCharge = BuildConfig.SHOW_PLUG_AND_CHARGE_CARD,
        )
}
