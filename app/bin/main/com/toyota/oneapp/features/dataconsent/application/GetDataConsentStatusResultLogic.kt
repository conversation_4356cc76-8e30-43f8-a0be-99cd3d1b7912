/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.application

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.dataconsent.domain.repo.DataConsentRepository
import com.toyota.oneapp.features.dataconsent.domain.usecase.GetDataConsentProviderUseCase
import com.toyota.oneapp.features.dataconsent.domain.usecase.GetDataConsentStatusResultUseCase
import com.toyota.oneapp.features.dataconsent.presentation.details.toDataConsentStatus
import com.toyota.oneapp.network.Resource
import javax.inject.Inject

class GetDataConsentStatusResultLogic
    @Inject
    constructor(
        private val repository: DataConsentRepository,
        private val getDataConsentProvider: GetDataConsentProviderUseCase,
        private val applicationData: ApplicationData,
    ) : GetDataConsentStatusResultUseCase {
        override suspend fun invoke(strategy: DataConsentStrategy): Result<DataConsentStatus> {
            val vehicleInfo = applicationData.getSelectedVehicle() ?: return getGenericFailure()
            val (consentId, response) =
                repository.fetchDataConsent(
                    consentProvider = getDataConsentProvider(strategy),
                    vehicleInfo = vehicleInfo,
                )
            return if (response is Resource.Success) {
                response.data?.payload?.let { payload ->
                    Result.success(payload.toDataConsentStatus(consentId))
                } ?: getGenericFailure()
            } else {
                Result.failure(Exception(response.message))
            }
        }

        private fun getGenericFailure(): Result<DataConsentStatus> = Result.failure(Exception())
    }
