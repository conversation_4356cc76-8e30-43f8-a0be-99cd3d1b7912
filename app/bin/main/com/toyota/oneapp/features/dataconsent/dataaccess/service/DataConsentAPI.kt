/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.dataaccess.service

import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.ConsentBody
import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.EligibleConsentsPayload
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsentRequest
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

interface DataConsentAPI {
    @POST("/oneapi/v2/dataconsent")
    suspend fun fetchDataConsentDetails(
        @Header("vin") vin: String,
        @Header("x-brand") brand: String,
        @Header("region") region: String,
        @Header("eligible-consent") consent: String,
        @Body consentRequest: CombineDataConsentRequest,
    ): Response<ApiResponse<EligibleConsentsPayload>?>

    @POST("/oneapi/v1/customerconsent")
    suspend fun acknowledgeDataConsent(
        @Body consentBody: ConsentBody,
    ): Response<BaseResponse?>
}
