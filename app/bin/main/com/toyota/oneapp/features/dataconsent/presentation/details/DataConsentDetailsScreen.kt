/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.
 */

package com.toyota.oneapp.features.dataconsent.presentation.details

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.ComposableListShimmer
import com.toyota.oneapp.features.core.composable.ExpandableCard
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.OAHtmlTextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.presentation.NavigationEvent
import com.toyota.oneapp.features.core.presentation.ProgressEvent
import com.toyota.oneapp.features.core.presentation.UiEvent
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider
import com.toyota.oneapp.features.dataconsent.presentation.details.DataConsentDetailsEvent.AcceptDataConsentClicked
import com.toyota.oneapp.features.dataconsent.presentation.details.DataConsentDetailsEvent.DeclineDataConsentClicked
import com.toyota.oneapp.features.dealerservice.presentation.common.components.OATopAppBar

@Composable
fun DataConsentDetailsScreen(
    navHostController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: DataConsentDetailsViewModel = hiltViewModel(),
) {
    BackHandler {
        navHostController.navigateUp()
    }

    var showProgress by remember { mutableStateOf(false) }

    if (showProgress) {
        ShowProgressIndicator(dialogState = true)
    }
    val state = viewModel.state.collectAsState().value

    LaunchedEffect(Unit) {
        viewModel.fetchDataConsentDetails()
    }

    LaunchedEffect(Unit) {
        viewModel.uiEvent.collect {
            when (it) {
                is UiEvent.Navigation -> {
                    if (it.event is NavigationEvent.NavigateUp) {
                        navHostController.navigateUp()
                    }
                }

                is UiEvent.Progress -> {
                    showProgress =
                        when (it.event) {
                            ProgressEvent.ShowProgress -> true
                            ProgressEvent.DismissProgress -> false
                        }
                }
            }
        }
    }

    Scaffold(
        backgroundColor = AppTheme.colors.tertiary15,
        topBar = {
            OATopAppBar(
                title = stringResource(R.string.charging_networks),
                titleTestTagId = AccessibilityId.ID_CA_APPBAR_TITLE,
                drawableRes = R.drawable.ic_back_arrow,
                onButtonPressed = { navHostController.navigateUp() },
            )
        },
        modifier = modifier,
    ) { contentPadding ->
        ComposableListShimmer(
            modifier = Modifier.padding(contentPadding),
            isLoading = state == DataConsentDetailsState.Loading,
            contentAfterLoading = {
                when (state) {
                    is DataConsentDetailsState.Success -> {
                        DataConsentDetailsContent(
                            enrollState = state.dataConsentDetails,
                            onEvent = viewModel::onEvent,
                        )
                    }

                    is DataConsentDetailsState.Error -> {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center,
                        ) {
                            val text = state.message ?: stringResource(id = R.string.generic_error)
                            OABody4TextView(
                                text = text,
                                color = AppTheme.colors.tertiary03,
                            )
                        }
                    }

                    else -> {
                        // do nothing
                    }
                }
            },
        )
    }
}

@Composable
private fun DataConsentDetailsContent(
    enrollState: DataConsentDetails,
    onEvent: (DataConsentDetailsEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier.verticalScroll(rememberScrollState())) {
        ExpandableCard(
            title = stringResource(R.string.ev_privacy_terms),
            isDefaultExpanded = true,
            modifier = Modifier.padding(horizontal = 16.dp),
        ) {
            Column(
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                OAHtmlTextView(
                    modifier = modifier.testTagID(AccessibilityId.ID_CA_PRIVACY_TERMS_INFO),
                    text = enrollState.descriptionBody,
                    textStyle = AppTheme.fontStyles.callout1,
                    color = AppTheme.colors.tertiary03,
                )
            }
        }
        OACallOut2TextView(
            text = stringResource(R.string.ev_ca_enrollment_decline),
            color = AppTheme.colors.primaryButton02,
            textAlign = TextAlign.Center,
            modifier =
                Modifier
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() },
                        onClick = {
                            onEvent(DeclineDataConsentClicked)
                        },
                    )
                    .fillMaxWidth(),
        )
        Spacer(modifier = Modifier.height(20.dp))
        PrimaryButton02(
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(bottom = 30.dp),
            text = stringResource(R.string.ev_ca_enrollment_accept),
            click = {
                onEvent.invoke(AcceptDataConsentClicked)
            },
        )
        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
@Preview(showBackground = true)
fun DataConsentDetailsContentPreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(themeMode) {
        DataConsentDetailsContent(
            enrollState =
                DataConsentDetails(
                    descriptionBody =
                        """
                        <!DOCTYPE html>
                        <html>
                        <body style="text-align:left">
                            <p>
                                By tapping Accept, you direct and authorize Toyota to share vehicle charging 
                                data in order to participate in environmental initiatives as described in 
                                the <a href="https://www.toyota.com/privacyvts/"><b>Connected Services 
                                Privacy Notice</b></a>. If you opt-in, Toyota may share your vehicle's 
                                VIN, location, and the amount of electricity used, to determine eligibility 
                                for incentive programs. Sharing information using Clean Assist allows 
                                Toyota to earn credits to promote electric vehicles. You can disable Clean 
                                Assist at any time in the OneApp consent preferences.
                                <br><br>
                                To learn more, please review the Clean Assist 
                                <a href="https://support.toyota.com/s/article/Clean-Assist?language=en_US">
                                <b>FAQs</b></a>.
                            </p>
                        </body>
                        </html>
                        """.trimIndent(),
                ),
            {},
        )
    }
}
