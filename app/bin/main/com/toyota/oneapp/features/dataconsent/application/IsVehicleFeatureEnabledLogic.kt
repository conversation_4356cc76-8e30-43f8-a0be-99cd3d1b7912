/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.dataconsent.application

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.dataconsent.domain.usecase.IsVehicleFeatureEnabledUseCase
import com.toyota.oneapp.model.vehicle.Feature
import javax.inject.Inject

class IsVehicleFeatureEnabledLogic
    @Inject
    constructor(
        private val applicationData: ApplicationData,
    ) : IsVehicleFeatureEnabledUseCase {
        override fun invoke(feature: Feature): Boolean =
            applicationData
                .getSelectedVehicle()
                ?.isFeatureEnabled(feature) == true
    }
