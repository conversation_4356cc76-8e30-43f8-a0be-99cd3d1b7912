/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.presentation.details

import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.EligibleConsentsPayload
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus

/**
 * This is generic class for any data consent details types
 */
data class DataConsentDetails(
    val descriptionBody: String = "",
)

fun EligibleConsentsPayload.toDataConsentStatus(consentId: String): DataConsentStatus {
    val acknowledgedConsent = acknowledgedConsents?.firstOrNull { it.consentId == consentId }
    return DataConsentStatus.from(acknowledgedConsent?.consentStatus)
}

fun EligibleConsentsPayload.toDataConsentDetails(consentId: String): DataConsentDetails {
    val eligibleConsent = eligibleConsents?.firstOrNull { it.consentId == consentId }
    return eligibleConsent?.description?.let {
        DataConsentDetails(
            descriptionBody = it.body.orEmpty(),
        )
    } ?: DataConsentDetails()
}
