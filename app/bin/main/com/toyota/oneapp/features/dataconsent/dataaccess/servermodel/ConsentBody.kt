/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.dataaccess.servermodel

data class ConsentBody(
    val eventType: String? = null,
    val vin: String? = null,
    val consents: List<Consents>? = emptyList(),
)

data class Consents(
    val consentId: String? = null,
    val status: String? = null,
    val versionId: String? = null,
)
