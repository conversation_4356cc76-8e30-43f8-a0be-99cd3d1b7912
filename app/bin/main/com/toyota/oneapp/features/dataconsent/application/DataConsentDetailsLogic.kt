/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.application

import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.dataconsent.domain.repo.DataConsentRepository
import com.toyota.oneapp.features.dataconsent.domain.usecase.DataConsentDetailsUseCase
import com.toyota.oneapp.features.dataconsent.domain.usecase.GetDataConsentProviderUseCase
import com.toyota.oneapp.features.dataconsent.presentation.details.DataConsentDetailsState.Error
import com.toyota.oneapp.features.dataconsent.presentation.details.DataConsentDetailsState.Success
import com.toyota.oneapp.features.dataconsent.presentation.details.toDataConsentDetails
import com.toyota.oneapp.features.dataconsent.util.DataConsentConstants.ENROLLMENT_SUCCESS_CODE
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class DataConsentDetailsLogic
    @Inject
    constructor(
        private val repository: DataConsentRepository,
        private val getDataConsentProvider: GetDataConsentProviderUseCase,
    ) : DataConsentDetailsUseCase {
        override fun fetchDataConsentDetails(
            vehicleInfo: VehicleInfo,
            strategy: DataConsentStrategy,
        ) = flow {
            val (consentId, response) =
                repository.fetchDataConsent(
                    consentProvider = getDataConsentProvider(strategy),
                    vehicleInfo = vehicleInfo,
                )
            if (response is Resource.Success) {
                val data = response.data?.payload
                val consentState =
                    data?.let {
                        Success(it.toDataConsentDetails(consentId))
                    } ?: Error()
                emit(consentState)
            } else {
                emit(Error(response.message))
            }
        }

        override fun acknowledgeDataConsent(
            vin: String,
            accepted: Boolean,
            strategy: DataConsentStrategy,
        ) = flow {
            val provider = getDataConsentProvider(strategy)
            val response = repository.acknowledgeDataConsent(provider.consentBody(accepted, vin))
            val messages = response.data?.status?.messages
            val responseCode = messages?.firstOrNull()?.responseCode
            val isSucess = response is Resource.Success
            emit(isSucess && responseCode.equals(ENROLLMENT_SUCCESS_CODE))
        }
    }
