/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.di

import com.toyota.oneapp.features.dataconsent.application.DataConsentDetailsLogic
import com.toyota.oneapp.features.dataconsent.application.GetDataConsentProviderLogic
import com.toyota.oneapp.features.dataconsent.application.GetDataConsentStatusResultLogic
import com.toyota.oneapp.features.dataconsent.application.IsVehicleFeatureEnabledLogic
import com.toyota.oneapp.features.dataconsent.dataaccess.repository.DataConsentDefaultRepository
import com.toyota.oneapp.features.dataconsent.domain.repo.DataConsentRepository
import com.toyota.oneapp.features.dataconsent.domain.usecase.DataConsentDetailsUseCase
import com.toyota.oneapp.features.dataconsent.domain.usecase.GetDataConsentProviderUseCase
import com.toyota.oneapp.features.dataconsent.domain.usecase.GetDataConsentStatusResultUseCase
import com.toyota.oneapp.features.dataconsent.domain.usecase.IsVehicleFeatureEnabledUseCase
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
interface DataConsentModule {
    @Binds
    fun bindDataConsentRepo(repo: DataConsentDefaultRepository): DataConsentRepository

    @Binds
    fun bindDataConsentStatusUseCase(logic: GetDataConsentStatusResultLogic): GetDataConsentStatusResultUseCase

    @Binds
    fun bindDataConsentDetailsUseCase(logic: DataConsentDetailsLogic): DataConsentDetailsUseCase

    @Binds
    fun bindIsVehicleFeatureEnabledUseCase(logic: IsVehicleFeatureEnabledLogic): IsVehicleFeatureEnabledUseCase

    @Binds
    fun bindGetDataConsentProviderUseCase(logic: GetDataConsentProviderLogic): GetDataConsentProviderUseCase
}
