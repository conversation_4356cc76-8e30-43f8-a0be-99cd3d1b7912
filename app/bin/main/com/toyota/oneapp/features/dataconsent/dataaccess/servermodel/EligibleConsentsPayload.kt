/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class AcknowledgedConsent(
    @SerializedName("alreadyAcknowledged")
    val alreadyAcknowledged: Boolean? = null,
    @SerializedName("category")
    val category: String? = null,
    @SerializedName("consentAcknowledgedDate")
    val consentAcknowledgedDate: String? = null,
    @SerializedName("consentEditable")
    val consentEditable: Boolean? = null,
    @SerializedName("consentId")
    val consentId: String? = null,
    @SerializedName("consentStatus")
    val consentStatus: String? = "Declined",
    @SerializedName("consentType")
    val consentType: String? = null,
    @SerializedName("declineMasterConsentEligible")
    val declineMasterConsentEligible: Boolean? = null,
    @SerializedName("detailSection")
    val detailSection: DetailSection? = null,
    @SerializedName("editable")
    val editable: String? = null,
    @SerializedName("imageUrl")
    val imageUrl: String? = null,
    @SerializedName("language")
    val language: String? = null,
    @SerializedName("manageSection")
    val manageSection: ManageSection? = null,
    @SerializedName("manageServiceEnabled")
    val manageServiceEnabled: Boolean? = null,
    @SerializedName("manageServiceText")
    val manageServiceText: String? = null,
    @SerializedName("masterConsent")
    val masterConsent: Boolean? = null,
    @SerializedName("moreDetailsText")
    val moreDetailsText: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("rank")
    val rank: String? = null,
    @SerializedName("version")
    val version: String? = null,
)

data class Consent(
    @SerializedName("consentId")
    val consentId: String? = null,
    @SerializedName("consentStatus")
    val consentStatus: String? = null,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("imageUrl")
    val imageUrl: String? = null,
    @SerializedName("masterConsent")
    val masterConsent: Boolean? = null,
    @SerializedName("name")
    val name: String? = null,
)

data class DataConsent(
    @SerializedName("can300")
    val can300: String? = null,
    @SerializedName("dealerContact")
    val dealerContact: String? = null,
    @SerializedName("serviceConnect")
    val serviceConnect: String? = null,
    @SerializedName("ubi")
    val ubi: String? = null,
)

data class DataConsents(
    @SerializedName("consents")
    val consents: List<Consent?>? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class DataShared(
    @SerializedName("items")
    val items: List<Item?>? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class DataSharedX(
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("objects")
    val objects: List<String?>? = null,
    @SerializedName("pageTitle")
    val pageTitle: String? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class DeclineConsent(
    @SerializedName("cancelKey")
    val cancelKey: String? = null,
    @SerializedName("declineKey")
    val declineKey: String? = null,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class DeclinePayload(
    @SerializedName("body")
    val body: String? = null,
    @SerializedName("imageUrl")
    val imageUrl: String? = null,
    @SerializedName("otherProducts")
    val otherProducts: OtherProductsX? = null,
    @SerializedName("pageTitle")
    val pageTitle: String? = null,
    @SerializedName("partnerProducts")
    val partnerProducts: PartnerProducts? = null,
    @SerializedName("positiveButtonText")
    val positiveButtonText: String? = null,
    @SerializedName("subscriptions")
    val subscriptions: Subscriptions? = null,
    @SerializedName("subscriptionsNotDeclined")
    val subscriptionsNotDeclined: SubscriptionsNotDeclined? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class DeclinePayloadX(
    @SerializedName("body")
    val body: String? = null,
    @SerializedName("negativeButtonText")
    val negativeButtonText: String? = null,
    @SerializedName("partnerProducts")
    val partnerProducts: PartnerProductsXX? = null,
    @SerializedName("positiveButtonText")
    val positiveButtonText: String? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class Description(
    @SerializedName("body")
    val body: String? = null,
    @SerializedName("declinePayload")
    val declinePayload: DeclinePayloadX? = null,
    @SerializedName("dialogs")
    val dialogs: List<Dialog>? = null,
    @SerializedName("negativeButtonText")
    val negativeButtonText: String? = null,
    @SerializedName("pageTitle")
    val pageTitle: String? = null,
    @SerializedName("positiveButtonText")
    val positiveButtonText: String? = null,
)

data class DetailSection(
    @SerializedName("animatedGifUrl")
    val animatedGifUrl: String? = null,
    @SerializedName("animationUrl")
    val animationUrl: String? = null,
    @SerializedName("consentAcknowledgedDate")
    val consentAcknowledgedDate: String? = null,
    @SerializedName("consentAcknowledgedDateInUTC")
    val consentAcknowledgedDateInUTC: String? = null,
    @SerializedName("dataShared")
    val dataShared: DataShared? = null,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("imageUrl")
    val imageUrl: String? = null,
    @SerializedName("otherProducts")
    val otherProducts: OtherProducts? = null,
    @SerializedName("pageTitle")
    val pageTitle: String? = null,
    @SerializedName("partnerProducts")
    val partnerProducts: PartnerProducts? = null,
    @SerializedName("serviceSubscriptions")
    val serviceSubscriptions: ServiceSubscriptions? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class Dialog(
    @SerializedName("body")
    val body: String? = null,
    @SerializedName("scheme")
    val scheme: String? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class EligibleConsent(
    @SerializedName("alreadyAcknowledged")
    val alreadyAcknowledged: Boolean? = null,
    @SerializedName("category")
    val category: String? = null,
    @SerializedName("consentEditable")
    val consentEditable: Boolean? = null,
    @SerializedName("consentId")
    val consentId: String? = null,
    @SerializedName("consentType")
    val consentType: String? = null,
    @SerializedName("declineMasterConsentEligible")
    val declineMasterConsentEligible: Boolean? = null,
    @SerializedName("description")
    val description: Description? = null,
    @SerializedName("editable")
    val editable: String? = null,
    @SerializedName("language")
    val language: String? = null,
    @SerializedName("manageServiceEnabled")
    val manageServiceEnabled: Boolean? = null,
    @SerializedName("masterConsent")
    val masterConsent: Boolean? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("rank")
    val rank: String? = null,
    @SerializedName("version")
    val version: String? = null,
)

data class Item(
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("items")
    val items: List<String?>? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class ManageSection(
    @SerializedName("body")
    val body: String? = null,
    @SerializedName("declinePayload")
    val declinePayload: DeclinePayload? = null,
    @SerializedName("dialogs")
    val dialogs: List<Dialog>? = null,
    @SerializedName("negativeButtonText")
    val negativeButtonText: String? = null,
    @SerializedName("pageTitle")
    val pageTitle: String? = null,
    @SerializedName("positiveButtonText")
    val positiveButtonText: String? = null,
)

data class MarketingConsents(
    @SerializedName("consents")
    val consents: List<Any?>? = null,
)

data class Message(
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("detailedDescription")
    val detailedDescription: String? = null,
    @SerializedName("responseCode")
    val responseCode: String? = null,
)

data class MoreDetails(
    @SerializedName("items")
    val items: List<Item>? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class OtherProducts(
    @SerializedName("products")
    val products: List<Product?>? = null,
    @SerializedName("sectionSubTitle")
    val sectionSubTitle: String? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class OtherProductsX(
    @SerializedName("products")
    val products: List<ProductX>? = null,
    @SerializedName("sectionSubTitle")
    val sectionSubTitle: String? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class PartnerConsents(
    @SerializedName("consents")
    val consents: List<Any?>? = null,
)

data class PartnerProducts(
    @SerializedName("products")
    val products: List<Any?>? = null,
    @SerializedName("sectionSubTitle")
    val sectionSubTitle: String? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class PartnerProductsXX(
    @SerializedName("products")
    val products: List<Any?>? = null,
)

data class EligibleConsentsPayload(
    @SerializedName("acknowledgedConsents")
    val acknowledgedConsents: List<AcknowledgedConsent>? = null,
    @SerializedName("dataConsent")
    val dataConsent: DataConsent? = null,
    @SerializedName("eligibleConsents")
    val eligibleConsents: List<EligibleConsent>? = null,
    @SerializedName("guid")
    val guid: String? = null,
    @SerializedName("language")
    val language: String? = null,
    @SerializedName("privacyConsents")
    val privacyConsents: PrivacyConsents? = null,
    @SerializedName("timestamp")
    val timestamp: String? = null,
    @SerializedName("vin")
    val vin: String? = null,
)

data class PrivacyConsents(
    @SerializedName("dataConsents")
    val dataConsents: DataConsents? = null,
    @SerializedName("marketingConsents")
    val marketingConsents: MarketingConsents? = null,
    @SerializedName("partnerConsents")
    val partnerConsents: PartnerConsents? = null,
    @SerializedName("smsConsents")
    val smsConsents: SmsConsents? = null,
    @SerializedName("staticData")
    val staticData: StaticData? = null,
    @SerializedName("sxmConsents")
    val sxmConsents: SxmConsents? = null,
    @SerializedName("termsOfUse")
    val termsOfUse: TermsOfUse? = null,
)

data class Product(
    @SerializedName("category")
    val category: String? = null,
    @SerializedName("imageUrl")
    val imageUrl: String? = null,
    @SerializedName("name")
    val name: String? = null,
)

data class ProductDetail(
    @SerializedName("dataShared")
    val dataShared: DataSharedX? = null,
    @SerializedName("declineConsent")
    val declineConsent: DeclineConsent? = null,
    @SerializedName("imageUrl")
    val imageUrl: String? = null,
    @SerializedName("moreDetails")
    val moreDetails: MoreDetails? = null,
    @SerializedName("name")
    val name: String? = null,
)

data class ProductX(
    @SerializedName("imageUrl")
    val imageUrl: String? = null,
    @SerializedName("name")
    val name: String? = null,
)

data class ServiceSubscriptions(
    @SerializedName("productDetails")
    val productDetails: List<ProductDetail>? = null,
    @SerializedName("sectionSubTitle")
    val sectionSubTitle: String? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class SmsConsents(
    @SerializedName("consents")
    val consents: List<Any?>? = null,
)

data class StaticData(
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("footerText")
    val footerText: String? = null,
    @SerializedName("imageUrl")
    val imageUrl: String? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class Status(
    @SerializedName("messages")
    val messages: List<Message?>? = null,
)

data class Subscription(
    @SerializedName("imageUrl")
    val imageUrl: String? = null,
    @SerializedName("name")
    val name: String? = null,
)

data class Subscriptions(
    @SerializedName("sectionSubTitle")
    val sectionSubTitle: String? = null,
    @SerializedName("sectionTitle")
    val sectionTitle: String? = null,
    @SerializedName("subscriptions")
    val subscriptions: List<Subscription?>? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class SubscriptionsNotDeclined(
    @SerializedName("sectionSubTitle")
    val sectionSubTitle: String? = null,
    @SerializedName("sectionTitle")
    val sectionTitle: String? = null,
    @SerializedName("subscriptions")
    val subscriptions: List<Any?>? = null,
    @SerializedName("title")
    val title: String? = null,
)

data class SxmConsents(
    @SerializedName("consents")
    val consents: List<Any?>? = null,
)

data class TermsOfUse(
    @SerializedName("consents")
    val consents: List<Any?>? = null,
)
