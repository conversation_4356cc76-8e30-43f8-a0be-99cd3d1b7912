/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.dataconsent.util

import androidx.annotation.StringRes
import com.toyota.oneapp.R
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus

@StringRes
internal fun DataConsentStatus.toStringResource(): Int =
    when (this) {
        DataConsentStatus.REGISTER -> R.string.register
        DataConsentStatus.ACCEPTED -> R.string.registered
        DataConsentStatus.DECLINED -> R.string.declined
    }
