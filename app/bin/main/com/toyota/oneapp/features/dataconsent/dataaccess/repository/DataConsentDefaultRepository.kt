/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.dataaccess.repository

import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.ConsentBody
import com.toyota.oneapp.features.dataconsent.dataaccess.service.DataConsentAPI
import com.toyota.oneapp.features.dataconsent.domain.repo.DataConsentRepository
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsentRequest
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class DataConsentDefaultRepository
    @Inject
    constructor(
        private val dataConsentAPI: DataConsentAPI,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), DataConsentRepository {
        override suspend fun fetchDataConsentDetails(
            vin: String,
            brand: String,
            region: String,
            consent: String,
            consentRequest: CombineDataConsentRequest,
        ) = makeApiCall {
            dataConsentAPI.fetchDataConsentDetails(
                vin = vin,
                brand = brand,
                region = region,
                consent = consent,
                consentRequest = consentRequest,
            )
        }

        override suspend fun acknowledgeDataConsent(body: ConsentBody) =
            makeApiCall {
                dataConsentAPI.acknowledgeDataConsent(body)
            }
    }
