/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.domain.repo

import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.ConsentBody
import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.EligibleConsentsPayload
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsentRequest
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse

interface DataConsentRepository {
    suspend fun fetchDataConsentDetails(
        vin: String,
        brand: String,
        region: String,
        consent: String,
        consentRequest: CombineDataConsentRequest,
    ): Resource<ApiResponse<EligibleConsentsPayload>?>

    suspend fun acknowledgeDataConsent(body: ConsentBody): Resource<BaseResponse?>
}
