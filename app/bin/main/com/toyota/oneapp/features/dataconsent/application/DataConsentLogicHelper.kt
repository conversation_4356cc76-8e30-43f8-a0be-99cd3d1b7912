/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.application

import com.toyota.oneapp.features.dataconsent.dataaccess.servermodel.EligibleConsentsPayload
import com.toyota.oneapp.features.dataconsent.domain.repo.DataConsentRepository
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.ApiResponse

internal suspend fun DataConsentRepository.fetchDataConsent(
    consentProvider: DataConsentProvider,
    vehicleInfo: VehicleInfo,
): Pair<String, Resource<ApiResponse<EligibleConsentsPayload>?>> {
    val consentName = consentProvider.consentName()
    val consentId = consentProvider.consentId()
    val request = consentProvider.consentRequest()
    val response =
        fetchDataConsentDetails(
            vin = vehicleInfo.vin,
            brand = vehicleInfo.brand,
            region = vehicleInfo.region,
            consent = consentName,
            consentRequest = request,
        )
    return Pair(consentId, response)
}
