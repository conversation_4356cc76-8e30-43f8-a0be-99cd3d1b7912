package com.toyota.oneapp.features.dataconsent.domain.model

enum class DataConsentStatus {
    REGISTER,
    ACCEPTED,
    DECLINED,
    ;

    companion object {
        fun from(value: String?) =
            entries.firstOrNull {
                it.name.equals(value, ignoreCase = true)
            } ?: REGISTER
    }
}

fun DataConsentStatus.isAccepted() = this == DataConsentStatus.ACCEPTED

fun DataConsentStatus.isDeclined() = this == DataConsentStatus.DECLINED
