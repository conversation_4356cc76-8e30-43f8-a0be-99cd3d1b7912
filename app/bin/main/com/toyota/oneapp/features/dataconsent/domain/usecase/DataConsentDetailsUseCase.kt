/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.dataconsent.domain.usecase

import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.dataconsent.presentation.details.DataConsentDetailsState
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface DataConsentDetailsUseCase {
    fun fetchDataConsentDetails(
        vehicleInfo: VehicleInfo,
        strategy: DataConsentStrategy,
    ): Flow<DataConsentDetailsState>

    fun acknowledgeDataConsent(
        vin: String,
        accepted: <PERSON><PERSON>an,
        strategy: DataConsentStrategy,
    ): Flow<Boolean>
}
