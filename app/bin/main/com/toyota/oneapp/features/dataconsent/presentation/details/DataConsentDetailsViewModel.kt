/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dataconsent.presentation.details

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.core.navigation.OANavArguments
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.core.presentation.NavigationEvent
import com.toyota.oneapp.features.core.presentation.ProgressEvent
import com.toyota.oneapp.features.core.presentation.UiEvent
import com.toyota.oneapp.features.core.presentation.UiEvent.Navigation
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy
import com.toyota.oneapp.features.dataconsent.domain.usecase.DataConsentDetailsUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class DataConsentDetailsViewModel
    @Inject
    constructor(
        private val detailsUseCase: DataConsentDetailsUseCase,
        private val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
        private val savedStateHandle: SavedStateHandle,
    ) : BaseViewModel<DataConsentDetailsState, DataConsentDetailsEvent>() {
        val strategy: DataConsentStrategy = getDataConsentStrategy()

        override fun defaultState() = DataConsentDetailsState.Loading

        override fun onEvent(event: DataConsentDetailsEvent) {
            when (event) {
                DataConsentDetailsEvent.DeclineDataConsentClicked -> {
                    acknowledgeDataConsent(accepted = false)
                }

                DataConsentDetailsEvent.AcceptDataConsentClicked -> {
                    acknowledgeDataConsent(accepted = true)
                }
            }
        }

        fun fetchDataConsentDetails() {
            applicationData.getSelectedVehicle()?.let { vehicleInfo ->
                viewModelScope.launch(dispatcherProvider.io()) {
                    detailsUseCase.fetchDataConsentDetails(
                        vehicleInfo = vehicleInfo,
                        strategy = strategy,
                    ).collect { detailState ->
                        state.update { detailState }
                    }
                }
            }
        }

        private fun getDataConsentStrategy(): DataConsentStrategy {
            val strategyParameter: String? = savedStateHandle[OANavArguments.STRATEGY]
            return if (strategyParameter == DataConsentStrategy.TESLA.name) {
                DataConsentStrategy.TESLA
            } else {
                DataConsentStrategy.IONNA
            }
        }

        internal fun acknowledgeDataConsent(accepted: Boolean) {
            applicationData.getSelectedVehicle()?.let {
                viewModelScope.launch(dispatcherProvider.io()) {
                    sendEvent(UiEvent.Progress(ProgressEvent.ShowProgress))
                    detailsUseCase.acknowledgeDataConsent(
                        vin = it.vin,
                        accepted = accepted,
                        strategy = strategy,
                    ).collect {
                        sendEvent(UiEvent.Progress(ProgressEvent.DismissProgress))
                        sendEvent(Navigation(NavigationEvent.NavigateUp))
                    }
                }
            }
        }
    }
