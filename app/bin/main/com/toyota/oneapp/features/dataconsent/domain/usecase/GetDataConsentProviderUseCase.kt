/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.dataconsent.domain.usecase

import com.toyota.oneapp.features.dataconsent.application.DataConsentProvider
import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStrategy

fun interface GetDataConsentProviderUseCase {
    operator fun invoke(strategy: DataConsentStrategy): DataConsentProvider
}
