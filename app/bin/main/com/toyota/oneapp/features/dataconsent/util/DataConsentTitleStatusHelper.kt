/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.dataconsent.util

import com.toyota.oneapp.features.dataconsent.domain.model.DataConsentStatus
import com.toyota.oneapp.features.entrollment.application.WalletState
import com.toyota.oneapp.features.entrollment.util.Constance
import com.toyota.oneapp.features.entrollment.util.Constance.SETUP_WALLET
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import com.toyota.oneapp.features.entrollment.application.WalletState.Success as WalletSuccess

/**
 * In the Station details, check if the VINlist feature has feature flag enabled if yes, then
 * - If the user does not have Wallet setup then, show the Setup Wallet button
 * - If user has already setup the Wallet then, show the status of the data consent ( Register/Accepted/Declined)
 * - Go through the consent and update the status appropriately,
 * - If user has accepted, then show the Unlock button.
 * - if user declined show Declined button with No Action.
 */
class DataConsentTitleStatusHelper
    @Inject
    constructor() {
        fun getTitle(
            walletState: StateFlow<WalletState>,
            consentStatus: DataConsentStatus?,
            station: ChargeStationInfo? = null,
        ): String {
            consentStatus ?: return SETUP_WALLET
            val walletStateValue = walletState.value
            val walletFetched = walletStateValue is WalletSuccess
            if (!walletFetched) return SETUP_WALLET
            val paymentMethod = (walletStateValue as WalletSuccess).data?.paymentMethod
            val hasCardSetup =
                paymentMethod
                    ?.card
                    ?.last4
                    .orEmpty()
                    .isNotEmpty()

            return if (!hasCardSetup) SETUP_WALLET else getTitle(consentStatus, station)
        }

        private fun getTitle(
            consentStatus: DataConsentStatus,
            station: ChargeStationInfo?,
        ): String {
            if (consentStatus != DataConsentStatus.ACCEPTED) {
                return when (consentStatus) {
                    DataConsentStatus.REGISTER -> Constance.REGISTER
                    DataConsentStatus.DECLINED -> Constance.DECLINED
                    else -> Constance.SETUP_WALLET
                }
            }

            // For accepted status, check if the station is a ChargePoint partner
            val isChargePointPartner =
                station?.let {
                    it.evEvSource.equals("chargepoint", ignoreCase = true) && it.evIsPartner
                } ?: false

            return if (isChargePointPartner) {
                Constance.UNLOCK_STATION
            } else {
                Constance.START_CHARGING
            }
        }
    }
