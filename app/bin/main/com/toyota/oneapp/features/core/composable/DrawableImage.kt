/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.core.composable

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

/**
 * Displays an image from a drawable resource using the given [drawableId].
 *
 * This composable wraps the [Image] composable with [painterResource] to easily show drawable assets.
 *
 * Example of usage:
 * ```kotlin
 * @Composable
 * fun ExampleUsage() {
 *     DrawableImage(
 *         drawableId = R.drawable.ic_launcher_foreground,
 *         contentDescription = R.string.app_name,
 *     )
 * }
 * ```
 * @param modifier Optional [Modifier] to be applied to the image.
 * @param drawableId Resource ID of the drawable to be shown. Must be a valid drawable resource.
 * @param contentDescriptionId Text ID used by accessibility services to describe what this image represents.
 * @param colorFilter Optional [ColorFilter] to apply to the image.
 */
@Composable
fun DrawableImage(
    modifier: Modifier = Modifier,
    @DrawableRes drawableId: Int,
    @StringRes contentDescriptionId: Int?,
    colorFilter: ColorFilter? = null,
) {
    Image(
        modifier = modifier,
        painter = painterResource(drawableId),
        contentDescription = contentDescriptionId?.let { stringResource(it) },
        colorFilter = colorFilter,
    )
}

@Composable
@Preview
private fun DrawableImagePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        DrawableImage(
            drawableId = R.drawable.amazon_music,
            contentDescriptionId = null,
        )
    }
}
