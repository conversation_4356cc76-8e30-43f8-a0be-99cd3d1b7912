package com.toyota.oneapp.features.core.commonapicalls.dataaccess.repository

import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeTimerRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CommonScheduleResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.EVChargeSessionResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.RealTimeStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.service.CommonApi
import com.toyota.oneapp.features.core.commonapicalls.domain.repository.CommonApiRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import java.util.UUID
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class CommonApiDefaultRepository
    @Inject
    constructor(
        val service: CommonApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : CommonApiRepository(errorParser, ioContext) {
        override suspend fun fetchChargeManagementDetail(
            vin: String,
            generation: String,
            brand: String,
        ): Resource<ElectricStatusResponse?> =
            makeApiCall {
                service.fetchChargeManagementDetail(
                    vin = vin,
                    generation = generation,
                    brand = brand,
                )
            }

        override suspend fun fetchProfileDetails(
            brand: String,
            guid: String,
        ): Resource<ProfileInfoResponse?> =
            makeApiCall {
                service.fetchProfileDetails(
                    brand = brand,
                    guid = guid,
                )
            }

        override suspend fun fetchEvChargingSessionDetails(
            requestId: String,
            chargingId: String,
            apiKey: String,
            partner: String,
        ): Resource<EVChargeSessionResponse?> =
            makeApiCall {
                service.fetchEvChargingSessionDetails(
                    requestId = requestId,
                    chargingId = chargingId,
                    apiKey = apiKey,
                    partnerName = partner,
                )
            }

        override suspend fun postEvRealTimeStatus(
            requestId: String,
            vin: String,
            brand: String,
            generation: String,
            deviceId: String,
        ): Resource<RealTimeStatusResponse?> =
            makeApiCall {
                service.postElectricVehicleRealTimeStatus(
                    requestId = requestId,
                    brand = brand,
                    vin = vin,
                    generation = generation,
                    deviceId = deviceId,
                )
            }

        override suspend fun fetchCdrSessionDetails(
            requestId: String,
            chargingId: String,
            apiKey: String,
            make: String,
            partnerName: String,
        ): Resource<CdrSessionResponse?> =
            makeApiCall {
                service.fetchCdrSessionDetails(
                    requestId = requestId,
                    chargingId = chargingId,
                    apiKey = apiKey,
                    make = make,
                    partnerName = partnerName,
                )
            }

        override suspend fun postEvRealTimeChargeInfoStatus(
            vin: String,
            brand: String,
            generation: String,
            deviceId: String,
        ): Resource<ElectricStatusResponse?> =
            makeApiCall {
                service.postElectricVehicleRealTimeStatusForChargeInfo(
                    brand = brand,
                    vin = vin,
                    generation = generation,
                    deviceId = deviceId,
                )
            }

        override suspend fun getEvRealTimeStatus(
            vin: String,
            brand: String,
            generation: String,
            appRequestNo: String,
        ): Resource<ElectricStatusResponse?> =
            makeApiCall {
                service.getElectricVehicleRealTimeStatus(
                    vin = vin,
                    brand = brand,
                    generation = generation,
                    appRequestNo = appRequestNo,
                )
            }

        override suspend fun getEVRemoteControlStatus(
            vin: String,
            brand: String,
            generation: String,
            appRequestNo: String,
        ): Resource<ElectricStatusResponse?> =
            makeApiCall {
                service.getElectricVehicleRemoteControlStatus(
                    vin = vin,
                    brand = brand,
                    generation = generation,
                    appRequestNo = appRequestNo,
                )
            }

        override suspend fun postElectricVehicleCommand(
            vin: String,
            brand: String,
            generation: String,
            requestBody: ChargeTimerRequest,
        ): Resource<CommonScheduleResponse?> =
            makeApiCall {
                service.postElectricVehicleCommand(
                    requestBody = requestBody,
                    brand = brand,
                    vin = vin,
                    generation = generation,
                    deviceId = UUID.randomUUID().toString(),
                )
            }
    }
