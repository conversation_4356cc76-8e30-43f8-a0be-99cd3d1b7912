/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.core.navigation.route

import androidx.activity.compose.BackHandler
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SheetState
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.core.ActionCollector
import com.toyota.oneapp.features.core.util.LocalBottomSheetState
import com.toyota.oneapp.features.core.util.launchPrimaryBottomSheetAction
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.PlugAndChargeEnrollmentScreenLayout
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.component.PlugAndChargeEnrollmentErrorBottomSheet
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentUiAction
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.model.PlugAndChargeEnrollmentUiEvent
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.viewmodel.PlugAndChargeEnrollmentViewModel
import kotlinx.coroutines.CoroutineScope

@OptIn(ExperimentalMaterial3Api::class)
fun launchPlugAndChargeActivation(
    coroutineScope: CoroutineScope,
    localBottomSheetState: LocalBottomSheetState,
) {
    coroutineScope.launchPrimaryBottomSheetAction(localBottomSheetState) { bottomSheetState ->
        val viewModel = hiltViewModel<PlugAndChargeEnrollmentViewModel>()
        val uiState by viewModel.enrollmentState.collectAsState()
        val onEvent = viewModel::onEvent
        val errorBottomSheetState: SheetState = rememberModalBottomSheetState()
        BackHandler {
            onEvent(PlugAndChargeEnrollmentUiEvent.ON_SYSTEM_BACK_CLICK)
        }
        ActionCollector(viewModel.uiAction) { uiAction ->
            when (uiAction) {
                PlugAndChargeEnrollmentUiAction.BACK_TO_PREVIOUS_SCREEN ->
                    bottomSheetState.hide()
                PlugAndChargeEnrollmentUiAction.CLOSE_ERROR_BOTTOM_SHEET ->
                    errorBottomSheetState.hide()
            }
        }
        uiState.run {
            PlugAndChargeEnrollmentScreenLayout(
                progressModel = progressModel,
                buttonsModel = buttonsModel,
                onEvent = onEvent,
            )
            if (this is PlugAndChargeEnrollmentState.ContainsError && isShowingError) {
                PlugAndChargeEnrollmentErrorBottomSheet(
                    errorModel = errorModel,
                    onEvent = onEvent,
                    sheetState = errorBottomSheetState,
                )
            }
        }
    }
}
