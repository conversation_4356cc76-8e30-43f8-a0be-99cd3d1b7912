/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.core.composable.image

import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.DrawableImage
import com.toyota.oneapp.features.core.theme.ThemeMode
import com.toyota.oneapp.features.core.util.ContentPreview
import com.toyota.oneapp.features.core.util.OAThemePreviewProvider

@Composable
fun GreenCheckComposable(modifier: Modifier = Modifier) {
    DrawableImage(
        modifier = modifier,
        drawableId = R.drawable.green_checkmark,
        contentDescriptionId = null,
    )
}

@Composable
@Preview
private fun GreenCheckComposablePreview(
    @PreviewParameter(OAThemePreviewProvider::class)
    themeMode: ThemeMode,
) {
    ContentPreview(
        themeMode = themeMode,
        modifier = Modifier.wrapContentSize(),
    ) {
        GreenCheckComposable()
    }
}
