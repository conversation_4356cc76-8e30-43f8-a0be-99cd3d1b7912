/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.presentation

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.TabRow
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeinfo.domain.model.RangeInfo
import com.toyota.oneapp.features.chargeinfo.presentation.ChargeInfoProgressIndicator
import com.toyota.oneapp.features.chargeinfo.presentation.HVACRangeWidget
import com.toyota.oneapp.features.chargemanagement.application.ChargeManagementState
import com.toyota.oneapp.features.chargemanagement.application.ChargeScheduleState
import com.toyota.oneapp.features.chargemanagement.domain.model.ChargeInfoModel
import com.toyota.oneapp.features.chargemanagement.domain.model.ChargeManagementModel
import com.toyota.oneapp.features.chargemanagement.domain.model.ChargeStatus
import com.toyota.oneapp.features.chargemanagement.presentation.navigation.navigateToChargeManagementMultiDaySchedule
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeManagementMultiDayScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeManagementPHEVScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleViewModel
import com.toyota.oneapp.features.cleanassist.presentation.CleanAssistViewModel
import com.toyota.oneapp.features.cleanassist.presentation.CleanAssistWidget
import com.toyota.oneapp.features.core.composable.OAButton
import com.toyota.oneapp.features.core.composable.OAButtonTextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OAFootNote1TextView
import com.toyota.oneapp.features.core.composable.OAFullScreenBottomSheetLayout
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine4TextView
import com.toyota.oneapp.features.core.composable.OASubHeadingLine3TextView
import com.toyota.oneapp.features.core.composable.OATabButton
import com.toyota.oneapp.features.core.composable.OATextTitle2TextView
import com.toyota.oneapp.features.core.composable.OATextTitle4TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.entrollment.util.Constance.PARTNER_NAME
import com.toyota.oneapp.util.NavigationUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.toFormattedDuration
import kotlinx.coroutines.launch

@Composable
fun ChargeManagementScreen(
    navController: NavHostController,
    chargeScheduleViewModel: ChargeScheduleViewModel,
    cleanAssistViewModel: CleanAssistViewModel,
    viewModel: ChargeManagementViewModel = hiltViewModel(),
    isChargeNow: Boolean = false,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.onEvent(ChargeManagementEvents.FetchChargeManagementDetail(isChargeNow))
    }

    ShowProgressIndicator(dialogState = uiState.showProgress)

    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tertiary15,
        screenTitle = stringResource(id = R.string.charge_info_title),
        testTagId = AccessibilityId.ID_CHARGE_MANAGEMENT_BACK_BTN,
        actionWidget = {
            if (uiState.showECOBadge) {
                ActionWidget {}
            }
        },
        onBack = { navController.popBackStack() },
        modifier = modifier.padding(vertical = 8.dp, horizontal = 16.dp),
    ) {
        ChargeManagementContent(
            uiState = uiState,
            viewModelPair = Pair(chargeScheduleViewModel, cleanAssistViewModel),
            navHostController = navController,
            onUpdateCurrentTab = { index ->
                viewModel.onEvent(ChargeManagementEvents.OnUpdateCurrentTab(index))
            },
            onCheckMaxScheduleLimit = { count ->
                viewModel.onEvent(ChargeManagementEvents.CheckMaxScheduleLimit(count))
            },
        ) { buttonType ->
            when (buttonType) {
                ClickType.FIND_STATION -> {
                    NavigationUtil.navigateToStations(
                        context = context,
                        navController = navController,
                        isEVPublicChargingEnabled = viewModel.isEVPublicChargingFeatureEnabled(),
                    )
                }
                ClickType.START_CHARGING -> {
                    viewModel.onEvent(ChargeManagementEvents.OnStartVehicleCharging)
                }
                ClickType.CREATE_SCHEDULE -> {
                    navController.navigateToChargeManagementMultiDaySchedule(
                        false,
                        null,
                    )
                }
                ClickType.WALLET_SETUP -> {
                    navController.navigate(OAScreen.AddEVCardScreen.route)
                }
                ClickType.WALLET_DETAIL -> {
                    NavigationUtil.navigateToWalletHome(
                        context = context,
                        navController = navController,
                    )
                }
                else -> {
                    // Do nothing
                }
            }
        }
    }
}

@Composable
fun ChargeManagementContent(
    uiState: ChargeManagementUIState,
    viewModelPair: Pair<ChargeScheduleViewModel, CleanAssistViewModel>,
    navHostController: NavHostController,
    modifier: Modifier = Modifier,
    onUpdateCurrentTab: (index: Int) -> Unit,
    onCheckMaxScheduleLimit: (count: Int) -> Unit,
    clickCallback: (type: ClickType) -> Unit,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth(),
    ) {
        when (uiState.screenState) {
            is ChargeManagementState.Init -> {
                // don't do anything
            }
            is ChargeManagementState.EmptyChargeManagement -> {
                EmptyChargeManagement()
            }
            is ChargeManagementState.ShowChargeInfo -> {
                ChargeManagementDetail(
                    uiState = uiState,
                    uiModel = uiState.screenState.uiModel,
                    onUpdateCurrentTab = { onUpdateCurrentTab(it) },
                    onShowSchedule = { scheduleType ->
                        when (scheduleType) {
                            ScheduleType.PHEV_SCHEDULE -> {
                                ChargeManagementPHEVScheduleScreen(
                                    viewModel = viewModelPair.first,
                                    navController = navHostController,
                                )
                            }
                            ScheduleType.MULTI_DAY_SCHEDULE -> {
                                ChargeManagementMultiDayScheduleScreen(
                                    viewModel = viewModelPair.first,
                                    navController = navHostController,
                                ) { count ->
                                    onCheckMaxScheduleLimit(count)
                                }
                            }
                        }
                    },
                    onShowCleanAssistCard = {
                        CleanAssistWidget(
                            navHostController = navHostController,
                            viewModel = viewModelPair.second,
                            modifier =
                                Modifier
                                    .padding(vertical = 8.dp)
                                    .testTagID(AccessibilityId.ID_CLEAN_ASSIST_CARD),
                        )
                    },
                    onShowDisclaimer = { partner ->
                        navHostController.currentBackStackEntry?.savedStateHandle?.apply {
                            set(PARTNER_NAME, partner)
                        }
                        navHostController.navigate(OAScreen.ChargeSettingDisclaimerScreen.route)
                    },
                ) { type ->
                    clickCallback(type)
                }
            }
        }
    }
}

@Composable
private fun ChargeManagementDetail(
    uiState: ChargeManagementUIState,
    uiModel: ChargeManagementModel,
    onUpdateCurrentTab: (index: Int) -> Unit,
    onShowSchedule: @Composable (scheduleType: ScheduleType) -> Unit,
    onShowCleanAssistCard: @Composable () -> Unit,
    onShowDisclaimer: (partner: String) -> Unit,
    onHandleClick: (type: ClickType) -> Unit,
) {
    Column(
        modifier =
            Modifier
                .fillMaxSize(),
    ) {
        LazyColumn(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                Modifier
                    .fillMaxSize()
                    .weight(1f, fill = false),
        ) {
            uiModel.chargeInfoModel?.let {
                item {
                    ChargeMeterLayout(
                        uiModel = it,
                    )
                }
            }

            item {
                AnimatedVisibility(
                    visible = uiState.showTabs,
                ) {
                    ChargeManagementTabLayout(
                        uiState = uiState,
                        onUpdateTabIndex = { onUpdateCurrentTab(it) },
                        onShowCleanAssistCard = onShowCleanAssistCard,
                        onShowSchedule = { onShowSchedule(it) },
                        onShowDisclaimer = { onShowDisclaimer(it) },
                        onWalletNavigation = { onHandleClick(it) },
                        onNavigateToFindStation = { onHandleClick(ClickType.FIND_STATION) },
                    )
                }
            }

            item {
                AnimatedVisibility(
                    visible = !uiState.showTabs,
                ) {
                    HomeTabLayout(
                        uiState = uiState,
                        onShowCleanAssistCard = { onShowCleanAssistCard() },
                        onShowPHEVSchedule = { onShowSchedule(ScheduleType.PHEV_SCHEDULE) },
                        onShowMultiDaySchedule = { onShowSchedule(ScheduleType.MULTI_DAY_SCHEDULE) },
                    )
                }
            }
        }

        FloatingButtonWidget(
            showBottomButton = uiState.buttonType != null && uiState.currentTab == 1,
            buttonType = uiState.buttonType,
        ) { buttonType ->
            onHandleClick(buttonType)
        }
    }
}

@Composable
private fun ChargeMeterLayout(
    uiModel: ChargeInfoModel,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.wrapContentSize(),
    ) {
        Row {
            Image(
                painter = painterResource(R.drawable.ic_charge),
                contentDescription = stringResource(R.string.charge_info_title),
                colorFilter =
                    ColorFilter.tint(
                        if (uiModel.isBatteryLow) {
                            AppTheme.colors.primary01
                        } else {
                            AppTheme.colors.secondary01
                        },
                    ),
                modifier =
                    modifier
                        .size(48.dp)
                        .align(Alignment.CenterVertically),
            )

            Spacer(modifier = Modifier.width(8.dp))

            OATextTitle2TextView(
                text = uiModel.chargePercentage.toString(),
                color = AppTheme.colors.tertiary00,
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .align(Alignment.CenterVertically)
                        .testTagID(AccessibilityId.ID_CHARGE_PERCENTAGE_TEXT),
            )

            OASubHeadingLine3TextView(
                text = "%",
                color = AppTheme.colors.tertiary00,
                modifier =
                    Modifier
                        .align(Alignment.Bottom)
                        .padding(bottom = 6.dp),
            )

            val text =
                if (uiModel.isCharging) {
                    stringResource(R.string.charging_status)
                } else if (uiModel.isChargingOrWaiting) {
                    stringResource(R.string.plugged_in_status)
                } else {
                    ToyotaConstants.EMPTY_STRING
                }

            if (text.isNotEmpty()) {
                OASubHeadLine1TextView(
                    modifier =
                        Modifier
                            .align(Alignment.CenterVertically),
                    text = "・$text",
                    color = AppTheme.colors.tertiary00,
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        ChargeInfoProgressIndicator(
            chargePercentage = uiModel.chargePercentage,
            progressColor =
                if (uiModel.isBatteryLow) {
                    AppTheme.colors.primary01
                } else {
                    AppTheme.colors.secondary01
                },
            backgroundColor = AppTheme.colors.tertiary10,
            modifier =
                Modifier
                    .width(168.dp)
                    .testTagID(AccessibilityId.ID_CHARGE_PERCENTAGE_PROGRESS_INDICATOR),
        )

        // Only show RangeInfoWidget to match iOS - OAD01-25294
        RangeInfoWidget(
            uiModel = uiModel,
            modifier = Modifier.padding(top = 24.dp),
        )
    }
}

@Composable
private fun RangeInfoWidget(
    uiModel: ChargeInfoModel,
    modifier: Modifier = Modifier,
) {
    if (uiModel.isEV) {
        HVACRangeWidget(
            rangeInfo = uiModel.rangeInfo,
            rangeInfoWithAC = uiModel.rangeInfoWithAC,
            modifier = modifier,
        )
    } else {
        DistanceToEmptyWidget(
            isMultiDayEnabled = uiModel.isMultiDayScheduleEnabled,
            rangeInfo = uiModel.rangeInfo,
            rangeInfoWithAC = uiModel.rangeInfoWithAC,
            modifier = modifier,
        )
    }
}

@Composable
private fun DistanceToEmptyWidget(
    isMultiDayEnabled: Boolean,
    rangeInfo: RangeInfo?,
    rangeInfoWithAC: RangeInfo?,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier,
    ) {
        Row {
            OATextTitle4TextView(
                text = rangeInfo?.range.toString(),
                color = AppTheme.colors.tertiary00,
                textAlign = TextAlign.Center,
                modifier = Modifier.testTagID(AccessibilityId.ID_ESTIMATED_DISTANCE_TEXT),
            )

            OASubHeadLine4TextView(
                text = stringResource(id = R.string.unit_est_test, rangeInfo?.unit.orEmpty()),
                color = AppTheme.colors.tertiary00,
                modifier =
                    Modifier
                        .align(Alignment.Bottom)
                        .padding(bottom = 8.dp),
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        OACallOut1TextView(
            text =
                if (isMultiDayEnabled) {
                    stringResource(R.string.estimate_distance_without_ac_heat)
                } else {
                    stringResource(
                        R.string.estimate_distance_with_ac,
                        "${rangeInfoWithAC?.range} ${rangeInfoWithAC?.unit}",
                    )
                },
            color = AppTheme.colors.tertiary05,
            modifier = Modifier.testTagID(AccessibilityId.ID_DISTANCE_TO_EMPTY_DESCRIPTION_TXT),
        )
    }
}

@Composable
private fun ChargeStatusPlugInWidget(
    uiModel: ChargeInfoModel,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier,
    ) {
        OAButton(
            text =
                when (uiModel.chargeStatus) {
                    ChargeStatus.CHARGING -> {
                        stringResource(R.string.charging_status)
                    }
                    ChargeStatus.PLUGGED_IN -> {
                        stringResource(R.string.plugged_in_status)
                    }
                    else -> {
                        ToyotaConstants.EMPTY_STRING
                    }
                },
            textColor =
                if (uiModel.isBatteryLow) {
                    AppTheme.colors.primary01
                } else {
                    AppTheme.colors.button03b
                },
            bgColor =
                if (uiModel.isBatteryLow) {
                    AppTheme.colors.primary02
                } else {
                    AppTheme.colors.secondary02
                },
            textModifier =
                Modifier.padding(
                    vertical = 6.dp,
                    horizontal = 10.dp,
                ),
            click = {},
        )

        Spacer(modifier = Modifier.height(16.dp))

        OACallOut1TextView(
            text =
                uiModel.timeRemaining?.run {
                    stringResource(
                        R.string.estimate_time_until_full_charge,
                        this.toFormattedDuration(LocalContext.current),
                    )
                } ?: run {
                    stringResource(R.string.distance_remaining, uiModel.distanceRemaining.orEmpty())
                },
            color = AppTheme.colors.tertiary05,
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
private fun ActionWidget(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Image(
        painter = painterResource(R.drawable.ic_eco_badge),
        contentDescription = stringResource(R.string.eco_charging),
        modifier =
            modifier
                .size(32.dp)
                .clickable { onClick() },
    )
}

@Composable
private fun HomeTabLayout(
    uiState: ChargeManagementUIState,
    modifier: Modifier = Modifier,
    onShowCleanAssistCard: @Composable () -> Unit,
    onShowMultiDaySchedule: @Composable () -> Unit,
    onShowPHEVSchedule: @Composable () -> Unit,
) {
    Column {
        Spacer(modifier = Modifier.height(24.dp))

        AnimatedVisibility(
            visible = uiState.isLCFSEnabled,
        ) {
            onShowCleanAssistCard()
        }

        when (uiState.scheduleState) {
            is ChargeScheduleState.Init, ChargeScheduleState.SchedulesNotAvailable -> {
                // don't do anything
            }
            is ChargeScheduleState.NonMultiDaySchedule -> {
                onShowPHEVSchedule()
            }
            is ChargeScheduleState.MultiDaySchedule -> {
                onShowMultiDaySchedule()
            }
            is ChargeScheduleState.EmptySchedule -> {
                EmptyChargeManagementScreen(
                    modifier = modifier,
                    vehicleBrand = uiState.scheduleState.vehicleBrand,
                    title = stringResource(R.string.no_schedules_title),
                    description = stringResource(R.string.set_timer_in_vehicle_note),
                )
            }
        }
    }
}

@Composable
private fun ChargeManagementTabLayout(
    uiState: ChargeManagementUIState,
    modifier: Modifier = Modifier,
    onUpdateTabIndex: (index: Int) -> Unit,
    onShowCleanAssistCard: @Composable () -> Unit,
    onShowSchedule: @Composable (scheduleType: ScheduleType) -> Unit,
    onShowDisclaimer: (partner: String) -> Unit,
    onWalletNavigation: (type: ClickType) -> Unit,
    onNavigateToFindStation: (isPublicChargingEnabled: Boolean) -> Unit,
) {
    val pagerState = rememberPagerState(initialPage = 1, pageCount = { 2 })
    val coroutineScope = rememberCoroutineScope()
    Column {
        TabRow(
            selectedTabIndex = pagerState.currentPage,
            backgroundColor = AppTheme.colors.tertiary15,
            indicator = {},
            divider = {},
            modifier = modifier.padding(8.dp),
        ) {
            OATabButton(
                accessibilityID = AccessibilityId.ID_STATIONS_TAB,
                text = stringResource(R.string.find_station_title),
                isSelected = pagerState.currentPage == 0,
            ) {
                coroutineScope.launch {
                    pagerState.animateScrollToPage(0)
                }
            }

            OATabButton(
                accessibilityID = AccessibilityId.ID_HOME_TAB,
                text = stringResource(R.string.ev_home_tab),
                isSelected = pagerState.currentPage == 1,
            ) {
                coroutineScope.launch {
                    pagerState.animateScrollToPage(1)
                }
            }
        }

        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxWidth(),
        ) { page ->
            onUpdateTabIndex(pagerState.currentPage)
            when (page) {
                0 -> {
                    StationsTabScreen(
                        uiState = uiState,
                        onShowDisclaimer = { onShowDisclaimer(it) },
                        onWalletDetail = { onWalletNavigation(ClickType.WALLET_DETAIL) },
                        onSetupWallet = { onWalletNavigation(ClickType.WALLET_SETUP) },
                    ) {
                        onNavigateToFindStation(it)
                    }
                }
                1 -> {
                    HomeTabLayout(
                        uiState = uiState,
                        onShowCleanAssistCard = { onShowCleanAssistCard() },
                        onShowPHEVSchedule = { onShowSchedule(ScheduleType.PHEV_SCHEDULE) },
                        onShowMultiDaySchedule = { onShowSchedule(ScheduleType.MULTI_DAY_SCHEDULE) },
                    )
                }
            }
        }
    }
}

@Composable
private fun FloatingButtonWidget(
    showBottomButton: Boolean,
    buttonType: ClickType?,
    modifier: Modifier = Modifier,
    onClick: (type: ClickType) -> Unit,
) {
    AnimatedVisibility(
        visible = showBottomButton,
        modifier = modifier,
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(top = 8.dp, bottom = 8.dp),
        ) {
            if (buttonType == ClickType.MAX_LIMIT_REACHED) {
                OAButtonTextView(
                    text = stringResource(R.string.max_schedule_reached),
                    color = AppTheme.colors.tertiary03,
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                )

                Spacer(modifier = Modifier.height(8.dp))

                OAFootNote1TextView(
                    text = stringResource(R.string.max_schedule_foot_note),
                    color = AppTheme.colors.tertiary07,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                )
            } else {
                PrimaryButton02(
                    text =
                        when (buttonType) {
                            ClickType.START_CHARGING -> {
                                stringResource(
                                    R.string.start_charging_btn,
                                )
                            }
                            else -> {
                                stringResource(id = R.string.create_schedule)
                            }
                        },
                    modifier =
                        Modifier
                            .align(Alignment.CenterHorizontally)
                            .testTagID(
                                when (buttonType) {
                                    ClickType.CREATE_SCHEDULE -> {
                                        AccessibilityId.ID_CREATE_SCHEDULE_BTN
                                    }
                                    else -> {
                                        AccessibilityId.ID_CHARGEMANAGEMENT_START_CHARGING_BTN
                                    }
                                },
                            ),
                ) {
                    onClick(buttonType ?: ClickType.MAX_LIMIT_REACHED)
                }
            }
        }
    }
}

@Composable
private fun EmptyChargeManagement(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier.fillMaxSize(),
    ) {
        OASubHeadLine3TextView(
            text = stringResource(R.string.charge_info_no_data_available),
            color = AppTheme.colors.tertiary07,
            modifier = Modifier.align(Alignment.Center),
        )
    }
}

enum class ScheduleType {
    PHEV_SCHEDULE,
    MULTI_DAY_SCHEDULE,
}

@Preview
@Composable
fun PreviewChargeManagementScreen(modifier: Modifier = Modifier) {
    val uiState =
        ChargeManagementUIState(
            showTabs = true,
            buttonType = ClickType.CREATE_SCHEDULE,
            scheduleState = ChargeScheduleState.EmptySchedule("L"),
        )
    val uiModel =
        ChargeManagementModel(
            response = null,
            chargeInfoModel =
                ChargeInfoModel(
                    chargePercentage = 80,
                    isBatteryLow = true,
                    isEV = true,
                    isMultiDayScheduleEnabled = true,
                    isCharging = true,
                    isChargingOrWaiting = false,
                    chargeStatus = ChargeStatus.CHARGING,
                    showStartCharging = true,
                    timeRemaining = 40,
                    rangeInfo =
                        RangeInfo(
                            range = 59.9,
                            unit = "mi",
                            description = R.string.ev_without_ac_desc,
                        ),
                    rangeInfoWithAC =
                        RangeInfo(
                            range = 45.9,
                            unit = "mi",
                            description = R.string.ev_with_ac_desc,
                        ),
                ),
        )
    OAFullScreenBottomSheetLayout(
        backgroundColor = AppTheme.colors.tertiary15,
        screenTitle = stringResource(id = R.string.charge_info_title),
        testTagId = AccessibilityId.ID_MULTIDAY_BACK_BTN,
        onBack = { },
        actionWidget = {
            ActionWidget { }
        },
        modifier = modifier.padding(vertical = 8.dp, horizontal = 16.dp),
    ) {
        ChargeManagementDetail(
            uiState,
            uiModel,
            onUpdateCurrentTab = {},
            onShowSchedule = {},
            onShowCleanAssistCard = {},
            onShowDisclaimer = {},
        ) {}
    }
}
