/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.di

import com.toyota.oneapp.features.chargemanagement.application.ChargeManagementLogic
import com.toyota.oneapp.features.chargemanagement.application.ChargeManagementUseCase
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class ChargeManagementModule {
    @Binds
    abstract fun bindChargeManagementUseCase(logic: ChargeManagementLogic): ChargeManagementUseCase
}
