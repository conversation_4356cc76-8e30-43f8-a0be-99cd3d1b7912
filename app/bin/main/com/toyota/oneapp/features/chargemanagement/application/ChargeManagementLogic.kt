/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.application

import com.toyota.oneapp.features.chargemanagement.domain.model.ChargeManagementModel
import com.toyota.oneapp.features.chargemanagement.domain.model.PartnerEnrollmentModel
import com.toyota.oneapp.features.chargemanagement.domain.model.toChargeManagementModel
import com.toyota.oneapp.features.chargemanagement.domain.model.toUIModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeTimerRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.getAppRequestNo
import com.toyota.oneapp.features.core.commonapicalls.domain.repository.CommonApiRepository
import com.toyota.oneapp.features.entrollment.domain.repository.EnrollmentRepository
import com.toyota.oneapp.features.pay.wallet.domain.repository.WalletRepository
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class ChargeManagementLogic
    @Inject
    constructor(
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val commonApiRepository: CommonApiRepository,
        private val enrollmentRepository: EnrollmentRepository,
        private val walletRepository: WalletRepository,
    ) : ChargeManagementUseCase {
        companion object {
            private const val COMMAND_IMMEDIATE_CHARGE = "immediate-charge"
        }

        override fun fetchEVVehicleInfo(vehicleInfo: VehicleInfo): Flow<ChargeManagementModel?> =
            flow {
                val response =
                    commonApiRepository.fetchChargeManagementDetail(
                        vin = vehicleInfo.vin,
                        generation = vehicleInfo.generation,
                        brand = vehicleInfo.brand,
                    )

                if (response is Resource.Success) {
                    emit(constructChargeManagementModel(vehicleInfo, response.data))
                } else {
                    emit(null)
                }
            }

        override fun constructChargeManagementModel(
            vehicleInfo: VehicleInfo,
            response: ElectricStatusResponse?,
        ): ChargeManagementModel? = response?.toChargeManagementModel(vehicleInfo)

        override fun postStartVehicleCharging(
            vin: String,
            generation: String,
            brand: String,
        ): Flow<String?> =
            flow {
                val response =
                    commonApiRepository.postElectricVehicleCommand(
                        vin = vin,
                        brand = brand,
                        generation = generation,
                        requestBody =
                            ChargeTimerRequest(
                                command = COMMAND_IMMEDIATE_CHARGE,
                                remoteHvac = null,
                                reservationCharge = null,
                            ),
                    )

                if (response is Resource.Success) {
                    emit(response.data?.payload?.getAppRequestNo())
                } else {
                    emit(null)
                }
            }

        override fun fetchRemoteControlElectricStatus(
            vehicleInfo: VehicleInfo,
            appReqNo: String,
        ): Flow<ElectricStatusRemoteControlState?> =
            flow {
                val response =
                    commonApiRepository.getEVRemoteControlStatus(
                        vin = vehicleInfo.vin,
                        brand = vehicleInfo.brand,
                        generation = vehicleInfo.generation,
                        appRequestNo = appReqNo,
                    )

                if (response is Resource.Success) {
                    response.data?.payload?.let {
                        it.remoteControlResult?.also { result ->
                            if (result.status == 0 && result.result == 0) {
                                emit(
                                    ElectricStatusRemoteControlState.Success(
                                        constructChargeManagementModel(vehicleInfo, response.data),
                                    ),
                                )
                            } else {
                                emit(null)
                            }
                        }
                    } ?: run {
                        emit(null)
                    }
                } else {
                    emit(ElectricStatusRemoteControlState.Error)
                }
            }

        override fun fetchWalletDetail(): Flow<WalletCardState?> {
            return flow {
                val response = walletRepository.fetchWalletDetails()
                (response as? Resource.Success)?.data?.payload?.let {
                    if (it.paymentMethods?.isNotEmpty() == true) {
                        val defaultPaymentMethod =
                            it.paymentMethods.first {
                                it.default == true
                            }
                        emit(WalletCardState.WalletDetailCard(defaultPaymentMethod.card.last4))
                    } else {
                        emit(WalletCardState.SetUpWallet)
                    }
                } ?: run {
                    emit(null)
                }
            }
        }

        override fun fetchPartnerEnrollment(isEvGoEnabled: Boolean): Flow<List<PartnerEnrollmentModel>> {
            return flow {
                val response =
                    enrollmentRepository.fetchEnrollmentCheck(
                        eMail =
                            oneAppPreferenceModel.getAccountInfoSubscriber()
                                ?.customerEmails?.get(0)?.emailAddress.orEmpty(),
                    )
                (response as? Resource.Success)?.data?.payload?.let {
                    emit(it.partnerEnrollment?.toUIModel(isEvGoEnabled) ?: emptyList())
                } ?: run {
                    emit(emptyList())
                }
            }
        }
    }
