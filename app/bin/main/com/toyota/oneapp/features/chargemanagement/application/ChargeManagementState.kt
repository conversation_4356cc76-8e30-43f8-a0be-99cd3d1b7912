/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.application

import com.toyota.oneapp.features.chargemanagement.domain.model.ChargeManagementModel
import com.toyota.oneapp.features.chargemanagement.domain.model.PartnerEnrollmentModel

sealed class ChargeManagementState {
    object Init : ChargeManagementState()

    object EmptyChargeManagement : ChargeManagementState()

    data class ShowChargeInfo(
        val uiModel: ChargeManagementModel,
    ) : ChargeManagementState()
}

sealed class ChargeScheduleState {
    object Init : ChargeScheduleState()

    data class EmptySchedule(
        val vehicleBrand: String,
    ) : ChargeScheduleState()

    object MultiDaySchedule : ChargeScheduleState()

    object NonMultiDaySchedule : ChargeScheduleState()

    data object SchedulesNotAvailable : ChargeScheduleState()
}

sealed class ElectricStatusRemoteControlState {
    data class Success(
        val uiModel: ChargeManagementModel?,
    ) : ElectricStatusRemoteControlState()

    object Error : ElectricStatusRemoteControlState()
}

sealed class WalletCardState {
    object Loading : WalletCardState()

    object SetUpWallet : WalletCardState()

    data class WalletDetailCard(val digits: String) : WalletCardState()
}

sealed class PartnerListState {
    object Loading : PartnerListState()

    data class ShowPartners(
        val enrolledPartnerList: List<PartnerEnrollmentModel>,
        val nonEnrolledPartnerList: List<PartnerEnrollmentModel>,
    ) : PartnerListState()
}
