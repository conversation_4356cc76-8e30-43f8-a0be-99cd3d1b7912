/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.presentation

import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.chargemanagement.application.ChargeManagementState
import com.toyota.oneapp.features.chargemanagement.application.ChargeManagementUseCase
import com.toyota.oneapp.features.chargemanagement.application.ChargeScheduleState
import com.toyota.oneapp.features.chargemanagement.application.ElectricStatusRemoteControlState
import com.toyota.oneapp.features.chargemanagement.application.PartnerListState
import com.toyota.oneapp.features.chargemanagement.application.WalletCardState
import com.toyota.oneapp.features.chargemanagement.domain.model.ChargeManagementModel
import com.toyota.oneapp.features.chargeschedule.util.collectAndRetry
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

@HiltViewModel
class ChargeManagementViewModel
    @Inject
    constructor(
        private val chargeManagementUseCase: ChargeManagementUseCase,
        private val sharedDataSource: SharedDataSource,
        private val applicationData: ApplicationData,
        private val dispatcherProvider: DispatcherProvider,
    ) : BaseViewModel<ChargeManagementUIState, ChargeManagementEvents>() {
        private var job: Job? = null

        companion object {
            private const val MAX_CHARGE_SCHEDULE_COUNT = 15
        }

        init {
            applicationData.getSelectedVehicleState().value?.let { vehicle ->
                state.update {
                    it.copy(showTabs = isEVPublicChargingFeatureEnabled())
                }
                onEvent(ChargeManagementEvents.InitScreen(vehicle))
            }
        }

        fun isEVPublicChargingFeatureEnabled(): Boolean {
            return applicationData.getSelectedVehicleState().value?.isFeatureEnabled(
                Feature.EV_PUBLIC_CHARGING_CONTROL,
            ) ?: false
        }

        override fun defaultState() = ChargeManagementUIState()

        override fun onEvent(event: ChargeManagementEvents) {
            when (event) {
                is ChargeManagementEvents.InitScreen -> {
                    state.update {
                        it.copy(
                            isLCFSEnabled = event.vehicleInfo.isFeatureEnabled(Feature.LCFS),
                            showECOBadge = event.vehicleInfo.isFeatureEnabled(Feature.ECO_HISTORY),
                            scheduleState = getScheduleState(event.vehicleInfo),
                        )
                    }
                }
                is ChargeManagementEvents.FetchChargeManagementDetail -> {
                    fetchChargeManagementDetail(event.isChargeNow)
                }
                is ChargeManagementEvents.OnUpdateCurrentTab -> {
                    state.update {
                        it.copy(currentTab = event.index)
                    }
                }
                is ChargeManagementEvents.CheckMaxScheduleLimit -> {
                    checkMaxScheduleLimitReached(event.scheduleCount)
                }
                is ChargeManagementEvents.OnStartVehicleCharging -> {
                    postStartChargingCommand()
                }
            }
        }

        fun onStationsTabEvent(event: StationsTabEvents) {
            when (event) {
                is StationsTabEvents.InitStationTab -> {
                    applicationData.getSelectedVehicleState().value?.let { vehicle ->
                        state.update {
                            it.copy(isWalletEnabled = vehicle.isFeatureEnabled(Feature.WALLET))
                        }
                    }
                }
                is StationsTabEvents.OnFetchWalletDetail -> {
                    fetchWalletDetail()
                }
                is StationsTabEvents.OnFetchPartnerEnrollment -> {
                    fetchPartnerEnrollment()
                }
            }
        }

        private fun getScheduleState(vehicleInfo: VehicleInfo): ChargeScheduleState {
            if (isRemoteSharedUser() == true) {
                return ChargeScheduleState.SchedulesNotAvailable
            }
            return if (vehicleInfo.isScheduleEnabled) {
                if (vehicleInfo.isFeatureEnabled(Feature.MULTI_DAY_CHARGING)) {
                    ChargeScheduleState.MultiDaySchedule
                } else {
                    ChargeScheduleState.NonMultiDaySchedule
                }
            } else {
                ChargeScheduleState.EmptySchedule(vehicleBrand = vehicleInfo.brand)
            }
        }

        private fun isRemoteSharedUser(): Boolean? {
            return applicationData.getSelectedVehicleState().value?.isPrimaryButNotRemoteUser
        }

        private fun fetchChargeManagementDetail(isChargeNow: Boolean) {
            job =
                viewModelScope.launch(dispatcherProvider.main()) {
                    state.update { it.copy(showProgress = true) }
                    if (isChargeNow) {
                        postStartChargingCommand()
                    } else {
                        applicationData.getSelectedVehicleState().value?.let { vehicle ->
                            sharedDataSource.getElectricStatusState().collect { electricStatusState ->
                                if (electricStatusState is ElectricStatusState.Success) {
                                    val uiModel =
                                        chargeManagementUseCase.constructChargeManagementModel(
                                            vehicleInfo = vehicle,
                                            response = electricStatusState.response,
                                        )
                                    handleScreenState(uiModel)
                                } else {
                                    fetchEVVehicleInfoFromAPI(vehicle)
                                }
                            }
                        }
                    }
                }
        }

        private fun fetchEVVehicleInfoFromAPI(vehicleInfo: VehicleInfo) {
            viewModelScope.launch(dispatcherProvider.main()) {
                chargeManagementUseCase
                    .fetchEVVehicleInfo(vehicleInfo)
                    .flowOn(dispatcherProvider.io())
                    .collect { uiModel ->
                        sharedDataSource.setElectricStatusState(
                            ElectricStatusState.Success(uiModel?.response),
                        )
                        handleScreenState(uiModel)
                    }
            }
        }

        private fun handleScreenState(uiModel: ChargeManagementModel?) {
            job?.cancel()
            uiModel?.let {
                val floatingButtonType =
                    if (it.chargeInfoModel?.showStartCharging == true) {
                        ClickType.START_CHARGING
                    } else if (it.chargeInfoModel?.isMultiDayScheduleEnabled == true && isRemoteSharedUser() == false) {
                        ClickType.CREATE_SCHEDULE
                    } else {
                        null
                    }
                state.update { _state ->
                    _state.copy(
                        showProgress = false,
                        buttonType = floatingButtonType,
                        screenState = ChargeManagementState.ShowChargeInfo(it),
                    )
                }
            } ?: run {
                state.update {
                    it.copy(
                        showProgress = false,
                        screenState = ChargeManagementState.EmptyChargeManagement,
                    )
                }
            }
        }

        private fun checkMaxScheduleLimitReached(scheduleCount: Int) {
            val floatingButtonType = state.value.buttonType
            if (floatingButtonType == ClickType.CREATE_SCHEDULE ||
                floatingButtonType == ClickType.MAX_LIMIT_REACHED
            ) {
                state.update {
                    it.copy(
                        buttonType =
                            if (scheduleCount >= MAX_CHARGE_SCHEDULE_COUNT) {
                                ClickType.MAX_LIMIT_REACHED
                            } else {
                                ClickType.CREATE_SCHEDULE
                            },
                    )
                }
            }
        }

        private fun fetchWalletDetail() {
            state.update { it.copy(walletCardState = WalletCardState.Loading) }
            viewModelScope.launch(dispatcherProvider.main()) {
                chargeManagementUseCase.fetchWalletDetail()
                    .flowOn(dispatcherProvider.io())
                    .collect { walletState ->
                        state.update {
                            it.copy(walletCardState = walletState)
                        }
                    }
            }
        }

        private fun fetchPartnerEnrollment() {
            state.update { it.copy(partnersState = PartnerListState.Loading) }
            viewModelScope.launch(dispatcherProvider.main()) {
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    val isEvGoEnabled = vehicle.isFeatureEnabled(Feature.EV_GO)
                    chargeManagementUseCase.fetchPartnerEnrollment(isEvGoEnabled)
                        .flowOn(dispatcherProvider.io())
                        .collect { partnerList ->
                            val enrolledPartners = partnerList.filter { it.isEnrolled }
                            state.update {
                                it.copy(
                                    partnersState =
                                        PartnerListState.ShowPartners(
                                            enrolledPartnerList = enrolledPartners,
                                            nonEnrolledPartnerList = partnerList.filter { !it.isEnrolled },
                                        ),
                                )
                            }
                        }
                }
            }
        }

        private fun postStartChargingCommand() {
            viewModelScope.launch(dispatcherProvider.main()) {
                state.update { it.copy(showProgress = true) }
                applicationData.getSelectedVehicleState().value?.let { vehicle ->
                    chargeManagementUseCase.postStartVehicleCharging(
                        vin = vehicle.vin,
                        generation = vehicle.generation,
                        brand = vehicle.brand,
                    )
                        .flowOn(dispatcherProvider.io())
                        .collect { appRequestNo ->
                            appRequestNo?.let {
                                fetchElectricStatusResponseForRemoteControl(
                                    System.currentTimeMillis(),
                                    it,
                                )
                            } ?: run {
                                state.update { it.copy(showProgress = false) }
                            }
                        }
                }
            }
        }

        private fun fetchElectricStatusResponseForRemoteControl(
            startTime: Long,
            appRequestNo: String,
        ) {
            job =
                viewModelScope.launch(dispatcherProvider.main()) {
                    sharedDataSource.setElectricStatusState(ElectricStatusState.Loading)
                    applicationData.getSelectedVehicleState().value?.let { vehicle ->
                        chargeManagementUseCase
                            .fetchRemoteControlElectricStatus(
                                vehicleInfo = vehicle,
                                appReqNo = appRequestNo,
                            ).flowOn(dispatcherProvider.io())
                            .collectAndRetry(
                                startTime = startTime,
                                onRetry = {
                                    fetchElectricStatusResponseForRemoteControl(
                                        startTime = startTime,
                                        appRequestNo = appRequestNo,
                                    )
                                },
                                onTimeout = {
                                    handleScreenState(null)
                                },
                            ) { responseState ->
                                when (responseState) {
                                    is ElectricStatusRemoteControlState.Success -> {
                                        sharedDataSource.setElectricStatusState(
                                            ElectricStatusState.Success(responseState.uiModel?.response),
                                        )
                                        handleScreenState(responseState.uiModel)
                                    }
                                    else -> {
                                        handleScreenState(null)
                                    }
                                }
                            }
                    }
                }
        }
    }

enum class ClickType {
    CREATE_SCHEDULE,
    START_CHARGING,
    MAX_LIMIT_REACHED,
    FIND_STATION,
    WALLET_DETAIL,
    WALLET_SETUP,
}

data class ChargeManagementUIState(
    val showTabs: Boolean = false,
    val currentTab: Int = 1,
    val showProgress: Boolean = false,
    val isLCFSEnabled: Boolean = false,
    val showECOBadge: Boolean = false,
    val isWalletEnabled: Boolean = false,
    val buttonType: ClickType? = null,
    val scheduleState: ChargeScheduleState = ChargeScheduleState.Init,
    val screenState: ChargeManagementState = ChargeManagementState.Init,
    val walletCardState: WalletCardState? = null,
    val partnersState: PartnerListState? = null,
)

sealed class ChargeManagementEvents {
    data class InitScreen(val vehicleInfo: VehicleInfo) : ChargeManagementEvents()

    data class FetchChargeManagementDetail(val isChargeNow: Boolean) : ChargeManagementEvents()

    data class OnUpdateCurrentTab(val index: Int) : ChargeManagementEvents()

    data class CheckMaxScheduleLimit(val scheduleCount: Int) : ChargeManagementEvents()

    object OnStartVehicleCharging : ChargeManagementEvents()
}

sealed class StationsTabEvents {
    object InitStationTab : StationsTabEvents()

    object OnFetchWalletDetail : StationsTabEvents()

    object OnFetchPartnerEnrollment : StationsTabEvents()
}
