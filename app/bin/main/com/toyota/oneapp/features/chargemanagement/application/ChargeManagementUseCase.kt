/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.application

import com.toyota.oneapp.features.chargemanagement.domain.model.ChargeManagementModel
import com.toyota.oneapp.features.chargemanagement.domain.model.PartnerEnrollmentModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface ChargeManagementUseCase {
    fun fetchEVVehicleInfo(vehicleInfo: VehicleInfo): Flow<ChargeManagementModel?>

    fun constructChargeManagementModel(
        vehicleInfo: VehicleInfo,
        response: ElectricStatusResponse?,
    ): ChargeManagementModel?

    fun postStartVehicleCharging(
        vin: String,
        generation: String,
        brand: String,
    ): Flow<String?>

    fun fetchRemoteControlElectricStatus(
        vehicleInfo: VehicleInfo,
        appReqNo: String,
    ): Flow<ElectricStatusRemoteControlState?>

    fun fetchWalletDetail(): Flow<WalletCardState?>

    fun fetchPartnerEnrollment(isEvGoEnabled: Boolean): Flow<List<PartnerEnrollmentModel>>
}
