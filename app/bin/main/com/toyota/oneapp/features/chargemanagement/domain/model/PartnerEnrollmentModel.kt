/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.domain.model

import com.toyota.oneapp.features.entrollment.dataaccess.servermodel.PartnerEnrollment
import com.toyota.oneapp.features.entrollment.util.Constance.EV_GO

data class PartnerEnrollmentModel(
    val isEnrolled: Boolean,
    val status: String,
    val partnerName: String,
)

const val STATUS_FOUND = "FOUND"
const val STATUS_REGISTER = "Register"
const val STATUS_ENROLLED = "Enrolled"

fun PartnerEnrollment.toUIModel(isEvGoEnabled: Boolean): List<PartnerEnrollmentModel> {
    return this.partnerStatus?.mapNotNull { partner ->
        if (!isEvGoEnabled && partner.partnerName?.lowercase() == EV_GO) {
            null
        } else {
            PartnerEnrollmentModel(
                isEnrolled = partner.status?.uppercase() == STATUS_FOUND,
                status =
                    getEnrollmentStatus(
                        wallet = this.wallet?.uppercase().orEmpty(),
                        status = partner.status?.uppercase().orEmpty(),
                    ),
                partnerName = partner.partnerName.orEmpty(),
            )
        }
    } ?: emptyList()
}

private fun getEnrollmentStatus(
    wallet: String,
    status: String,
): String {
    val foundStatusKey = STATUS_FOUND
    return when {
        wallet == foundStatusKey && status == foundStatusKey -> {
            STATUS_ENROLLED
        }
        else -> {
            STATUS_REGISTER
        }
    }
}
