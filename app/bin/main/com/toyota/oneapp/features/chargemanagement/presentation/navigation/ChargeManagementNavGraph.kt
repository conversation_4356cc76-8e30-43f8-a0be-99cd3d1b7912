/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.presentation.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import com.toyota.oneapp.features.chargemanagement.presentation.ChargeManagementScreen
import com.toyota.oneapp.features.chargemanagement.presentation.navigation.ChargeManagementRoute.Companion.NAV_ARG_CHARGE_MANAGEMENT_FLOW
import com.toyota.oneapp.features.chargemanagement.presentation.navigation.ChargeManagementRoute.Companion.NAV_ARG_IS_CHARGE_NOW
import com.toyota.oneapp.features.chargemanagement.presentation.navigation.ChargeManagementRoute.Companion.NAV_ARG_OFF_PEAK_SCHEDULE
import com.toyota.oneapp.features.chargeschedule.presentation.ChargeScheduleViewModel
import com.toyota.oneapp.features.chargeschedule.presentation.EditScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.PHEVNewScheduleScreen
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute
import com.toyota.oneapp.features.chargeschedule.presentation.navigation.ChargeScheduleRoute.Companion.ARG_IS_START_FLOW
import com.toyota.oneapp.features.cleanassist.presentation.CleanAssistViewModel
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeInfo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.TimerChargeInfo
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.navigation.sharedViewModel

fun NavGraphBuilder.chargeManagementNavGraph(navController: NavHostController) {
    navigation(
        route = OAScreen.ChargeManagementNestedRoute.route,
        startDestination = ChargeManagementRoute.ChargeManagementScreen.route,
    ) {
        composable(route = ChargeManagementRoute.ChargeManagementScreen.route) { entry ->
            val isChargeNow =
                navController.previousBackStackEntry?.savedStateHandle?.get<Boolean>(
                    NAV_ARG_IS_CHARGE_NOW,
                ) ?: false
            val chargeScheduleViewModel =
                entry.sharedViewModel<ChargeScheduleViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )

            val cleanAssistViewModel =
                entry.sharedViewModel<CleanAssistViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )

            ChargeManagementScreen(
                chargeScheduleViewModel = chargeScheduleViewModel,
                cleanAssistViewModel = cleanAssistViewModel,
                isChargeNow = isChargeNow,
                navController = navController,
            )
        }
        composable(
            route = "${ChargeManagementRoute.PHEVScheduleScreen.route}/{$ARG_IS_START_FLOW}",
            arguments = listOf(navArgument(ARG_IS_START_FLOW) { defaultValue = false }),
        ) { entry ->
            val chargeScheduleViewModel =
                entry.sharedViewModel<ChargeScheduleViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )
            val chargeInfo =
                navController.previousBackStackEntry?.savedStateHandle?.get<ChargeInfo>(
                    ChargeScheduleRoute.ARG_CHARGE_INFO_MODEL,
                )
            val isStartFlow = entry.arguments?.getBoolean(ARG_IS_START_FLOW) ?: false

            PHEVNewScheduleScreen(
                navController = navController,
                isStartScheduleFlow = isStartFlow,
                viewModel = chargeScheduleViewModel,
                chargeInfo = chargeInfo,
            )
        }
        composable(
            route =
                "${ChargeManagementRoute.EditMultiDayScheduleScreen.route}/" +
                    "{$NAV_ARG_CHARGE_MANAGEMENT_FLOW}/{$NAV_ARG_OFF_PEAK_SCHEDULE}",
            arguments =
                listOf(
                    navArgument(NAV_ARG_CHARGE_MANAGEMENT_FLOW) {
                        type = NavType.BoolType
                        defaultValue = false
                    },
                    navArgument(NAV_ARG_OFF_PEAK_SCHEDULE) {
                        type = NavType.BoolType
                        defaultValue = false
                    },
                ),
        ) { entry ->
            val chargeScheduleViewModel =
                entry.sharedViewModel<ChargeScheduleViewModel>(
                    getBackStackEntry = { path ->
                        navController.getBackStackEntry(path)
                    },
                )

            val dataModel =
                navController.previousBackStackEntry?.savedStateHandle?.get<TimerChargeInfo>(
                    ChargeScheduleRoute.ARG_EDIT_SCHEDULE_MODEL,
                )
            val isChargeManagementFlow = entry.arguments?.getBoolean(NAV_ARG_CHARGE_MANAGEMENT_FLOW) ?: false
            val isOffPeakSchedule = entry.arguments?.getBoolean(NAV_ARG_OFF_PEAK_SCHEDULE) ?: false

            EditScheduleScreen(
                navController = navController,
                viewModel = chargeScheduleViewModel,
                isChargeManagementFlow = isChargeManagementFlow,
                isOffPeakSchedule = isOffPeakSchedule,
                dataModel = dataModel,
            )
        }
    }
}

fun NavHostController.navigateToChargeManagementPHEVSchedule(
    isStartFlow: Boolean,
    chargeInfo: ChargeInfo?,
) {
    this.currentBackStackEntry?.savedStateHandle?.set(
        ChargeScheduleRoute.ARG_CHARGE_INFO_MODEL,
        chargeInfo,
    )
    this.navigate(
        "${ChargeManagementRoute.PHEVScheduleScreen.route}/$isStartFlow",
    )
}

fun NavHostController.navigateToChargeManagementMultiDaySchedule(
    isOffPeak: Boolean,
    dataModel: TimerChargeInfo?,
) {
    this.currentBackStackEntry?.savedStateHandle?.set(
        ChargeScheduleRoute.ARG_EDIT_SCHEDULE_MODEL,
        dataModel,
    )
    this.navigate(
        "${ChargeManagementRoute.EditMultiDayScheduleScreen.route}/${true}/$isOffPeak",
    )
}

sealed class ChargeManagementRoute(
    val route: String,
) {
    companion object {
        const val NAV_ARG_IS_CHARGE_NOW = "isChargeNow"
        const val NAV_ARG_CHARGE_MANAGEMENT_FLOW = "isChargeManagementFlow"
        const val NAV_ARG_OFF_PEAK_SCHEDULE = "isOffPeakSchedule"
    }

    object ChargeManagementScreen : ChargeManagementRoute("ChargeManagementScreen")

    object EditMultiDayScheduleScreen : ChargeManagementRoute("ChargeManagementEditScheduleScreen")

    object PHEVScheduleScreen : ChargeManagementRoute("ChargeManagementPHEVScheduleScreen")
}
