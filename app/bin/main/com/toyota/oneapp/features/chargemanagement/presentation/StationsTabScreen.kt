/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargemanagement.application.PartnerListState
import com.toyota.oneapp.features.chargemanagement.application.WalletCardState
import com.toyota.oneapp.features.chargemanagement.domain.model.PartnerEnrollmentModel
import com.toyota.oneapp.features.core.composable.CommonCardShimmer
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OAButton
import com.toyota.oneapp.features.core.composable.OASubHeadLine1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.core.util.LocalBottomSheet

@Composable
fun StationsTabScreen(
    uiState: ChargeManagementUIState,
    viewModel: ChargeManagementViewModel = hiltViewModel<ChargeManagementViewModel>(),
    modifier: Modifier = Modifier,
    onShowDisclaimer: (partner: String) -> Unit,
    onWalletDetail: () -> Unit,
    onSetupWallet: () -> Unit,
    onNavigateToFindStation: (isPublicChargingEnabled: Boolean) -> Unit,
) {
    LaunchedEffect(Unit) {
        viewModel.onStationsTabEvent(StationsTabEvents.InitStationTab)
    }

    LaunchedEffect(key1 = uiState.isWalletEnabled) {
        if (uiState.isWalletEnabled) {
            viewModel.onStationsTabEvent(StationsTabEvents.OnFetchWalletDetail)
            viewModel.onStationsTabEvent(StationsTabEvents.OnFetchPartnerEnrollment)
        }
    }

    StationsTabContent(
        uiState,
        modifier,
        onShowDisclaimer = { onShowDisclaimer(it) },
        onWalletDetail = onWalletDetail,
        onSetupWallet = onSetupWallet,
    ) {
        onNavigateToFindStation(viewModel.isEVPublicChargingFeatureEnabled())
    }
}

@Composable
private fun StationsTabContent(
    uiState: ChargeManagementUIState,
    modifier: Modifier = Modifier,
    onShowDisclaimer: (partner: String) -> Unit,
    onWalletDetail: () -> Unit,
    onSetupWallet: () -> Unit,
    onFindStationsClicked: () -> Unit,
) {
    var selectedPartner: String? by remember { mutableStateOf(null) }
    val bottomSheet = LocalBottomSheet.current

    LaunchedEffect(bottomSheet.primarySheetState.value) {
        snapshotFlow { bottomSheet.primarySheetState.value.isVisible }.collect { isVisible ->
            if (!isVisible) {
                selectedPartner = null
            }
        }
    }

    if (selectedPartner?.isNotEmpty() == true) {
        onShowDisclaimer(selectedPartner.orEmpty())
    }

    Column(
        modifier =
            modifier
                .fillMaxSize()
                .padding(16.dp),
    ) {
        if (uiState.isWalletEnabled) {
            WalletCard(
                walletCardState = uiState.walletCardState,
                onSetupWallet = onSetupWallet,
                onWalletDetail = onWalletDetail,
            )
        }

        when (uiState.partnersState) {
            is PartnerListState.Loading -> {
                CommonCardShimmer()
            }
            is PartnerListState.ShowPartners -> {
                PartnerItemList(uiState.partnersState.enrolledPartnerList) { partner ->
                    selectedPartner = partner
                }
                PartnerItemList(uiState.partnersState.nonEnrolledPartnerList) { partner ->
                    selectedPartner = partner
                }
            }
            else -> {
                // DO nothing
            }
        }

        PrimaryButton02(
            text = stringResource(R.string.find_stations),
            modifier =
                Modifier
                    .testTagID(AccessibilityId.ID_STATIONS_TAB_FIND_STATIONS_BTN)
                    .align(Alignment.CenterHorizontally)
                    .padding(top = 24.dp),
        ) {
            onFindStationsClicked()
        }
    }
}

@Composable
private fun WalletCard(
    walletCardState: WalletCardState?,
    onWalletDetail: () -> Unit,
    onSetupWallet: () -> Unit,
) {
    when (walletCardState) {
        is WalletCardState.Loading -> {
            CommonCardShimmer()
        }
        is WalletCardState.SetUpWallet -> {
            WalletCard(
                button = {
                    OAButton(
                        modifier = Modifier.testTagID(AccessibilityId.ID_WALLET_SETUP_BTN),
                        text = stringResource(R.string.setup_wallet),
                        textColor = AppTheme.colors.button02a,
                        bgColor = AppTheme.colors.button05b,
                        textModifier =
                            Modifier
                                .padding(horizontal = 16.dp, vertical = 6.dp),
                        click = {
                            onSetupWallet()
                        },
                    )
                },
            ) {
                onSetupWallet()
            }
        }
        is WalletCardState.WalletDetailCard -> {
            WalletCard(
                button = {
                    OAButton(
                        modifier = Modifier.testTagID(AccessibilityId.ID_WALLET_DETAIL_BTN),
                        text = stringResource(R.string.masked_digits, walletCardState.digits),
                        textColor = AppTheme.colors.secondary01,
                        bgColor = AppTheme.colors.success02,
                        textModifier =
                            Modifier
                                .padding(horizontal = 16.dp, vertical = 6.dp),
                        click = {
                            onWalletDetail()
                        },
                    )
                },
            ) {
                onWalletDetail()
            }
        }
        else -> { /* Do nothing */ }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun WalletCard(
    modifier: Modifier = Modifier,
    button: @Composable () -> Unit,
    onClick: () -> Unit,
) {
    Card(
        backgroundColor = AppTheme.colors.tertiary15,
        shape = RoundedCornerShape(8.dp),
        onClick = onClick,
        modifier = modifier,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Absolute.SpaceBetween,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 16.dp),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_ev_wallet_card),
                    contentDescription = stringResource(R.string.wallet),
                    modifier = Modifier.size(24.dp),
                )

                Spacer(modifier = Modifier.width(16.dp))

                OASubHeadLine1TextView(
                    text = stringResource(R.string.wallet),
                    color = AppTheme.colors.tertiary03,
                )
            }

            button()
        }
    }
}

@Composable
private fun PartnerItemList(
    partners: List<PartnerEnrollmentModel>,
    modifier: Modifier = Modifier,
    onItemClicked: (partnerName: String) -> Unit,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
    ) {
        partners.forEach { partnerItem ->
            PartnerItem(
                item = partnerItem,
                modifier =
                    Modifier
                        .testTagID(AccessibilityId.ID_PARTNER_ITEM_CARD)
                        .padding(bottom = 8.dp),
            ) { partnerName ->
                onItemClicked(partnerName)
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun PartnerItem(
    item: PartnerEnrollmentModel,
    modifier: Modifier = Modifier,
    onClick: (partnerName: String) -> Unit,
) {
    Card(
        backgroundColor = AppTheme.colors.tile02,
        elevation =
            if (item.isEnrolled) {
                1.dp
            } else {
                0.dp
            },
        shape = RoundedCornerShape(8.dp),
        onClick = { onClick(item.partnerName.lowercase()) },
        modifier = modifier,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Absolute.SpaceBetween,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 16.dp),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(item.isEnrolled.getPartnerIcon().first),
                    contentDescription = item.partnerName,
                    colorFilter = ColorFilter.tint(item.isEnrolled.getPartnerIcon().second),
                    modifier = Modifier.size(24.dp),
                )

                Spacer(modifier = Modifier.width(16.dp))

                OABody4TextView(
                    text = item.partnerName,
                    color = AppTheme.colors.tertiary00,
                )
            }

            if (!item.isEnrolled) {
                OAButton(
                    text = item.status,
                    textColor = AppTheme.colors.button02a,
                    bgColor = AppTheme.colors.button05b,
                    textModifier =
                        Modifier
                            .padding(horizontal = 16.dp, vertical = 6.dp),
                    click = {
                        onClick(item.partnerName.lowercase())
                    },
                )
            }
        }
    }
}

@Composable
private fun Boolean.getPartnerIcon(): Pair<Int, Color> {
    return if (this) {
        Pair(R.drawable.ic_ev_enrolled, AppTheme.colors.button03b)
    } else {
        Pair(R.drawable.ic_ev_charge_point, AppTheme.colors.tertiary03)
    }
}

@Preview
@Composable
private fun PreviewStationsContent(modifier: Modifier = Modifier) {
    val uiState =
        ChargeManagementUIState(
            isWalletEnabled = true,
            walletCardState = WalletCardState.WalletDetailCard("1234"),
            partnersState =
                PartnerListState.ShowPartners(
                    enrolledPartnerList =
                        listOf(
                            PartnerEnrollmentModel(
                                isEnrolled = true,
                                status = "Enrolled",
                                partnerName = "Charge Point",
                            ),
                        ),
                    nonEnrolledPartnerList =
                        listOf(
                            PartnerEnrollmentModel(
                                isEnrolled = false,
                                status = "Register",
                                partnerName = "EvGo",
                            ),
                        ),
                ),
        )

    StationsTabContent(uiState, modifier, {}, {}, {}) {}
}
