/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.model.vehicle.VehicleInfo.BRAND_LEXUS
import com.toyota.oneapp.model.vehicle.VehicleInfo.BRAND_SUBARU

@Composable
fun EmptyChargeManagementScreen(
    vehicleBrand: String,
    title: String,
    description: String,
    modifier: Modifier = Modifier,
) {
    Card(
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .height(300.dp),
    ) {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth(),
        ) {
            Image(
                painter =
                    painterResource(
                        when (vehicleBrand) {
                            BRAND_LEXUS -> {
                                R.drawable.no_schedule_banner_lexus
                            }
                            BRAND_SUBARU -> {
                                R.drawable.no_schedule_banner_subaru
                            }
                            else -> {
                                R.drawable.no_schedule_banner
                            }
                        },
                    ),
                contentDescription = stringResource(R.string.no_schedule_title),
                contentScale = ContentScale.Crop,
            )

            Box(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .background(
                            Brush.verticalGradient(
                                colors =
                                    listOf(
                                        Color.Black,
                                        Color.Transparent,
                                    ),
                                endY = 500f,
                            ),
                        ),
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier =
                        Modifier
                            .padding(vertical = 16.dp, horizontal = 32.dp)
                            .align(Alignment.TopCenter),
                ) {
                    OACallOut2TextView(
                        text = title,
                        color = AppTheme.colors.button01a,
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    OASubHeadLine3TextView(
                        text = description,
                        color = AppTheme.colors.button01a,
                        textAlign = TextAlign.Center,
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun PreviewEmptyScheduleCard(modifier: Modifier = Modifier) {
    EmptyChargeManagementScreen(
        vehicleBrand = "T",
        title = stringResource(R.string.no_schedules_title),
        description = stringResource(R.string.set_timer_in_vehicle_note),
        modifier = modifier,
    )
}
