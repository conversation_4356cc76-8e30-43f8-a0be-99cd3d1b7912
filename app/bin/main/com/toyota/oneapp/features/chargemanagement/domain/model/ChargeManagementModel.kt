/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargemanagement.domain.model

import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeinfo.domain.model.RangeInfo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeInfo
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.isCharging
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.isPluggedIn
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.DoubleUtil.roundOffTo

data class ChargeManagementModel(
    val response: ElectricStatusResponse?,
    val chargeInfoModel: ChargeInfoModel?,
)

data class ChargeInfoModel(
    val chargePercentage: Int,
    val isBatteryLow: Boolean,
    val isEV: <PERSON><PERSON>an,
    val isMultiDayScheduleEnabled: <PERSON>olean,
    val isCharging: <PERSON>olean,
    val isChargingOrWaiting: Boolean,
    val chargeStatus: ChargeStatus? = null,
    val showStartCharging: Boolean,
    val timeRemaining: Int? = null,
    val distanceRemaining: String? = null,
    val rangeInfo: RangeInfo? = null,
    val rangeInfoWithAC: RangeInfo? = null,
)

fun ElectricStatusResponse.toChargeManagementModel(vehicleInfo: VehicleInfo): ChargeManagementModel =
    ChargeManagementModel(
        response = this,
        chargeInfoModel =
            this.payload.vehicleInfo
                ?.chargeInfo
                ?.toChargeInfoModel(vehicleInfo),
    )

private fun ChargeInfo.toChargeInfoModel(vehicleInfo: VehicleInfo): ChargeInfoModel {
    val chargePercentage = chargeRemainingAmount?.toInt() ?: 0
    val rangeInfoPair = getRangeInfo(vehicleInfo.isEVModel)
    return ChargeInfoModel(
        chargePercentage = chargePercentage,
        isBatteryLow = chargePercentage <= 30,
        isEV = vehicleInfo.isEVModel,
        isMultiDayScheduleEnabled = vehicleInfo.isFeatureEnabled(Feature.MULTI_DAY_CHARGING),
        isCharging = isCharging(),
        isChargingOrWaiting = isCharging() || isPluggedIn(),
        chargeStatus = getChargeStatus(),
        showStartCharging = isPluggedIn(),
        timeRemaining =
            if (isCharging()) {
                remainingChargeTime ?: 0
            } else {
                null
            },
        distanceRemaining =
            if (isPluggedIn()) {
                "${evDistance?.roundOffTo(1) ?: 0} $evDistanceUnit"
            } else {
                null
            },
        rangeInfo = rangeInfoPair?.first,
        rangeInfoWithAC = rangeInfoPair?.second,
    )
}

private fun ChargeInfo.getChargeStatus(): ChargeStatus? =
    when {
        isCharging() -> {
            ChargeStatus.CHARGING
        }
        isPluggedIn() -> {
            ChargeStatus.PLUGGED_IN
        }
        else -> {
            null
        }
    }

private fun ChargeInfo.getRangeInfo(isEV: Boolean): Pair<RangeInfo, RangeInfo>? {
    val rangeInfo =
        RangeInfo(
            range = evDistance?.roundOffTo(1) ?: 0.0,
            unit = evDistanceUnit,
            description =
                if (isEV) {
                    R.string.ev_without_ac_desc
                } else {
                    null
                },
        )
    val rangeInfoWithAC =
        RangeInfo(
            range = evDistanceAC?.roundOffTo(1) ?: 0.0,
            unit = evDistanceUnit,
            description =
                if (isEV) {
                    R.string.ev_with_ac_desc
                } else {
                    null
                },
        )

    return Pair(rangeInfo, rangeInfoWithAC)
}

enum class ChargeStatus {
    CHARGING,
    PLUGGED_IN,
}
