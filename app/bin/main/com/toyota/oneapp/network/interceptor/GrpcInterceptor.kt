package com.toyota.oneapp.network.interceptor

import com.toyota.oneapp.component.IDPHelper
import com.toyota.oneapp.network.dataprovider.NetworkDataProvider
import io.grpc.*
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class GrpcInterceptor
    @Inject
    constructor(
        private val idpHelper: IDPHelper,
        private val provider: NetworkDataProvider,
    ) : ClientInterceptor {
        companion object {
            private const val TAG = "GrpcInterceptor"
        }

        override fun <ReqT : Any?, RespT : Any?> interceptCall(
            method: MethodDescriptor<ReqT, RespT>?,
            callOptions: CallOptions?,
            next: Channel?,
        ): ClientCall<ReqT, RespT> {
            return object : ClientCall<ReqT, RespT>() {
                var listener: Listener<RespT>? = null
                var metadata: Metadata? = null
                var message: ReqT? = null
                var request = 0
                var call: ClientCall<ReqT, RespT>? = null

                override fun sendMessage(message: ReqT) {
                    this.message = message
                }

                override fun halfClose() {
                    startCall(CheckingListener())
                }

                override fun start(
                    responseListener: Listener<RespT>?,
                    headers: Metadata?,
                ) {
                    this.listener = responseListener
                    this.metadata = headers
                }

                override fun cancel(
                    message: String?,
                    cause: Throwable?,
                ) {
                    if (call != null) {
                        call?.cancel(message, cause)
                    }
                    listener?.onClose(
                        Status.CANCELLED.withDescription(message).withCause(cause),
                        Metadata(),
                    )
                }

                override fun request(numMessages: Int) {
                    request += numMessages
                }

                private fun startCall(listener: Listener<RespT>?) {
                    var token: String? = null
                    try {
                        token = idpHelper.getAccessToken()
                    } catch (e: java.lang.Exception) {
                        LogTool.e(TAG, "Error occurred while fetching access token", e)
                    }

                    call = next?.newCall(method, callOptions)
                    call?.run {
                        val headers = Metadata()
                        headers.put(
                            Metadata.Key.of("authorization", Metadata.ASCII_STRING_MARSHALLER),
                            "bearer $token",
                        )
                        headers.put(
                            Metadata.Key.of("x-oneapp", Metadata.ASCII_STRING_MARSHALLER),
                            String.format(
                                "%s;%s;%s;%s;%s;%s",
                                provider.appBrand(),
                                provider.appName(),
                                provider.locale(),
                                provider.release(),
                                provider.version(),
                                provider.appType(),
                            ),
                        )
                        start(listener, headers)
                        request(request)
                        sendMessage(message)
                        halfClose()
                    }
                }

                inner class CheckingListener : ForwardingClientCallListener<RespT>() {
                    var delegate: Listener<RespT>? = null

                    override fun delegate(): Listener<RespT> {
                        if (delegate == null) {
                            delegate = listener
                        }
                        return delegate!!
                    }

                    override fun onHeaders(headers: Metadata?) {
                        delegate = listener
                        super.onHeaders(headers)
                    }

                    override fun onClose(
                        status: Status?,
                        trailers: Metadata?,
                    ) {
                        delegate = listener
                        super.onClose(status, trailers)
                    }
                }
            }
        }
    }
