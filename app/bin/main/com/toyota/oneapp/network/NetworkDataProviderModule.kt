package com.toyota.oneapp.network

import com.toyota.oneapp.network.dataprovider.BaseUrlProvider
import com.toyota.oneapp.network.dataprovider.NetworkDataProvider
import com.toyota.oneapp.network.dataproviders.BaseUrlProviderImpl
import com.toyota.oneapp.network.dataproviders.NetworkDataProviderIml
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module
abstract class NetworkDataProviderModule {
    @Binds
    abstract fun networkDataProvider(networkProvider: NetworkDataProviderIml): NetworkDataProvider

    @Binds
    abstract fun baseUrlProvider(baseUrlProvider: BaseUrlProviderImpl): BaseUrlProvider
}
