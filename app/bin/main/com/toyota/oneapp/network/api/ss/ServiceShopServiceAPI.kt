package com.toyota.oneapp.network.api.ss

import com.toyota.oneapp.model.dealer.SearchDealerResponse
import retrofit2.Call
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Query

interface ServiceShopServiceAPI {
    @GET("/serviceshop/oneapi/v1/service-shop/oneapp/dealers")
    fun getDealersListByAddress(
        @Header("X-REGION") regionHeader: String?,
        @Header("X-BRAND") xBrand: String?,
        @Query("q") city: String?,
        @Query("brand") appBrand: String?,
        @Query("region") region: String?,
    ): Call<SearchDealerResponse?>?

    @GET("/serviceshop/oneapi/v1/service-shop/oneapp/dealers/map")
    fun getDealersListByCurrentLoc(
        @Header("X-REGION") xregion: String?,
        @Header("X-BRAND") xBrand: String?,
        @Query("latitude") latitude: String?,
        @Query("longitude") longitude: String?,
        @Query("radius") radius: String?,
        @Query("brand") appBrand: String?,
        @Query("region") region: String?,
    ): Call<SearchDealerResponse?>?
}
