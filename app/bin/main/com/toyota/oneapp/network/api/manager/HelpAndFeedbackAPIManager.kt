package com.toyota.oneapp.network.api.manager

import android.content.res.AssetManager
import com.google.gson.Gson
import com.toyota.oneapp.model.FeedbackThumbsUpRequest
import com.toyota.oneapp.model.account.helpandfeedback.ContactResponse
import com.toyota.oneapp.model.account.helpandfeedback.FaqsResponse
import com.toyota.oneapp.model.account.helpandfeedback.FeedbackMetadataResponse
import com.toyota.oneapp.model.account.helpandfeedback.SubmitFeedbackV2Request
import com.toyota.oneapp.network.api.cy17plus.CY17PlusServiceAPI
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.CallbackProvider
import com.toyota.oneapp.network.models.BaseResponse
import javax.inject.Inject

class HelpAndFeedbackAPIManager
    @Inject
    constructor(
        private val client: CY17PlusServiceAPI,
        private val callbackProvider: CallbackProvider,
        private val assetManager: AssetManager,
        private val gson: <PERSON><PERSON>,
    ) : ServiceAPIManager() {
        fun getContactOptions(
            hasVehicle: Boolean,
            brand: String,
            region: String,
            callback: BaseCallback<ContactResponse>,
        ) {
            return executeSubscriber(
                client.getContactOptions(hasVehicle, brand, region),
                callbackProvider.create(callback),
            )
        }

        fun getFaqs(
            cvFlag: Boolean,
            hasVehicle: Boolean,
            brand: String,
            region: String,
            generation: String,
            callback: BaseCallback<FaqsResponse>,
        ) {
            return executeSubscriber(
                client.getFaqs(cvFlag, hasVehicle, brand, region, generation),
                callbackProvider.create(callback),
            )
        }

        fun submitFeedbackV2(
            brand: String,
            submitFeedbackV2Request: SubmitFeedbackV2Request,
            callback: BaseCallback<BaseResponse>,
        ) {
            return executeSubscriber(
                client.submitFeedbackV2(brand, submitFeedbackV2Request),
                callbackProvider.create(callback),
            )
        }

        fun getFeedbackMetadata(
            cvFlag: Boolean,
            brand: String,
            region: String,
            generation: String,
            callback: BaseCallback<FeedbackMetadataResponse>,
        ) {
            return executeSubscriber(
                client.getFeedbackMetadata(cvFlag, brand, region, generation),
                callbackProvider.create(callback),
            )
        }

        fun submitThumbsUpFeedback(
            body: FeedbackThumbsUpRequest,
            callback: BaseCallback<BaseResponse>,
        ) {
            return executeSubscriber(
                client.submitFeedbackThumbsUp(body),
                callbackProvider.create(callback),
            )
        }
    }
