package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.account.VaNotificationPrefsPayload
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.mm21.VaNotificationPreferenceApi
import com.toyota.oneapp.network.models.ApiResponse
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class VaRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val vaNotificationPreferenceApi: VaNotificationPreferenceApi,
    ) : BaseRepository(
            errorMessageParser,
            dispatcherProvider.io(),
        ) {
        suspend fun getVaNotificationPrefs(): Resource<ApiResponse<VaNotificationPrefsPayload?>?> {
            return makeApiCall { vaNotificationPreferenceApi.getVaNotificationPreferences() }
        }

        suspend fun updateVaNotificationPrefs(
            prefsPayload: VaNotificationPrefsPayload,
        ): Resource<ApiResponse<VaNotificationPrefsPayload?>?> {
            return makeApiCall {
                vaNotificationPreferenceApi.updateVaNotificationPreferences(
                    prefsPayload,
                )
            }
        }
    }
