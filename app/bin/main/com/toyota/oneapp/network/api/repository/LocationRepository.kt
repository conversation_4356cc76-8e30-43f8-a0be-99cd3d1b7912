package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.poi.*
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.mm21.LocationServicesApi
import com.toyota.oneapp.network.models.ApiResponse
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class LocationRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val locationServicesApi: LocationServicesApi,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun sendPOIToCar(sendToCarLocationDetails: SendPOIToCarRequest): Resource<ApiResponse<StcLocationResponse?>?> {
            return makeApiCall { locationServicesApi.sendPOIToCar(sendToCarLocationDetails) }
        }

        suspend fun getSharedLocations(): Resource<ApiResponse<List<StcLocationResponse>?>?> {
            return makeApiCall { locationServicesApi.getSharedLocations() }
        }

        suspend fun deleteSharedLocations(stcDeleteRequest: StcDeleteRequest): Resource<ApiResponse<StcLocationResponse?>?> {
            return makeApiCall { locationServicesApi.deleteSharedLocation(stcDeleteRequest) }
        }

        suspend fun getPOIDataFromSharedText(locationRequest: SharePOIRequest): Resource<ApiResponse<SharePOIResponse?>?> {
            return makeApiCall { locationServicesApi.getPOIDataFromSharedText(locationRequest) }
        }
    }
