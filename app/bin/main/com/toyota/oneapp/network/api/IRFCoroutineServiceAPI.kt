package com.toyota.oneapp.network.api

import com.toyota.oneapp.model.vehiclehealth.IRFApprovalGrantRequest
import com.toyota.oneapp.model.vehiclehealth.IRFApprovalRevokeRequest
import com.toyota.oneapp.model.vehiclehealth.IRFResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.*

interface IRFCoroutineServiceAPI {
    @Headers("Content-Type: application/json")
    @GET("/oneapi/v1/irf/list")
    suspend fun getGrantedIRFs(
        @Header("vin") vin: String,
    ): Response<IRFResponse?>

    @Headers("Content-Type: application/json")
    @GET("/oneapi/v1/irf/search")
    suspend fun searchForIRFsByName(
        @Header("vin") vin: String,
        @Header("search-string") query: String,
    ): Response<IRFResponse?>

    @POST("/oneapi/v1/irf/grant")
    suspend fun grantIRFAccess(
        @Header("id") id: String,
        @Header("vin") vin: String,
        @Body body: IRFApprovalGrantRequest,
    ): Response<BaseResponse>

    @PUT("/oneapi/v1/irf/grant")
    suspend fun revokeIRFAccess(
        @Header("id") id: String,
        @Header("vin") vin: String,
        @Body body: IRFApprovalRevokeRequest,
    ): Response<BaseResponse>
}
