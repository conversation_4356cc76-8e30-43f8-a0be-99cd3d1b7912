package com.toyota.oneapp.network.api.mm21

import com.toyota.oneapp.model.account.VaNotificationPrefsPayload
import com.toyota.oneapp.network.models.ApiResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST

interface VaNotificationPreferenceApi {
    @Headers(CONTENT_TYPE_JSON)
    @POST("/oa21mm/v1/svc/sync/vapreference")
    suspend fun updateVaNotificationPreferences(
        @Body vaPref: VaNotificationPrefsPayload,
    ): Response<ApiResponse<VaNotificationPrefsPayload?>?>

    @Headers(CONTENT_TYPE_JSON)
    @GET("/oa21mm/v1/svc/sync/vapreference")
    suspend fun getVaNotificationPreferences(): Response<ApiResponse<VaNotificationPrefsPayload?>?>
}
