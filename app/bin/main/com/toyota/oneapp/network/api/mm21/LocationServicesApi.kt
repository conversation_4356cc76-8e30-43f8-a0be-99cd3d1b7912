package com.toyota.oneapp.network.api.mm21

import com.toyota.oneapp.model.poi.SendPOIToCarRequest
import com.toyota.oneapp.model.poi.SharePOIRequest
import com.toyota.oneapp.model.poi.SharePOIResponse
import com.toyota.oneapp.model.poi.StcDeleteRequest
import com.toyota.oneapp.model.poi.StcLocationResponse
import com.toyota.oneapp.network.models.ApiResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.HTTP
import retrofit2.http.POST

interface LocationServicesApi {
    @POST("/oa21mm/v1/svc/share")
    suspend fun getPOIDataFromSharedText(
        @Body request: SharePOIRequest,
    ): Response<ApiResponse<SharePOIResponse?>?>

    @POST("/oa21mm/v1/svc/sendtocar")
    suspend fun sendPOIToCar(
        @Body request: SendPOIToCarRequest,
    ): Response<ApiResponse<StcLocationResponse?>?>

    @GET("/oa21mm/v1/svc/getsharedlocations")
    suspend fun getSharedLocations(): Response<ApiResponse<List<StcLocationResponse>?>?>

    @HTTP(method = "DELETE", path = "/oa21mm/v1/svc/deletesharedlocation", hasBody = true)
    suspend fun deleteSharedLocation(
        @Body request: StcDeleteRequest,
    ): Response<ApiResponse<StcLocationResponse?>?>
}
