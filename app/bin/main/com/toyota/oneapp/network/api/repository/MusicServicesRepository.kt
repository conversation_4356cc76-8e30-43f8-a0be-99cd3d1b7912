package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.subscription.MusicPreferencePayload
import com.toyota.oneapp.model.subscription.MusicSubscriptionRequest
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.mm21.MusicServicesApi
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class MusicServicesRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val musicServicesApi: MusicServicesApi,
    ) : BaseRepository(
            errorMessageParser,
            dispatcherProvider.io(),
        ) {
        suspend fun getMusicSubscriptions(guid: String): Resource<ApiResponse<List<String>?>?> {
            return makeApiCall { musicServicesApi.getMusicSubscriptions(guid) }
        }

        suspend fun updateMusicAuthToken(musicSubscriptionRequest: MusicSubscriptionRequest): Resource<BaseResponse?> {
            return makeApiCall { musicServicesApi.updateMusicAuthToken(musicSubscriptionRequest) }
        }

        suspend fun deleteMusicToken(musicSubscriptionRequest: MusicSubscriptionRequest): Resource<BaseResponse?> {
            return makeApiCall { musicServicesApi.deleteMusicToken(musicSubscriptionRequest) }
        }

        suspend fun getMusicPreferences(guid: String): Resource<ApiResponse<MusicPreferencePayload?>?> {
            return makeApiCall { musicServicesApi.getMusicPreferences(guid) }
        }

        suspend fun updateMusicPreferences(musicPreferenceRequest: MusicPreferencePayload): Resource<BaseResponse?> {
            return makeApiCall { musicServicesApi.updateMusicPreferences(musicPreferenceRequest) }
        }
    }
