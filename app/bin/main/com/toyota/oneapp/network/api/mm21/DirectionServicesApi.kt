package com.toyota.oneapp.network.api.mm21

import com.toyota.oneapp.model.poi.Directions
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

interface DirectionServicesApi {
    @GET("directions/json")
    suspend fun getDirectionsBetween(
        @Query("origin") origin: String?,
        @Query("destination") Destination: String?,
        @Query("key") Key: String,
    ): Response<Directions>
}
