package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.fr.SXMIdTokenFromFRIdpResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.fridp.IDPServiceApi
import com.toyota.oneapp.network.models.B2BIDP
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class FRIdpRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val iDPServiceApi: IDPServiceApi,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getSXMIdTokenFromFRIdp(): Resource<SXMIdTokenFromFRIdpResponse?> {
            return makeApiCall {
                iDPServiceApi.getB2BTokenSXMTokenFromFR(
                    grantType = B2BIDP.GRANT_TYPE.value,
                    username = B2BIDP.ANANYMOUS.value,
                    password = B2BIDP.ANANYMOUS.value,
                    scope = B2BIDP.SCOPE.value,
                    clientId = B2BIDP.JANUS_CLIENT.value,
                    authChain = B2BIDP.CHAIN_AUTH.value,
                )
            }
        }
    }
