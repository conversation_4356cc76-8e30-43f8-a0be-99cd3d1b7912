package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.digitalkey.api.SmsOptInServiceApi
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import org.forgerock.android.auth.retrofit.ConsentResponse
import retrofit2.http.Header
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class SmsOptInRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val smsOptInServiceApi: SmsOptInServiceApi,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getConsentStatus(
            @Header("phoneNumber") phoneNumber: String?,
        ): Resource<ConsentResponse?> {
            return makeApiCall { smsOptInServiceApi.getConsentStatus(phoneNumber) }
        }
    }
