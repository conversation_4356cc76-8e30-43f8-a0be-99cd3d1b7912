package com.toyota.oneapp.network.api.manager

import com.toyota.oneapp.model.garage.NotificationRequest20TM
import com.toyota.oneapp.network.api.cy17plus.CY17PlusServiceAPI
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.CallbackProvider
import javax.inject.Inject

class SoftwareUpdate20TMAPIManager
    @Inject
    constructor(
        private val client: CY17PlusServiceAPI,
        private val callbackProvider: CallbackProvider,
    ) : ServiceAPIManager() {
        fun getNotificationResponse(
            vin: String,
            callback: BaseCallback<NotificationRequest20TM>,
        ) {
            executeSubscriber(client.getNotificationResponse(vin), callbackProvider.create(callback))
        }
    }
