package com.toyota.oneapp.network.api.manager

import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.lcfs.LCFSCustomerConsentRequest
import com.toyota.oneapp.network.api.vgi.VGIServiceAPI
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.CallbackProvider
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class LCFSAPIManager
    @Inject
    internal constructor(
        private val vgiServiceAPI: VGIServiceAPI,
        private val callbackProvider: CallbackProvider,
    ) : ServiceAPIManager(),
        CoroutineScope {
        override val defaultRetryAttempts: Long
            get() = 3

        override val coroutineContext: CoroutineContext
            get() = Dispatchers.IO

        fun sendLCFSCustomerConsent(
            vin: String,
            guid: String,
            status: String,
            lcfsConsentRequestItem: ConsentRequestItem,
            callback: BaseCallback<BaseResponse>,
        ) {
            launch {
                executeSubscriber(
                    vgiServiceAPI.sendLcfsCustomerConsent(
                        LCFSCustomerConsentRequest(
                            vin = vin,
                            guid = guid,
                            eventType = "UpdateSubscription",
                            consents = listOf(lcfsConsentRequestItem.copy(status = status)),
                        ),
                    ),
                    callbackProvider.create(callback),
                    defaultRetryAttempts,
                )
            }
        }
    }
