package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.features.core.commonapicalls.dataaccess.service.CommonApi
import com.toyota.oneapp.features.vehicleinfo.dataaccess.servermodel.SoftwareUpdateResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class SoftwareUpdate20TMRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val commonApi: CommonApi,
    ) : BaseRepository(
            errorMessageParser,
            dispatcherProvider.io(),
        ) {
        suspend fun getSoftwareUpdateNotification(vin: String): Resource<SoftwareUpdateResponse?> {
            return makeApiCall { commonApi.fetchSoftwareUpdates(vin) }
        }
    }
