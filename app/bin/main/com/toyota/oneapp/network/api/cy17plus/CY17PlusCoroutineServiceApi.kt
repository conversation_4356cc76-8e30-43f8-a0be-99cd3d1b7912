package com.toyota.oneapp.network.api.cy17plus

import com.toyota.oneapp.model.account.AcknowledgeConsentRequest
import com.toyota.oneapp.model.account.UpdateDataConsentRequest
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsentRequest
import com.toyota.oneapp.model.combineddataconsent.CombinedDataConsentPayload
import com.toyota.oneapp.model.dashboard.MarkReadRequestPayload
import com.toyota.oneapp.model.dashboard.NotificationHistoryResponse
import com.toyota.oneapp.model.dealer.DealerIdResponse
import com.toyota.oneapp.model.dealer.PreferDealerResponse
import com.toyota.oneapp.model.lcfs.LCFSEligibilityResponse
import com.toyota.oneapp.model.reconsent.ReConsentResponse
import com.toyota.oneapp.model.subscription.CalculateTaxesRequest
import com.toyota.oneapp.model.subscription.CalculateTaxesResponse
import com.toyota.oneapp.model.subscription.CancelSubscriptionPlusRequest
import com.toyota.oneapp.model.subscription.CancelSubscriptionResponse
import com.toyota.oneapp.model.subscription.CancellationDataRequest
import com.toyota.oneapp.model.subscription.CancellationDataResponse
import com.toyota.oneapp.model.subscription.SubscriptionPreviewDetailV2
import com.toyota.oneapp.model.subscriptionV2.VehicleSubscriptionResponse
import com.toyota.oneapp.model.vehicle.VehicleDetailResponse
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.HeaderMap
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path

interface CY17PlusCoroutineServiceApi {
    @Headers(
        "Content-Type: application/json",
        "Accept-Encoding: deflate",
        "cache-control: no-cache",
    )
    @PUT("/oneapi/v1/subscription/dataconsent")
    suspend fun updateDataConsents(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("X-REGION") region: String,
        @Header("X-GENERATION") generation: String,
        @Header("DATETIME") dateTime: Long,
        @Body body: UpdateDataConsentRequest,
    ): Response<BaseResponse>

    @Headers("Content-Type: application/json", "accept-encoding: deflate")
    @GET("/oneapi/v2/vehicle-subscriptions")
    suspend fun getVehicleSubscriptions(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("REGION") region: String,
        @Header("GENERATION") generation: String,
        @Header("ASI-CODE") asiCode: String,
        @Header("HW-TYPE") hwType: String,
        @Header("DATETIME") dateTime: Long,
    ): Response<VehicleSubscriptionResponse>

    @POST("/oneapi/v1/previewrefund")
    suspend fun getRefundPreview(
        @Body cancellationDataRequest: CancellationDataRequest,
    ): Response<CancellationDataResponse?>

    @Headers("cache-control: no-cache", "X-CHANNEL: TC_AGENT", "Content-Type: application/json")
    @POST("/oneapi/v1/preview")
    suspend fun getSubscriptionsTaxAmount(
        @Header("REGION") region: String,
        @Body body: CalculateTaxesRequest?,
    ): Response<CalculateTaxesResponse>

    @POST("/oneapi/v1/vehicle-subscriptions")
    suspend fun createOrWaiveSubscriptionV2(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("REGION") region: String,
        @Header("GENERATION") generation: String,
        @Header("ASI-CODE") asiCode: String?,
        @Header("HW-TYPE") hwType: String?,
        @Body body: SubscriptionPreviewDetailV2,
    ): Response<BaseResponse>

    @Headers("Content-Type: application/json")
    @PUT("/oneapi/v1/subscription/cancel")
    suspend fun cancelSubscription(
        @Header("VIN") vin: String,
        @Header("X-BRAND") brand: String,
        @Header("GUID") guid: String,
        @Header("DATETIME") dateTime: Long,
        @Body request: CancelSubscriptionPlusRequest,
    ): Response<CancelSubscriptionResponse>

    // Privacy Portal

    @POST("/oneapi/v2/dataconsent")
    suspend fun getDataConsent(
        @HeaderMap headers: HashMap<String, String>,
        @Body body: CombineDataConsentRequest,
    ): Response<ApiResponse<CombinedDataConsentPayload>?>

    // TODO Create its own service api manager
    @GET("/oneapi/v1/vgi/eligibility")
    suspend fun getLcfsEligibility(
        @Header("vin") vin: String,
        @Header("X-BRAND") brand: String,
    ): Response<LCFSEligibilityResponse?>

    @GET("/oneapi/v2/notification/history")
    suspend fun sendNotificationHistoryRequest(
        @Header("GUID") guid: String,
    ): Response<NotificationHistoryResponse>

    @Headers("Content-Type: application/json")
    @POST("/oneapi/v1/notification/history/markread")
    suspend fun sendNotificationMarkReadRequest(
        @Body request: MarkReadRequestPayload,
    ): Response<BaseResponse>

    @GET("/oneapi/v1/preferred-dealer")
    suspend fun getPreferredDealer(
        @Header("X-REGION") region: String?,
        @Header("VIN") vinNumber: String?,
    ): Response<PreferDealerResponse>

    @GET("/oneapi/v4/dealers/{dealerCode}")
    suspend fun getDealerId(
        @Header("VIN") vin: String,
        @Path("dealerCode") dealerCode: String,
    ): Response<DealerIdResponse>

    @Headers("Content-Type: application/json")
    @POST("/oneapi/v1/customerconsent")
    suspend fun acknowledgeConsent(
        @Header("vin") vin: String,
        @Header("x-brand") brand: String,
        @Header("region") region: String,
        @Header("eligible-consent") eligibleconsent: String,
        @Body body: AcknowledgeConsentRequest,
    ): Response<BaseResponse>

    @GET("/oneapi/v1/one/vehicle")
    suspend fun getVehicleDetail(
        @Header("VIN") vehicleVIN: String,
    ): Response<VehicleDetailResponse>

    @GET("/oneapi/v1/reconsent/eligibility")
    suspend fun reConsent(
        @Header("x-vin") vin: String,
        @Header("x-region") vinRegion: String,
        @Header("x-generation") generation: String,
        @Header("x-brand") vinBrand: String,
    ): Response<ApiResponse<ReConsentResponse>?>
}
