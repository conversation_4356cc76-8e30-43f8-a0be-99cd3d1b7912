package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.subscription.BillingAddress
import com.toyota.oneapp.model.subscription.CalculateTaxesRequest
import com.toyota.oneapp.model.subscription.CalculateTaxesResponse
import com.toyota.oneapp.model.subscription.CancelSubscriptionPlusRequest
import com.toyota.oneapp.model.subscription.CancelSubscriptionResponse
import com.toyota.oneapp.model.subscription.PaymentRecord
import com.toyota.oneapp.model.subscription.ProductsAmount
import com.toyota.oneapp.model.subscription.ProductsTotalAmount
import com.toyota.oneapp.model.subscription.SubscriptionIDField
import com.toyota.oneapp.model.subscription.SubscriptionPreviewDetailV2
import com.toyota.oneapp.model.subscription.TaxAmount
import com.toyota.oneapp.model.subscription.TaxableProducts
import com.toyota.oneapp.model.subscriptionV2.CreateOrWaiveSubscriptionRequest
import com.toyota.oneapp.model.subscriptionV2.SubscriptionPackage
import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2
import com.toyota.oneapp.model.subscriptionV2.VehicleSubscriptionResponse
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.cy17plus.CY17PlusCoroutineServiceApi
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.DateUtil
import toyotaone.commonlib.coroutine.DispatcherProvider
import java.util.Date
import javax.inject.Inject

class SubscriptionV2Repository
    @Inject
    constructor(
        private val dateUtil: DateUtil,
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val cY17PlusCoroutineServiceApi: CY17PlusCoroutineServiceApi,
        private val preferenceModel: OneAppPreferenceModel,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getVehicleSubscriptions(
            vin: String,
            brand: String,
            region: String,
            generation: String,
            asiCode: String,
            hwType: String,
        ): Resource<VehicleSubscriptionResponse?> =
            makeApiCall {
                cY17PlusCoroutineServiceApi.getVehicleSubscriptions(
                    vin = vin,
                    brand = brand,
                    region = region,
                    generation = generation,
                    asiCode = asiCode,
                    hwType = hwType,
                    dateTime = System.currentTimeMillis(),
                )
            }

        suspend fun getSubscriptionTaxAmount(
            region: String,
            billingAddress: BillingAddress,
            packages: List<SubscriptionPackage>,
        ): Resource<CalculateTaxesResponse?> {
            val subscriptions =
                packages.map {
                    TaxableProducts(
                        ratePlanID = it.ratePlanID.orEmpty(),
                        period = it.term ?: 0,
                        periodType = if ("YEAR" == it.termUnit) "Year" else "Month",
                    )
                }
            val request =
                CalculateTaxesRequest(
                    city = billingAddress.city,
                    state = billingAddress.state,
                    country = billingAddress.country,
                    postalCode = billingAddress.postalCode,
                    orderDate = dateUtil.formatLocalDate(Date()),
                    subscriptions = subscriptions,
                )

            return makeApiCall {
                cY17PlusCoroutineServiceApi.getSubscriptionsTaxAmount(
                    region = region,
                    body = request,
                )
            }
        }

        suspend fun createOrWaiveSubscription(
            // headers parameters
            vin: String,
            brand: String,
            region: String,
            generation: String,
            // Request parameters
            consents: List<ConsentRequestItem>?,
            waiver: Boolean,
            subscriptions: List<CreateOrWaiveSubscriptionRequest.Subscriptions>?,
            productsAmount: ProductsAmount?,
            taxAmount: TaxAmount?,
            productsTotalAmount: ProductsTotalAmount?,
            remoteUser: CreateOrWaiveSubscriptionRequest.RemoteUser,
            setAsDefaultPaymentMethod: Boolean,
            paymentMethodId: String?,
            accessToken: String?,
            paymentToken: String?,
            externalSubscriptions: List<CreateOrWaiveSubscriptionRequest.ExternalSubscriptions>?,
            dataConsent: DataConsent,
        ): Resource<BaseResponse?> {
            val request =
                CreateOrWaiveSubscriptionRequest(
                    consents = consents,
                    waiver = waiver,
                    subscriptions = subscriptions,
                    taxAmount = taxAmount,
                    productsAmount = productsAmount,
                    productsTotalAmount = productsTotalAmount,
                    remoteUser = remoteUser,
                    setAsDefaultPaymentMethod = setAsDefaultPaymentMethod,
                    paymentMethodId = paymentMethodId,
                    accessToken = accessToken,
                    paymentToken = paymentToken,
                    externalSubscriptions = externalSubscriptions,
                    dataConsent = dataConsent,
                )

            return makeApiCall {
                cY17PlusCoroutineServiceApi.createOrWaiveSubscriptionV2(
                    vin = vin,
                    brand = brand,
                    region = region,
                    generation = generation,
                    asiCode = null,
                    hwType = null,
                    body = SubscriptionPreviewDetailV2(),
                )
            }
        }

        suspend fun cancelSubscriptions(
            vehicleInfo: VehicleInfo,
            subscriptionIds: ArrayList<String>?,
            externalSubscriptions: List<SubscriptionV2> = emptyList(),
            cancelledReason: String? = null,
            paymentRecord: PaymentRecord? = null,
        ): Resource<CancelSubscriptionResponse?> {
            val subscriptionIDField = SubscriptionIDField()
            subscriptionIDField.subscriptionIds = subscriptionIds
            return makeApiCall {
                cY17PlusCoroutineServiceApi.cancelSubscription(
                    vin = vehicleInfo.vin,
                    brand = vehicleInfo.brand,
                    guid = preferenceModel.getGuid(),
                    dateTime = System.currentTimeMillis(),
                    request =
                        CancelSubscriptionPlusRequest(
                            subscriberGuid = preferenceModel.getGuid(),
                            vin = vehicleInfo.vin,
                            generation = vehicleInfo.generation,
                            subscription = subscriptionIDField,
                            externalSubscriptions = externalSubscriptions,
                            canceledReason = cancelledReason,
                            refundPaymentMethodId = paymentRecord?.id,
                            refundPaymentType = paymentRecord?.type,
                        ),
                )
            }
        }
    }
