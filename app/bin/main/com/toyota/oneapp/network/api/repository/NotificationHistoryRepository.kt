package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.dashboard.MarkReadRequestPayload
import com.toyota.oneapp.model.dashboard.NotificationHistoryResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.cy17plus.CY17PlusCoroutineServiceApi
import com.toyota.oneapp.network.models.BaseResponse
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class NotificationHistoryRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val serviceApi: CY17PlusCoroutineServiceApi,
    ) : BaseRepository(
            errorMessageParser,
            dispatcherProvider.io(),
        ) {
        suspend fun sendNotificationHistoryRequest(guid: String): Resource<NotificationHistoryResponse?> {
            return makeApiCall { serviceApi.sendNotificationHistoryRequest(guid) }
        }

        suspend fun sendNotificationMarkReadRequest(markReadRequestPayload: MarkReadRequestPayload): Resource<BaseResponse?> {
            return makeApiCall { serviceApi.sendNotificationMarkReadRequest(markReadRequestPayload) }
        }
    }
