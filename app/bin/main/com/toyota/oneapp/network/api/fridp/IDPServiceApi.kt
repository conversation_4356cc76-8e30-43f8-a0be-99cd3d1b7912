package com.toyota.oneapp.network.api.fridp

import com.toyota.oneapp.model.fr.SXMIdTokenFromFRIdpResponse
import retrofit2.Response
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.Headers
import retrofit2.http.POST

interface IDPServiceApi {
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @POST("/oauth2/realms/root/realms/tmna-native/access_token")
    @FormUrlEncoded
    suspend fun getB2BTokenSXMTokenFromFR(
        @Field("grant_type") grantType: String,
        @Field("username") username: String,
        @Field("password") password: String,
        @Field("scope") scope: String,
        @Field("client_id") clientId: String,
        @Field("auth_chain") authChain: String,
    ): Response<SXMIdTokenFromFRIdpResponse>
}
