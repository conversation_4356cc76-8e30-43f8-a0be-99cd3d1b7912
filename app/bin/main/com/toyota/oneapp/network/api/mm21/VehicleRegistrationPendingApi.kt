package com.toyota.oneapp.network.api.mm21

import com.toyota.oneapp.model.account.VehicleRegistrationPendingPayload
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.*

interface VehicleRegistrationPendingApi {
    @Headers(CONTENT_TYPE_JSON)
    @PUT("/oa21mm/v1/svc/guid/{guid}/vin/{vin}")
    suspend fun updateVehicleRegistrationStatus(
        @Path("guid") guid: String,
        @Path("vin") vin: String,
    ): Response<ApiResponse<VehicleRegistrationPendingPayload>>

    @Headers(CONTENT_TYPE_JSON)
    @GET("/oa21mm/v1/svc/guid/{guid}/vins")
    suspend fun getPendingVins(
        @Path("guid") guid: String,
    ): Response<ApiResponse<List<VehicleRegistrationPendingPayload>>?>

    @DELETE("/oa21mm/v1/svc/guid/{guid}/vin/{vin}")
    suspend fun removeVin(
        @Path("guid") guid: String,
        @Path("vin") vin: String,
    ): Response<ApiResponse<BaseResponse>>
}
