package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.dealer.DealerIdResponse
import com.toyota.oneapp.model.dealer.PreferDealerResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.cy17plus.CY17PlusCoroutineServiceApi
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class ScheduleMaintenanceRepository
    @Inject
    constructor(
        dispatcherProvider: DispatcherProvider,
        errorMessageParser: ErrorMessageParser,
        private val cY17PlusCoroutineServiceApi: CY17PlusCoroutineServiceApi,
    ) :
    BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getPreferredDealer(
            vin: String,
            region: String,
        ): Resource<PreferDealerResponse?> {
            return makeApiCall { cY17PlusCoroutineServiceApi.getPreferredDealer(region, vin) }
        }

        suspend fun getDealerId(
            vin: String,
            dealerCode: String,
        ): Resource<DealerIdResponse?> {
            return makeApiCall {
                cY17PlusCoroutineServiceApi.getDealerId(
                    vin = vin,
                    dealerCode = dealerCode,
                )
            }
        }
    }
