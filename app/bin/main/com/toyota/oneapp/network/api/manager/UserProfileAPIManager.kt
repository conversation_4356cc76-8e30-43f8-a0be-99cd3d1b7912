package com.toyota.oneapp.network.api.manager

import android.content.res.Resources
import com.google.protobuf.ByteString
import com.google.protobuf.FieldMask
import com.google.protobuf.StringValue
import com.google.protobuf.Timestamp
import com.toyota.ctp.v1.*
import com.toyota.ctp.v1.LocationOuterClass.Location
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.model.dealer.Dealer
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.network.dataprovider.NetworkDataProvider
import com.toyota.oneapp.network.interceptor.GrpcInterceptor
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.destinations.destination.DestinationsFragment
import com.toyota.oneapp.util.ToyotaConstants
import io.grpc.stub.StreamObserver
import toyotaone.commonlib.log.LogTool
import java.io.IOException
import javax.inject.Inject

class UserProfileAPIManager
    @Inject
    constructor(
        private val networkDataProvider: NetworkDataProvider,
        private val preferenceModel: OneAppPreferenceModel,
        private val grpcInterceptor: GrpcInterceptor,
        private val resources: Resources,
    ) : ServiceAPIManager() {
        companion object {
            private const val TAG = "UserProfileAPIManager"
            private const val HOME_FIELD_MASK = "commonVehicleSettings.navigationSettings.home.value"
            private const val WORK_FIELD_MASK = "commonVehicleSettings.navigationSettings.work.value"
            private const val FAV_FIELD_MASK =
                "commonVehicleSettings.navigationSettings.favorites.value"

            private const val PROFILE_NAME_FIELD_MASK = "userInfo.profileName"
            private const val PREFERRED_DEALER_INFO_FIELD_MASK = "vehicleSpecificSettings"
        }

        fun getBasicProfile(responseObserver: StreamObserver<ProfileServiceServer.GetBaseProfileResponse>) {
            if (networkDataProvider.hasNetwork()) {
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Downloading packets")
                val channel =
                    generateRPCManagedChannel(
                        BuildConfig.USER_PROFILE_URL,
                        Integer.valueOf(BuildConfig.USER_PROFILE_PORT),
                        networkDataProvider.rawCertData(),
                        grpcInterceptor,
                    )
                val request =
                    ProfileServiceServer.GetBaseProfileRequest
                        .newBuilder()
                        .setUserId(preferenceModel.getGuid())
                        .build()
                ProfileServiceGrpc.newStub(channel).getBaseProfile(
                    request,
                    object : StreamObserver<ProfileServiceServer.GetBaseProfileResponse> {
                        override fun onNext(value: ProfileServiceServer.GetBaseProfileResponse?) {
                            preferenceModel.setUserProfilePacket1(value?.packet1 ?: ByteString.EMPTY)
                            preferenceModel.setUserProfilePacket2(value?.packet2 ?: ByteString.EMPTY)
                            responseObserver.onNext(value)
                        }

                        override fun onError(t: Throwable?) {
                            LogTool.d(TAG, "Failed to download profile packets: ${t?.message}", t)
                            if (hasCachedProfile()) {
                                responseObserver.onNext(getUserProfileResponseFromCache())
                                responseObserver.onCompleted()
                            } else {
                                responseObserver.onError(t)
                            }
                            channel?.shutdown()
                        }

                        override fun onCompleted() {
                            channel?.shutdown()
                            responseObserver.onCompleted()
                        }
                    },
                )
            } else if (hasCachedProfile()) {
                responseObserver.onNext(getUserProfileResponseFromCache())
                responseObserver.onCompleted()
            }
        }

        fun getUserProfile(responseObserver: StreamObserver<ProfileServiceServer.GetUserProfileResponse>) {
            val channel =
                generateRPCManagedChannel(
                    BuildConfig.USER_PROFILE_URL,
                    Integer.valueOf(BuildConfig.USER_PROFILE_PORT),
                    networkDataProvider.rawCertData(),
                    grpcInterceptor,
                )

            if (channel == null) {
                responseObserver.onError(
                    IOException(resources.getString(R.string.grpc_channel_null_exception)),
                )
                return
            }

            val request =
                ProfileServiceServer.GetUserProfileRequest
                    .newBuilder()
                    .setUserId(preferenceModel.getGuid())
                    .build()

            ProfileServiceGrpc.newStub(channel).getUserProfile(
                request,
                object : StreamObserver<ProfileServiceServer.GetUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.GetUserProfileResponse) {
                        responseObserver.onNext(value)
                    }

                    override fun onError(t: Throwable) {
                        LogTool.d(TAG, "Failed to download profile: ${t.message}", t)
                        channel.shutdown()
                        responseObserver.onError(t)
                    }

                    override fun onCompleted() {
                        channel.shutdown()
                        responseObserver.onCompleted()
                    }
                },
            )
        }

        fun updateLocation(
            currentLocationItem: LocationDetails,
            preferenceType: String,
            responseObserver: StreamObserver<ProfileServiceServer.UpdateUserProfileResponse>,
        ) {
            val poiLocation = getPoiLocation(currentLocationItem)
            val poiValue =
                ProfileServiceTier1.NavigationSettings.PoiValue
                    .newBuilder()
                    .setValue(poiLocation)
            val navigationSettingsBuilder = ProfileServiceTier1.NavigationSettings.newBuilder()
            var fieldMaskPath = ""
            when (preferenceType) {
                DestinationsFragment.HOME_PREFERENCE -> {
                    navigationSettingsBuilder.setHome(poiValue)
                    fieldMaskPath = HOME_FIELD_MASK
                }
                DestinationsFragment.WORK_PREFERENCE -> {
                    navigationSettingsBuilder.setWork(poiValue)
                    fieldMaskPath = WORK_FIELD_MASK
                }
            }
            syncNavigationSettings(
                navigationSettingsBuilder,
                responseObserver,
                fieldMaskPath,
            )
        }

        private fun syncNavigationSettings(
            navigationSettingsBuilder: ProfileServiceTier1.NavigationSettings.Builder?,
            responseObserver: StreamObserver<ProfileServiceServer.UpdateUserProfileResponse>? = null,
            fieldMaskPath: String,
        ) {
            val comVehicleSettings =
                ProfileServiceTier1.CommonVehicleSettings
                    .newBuilder()
                    .setNavigationSettings(navigationSettingsBuilder?.build())
                    .build()
            val vehicleUserProfile =
                ProfileServiceServer.UserProfile
                    .newBuilder()
                    .setUserId(preferenceModel.getGuid())
                    .setCommonVehicleSettings(comVehicleSettings)
                    .build()

            updateUserProfile(responseObserver, vehicleUserProfile, fieldMaskPath)
        }

        fun updateFavouriteLocation(
            favList: ArrayList<LocationDetails>,
            responseObserver: StreamObserver<ProfileServiceServer.UpdateUserProfileResponse>? = null,
        ) {
            val poiList = ProfileServiceTier1.NavigationSettings.PoiList.newBuilder()
            poiList.addAllValue(favList.map { fav -> getPoiLocation(fav) })

            val navigationSettingsBuilder = ProfileServiceTier1.NavigationSettings.newBuilder()
            navigationSettingsBuilder.setFavorites(poiList)
            syncNavigationSettings(
                navigationSettingsBuilder,
                responseObserver,
                FAV_FIELD_MASK,
            )
        }

        fun updateProfileName(
            profileName: String,
            responseObserver: StreamObserver<ProfileServiceServer.UpdateUserProfileResponse>? = null,
        ) {
            val stringValue = StringValue.newBuilder().setValue(profileName).build()
            val userInfo =
                ProfileServiceTier1.UserInfo
                    .newBuilder()
                    .setProfileName(stringValue)

            val vehicleUserProfile =
                ProfileServiceServer.UserProfile
                    .newBuilder()
                    .setUserId(preferenceModel.getGuid())
                    .setUserInfo(userInfo)
                    .build()
            updateUserProfile(
                responseObserver,
                vehicleUserProfile,
                PROFILE_NAME_FIELD_MASK,
            )
        }

        fun updatePreferredDealer(
            vin: String,
            dealer: Dealer,
            responseObserver: StreamObserver<ProfileServiceServer.UpdateUserProfileResponse>? = null,
        ) {
            val phoneNumber =
                if (dealer.phoneNumbers != null) {
                    dealer.phoneNumbers?.get(0)?.number
                } else {
                    ""
                }
            val dealerInfo =
                ProfileServiceTier1.VehicleRemoteServicesSettings.DealerInformation
                    .newBuilder()
                    .setDealerName(dealer.dealershipName)
                    .setContactName(dealer.dealershipName)
                    .setPhoneNumber(phoneNumber)
                    .build()
            val remoteServiceSettings =
                ProfileServiceTier1.VehicleRemoteServicesSettings
                    .newBuilder()
                    .setDealerInformation(
                        ProfileServiceTier1.VehicleRemoteServicesSettings.DealerInformationValue
                            .newBuilder()
                            .setValue(dealerInfo)
                            .build(),
                    )
            val vehicleSpecificSettings =
                ProfileServiceTier1.VehicleSpecificSettings
                    .newBuilder()
                    .setRemoteServicesSettings(remoteServiceSettings)
                    .build()

            val vehicleUserProfile =
                ProfileServiceServer.UserProfile
                    .newBuilder()
                    .setUserId(preferenceModel.getGuid())
                    .putVehicleSpecificSettings(vin, vehicleSpecificSettings)
                    .build()
            updateUserProfile(
                responseObserver,
                vehicleUserProfile,
                PREFERRED_DEALER_INFO_FIELD_MASK,
            )
        }

        private fun getPoiLocation(locationDetails: LocationDetails): ProfileServiceTier1.NavigationSettings.Poi {
            val locationValue =
                Location
                    .newBuilder()
                    .setLongitude(locationDetails.locCoordinate?.longitude ?: 0.0)
                    .setLatitude(locationDetails.locCoordinate?.latitude ?: 0.0)
                    .build()
            val routingValue =
                Location
                    .newBuilder()
                    .setLongitude(locationDetails.routCoordinate.longitude)
                    .setLatitude(locationDetails.routCoordinate.latitude)
                    .build()
            val address =
                ProfileServiceTier1.NavigationSettings.Poi.Address
                    .newBuilder()
                    .apply {
                        street = locationDetails.address?.street
                        city = locationDetails.address?.city
                        adminRegion = locationDetails.address?.adminRegion
                        adminRegionShort = locationDetails.address?.adminRegionShort
                        postalCode = locationDetails.address?.postalCode
                        houseNumber = locationDetails.address?.houseNumber
                        intersection = locationDetails.intersection ?: ""
                        countryShort = locationDetails.address?.countryShort
                    }.build()

            return ProfileServiceTier1.NavigationSettings.Poi
                .newBuilder()
                .setName(locationDetails.name)
                .setAddress(address)
                .setPlaceId(locationDetails.placeId)
                .setRouting(routingValue)
                .setLocationType(locationDetails.location_type?.toLong() ?: 0)
                .setFormattedAddress(locationDetails.formattedAddress)
                .setPhoneNumber(locationDetails.phoneNumber ?: "")
                .setRefreshDate(getRefreshDate(locationDetails))
                .setTimestamp(getTimeStamp(locationDetails))
                .setLocation(locationValue)
                .build()
        }

        private fun getRefreshDate(locationDetails: LocationDetails): Timestamp {
            val refreshDate = locationDetails.refreshDate ?: 0
            return Timestamp
                .newBuilder()
                .setSeconds(refreshDate)
                .setNanos((refreshDate % 1000 * 1000000).toInt())
                .build()
        }

        private fun getTimeStamp(locationDetails: LocationDetails): Timestamp {
            val timestamp = locationDetails.timeStamp ?: 0
            return Timestamp
                .newBuilder()
                .setSeconds(timestamp)
                .setNanos((timestamp % 1000 * 1000000).toInt())
                .build()
        }

        fun ProfileServiceTier1.NavigationSettings.Poi.exists(): Boolean =
            hasLocation() && !(location?.latitude == 0.0 && location?.longitude == 0.0)

        private fun updateUserProfile(
            responseObserver: StreamObserver<ProfileServiceServer.UpdateUserProfileResponse>?,
            vehicleUserProfile: ProfileServiceServer.UserProfile,
            fieldMaskValue: String,
        ) {
            val channel =
                generateRPCManagedChannel(
                    BuildConfig.USER_PROFILE_URL,
                    Integer.valueOf(BuildConfig.USER_PROFILE_PORT),
                    networkDataProvider.rawCertData(),
                    grpcInterceptor,
                )

            if (channel == null) {
                responseObserver?.onError(
                    IOException(resources.getString(R.string.grpc_channel_null_exception)),
                )
                return
            }

            val fieldMaskPath = FieldMask.newBuilder().addPaths(fieldMaskValue).build()
            val request =
                ProfileServiceServer.UpdateUserProfileRequest
                    .newBuilder()
                    .apply {
                        userProfile = vehicleUserProfile
                        fieldMask = fieldMaskPath
                    }.setUserProfile(vehicleUserProfile)
                    .build()
            ProfileServiceGrpc.newStub(channel).updateUserProfile(
                request,
                object : StreamObserver<ProfileServiceServer.UpdateUserProfileResponse> {
                    override fun onNext(value: ProfileServiceServer.UpdateUserProfileResponse) {
                        responseObserver?.onNext(value)
                    }

                    override fun onError(t: Throwable) {
                        LogTool.d(TAG, "Update User Profile Failed: ${t.message}", t)
                        channel.shutdown()
                        responseObserver?.onError(t)
                    }

                    override fun onCompleted() {
                        channel.shutdown()
                        responseObserver?.onCompleted()
                    }
                },
            )
        }

        fun hasCachedProfile(): Boolean =
            preferenceModel.getUserProfilePacket1() != null &&
                preferenceModel.getUserProfilePacket2() != null

        fun getCachedPacket2(): ByteString = preferenceModel.getUserProfilePacket2() ?: ByteString.EMPTY

        private fun getUserProfileResponseFromCache(): ProfileServiceServer.GetBaseProfileResponse {
            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Using cached packets")
            return ProfileServiceServer.GetBaseProfileResponse
                .newBuilder()
                .setPacket1(preferenceModel.getUserProfilePacket1())
                .setPacket2(preferenceModel.getUserProfilePacket2())
                .build()
        }

        // Vehicle Profile
        fun unRegisterVehicle(
            selectedVehicleVin: String? = null,
            responseObserver: StreamObserver<VehicleRegistrationServiceOuterClass.DeregisterVehicleResponse>?,
        ) {
            val deregisterVehicleRequest =
                VehicleRegistrationServiceOuterClass.DeregisterVehicleRequest
                    .newBuilder()
                    .apply {
                        vin = selectedVehicleVin
                        userId = preferenceModel.getGuid()
                    }.build()
            val channel =
                generateRPCManagedChannel(
                    BuildConfig.VEHICLE_REGISTRATION_BASE_URL,
                    Integer.valueOf(BuildConfig.USER_PROFILE_PORT),
                    networkDataProvider.rawCertData(),
                    grpcInterceptor,
                )
            VehicleRegistrationServiceGrpc.newStub(channel).deregisterVehicle(
                deregisterVehicleRequest,
                object : StreamObserver<VehicleRegistrationServiceOuterClass.DeregisterVehicleResponse> {
                    override fun onNext(value: VehicleRegistrationServiceOuterClass.DeregisterVehicleResponse) {
                        responseObserver?.onNext(value)
                    }

                    override fun onError(t: Throwable) {
                        LogTool.d(TAG, "Vehicle DeRegistration Failed:  ${t.message}", t)
                        channel?.shutdown()
                        responseObserver?.onError(t)
                    }

                    override fun onCompleted() {
                        channel?.shutdown()
                        responseObserver?.onCompleted()
                    }
                },
            )
        }
    }
