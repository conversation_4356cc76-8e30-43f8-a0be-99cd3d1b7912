package com.toyota.oneapp.network.api.manager

import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.model.subscription.SXMAccessTokenResponse
import com.toyota.oneapp.network.api.SXM.SXMServiceAPI
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.CallbackProvider
import com.toyota.oneapp.util.ToyotaConstants
import javax.inject.Inject

class SXMAPIManager
    @Inject
    constructor(
        private val client: SXMServiceAPI,
        private val callbackProvider: CallbackProvider,
    ) {
        fun sendGetSxmToken(
            token: String = ToyotaConstants.EMPTY_STRING,
            callback: BaseCallback<SXMAccessTokenResponse>,
        ) {
            client.sendGetSXMPaymentToken(
                token = token,
                clientId = if (BuildConfig.IS_TOYOTA_APP) BuildConfig.SXM_CLIENT_ID else BuildConfig.SXM_CLIENT_ID_LEXUS,
            ).enqueue(callbackProvider.create(callback))
        }
    }
