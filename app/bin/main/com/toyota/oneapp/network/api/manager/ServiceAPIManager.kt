package com.toyota.oneapp.network.api.manager

import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.network.callback.RxCallback
import com.toyota.oneapp.network.interceptor.GrpcInterceptor
import com.toyota.oneapp.util.ToyotaConstants
import io.grpc.ManagedChannel
import io.grpc.okhttp.OkHttpChannelBuilder
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import toyotaone.commonlib.log.LogTool
import java.io.ByteArrayInputStream
import java.security.KeyStore
import java.security.cert.CertificateFactory
import java.util.concurrent.TimeUnit
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManagerFactory

open class ServiceAPIManager {
    protected open val defaultRetryAttempts: Long = 0

    @JvmOverloads
    protected fun <T> executeSubscriber(
        observable: Observable<T>,
        callback: RxCallback<T>,
        retryAttempts: Long = defaultRetryAttempts,
        delay: Long = 0,
    ) {
        observable
            .delay(delay, TimeUnit.MILLISECONDS)
            .retry(retryAttempts)
            .subscribeOn(Schedulers.io())
            .unsubscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callback)
    }

    fun generateRPCManagedChannel(
        url: String?,
        port: Int,
        certData: ByteArray,
        grpcInterceptor: GrpcInterceptor,
    ): ManagedChannel? {
        val channelBuilder: OkHttpChannelBuilder = OkHttpChannelBuilder.forAddress(url, port)
        channelBuilder.intercept(grpcInterceptor)
        if (!BuildConfig.IS_CERTIFICATION_REQUIRED.toBoolean()) {
            return channelBuilder.build()
        }
        var sslSocketFactory: SSLSocketFactory? = null
        try {
            val inputStream = ByteArrayInputStream(certData)
            val keyStore =
                KeyStore.getInstance(KeyStore.getDefaultType())
            keyStore.load(null, null)
            val cf =
                CertificateFactory.getInstance(ToyotaConstants.GRPC_CERT_TYPE)
            keyStore.setCertificateEntry(
                ToyotaConstants.GRPC_CERT_ALIAS,
                cf.generateCertificate(inputStream),
            )
            inputStream.close()
            val keyManagerFactory =
                KeyManagerFactory.getInstance(ToyotaConstants.KEY_MANAGER_ALGO)
            keyManagerFactory.init(keyStore, ToyotaConstants.EMPTY_STRING.toCharArray())
            val trustManagerFactory =
                TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
            trustManagerFactory.init(keyStore)
            val sslContext = SSLContext.getInstance(ToyotaConstants.SSL_PROTOCOL)
            sslContext.init(
                keyManagerFactory.keyManagers,
                trustManagerFactory.trustManagers,
                null,
            )
            sslSocketFactory = sslContext.socketFactory
        } catch (e: Exception) {
            logError(e)
        }

        return sslSocketFactory?.let {
            channelBuilder.sslSocketFactory(it).build()
        }
    }

    private fun logError(ex: Exception) {
        LogTool.d(ServiceAPIManager::class.java.simpleName, ex.message, ex)
    }
}
