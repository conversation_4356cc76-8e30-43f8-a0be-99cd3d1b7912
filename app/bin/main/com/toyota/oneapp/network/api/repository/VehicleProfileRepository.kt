package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.vehicle.SecondaryVehicleProfilePayload
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.mm21.CoroutineService21mmApi
import com.toyota.oneapp.network.models.ApiResponse
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class VehicleProfileRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val coroutineService21mmApi: CoroutineService21mmApi,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getSecondaryVehicleProfile(): Resource<ApiResponse<List<SecondaryVehicleProfilePayload>>?> {
            return makeApiCall { coroutineService21mmApi.getSecondaryVehicleDetails() }
        }
    }
