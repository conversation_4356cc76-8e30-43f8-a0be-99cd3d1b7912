package com.toyota.oneapp.network.api.SXM

import com.toyota.oneapp.model.subscription.SXMAccessTokenResponse
import com.toyota.oneapp.util.ToyotaConstants
import retrofit2.Call
import retrofit2.http.*

interface SXMServiceAPI {
    @Headers(
        "Content-Type: application/json",
        "CV-AppType: MOBILE",
        "CV-OS-Type: Android",
        "CV-TSP: ONEAPP",
    )
    @POST("/auth/oauth2/exchange/token")
    fun sendGetSXMPaymentToken(
        @Header("cv-oem-auth-token") token: String = ToyotaConstants.EMPTY_STRING,
        @Header("cv-client-id") clientId: String,
    ): Call<SXMAccessTokenResponse>
}
