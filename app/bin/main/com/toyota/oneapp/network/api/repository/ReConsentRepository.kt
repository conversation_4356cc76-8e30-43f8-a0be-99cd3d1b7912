package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.reconsent.ReConsentResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.cy17plus.CY17PlusCoroutineServiceApi
import com.toyota.oneapp.network.models.ApiResponse
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class ReConsentRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val cY17PlusCoroutineServiceApi: CY17PlusCoroutineServiceApi,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getReConsent(
            vin: String,
            vinRegion: String,
            generation: String,
            vinBrand: String,
        ): Resource<ApiResponse<ReConsentResponse>?> {
            return makeApiCall {
                cY17PlusCoroutineServiceApi.reConsent(
                    vin = vin,
                    vinRegion = vinRegion,
                    generation = generation,
                    vinBrand = vinBrand,
                )
            }
        }
    }
