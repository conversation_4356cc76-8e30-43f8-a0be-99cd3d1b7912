package com.toyota.oneapp.network.api

import com.toyota.oneapp.model.EmailReportRequest
import com.toyota.oneapp.model.HealthHistoryResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.*

interface HealthReportCoroutineServiceApi {
    @Headers("Content-Type: application/json")
    @GET("/oneapi/v1/vhr/history")
    suspend fun getHealthHistoryResponse(
        @Header("vin") vin: String?,
    ): Response<HealthHistoryResponse?>

    @Headers("Content-Type: application/json")
    @GET("/oneapi/v1/vdr/history")
    suspend fun getHealthDiagnosticsResponse(
        @Header("vin") vin: String?,
    ): Response<HealthHistoryResponse?>

    @Headers("Content-Type: application/json")
    @POST("/oneapi/v1/vhr/email")
    suspend fun sendReportEmail(
        @Header("vin") vin: String?,
        @Body emailReportRequest: EmailReportRequest,
    ): Response<BaseResponse?>
}
