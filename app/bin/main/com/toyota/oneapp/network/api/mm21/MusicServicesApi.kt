package com.toyota.oneapp.network.api.mm21

import com.toyota.oneapp.model.subscription.MusicPreferencePayload
import com.toyota.oneapp.model.subscription.MusicSubscriptionRequest
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.*

const val CONTENT_TYPE_JSON = "Content-Type: application/json"

interface MusicServicesApi {
    @Headers(CONTENT_TYPE_JSON)
    @GET("/oa21mm/v1/svc/subscriptions")
    suspend fun getMusicSubscriptions(
        @Query("userProfileID") userProfileID: String,
    ): Response<ApiResponse<List<String>?>?>

    @Headers(CONTENT_TYPE_JSON)
    @POST("/oa21mm/v1/svc/oauthtoken")
    suspend fun updateMusicAuthToken(
        @Body musicRequest: MusicSubscriptionRequest,
    ): Response<BaseResponse?>

    @Headers(CONTENT_TYPE_JSON)
    @HTTP(method = "DELETE", path = "/oa21mm/v1/svc/oauthtoken", hasBody = true)
    suspend fun deleteMusicToken(
        @Body musicRequest: MusicSubscriptionRequest,
    ): Response<BaseResponse?>

    @Headers(CONTENT_TYPE_JSON)
    @POST("/oa21mm/v1/svc/musicpreference")
    suspend fun updateMusicPreferences(
        @Body musicPrefRequest: MusicPreferencePayload,
    ): Response<BaseResponse?>

    @Headers(CONTENT_TYPE_JSON)
    @GET("/oa21mm/v1/svc/musicpreference?")
    suspend fun getMusicPreferences(
        @Query("userid") userId: String,
    ): Response<ApiResponse<MusicPreferencePayload?>?>
}
