package com.toyota.oneapp.network.api.manager

import com.toyota.oneapp.model.AppDetailsResponse
import com.toyota.oneapp.network.api.cy17plus.CY17PlusInsecureServiceApi
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.callback.CallbackProvider
import javax.inject.Inject

class AppDetailsAPIManager
    @Inject
    constructor(
        private val client: CY17PlusInsecureServiceApi,
        private val callbackProvider: CallbackProvider,
    ) : ServiceAPIManager() {
        fun sendFeedbackRequest(callback: BaseCallback<AppDetailsResponse?>) {
            executeSubscriber(client.getAppDetails(), callbackProvider.create(callback))
        }
    }
