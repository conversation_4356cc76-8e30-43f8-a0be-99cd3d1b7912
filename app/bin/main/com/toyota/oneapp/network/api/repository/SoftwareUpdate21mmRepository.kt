package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.garage.NotificationRequest20TMPayload
import com.toyota.oneapp.model.garage.SoftwareVersion21MMPayloadResponse
import com.toyota.oneapp.model.garage.SoftwareVersionUpdateRequest
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.mm21.VehicleSoftwareServiceApi
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class SoftwareUpdate21mmRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val vehicleSoftwareServiceApi: VehicleSoftwareServiceApi,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getSoftware21MMVersion(
            vin: String,
            registrationId: String? = null,
        ): Resource<ApiResponse<List<SoftwareVersion21MMPayloadResponse?>?>?> {
            return makeApiCall { vehicleSoftwareServiceApi.getSoftware21MMVersion(vin, registrationId) }
        }

        suspend fun updateSoftwareVersion21MM(softwareVersionUpdateRequest: SoftwareVersionUpdateRequest): Resource<BaseResponse?> {
            return makeApiCall {
                vehicleSoftwareServiceApi.updateSoftwareVersion(
                    softwareVersionUpdateRequest,
                )
            }
        }

        suspend fun getNotificationResponse(vin: String?): Resource<ApiResponse<NotificationRequest20TMPayload?>?> {
            return makeApiCall { vehicleSoftwareServiceApi.getNotificationResponse(vin) }
        }
    }
