/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.models.GetBrandRequestBody
import com.toyota.oneapp.network.models.ScanQrRequestBody
import com.toyota.oneapp.network.models.SsoSession
import com.toyota.oneapp.network.token.TokenServiceAPI
import okhttp3.ResponseBody
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class IdpRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val tokenServiceAPI: TokenServiceAPI,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun verifyVehicleBrand(
            userCode: String,
            csrf: String,
        ): Resource<ResponseBody?> {
            return makeApiCall {
                tokenServiceAPI.getBrand(
                    getBrandRequestBody =
                        GetBrandRequestBody(
                            userCode = userCode,
                            csrf = csrf,
                        ),
                    iPlanetDirectoryPro = csrf,
                )
            }
        }

        suspend fun startVehicleRegistration(
            userCode: String,
            csrf: String,
        ): Resource<ResponseBody?> {
            return makeApiCall {
                tokenServiceAPI.scanQR(
                    scanQrRequestBody =
                        ScanQrRequestBody(
                            userCode = userCode,
                            csrf = csrf,
                        ),
                    iPlanetDirectoryPro = csrf,
                )
            }
        }

        suspend fun getSsoTokenSessionValidity(ssoToken: String): Resource<SsoSession?> {
            return makeApiCall {
                tokenServiceAPI.getSsoTokenSessionValidity(
                    iPlanetDirectoryPro = ssoToken,
                )
            }
        }
    }
