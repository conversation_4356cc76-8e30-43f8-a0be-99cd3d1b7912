package com.toyota.oneapp.network.api.mm21

import com.toyota.oneapp.model.account.PhotoResponse
import com.toyota.oneapp.network.models.BaseResponse
import okhttp3.MultipartBody
import retrofit2.Call
import retrofit2.http.*

const val PROFILE_PICTURE_ENDPOINT = "/oa21mm/v2/profile/picture/{guid}"

interface ProfilePictureServicesApi {
    @DELETE(PROFILE_PICTURE_ENDPOINT)
    fun deleteProfilePicture(
        @Path("guid") guid: String,
    ): Call<BaseResponse>

    @GET(PROFILE_PICTURE_ENDPOINT)
    fun getProfilePicture(
        @Path("guid") guid: String,
    ): Call<PhotoResponse>

    @Multipart
    @POST(PROFILE_PICTURE_ENDPOINT)
    fun setProfilePicture(
        @Part file: MultipartBody.Part,
        @Path("guid") guid: String,
    ): Call<BaseResponse>
}
