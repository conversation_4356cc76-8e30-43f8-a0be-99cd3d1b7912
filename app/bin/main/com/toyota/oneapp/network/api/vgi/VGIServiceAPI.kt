package com.toyota.oneapp.network.api.vgi

import com.toyota.oneapp.model.lcfs.LCFSCustomerConsentRequest
import com.toyota.oneapp.network.models.BaseResponse
import io.reactivex.Observable
import retrofit2.http.Body
import retrofit2.http.POST

interface VGIServiceAPI {
    @POST("/oneapi/v1/customerconsent")
    fun sendLcfsCustomerConsent(
        @Body lcfsCustomerConsentRequest: LCFSCustomerConsentRequest,
    ): Observable<BaseResponse>
}
