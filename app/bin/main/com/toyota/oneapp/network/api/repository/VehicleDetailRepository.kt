package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.vehicle.VehicleDetailResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.cy17plus.CY17PlusCoroutineServiceApi
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class VehicleDetailRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val cY17PlusCoroutineServiceApi: CY17PlusCoroutineServiceApi,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getVehicleDetail(vehicleVIN: String): Resource<VehicleDetailResponse?> {
            return makeApiCall {
                cY17PlusCoroutineServiceApi.getVehicleDetail(
                    vehicleVIN,
                )
            }
        }
    }
