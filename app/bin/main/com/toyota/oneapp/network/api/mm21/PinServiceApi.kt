package com.toyota.oneapp.network.api.mm21

import com.toyota.oneapp.model.account.PinCheckRequest
import com.toyota.oneapp.model.account.PinRegisterRequest
import com.toyota.oneapp.model.account.PinResponse
import io.reactivex.Observable
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Query

private const val PIN_CHECK = "pinCheck"
private const val PIN_REGISTRATION = "pinRegistration"
private const val TMNA_NATIVE = "/tmna-native"

interface PinServiceApi {
    @Headers("X-OpenIDM-Username: anonymous", "X-OpenIDM-Password: anonymous")
    @POST("openidm/endpoint/pinService")
    fun pinCheck(
        @Query("_action") action: String = PIN_CHECK,
        @Query("realm", encoded = true) realm: String = TMNA_NATIVE,
        @Body request: PinCheckRequest,
    ): Observable<PinResponse?>

    @Headers("X-OpenIDM-Username: anonymous", "X-OpenIDM-Password: anonymous")
    @POST("openidm/endpoint/pinService")
    suspend fun pinReset(
        @Query("_action") action: String = PIN_REGISTRATION,
        @Query("realm", encoded = true) realm: String = TMNA_NATIVE,
        @Body request: PinRegisterRequest,
    ): Response<PinResponse>

    @Headers("X-OpenIDM-Username: anonymous", "X-OpenIDM-Password: anonymous")
    @POST("openidm/endpoint/pinService")
    suspend fun pinCheckValidity(
        @Query("_action") action: String = PIN_CHECK,
        @Query("realm", encoded = true) realm: String = TMNA_NATIVE,
        @Body request: PinCheckRequest,
    ): Response<PinResponse?>
}
