package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.account.PinCheckRequest
import com.toyota.oneapp.model.account.PinRegisterRequest
import com.toyota.oneapp.model.account.PinResponse
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.mm21.PinServiceApi
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class PinRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val pinClient: PinServiceApi,
    ) : BaseRepository(
            errorMessageParser,
            dispatcherProvider.io(),
        ) {
        suspend fun sendPinResetRequest(request: PinRegisterRequest): Resource<PinResponse?> {
            return makeApiCall { pinClient.pinReset(request = request) }
        }

        suspend fun sendPinCheckRequest(request: PinCheckRequest): Resource<PinResponse?> {
            return makeApiCall { pinClient.pinCheckValidity(request = request) }
        }
    }
