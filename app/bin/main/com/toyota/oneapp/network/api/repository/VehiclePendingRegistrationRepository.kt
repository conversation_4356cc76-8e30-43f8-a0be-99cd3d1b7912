package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.account.VehicleRegistrationPendingPayload
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.mm21.VehicleRegistrationPendingApi
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class VehiclePendingRegistrationRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        val preferenceModel: OneAppPreferenceModel,
        private val vehicleRegistrationPendingApi: VehicleRegistrationPendingApi,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getPendingVehicles(): Resource<ApiResponse<List<VehicleRegistrationPendingPayload>>?> {
            return makeApiCall { vehicleRegistrationPendingApi.getPendingVins(preferenceModel.getGuid()) }
        }

        suspend fun completeRegistration(vin: String): Resource<ApiResponse<VehicleRegistrationPendingPayload>?> {
            return makeApiCall {
                vehicleRegistrationPendingApi.updateVehicleRegistrationStatus(
                    preferenceModel.getGuid(),
                    vin,
                )
            }
        }

        suspend fun removeVehicle(vin: String): Resource<ApiResponse<BaseResponse>?> {
            return makeApiCall { vehicleRegistrationPendingApi.removeVin(preferenceModel.getGuid(), vin) }
        }

        fun removeVinAsync(vin: String) {
            GlobalScope.launch { removeVehicle(vin) }
        }

        fun updateVehicleRegistrationStatusAsync(vin: String) {
            GlobalScope.launch { completeRegistration(vin) }
        }
    }
