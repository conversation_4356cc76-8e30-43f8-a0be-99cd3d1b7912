package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.model.poi.Directions
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.mm21.DirectionServicesApi
import toyotaone.commonlib.coroutine.DispatcherProvider
import javax.inject.Inject

class DirectionServiceRepository
    @Inject
    constructor(
        private val directionServicesApi: DirectionServicesApi,
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        suspend fun getDirection(
            origin: String?,
            destination: String?,
        ): Resource<Directions?> {
            return makeApiCall {
                directionServicesApi.getDirectionsBetween(
                    origin,
                    destination,
                    BuildConfig.DIRECTION_API_KEY,
                )
            }
        }
    }
