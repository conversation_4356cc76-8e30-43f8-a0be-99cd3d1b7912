package com.toyota.oneapp.network.api.manager.demo

import io.reactivex.Observable
import io.reactivex.disposables.Disposable
import okhttp3.Request
import okio.Timeout
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class DemoCall<T> constructor(private val observable: Observable<T>) : Call<T> {
    private var disposable: Disposable? = null

    override fun enqueue(callback: Callback<T>) {
        disposable =
            observable.subscribe({
                callback.onResponse(this@DemoCall, Response.success(it))
            }, {
                callback.onFailure(this@DemoCall, it)
            }, {
                disposable?.dispose()
                disposable = null
            })
    }

    override fun isExecuted(): <PERSON><PERSON><PERSON> {
        throw UnsupportedOperationException()
    }

    override fun clone(): Call<T> {
        throw UnsupportedOperationException()
    }

    override fun isCanceled(): Boolean {
        throw UnsupportedOperationException()
    }

    override fun cancel() {
        throw UnsupportedOperationException()
    }

    override fun execute(): Response<T> {
        throw UnsupportedOperationException()
    }

    override fun request(): Request {
        throw UnsupportedOperationException()
    }

    override fun timeout(): Timeout {
        throw UnsupportedOperationException()
    }
}
