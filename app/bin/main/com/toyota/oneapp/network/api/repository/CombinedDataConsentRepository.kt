package com.toyota.oneapp.network.api.repository

import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.model.RemoteUser
import com.toyota.oneapp.model.account.AcknowledgeConsentRequest
import com.toyota.oneapp.model.account.UpdateDataConsentRequest
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsentRequest
import com.toyota.oneapp.model.combineddataconsent.CombinedDataConsentPayload
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.lcfs.LCFSEligibilityResponse
import com.toyota.oneapp.model.subscription.CancellationDataRequest
import com.toyota.oneapp.model.subscription.CancellationDataResponse
import com.toyota.oneapp.model.subscription.PreviewSubscriptionItem
import com.toyota.oneapp.model.subscription.PreviewSubscriptionItemV2
import com.toyota.oneapp.model.subscription.ProductsAmount
import com.toyota.oneapp.model.subscription.ProductsTotalAmount
import com.toyota.oneapp.model.subscription.SubscriptionGetPayload
import com.toyota.oneapp.model.subscription.SubscriptionPreviewDetailV2
import com.toyota.oneapp.model.subscription.TaxAmount
import com.toyota.oneapp.model.vehicle.CapabilityItem
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.cy17plus.CY17PlusCoroutineServiceApi
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import toyotaone.commonlib.coroutine.DispatcherProvider
import java.util.Date
import javax.inject.Inject
import kotlin.collections.ArrayList

class CombinedDataConsentRepository
    @Inject
    constructor(
        errorMessageParser: ErrorMessageParser,
        dispatcherProvider: DispatcherProvider,
        private val cY17PlusCoroutineServiceApi: CY17PlusCoroutineServiceApi,
        private val dateUtil: DateUtil,
    ) : BaseRepository(errorMessageParser, dispatcherProvider.io()) {
        enum class DataConsentFlowType(
            val value: String,
        ) {
            NONE(""),
            BANNER("banner"),
        }

        enum class DataConsentFlag(
            val value: String,
        ) {
            NONE(""),
            LCFS_ELIGIBLE_CONSENT("lcfs"),
            WIFI_CONSENT("wifi"),
            MARKETING("marketing_consent"),
            AMBER_ALERT("amber_alerts"),
        }

        suspend fun getCombinedDataConsent(
            vin: String,
            brand: String,
            gen: String,
            region: String,
            productCodes: List<String>? = null,
            eligibleConsent: String = "",
            flowType: String = "",
        ): Resource<ApiResponse<CombinedDataConsentPayload>?> {
            val headers =
                hashMapOf<String, String>().apply {
                    put("vin", vin)
                    put("x-brand", brand)
                    put("x-region", region)
                    put("region", region)
                    put("generation", gen)
                    put("eligible-consent", eligibleConsent)
                }
            return makeApiCall {
                cY17PlusCoroutineServiceApi.getDataConsent(
                    headers,
                    body =
                        CombineDataConsentRequest(
                            products = emptyList(),
                            productCodes = productCodes ?: emptyList(),
                            flowType = flowType,
                        ),
                )
            }
        }

        suspend fun createSubscription(
            asiCode: String,
            hwtType: String,
            refId: String,
            isPaymentDefault: Boolean,
            paymentToken: String,
            accessToken: String,
            subscriptionGetPayload: SubscriptionGetPayload?,
            capabilities: ArrayList<CapabilityItem>,
            consent: List<ConsentRequestItem>?,
            totalAmount: ProductsTotalAmount,
            productAmount: ProductsAmount,
            taxAmount: TaxAmount,
        ): Resource<BaseResponse?> {
            val list = ArrayList<PreviewSubscriptionItemV2>()
            subscriptionGetPayload?.list?.forEach {
                val item = it.previewSubscriptionItem
                item.productID?.let {
                    val previewSub =
                        PreviewSubscriptionItemV2(
                            autoRenew = item.isAutoRenew,
                            isCPOProduct = item.isCPOProduct,
                            isPPOProduct = item.isPPOProduct,
                            isService = item.isService,
                            packageID = item.packageID,
                        )
                    list.add(previewSub)
                }
            }

            val externalSubscriptions = ArrayList<PreviewSubscriptionItem?>()
            subscriptionGetPayload?.externalSubscriptions?.forEach {
                val subscriptionItem = PreviewSubscriptionItem()
                subscriptionItem.isCPOProduct = it.isCPOProduct
                subscriptionItem.isPPOProduct = it.isPPOProduct
                subscriptionItem.isService = it.isService
                subscriptionItem.packageID = it.packageID
                subscriptionItem.isAutoRenew = it.isAutoRenew
                externalSubscriptions.add(subscriptionItem)
            }

            val dataConsent = DataConsent()
            if (consent != null && consent.isNotEmpty()) {
                dataConsent.apply {
                    can300 = "False"
                    serviceConnect = "False"
                    ubi = "False"
                    dealerContact = "False"
                }
            } else {
                dataConsent.apply {
                    can300 = ToyUtil.toUpperFirstChar(subscriptionGetPayload?.isCan300.toString())
                    serviceConnect =
                        ToyUtil.toUpperFirstChar(
                            subscriptionGetPayload?.isServiceConnect.toString(),
                        )
                    ubi = ToyUtil.toUpperFirstChar(subscriptionGetPayload?.isUBI.toString())
                    dealerContact =
                        ToyUtil.toUpperFirstChar(
                            subscriptionGetPayload?.isDealerContact.toString(),
                        )
                }
            }

            var remoteUser: RemoteUser? = null
            if (ToyUtil.isRemoteCapable(capabilities)) {
                remoteUser = RemoteUser()
                remoteUser.remoteUserGuid = subscriptionGetPayload?.guid
            }

            val subscriptionDetail =
                SubscriptionPreviewDetailV2(
                    accessToken = if (accessToken.isEmpty()) subscriptionGetPayload?.accessToken else accessToken,
                    paymentMethodId = refId,
                    contractID = "",
                    paymentToken = if (paymentToken.isEmpty()) subscriptionGetPayload?.accessToken else paymentToken,
                    waiver = false,
                    setAsDefaultPaymentMethod = isPaymentDefault,
                    isCPOEligible = subscriptionGetPayload?.isCPOEligible ?: false,
                    isPPOEligible = subscriptionGetPayload?.isPPOEligible ?: false,
                    isService = subscriptionGetPayload?.isServiceConnect ?: false,
                    subscriptions = list,
                    externalSubscriptions = externalSubscriptions,
                    dataConsent = dataConsent,
                    remoteUser = remoteUser,
                    productsAmount = productAmount,
                    productsTotalAmount = totalAmount,
                    taxAmount = taxAmount,
                    consents = consent,
                )

            return makeApiCall {
                cY17PlusCoroutineServiceApi.createOrWaiveSubscriptionV2(
                    vin = subscriptionGetPayload?.vin.orEmpty(),
                    brand = subscriptionGetPayload?.vehicleBrand.orEmpty(),
                    region = subscriptionGetPayload?.region.orEmpty(),
                    generation = subscriptionGetPayload?.generation.orEmpty(),
                    asiCode = asiCode,
                    hwType = hwtType,
                    body = subscriptionDetail,
                )
            }
        }

        suspend fun waiveSubscription(
            isWaiver: Boolean,
            asiCode: String,
            hwtType: String,
            subscriptionGetPayload: SubscriptionGetPayload?,
            capabilities: ArrayList<CapabilityItem>,
        ): Resource<BaseResponse?> {
            val list = ArrayList<PreviewSubscriptionItemV2>()
            subscriptionGetPayload?.list?.forEach {
                val item = it.previewSubscriptionItem
                if (item.productID != null && item.type.equals(ToyotaConstants.TRIAL, true)) {
                    val previewSub =
                        PreviewSubscriptionItemV2(
                            autoRenew = item.isAutoRenew,
                            isCPOProduct = item.isCPOProduct,
                            isPPOProduct = item.isPPOProduct,
                            isService = item.isService,
                            packageID = item.packageID,
                        )
                    list.add(previewSub)
                }
            }

            val externalSubscriptions = ArrayList<PreviewSubscriptionItem?>()
            subscriptionGetPayload?.externalSubscriptions?.forEach {
                val subscriptionItem = PreviewSubscriptionItem()
                subscriptionItem.isCPOProduct = it.isCPOProduct
                subscriptionItem.isPPOProduct = it.isPPOProduct
                subscriptionItem.isService = it.isService
                subscriptionItem.packageID = it.packageID
                subscriptionItem.isAutoRenew = it.isAutoRenew
                externalSubscriptions.add(subscriptionItem)
            }

            val dataConsent =
                DataConsent().apply {
                    can300 = ToyUtil.toUpperFirstChar(subscriptionGetPayload?.isCan300.toString())
                    serviceConnect =
                        ToyUtil.toUpperFirstChar(
                            subscriptionGetPayload?.isServiceConnect.toString(),
                        )
                    ubi = ToyUtil.toUpperFirstChar(subscriptionGetPayload?.isUBI.toString())
                    dealerContact =
                        ToyUtil.toUpperFirstChar(
                            subscriptionGetPayload?.isDealerContact.toString(),
                        )
                }

            var remoteUser: RemoteUser? = null
            if (ToyUtil.isRemoteCapable(capabilities)) {
                remoteUser = RemoteUser()
                remoteUser.remoteUserGuid = subscriptionGetPayload?.guid
            }

            val subscriptionDetail =
                SubscriptionPreviewDetailV2(
                    accessToken = subscriptionGetPayload?.accessToken,
                    paymentMethodId = null,
                    contractID = "",
                    paymentToken = null,
                    waiver = isWaiver,
                    setAsDefaultPaymentMethod = false,
                    isCPOEligible = false,
                    isPPOEligible = subscriptionGetPayload?.isPPOEligible,
                    subscriptions = list,
                    externalSubscriptions = externalSubscriptions,
                    dataConsent = dataConsent,
                    remoteUser = remoteUser,
                    productsAmount = null,
                    productsTotalAmount = null,
                    taxAmount = null,
                    consents = null,
                )

            return makeApiCall {
                cY17PlusCoroutineServiceApi.createOrWaiveSubscriptionV2(
                    vin = subscriptionGetPayload?.vin.orEmpty(),
                    brand = subscriptionGetPayload?.vehicleBrand.orEmpty(),
                    region = subscriptionGetPayload?.region.orEmpty(),
                    generation = subscriptionGetPayload?.generation.orEmpty(),
                    asiCode = asiCode,
                    hwType = hwtType,
                    body = subscriptionDetail,
                )
            }
        }

        suspend fun createOrWaive17CYPlusSubscription(
            vin: String,
            brand: String,
            region: String,
            generation: String,
            asiCode: String,
            hwtType: String,
            subscriptionRequest: SubscriptionPreviewDetailV2,
        ): Resource<BaseResponse?> =
            makeApiCall {
                cY17PlusCoroutineServiceApi.createOrWaiveSubscriptionV2(
                    vin = vin,
                    brand = brand,
                    region = region,
                    generation = generation,
                    asiCode = asiCode,
                    hwType = hwtType,
                    body = subscriptionRequest,
                )
            }

        suspend fun updateDataConsents(
            vin: String,
            brand: String,
            region: String,
            generation: String,
            dateTime: Long,
            body: UpdateDataConsentRequest,
        ): Resource<BaseResponse?> =
            makeApiCall {
                cY17PlusCoroutineServiceApi.updateDataConsents(
                    vin = vin,
                    brand = brand,
                    region = region,
                    generation = generation,
                    dateTime = dateTime,
                    body = body,
                )
            }

        suspend fun acknowledgeConsent(
            vin: String,
            brand: String,
            region: String,
            eligibleConsent: String,
            body: AcknowledgeConsentRequest,
        ): Resource<BaseResponse?> =
            makeApiCall {
                cY17PlusCoroutineServiceApi.acknowledgeConsent(
                    vin = vin,
                    brand = brand,
                    region = region,
                    eligibleconsent = eligibleConsent,
                    body = body,
                )
            }

        suspend fun getPreviewRefund(
            guid: String,
            vin: String,
            subscriptionIds: List<String>,
        ): Resource<CancellationDataResponse?> {
            val cancellationDataRequest =
                CancellationDataRequest(
                    orderDate = dateUtil.formatLocalDate(Date()),
                    guid = guid,
                    vin = vin,
                    subscriptionsIds = subscriptionIds,
                )
            return makeApiCall { cY17PlusCoroutineServiceApi.getRefundPreview(cancellationDataRequest) }
        }

        suspend fun getPrivacyPortalDataConsent(
            vin: String,
            brand: String,
            gen: String,
            region: String,
            productCodes: List<String>? = emptyList(),
            flowType: String? = null,
            eligibleConsent: String = "",
            otherEligibleConsent: String = "",
        ): Resource<ApiResponse<CombinedDataConsentPayload>?> {
            val headers =
                hashMapOf<String, String>().apply {
                    put("vin", vin)
                    put("x-brand", brand)
                    put("x-region", region)
                    put("region", region)
                    put("generation", gen)
                    put("eligible-consent", eligibleConsent)
                    put("other-eligible-consents", otherEligibleConsent)
                }

        /*val type = object : TypeToken<ApiResponse<CombinedDataConsentPayload>>() {}.type
        return Resource.Success(data = Gson().fromJson(ppResponse, type))*/

            return makeApiCall {
                cY17PlusCoroutineServiceApi.getDataConsent(
                    headers,
                    body =
                        CombineDataConsentRequest(
                            products = emptyList(),
                            productCodes = productCodes,
                            flowType = flowType,
                        ),
                )
            }
        }

        suspend fun getLCFSEligibility(
            vin: String,
            brand: String,
        ): Resource<LCFSEligibilityResponse?> = makeApiCall { cY17PlusCoroutineServiceApi.getLcfsEligibility(vin, brand) }
    }
