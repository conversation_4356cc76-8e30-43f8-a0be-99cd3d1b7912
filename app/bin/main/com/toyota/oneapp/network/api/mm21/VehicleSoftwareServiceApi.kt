package com.toyota.oneapp.network.api.mm21

import com.toyota.oneapp.model.garage.NotificationRequest20TMPayload
import com.toyota.oneapp.model.garage.SoftwareVersion21MMPayloadResponse
import com.toyota.oneapp.model.garage.SoftwareVersionUpdateRequest
import com.toyota.oneapp.network.models.ApiResponse
import com.toyota.oneapp.network.models.BaseResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

interface VehicleSoftwareServiceApi {
    @GET("/oa21mm/v1/ota/notification")
    suspend fun getSoftware21MMVersion(
        @Header("vin") vin: String,
        @Header("registrationRequestId") registrationId: String? = null,
    ): Response<ApiResponse<List<SoftwareVersion21MMPayloadResponse?>?>?>

    @POST("/oa21mm/v1/ota/update/authorize")
    suspend fun updateSoftwareVersion(
        @Body request: SoftwareVersionUpdateRequest,
    ): Response<BaseResponse>

    @GET("/oa21mm/v1/ota/update/check")
    suspend fun getNotificationResponse(
        @Header("vin") vin: String?,
    ): Response<ApiResponse<NotificationRequest20TMPayload?>?>
}
