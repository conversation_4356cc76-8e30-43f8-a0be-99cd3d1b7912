package com.toyota.oneapp.network.dataproviders

import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.features.pay.tfs.presentation.utils.TFSTokenProviderHelper
import com.toyota.oneapp.network.dataprovider.TFSDataProvider
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

class TFSDataProviderImpl
    @Inject
    constructor(
        private val idpData: IDPData,
        private val tfsTokenHelper: TFSTokenProviderHelper,
    ) : TFSDataProvider {
        override fun tfsIDToken(): String {
            return tfsTokenHelper.getTFSIdToken()
        }

        override fun tfsRefreshToken(): String {
            return tfsTokenHelper.getTFSRefreshToken()
        }

        override fun isNewAccessTokenNeeded(): Boolean {
            return tfsTokenHelper.newTfsIdTokenNeeded()
        }

        override suspend fun getFreshTfsIDToken(): String {
            var freshTfsIdToken = ToyotaConstants.EMPTY_STRING
            withContext(Dispatchers.IO) {
                tfsTokenHelper.getNewTfsIdToken().collect {
                    freshTfsIdToken = it
                }
            }
            return freshTfsIdToken
        }

        override fun idToken(): String? {
            return idpData.idToken
        }
    }
