package com.toyota.oneapp.network.dataproviders

import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.network.dataprovider.BaseUrlProvider
import com.toyota.oneapp.util.Brand
import javax.inject.Inject

class BaseUrlProviderImpl
    @Inject
    constructor() : BaseUrlProvider {
        override fun cdnBaseUrl() = BuildConfig.CDN_BASE_URL

        override fun cdnCertificateHostName() = BuildConfig.CDN_CERTIFICATE_HOST_NAME

        override fun cdnRootCertificateKey() = BuildConfig.CDN_ROOT_CERTIFICATE_KEY

        override fun cdnAmazonRootCertificateKey() = BuildConfig.CDN_AMAZON_ROOT_CERTIFICATE_KEY

        override fun cdnDigiCertificateKey() = BuildConfig.CDN_DIGI_CERTIFICATE_KEY

        override fun sxmTokenExchangeBaseUrl() = BuildConfig.SXM_TOKEN_EXCHANGE_BASE_URL

        override fun idpBaseUrl() = idpQrUrl() + "oauth2/realms/root/realms/tmna/"

        override fun idpQrUrl() = Brand.currentAppBrand().forgerockUrl

        override fun pinBaseUrl() = Brand.currentAppBrand().pinBaseUrl

        override fun googleClient() = BuildConfig.GOOGLE_CLIENT

        override fun dkMopBaseUrl() = BuildConfig.DK_MOP_URL

        override fun dkBaseUrl() = BuildConfig.DK_BASE_URL

        override fun frIDPBaseUrl(): String {
            return if (BuildConfig.APP_BRAND == "T") {
                BuildConfig.forgerock_oauth_url
            } else {
                BuildConfig.forgerock_oauth_lexus_url
            }
        }

        override fun tfsAuthenticateBaseUrl(): String {
            return if (BuildConfig.APP_BRAND == "T") {
                BuildConfig.TFS_API_TOYOTA_AUTHENTICATE_URL
            } else {
                BuildConfig.TFS_API_LEXUS_AUTHENTICATE_URL
            }
        }

        override fun tfsAuthorizeBaseUrl(): String {
            return if (BuildConfig.APP_BRAND == "T") {
                BuildConfig.TFS_API_TOYOTA_AUTHORIZE_URL
            } else {
                BuildConfig.TFS_API_LEXUS_AUTHORIZE_URL
            }
        }

        override fun tfsApiBaseUrl(): String {
            return if (BuildConfig.APP_BRAND == "T") {
                BuildConfig.TFS_API_TOYOTA_URL
            } else {
                BuildConfig.TFS_API_TOYOTA_URL
            }
        }
    }
