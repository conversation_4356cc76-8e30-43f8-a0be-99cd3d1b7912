package com.toyota.oneapp.network.dataproviders

import android.content.Context
import android.os.Build
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.component.IDPHelper
import com.toyota.oneapp.core.ApplicationContext
import com.toyota.oneapp.network.api.CorrelationIdProvider
import com.toyota.oneapp.network.dataprovider.NetworkDataProvider
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.ToyUtil
import org.apache.commons.io.IOUtils
import java.util.Locale
import javax.inject.Inject

class NetworkDataProviderIml
    @Inject
    constructor(
        private val idpData: IDPData,
        @ApplicationContext private val context: Context,
        private val idpHelper: IDPHelper,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val correlationIdProvider: CorrelationIdProvider,
    ) : NetworkDataProvider {
        private val appVersion = BuildConfig.VERSION_NAME.let { it.split("-")[0] }

        override fun apiKey(): String = BuildConfig.API_GATEWAY_KEY

        override fun locale(): String = AppLanguageUtils.getCurrentLocaleString()

        override fun appBrand(): String = BuildConfig.APP_BRAND

        override fun guid(): String = oneAppPreferenceModel.getGuid()

        override fun release(): String = Build.VERSION.RELEASE

        override fun version(): String = appVersion

        override fun correlationID(): String = correlationIdProvider.get()

        override fun accessToken(): String = idpHelper.getAccessToken() ?: ""

        override fun refreshToken(): String? = idpData.refreshToken

        override fun idToken(): String? = idpData.idToken

        override fun attToken(): String? = idpHelper.getLimitedAccessToken()

        override fun hasNetwork(): Boolean = ToyUtil.hasNetwork(context)

        override fun currentlocale(): Locale = AppLanguageUtils.getCurrentLocale()

        override fun rawCertData(): ByteArray = IOUtils.toByteArray(context.resources?.openRawResource(R.raw.stgcio))

        override fun isRefreshTokenValid(interval: Int): Boolean = IDPHelper.isRefreshTokenValid(interval, oneAppPreferenceModel)

        override fun freshAccessToken(): String = idpHelper.getAccessToken(true) ?: ""
    }
