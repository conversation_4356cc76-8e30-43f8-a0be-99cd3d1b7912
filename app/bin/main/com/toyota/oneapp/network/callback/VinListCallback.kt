package com.toyota.oneapp.network.callback

import com.toyota.oneapp.model.vehicle.VehiclelListResponse
import com.toyota.oneapp.network.callback.CommonError.REQUEST_TIMEOUT_ERROR
import com.toyota.oneapp.network.models.ResponseDataClass

/**
 * Created by <PERSON> on 3/16/21.
 */
const val ONE_VL_10001 = "ONE-VL-10001"
const val ONE_VL_10002 = "ONE-VL-10002"

abstract class VinListCallback
    @JvmOverloads
    constructor(
        private val defaultErrorMsg: String? = null,
    ) : BaseCallback<VehiclelListResponse>() {
        override fun onFailError(
            httpCode: Int,
            errorMsg: String?,
        ) {
            super.onFailError(httpCode, errorMsg)
            onVinListFailure(httpCode.toString(), defaultErrorMsg)
        }

        override fun onSuccess(response: VehiclelListResponse) {
            super.onSuccess(response)
            when (response.responseCode) {
                ONE_VL_10001 ->
                    onVinListSuccess(
                        response,
                        response.getDescription(response.responseCode),
                    )
                ONE_VL_10002 ->
                    onVinListFailure(
                        response.responseCode,
                        response.getDescription(response.responseCode) ?: defaultErrorMsg,
                    )
                else -> onVinListSuccess(response, null)
            }
        }

        open fun onVinListRetry(
            httpCode: Int?,
            errorMsg: String?,
        ) {}

        abstract fun onVinListSuccess(
            response: VehiclelListResponse,
            errorMessage: String?,
        )

        abstract fun onVinListFailure(
            responseCode: String?,
            errorMessage: String?,
        )
    }

abstract class VinListRetryCallback(
    private val retryTimes: Int,
    private val callback: BaseCallback<VehiclelListResponse>?,
) : BaseCallback<VehiclelListResponse>() {
    private var shouldThrowOutResult = false

    override fun onSuccess(response: VehiclelListResponse) {
        super.onSuccess(response)
        shouldThrowOutResult = true
        callback?.onSuccess(response)
    }

    override fun onFailError(
        httpCode: Int,
        errorMsg: String?,
    ) {
        super.onFailError(httpCode, errorMsg)
        if (httpCode in 500..599 || (httpCode == REQUEST_TIMEOUT_ERROR)) {
            if (retryTimes > 0) {
                if (callback is VinListCallback) {
                    callback.onVinListRetry(httpCode, errorMsg)
                }
                onRequestRetry(callback)
            } else {
                shouldThrowOutResult = true
                callback?.onFailError(httpCode, errorMsg)
            }
        } else {
            shouldThrowOutResult = true
            callback?.onFailError(httpCode, errorMsg)
        }
    }

    override fun onFailError(
        httpCode: Int,
        errorMsg: String?,
        payload: ResponseDataClass.Payload,
    ) {
        super.onFailError(httpCode, errorMsg, payload)
        if (retryTimes <= 0 || shouldThrowOutResult) {
            callback?.onFailError(httpCode, errorMsg, payload)
        }
    }

    override fun onFailError(
        httpCode: Int,
        errorCode: String?,
        errorMsg: String?,
    ) {
        super.onFailError(httpCode, errorCode, errorMsg)
        if (retryTimes <= 0 || shouldThrowOutResult) {
            callback?.onFailError(httpCode, errorCode, errorMsg)
        }
    }

    override fun onComplete() {
        super.onComplete()
        if (retryTimes <= 0 || shouldThrowOutResult) {
            callback?.onComplete()
        }
    }

    abstract fun onRequestRetry(callback: BaseCallback<VehiclelListResponse>?)
}
