package com.toyota.oneapp.network

import com.toyota.oneapp.network.dataprovider.TFSDataProvider
import com.toyota.oneapp.network.dataproviders.TFSDataProviderImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module
abstract class TFSDataProviderModule {
    @Binds
    abstract fun tfsDataProvider(tfsDataProviderImpl: TFSDataProviderImpl): TFSDataProvider
}
