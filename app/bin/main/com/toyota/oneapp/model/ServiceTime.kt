package com.toyota.oneapp.model

import android.os.Parcelable
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.toCalendar
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.Calendar

@Parcelize
@Deprecated(
    "Don't use this anymore.  It's sketchy, hard to test, and weird with localization. Better solution is to use date object with format strings, which will handle localization elegantly.",
)
data class ServiceTime(
    private val fullDateString: String,
    var isSelected: Boolean = false,
) : Parcelable {
    constructor(fullDateString: String) : this(fullDateString, false)

    companion object {
        private val dateFormat_short =
            SimpleDateFormat(
                "EEE, MMM dd, yyyy",
                AppLanguageUtils.getCurrentLocale(),
            ) // Mon, Oct 17 2018
        private val dateFormat_long_no_year =
            SimpleDateFormat(
                "EEEE, MMMM dd",
                AppLanguageUtils.getCurrentLocale(),
            ) // Monday, October 17 2018
        private val dateFormat_long =
            SimpleDateFormat(
                "EEEE, MMMM dd, yyyy",
                AppLanguageUtils.getCurrentLocale(),
            ) // Monday, October 17 2018
    }

    val calendar = fullDateString.toCalendar()
    val year: String get() = calendar[Calendar.YEAR].toString() + ""
    val month: String? get() =
        calendar.getDisplayName(
            Calendar.MONTH,
            Calendar.LONG,
            AppLanguageUtils.getCurrentLocale(),
        )
    val day: String? get() = calendar[Calendar.DATE].toString() + ""
    val week: String? get() =
        calendar.getDisplayName(
            Calendar.DAY_OF_WEEK,
            Calendar.LONG,
            AppLanguageUtils.getCurrentLocale(),
        )
    val time: String? get() = ToyUtil.getFormattedTime(ToyUtil.getServerDtFormat(), fullDateString)
    val dateStringShort = ToyUtil.getFormattedDate(dateFormat_short, calendar.time)
    val dateStringLong = ToyUtil.getFormattedDate(dateFormat_long, calendar.time)
    val dateStringLongNoYear = ToyUtil.getFormattedDate(dateFormat_long_no_year, calendar.time)
    var originalDate: String = fullDateString

    fun isEmpty(): Boolean = year == null && month == null && day == null && week == null && time == null
}
