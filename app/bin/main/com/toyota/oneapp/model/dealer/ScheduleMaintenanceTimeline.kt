package com.toyota.oneapp.model.dealer

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class ScheduleMaintenanceTimeline(
    @SerializedName("payload") val payload: Payload,
) : Parcelable

@Parcelize
data class Payload(
    @SerializedName("scheduleMaintenanceDetails") val scheduleMaintenanceDetails: ArrayList<ScheduleMaintenanceDetails>,
    @SerializedName("vin") val vin: String,
    @SerializedName("language") val language: String,
    @SerializedName("outputType") val outputType: String,
    @SerializedName("lastKnownMileage") val lastKnownMileage: String,
) : Parcelable

@Parcelize
data class ScheduleMaintenanceDetails(
    @SerializedName("interval") val interval: String,
    @SerializedName("intervalMileage") val intervalMileage: String,
    @SerializedName("mileageUnit") val mileageUnit: String,
    @SerializedName("serviceIntervalTime") val serviceIntervalTime: String,
    @SerializedName("timeUnit") val timeUnit: String,
    @SerializedName("maintenanceTasks") val maintenanceTasks: ArrayList<MaintenanceTasks>,
) : Parcelable

@Parcelize
data class MaintenanceTasks(
    @SerializedName("operatingCondDescription") val operatingCondDescription: String,
    @SerializedName("serviceItemDescription") val serviceItemDescription: String,
) : Parcelable
