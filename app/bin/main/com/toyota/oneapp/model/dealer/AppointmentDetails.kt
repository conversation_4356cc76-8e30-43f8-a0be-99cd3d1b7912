package com.toyota.oneapp.model.dealer

import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.model.ServiceRepair
import com.toyota.oneapp.network.models.BaseResponse

data class AppointmentDetailsResponse(
    @SerializedName("payload") val payload: AppointmentDetails,
) : BaseResponse()

data class AppointmentDetails(
    @SerializedName("dealer") val dealer: DealerInformation,
    @SerializedName("summary") val summary: AppointmentSummary,
    @SerializedName("drs") val drs: Array<ServiceRepair>,
    @SerializedName("frs") val frs: Array<ServiceRepair>,
    @SerializedName("repair") val repairs: Array<ServiceRepair>,
    @SerializedName("customerConcern") val customerConcern: String,
    @SerializedName("advisorName") val advisorName: String,
    @SerializedName("transportationDes") val transportationDes: String,
    @SerializedName("scheduledTime") val scheduledTime: String,
    @SerializedName("mileage") val mileage: Int,
    @SerializedName("mileageUnit") val mileageUnit: String,
    @SerializedName("status") val status: String,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as AppointmentDetails

        if (!drs.contentEquals(other.drs)) return false
        if (!frs.contentEquals(other.frs)) return false
        if (!repairs.contentEquals(other.repairs)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = customerConcern.hashCode()
        result += drs.contentHashCode()
        result += frs.contentHashCode()
        result += repairs.contentHashCode()
        result *= 31
        return result
    }
}
