package com.toyota.oneapp.model.dealer

import android.annotation.SuppressLint
import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.toyota.oneapp.model.Advisor
import com.toyota.oneapp.model.Transport
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.Date

@Parcelize
@SuppressLint("SimpleDateFormat") // This is for the API, not the user.  We do not want to pass locale.
data class BookAppointmentRequest(
    @Expose val services: ServicesRepairsList,
    @Expose val transportOption: Transport? = null,
    @Expose val scheduledTime: String? = null,
    @Expose val advisor: Advisor? = null,
    @Expose val phoneNumber: String? = null,
) : Parcelable {
    constructor(
        services: ServicesRepairsList,
        transportOption: Transport? = null,
        scheduledTimeDate: Date,
        advisor: Advisor? = null,
        phoneNumber: String? = null,
    ) : this(
        services,
        transportOption,
        SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSXXX").format(scheduledTimeDate),
        advisor,
        phoneNumber,
    )
}
