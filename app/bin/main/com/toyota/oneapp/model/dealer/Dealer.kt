package com.toyota.oneapp.model.dealer

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@Parcelize
data class Dealer(
    var lat: Double? = null,
    var longi: Double? = null,
    var accessibilityOptions: @RawValue List<Any?>? = null,
    var addresses: ArrayList<Addresses?>? = null,
    var amenities: ArrayList<String?>? = null,
    var brand: String? = null,
    var businessHours: ArrayList<BusinessHour?>? = null,
    var dealerId: String? = null,
    var dealershipName: String? = null,
    var emails: ArrayList<String?>? = null,
    var paymentOptions: ArrayList<String?>? = null,
    var phoneNumbers: ArrayList<PhoneNumber?>? = null,
    var region: String? = null,
    var services: @RawValue ArrayList<Any?>? = null,
    var isSmartPath: Boolean? = null,
    var toyotaCode: String? = null,
    var transportationOptions: @RawValue ArrayList<Any?>? = null,
    var website: String? = null,
    var distance: Float? = null,
    var distanceUnit: String? = null,
) : Parcelable

@Parcelize
data class Addresses(
    var city: String? = null,
    var coordinate: Coordinate? = Coordinate(),
    var country: String? = null,
    var line1: String? = null,
    var line2: String? = null,
    var state: String? = null,
    var timeZone: String? = null,
    var zipCode: String? = null,
) : Parcelable

@Parcelize
data class Coordinate(var latitude: Double = 0.0, var longitude: Double = 0.0) : Parcelable

@Parcelize
data class BusinessHour(
    var closingTime: String?,
    var dayOfWeek: String?,
    var openingTime: String?,
) : Parcelable

@Parcelize
data class PhoneNumber(
    var countryCode: String? = null,
    var label: String? = null,
    var number: String? = null,
) : Parcelable
