package com.toyota.oneapp.model.dealer

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.toyota.oneapp.model.RepairServicesPayload
import com.toyota.oneapp.model.ServiceRepair
import kotlinx.parcelize.Parcelize

@Parcelize
data class ServicesRepairsList(
    @Expose var drs: MutableList<ServiceRepair> = mutableListOf(),
    @Expose var frs: MutableList<ServiceRepair> = mutableListOf(),
    @Expose var grs: MutableList<ServiceRepair> = mutableListOf(),
) : Parcelable {
    constructor (repairServicesPayload: RepairServicesPayload) : this(
        repairServicesPayload.drs,
        repairServicesPayload.frs,
        repairServicesPayload.grs,
    )

    val isEmpty: Boolean get() = drs.isEmpty() && frs.isEmpty() && grs.isEmpty()

    val repairsAndServicesString: String get() = allServiceRepairs.joinToString(separator = "\n") { it.name }

    val allServiceRepairs: List<ServiceRepair> get() = drs + frs + grs

    /**
     * Makes a copy of this object except it only includes services that are selected.
     */
    fun filterToOnlySelected(): ServicesRepairsList =
        ServicesRepairsList(
            drs.filter { it.selected }.toMutableList(),
            frs.filter { it.selected }.toMutableList(),
            grs.filter { it.selected }.toMutableList(),
        )

    operator fun get(index: Int): ServiceRepair = allServiceRepairs[index]
}
