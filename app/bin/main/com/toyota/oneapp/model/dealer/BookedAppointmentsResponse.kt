package com.toyota.oneapp.model.dealer

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.model.ServiceRepair
import kotlinx.parcelize.Parcelize
import kotlin.collections.ArrayList

@Parcelize
data class BookedAppointmentsResponse(
    val payload: ArrayList<BookedAppointment>,
) : Parcelable

@Parcelize
data class BookAppointmentResponse(
    @SerializedName("payload") val payload: BookAppointment,
) : Parcelable

@Parcelize
data class BookAppointment(
    @SerializedName("scheduleTime") val scheduleTime: String,
    @SerializedName("appointmentId") val appointmentId: String,
    @SerializedName("departmentId") val departmentId: String,
    @SerializedName("confirmationCode") val confirmationCode: String,
    @SerializedName("summary") val summary: AppointmentSummary,
) : Parcelable

@Parcelize
data class AppointmentSummary(
    @SerializedName("taxes") val taxes: Double,
    @SerializedName("taxesGt") val taxesGt: Int,
    @SerializedName("total") val total: Double,
    @SerializedName("totalLabourHours") val totalLabourHours: Double,
) : Parcelable

@Parcelize
data class BookedAppointment(
    val guid: String?,
    val vin: String?,
    val appointmentId: String?,
    val selectedServices: ArrayList<ServiceRepair> = arrayListOf(),
    val transportType: String?,
    val advisorName: String?,
    val scheduledTime: String?,
    val mileage: Int?,
    val dealer: DealerInformation,
) : Parcelable

@Parcelize
data class DealerInformation(
    val providerDealerCode: String,
    val dealerName: String,
    val address: String,
    val city: String,
    val state: String,
    val country: String,
    val zip: String,
    val phone: String,
    val latitude: Double = 0.0,
    val longitude: Double = 0.0,
) : Parcelable {
    fun getFullAddress(): String = "$address $city, $state $zip"
}
