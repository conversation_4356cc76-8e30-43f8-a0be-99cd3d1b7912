package com.toyota.oneapp.model.dealer

import android.os.Parcelable
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.parcelize.Parcelize

@Parcelize
data class DealerIdResponse(val payload: DealerId) : Parcelable, BaseResponse()

@Parcelize
data class DealerId(
    val providerDealerCode: String?,
    val isPhoneNumberRequired: Boolean = false,
    val isDealerAvailable: Boolean = false,
    val phoneNumber: String? = "",
) : Parcelable
