package com.toyota.oneapp.model.subscriptionV2

import android.os.Parcelable
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Model - used to hold Available Subscription.
 */
@Parcelize
data class AvailableSubscription(
    val productName: String,
    val productCode: String?,
    val productLine: String?,
    val displayTerm: String? = ToyotaConstants.EMPTY_STRING,
    val description: String? = ToyotaConstants.EMPTY_STRING,
    val productImageUrl: String?,
    val productLongDesc: String?,
    val formattedProductDesc: String?,
    val displayProductName: String,
    val renewable: Boolean?,
    val available: Boolean?,
    val availabilityCode: String?,
    val externalProduct: Boolean,
    val externalTargetUrl: String?,
    val externalContactDetail: String?,
    val packages: List<SubscriptionPackage>,
    val category: String?,
    val components: List<BundleComponent>?,
) : Parcelable

@Parcelize
data class SubscriptionPackage(
    val isSubscriptionBundle: Boolean,
    val productID: String?,
    val productCode: String?,
    val packageID: String?,
    val ratePlanID: String?,
    val term: Int?,
    val termUnit: String?,
    val subscriptionTerm: String?,
    val displaySubscriptionTerm: String?,
    val subscriptionStartDate: String?,
    val subscriptionEndDate: String?,
    val type: String,
    val price: Double?,
    val discount: Double?,
    val currency: String?,
    val displayOneTime: String?,
    val displayOneTimeDtl: String?,
) : Parcelable

fun SubscriptionPackage.type(): SubscriptionType =
    if (type == "Paid") {
        SubscriptionType.PAID
    } else {
        SubscriptionType.TRIAL
    }

fun SubscriptionPackage.subscriptionStartDate(dateUtil: DateUtil): Date? = subscriptionStartDate?.let { dateUtil.parseLocalDate(it) }

fun AvailableSubscription.productDescription(): String =
    if (formattedProductDesc.isNullOrEmpty()) {
        productLongDesc.orEmpty()
    } else {
        formattedProductDesc
    }
