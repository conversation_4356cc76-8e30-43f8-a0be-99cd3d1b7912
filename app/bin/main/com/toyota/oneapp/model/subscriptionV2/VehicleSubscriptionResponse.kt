package com.toyota.oneapp.model.subscriptionV2

import android.os.Parcelable
import com.toyota.oneapp.features.vehicleswitcher.domain.model.Subscription
import com.toyota.oneapp.model.subscription.VehicleSubscriptionAlert
import kotlinx.parcelize.Parcelize

/**
 * A Model used to hold Vehicle Subscription response.
 */
@Parcelize
data class VehicleSubscriptionResponse(
    val payload: VehicleSubscriptionPayload,
) : Parcelable

@Parcelize
data class VehicleSubscriptionPayload(
    val availableSubscriptions: List<AvailableSubscription>,
    val trialSubscriptions: List<SubscriptionV2>,
    val paidSubscriptions: List<SubscriptionV2>,
    val accessToken: String?,
    val isAzure: Boolean? = true,
    val isCPOEligible: Boolean? = true,
    val isPPOEligible: Boolean? = false,
    val ppoDisclaimer: String?,
    val ppoCancelDisclaimer: String?,
    val taxDisclaimer: String?,
    val isPaidEnabled: Boolean? = true,
    val isAppUpdateRequired: Boolean? = false,
    val alerts: List<VehicleSubscriptionAlert>?,
) : Parcelable

fun VehicleSubscriptionResponse.toUIModel(): Subscription {
    return Subscription(
        paidSubscriptions = payload.paidSubscriptions,
        trialSubscriptions = payload.trialSubscriptions,
    )
}
