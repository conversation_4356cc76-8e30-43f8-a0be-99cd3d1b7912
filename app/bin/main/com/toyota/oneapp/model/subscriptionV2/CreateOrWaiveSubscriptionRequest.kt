package com.toyota.oneapp.model.subscriptionV2

import android.os.Parcelable
import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import com.toyota.oneapp.model.subscription.ProductsAmount
import com.toyota.oneapp.model.subscription.ProductsTotalAmount
import com.toyota.oneapp.model.subscription.TaxAmount
import kotlinx.parcelize.Parcelize

/**
 *  Create subscription API request model class
 */
class CreateOrWaiveSubscriptionRequest(
    var consents: List<ConsentRequestItem>?,
    var subscriptions: List<Subscriptions>?,
    var waiver: Boolean,
    var productsAmount: ProductsAmount?,
    var taxAmount: TaxAmount?,
    var productsTotalAmount: ProductsTotalAmount?,
    var remoteUser: RemoteUser,
    var setAsDefaultPaymentMethod: Boolean,
    var paymentMethodId: String?,
    var accessToken: String?,
    var paymentToken: String?,
    var externalSubscriptions: List<ExternalSubscriptions>?,
    var dataConsent: DataConsent,
) {
    @Parcelize
    data class RemoteUser(var remoteUserGuid: String) : Parcelable

    @Parcelize
    data class Subscriptions(var packageID: String, var autoRenew: Boolean) : Parcelable

    @Parcelize
    data class ExternalSubscriptions(
        var subscriptionStartDate: String,
        var displayTerm: String,
        var available: Boolean,
        var productCode: String,
        var productName: String,
        var renewable: Boolean,
        var termUnit: String,
        var description: String,
        var subscriptionEndDate: String,
        var type: String,
        var term: Int,
    ) : Parcelable
}
