package com.toyota.oneapp.model.subscriptionV2

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Model used to hold Subscription.
 */
@Parcelize
data class SubscriptionV2(
    val subscriptionID: String?,
    val productName: String,
    val productDescription: String?,
    val displayProductName: String,
    val subscriptionEndDate: String?,
    val subscriptionNextBillingDate: String?,
    val subscriptionStartDate: String?,
    val status: String?,
    val type: String?,
    val subscriptionRemainingDays: Int?,
    val productCode: String?,
    val productLine: String?,
    val productType: String?,
    val productID: String?,
    val ratePlanID: String?,
    val term: Int,
    val termUnit: String?,
    val renewable: Boolean,
    val displayTerm: String,
    val subscriptionTerm: String?,
    val displaySubscriptionTerm: String?,
    val autoRenew: Boolean?,
    val futureCancel: Boolean?,
    val consolidatedProductIds: List<String>?,
    val consolidatedGoodwillIds: List<String>?,
    val productImageUrl: String?,
    val productLongDesc: String?,
    val formattedProductDesc: String?,
    val isExpiringSoon: Boolean?,
    val externalProduct: Boolean,
    val externalTargetUrl: String?,
    val externalContactDetail: String?,
    val currency: String?,
    val price: Double?,
    val packageID: String?,
    val hideSubscriptionStatus: Boolean?,
    val isCPOProduct: Boolean?,
    val isPPOProduct: Boolean?,
    val ppoDescription: String?,
    val category: String?,
    val components: List<BundleComponent>?,
    val isService: Boolean?,
) : Parcelable

enum class SubscriptionType {
    PAID,
    TRIAL,
}

fun SubscriptionV2.type(): SubscriptionType {
    return if (type == "Paid") {
        SubscriptionType.PAID
    } else {
        SubscriptionType.TRIAL
    }
}

fun SubscriptionV2.isActive(): Boolean {
    return status == "ACTIVE"
}

fun SubscriptionV2.isInActive(): Boolean {
    return status == "INACTIVE"
}

fun SubscriptionV2.productDescription(): String {
    return if (formattedProductDesc.isNullOrEmpty()) {
        productLongDesc.orEmpty()
    } else {
        formattedProductDesc
    }
}
