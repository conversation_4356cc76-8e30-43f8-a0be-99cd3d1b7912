package com.toyota.oneapp.model.account

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class SXMAccountUpdateRequest(
    @SerializedName("isNameChange") val isNameChange: <PERSON><PERSON><PERSON>,
    @SerializedName("primarySubscriber") val primarySubscriber: SXMPrimarySubscriber,
) : Parcelable

@Parcelize
data class SXMPrimarySubscriber(
    @SerializedName("firstName") val firstName: String,
    @SerializedName("lastName") val lastName: String,
    @SerializedName("language") val language: String,
    @SerializedName("contactPoints") val contactPoints: ContactPoints,
) : Parcelable

@Parcelize
data class ContactPoints(
    @SerializedName("emails") val emails: ArrayList<CustomerEmails>,
    @SerializedName("addresses") val addresses: ArrayList<Addresses>,
) : Parcelable

@Parcelize
data class CustomerEmails(
    @SerializedName("name") val name: String,
    @SerializedName("type") val type: String,
    @SerializedName("value") val value: String?,
) : Parcelable

@Parcelize
data class Addresses(
    @SerializedName("name") val name: String,
    @SerializedName("type") val type: String,
    @SerializedName("street") val street: String,
    @SerializedName("addressLine2") val addressLine2: String,
    @SerializedName("city") val city: String,
    @SerializedName("state") val state: String,
    @SerializedName("postalCode") val postalCode: String,
    @SerializedName("country") val country: String,
) : Parcelable

@Parcelize
data class IDPTokenExchangeRequest(
    @SerializedName("grant_type") val grantType: String,
    @SerializedName("response_type") val responseType: String,
    @SerializedName("client_id") val clientId: String,
    @SerializedName("redirect_uri") val redirectUri: String,
    @SerializedName("scope") val scope: String,
    @SerializedName("username") val userName: String,
    @SerializedName("password") val password: String,
) : Parcelable
