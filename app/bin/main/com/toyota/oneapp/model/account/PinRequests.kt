package com.toyota.oneapp.model.account

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.parcelize.Parcelize

@Parcelize
data class PinRegisterRequest(
    @SerializedName("pin") val pin: String,
    @SerializedName("guid") val guid: String,
) : Parcelable

@Parcelize
data class PinCheckRequest(
    @SerializedName("guid") val guid: String,
) : Parcelable

@Parcelize
data class PinResponse(
    @SerializedName("result") val result: Boolean,
) : BaseResponse(), Parcelable
