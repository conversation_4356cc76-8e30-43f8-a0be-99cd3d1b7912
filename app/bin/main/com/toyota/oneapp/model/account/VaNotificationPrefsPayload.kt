package com.toyota.oneapp.model.account

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class VaNotificationPrefsPayload(
    @SerializedName("vaNotificationsEnabled") val vaNotificationsEnabled: Boolean,
    @SerializedName("navigationNotificationEnabled") val navigationNotificationEnabled: Boolean? = null,
    @SerializedName("mntAndFuelNotificationEnabled") val mntAndFuelNotificationEnabled: Boolean? = null,
    @SerializedName("weatherNotificationEnabled") val weatherNotificationEnabled: Boolean? = null,
) : Parcelable
