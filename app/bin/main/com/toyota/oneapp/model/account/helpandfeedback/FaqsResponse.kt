package com.toyota.oneapp.model.account.helpandfeedback

import com.google.gson.annotations.SerializedName

/**
 * A File contains response models for GetFaqs API.
 */

data class FaqsResponse(
    val payload: FaqsPayload?,
)

data class FaqsPayload(
    val faqs: List<Faq>,
)

data class Faq(
    val question: String,
    val url: String,
    val firebaseEvent: String,
    val category: Category,
) {
    enum class Category(value: String) {
        @SerializedName("POPULAR_FAQ")
        POPULAR("POPULAR_FAQ"),

        @SerializedName("ADDITIONAL_FAQ")
        ADDITIONAL("ADDITIONAL_FAQ"),
    }
}
