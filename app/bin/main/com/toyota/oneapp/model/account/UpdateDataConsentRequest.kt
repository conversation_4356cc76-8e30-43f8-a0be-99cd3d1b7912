package com.toyota.oneapp.model.account

import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem

class UpdateDataConsentRequest(
    val vin: String,
    val subscriberGuid: String,
    var dataConsent: DataConsent,
    var consents: List<ConsentRequestItem>? = null,
)

class AcknowledgeConsentRequest(
    val vin: String,
    val guid: String,
    val eventType: String? = null,
    var consents: List<ConsentRequestItem>? = null,
)
