package com.toyota.oneapp.model.account.helpandfeedback

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class FeedbackMetadataResponse(
    val payload: FeedbackMetadataPayload,
)

data class FeedbackMetadataPayload(
    val categories: List<FeedbackCategory>,
    val feedbackTypes: List<FeedbackType>,
)

@Parcelize
data class FeedbackCategory(
    val categoryName: String,
    val displayName: String,
    val displayDescription: String,
) : Parcelable

@Parcelize
data class FeedbackType(
    val feedbackType: String,
    val displayName: String,
    val displayDescription: String,
) : Parcelable
