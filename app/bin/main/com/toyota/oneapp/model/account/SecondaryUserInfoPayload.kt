package com.toyota.oneapp.model.account

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class SecondaryUserInfoPayload(
    @SerializedName("customer") val secondaryUserInfo: SecondaryUserInfo,
) : Parcelable

@Parcelize
data class SecondaryUserInfo(
    @SerializedName("firstName") val firstName: String,
    @SerializedName("lastName") val lastName: String,
) : Parcelable
