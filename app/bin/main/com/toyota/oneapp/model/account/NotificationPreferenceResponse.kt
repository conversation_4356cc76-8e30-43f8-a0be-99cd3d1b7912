package com.toyota.oneapp.model.account

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

data class NotificationPreferenceResponse(
    @SerializedName("payload") var payload: ArrayList<NotificationPreference>,
)

@Parcelize
data class NotificationPreference(
    @SerializedName("guid") val guid: String,
    @SerializedName("capability") val capability: String,
    @SerializedName("name") val name: String,
    @SerializedName("subtitle") val subtitle: String,
    @SerializedName("pushEnabled") var pushEnabled: <PERSON><PERSON><PERSON>,
    @SerializedName("smsEnabled") var smsEnabled: Boolean,
    @SerializedName("emailEnabled") var emailEnabled: Boolean,
    @SerializedName("automatedCallEnabled") var callEnabled: Boolean,
    @SerializedName("pushAvailable") var pushAvailable: <PERSON><PERSON><PERSON>,
    @SerializedName("emailAvailable") var emailAvailable: Boolean,
    @SerializedName("smsAvailable") var smsAvailable: Boolean,
    @SerializedName("automatedCallAvailable") var callAvailable: Boolean,
    @SerializedName("alertPreferences") val alertPreferences: List<AlertPreference>?,
) : Parcelable {
    fun isAvailable() = emailAvailable || pushAvailable || smsAvailable

    fun isSomethingEnabled() = emailEnabled || pushEnabled || smsEnabled
}

@Parcelize
data class AlertPreference(
    @SerializedName("preference") val preference: String,
    @SerializedName("preferenceName") val preferenceName: String,
    @SerializedName("available") var available: Boolean,
    @SerializedName("enabled") var enabled: Boolean,
) : Parcelable
