package com.toyota.oneapp.model.account

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class NotificationPreferenceRequest(
    @SerializedName("guid") val guid: String,
    @SerializedName("capability") val capability: String,
    @SerializedName("name") val name: String,
    @SerializedName("subtitle") val subtitle: String,
    @SerializedName("pushEnabled") val pushEnabled: String,
    @SerializedName("smsEnabled") val smsEnabled: String,
    @SerializedName("emailEnabled") val emailEnabled: String,
    @SerializedName("pushAvailable") val pushAvailable: String,
    @SerializedName("emailAvailable") val emailAvailable: String,
    @SerializedName("smsAvailable") val smsAvailable: String,
) : Parcelable
