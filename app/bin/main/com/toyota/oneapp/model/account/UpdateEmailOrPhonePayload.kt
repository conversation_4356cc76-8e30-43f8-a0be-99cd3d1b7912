package com.toyota.oneapp.model.account

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class UpdateEmailOrPhonePayload(val customer: CustomerUpdate) : Parcelable

@Parcelize
class CustomerUpdate(
    val objectId: String?,
    val guid: String,
    val emails: List<CustomerEmail>? = null,
    val phoneNumbers: List<CustomerPhoneNumber>? = null,
    val verificationCode: String? = null,
) : Parcelable
