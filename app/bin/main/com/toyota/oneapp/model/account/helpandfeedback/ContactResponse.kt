package com.toyota.oneapp.model.account.helpandfeedback

import com.google.gson.annotations.SerializedName

data class ContactResponse(
    val payload: ContactPayload?,
)

data class ContactPayload(
    val response: List<ContactOption>?,
)

data class ContactOption(
    val title: String?,
    val description: String?,
    val type: ContactMode,
    val targetValue: String?,
    val firebaseEvent: String?,
)

enum class ContactMode(val value: String) {
    @SerializedName("0")
    EMAIL("0"),

    @SerializedName("1")
    PHONE("1"),
}
