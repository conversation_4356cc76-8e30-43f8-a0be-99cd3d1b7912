package com.toyota.oneapp.model.account

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class SXMAccountUpdateResponse(
    @SerializedName("payload") val payload: SXMAccountUpdatePayload,
) : Parcelable

@Parcelize
data class SXMAccountUpdatePayload(
    @SerializedName("id") val id: String,
    @SerializedName("status") val status: String,
    @SerializedName("type") val type: String,
    @SerializedName("primarySubscriber") val primarySubscriber: SXMPrimarySubscriber,
) : Parcelable
