package com.toyota.oneapp.model.account

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Created by <PERSON> on 2018/12/3.
 */
@Parcelize
data class AccountUpdateRequest(val customer: AccountUpdateCustomer) : Parcelable

@Parcelize
data class AccountUpdateCustomer(
    var guid: String,
    var objectId: String,
    var firstName: String,
    var lastName: String,
    var addresses: Array<CustomerAddress>? = arrayOf(),
) : Parcelable {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as AccountUpdateCustomer

        if (guid != other.guid) return false
        if (objectId != other.objectId) return false
        if (firstName != other.firstName) return false
        if (lastName != other.lastName) return false
        if (!addresses.contentEquals(other.addresses)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = guid.hashCode()
        result = 31 * result + objectId.hashCode()
        result = 31 * result + firstName.hashCode()
        result = 31 * result + lastName.hashCode()
        result = 31 * result + addresses.contentHashCode()
        return result
    }
}

@Parcelize
data class CustomerAddress(
    var addressType: String,
    var address: String?,
    var city: String?,
    var state: String?,
    var zipCode: String?,
    var country: String?,
) : Parcelable

@Parcelize
data class CustomerEmail(
    var emailAddress: String,
    var emailType: String,
    var emailVerified: Boolean,
) : Parcelable

@Parcelize
data class CustomerPhoneNumber(
    var phoneNumber: String?,
    val countryCode: String?,
    var phoneType: String?,
    var phoneVerified: Boolean,
) : Parcelable
