package com.toyota.oneapp.model.poi

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.parcelize.Parcelize

@Parcelize
data class Address(
    var street: String = ToyotaConstants.EMPTY_STRING,
    @SerializedName("house_number")
    var houseNumber: String = ToyotaConstants.EMPTY_STRING,
    var city: String = ToyotaConstants.EMPTY_STRING,
    @SerializedName("postal_code")
    var postalCode: String = ToyotaConstants.EMPTY_STRING,
    @SerializedName("country_short")
    var countryShort: String = ToyotaConstants.EMPTY_STRING,
    @SerializedName("admin_region")
    var adminRegion: String = ToyotaConstants.EMPTY_STRING,
    @SerializedName("admin_region_short")
    var adminRegionShort: String = ToyotaConstants.EMPTY_STRING,
) : Parcelable

@Parcelize
data class Coordinates(
    var latitude: Double = 0.0,
    var longitude: Double = 0.0,
) : Parcelable

@Parcelize
data class SendToCarLocation(
    var latitude: Double = 0.0,
    var longitude: Double = 0.0,
    var source: String = ToyotaConstants.EMPTY_STRING,
    var name: String = ToyotaConstants.EMPTY_STRING,
    @SerializedName("location")
    var locCoordinate: Coordinates = Coordinates(),
    var formattedAddress: String = ToyotaConstants.EMPTY_STRING,
    var address: Address = Address(),
    @SerializedName("location_type")
    var locationType: Int = 0,
    @SerializedName("routing")
    var routingCoordinates: Coordinates = Coordinates(),
    @SerializedName("place_id")
    var placeId: String = ToyotaConstants.EMPTY_STRING,
    @SerializedName("phone_number")
    var phoneNumber: String = ToyotaConstants.EMPTY_STRING,
) : Parcelable
