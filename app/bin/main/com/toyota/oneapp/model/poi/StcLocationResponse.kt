package com.toyota.oneapp.model.poi

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.parcelize.Parcelize

@Parcelize
data class StcLocationResponse(
    @SerializedName("id")
    val id: String? = ToyotaConstants.EMPTY_STRING,
    @SerializedName("location")
    val location: LocationDetails? = LocationDetails(),
    @SerializedName("createdAt")
    val createdAt: String? = ToyotaConstants.EMPTY_STRING,
) : Parcelable
