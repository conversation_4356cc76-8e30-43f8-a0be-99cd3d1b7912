package com.toyota.oneapp.model.poi

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class SharePOIResponse(
    val source: String,
    val name: String?,
    val intersection: String?,
    val location: GeoLocation,
    val routing: GeoLocation,
    @SerializedName("place_id")
    val placeId: String,
    @SerializedName("location_type")
    val locationType: Int,
    @SerializedName("phone_number")
    val phoneNumber: String?,
) : Parcelable {
    @IgnoredOnParcel
    var formattedAddress: String? = null
    var address: Address? = null

    fun toLocationDetails(): LocationDetails {
        return LocationDetails(
            formattedAddress,
            Coordinates(location.latitude, location.longitude),
            name,
            address,
            placeId,
            Coordinates(location.latitude, location.longitude),
            intersection,
            locationType,
            phoneNumber,
        )
    }
}

@Parcelize
data class GeoLocation(
    val latitude: Double,
    val longitude: Double,
) : Parcelable
