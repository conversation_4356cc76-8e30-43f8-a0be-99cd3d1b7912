package com.toyota.oneapp.model.poi

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

data class SharePOIRequest(
    val source: String,
    val text: String,
)

@Parcelize
data class SendPOIToCarRequest(
    var guid: String = "",
    @SerializedName("poi_name")
    var poiName: String = "",
    var location: SendToCarLocation = SendToCarLocation(),
) : Parcelable
