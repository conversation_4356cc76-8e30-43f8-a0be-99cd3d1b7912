package com.toyota.oneapp.model.poi

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.parcelize.Parcelize

@Parcelize
data class LocationDetails(
    var formattedAddress: String? = ToyotaConstants.EMPTY_STRING,
    @SerializedName("location")
    var locCoordinate: Coordinates? = Coordinates(),
    var name: String? = ToyotaConstants.EMPTY_STRING,
    var address: Address? = Address(),
    @SerializedName("place_id")
    var placeId: String? = ToyotaConstants.EMPTY_STRING,
    @SerializedName("routing")
    var routCoordinate: Coordinates = Coordinates(),
    var intersection: String? = ToyotaConstants.EMPTY_STRING,
    var location_type: Int? = 0,
    @SerializedName("phone_number")
    var phoneNumber: String? = ToyotaConstants.EMPTY_STRING,
    @SerializedName("refresh_date")
    var refreshDate: Long? = 0L,
    @SerializedName("timestamp")
    var timeStamp: Long? = 0L,
) : Parcelable {
    fun getAddressLineOne(): String {
        return "${address?.houseNumber} ${address?.street}"
    }

    fun getAddressLineTwo(): String {
        return "${address?.city} ${address?.adminRegionShort} ${address?.postalCode}"
    }
}

fun LocationDetails.exists(): Boolean {
    return (
        locCoordinate?.latitude != null &&
            locCoordinate?.longitude != null &&
            locCoordinate?.latitude != 0.0 &&
            locCoordinate?.longitude != 0.0
    )
}

fun LocationDetails.isSameAs(location: LocationDetails?): Boolean {
    return location?.let {
        locCoordinate?.latitude == location.locCoordinate?.latitude &&
            locCoordinate?.longitude == location.locCoordinate?.longitude
    } ?: false
}
