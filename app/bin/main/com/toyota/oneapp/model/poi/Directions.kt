package com.toyota.oneapp.model.poi

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Directions(
    @SerializedName("status") var status: String?,
    @SerializedName("routes") var routes: List<Routes>,
) : Parcelable

@Parcelize
data class Routes(
    @SerializedName("legs") var legs: List<Legs>,
) : Parcelable

@Parcelize
data class Legs(
    @SerializedName("distance") var distance: Distance,
    @SerializedName("duration") var duration: Distance,
) : Parcelable

@Parcelize
data class Distance(
    @SerializedName("text") var text: String,
    @SerializedName("value") var value: Int,
) : Parcelable
