package com.toyota.oneapp.model.lcfs

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.parcelize.Parcelize

@Parcelize
data class LCFSEligibilityResponse(val payload: LCFSPayload?) : BaseResponse(), Parcelable {
    @Parcelize
    data class LCFSPayload(
        @SerializedName("vin")
        val vin: String? = null,
        @SerializedName("userGuid")
        val userGuid: String? = null,
        @SerializedName("lcfsEligible")
        val lcfsEligible: Boolean? = false,
        @SerializedName("showLcfsBanner")
        val showLcfsBanner: Boolean? = false,
        @SerializedName("lcfsOptIn")
        val lcfsOptIn: OptInStatus? = null,
        @SerializedName("v1gEligible")
        val v1gEligible: String? = null,
        @SerializedName("showV1gBanner")
        val showV1gBanner: String? = null,
        @SerializedName("v1gOptIn")
        val v1gOptIn: String? = null,
        @SerializedName("lcfsImg")
        val lcfsImg: String? = null,
        @SerializedName("v1gImg")
        val v1gImg: String? = null,
    ) : Parcelable

    enum class OptInStatus {
        @SerializedName("In")
        In,

        @SerializedName("Out")
        Out,
    }
}
