package com.toyota.oneapp.model.combineddataconsent

import android.content.Context
import android.os.Parcelable
import android.text.SpannableString
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.R
import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.util.DateUtil
import com.toyota.oneapp.util.ToyotaConstants.Companion.CONSENT_ACCEPTED
import com.toyota.oneapp.util.ToyotaConstants.Companion.CONSENT_DECLINED
import com.toyota.oneapp.util.ToyotaConstants.Companion.TRUE
import kotlinx.parcelize.Parcelize
import java.util.Date

data class CombinedDataConsentPayload(
    var eligibleConsents: List<CombineDataConsent>?,
    var acknowledgedConsents: List<CombineDataConsent>?,
    var privacyConsents: PrivacyConsents?,
    var dataConsent: DataConsent?,
)

@Parcelize
data class CombineDataConsent(
    var consentId: String,
    var editable: String?,
    var language: String?,
    var name: String?,
    var version: String?,
    var category: String?,
    var consentStatus: String?,
    var description: DataConsentDescription?,
    var consentAcknowledgedDate: String?,
    var consentAcknowledgedDateInUTC: Date?,
    var masterConsent: Boolean = false,
    var versionId: String?,
    var status: String?,
    var showButtons: Boolean = true,
    var consentEditable: Boolean = true,
    var masterConsentUnEditable: Boolean = false,
    var detailSection: DetailSection? = null,
    var manageSection: ManageSection? = null,
    var imageUrl: String? = "",
    var manageServiceButtonEnabled: Boolean = false,
    var manageServiceText: String? = "",
    var descriptionTex: String?,
    @SerializedName("animationUrl")
    var videoUrl: String?,
    var consentType: String?,
    var alreadyAcknowledged: Boolean? = false,
    var declineMasterConsentEligible: Boolean? = false,
) : CombinedConsentBaseItem(),
    Parcelable {
    companion object {
        const val CATEGORY_TELEMATICS_CONSENT = "TELEMATICS_CONSENT"
        const val CONSENT_TYPE_AMBER_ALERTS = "AMBER_ALERTS_CONSENT"
        const val CONSENT_TYPE_PRIMARY = "CONSENT_TYPE_PRIMARY"
        const val CONSENT_TYPE_PARTNER = "CONSENT_TYPE_PARTNER"
        const val CONSENT_TYPE_MARKETING = "CONSENT_TYPE_MARKETING"
        const val CONSENT_TYPE_TERMS_OF_USE = "CONSENT_TYPE_TERMS_OF_USE"
        const val CONSENT_TYPE_SMS_CONSENT = "SMS_CONSENT"
    }

    val canEdit get() = editable.equals(TRUE, true)

    fun getStatusText(context: Context): String =
        when (statusValue) {
            DataConsentStatus.ACCEPTED -> context.getString(R.string.DataConsents_accepted)
            DataConsentStatus.DECLINED -> context.getString(R.string.AccountSettings_declined)
            else -> ""
        }

    val statusValue get() = DataConsentStatus.fromValue(consentStatus)

    val isAccepted get() = statusValue == DataConsentStatus.ACCEPTED

    val isDeclined get() = statusValue == DataConsentStatus.DECLINED

    fun getConsentUpdatedDate(
        context: Context,
        dateUtil: DateUtil?,
    ): String = getConsentUpdatedDate(context)

    fun getConsentUpdatedDate(context: Context): String =
        try {
            if (!consentAcknowledgedDate.isNullOrEmpty()) {
                String.format(
                    context.getString(R.string.consent_update_format),
                    consentAcknowledgedDate,
                )
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }

    fun getConsentDateByStatus(context: Context): String =
        when {
            detailSection?.consentAcknowledgedDate.isNullOrEmpty() -> ""
            isDeclined ->
                String.format(
                    context.getString(R.string.declined_on),
                    detailSection?.consentAcknowledgedDate,
                )
            isAccepted ->
                String.format(
                    context.getString(R.string.accepted_on),
                    detailSection?.consentAcknowledgedDate,
                )
            else -> ""
        }

    fun getNewConsentDateByStatus(
        context: Context,
        dateUtil: DateUtil,
    ): String =
        when {
            detailSection?.consentAcknowledgedDateInUTC == null -> ""
            isDeclined ->
                String.format(
                    context.getString(R.string.declined_on),
                    dateUtil.formatMediumDate(detailSection?.consentAcknowledgedDateInUTC ?: Date()),
                )
            isAccepted ->
                String.format(
                    context.getString(R.string.accepted_on),
                    dateUtil.formatMediumDate(detailSection?.consentAcknowledgedDateInUTC ?: Date()),
                )
            else -> ""
        }

    val colorResId get(): Int = if (isAccepted) R.color.paleGreen else R.color.paleRed

    val showImage get() = !imageUrl.isNullOrEmpty()

    val showManageConsentButton get() = !manageServiceText?.trim().isNullOrEmpty() && consentEditable

    val showStatus get() = consentType?.equals(CONSENT_TYPE_TERMS_OF_USE, true) == false

    // Hiding Accept and Decline buttons for legacy
    val hideButtons get() = false // masterConsent && masterConsentUnEditable
}

@Parcelize
data class DataConsentDescription(
    var body: String?,
    var declinePayload: DeclinePayload?,
    var dialogs: List<Dialog>?,
    var negativeButtonText: String?,
    var positiveButtonText: String?,
) : Parcelable

@Parcelize
data class DeclinePayload(
    var body: String?,
    var negativeButtonText: String?,
    var positiveButtonText: String?,
    var title: String?,
    var subTitleHeader: String?,
    var subTitleHeaderDesc: String?,
    var otherProducts: ProductList? = null,
    var partnerProducts: ProductList? = null,
    var subscriptions: Subscriptions? = null,
    var subscriptionsNotDeclined: Subscriptions? = null,
    var imageUrl: String? = "",
    var pageTitle: String? = "",
    var optInTitle: String? = "",
    var optOutTitle: String? = "",
) : Parcelable {
    val getAllServices: List<CombinedConsentBaseItem>
        get() {
            val items = mutableListOf<CombinedConsentBaseItem>()
            subscriptions?.let {
                if (!it.services.isNullOrEmpty()) {
                    items.add(
                        ConsentProductHeaderItem(
                            title = it.sectionTitle,
                            subTitle = it.sectionSubTitle,
                        ),
                    )
                    it.services?.forEach { service ->
                        items.add(
                            ConsentProductUIItem(
                                imageUrl = service.imageUrl,
                                name = service.name,
                                alertMessage = service.alertMessage,
                            ),
                        )
                    }
                }
            }
            otherProducts?.let {
                if (!it.products.isNullOrEmpty()) {
                    items.add(
                        ConsentProductHeaderItem(
                            title = it.sectionTitle ?: "",
                            subTitle = it.sectionSubTitle,
                        ),
                    )
                    it.products?.forEach { product ->
                        items.add(
                            ConsentProductUIItem(imageUrl = product.imageUrl, name = product.name),
                        )
                    }
                }
            }
            partnerProducts?.let {
                if (!it.products.isNullOrEmpty()) {
                    items.add(
                        ConsentProductHeaderItem(
                            title = it.sectionTitle ?: "",
                            subTitle = it.sectionSubTitle,
                        ),
                    )
                    it.products?.forEach { product ->
                        items.add(
                            ConsentProductUIItem(imageUrl = product.imageUrl, name = product.name),
                        )
                    }
                }
            }
            subscriptionsNotDeclined?.let {
                if (!it.services.isNullOrEmpty()) {
                    items.add(
                        ConsentProductHeaderItem(
                            title = it.sectionTitle ?: "",
                            subTitle = it.sectionSubTitle,
                        ),
                    )
                    it.services?.forEach { product ->
                        items.add(
                            ConsentProductUIItem(
                                imageUrl = product.imageUrl,
                                name = product.name,
                                alertMessage = product.alertMessage,
                            ),
                        )
                    }
                }
            }
            return items
        }
}

@Parcelize
data class Dialog(
    var message: String?,
    var scheme: String?,
    var title: String?,
    var body: String?,
) : Parcelable

data class CombineDataConsentRequest(
    @SerializedName("products")
    var products: List<Product>?,
    @SerializedName("productCodes")
    var productCodes: List<String>? = null,
    @SerializedName("flowType")
    var flowType: String? = null,
)

data class Product(
    @SerializedName("productName")
    var productName: String?,
)

@Parcelize
data class ConsentRequestItem(
    var consentId: String,
    var status: String?,
    var versionId: String?,
    var category: String?,
) : Parcelable

enum class DataConsentStatus(
    val value: String,
) {
    UNSELECTED(""),
    ACCEPTED(CONSENT_ACCEPTED),
    DECLINED(CONSENT_DECLINED),
    ;

    companion object {
        fun fromValue(value: String?): DataConsentStatus = values().find { it.value.equals(value, true) } ?: UNSELECTED
    }
}

@Parcelize
data class DetailSection(
    var description: String? = "",
    var descriptionHtml: String? = null,
    var dataShared: DataShared? = null,
    var otherProducts: ProductList? = null,
    var partnerProducts: ProductList? = null,
    var serviceSubscriptions: ServiceSubscriptions? = null,
    var subdescription: String? = "",
    var subtitle: String? = "",
    var title: String? = "",
    @SerializedName("animatedGifUrl")
    var gifUrl: String?,
    @SerializedName("animationUrl")
    var videoUrl: String?,
    var pageTitle: String?,
    var imageUrl: String? = null,
    var consentAcknowledgedDate: String? = null,
    var consentAcknowledgedDateInUTC: Date? = null,
) : Parcelable {
    val showPartnerProducts get() = partnerProducts?.products?.isNotEmpty() ?: false

    fun getHtmlDescription() = if (descriptionHtml.isNullOrBlank()) description else descriptionHtml
}

@Parcelize
data class ManageSection(
    var body: String? = "",
    var declinePayload: DeclinePayload? = null,
    var dialogs: List<Dialog>? = null,
    var negativeButtonText: String? = "",
    var positiveButtonText: String? = "",
    var pageTitle: String? = "",
) : Parcelable {
    val showButton get() = !positiveButtonText.isNullOrEmpty()
}

abstract class CombinedConsentBaseItem

@Parcelize
data class CombinedConsentHeaderItem(
    var title: String?,
    var description: String? = "",
) : CombinedConsentBaseItem(),
    Parcelable

@Parcelize
data class DataShared(
    var objects: List<String>? = null,
    var items: List<ConsentMoreDetailItemUIData>? = null,
    var title: String? = "",
    var moreDetails: ConsentMoreDetails?,
    var description: String?,
    var pageTitle: String?,
) : Parcelable

@Parcelize
data class ConsentMoreDetails(
    var name: String?,
    var expandImage: String?,
    var collapseImage: String?,
    var pageData: ConsentMoreDetailPageUIData?,
) : Parcelable

@Parcelize
data class ProductList(
    var products: List<ProductDetail>? = null,
    var sectionSubTitle: String? = "",
    var sectionTitle: String? = "",
) : Parcelable

@Parcelize
data class ProductDetail(
    var benefits: String? = "",
    var imageUrl: String? = "",
    var name: String? = "",
    var isLastItem: Boolean = false,
) : Parcelable

@Parcelize
data class Subscription(
    var imageUrl: String? = "",
    var name: String? = "",
    var term: String? = "",
    var alertMessage: String? = "",
) : Parcelable

@Parcelize
data class Subscriptions(
    @SerializedName("subscriptions")
    var services: List<Subscription>? = null,
    var sectionSubTitle: String? = "",
    var sectionTitle: String? = "",
) : Parcelable {
    val showList get() = services?.isNotEmpty() ?: false
}

@Parcelize
data class ServiceSubscriptions(
    var productDetails: List<SubscriptionProduct>? = null,
    var title: String? = "",
) : Parcelable

data class ConsentProductUIItem(
    var imageUrl: String?,
    var name: String?,
    var description: String? = "",
    var alertMessage: String? = "",
) : CombinedConsentBaseItem()

data class ConsentProductHeaderItem(
    var title: String?,
    var subTitle: String?,
) : CombinedConsentBaseItem()

@Parcelize
data class PrivacyConsents(
    var consentId: String,
    var editable: String?,
    var language: String?,
    var name: String?,
    var version: String?,
    var category: String?,
    var consentStatus: String?,
    var moreDetailsText: String?,
    var description: String?,
    var staticData: PrivacyPolicyHeader?,
    var dataConsents: PrivacyDataConsents?,
    var smsConsents: PrivacyDataConsents?,
    var partnerConsents: PrivacyDataConsents?,
    var marketingConsents: PrivacyDataConsents?,
    var termsOfUse: PrivacyDataConsents?,
    @SerializedName("animatedGifUrl")
    var gifUrl: String?,
    var animationUrl: String?,
) : Parcelable

@Parcelize
data class PrivacyDataConsents(
    var title: String?,
    var subTitle: String?,
    var consents: List<PrivacyConsentItem>?,
) : Parcelable

@Parcelize
data class PrivacyConsentItem(
    var consentId: String,
    var editable: String?,
    var language: String?,
    var name: String?,
    var version: String?,
    var category: String?,
    var consentStatus: String?,
    var imageUrl: String?,
    var description: String?,
    var animationUrl: String?,
) : Parcelable

@Parcelize
data class PrivacyPolicyHeader(
    var imageUrl: String?,
    var title: String?,
    var description: String?,
    var footerText: String,
) : Parcelable

@Parcelize
data class ConsentMoreDetailItemUIData(
    var title: String?,
    var description: String?,
    var items: List<String>? = emptyList(),
) : Parcelable

@Parcelize
data class ConsentMoreDetailPageUIData(
    var title: String?,
    @SerializedName("items")
    var pageItem: List<ConsentMoreDetailItemUIData>,
) : Parcelable

@Parcelize
data class SubscriptionProduct(
    var name: String?,
    var imageUrl: String?,
    var dataShared: DataShared?,
    var moreDetails: ConsentMoreDetailPageUIData?,
    var declineConsent: SubscriptionDecline?,
    var pageTitle: String?,
) : Parcelable

@Parcelize
data class SubscriptionDecline(
    var title: String?,
    var description: String?,
    var cancelKey: String?,
    var declineKey: String?,
) : Parcelable {
    fun getSpannableDescription(context: Context): SpannableString = SpannableString(description)
}
