package com.toyota.oneapp.model.ubi

import com.google.gson.annotations.SerializedName

data class UserOptStatusResponse(
    @SerializedName("status")
    val status: ResponseStatus,
    @SerializedName("payload")
    val payload: PayLoadResponse,
    @SerializedName("timestamp")
    val timeStamp: String,
) {
    inner class PayLoadResponse(
        @SerializedName("headline")
        val code: String,
        @SerializedName("imageURL")
        val imgUrl: String,
        @SerializedName("message")
        val detailMessage: String,
    )

    inner class ResponseStatus(
        @SerializedName("messages")
        val responses: ArrayList<MessageResponse>,
    ) {
        inner class MessageResponse(
            @SerializedName("responseCode")
            val code: String,
            @SerializedName("description")
            val shortDesc: String,
            @SerializedName("detailedDescription")
            val detailDesc: String,
        )
    }
}
