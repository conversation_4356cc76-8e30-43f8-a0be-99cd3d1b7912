package com.toyota.oneapp.model.dashboard.card

import android.os.Parcelable
import com.toyota.oneapp.model.poi.LocationDetails
import kotlinx.parcelize.Parcelize

@Parcelize
class MyDestinationsItem :
    BaseCardDataItem(CurrentLocationItem::class.java.simpleName),
    Parcelable {
    var home: LocationDetails = LocationDetails()
    var work: LocationDetails = LocationDetails()
    var recentDestinations = ArrayList<LocationDetails>()
    var favList: ArrayList<LocationDetails> = ArrayList()
}
