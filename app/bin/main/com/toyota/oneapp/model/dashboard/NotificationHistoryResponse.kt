package com.toyota.oneapp.model.dashboard

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class NotificationHistoryResponse(
    @SerializedName("payload") var payload: ArrayList<NotificationHistoryPayload>?,
) : Parcelable

@Parcelize
data class NotificationHistoryPayload(
    @SerializedName("notifications") var payload: ArrayList<NotificationHistoryItem>?,
    @SerializedName("modelDesc") var modelDesc: String?,
    @SerializedName("vin") var vin: String? = null,
) : Parcelable

@Parcelize
data class NotificationHistoryItem(
    @SerializedName("title") var title: String?,
    @SerializedName("messageId") val messageId: String?,
    @SerializedName("isRead") var isRead: Boolean? = false,
    @SerializedName("readTimestamp") var readTimestamp: String?,
    @SerializedName("notificationDate") var notificationDate: String?,
    @SerializedName("message") var message: String?,
    @SerializedName("status") var status: String?,
    @SerializedName("iconUrl") var iconUrl: String?,
    @SerializedName("subcategory") var subcategory: String?,
    @SerializedName("category") var category: String?,
    @SerializedName("displayCategory") var displayCategory: String?,
    @SerializedName("vin") var vin: String?,
    @SerializedName("type") var type: String?,
    @SerializedName("lat") val latitude: Double? = null,
    @SerializedName("lon") val longitude: Double? = null,
) : Parcelable

data class MarkReadRequestPayload(
    @SerializedName("guid") val guid: String,
    @SerializedName(
        "messageIds",
    ) val messageIds: List<String>,
)
