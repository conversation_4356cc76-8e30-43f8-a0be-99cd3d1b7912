package com.toyota.oneapp.model.vehiclehealth

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class IRFApprovalGrantRequest(
    var name: String? = null,
    var address: String? = null,
    var city: String? = null,
    var state: String? = null,
    var zipCode: String? = null,
    var website: String? = null,
    var phoneNumber: String? = null,
    var approval: Char = Approval.GRANT.value,
) : Parcelable {
    constructor(irfInfoItem: IRFInfoItem) : this() {
        irfInfoItem.let { item ->
            name = item.name
            address = item.address
            city = item.city
            state = item.state
            zipCode = item.zipCode
            website = item.website
            phoneNumber = item.phoneNumber
        }
    }
}

enum class Approval(val value: Char) {
    GRANT('Y'),
    REVOKE('N'),
}
