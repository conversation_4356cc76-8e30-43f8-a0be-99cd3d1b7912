package com.toyota.oneapp.model.vehiclehealth

import android.os.Parcelable
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.parcelize.Parcelize

@Parcelize
data class IRFResponse(val payload: List<IRFInfoItem?>? = null) : Parcelable, BaseResponse()

@Parcelize
data class IRFInfoItem(
    val id: String,
    val name: String? = null,
    val address: String? = null,
    val city: String? = null,
    val state: String? = null,
    val zipCode: String? = null,
    val website: String? = null,
    val phoneNumber: String? = null,
    val validUntil: String? = null,
) : Parcelable {
    fun getMultiLineAddress(): String {
        val separator = if (city != null) ", " else ""
        val addressLine =
            if (!address.isNullOrEmpty()) {
                "$address\n"
            } else {
                ""
            }
        return "$addressLine ${city ?: ""}${separator}${state ?: ""} ${zipCode ?: ""}"
    }
}
