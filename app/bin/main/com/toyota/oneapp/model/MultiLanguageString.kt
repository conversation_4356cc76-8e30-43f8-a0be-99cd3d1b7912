package com.toyota.oneapp.model

import android.os.Parcelable
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.util.language.SupportedLanguages
import kotlinx.parcelize.Parcelize

@Parcelize
class MultiLanguageString(
    private val english: String?,
    private val french: String?,
    private val spanish: String?,
) : Parcelable {
    fun getString(supportedLanguage: SupportedLanguages) =
        when (supportedLanguage) {
            SupportedLanguages.ENGLISH -> english
            SupportedLanguages.SPANISH -> (spanish ?: english)
            SupportedLanguages.FRENCH -> (french ?: english)
        }

    fun getString(languagePreferenceModel: LanguagePreferenceModel?) =
        getString(
            SupportedLanguages.getLanguageFromAcronym(languagePreferenceModel?.getLanguage()),
        )
} // MultiLanguageString class
