package com.toyota.oneapp.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class DataConsent(
    var serviceConnect: String? = null,
    var can300: String? = null,
    var dealerContact: String? = null,
    var ubi: String? = null,
) : Parcelable {
    init {
        serviceConnect = serviceConnect?.capitalize()
        can300 = can300?.capitalize()
        dealerContact = dealerContact?.capitalize()
        ubi = ubi?.capitalize()
    }
}
