package com.toyota.oneapp.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class DealerScheduleSlotsResponse(val payload: List<String>) : Parcelable {
    fun getDealerScheduleSlotsFromResponse(): ArrayList<ServiceTime> {
        val list = arrayListOf<ServiceTime>()
        payload.forEach {
            list.add(ServiceTime(it))
        }
        return list
    }
}
