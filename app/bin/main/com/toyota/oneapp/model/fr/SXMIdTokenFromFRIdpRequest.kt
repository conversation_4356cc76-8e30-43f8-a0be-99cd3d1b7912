package com.toyota.oneapp.model.fr

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class SXMIdTokenFromFRIdpRequest(
    @SerializedName("input_token_state") val inPutTokenSate: InputTokenState,
    @SerializedName("output_token_state") val outputTokenState: OutputTokenState,
) : Parcelable

@Parcelize
data class InputTokenState(
    @SerializedName("token_type") val tokenType: String = "OPENIDCONNECT",
    @SerializedName("oidc_id_token") val oidcIdToken: String,
) : Parcelable

@Parcelize
data class OutputTokenState(
    @SerializedName("token_type") val tokenType: String = "OPENIDCONNECT",
    @SerializedName("nonce") val nonce: String,
    @SerializedName("allow_access") val allowAccess: Boolean = true,
) : Parcelable
