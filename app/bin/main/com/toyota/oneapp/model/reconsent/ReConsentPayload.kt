package com.toyota.oneapp.model.reconsent

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class ReConsentResponse(
    val reconsent: ReConsentPayload,
)

data class ReConsentPayload(
    val consentId: String,
    val eligible: <PERSON>olean,
)

@Parcelize
data class ReConsentData(
    val hasVehicle: Boolean = false,
    val errorMessage: String = "",
    val consentId: String = "",
    val reConsentEnabled: <PERSON><PERSON><PERSON> = false,
) : Parcelable

sealed class ReConsentState {
    object Idle : ReConsentState()

    object Loading : ReConsentState()

    class Success(val data: ReConsentData) : ReConsentState()

    object Error : ReConsentState()
}
