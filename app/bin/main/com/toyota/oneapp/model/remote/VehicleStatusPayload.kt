package com.toyota.oneapp.model.remote

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

@Parcelize
data class VehicleStatusPayload(
    var occurrenceDate: Date,
    var cautionOverallCount: Int = 0,
    var latitude: String? = null,
    var longitude: String? = null,
    var vehicleStatus: List<VehicleStatus>? = null,
    var address: String,
    var locationAcquisitionDatetime: Date?,
) : Parcelable

@Parcelize
data class VehicleStatus(
    var displayOrder: Int = 0,
    var category: String?,
    var sections: List<SectionsItem>? = null,
) : Parcelable

@Parcelize
data class SectionsItem(
    var values: List<ValuesItem>? = null,
    var section: String? = null,
) : Parcelable

@Parcelize
data class ValuesItem(
    var value: String? = null,
    var status: Int = 0,
) : Parcelable
