/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.model.remote

data class DigitalKeyPushPayload(
    val notifications: List<NotificationItem>?,
    val type: String?,
)

data class NotificationItem(
    val messageId: String?,
    val originURI: String?,
    val destinationURI: String?,
    val payload: PayloadItem?,
)

data class PayloadItem(
    val type: String?,
    val value: KeyDeactivationValue?,
)

data class KeyDeactivationValue(
    val timestamp: Long?,
    val transaction: TransactionInfo?,
    val dataPackageApdu: String?,
    val intMvkVersion: Int?,
    val destinationKeyInfoId: String?,
    val aboutKeyInfoId: String?,
    val serviceKeyId: String?,
    val vehicleId: String?,
    val subjectId: String?,
    val removesKey: Boolean?,
)

data class TransactionInfo(
    val name: String?,
    val transactionId: String?,
)
