package com.toyota.oneapp.model.remote

import com.toyota.one_ui.widgets.Generation
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.ToyotaConstants.Companion.STANDARD_UTC_DATE_FORMAT
import java.time.Duration
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

open class EngineStatusPayload {
    var vin: String? = null
    var status = 0
    var date: String? = null
    var timer: Int? = null

    fun getEngineCountDownTimer(vehicleInfo: VehicleInfo): String {
        return if (date != null) {
            val engineTimer: Int = timer ?: getDefaultTimer(vehicleInfo)
            val formatter = DateTimeFormatter.ofPattern(STANDARD_UTC_DATE_FORMAT)
            val engineDateTime =
                LocalDateTime.parse(date, formatter).plusMinutes(
                    engineTimer.toLong(),
                )

            val duration =
                Duration.between(
                    LocalDateTime.now(ZoneOffset.UTC),
                    engineDateTime,
                )

            val minutes = duration.toMinutes() % 60
            val seconds = duration.seconds % 60

            "$minutes:$seconds"
        } else {
            ToyotaConstants.EMPTY_STRING
        }
    }

    private fun getDefaultTimer(vehicleInfo: VehicleInfo) =
        when (vehicleInfo.generationType) {
            Generation.MM21 -> 20
            else -> 10
        }
}
