package com.toyota.oneapp.model.remote

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class RemoteAuthCodeRequest(
    @SerializedName("authCode")
    var authCode: String? = null,
    @SerializedName("notifyByEmail")
    var notifyByEmail: Boolean = false,
    @SerializedName("notifyByPhone")
    var notifyByPhone: Boolean = false,
    @SerializedName("sendNotification")
    val sendNotification: Boolean = true,
) : Parcelable {
    constructor(isMobile: Boolean) : this() {
        notifyByEmail = !isMobile
        notifyByPhone = isMobile
    }

    constructor(authCode: String, isMobile: Boolean) : this(isMobile) {
        this.authCode = authCode
    }
}
