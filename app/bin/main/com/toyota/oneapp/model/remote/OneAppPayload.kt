package com.toyota.oneapp.model.remote

data class OneAppPayload(
    val category: String? = null,
    val commandEnded: Boolean? = null,
    val occurrenceDate: String? = null,
    val repairMode: Int? = null,
    val type: String? = null,
    val status: String? = null,
    val vehicleAlertStatusList: List<VehicleAlertStatus>? = null,
    val vin: String? = null,
    val collisionId: String? = null,
    val subcategory: String? = null,
    val deeplink: String? = null,
    val registrationRequestId: String? = null,
    val destinationInfo: DestinationInfo? = null,
) {
    data class VehicleAlertStatus(
        val `data`: String,
        val security: String,
        val type: String,
    )

    companion object {
        const val OTA_UPDATE = "OTA_UPDATES"
        const val PIN_UPDATE = "PIN_UPDATE"
    }
}

data class DestinationInfo(
    val coordinate: Coordinate?,
    val address: String,
    val name: String,
    val poiId: String,
    val addressComponents: AddressComponents,
)

data class Coordinate(val lon: String, val lat: String)

data class AddressComponents(
    val country: String,
    val unit: String,
    val adminRegion: String,
    val city: String,
    val street: String,
    val postalCode: String,
    val intersection: String,
    val houseNumber: String,
    val adminRegionShort: String,
)

data class OneAppP7Payload(
    val action: String? = null,
    val vin: String? = null,
    val statusCode: String? = null,
)
