package com.toyota.oneapp.model.remote

import android.os.Build
import com.toyota.oneapp.BuildConfig

data class SendDeviceStatusRequest(
    val GUID: String?,
    val device_id: String,
    val advertising_id: String? = null,
) {
    @Suppress("ktlint:standard:property-naming")
    val device_type = "android"
    val brand = BuildConfig.APP_BRAND

    @Suppress("ktlint:standard:property-naming")
    val app_version = BuildConfig.VERSION_NAME

    @Suppress("ktlint:standard:property-naming")
    val device_version = Build.VERSION.RELEASE

    @Suppress("ktlint:standard:property-naming")
    val device_model = Build.MODEL
    val label = BuildConfig.APPLICATION_ID
}
