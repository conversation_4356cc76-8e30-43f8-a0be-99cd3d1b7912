package com.toyota.oneapp.model.remote

import com.toyota.oneapp.network.models.BaseAPIStatus

data class RemoteBaseResponse(
    val payload: Payload? = null,
    val status: BaseAPIStatus? = null,
    var code: Int = 0,
)

data class Payload(
    val appRequestNo: String? = null,
    val returnCode: String? = null,
    val correlationId: String? = null,
)

data class MessagesItem(
    val detailedDescription: String? = null,
    val description: String? = null,
    val responseCode: String? = null,
)
