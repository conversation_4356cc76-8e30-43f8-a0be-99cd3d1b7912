package com.toyota.oneapp.model.remote

import com.google.gson.annotations.SerializedName

class RemoteRequest {
    var guid: String? = null
    var vin: String? = null
    var deviceId: String? = null
    var deviceType: String? = "Android"
    var command: Any? = null
    var autoFixPopup: Boolean? = false

    @SerializedName("doorLock")
    var trunkAttribute: TrunkAttribute? = null
    var beepCount: Int? = null
}

data class TrunkAttribute(val target: Int? = 1)
