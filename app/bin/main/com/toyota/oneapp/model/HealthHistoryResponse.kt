package com.toyota.oneapp.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.parcelize.Parcelize

@Parcelize
data class HealthHistoryResponse(
    @SerializedName("payload")
    val payload: List<HealthHistoryItem?>? = null,
) : BaseResponse(), Parcelable

@Parcelize
data class HealthHistoryItem(
    @SerializedName("displayTime")
    val displayTime: String? = null,
    @SerializedName("month")
    val month: String? = null,
    @SerializedName("year")
    val year: String? = null,
) : Parcelable
