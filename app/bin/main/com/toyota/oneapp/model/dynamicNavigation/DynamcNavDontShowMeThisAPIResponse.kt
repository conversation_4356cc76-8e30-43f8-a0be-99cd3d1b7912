package com.toyota.oneapp.model.dynamicNavigation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class DynamcNavDontShowMeThisAPIResponse(
    @SerializedName("description") val description: String,
    @SerializedName("detailedDescription") val detailedDescription: String,
    @SerializedName("responseCode") val responseCode: String,
) : Parcelable
