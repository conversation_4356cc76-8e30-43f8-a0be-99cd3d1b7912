package com.toyota.oneapp.model.dynamicNavigation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class DynamicNavResponse(
    @SerializedName("payload") val payload: DynamicPayLoadResponse,
) : Parcelable

@Parcelize
data class DynamicPayLoadResponse(
    @SerializedName("vin") val vin: String,
    @SerializedName("guid") val guid: String,
    @SerializedName("dofu") val dofu: String,
    @SerializedName("mapDetails") val mapDetails: MapDetails?,
    @SerializedName("createDate") val createDate: String,
    @SerializedName("createSource") val createSource: String,
    @SerializedName("updatedDate") val updatedDate: String,
    @SerializedName("updatedSource") val updatedSource: String,
) : Parcelable

@Parcelize
data class MapDetails(
    @SerializedName("mapId") val mapId: String,
    @SerializedName("mapVersion") val mapVersion: String,
    @SerializedName("mapExpirationDate") val mapExpirationDate: String,
    @SerializedName("mapActivationNumber") val mapActivationNumber: String,
) : Parcelable
