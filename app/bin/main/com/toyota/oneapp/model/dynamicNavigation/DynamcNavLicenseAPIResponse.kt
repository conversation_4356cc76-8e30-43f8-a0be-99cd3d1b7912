package com.toyota.oneapp.model.dynamicNavigation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class DynamcNavLicenseAPIResponse(
    @SerializedName("payload") val payload: DynamicLicensePayLoadResponse,
) : Parcelable

@Parcelize
data class DynamicLicensePayLoadResponse(
    @SerializedName("vin") val vin: String,
    @SerializedName("guid") val guid: String,
    @SerializedName("licenseKey") val licenseKey: String,
) : Parcelable
