package com.toyota.oneapp.model.dynamicNavigation

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class DynamicNavUpdateEligibiltyRequest(
    var vin: String? = null,
    var guid: String? = null,
    var mapId: String? = null,
    var mapVersion: String? = null,
    var mapExpirationDate: String? = null,
) : Parcelable

@Parcelize
data class ContactDealerRequest(var dealerContact: Boolean) : Parcelable
