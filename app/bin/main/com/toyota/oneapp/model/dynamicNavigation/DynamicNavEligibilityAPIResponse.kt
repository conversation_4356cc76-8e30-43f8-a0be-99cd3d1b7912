package com.toyota.oneapp.model.dynamicNavigation

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class DynamicNavEligibilityAPIResponse(
    @SerializedName("payload") val payload: DynamicNavEligibilityPayLoadResponse,
) : Parcelable

@Parcelize
data class DynamicNavEligibilityPayLoadResponse(
    @SerializedName("vin") val vin: String,
    @SerializedName("guid") val guid: String,
    @SerializedName("eligible") val eligible: Boolean,
    @SerializedName("pcoLink") val pcoLink: String,
    @SerializedName("declined") val declined: Boolean,
    @SerializedName("mapDetailsExist") val mapDetailsExist: Boolean,
    @SerializedName("readyToUpdate") val readyToUpdate: <PERSON><PERSON><PERSON>,
    @SerializedName("purchaseEligible") val purchaseEligible: Boolean,
    @SerializedName("showUpdateButton") val showUpdateButton: Boolean,
) : Parcelable
