package com.toyota.oneapp.model.dynamicNavigation

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class DynamicInstallUpdateRequest(
    var vin: String? = null,
    var guid: String? = null,
    var mapId: String? = null,
    var declined: Boolean = false,
    var mapVersion: String? = null,
    var mapExpirationDate: String? = null,
    var mapActivationDate: String? = null,
    var mapPurchaseDate: String? = null,
    var mapActivationNumber: String? = null,
    var remindMeTomorrow: Boolean = false,
    var remindMeAfterOneWeek: Boolean = false,
    var remindMeOnADate: Boolean = false,
    var updateComplete: Boolean = false,
    var resumed: Boolean = false,
) : Parcelable
