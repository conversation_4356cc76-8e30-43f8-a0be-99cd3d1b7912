package com.toyota.oneapp.model

import android.os.Parcelable
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.parcelize.Parcelize

data class RepairServicesResponse(val payload: RepairServicesPayload?) : BaseResponse()

@Parcelize
data class RepairServicesPayload(
    val drsPricingAvailable: Boolean = true,
    val grs: ArrayList<ServiceRepair> = arrayListOf(),
    val drs: ArrayList<ServiceRepair> = arrayListOf(),
    val frs: ArrayList<ServiceRepair> = arrayListOf(),
) : Parcelable
