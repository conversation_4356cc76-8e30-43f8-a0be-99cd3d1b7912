package com.toyota.oneapp.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.parcelize.Parcelize

@Parcelize
data class AppDetailsResponse(
    @SerializedName("payload") val payload: AppDetailsPayload,
) : Parcelable

@Parcelize
data class AppDetailsPayload(
    @SerializedName("androidAppStoreVersionCode") val appStoreVersionCode: Int?,
    @SerializedName("androidMandatoryVersionCode") val mandatoryVersionCode: Int?,
    @SerializedName("androidAppStoreVersion") val appStoreVersion: String?,
    @SerializedName("androidMandatoryVersion") val mandatoryVersion: String?,
    @SerializedName("upcomingDowntime") val upcomingDowntime: ArrayList<UpcomingDowntime>,
    @SerializedName("appVersionBanner") val appVersionBanner: AppVersionBanner?,
) : Parcelable

@Parcelize
data class UpcomingDowntime(
    @SerializedName("startTime") val startTime: String,
    @SerializedName("endTime") val endTime: String,
    @SerializedName("type") val type: String,
    @SerializedName("reason") val reason: String? = ToyotaConstants.EMPTY_STRING,
    @SerializedName("feature") val features: ArrayList<String>? =
        arrayListOf(
            ToyotaConstants.EMPTY_STRING,
        ),
) : Parcelable

@Parcelize
data class AppVersionBanner(
    @SerializedName("header") val header: String?,
    val banner: String?,
    @SerializedName("cta") val updateNow: String?,
    @SerializedName("cta1") val later: String?,
) : Parcelable
