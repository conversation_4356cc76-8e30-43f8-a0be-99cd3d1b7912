package com.toyota.oneapp.model.pref

import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.model.language.LanguageItem
import com.toyota.oneapp.model.language.RegionItem
import com.toyota.oneapp.util.ToyotaConstants
import toyotaone.commonlib.sharepreference.HawkUtil
import java.util.Locale
import javax.inject.Inject

class LanguagePreferenceModel
    @Inject
    constructor() {
        companion object {
            const val CODE_EN = "EN"
            const val CODE_ES = "ES"
            const val CODE_FR = "FR"

            const val CODE_US = "US"
            const val CODE_CA = "CA"
            const val CODE_PR = "PR"
            const val CODE_MX = "MX"
            const val CODE_AU = "AU"

            val US_ENGLISH = Locale(CODE_EN, CODE_US, "")
            val TDPR_ENGLISH = Locale(CODE_EN, CODE_PR, "")
            val TDPR_SPANISH = Locale(CODE_ES, CODE_PR, "")
            val CANADA_ENGLISH = Locale(CODE_EN, CODE_CA, "")
            val CANADA_FRANCE = Locale(CODE_FR, CODE_CA, "")
            val MEXICO_ENGLISH = Locale(CODE_EN, CODE_MX, "")
            val MEXICO_SPANISH = Locale(CODE_ES, CODE_MX, "")
            val AU_ENGLISH = Locale(CODE_EN, CODE_AU, "")

            private const val LANGUAGE = "LANGUAGE"
            private const val REGION = "REGION"
        }

        fun getLanguage(): String =
            HawkUtil.get<String?>(LANGUAGE, null)
                ?: getAppSupportedSystemDefaultLocal().language

        fun setLanguage(language: String) {
            HawkUtil.put(LANGUAGE, language)
        }

        fun getRegion(): RegionItem {
            val languageList: ArrayList<LanguageItem> = arrayListOf()
            when (getAppSupportedSystemDefaultLocal().country.toString()) {
                CODE_US -> {
                    languageList.add(
                        LanguageItem(
                            US_ENGLISH.language,
                            US_ENGLISH.language,
                        ),
                    )
                }
                CODE_CA -> {
                    languageList.add(
                        LanguageItem(
                            CANADA_ENGLISH.language,
                            CANADA_ENGLISH.language,
                        ),
                    )
                    languageList.add(
                        LanguageItem(
                            CANADA_FRANCE.language,
                            CANADA_FRANCE.language,
                        ),
                    )
                }
                CODE_PR -> {
                    languageList.add(
                        LanguageItem(
                            TDPR_ENGLISH.language,
                            TDPR_ENGLISH.language,
                        ),
                    )
                    languageList.add(
                        LanguageItem(
                            TDPR_SPANISH.language,
                            TDPR_SPANISH.language,
                        ),
                    )
                }
                CODE_MX -> {
                    languageList.add(
                        LanguageItem(
                            MEXICO_ENGLISH.language,
                            MEXICO_ENGLISH.language,
                        ),
                    )
                    languageList.add(
                        LanguageItem(
                            MEXICO_SPANISH.language,
                            MEXICO_SPANISH.language,
                        ),
                    )
                }
                CODE_AU -> {
                    languageList.add(
                        LanguageItem(
                            AU_ENGLISH.language,
                            AU_ENGLISH.language,
                        ),
                    )
                }
                else -> {
                    languageList.add(
                        LanguageItem(
                            US_ENGLISH.language,
                            US_ENGLISH.language,
                        ),
                    )
                }
            }
            return HawkUtil.get<RegionItem?>(REGION, null)
                ?: getAppSupportedSystemDefaultLocal().let {
                    RegionItem(it.displayCountry, it.country, it.country, languageList)
                }
        }

        fun setRegion(region: RegionItem) {
            HawkUtil.put(REGION, region)
        }

        fun clearLanguage() {
            HawkUtil.delete(LANGUAGE)
        }

        fun clearRegion() {
            HawkUtil.delete(REGION)
        }

        private fun getAppSupportedSystemDefaultLocal(): Locale {
            val defaultLocal = Locale.getDefault()
            return with(defaultLocal) {
                when {
                    BuildConfig.FLAVOR_region_brand.contains(ToyotaConstants.REGION_AU, true) -> AU_ENGLISH
                    language == US_ENGLISH.language && country == US_ENGLISH.country -> US_ENGLISH
                    language == TDPR_ENGLISH.language && country == TDPR_ENGLISH.country -> TDPR_ENGLISH
                    language == TDPR_SPANISH.language && country == TDPR_SPANISH.country -> TDPR_SPANISH
                    language == CANADA_ENGLISH.language && country == CANADA_ENGLISH.country -> CANADA_ENGLISH
                    language == CANADA_FRANCE.language && country == CANADA_FRANCE.country -> CANADA_FRANCE
                    language == MEXICO_ENGLISH.language && country == MEXICO_ENGLISH.country -> MEXICO_ENGLISH
                    language == MEXICO_SPANISH.language && country == MEXICO_SPANISH.country -> MEXICO_SPANISH
                    language == AU_ENGLISH.language && country == AU_ENGLISH.country -> AU_ENGLISH
                    else -> US_ENGLISH
                }
            }
        }
    }
