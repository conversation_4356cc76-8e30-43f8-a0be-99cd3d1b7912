package com.toyota.oneapp.model.garage

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.Date

data class ServiceHistoryResponse(
    val payload: ServiceHistoryPayload?,
)

data class ServiceHistoryPayload(
    val serviceHistories: List<ServiceHistoryItem>?,
)

@Parcelize
data class ServiceHistoryItem(
    @SerializedName("serviceHistoryId") val id: String?,
    var servicingDealer: DealerItem?,
    var mileage: String?,
    val unit: String?,
    val roNumber: String?,
    var serviceDate: Date?, // "yyyy-MM-dd"
    val operationsPerformed: ArrayList<String>?,
    val customerCreatedRecord: Boolean = false,
    val serviceProvider: String?,
    val notes: String?,
) : Parcelable

@Parcelize
data class DealerItem(
    val servicingDealerCode: String?,
    var servicingDealerName: String?,
    val address: String?,
    val city: String?,
    val state: String?,
    val country: String?,
    val zip: String?,
    val latitude: Double?,
    val longitude: Double?,
    val phone: String?,
) : Parcelable
