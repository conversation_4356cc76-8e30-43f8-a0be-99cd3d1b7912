package com.toyota.oneapp.model.garage

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class VehicleStatusHistoryModel(
    val payload: UpdateHistoryPayload,
) : Parcelable

@Parcelize
data class UpdateHistoryPayload(
    val updates: List<Updates>,
) : Parcelable

@Parcelize
data class Updates(
    val version: String,
    val notificationCode: String,
    val notificationType: String,
    val notificationTime: String,
    val description: String,
) : Parcelable
