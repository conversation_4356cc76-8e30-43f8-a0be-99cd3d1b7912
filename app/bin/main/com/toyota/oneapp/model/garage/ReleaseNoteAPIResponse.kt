package com.toyota.oneapp.model.garage

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class ReleaseNoteAPIResponse(
    @SerializedName("payload") val payload: ReleaseNotePayload,
) : Parcelable

@Parcelize
data class ReleaseNotePayload(
    @SerializedName("latestUpdate") val latestUpdate: LatestUpdate,
    @SerializedName("previousUpdate") val previousUpdate: PreviousUpdate,
    @SerializedName("update") val update: <PERSON><PERSON>an,
    @SerializedName("help") val help: String,
    @SerializedName("latestAvailableUpdate") val latestUpdateAvailable: Boolean,
    @SerializedName("previousAvailableUpdate") val previousUpdateAvailable: Boolean,
) : Parcelable

@Parcelize
data class LatestUpdate(
    @SerializedName("version") val version: String,
    @SerializedName("installDate") val installDate: String,
    @SerializedName("description") val description: String,
) : Parcelable

@Parcelize
data class PreviousUpdate(
    @SerializedName("version") val version: String,
    @SerializedName("installDate") val installDate: String,
    @SerializedName("description") val description: String,
) : Parcelable
