package com.toyota.oneapp.model.garage

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class NotificationRequest20TM(
    @SerializedName("payload") val payload: NotificationRequest20TMPayload,
) : Parcelable

@Parcelize
data class NotificationRequest20TMPayload(
    @SerializedName("notificationStatus") val notificationStatus: Int,
    @SerializedName("updateAvailable") val updateAvailable: <PERSON><PERSON><PERSON>,
    @SerializedName("versionNumber") val versionNumber: String?,
    @SerializedName("timeStamp") val installDate: String?,
) : Parcelable {
    companion object {
        const val SOFTWARE_UPDATE_AVAILABLE = 1
        const val SOFTWARE_UPDATE_PROGRESS = 2
        const val SOFTWARE_UPDATE_COMPLETE = 3
        const val SOFTWARE_ERROR_NOTIFICATION = 4
        const val SOFTWARE_UPDATE_AVAILABLE_21MM = 5
        const val CUSTOMER_ACTION_COMPLETE = 6
    }
}
