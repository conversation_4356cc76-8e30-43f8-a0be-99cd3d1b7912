package com.toyota.oneapp.model.garage

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class HowToResponse(val payload: ArrayList<HowToVideo>? = arrayListOf())

@Parcelize
data class HowToVideo(
    val videoId: String?,
    val tags: List<String>?,
    val title: String?,
    val description: String?,
    val brand: String?,
    val type: String?,
    val createdDate: String?,
    val modifiedDate: String?,
    val modelYear: List<String>?,
) : Parcelable
