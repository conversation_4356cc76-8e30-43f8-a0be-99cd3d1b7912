package com.toyota.oneapp.model.garage

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class SoftwareVersion20TMResponse(
    @SerializedName("payload") val payload: SoftwareVersion20TMPayloadResponse,
) : Parcelable

@Parcelize
data class SoftwareVersion20TMPayloadResponse(
    @SerializedName("version") val version: String,
    @SerializedName("status") val status: String,
    @SerializedName("instructions") val instructions: String,
    @SerializedName("descriptions") val descriptions: String,
    @SerializedName("workingTime") val workingTime: String,
) : Parcelable

@Parcelize
data class SoftwareUpdateNotification20tmResponse(
    @SerializedName("notificationStatus") val notificationStatus: Int?,
    @SerializedName("updateAvailable") val updateAvailable: Boolean?,
) : Parcelable

@Parcelize
data class SoftwareVersion21MMPayloadResponse(
    @SerializedName("registrationRequestId") val registrationRequestId: String?,
    @SerializedName("vin") val vin: String?,
    @SerializedName("messageContent") val messageContent: MessageContentResponse?,
) : Parcelable

@Parcelize
data class MessageContentResponse(
    @SerializedName("titleOfMessage") val titleOfMessage: String?,
    @SerializedName("message") val message: String?,
    @SerializedName("updateContent") val updateContent: UpdateContentResponse?,
) : Parcelable

@Parcelize
data class UpdateContentResponse(
    @SerializedName("termsOfUseStatement") val termsOfUseStatement: String?,
    @SerializedName("updateInformation") val updateInformation: String?,
    @SerializedName("currentVersion") val currentVersion: String?,
    @SerializedName("updateVersion") val updateVersion: String?,
    @SerializedName("caution") val caution: String?,
    @SerializedName("workingTime") val workingTime: String?,
    @SerializedName("contentImageUrl") val contentImageUrlList: List<String>,
    @SerializedName("urlOfOwnersManual") val urlOfOwnersManual: String?,
    @SerializedName("urlOfInstructionManualMovie") val urlOfInstructionManualMovie: String?,
    @SerializedName("howToUse") val howToUse: String?,
) : Parcelable

@Parcelize
data class SoftwareVersionUpdateRequest(
    @SerializedName("registrationRequestId") val registrationRequestId: String?,
    @SerializedName("vin") val vin: String?,
    @SerializedName("authorized") val isAuthorized: Boolean? = true,
) : Parcelable
