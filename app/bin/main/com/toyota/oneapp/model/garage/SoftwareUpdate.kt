package com.toyota.oneapp.model.garage

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class SoftwareUpdate(
    val latestVersion: String,
    val latestVersionInstalledDate: String,
    val latestVersionDescription: String,
    val previousVersionInstalledDate: String,
    val previousVersion: String,
    val previousVersionDescription: String,
    val isUpdate: Boolean,
    val helpLink: String,
    val isLatestUpdateAvailable: Boolean,
    val isPreviousUpdateAvailable: Boolean,
) : Parcelable
