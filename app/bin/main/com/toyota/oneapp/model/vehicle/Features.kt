package com.toyota.oneapp.model.vehicle

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.EnumSet

@Suppress("all")
@Parcelize
data class Features(
    val amberAlert: Int? = DISABLED,
    val achPayment: Int? = DISABLED,
    val addServiceRecord: Int? = ENABLED,
    val autoDrive: Int? = ENABLED,
    val cerence: Int? = ENABLED,
    val chargeAssist: Int? = ENABLED,
    val collisionAssistance: Int? = ENABLED,
    val connectedCard: Int? = ENABLED,
    val connectedSupport: Int? = ENABLED,
    val criticalAlert: Int? = ENABLED,
    val dashboardLights: Int? = ENABLED,
    val dealerAppointment: Int? = ENABLED,
    val driverCompanion: Int? = ENABLED,
    val driverScore: Int? = ENABLED,
    val doorLockCapable: Int? = ENABLED,
    val dkChat: Int? = DISABLED,
    val dynamicNavi: Int? = ENABLED,
    val ecoHistory: Int? = ENABLED,
    val ecoRanking: Int? = ENABLED,
    val evBattery: Int? = ENABLED,
    val evChargeStation: Int? = ENABLED,
    val evRemoteServices: Int? = ENABLED,
    val evSwap: Int? = ENABLED,
    val evTimerSettings: Int? = ENABLED,
    val evVehicleStatus: Int? = ENABLED,
    val wattTime: Int? = ENABLED,
    val financialServices: Int? = ENABLED,
    val flexRental: Int? = ENABLED,
    val howToVideos: Int? = ENABLED,
    val importantMessage: Int? = ENABLED,
    val insurance: Int? = ENABLED,
    val lastParked: Int? = ENABLED,
    val lcfs: Int? = ENABLED,
    val maintenanceTimeline: Int? = ENABLED,
    val marketingCard: Int? = ENABLED,
    val marketingConsent: Int? = ENABLED,
    val masterConsentEditable: Int? = ENABLED,
    val ownersManual: Int? = ENABLED,
    val paidProduct: Int? = ENABLED,
    val parking: Int? = ENABLED,
    val remoteParking: Int? = ENABLED,
    val recentTrip: Int? = ENABLED,
    val remoteDtc: Int? = ENABLED,
    val remoteService: Int? = ENABLED,
    val roadsideAssistance: Int? = ENABLED,
    val safetyRecall: Int? = ENABLED,
    val scheduleMaintenance: Int? = ENABLED,
    val serviceHistory: Int? = ENABLED,
    val shopGenuineParts: Int? = ENABLED,
    val ssaDownload: Int? = ENABLED,
    val sxmRadio: Int? = ENABLED,
    val telemetry: Int? = ENABLED,
    val tff: Int? = ENABLED,
    val tirePressure: Int? = ENABLED,
    val v1g: Int? = ENABLED,
    val vehicleDiagnostic: Int? = ENABLED,
    val vehicleHealthReport: Int? = ENABLED,
    val vehicleSpecifications: Int? = ENABLED,
    val vehicleStatus: Int? = ENABLED,
    val wifi: Int? = ENABLED,
    val wifiTrialReminderCard: Int? = ENABLED,
    val xcapp: Int? = ENABLED,
    val xcappV2: Int? = ENABLED,
    val personalizedSettings: Int? = ENABLED,
    val proXSeats: Int? = DISABLED,
    val myDestination: Int? = ENABLED,
    val linkedAccounts: Int? = ENABLED,
    val vaSetting: Int? = ENABLED,
    var digitalKey: Int? = DISABLED,
    var evPublicChargingControl: Int? = DISABLED,
    var wallet: Int? = DISABLED,
    val dtcAccess: Int? = DISABLED,
    val hybridPulse: Int? = DISABLED,
    val hybridTrips: Int? = DISABLED,
    val multiDayCharging: Int? = DISABLED,
    val serviceShopAdvancedSearch: Int? = DISABLED,
    val disableOneTimePayment: Int? = DISABLED,
    val disableRecurringPayment: Int? = DISABLED,
    val financialServicesV2: Int? = DISABLED,
    val evGo: Int? = DISABLED,
    val cdas: Int? = DISABLED,
    val evIonna: Int? = ENABLED,
    val teslaSuperChargerNetwork: Int? = DISABLED,
    val plugNCharge: Int? = DISABLED,
) : Parcelable {
    companion object {
        const val ENABLED = 1
        const val DISABLED = 0
        const val MAINTENANCE = 2
    }

    fun isFeatureEnabled(feature: Feature): Boolean =
        when (feature) {
            Feature.CHARGE_ASSIST -> chargeAssist == ENABLED
            Feature.AMBER_ALERT -> amberAlert == ENABLED
            Feature.CRITICAL_ALERTS -> criticalAlert == ENABLED
            Feature.INSURANCE -> insurance == ENABLED
            Feature.OWNERS_MANUAL -> ownersManual == ENABLED
            Feature.SERVICE_HISTORY -> serviceHistory == ENABLED
            Feature.SERVICE_HISTORY_ADD_RECORD -> addServiceRecord == ENABLED
            Feature.SHOP_GENUINE_PARTS -> shopGenuineParts == ENABLED
            Feature.WIFI -> wifi == ENABLED
            Feature.DEALER_APPOINTMENT -> dealerAppointment == ENABLED
            Feature.DOOR_LOCK_CAPABLE -> doorLockCapable == ENABLED
            Feature.DK_CHAT -> dkChat == ENABLED
            Feature.ROADSIDE_ASSISTANCE -> roadsideAssistance == ENABLED
            Feature.SCHEDULE_MAINTENANCE -> scheduleMaintenance == ENABLED
            Feature.TIRE_PRESSURE -> tirePressure == ENABLED
            Feature.VEHICLE_DIAGNOSTIC -> vehicleDiagnostic == ENABLED
            Feature.DRIVER_SCORE -> driverScore == ENABLED
            Feature.VEHICLE_HEALTH_REPORT -> vehicleHealthReport == ENABLED
            Feature.SXM_RADIO -> sxmRadio == ENABLED
            Feature.TELEMETRY -> telemetry == ENABLED
            Feature.RECENT_TRIP -> recentTrip == ENABLED
            Feature.REMOTE_DTC -> remoteDtc == ENABLED
            Feature.REMOTE_SERVICE -> remoteService == ENABLED
            Feature.HOW_TO_VIDEOS -> howToVideos == ENABLED
            Feature.VEHICLE_STATUS -> vehicleStatus == ENABLED
            Feature.XCAPP -> xcapp == ENABLED
            Feature.XCAPPV2 -> xcappV2 == ENABLED
            Feature.LAST_PARKED -> lastParked == ENABLED
            Feature.MARKETING -> marketingCard == ENABLED
            Feature.CONNECTED_SERVICE_INFO -> connectedCard == ENABLED
            Feature.DASHBOARD_LIGHTS -> dashboardLights == ENABLED
            Feature.SSA_DOWNLOAD -> ssaDownload == ENABLED
            Feature.PAID_SUBSCRIPTION -> paidProduct == ENABLED
            Feature.VEHICLE_SPECS -> vehicleSpecifications == ENABLED
            Feature.SAFETY_RECALL -> safetyRecall == ENABLED
            Feature.EV_BATTERY -> evBattery == ENABLED
            Feature.EV_CHARGE_STATION -> evChargeStation == ENABLED
            Feature.EV_REMOTE_SERVICE -> evRemoteServices == ENABLED
            Feature.EV_TIMER_SETTINGS -> evTimerSettings == ENABLED
            Feature.EV_VEHICLE_STATUS -> evVehicleStatus == ENABLED
            Feature.EV_WATT_TIME -> wattTime == ENABLED
            Feature.IMPORTANT_MESSAGE -> importantMessage == ENABLED
            Feature.MAINTENANCE_TIMELINE -> maintenanceTimeline == ENABLED
            Feature.FINANCIAL_SERVICES -> financialServices == ENABLED
            Feature.CONNECTED_SUPPORT -> connectedSupport == ENABLED
            Feature.LCFS -> lcfs == ENABLED
            Feature.AUTO_DRIVE -> autoDrive == ENABLED
            Feature.PARKING -> parking == ENABLED
            Feature.REMOTE_PARKING -> remoteParking == ENABLED
            Feature.FLEXRENTAL -> flexRental == ENABLED
            Feature.V1G -> v1g == ENABLED
            Feature.TOYOTA_FOR_FAMILIES -> tff == ENABLED
            Feature.ACH_PAYMENT -> achPayment == ENABLED
            Feature.ECO_HISTORY -> ecoHistory == ENABLED
            Feature.ECO_RANKING -> ecoRanking == ENABLED
            Feature.CERENCE -> cerence == ENABLED
            Feature.COLLISION_ASSISTANCE -> collisionAssistance == ENABLED
            Feature.EDIT_MASTER_CONSENT -> masterConsentEditable == ENABLED
            Feature.MARKETING_CONSENT -> marketingConsent == ENABLED
            Feature.DRIVER_COMPANION -> driverCompanion == ENABLED
            Feature.PERSONAL_SETTINGS -> personalizedSettings == ENABLED
            Feature.PRO_X_SEATS -> proXSeats == ENABLED
            Feature.MY_DESTINATION -> myDestination == ENABLED
            Feature.LINKED_ACCOUNTS -> linkedAccounts == ENABLED
            Feature.VA_SETTING -> vaSetting == ENABLED
            Feature.DIGITAL_KEY -> digitalKey == ENABLED
            Feature.WIFI_TRIAL_REMINDER -> wifiTrialReminderCard == ENABLED
            Feature.EV_PUBLIC_CHARGING_CONTROL -> evPublicChargingControl == ENABLED
            Feature.WALLET -> wallet == ENABLED
            Feature.DTC_ACCESS -> dtcAccess == ENABLED
            Feature.HYBRID_PULSE -> hybridPulse == ENABLED
            Feature.HYBRID_TRIPS -> hybridTrips == ENABLED
            Feature.MULTI_DAY_CHARGING -> multiDayCharging == ENABLED
            Feature.SERVICE_SHOP_ADVANCED_SEARCH -> serviceShopAdvancedSearch == ENABLED
            Feature.EV_SWAP -> evSwap == ENABLED
            Feature.TFS_DISABLE_ONETIME_PAYMENT -> disableOneTimePayment == ENABLED
            Feature.TFS_DISABLE_RECURRING_PAYMENT -> disableRecurringPayment == ENABLED
            Feature.TFS_FINANCIAL_SERVICES_V2 -> financialServicesV2 == ENABLED
            Feature.EV_GO -> evGo == ENABLED
            Feature.CAS -> cdas == ENABLED
            Feature.IONNA -> evIonna == ENABLED
            Feature.TESLA -> teslaSuperChargerNetwork == ENABLED
            Feature.PLUG_AND_CHARGE -> plugNCharge == ENABLED
        }

    fun isFeatureMaintenance(feature: Feature): Boolean =
        when (feature) {
            Feature.DIGITAL_KEY -> digitalKey == MAINTENANCE
            Feature.TFS_FINANCIAL_SERVICES_V2 -> financialServicesV2 == MAINTENANCE
            else -> false
        }
}

enum class Feature(
    val featureName: String,
) {
    AMBER_ALERT("amberAlert"),
    CHARGE_ASSIST("chargeAssist"),
    CRITICAL_ALERTS("criticalAlert"),
    INSURANCE("insurance"),
    OWNERS_MANUAL("ownersManual"),
    SERVICE_HISTORY("serviceHistory"),
    SERVICE_HISTORY_ADD_RECORD("addServiceRecord"),
    SHOP_GENUINE_PARTS("shopGenuineParts"),
    WIFI("wifi"),
    DEALER_APPOINTMENT("dealerAppointment"),
    DK_CHAT("dkChat"),
    DOOR_LOCK_CAPABLE("doorLockCapable"),
    ROADSIDE_ASSISTANCE("roadsideAssistance"),
    SCHEDULE_MAINTENANCE("scheduleMaintenance"),
    TIRE_PRESSURE("tirePressure"),
    VEHICLE_DIAGNOSTIC("vehicleDiagnostic"),
    DRIVER_SCORE("driverScore"),
    VEHICLE_HEALTH_REPORT("vehicleHealthReport"),
    SXM_RADIO("sxmRadio"),
    TELEMETRY("telemetry"),
    RECENT_TRIP("recentTrip"),
    REMOTE_DTC("remoteDtc"),
    REMOTE_SERVICE("remoteService"),
    HOW_TO_VIDEOS("howToVideos"),
    VEHICLE_STATUS("vehicleStatus"),
    XCAPP("xcapp"),
    XCAPPV2("xcappV2"),
    LAST_PARKED("lastParked"),
    MARKETING("marketingCard"),
    CONNECTED_SERVICE_INFO("connectedCard"),
    DASHBOARD_LIGHTS("dashboardLights"),
    SSA_DOWNLOAD("ssaDownload"),
    PAID_SUBSCRIPTION("paidProduct"),
    VEHICLE_SPECS("vehicleSpecifications"),
    SAFETY_RECALL("safetyRecall"), // TODO Check Where to Use this
    EV_BATTERY("evBattery"),
    EV_CHARGE_STATION("evChargeStation"),
    EV_REMOTE_SERVICE("evRemoteServices"),
    EV_TIMER_SETTINGS("evTimerSettings"),
    EV_VEHICLE_STATUS("evVehicleStatus"),
    IMPORTANT_MESSAGE("importantMessage"),
    MAINTENANCE_TIMELINE("maintenanceTimeline"),
    FINANCIAL_SERVICES("financialServices"),
    CONNECTED_SUPPORT("connectedSupport"),
    AUTO_DRIVE("autoDrive"),
    PARKING("parking"),
    REMOTE_PARKING("remoteParking"),
    FLEXRENTAL("flexRental"),
    TOYOTA_FOR_FAMILIES("tff"),
    ACH_PAYMENT("achPayment"),
    ECO_HISTORY("ecoHistory"),
    ECO_RANKING("ecoRanking"),
    CERENCE("cerence"),
    EDIT_MASTER_CONSENT("masterConsentEditable"),
    COLLISION_ASSISTANCE("collisionAssistance"),
    LCFS("lcfs"),
    V1G("v1g"),
    MARKETING_CONSENT("MARKETING_CONSENT"),
    DRIVER_COMPANION("driverCompanion"),
    PERSONAL_SETTINGS("personalizedSettings"),
    PRO_X_SEATS("proXSeats"),
    MY_DESTINATION("myDestination"),
    LINKED_ACCOUNTS("linkedAccounts"),
    VA_SETTING("vaSetting"),
    DIGITAL_KEY("digitalKey"),
    WIFI_TRIAL_REMINDER("wifiTrialReminderCard"),
    EV_PUBLIC_CHARGING_CONTROL("evPublicChargingControl"),
    WALLET("wallet"),
    DTC_ACCESS("dtcAccess"),
    HYBRID_PULSE("hybridPulse"),
    HYBRID_TRIPS("hybridTrips"),
    MULTI_DAY_CHARGING("multiDayCharging"),
    SERVICE_SHOP_ADVANCED_SEARCH("serviceShopAdvancedSearch"),
    EV_SWAP("evSwap"),
    EV_WATT_TIME("wattTime"),
    TFS_DISABLE_ONETIME_PAYMENT("disableOneTimePayment"),
    TFS_DISABLE_RECURRING_PAYMENT("disableRecurringPayment"),
    TFS_FINANCIAL_SERVICES_V2("financialServicesV2"),
    EV_GO("evGo"),
    CAS("cdas"),
    IONNA("evIonna"),
    TESLA("teslaSuperChargerNetwork"),
    PLUG_AND_CHARGE("plugNCharge"),
    ;

    companion object {
        fun getFeatureList(): EnumSet<Feature> = EnumSet.allOf(Feature::class.java)
    }
}
