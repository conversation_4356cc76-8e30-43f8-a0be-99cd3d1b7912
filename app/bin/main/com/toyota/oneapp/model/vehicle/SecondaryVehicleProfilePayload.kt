package com.toyota.oneapp.model.vehicle

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class SecondaryVehicleProfilePayload(
    @SerializedName("vin") val vin: String?,
    @SerializedName("generation") val generation: String?,
    @SerializedName("region") val region: String?,
    @SerializedName("modelName") val modelName: String?,
    @SerializedName("modelYear") val modelYear: String?,
    @SerializedName("role") val role: String?,
    @SerializedName("brand") val brand: String?,
    @SerializedName("modelDescription") val modelDescription: String?,
    @SerializedName("image") val image: String?,
) : Parcelable
