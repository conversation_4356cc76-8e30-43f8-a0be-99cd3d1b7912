package com.toyota.oneapp.model.vehicle

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class OwnersManualResponse(
    @SerializedName("payload") val payload: OwnersManualPayload,
) : Parcelable

@Parcelize
data class OwnersManualPayload(
    @SerializedName("documents") val documents: OwnerManualList,
    @SerializedName("model") val model: String,
    @SerializedName("modelYear") val modelYear: String,
    @SerializedName("make") val make: String,
    @SerializedName("translations") val translations: Translations?,
    @SerializedName("pdfLink") val pdfLink: String? = null,
    @SerializedName("appSubTitle") val appTitle: String? = null,
) : Parcelable

@Parcelize
data class OwnerManualList(
    @SerializedName("omms") val warrantiesAndMaintenance: ArrayList<OwnerManualItem>? = null,
    @SerializedName("omnav") val audioAndNavigation: ArrayList<OwnerManualItem>? = null,
    @SerializedName("om") val ownersManual: ArrayList<OwnerManualItem>? = null,
    @SerializedName("manuals") val ownersManualsForCanada: ArrayList<OwnerManualItem>? = null,
) : Parcelable

@Parcelize
data class OwnerManualItem(
    @SerializedName("summary") val manualSummary: String? = null,
    @SerializedName("documentUrl") val manualUrl: String? = null,
    @SerializedName("documentType") val documentType: String? = null,
    @SerializedName("title") val manualTitle: String? = null,
) : Parcelable

@Parcelize
data class Translations(
    @SerializedName("omms") val ommsName: String? = null,
    @SerializedName("omnav") val omnavName: String? = null,
    @SerializedName("om") val omName: String? = null,
    @SerializedName("manuals") val manualTitle: String? = null,
) : Parcelable

// Function to check if OwnerManual data available.
fun OwnersManualPayload.isOwnerManualDataAvailable(): Boolean {
    return documents != null &&
        (
            !documents.audioAndNavigation.isNullOrEmpty() ||
                !documents.ownersManual.isNullOrEmpty() ||
                !documents.warrantiesAndMaintenance.isNullOrEmpty() ||
                !documents.ownersManualsForCanada.isNullOrEmpty()
        )
}
