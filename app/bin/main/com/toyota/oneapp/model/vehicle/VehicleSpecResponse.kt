package com.toyota.oneapp.model.vehicle

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

data class VehicleSpecResponse(
    val payload: VehicleSpecs?,
)

@Parcelize
data class VehicleSpecs(
    val vehicleSpecifications: VehicleSpecSection?,
    val additionalDetails: VehicleSpecSection?,
    val factoryInstalledEquipment: VehicleSpecSection?,
    val standardInstalledEquipment: VehicleSpecSection?,
) : Parcelable

@Parcelize
data class VehicleSpecSection(
    val headerName: String?,
    @SerializedName("dataItems") val items: List<VehicleSpecData>?,
) : Parcelable

@Parcelize
data class VehicleSpecData(
    @SerializedName("dataName") val name: String?,
    @SerializedName("dataValue") val value: String?,
) : Parcelable
