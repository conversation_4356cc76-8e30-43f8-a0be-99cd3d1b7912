package com.toyota.oneapp.model.vehicle

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class ExtendedCapabilities(
    val remoteEngineStartStop: Boolean,
    val remoteEConnectCapable: Boolean,
    val doorLockUnlockCapable: <PERSON><PERSON>an,
    val frontDriverDoorLockStatus: <PERSON>olean,
    val frontPassengerDoorLockStatus: <PERSON>olean,
    val rearDriverDoorLockStatus: <PERSON>olean,
    val frontDriverDoorOpenStatus: <PERSON>olean,
    val frontPassengerDoorOpenStatus: <PERSON>olean,
    val rearDriverDoorOpenStatus: <PERSON>olean,
    val rearPassengerDoorOpenStatus: <PERSON>olean,
    val frontDriverDoorWindowStatus: <PERSON>olean,
    val frontPassengerDoorWindowStatus: <PERSON>olean,
    val rearDriverDoorWindowStatus: Boolean,
    val rearPassengerDoorWindowStatus: <PERSON><PERSON><PERSON>,
    val rearHatchRearWindow: <PERSON>olean,
    val moonroof: Boolean,
    val powerWindowsCapable: Boolean,
    val hazardCapable: Boolean,
    val hornCapable: <PERSON>olean,
    val lightsCapable: Boolean,
    val climateCapable: Boolean,
    val climateTemperatureControlFull: Boolean,
    val climateTemperatureControlLimited: Boolean,
    val frontDriverSeatHeater: Boolean,
    val frontPassengerSeatHeater: Boolean,
    val rearDriverSeatHeater: Boolean,
    val rearPassengerSeatHeater: Boolean,
    val frontDriverSeatVentilation: Boolean,
    val frontPassengerSeatVentilation: Boolean,
    val rearDriverSeatVentilation: Boolean,
    val rearPassengerSeatVentilation: Boolean,
    val steeringHeater: Boolean,
    val mirrorHeater: Boolean,
    val frontDefogger: Boolean,
    val rearDefogger: Boolean,
    val vehicleFinder: Boolean,
    val guestDriver: Boolean,
    val buzzerCapable: Boolean,
    val trunkLockUnlockCapable: Boolean,
    val evChargeStationsCapable: Boolean,
    val fcvStationsCapable: Boolean,
    val lastParkedCapable: Boolean,
    val acScheduling: Boolean,
    val chargeManagement: Boolean,
    val nextCharge: Boolean,
    val weeklyCharge: Boolean,
    val powerTailgateCapable: Boolean,
) : Parcelable
