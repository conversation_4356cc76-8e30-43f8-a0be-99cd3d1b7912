package com.toyota.oneapp.model.vehicle

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.model.MultiLanguageString
import kotlinx.parcelize.Parcelize

@Parcelize
data class CapabilityNew(
    @SerializedName("name") val name: String?,
    @SerializedName("displayName") val displayName: String?,
    @SerializedName("description") val description: String?,
    @SerializedName("translation") val translation: MultiLanguageString?,
    @SerializedName("value") val value: Boolean?,
    @SerializedName("display") val display: Boolean? = true,
    // Default should display
) : Parcelable {
    constructor(capabilityItem: CapabilityItem) : this(
        name = capabilityItem.name,
        displayName = capabilityItem.displayName,
        description = "",
        translation = capabilityItem.translation,
        value = capabilityItem.value,
        display = capabilityItem.display,
    )

    // For unit test
    constructor(displayName: String?) : this(
        name = "",
        displayName = displayName,
        description = "",
        translation = null,
        value = true,
        display = true,
    )
}
