package com.toyota.oneapp.model.vehicle

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class VehicleReportResponse(
    var safetyRecallsList: List<SafetyRecall>?,
    var serviceCampaigns: List<ServiceCampaign>?,
    var vehicleAlertList: List<VehicleAlert>?,
    var maintenanceInformation: MaintenanceInformation?,
    var vehicleStatus: VehicleStatus?,
    var auditTrail: VehicleHealthReportInfo?,
    var safetyRecall: SafetyRecallResponse?,
    var campaignsExists: Boolean?,
    var recallsListExists: Boolean?,
) : Parcelable
