package com.toyota.oneapp.model.vehicle

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class TelemetryItem(
    val unit: String,
    @SerializedName("value")
    val _value: Double,
) : Parcelable {
    val value: Double
        get() = _value

    fun hasTirePressureWarning(): Boolean {
        return value < 30
    }
}
