package com.toyota.oneapp.model.vehicle

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.model.MultiLanguageString
import kotlinx.parcelize.Parcelize

@Parcelize
data class CapabilityItem(
    @SerializedName("name") val name: String?,
    @SerializedName("value") val value: Boolean?,
    @SerializedName("display") val display: Boolean? = true, // De<PERSON>ult should display
    @SerializedName("displayName") val displayName: String?,
    @SerializedName("translation") val translation: MultiLanguageString?,
) : Parcelable
