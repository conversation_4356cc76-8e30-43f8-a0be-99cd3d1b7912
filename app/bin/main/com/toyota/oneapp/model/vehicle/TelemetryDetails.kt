package com.toyota.oneapp.model.vehicle

import java.util.*

data class TelemetryDetails(
    val driverWindow: Int,
    val flTirePressure: TelemetryItem,
    val frTirePressure: TelemetryItem,
    val fuelLevel: Int,
    val lastTimestamp: Date?,
    val odometer: TelemetryItem?,
    val passengerWindow: Int,
    val rlTirePressure: TelemetryItem,
    val rlWindow: Int,
    val rrTirePressure: TelemetryItem,
    val rrWindow: Int,
    val spareTirePressure: TelemetryItem,
    val nextService: TelemetryItem,
    val speed: TelemetryItem,
    val sunRoof: Int,
    val vehicleName: String,
    val vin: String,
    val displayNextService: Boolean,
    val distanceToEmpty: TelemetryItem?,
    var displayDistanceToEmpty: Boolean?,
    var vehicleLocation: VehicleLocation?,
)
