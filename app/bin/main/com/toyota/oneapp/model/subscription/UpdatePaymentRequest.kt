package com.toyota.oneapp.model.subscription

open class UpdatePaymentRequest(
    val cardHolderName: String? = null,
    val expirationMonth: Int? = null,
    val expirationYear: Int? = null,
    val defaultPaymentMethod: Boolean? = null,
    val securityCode: String? = null,
    val addressLine1: String? = null,
    val addressLine2: String? = null,
    val city: String? = null,
    val state: String? = null,
    val zipCode: String? = null,
    val country: String? = null,
    val paymentMethodNickname: String? = null,
    val email: String? = null,
    val phone: String? = null,
)

class SetDefaultPaymentRequest : UpdatePaymentRequest(
    defaultPaymentMethod = true,
)
