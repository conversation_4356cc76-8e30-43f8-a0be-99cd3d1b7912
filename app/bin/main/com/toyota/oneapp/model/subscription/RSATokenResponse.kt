package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class RSATokenResponse(val payload: RSATokenResponsePayload) : Parcelable

@Parcelize
data class RSATokenResponsePayload(
    val signature: String,
    val token: String,
    val tenantId: String,
    val key: String,
    val method: String,
    val pageId: String,
    val uri: String,
    val host: String?,
    val paymentGateway: String?,
) : Parcelable
