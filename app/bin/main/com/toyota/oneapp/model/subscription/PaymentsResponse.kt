package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class PaymentsResponse(
    val payload: PaymentsPayload,
) : Parcelable

@Parcelize
class PaymentsPayload(
    val success: Boolean?,
    val records: ArrayList<PaymentRecord>?,
) : Parcelable

@Parcelize
class PaymentRecord(
    val id: String?,
    val defaultPaymentMethod: Boolean? = false,
    val accountId: String? = null,
    val lastTransactionDateTime: String? = null,
    val creditCardHolderName: String? = null,
    val active: Boolean? = false,
    val creditCardType: String? = null,
    val updatedById: String? = null,
    val createdDate: String? = null,
    val useDefaultRetryRule: Boolean? = false,
    val lastTransactionStatus: String? = null,
    val bankIdentificationNumber: String? = null,
    val paymentMethodStatus: String? = null,
    val updatedDate: String? = null,
    val createdById: String? = null,
    val type: String? = null,
    val totalNumberOfErrorPayments: Int? = 0,
    val totalNumberOfProcessedPayments: Int? = 0,
    val creditCardExpirationMonth: Int? = 0,
    val creditCardMaskNumber: String? = null,
    val creditCardExpirationYear: Int? = 0,
    val achAccountNumberMask: String? = null,
    val creditCardAddress1: String? = null,
    val creditCardCity: String? = null,
    val creditCardState: String? = null,
    val creditCardPostalCode: String? = null,
    val creditCardCountry: String? = null,
    val achCardAddress1: String? = null,
    val achCity: String? = null,
    val achState: String? = null,
    val achPostalCode: String? = null,
    val achCountry: String? = null,
) : Parcelable
