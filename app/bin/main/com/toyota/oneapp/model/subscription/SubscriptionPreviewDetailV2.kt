package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import com.toyota.oneapp.model.DataConsent
import com.toyota.oneapp.model.RemoteUser
import com.toyota.oneapp.model.combineddataconsent.ConsentRequestItem
import kotlinx.parcelize.Parcelize

@Parcelize
data class SubscriptionPreviewDetailV2(
    var accessToken: String? = null,
    var paymentMethodId: String? = null,
    var contractID: String? = null,
    var paymentToken: String? = null,
    var waiver: Boolean? = null,
    var setAsDefaultPaymentMethod: Boolean? = null,
    var isCPOEligible: Boolean? = null,
    var isPPOEligible: Boolean? = null,
    var isService: Boolean? = null,
    var subscriptions: ArrayList<PreviewSubscriptionItemV2>? = null,
    var externalSubscriptions: ArrayList<PreviewSubscriptionItem?>? = null,
    var dataConsent: DataConsent? = null,
    var remoteUser: RemoteUser? = null,
    var productsAmount: ProductsAmount? = null,
    var productsTotalAmount: ProductsTotalAmount? = null,
    var taxAmount: TaxAmount? = null,
    var consents: List<ConsentRequestItem>? = null,
) : Parcelable
