package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class ProductsTotalAmount(
    var currency: String = "USD",
    var amount: Double = 0.0,
) : Parcelable

@Parcelize
data class ProductsAmount(
    var currency: String = "USD",
    var amount: Double = 0.0,
) : Parcelable

@Parcelize
data class TaxAmount(
    var currency: String = "USD",
    var amount: Double = 0.0,
) : Parcelable
