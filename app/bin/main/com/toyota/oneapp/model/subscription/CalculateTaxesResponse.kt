package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CalculateTaxesResponse(val payload: TaxesResponsePayload) : Parcelable

@Parcelize
data class TaxesResponsePayload(
    val totalAmount: Double,
    val totalTaxAmount: Double,
    val totalAmountWithoutTax: Double,
    val invoiceItems: List<InvoiceItems>?,
) : Parcelable

@Parcelize
data class InvoiceItems(
    val productName: String,
    val amountWithoutTax: String,
    val taxAmount: String,
    val taxationItems: List<TaxationItems>?,
) : Parcelable

@Parcelize
data class TaxationItems(
    val taxAmount: Double = 0.00,
    val taxCode: String = "",
    val taxCodeDescription: String = "",
) : Parcelable
