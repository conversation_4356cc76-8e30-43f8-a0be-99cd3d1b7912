package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class SubscriptionPrices(
    val name: String,
    val prices: MutableList<ProductPrice>,
    var checked: Boolean,
    var previewItem: PreviewSubscriptionItem?,
) : Parcelable

@Parcelize
data class ProductPrice(
    val price: Double,
    val termUnit: String,
    val term: Int,
    var checked: Boolean,
    var ratePlanId: String,
    var packageID: String,
    var productID: String,
    var displayTerm: String,
    var subscriptionEndDate: String,
    var subscriptionStartDate: String,
) : Parcelable
