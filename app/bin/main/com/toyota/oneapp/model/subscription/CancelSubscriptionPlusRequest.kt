package com.toyota.oneapp.model.subscription

import com.toyota.oneapp.model.subscriptionV2.SubscriptionV2

class CancelSubscriptionPlusRequest(
    var subscriberGuid: String,
    var vin: String,
    var generation: String,
    var subscription: SubscriptionIDField,
    // External Subscriptions - will not be cancelled, but this field is required by backend to send email to user on how to cancel external products.
    var externalSubscriptions: List<SubscriptionV2>,
    var canceledReason: String?,
    var refundPaymentMethodId: String?,
    var refundPaymentType: String?,
) {
    var vehicleStatus = "CANCELED"
}
