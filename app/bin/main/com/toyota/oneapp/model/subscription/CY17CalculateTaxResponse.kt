package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CY17CalculateTaxResponse(val payload: CY17TaxesResponsePayload) : Parcelable

@Parcelize
data class CY17TaxesResponsePayload(val taxResponse: TaxResponse) : Parcelable

@Parcelize
data class TaxResponse(
    val taxedProducts: List<TaxedProducts>,
    val taxJurisdiction: TaxJurisdiction,
    val productTotalAmount: CY17Price,
    val taxTotalAmount: CY17Price,
    val totalPurchaseAmount: CY17Price,
    val taxationItems: List<TaxationItems>?,
) : Parcelable

@Parcelize
data class TaxedAmount(
    val type: String,
    val totalAmount: CY17Price,
) : Parcelable

@Parcelize
data class TaxedProducts(
    val productOfferId: String,
    val productPrice: CY17Price,
    val taxAmount: TaxedAmount,
    val totalAmount: CY17Price,
) : Parcelable

@Parcelize
data class TaxJurisdiction(
    val city: String,
    val state: String,
    val country: String,
    val postalCode: String,
) : Parcelable
