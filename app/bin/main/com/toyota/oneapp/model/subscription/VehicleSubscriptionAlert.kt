package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class VehicleSubscriptionAlert(
    val category: String?,
    val title: String?,
    val subtitle: String?,
    val body: String?,
    val url: String?,
    val image: String?,
) : Parcelable {
    companion object {
        lateinit var CREATOR: Parcelable.Creator<VehicleSubscriptionAlert>
    }
}
