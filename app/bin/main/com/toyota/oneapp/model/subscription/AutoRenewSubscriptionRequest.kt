package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class AutoRenewSubscriptionRequest(
    val vin: String,
    val subscriberGuid: String,
    val generation: String,
    val subscriptions: ArrayList<AutoRenew>,
) : Parcelable

@Parcelize
data class AutoRenew(
    val subscriptionId: String,
    val autoRenew: Boolean,
) : Parcelable
