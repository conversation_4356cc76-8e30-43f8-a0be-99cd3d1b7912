package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class MusicPreferencePayload(
    @SerializedName("userid")
    val userId: String?,
    @SerializedName("streamingquality")
    var streamingQuality: String?,
    @SerializedName("explicitsongs")
    var explicitSongs: Boolean?,
    @SerializedName("mediasource")
    var mediaSource: Int,
) : Parcelable

data class MusicSubscriptionRequest(
    @SerializedName("authCode")
    val authCode: String? = null,
    @SerializedName("userProfileID")
    val userProfileID: String? = null,
    @SerializedName("authServerName")
    val authServerName: String? = null,
    @SerializedName("redirect_uri")
    val redirectUri: String? = null,
)
