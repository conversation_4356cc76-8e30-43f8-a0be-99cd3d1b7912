package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.parcelize.Parcelize
import java.util.Date
import kotlin.collections.ArrayList

@Parcelize
data class CancellationDataResponse(
    val payload: CancellationDataPayload,
) : BaseResponse(),
    Parcelable

@Parcelize
data class CancellationDataPayload(
    val previewResult: PreviewResult?,
    val records: PaymentRecord?,
) : Parcelable

@Parcelize
data class PreviewResult(
    val creditMemos: ArrayList<CreditMemo>? = arrayListOf(),
) : Parcelable

@Parcelize
data class CreditMemo(
    val amount: Double?,
    val amountWithoutTax: Double?,
    val taxAmount: Double?,
    val targetDate: String?,
    val creditMemoItems: ArrayList<CreditMemoItems>? = arrayListOf(),
) : Parcelable

@Parcelize
data class CreditMemoItems(
    val serviceStartDate: Date?,
    val serviceEndDate: Date?,
    val amountWithoutTax: Double? = 0.0,
    val taxAmount: Double? = 0.0,
    val chargeName: String?,
    val productName: String?,
    val refundEligibilityStatus: Boolean?,
    val productRatePlanChargeId: String?,
    val subscriptionNumber: String?,
    val reasonCode: String?,
) : Parcelable
