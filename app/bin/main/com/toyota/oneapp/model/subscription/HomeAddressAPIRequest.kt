package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.ArrayList

@Parcelize
data class HomeAddressAPIRequest(
    @SerializedName("addresses") val addresses: ArrayList<AddressList>,
) : Parcelable

@Parcelize
data class AddressList(
    @SerializedName("addressType") val addressType: String,
    @SerializedName("city") val city: String,
    @SerializedName("country") val country: String,
    @SerializedName("state") val state: String,
    @SerializedName("zipCode") val zipCode: String,
    @SerializedName("address") val address: String,
) : Parcelable
