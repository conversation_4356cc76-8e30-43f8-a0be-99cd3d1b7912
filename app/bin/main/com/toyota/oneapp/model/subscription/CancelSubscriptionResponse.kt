package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.parcelize.Parcelize

@Parcelize
data class CancelSubscriptionResponse(val payload: CancelSubscriptionPayload?) : BaseResponse(), Parcelable

@Parcelize
data class CancelSubscriptionPayload(
    val refundResponse: RefundResponse?,
) : Parcelable

@Parcelize
data class RefundResponse(
    val id: String?,
    val type: String? = ELECTRONIC, // "Electronic" / "ManualCheck"
    val amount: Double?,
    val amountWithoutTax: Double?,
    val taxAmount: Double?,
    val refundDate: String?,
    val targetDate: String?,
    val currency: String?,
    val paymentMethod: PaymentMethod?,
    val reasons: List<CancellationReasons>?,
) : Parcelable {
    companion object {
        const val ELECTRONIC = "Electronic"
        const val MANUALCHECK = "ManualCheck"
    }
}

@Parcelize
data class PaymentMethod(
    val methodType: String?,
    val id: String?,
) : Parcelable

@Parcelize
data class CancellationReasons(
    val code: String?,
    val message: String?,
) : Parcelable
