package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CY17CalculateTaxRequest(
    val billingAddress: BillingAddress,
    val taxableProducts: List<CY17TaxableProducts>,
    val totalProductPrice: CY17Price,
    val vin: String,
) : Parcelable

@Parcelize
data class BillingAddress(
    val postalCode: String,
    val country: String,
    val state: String,
    val street: String,
    val city: String,
    val addressLine2: String = "",
) : Parcelable

@Parcelize
data class CY17TaxableProducts(
    val productOfferId: String,
    val productPrice: CY17Price,
    val totalProductPrice: CY17Price,
) : Parcelable

@Parcelize
data class CY17Price(
    val currency: String = "",
    val amount: Double,
) : Parcelable
