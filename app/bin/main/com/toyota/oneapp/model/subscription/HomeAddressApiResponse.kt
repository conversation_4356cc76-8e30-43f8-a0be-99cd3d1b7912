package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.network.models.BaseResponse
import kotlinx.parcelize.Parcelize

@Parcelize
data class HomeAddressApiResponse(
    @SerializedName("payload") val payload: Payload,
) : BaseResponse(), Parcelable

@Parcelize
data class Payload(
    @SerializedName("addresses") val addresses: ArrayList<Addresses>,
) : Parcelable

@Parcelize
data class Addresses(
    @SerializedName("addressType") val addressType: String,
    @SerializedName("city") val city: String,
    @SerializedName("state") val state: String,
    @SerializedName("zipCode") val zipCode: String,
    @SerializedName("country") val country: String,
    @SerializedName("resultNumber") val resultNumber: String,
    @SerializedName("resultPercentage") val resultPercentage: String? = "0.00",
    @SerializedName("address1") val address1: String,
) : Parcelable

@Parcelize
data class AddressEnteredByUser(
    val enteredCity: String?,
    val enteredState: String?,
    val enteredZipCode: String?,
    val enteredCountry: String?,
    val streetAddress: String?,
) : Parcelable
