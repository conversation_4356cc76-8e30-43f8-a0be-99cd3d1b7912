package com.toyota.oneapp.model.subscription

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CalculateTaxesRequest(
    val city: String,
    val state: String,
    val country: String,
    val orderDate: String,
    val subscriptions: List<TaxableProducts>,
    val postalCode: String,
) : Parcelable

@Parcelize
data class TaxableProducts(val ratePlanID: String, val period: Int, val periodType: String) : Parcelable
