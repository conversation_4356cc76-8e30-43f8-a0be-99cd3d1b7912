package com.toyota.oneapp.digitalkey

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.os.Parcelable
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.LiveData
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.app.DataDogUtils
import com.toyota.oneapp.app.ToyotaApplication
import com.toyota.oneapp.component.SingletonHolder
import com.toyota.oneapp.digitalkey.ui.DigitalKeyShareDialogFragment
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.PendingInvite
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.newdashboard.DashboardActivity
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import jp.co.denso.dkctllib.DKCtlLib
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import toyotaone.commonlib.log.LogTool
import java.util.Calendar
import java.util.Date

class DigitalMopKeyUtils private constructor(
    private val context: Context,
) {
    var registerVehicleKeyInfoId: String = ToyotaConstants.EMPTY_STRING

    var currentVehicle: String = ToyotaConstants.EMPTY_STRING

    private val mDkMopStatus = SingleLiveEvent<DkMopResultStatus>()
    val dkMopStatus: LiveData<DkMopResultStatus> get() = mDkMopStatus

    var isSyncCompleted: Boolean = false
    var dKLib: DKLib? = null
    var dKCtlLib: DKCtlLib? = null

    var isCurrentDigitalKeyProcess = false

    private var digitalKeyLocalData: DigitalKeyLocalData? = null

    private lateinit var sharedPreferences: SharedPreferences

    var isSecondaryDkAccepted = false

    val isSecondaryDkAcceptedState = MutableStateFlow(value = false)
    val isSecondaryAccepted = isSecondaryDkAcceptedState.asStateFlow()

    var isPrimaryLukSelected = false

    enum class KeyStatus(
        val status: String,
    ) {
        INACTIVE("INACTIVE"),
        ROTATION("ROTATION"),
    }

    enum class KeyType(
        val type: String,
    ) {
        OWNER("ownertoken"),
        ROTATION_KEY("rotatetoken"),
    }

    companion object : SingletonHolder<DigitalMopKeyUtils, Context>(::DigitalMopKeyUtils) {
        const val TAG = "DKApp::DKLib"
        const val DK_REVOKE_TAG = "DKApp::REVOKE"
        const val DK_INVITE_TAG = "DKApp::INVITE"
        const val DK_ACTIVATE_TAG = "DKApp::ACTIVE"
        const val DK_REGISTRATION_TAG = "DKApp::Registration"
        const val DK_RECEIVE_TAG = "DKApp::RECEIVE"
        const val DK_KEY_STATUS = "DKApp::DigitalKey Status"
        const val DK_OWNER_TOKEN_TYPE = "OWNER"
        const val DK_DOWNLOAD_SUCCESS_STATUS = "PRIMARY_KEY_INSTALL_SUCCESS"
        const val DK_OWNER_LUK_TOKEN_TYPE = "OWNERLUK"
        const val DK_LUK_TOKEN_TYPE = "LUK"
        const val ACTION_INITIALIZE = "DK_INITIALIZE"
        const val ACTION_INSTALL_KEY = "INSTALL_KEY"
        const val ACTION_DOWNLOAD_RESULT = "DK_DOWNLOAD_RESULT"
        const val ACTION_DOWNLOAD_PROGRESS = "DK_DOWNLOAD_PROGRESS"
        const val ACTION_KEYINFO_CHANGE = "ACTION_KEYINFO_CHANGE"
        const val ACTION_DK_BLE_LINK_STATUS_CHANGE = "ACTION_DK_BLE_LINK_STATUS_CHANGE"
        const val ACTION_SYNC_KEY_RESULT = "DK_SYNC_KEY_RESULT"
        const val ACTION_SYNC_ALL_KEY_RESULT = "DK_SYNC_ALL_KEY_RESULT"
        const val ACTION_DK_DOWNLOAD_FINISH = "ACTION_DK_DOWNLOAD_FINISH"
        const val ACTION_REGISTER_VEHICLE_RESULT = "DK_REGISTER_VEHICLE_RESULT"
        const val ACTION_CONNECT_VEHICLE_RESULT = "DK_CONNECT_VEHICLE_RESULT"
        const val ACTION_REMOTE_REGISTER = "DK_REMOTE_REGISTER"
        const val ACTION_REMOTE_DEACTIVATE = "DK_REMOTE_DEACTIVATE"
        const val ACTION_SHARE_DK_SYNC = "ACTION_SHARE_DK_SYNC"
        const val ACTION_DK_SYNC = "ACTION_DK_SYNC"
        const val ACTION_REMOTE_BLE_ON = "DK_REMOTE_BLE_ON"
        const val ACTION_REMOTE_BLE_OFF = "DK_REMOTE_BLE_OFF"
        const val ACTION_REMOTE_AUTH = "ACTION_REMOTE_AUTH"
        const val KEY_INFO_ID = "keyInfoId"
        const val KEY_KIND = "keyKind"
        const val S_RESULT = "S_RESULT"
        const val S_DETAIL = "S_DETAIL"
        const val IS_SUCCESS = "isSuccess"
        const val REGISTRATION_RESULT = "REGISTRATION_RESULT"
        const val DEACTIVATE_RESULT = "DEACTIVATE_RESULT"
        const val IS_SETUP_FLOW = "IS_SETUP_FLOW"
        const val IS_SHARE_FLOW = "IS_SHARE_FLOW"
        const val DEVICE_ID = "D8BBB23F7B082FA761AF9B6BDEF2FE25B8FADC7B84ED3BB4BE78904E7F358709"
        const val CALIBRATION_VALUE = 16
        val DK_FILTER =
            IntentFilter().apply {
                addAction(ACTION_INITIALIZE)
                addAction(ACTION_DOWNLOAD_RESULT)
                addAction(ACTION_SYNC_KEY_RESULT)
                addAction(ACTION_KEYINFO_CHANGE)
                addAction(ACTION_SYNC_ALL_KEY_RESULT)
                addAction(ACTION_REGISTER_VEHICLE_RESULT)
                addAction(ACTION_DOWNLOAD_PROGRESS)
                addAction(ACTION_REMOTE_REGISTER)
                addAction(ACTION_INSTALL_KEY)
                addAction(ACTION_REMOTE_DEACTIVATE)
                addAction(ACTION_REMOTE_BLE_ON)
                addAction(ACTION_REMOTE_BLE_OFF)
            }
        val digitalLog: StringBuffer by lazy { StringBuffer() }

        private fun FragmentManager.safeCommit(action: FragmentTransaction.() -> Unit) {
            if (!isStateSaved) {
                beginTransaction().apply(action).commit()
            }
        }

        @JvmStatic
        fun showPersonaTaskDialog(
            pendingInvites: PendingInvite,
            activity: FragmentActivity,
            isFromDashboard: Boolean = false,
        ) {
            if (pendingInvites.status == "SMS_SENT") {
                appendLog("ShowPersonaTaskDialog $pendingInvites")
                val digitalKeyShareDialogFragment =
                    DigitalKeyShareDialogFragment.newInstance(
                        pendingInvites,
                        isFromDashboard,
                    )
                CoroutineScope(Dispatchers.Main).launch {
                    if (!activity.isFinishing && !activity.supportFragmentManager.isStateSaved && !digitalKeyShareDialogFragment.isAdded) {
                        digitalKeyShareDialogFragment.isCancelable = false
                        activity.supportFragmentManager.safeCommit {
                            digitalKeyShareDialogFragment.show(
                                activity.supportFragmentManager,
                                DigitalKeyShareDialogFragment.TAG,
                            )
                        }
                    }
                }
            }
        }

        @JvmStatic
        fun appendLog(
            logInfo: String,
            tag: String = TAG,
            isDataDogRequired: Boolean = false,
            isError: Boolean = false,
            category: String? = null,
            logLevel: String? = null,
        ) {
            if (isDataDogRequired) {
                val dataDogUtils = DataDogUtils.getInstance(ToyotaApplication.getAppContext())
                category?.let {
                    dataDogUtils.setCategory(it)
                }
                logLevel?.let {
                    dataDogUtils.setLogLevel(it)
                }
                if (isError) {
                    dataDogUtils.logError(tag, logInfo)
                } else {
                    dataDogUtils.logInfo(tag, logInfo)
                }
            }
            if (BuildConfig.DEBUG) {
                LogTool.d("", logInfo)
                val current = Calendar.getInstance().time
                digitalLog.append("$current : ")
                digitalLog.append(logInfo + "\n")
            }
        }
    }

    fun onCreate(preferenceModel: OneAppPreferenceModel) {
        dKLib = null
        dKLib = DKLib.getInstance(context)
        dKLib?.let {
            appendLog("DKLib create success")
        } ?: run {
            appendLog("DKLib create failure")
        }
        dKCtlLib = DKCtlLib.getInstance(context)
        dKCtlLib?.let {
            appendLog("DKCtlLib create success")
        } ?: run {
            appendLog("DKCtlLib create failure")
        }
        appendLog(
            "DKLib Version ${dKLib?.versionInfo?.versionInfo()} DKCTRLIb Version ${dKCtlLib?.versionInfo?.versionInfo()} ",
            TAG,
            isDataDogRequired = true,
        )
        setInterface(preferenceModel)
        initializeDigitalKey(preferenceModel)
    }

    private fun setInterface(preferenceModel: OneAppPreferenceModel) {
        dKLib?.setInterface(DigitalKeyInterfaceImpl(context, this, dkResult, preferenceModel))
        dKCtlLib?.setInterface(DigitalKeyControlInterfaceImpl())
        sharedPreferences = context.getSharedPreferences(context.packageName + "_DKLib", 0)
    }

    val dkResult: (DkMopResultStatus) -> Unit = {
        mDkMopStatus.postValue(it)
    }

    private fun initializeDigitalKey(preferenceModel: OneAppPreferenceModel) {
        // Default Values
        val currentProcess = getCurrentProcess()
        if (currentProcess.funName != CurrentStage.DOWNLOAD_OWNER_KEY.toString()) {
            isSyncCompleted = false
            currentVehicle = ToyotaConstants.EMPTY_STRING
            registerVehicleKeyInfoId = ToyotaConstants.EMPTY_STRING
        }
        initialize(preferenceModel)
    }

    fun getDeviceId(): DigitalMopResponse {
        dKLib?.let {
            val ret = it.deviceId
            appendLog(
                "GetDeviceId s_result  ${ret.result()} ${ret.result()} ${ret.detail()}",
                TAG,
                false,
            )
            return when {
                ret.result() == DKLib.RES_RESULT_CODE.SUCCESS -> {
                    DigitalMopResponse(
                        true,
                        CurrentStage.DEVICE_ID_SUCCESS.name,
                        null,
                        null,
                        ret.deviceId(),
                    )
                }
                ret.result() == DKLib.RES_RESULT_CODE.ERROR_INPROCESS -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.DEVICE_ID_ERROR.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                    )
                }
                else -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.DEVICE_ID_ERROR.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                    )
                }
            }
        }
        return DigitalMopResponse(false, CurrentStage.DEVICE_ID_ERROR.name, null, null)
    }

    fun isInitialized(): Boolean {
        var isInitialized = false
        dKLib?.let { lib ->
            isInitialized = lib.deviceId.result() != DKLib.RES_RESULT_CODE.ERROR_NOT_INITIALIZED
        }
        return isInitialized
    }

    fun getCurrentProcess(): DigitalMopResponse {
        dKLib?.let {
            val ret = it.deviceId
            val iResult = ret.result().toString()
            val iDetail = ret.detail().toString()
            return when {
                ret.result() == DKLib.RES_RESULT_CODE.SUCCESS -> {
                    DigitalMopResponse(true, CurrentStage.NONE.toString(), iResult, iDetail)
                }
                (ret.detail() == DKLib.RES_DETAIL_CODE.INPROCESS_DOWNLOAD_KEY) ||
                    (ret.detail() == DKLib.RES_DETAIL_CODE.INPROCESS_KEY_ROTATION) -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.DOWNLOAD_OWNER_KEY.toString(),
                        iResult,
                        iDetail,
                    )
                }
                ret.detail() == DKLib.RES_DETAIL_CODE.INPROCESS_REGISTER_VEHICLE -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.REGISTER_VEHICLE.toString(),
                        iResult,
                        iDetail,
                    )
                }
                else -> {
                    DigitalMopResponse(false, CurrentStage.NONE.toString(), iResult, iDetail)
                }
            }
        }
        return DigitalMopResponse(false, CurrentStage.NONE.toString(), null, null)
    }

    private fun initialize(preferenceModel: OneAppPreferenceModel) {
        dKLib?.let { lib ->
            val notifyIntent = Intent(context, DashboardActivity::class.java)
            val acsNo: Long = 0L
            notifyIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            val config =
                lib.InitializationConfig(
                    BuildConfig.DK_BASE_URL,
                    BuildConfig.DK_ANALYTIC_URL,
                    BuildConfig.DK_NOTIFICATION_URL,
                    BuildConfig.DK_CMMS_URL,
                    DKLib.PushProviderName.CTP,
                    BuildConfig.DK_PUSH_PROVIDER_NAME,
                    BuildConfig.DK_APP_ID,
                    when {
                        BuildConfig.DEBUG -> BuildConfig.DK_CERT_HASH
                        BuildConfig.APP_BRAND == Brand.TOYOTA.appBrand -> BuildConfig.DK_CERT_HASH_TOYOTA_RELEASE
                        BuildConfig.APP_BRAND == Brand.LEXUS.appBrand -> BuildConfig.DK_CERT_HASH_LEXUS_RELEASE
                        BuildConfig.APP_BRAND == Brand.SUBARU.appBrand -> BuildConfig.DK_CERT_HASH_SUBARU_RELEASE
                        else -> BuildConfig.DK_CERT_HASH_TOYOTA_RELEASE
                    },
                    BuildConfig.APPLICATION_ID,
                    acsNo,
                    BuildConfig.DK_WASP_URL,
                )
            config.let {
                val ret = lib.initialize(notifyIntent, config)
                appendLog(
                    "DKLib Initialize Result = ${ret.result()} Detail ${ret.detail()}",
                    TAG,
                    isDataDogRequired = true,
                    isError = false,
                )
                when (ret.result()) {
                    DKLib.RES_RESULT_CODE.ERROR_ALREADY_INITIALIZED -> {
                        context.sendBroadcast(
                            Intent(ACTION_INITIALIZE).apply {
                                putExtra(IS_SUCCESS, true)
                            },
                        )
                        mDkMopStatus.postValue(
                            DkMopResultStatus.DkMopInitialization(
                                DigitalMopResponse(true, "", "", ""),
                            ),
                        )
                        initializeControlSdk()
                        syncAllKeys(preferenceModel)
                    }
                    else -> {
                    }
                }
            }
        } ?: run {
            appendLog("initialize dKLib is null")
            onCreate(preferenceModel)
        }
    }

    fun initializeControlSdk() {
        dKCtlLib?.let {
            val preferences: SharedPreferences =
                context.getSharedPreferences(
                    "setCountry",
                    Context.MODE_PRIVATE,
                )
            val setCountry = preferences.getString("setCountry", DKLib.Country.US.toString())
            val ret = it.initialize(R.raw._0000000101030c00, DKLib.Country.valueOf(setCountry!!))
            appendLog(
                "InitializeControlSdk s_result ${ret.result().code} s_detail ${ret.detail().code}",
                TAG,
                true,
                false,
            )
        }
    }

    fun downloadKeyStart(
        ownerKeyToken: String?,
        vin: String,
        isRotationKey: Boolean = false,
    ): DigitalMopResponse {
        dKLib?.let {
            appendLog("DownloadKeyStart   isRotationKey $isRotationKey")
            val ret =
                if (isRotationKey) {
                    it.keyRotationStart(
                        ownerKeyToken,
                    )
                } else {
                    it.downloadKeyStart(ownerKeyToken)
                }
            appendLog(
                "DownloadKeyStart Result  ${ret?.result()} Detail ${ret?.detail()} OwnerToken $ownerKeyToken",
                TAG,
                isDataDogRequired = true,
                isError = false,
            )

            return when {
                ret.result() == DKLib.RES_RESULT_CODE.SUCCESS -> {
                    currentVehicle = vin
                    DigitalMopResponse(true, CurrentStage.DOWNLOAD_OWNER_KEY.name, null, null)
                }
                ret.result() == DKLib.RES_RESULT_CODE.ERROR_NOT_INITIALIZED -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.MOP_INITIALIZATION_ERROR.name,
                        null,
                        null,
                    )
                }
                ret.result() == DKLib.RES_RESULT_CODE.ERROR_INPROCESS -> {
                    currentVehicle = vin
                    appendLog(
                        "DownloadKeyStart Failed: ${ret.result()} - ${ret.detail()}",
                        TAG,
                        isDataDogRequired = true,
                        isError = true,
                    )
                    DigitalMopResponse(
                        false,
                        CurrentStage.DOWNLOAD_OWNER_KEY.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                        "",
                    )
                }
                else -> {
                    currentVehicle = vin
                    appendLog(
                        "DownloadKeyStart Failed: ${ret.result()} - ${ret.detail()}",
                        TAG,
                        isDataDogRequired = true,
                        isError = true,
                    )
                    DigitalMopResponse(
                        false,
                        CurrentStage.DOWNLOAD_OWNER_KEY.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                    )
                }
            }
        }
        return DigitalMopResponse(false, CurrentStage.DOWNLOAD_OWNER_KEY.name, null, null)
    }

    fun cancelCurrentDownload() = dKLib?.downloadKeyCancel()

    fun deactivateKey(keyInfoId: String): DigitalMopResponse {
        dKLib?.let {
            val ret = it.deactivateKeyStart(keyInfoId)
            appendLog(
                "DeactivateKey Result ${ret?.result()} Detail ${ret?.detail()} KeyInfoId $keyInfoId",
                TAG,
                isDataDogRequired = true,
                isError = false,
            )
            return when {
                ret.result() == DKLib.RES_RESULT_CODE.SUCCESS -> {
                    DigitalMopResponse(true, CurrentStage.REVOKE_USER.name, null, null)
                }
                ret.result() == DKLib.RES_RESULT_CODE.ERROR_INPROCESS -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.REVOKE_USER.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                        "",
                    )
                }
                else -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.REVOKE_USER.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                    )
                }
            }
        }
        return DigitalMopResponse(false, CurrentStage.REVOKE_USER.name, null, null)
    }

    fun deactivateKeyCancel() = dKLib?.deactivateKeyCancel()

    fun syncAllKeys(oneAppPreferenceModel: OneAppPreferenceModel) {
        if (!isInitialized()) return
        // If Network not available , we are not syncing data.
        if (ToyUtil.hasNetwork(context)) {
            dKLib?.let {
                val ret: DKLib.Result = it.syncAllKeys()
                appendLog(
                    "SyncAllKeysStart Result ${ret.result()} Detail ${ret.detail()}",
                    TAG,
                    isDataDogRequired = true,
                    isError = false,
                )
            } ?: onCreate(preferenceModel = oneAppPreferenceModel)
        } else {
            isSyncCompleted = true
            val broadcastIntent = Intent(ACTION_SYNC_ALL_KEY_RESULT)
            context.sendBroadcast(broadcastIntent)
            appendLog("SyncAllKeysStart Stopped due to No network")
        }
    }

    fun checkSyncStatus(): DKLib.RES_RESULT_CODE = DKLib.RES_RESULT_CODE.SUCCESS

    fun syncKey(
        keyInfoId: String,
        oneAppPreferenceModel: OneAppPreferenceModel,
    ) {
        if (!isInitialized()) return
        // If Network not available , we are not syncing data.
        if (ToyUtil.hasNetwork(context)) {
            dKLib?.let {
                val ret: DKLib.Result = it.syncKey(keyInfoId)
                appendLog(
                    "SyncAllKeysStart Result ${ret.result()} Detail ${ret.detail()}",
                    TAG,
                    isDataDogRequired = true,
                    isError = false,
                )
            } ?: onCreate(preferenceModel = oneAppPreferenceModel)
        } else {
            isSyncCompleted = true
            val broadcastIntent = Intent(ACTION_SYNC_ALL_KEY_RESULT)
            context.sendBroadcast(broadcastIntent)
            appendLog("syncKey Stopped due to No network")
        }
    }

    fun registerVehicleStart(keyInfoId: String): DigitalMopResponse {
        dKLib?.let {
            val ret = it.registerVehicleStart(keyInfoId)
            if (ret.result() != DKLib.RES_RESULT_CODE.SUCCESS) {
                appendLog(
                    "RegisterVehicleStart Failed: ${ret.result()} - ${ret.detail()} KeyInfo $keyInfoId",
                    isDataDogRequired = true,
                    isError = true,
                )
            } else {
                appendLog(
                    "RegisterVehicleStart Result ${ret.result()} Detail ${ret.detail()} KeyInfo $keyInfoId",
                    isDataDogRequired = true,
                    isError = false,
                )
            }
            return when (ret.result()) {
                DKLib.RES_RESULT_CODE.SUCCESS -> {
                    registerVehicleKeyInfoId = keyInfoId
                    DigitalMopResponse(true, CurrentStage.REGISTER_VEHICLE.name, null, null)
                }
                DKLib.RES_RESULT_CODE.ERROR_INPROCESS -> {
                    if (DKLib.RES_DETAIL_CODE.INPROCESS_REGISTER_VEHICLE == ret.detail()) {
                        DigitalMopResponse(true, CurrentStage.REGISTER_VEHICLE.name, null, null)
                    } else {
                        DigitalMopResponse(
                            false,
                            CurrentStage.REGISTER_VEHICLE.name,
                            ret.result().toString(),
                            ret.detail().toString(),
                            ToyotaConstants.EMPTY_STRING,
                        )
                    }
                }
                else -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.REGISTER_VEHICLE.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                    )
                }
            }
        }
        return DigitalMopResponse(false, CurrentStage.REGISTER_VEHICLE.name, null, null)
    }

    fun registerVehicleCancel() {
        dKLib?.let {
            val ret = it.registerVehicleCancel()
            appendLog(
                "RegisterVehicleCancelStart Result ${ret?.result()} Detail ${ret?.detail()}",
                isDataDogRequired = true,
                isError = false,
            )
        }
    }

    fun activateRemoteAuth(keyInfoId: String) {
        appendLog("ActivateRemoteAuth Is Required $keyInfoId", TAG, true, false)
        remoteControl(
            keyInfoId,
            RcCmd.AUTHORIZATION.tag,
            RcCmd.AUTHORIZATION.par1,
            RcCmd.AUTHORIZATION.par2,
            RcCmd.AUTHORIZATION.par3,
        )
    }

    fun remoteControl(
        keyInfoId: String?,
        tag: Int,
        par1: Int,
        par2: Date?,
        par3: Date?,
    ): DigitalMopResponse {
        dKLib?.let {
            val ret = it.remoteControl(keyInfoId, tag, par1, par2, par3)
            appendLog(
                "RemoteControl  CommandType ${getCommandType(par1)} KeyInfo $keyInfoId ${ret?.result()} Detail ${ret?.detail()} ",
                TAG,
                isDataDogRequired = true,
                isError = false,
            )
            return when (ret.result()) {
                DKLib.RES_RESULT_CODE.SUCCESS -> {
                    DigitalMopResponse(true, CurrentStage.REGISTER_VEHICLE.name, null, null)
                }
                DKLib.RES_RESULT_CODE.ERROR_INPROCESS -> {
                    if (DKLib.RES_DETAIL_CODE.INPROCESS_REGISTER_VEHICLE == ret.detail()) {
                        DigitalMopResponse(true, CurrentStage.REGISTER_VEHICLE.name, null, null)
                    } else {
                        DigitalMopResponse(
                            false,
                            CurrentStage.REGISTER_VEHICLE.name,
                            ret.result().toString(),
                            ret.detail().toString(),
                            ToyotaConstants.EMPTY_STRING,
                        )
                    }
                }
                else -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.REGISTER_VEHICLE.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                    )
                }
            }
        }
        return DigitalMopResponse(false, CurrentStage.REGISTER_VEHICLE.name, null, null)
    }

    private fun getCommandType(par: Int) =
        when (par) {
            RcCmd.LOCK.par1 -> "LOCK"
            RcCmd.UNLOCK.par1 -> "UNLOCK"
            else -> "AUTH"
        }

    fun getKeyInfo(keyInfoId: String?): DKLib.MyKeyInfo? {
        dKLib?.let {
            val ret = it.getKeyInfo(keyInfoId)
            return if (ret.result() == DKLib.RES_RESULT_CODE.SUCCESS) {
                ret.myKeyInfo()
            } else {
                appendLog(
                    "GetKeyInfo Result ${ret?.result()} Detail ${ret?.detail()}",
                    TAG,
                    isDataDogRequired = true,
                    isError = false,
                )
                null
            }
        }
        return null
    }

    fun getKeysInfo(): ArrayList<DKLib.MyKeyInfo> {
        dKLib?.let {
            val ret = it.allKeyInfos
            appendLog("getKeysInfo success ${ret.result()}")
            return if (ret.result() == DKLib.RES_RESULT_CODE.SUCCESS) {
                ret.keyInfos()
            } else {
                appendLog(
                    "getKeysInfo Result ${ret?.result()} Detail ${ret?.detail()}",
                    TAG,
                    isDataDogRequired = true,
                    isError = false,
                )
                ArrayList()
            }
        }
        appendLog("getKeysInfo success Dklib null")
        return ArrayList()
    }

    fun setVehicleConnectAccept(
        keyInfoId: String,
        accept: Boolean,
    ): DigitalMopResponse {
        dKLib?.let {
            val ret = it.setVehicleConnectAccept(keyInfoId, accept)
            appendLog(
                "VehicleConnectAccept Result ${ret?.result()} Detail ${ret?.detail()} " +
                    "KeyInfo $keyInfoId",
                TAG,
                isDataDogRequired = true,
                isError = false,
            )

            return when {
                ret.result() == DKLib.RES_RESULT_CODE.SUCCESS -> {
                    DigitalMopResponse(true, CurrentStage.CONNECT_VEHICLE_START.name, null, null)
                }
                ret.result() == DKLib.RES_RESULT_CODE.ERROR_INPROCESS -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.CONNECT_VEHICLE_START.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                    )
                }
                else -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.CONNECT_VEHICLE_START.name,
                        ret.result().toString(),
                        ret.detail().toString(),
                    )
                }
            }
        }
        return DigitalMopResponse(false, CurrentStage.CONNECT_VEHICLE_START.name, null, null)
    }

    fun getKeyInfoFromLocal(
        vin: String,
        preferenceModel: OneAppPreferenceModel,
    ): String? {
        digitalKeyLocalData =
            DigitalKeyLocalData.getInstance(
                oneAppPreferenceModel = preferenceModel,
            )
        return digitalKeyLocalData?.getDigitalKeys()?.get(vin)
    }

    fun getVinKeyInfo(
        selectedVin: String,
        preferenceModel: OneAppPreferenceModel,
    ): DKLib.MyKeyInfo? {
        val keyInfoId = getKeyInfoFromLocal(vin = selectedVin, preferenceModel = preferenceModel)
        return keyInfoId?.let {
            getKeyInfo(it)
        }
    }

    fun removeKey(
        vin: String,
        preferenceModel: OneAppPreferenceModel,
    ) {
        digitalKeyLocalData = DigitalKeyLocalData.getInstance(preferenceModel)
        if (digitalKeyLocalData?.getDigitalKeys()?.containsKey(vin) == true) {
            dKLib?.deleteKeyInfo(digitalKeyLocalData?.getDigitalKeys()?.get(vin))
            digitalKeyLocalData?.getDigitalKeys()?.remove(vin)
            digitalKeyLocalData?.clearPersonas(vin)
        }
    }

    fun updateCaliberValues() {
        /*  if (!digitalKeyLocalData.getCalibration()) {
              dKLib?.allKeyInfos?.keyInfos()?.filter { it.bleRegister && it.isVehicleConnectAccept }.let {
                  digitalKeyLocalData.setCalibrarion()
                  val queue: Queue<DKLib.MyKeyInfo> = LinkedList(it)
                  setCaliber(queue)
              }

          }*/
    }

    fun setCaliber(
        keyInfo: DKLib.MyKeyInfo,
        value: Int,
    ): DigitalMopResponse {
        appendLog(
            "SetCaliber keyInfo ${keyInfo.keyInfoId} value $value",
            TAG,
            isDataDogRequired = true,
        )

        dKCtlLib?.let {
            val success = it.setCalibration(keyInfo.keyInfoId, value.toByte())
            appendLog(
                "SetCaliber status  ${success.result()} detail ${success.detail()}",
                TAG,
                isDataDogRequired = true,
            )
            return when {
                success.result() == DKLib.RES_RESULT_CODE.SUCCESS -> {
                    DigitalMopResponse(true, CurrentStage.CONNECT_VEHICLE_START.name, null, null)
                }
                success.result() == DKLib.RES_RESULT_CODE.ERROR_INPROCESS -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.CONNECT_VEHICLE_START.name,
                        success.result().toString(),
                        success.detail().toString(),
                    )
                }
                else -> {
                    DigitalMopResponse(
                        false,
                        CurrentStage.CONNECT_VEHICLE_START.name,
                        success.result().toString(),
                        success.detail().toString(),
                    )
                }
            }
        }
        return DigitalMopResponse(false, CurrentStage.CONNECT_VEHICLE_START.name, null, null)
    }

    fun getVehicleIdForKey(keyInfoStr: String): String {
        appendLog("getVehicleIdForKey ")
        if (::sharedPreferences.isInitialized) {
            appendLog("getVehicleIdForKey isInitialized $keyInfoStr")
            keyInfoStr.let {
                val strKeyTbl =
                    sharedPreferences.getString(
                        "MainKey$keyInfoStr",
                        null as String?,
                    )
                appendLog("getVehicleIdForKey str_keyTbl $strKeyTbl")

                if (strKeyTbl?.isNotEmpty() == true) {
                    return try {
                        val keyInfo = Gson().fromJson(strKeyTbl, KeyInfoTable::class.java)
                        if (keyInfo?.keyInfo?.keyInfoId == keyInfoStr) {
                            keyInfo.keyInfo.vehicleId ?: ""
                        } else {
                            ""
                        }
                    } catch (e: Exception) {
                        ""
                    }
                }
            }
        }
        return ""
    }
}

@Parcelize
data class KeyInfoTable(
    @SerializedName("keyInfo") var keyInfo: MyKeyInfoInternal,
) : Parcelable

@Parcelize
data class MyKeyInfoInternal(
    @SerializedName("keyInfoId") var keyInfoId: String?,
    @SerializedName(
        "vehicleId",
    ) var vehicleId: String?,
) : Parcelable
