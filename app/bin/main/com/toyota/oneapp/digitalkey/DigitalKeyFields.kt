package com.toyota.oneapp.digitalkey

const val IS_SHARE_SETUP_FLOW = "IS_SHARE_SETUP_FLOW"
const val SMART_OPERATION = "Smart Operation Stop"
const val SMART_WORKING = "Smart Working"
const val SMART_STOP = "Smart Working"
const val UNKNOWN = "Unknown"
const val POWER_SAVING = "Power Saving"
const val POWER_SAVING_MODE = "In Power Saving Mode"
const val POWER_NORMAL_MODE = "In Normal Mode"
const val POWER_SAVING_MODE_10MIN = "10Min Power Saving"
const val ENGINE_OFF = "Reminder After Engine Off"
const val FEED_BACK_DOOR_LOCKED = "Feedback when the Door is Locked"
const val FEED_BACK_ALL_DOOR_LOCKED = "All Doors Change Lock"
const val INGREDIENT_NOTIFY = "Inadvertent Notification"
const val INGREDIENT_UNLOCKING = "Inadvertent Unlocking"
const val INACTIVITY_FACTOR = "Inactivity Factor"
const val USE_COUNT = "Remaining use count"
const val USER_AUTH = "User authentication"
const val INVALIDATION = "User Invalidation"
const val USER_AUTH_NOT = "Authentication Not Possible"
const val ACTIVITY_NOT_FOUND = "Fragment activity not found"
const val AUTHENTIC_FAIL = "Failure to Authenticate"
const val NUMBER_1 = 1
const val NUMBER_0 = 0
const val NUMBER_2 = 2
const val NUMBER_3 = 3
const val NUMBER_4 = 4
const val NUMBER_5 = 5
const val NUMBER_6 = 6
const val NUMBER_10 = 10

const val DELAY_IN_MILLIS: Long = 2500
const val LUK_BLE_DELAY_IN_MILLIS: Long = 5000
const val REGISTRATION_DELAY: Long = 1000
const val TIMER_COUNT: Long = 30000
const val KEY_STATUS_DELAY: Long = 2000
const val INTERVAL: Long = 1000
