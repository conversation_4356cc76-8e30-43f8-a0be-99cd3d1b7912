package com.toyota.oneapp.digitalkey

import android.content.Context
import android.content.Intent
import com.idemia.deviceagent.PushListener
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.DEACTIVATE_RESULT
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.IS_SUCCESS
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.KEY_INFO_ID
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.KEY_KIND
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.REGISTRATION_RESULT
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.S_DETAIL
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.S_RESULT
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants
import jp.co.denso.dklib.DKLib

class DigitalKeyInterfaceImpl(
    private val context: Context,
    private val digitalMopKeyUtils: DigitalMopKeyUtils,
    private val dkResult: (DkMopResultStatus) -> Unit,
    private val preferenceModel: OneAppPreferenceModel,
) : DKLib.DKLibInterface {
    override fun downloadKeyResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
        keyInfoId: String?,
        keyKind: DKLib.KeyKind?,
    ) {
        val iResult = result.code
        val iDetail = detail.code
        DigitalMopKeyUtils.appendLog(
            "DownloadKeyResult $iResult Detail $iDetail KeyInfo" +
                " $keyInfoId KeyKind ${keyKind?.string} Vehicle ${digitalMopKeyUtils.currentVehicle}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        if (keyInfoId != null && digitalMopKeyUtils.currentVehicle.isNotEmpty()) {
            DigitalKeyLocalData.getInstance(oneAppPreferenceModel = preferenceModel).setDigitalKey(
                digitalMopKeyUtils.currentVehicle,
                keyInfoId,
            )
            dkResult(
                DkMopResultStatus.DkMopDownload(
                    DigitalMopResponse(true, "", iResult.toString(), iDetail.toString()),
                ),
            )
        } else {
            dkResult(
                DkMopResultStatus.DkMopDownload(
                    DigitalMopResponse(false, "", iResult.toString(), iDetail.toString()),
                ),
            )
        }
        digitalMopKeyUtils.currentVehicle = ToyotaConstants.EMPTY_STRING
        val broadcastIntent =
            Intent(DigitalMopKeyUtils.ACTION_DOWNLOAD_RESULT).apply {
                putExtra(KEY_INFO_ID, keyInfoId)
                putExtra(KEY_KIND, keyKind)
                putExtra(S_RESULT, iResult)
                putExtra(S_DETAIL, iDetail)
            }
        context.sendBroadcast(broadcastIntent)
    }

    override fun deactivateKeyResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
    ) {
        val broadcastIntent = Intent(DigitalMopKeyUtils.ACTION_REMOTE_DEACTIVATE)
        if (result == DKLib.NOT_RESULT_CODE.SUCCESS) {
            broadcastIntent.putExtra(DEACTIVATE_RESULT, true)
        }
        context.sendBroadcast(broadcastIntent)
    }

    override fun registerVehicleResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
        deviceIndex: Int,
    ) {
        val iResult = result.code
        val iDetail = detail.code
        val broadcastIntent = Intent(DigitalMopKeyUtils.ACTION_REGISTER_VEHICLE_RESULT)
        DigitalMopKeyUtils.appendLog(
            "RegisterVehicleResult Result ${result.code} Detail ${detail.code} " +
                "KeyInfoId ${digitalMopKeyUtils.registerVehicleKeyInfoId}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        if (result == DKLib.NOT_RESULT_CODE.SUCCESS) {
            broadcastIntent.putExtra(REGISTRATION_RESULT, true)
        } else {
            digitalMopKeyUtils.registerVehicleKeyInfoId = ToyotaConstants.EMPTY_STRING
            broadcastIntent.apply {
                putExtra(S_RESULT, iResult.toString())
                putExtra(S_DETAIL, iDetail.toString())
                putExtra(REGISTRATION_RESULT, false)
            }
        }
        context.sendBroadcast(broadcastIntent)
        val response =
            digitalMopKeyUtils.setVehicleConnectAccept(
                digitalMopKeyUtils.registerVehicleKeyInfoId,
                true,
            )
        if (!response.success) {
            val connIntent = Intent(DigitalMopKeyUtils.ACTION_CONNECT_VEHICLE_RESULT)
            broadcastIntent.apply {
                putExtra(S_RESULT, response.resCode ?: ToyotaConstants.EMPTY_STRING)
                putExtra(S_DETAIL, response.detCode ?: ToyotaConstants.EMPTY_STRING)
            }
            context.sendBroadcast(connIntent)
        }
    }

    override fun remoteControlResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
    ) {
        DigitalMopKeyUtils.appendLog(
            "RemoteControl Result ${result.code} Detail ${detail.code} " +
                "KeyInfoId ${digitalMopKeyUtils.registerVehicleKeyInfoId}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        val broadcastIntent = Intent(DigitalMopKeyUtils.ACTION_REMOTE_REGISTER)
        context.sendBroadcast(broadcastIntent)
    }

    override fun syncAllKeysResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
        myKeyInfos: ArrayList<DKLib.MyKeyInfo>?,
    ) {
        DigitalMopKeyUtils.appendLog(
            "SyncAllKeys Result ${result.code} Detail ${detail.code} " +
                "KeyInfoId ${digitalMopKeyUtils.registerVehicleKeyInfoId}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        if (result == DKLib.NOT_RESULT_CODE.SUCCESS) {
            digitalMopKeyUtils.isSyncCompleted = true
            val broadcastIntent = Intent(DigitalMopKeyUtils.ACTION_SYNC_ALL_KEY_RESULT)
            context.sendBroadcast(broadcastIntent)
            dkResult(
                DkMopResultStatus.DkMopSync(
                    DigitalMopResponse(true, "", result.toString(), detail.toString()),
                ),
            )
        } else {
            digitalMopKeyUtils.isSyncCompleted = true
            dkResult(
                DkMopResultStatus.DkMopInitialization(
                    DigitalMopResponse(false, "", result.toString(), detail.toString()),
                ),
            )
        }
    }

    override fun syncKeyResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
        myKeyInfo: DKLib.MyKeyInfo,
    ) {
        DigitalMopKeyUtils.appendLog(
            "SyncKey Result ${result.code} Detail ${detail.code} " +
                "KeyInfoId ${digitalMopKeyUtils.registerVehicleKeyInfoId}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        val messageData = arrayOfNulls<String>(3)
        messageData[0] = myKeyInfo.keyInfoId
        val broadcastIntent = Intent(DigitalMopKeyUtils.ACTION_SYNC_KEY_RESULT)
        broadcastIntent.putExtra("MyMessage", messageData)
        context.sendBroadcast(broadcastIntent)
    }

    override fun downloadKeyProgressInfo(
        progressRate: Double,
        wbcOperation: String,
    ) {
        DigitalMopKeyUtils.appendLog(
            "DownloadKeyProgressInfo $progressRate WbcOperation $wbcOperation",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        val broadcastIntent = Intent(DigitalMopKeyUtils.ACTION_DOWNLOAD_PROGRESS)
        context.sendBroadcast(broadcastIntent)
    }

    override fun keyInfoChangedAll(mykeyInfos: List<DKLib.MyKeyInfo>?) {
        val broadcastIntent = Intent(DigitalMopKeyUtils.ACTION_KEYINFO_CHANGE)
        context.sendBroadcast(broadcastIntent)
    }

    override fun initializationResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
    ) {
        val iResult = result.code
        val iDetail = detail.code
        var isSuccess = false
        DigitalMopKeyUtils.appendLog(
            "Initialization Result ${result.code} Detail ${detail.code} " +
                "KeyInfoId ${digitalMopKeyUtils.registerVehicleKeyInfoId}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        if (result == DKLib.NOT_RESULT_CODE.SUCCESS) {
            digitalMopKeyUtils.syncAllKeys(preferenceModel)
            digitalMopKeyUtils.initializeControlSdk()
            try {
                DigitalMopKeyUtils.appendLog(
                    "Initialization Result Updating Token ${preferenceModel.getDeviceToken()}",
                    DigitalMopKeyUtils.TAG,
                    isDataDogRequired = true,
                    isError = false,
                )
                PushListener.getInstance(context).onNewToken(preferenceModel.getDeviceToken())
            } catch (ex: Exception) {
                DigitalMopKeyUtils.appendLog(
                    "Got error while setting Token ${ex.message}",
                    DigitalMopKeyUtils.TAG,
                    true,
                    true,
                )
            }
            isSuccess = true
            dkResult(
                DkMopResultStatus.DkMopInitialization(
                    DigitalMopResponse(true, "", iResult.toString(), iDetail.toString()),
                ),
            )
        }
        val broadcastIntent =
            Intent(DigitalMopKeyUtils.ACTION_INITIALIZE).apply {
                action = DigitalMopKeyUtils.ACTION_INITIALIZE
                putExtra(IS_SUCCESS, isSuccess)
                putExtra(S_RESULT, iResult)
                putExtra(S_DETAIL, iDetail)
                dkResult(
                    DkMopResultStatus.DkMopInitialization(
                        DigitalMopResponse(true, "", iResult.toString(), iDetail.toString()),
                    ),
                )
            }
        context.sendBroadcast(broadcastIntent)
    }

    // This below callbacks not required for ONEAPP features.
    override fun bleLinkStatus(
        keyInfoId: String,
        isConnect: Boolean,
        operationMode: DKLib.OperationMode,
    ) {
        DigitalMopKeyUtils.appendLog(
            "bleLinkStatus $isConnect keyInfoId $keyInfoId",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        context.sendBroadcast(Intent(DigitalMopKeyUtils.ACTION_DK_BLE_LINK_STATUS_CHANGE))
        context.sendBroadcast(Intent(DigitalMopKeyUtils.ACTION_KEYINFO_CHANGE))
    }

    override fun logTransferResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
    ) {
        // This method is intentionally left empty.
    }

    override fun deleteForcedAllKeysRequestResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
    ) {
        // This method is intentionally left empty.
    }

    override fun rcvGeneralVehicleDataMsg(
        keyInfoId: String,
        result: Byte,
        rcvData: ByteArray,
    ) {
        // This method is intentionally left empty.
    }

    override fun keyRotationProgressInfo(
        progressRate: Double,
        wbcOperation: String,
    ) {
        DigitalMopKeyUtils.appendLog(
            "KeyRotationProgressInfo $progressRate WbcOperation $wbcOperation",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
    }

    override fun keyRotationResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
        totalKeyCount: Int,
        successKeyCount: Int,
        keyInfoIdList: List<String>?,
    ) {
        val iResult = result.code
        val iDetail = detail.code

        val keyInfoId = if (keyInfoIdList != null && result == DKLib.NOT_RESULT_CODE.SUCCESS) keyInfoIdList[0] else null
        DigitalMopKeyUtils.appendLog(
            "RotationKey Result $iResult Detail $iDetail KeyInfo" +
                " $keyInfoId KeyKind ${DKLib.KeyKind.OWNER.string} Vehicle ${digitalMopKeyUtils.currentVehicle}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        if (keyInfoId != null && digitalMopKeyUtils.currentVehicle.isNotEmpty()) {
            DigitalKeyLocalData.getInstance(oneAppPreferenceModel = preferenceModel).setDigitalKey(
                digitalMopKeyUtils.currentVehicle,
                keyInfoId,
            )
            dkResult(
                DkMopResultStatus.DKKeyRotationDownload(
                    DigitalMopResponse(true, "", iResult.toString(), iDetail.toString()),
                ),
            )
        } else {
            dkResult(
                DkMopResultStatus.DKKeyRotationDownload(
                    DigitalMopResponse(false, "", iResult.toString(), iDetail.toString()),
                ),
            )
        }

        digitalMopKeyUtils.currentVehicle = ToyotaConstants.EMPTY_STRING
        val broadcastIntent =
            Intent(DigitalMopKeyUtils.ACTION_DOWNLOAD_RESULT).apply {
                putExtra(KEY_INFO_ID, keyInfoId)
                putExtra(KEY_KIND, DKLib.KeyKind.OWNER.string)
                putExtra(S_RESULT, iResult)
                putExtra(S_DETAIL, iDetail)
            }
        context.sendBroadcast(broadcastIntent)
    }

    override fun setBleFunctionOnResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
    ) {
        DigitalMopKeyUtils.appendLog(
            "setBleFunctionOnResult s_result ${result.code} s_detail ${detail.code}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        val broadcastIntent = Intent(DigitalMopKeyUtils.ACTION_REMOTE_BLE_ON)
        context.sendBroadcast(broadcastIntent)
    }

    override fun setBleFunctionOffResult(
        result: DKLib.NOT_RESULT_CODE,
        detail: DKLib.NOT_DETAIL_CODE,
    ) {
        DigitalMopKeyUtils.appendLog(
            "setBleFunctionOffResult s_result ${result.code} s_detail ${detail.code}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        val broadcastIntent = Intent(DigitalMopKeyUtils.ACTION_REMOTE_BLE_OFF)
        context.sendBroadcast(broadcastIntent)
    }

    override fun abnormality(
        p0: DKLib.NOT_RESULT_CODE?,
        p1: DKLib.NOT_DETAIL_CODE?,
    ) {
        // This method is intentionally left empty.
    }

    override fun fcmCtpRegistrationResult(
        result: DKLib.NOT_RESULT_CODE?,
        detail: DKLib.NOT_DETAIL_CODE?,
    ) {
        DigitalMopKeyUtils.appendLog(
            "FcmCtpRegistrationResult s_result ${result?.code} s_detail ${detail?.code}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
    }

    override fun fcmCtpRegistrationStatus(
        result: DKLib.NOT_RESULT_CODE?,
        detail: DKLib.NOT_DETAIL_CODE?,
    ) {
        DigitalMopKeyUtils.appendLog(
            "FcmCtpRegistrationStatus s_result ${result?.code} s_detail ${detail?.code}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
    }

    override fun sendGeneralSPhoneDataResult(
        result: DKLib.NOT_RESULT_CODE?,
        detail: DKLib.NOT_DETAIL_CODE?,
    ) {
        // This method is intentionally left empty.
    }

    override fun advice(
        keyInfoId: String,
        par1: Byte,
        par2: Byte,
        par3: Byte,
    ) {
        val hexFormat = "0x%02x"
        val strPar1 = String.format(hexFormat, par1)
        val strPar2 = String.format(hexFormat, par2)
        val strPar3 = String.format(hexFormat, par3)

        val br = System.getProperty("line.separator")

        var outPar1 = "PAR1=$strPar1:"
        var outPar2 = "PAR2=$strPar2:"
        val outPar3 = "PAR3=$strPar3:"

        when (par1) {
            0x01.toByte() -> {
                outPar1 += INACTIVITY_FACTOR
                when (par2) {
                    0x20.toByte() -> {
                        val broadcastIntent =
                            Intent(DigitalMopKeyUtils.ACTION_REMOTE_AUTH).apply {
                                putExtra(KEY_INFO_ID, keyInfoId)
                            }
                        context.sendBroadcast(broadcastIntent)
                    }
                }
            }
            0x10.toByte() -> {
                outPar1 += USE_COUNT
                val iCount = par2.toInt() and 0x000000FF
                val count = iCount.toString()
                outPar2 += count
            }
            0x20.toByte() -> {
                outPar1 += USER_AUTH
                outPar2 =
                    when (par2) {
                        0x01.toByte() -> {
                            outPar2 + INVALIDATION
                        }
                        0x02.toByte() -> {
                            outPar2 + USER_AUTH_NOT
                        }
                        else -> {
                            outPar2 + ToyotaConstants.EMPTY_STRING
                        }
                    }
                val broadcastIntent =
                    Intent(DigitalMopKeyUtils.ACTION_REMOTE_AUTH).apply {
                        putExtra(KEY_INFO_ID, keyInfoId)
                    }
                context.sendBroadcast(broadcastIntent)
            }
            else -> {
                outPar1 += UNKNOWN
            }
        }

        val strGuide = keyInfoId + br + outPar1 + br + outPar2 + br + outPar3
        DigitalMopKeyUtils.appendLog(
            "Advice from DKLib $strGuide",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
    }
}
