package com.toyota.oneapp.digitalkey

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.PendingInvite
import com.toyota.oneapp.model.pref.SharePreferenceHelper
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants

class DigitalKeyLocalData {
    private val digitalKeys = mutableMapOf<String, String>()

    private val sharedCount = mutableMapOf<String, Int>()
    private val dkCalMap = mutableMapOf<String, DkCalibrationStatus>()
    private val personas = mutableMapOf<String, PersonaLocal>()
    private val invites = mutableMapOf<String, LuksStatusInvite>()
    private val secondaryVehicles = mutableListOf<VehicleInfo>()
    private var guid: String = ToyotaConstants.EMPTY_STRING
    private var isEnroll: Boolean = false
    private var phoneNumber: String = ToyotaConstants.EMPTY_STRING
    private var calibrationCheck = false
    private var pendingInviteList = listOf<PendingInvite?>()
    private var digitalkeyCalibration: DigitalkeyCalibration = DigitalkeyCalibration.MEDIUM
    private var isManualSetting: Boolean = false
    private var vehicleInfo: VehicleInfo? = null
    private var currentDownloadingVin: String = ToyotaConstants.EMPTY_STRING
    private var deviceId: String = ToyotaConstants.EMPTY_STRING
    private var digitalKeyVehicleId: String = ToyotaConstants.EMPTY_STRING
    private var digitalKeyInfoId: String = ToyotaConstants.EMPTY_STRING
    private var isRotationKey: Boolean = false
    private var isDigitalKeyMigrate = mutableMapOf<String, Boolean>()
    private var inviteSize: Int = 0
    private var tmpKeyInfoId = mutableMapOf<String, String>()

    companion object {
        private lateinit var digitalKeyLocalData: DigitalKeyLocalData
        internal var preferenceModel: OneAppPreferenceModel? = null

        fun getInstance(oneAppPreferenceModel: OneAppPreferenceModel): DigitalKeyLocalData {
            synchronized(DigitalKeyLocalData::class.java) {
                if (!::digitalKeyLocalData.isInitialized) {
                    try {
                        // TODO Remove this later
                        if (!oneAppPreferenceModel.isPreferenceMigrated()) {
                            oneAppPreferenceModel.setDigitalData(
                                SharePreferenceHelper.getSingletonPreference().digitalData,
                            )
                            SharePreferenceHelper.getSingletonPreference().deleteDigitalkey()
                            oneAppPreferenceModel.setPreferenceMigrated()
                        }
                        digitalKeyLocalData = oneAppPreferenceModel.getDigitalData()
                        preferenceModel = oneAppPreferenceModel
                    } catch (e: Exception) {
                        DigitalMopKeyUtils.appendLog("DigitalKeyLocalData Failed ${e.message}")
                        if (!oneAppPreferenceModel.isPreferenceMigrated()) {
                            SharePreferenceHelper.getSingletonPreference().deleteDigitalkey()
                        }
                        oneAppPreferenceModel.deleteDigitalData()
                        digitalKeyLocalData = DigitalKeyLocalData()
                        oneAppPreferenceModel.setDigitalData(digitalKeyLocalData)
                    }
                }
            }
            return digitalKeyLocalData
        }
    }

    fun setCalibrarion() {
        calibrationCheck = true
        updateLocalData()
    }

    fun getCalibration() = calibrationCheck

    fun setPendingInviteList(pendingInviteList: List<PendingInvite?>) {
        this.pendingInviteList = pendingInviteList
        updateLocalData()
    }

    fun getPendingInviteList() = pendingInviteList

    fun setGuid(guid: String) {
        this.guid = guid
        updateLocalData()
    }

    fun setDigitalKeyMigrate(
        key: String,
        isDigitalKeyMigrate: Boolean,
    ) {
        this.isDigitalKeyMigrate[key] = isDigitalKeyMigrate
        updateLocalData()
    }

    fun isDigitalKeyMigrate(key: String): Boolean = isDigitalKeyMigrate[key] ?: false

    fun setEnrollment(isEnrolled: Boolean) {
        this.isEnroll = isEnrolled
        updateLocalData()
    }

    fun isEnrolled() = isEnroll

    fun setIsTmpKeyInfoId(
        key: String,
        keyInfo: String,
    ) {
        this.tmpKeyInfoId[key] = keyInfo
        updateLocalData()
    }

    fun getTmpKeyInfoId(key: String): String? = tmpKeyInfoId[key]

    fun removeTmpKeyInfoId(key: String) {
        tmpKeyInfoId.remove(key)
        updateLocalData()
    }

    fun setPhoneNumber(phoneNumber: String) {
        this.phoneNumber = phoneNumber
        updateLocalData()
    }

    fun getPhoneNumber() = phoneNumber

    fun setCurrentDownloadingVin(currentDownloadingVin: String) {
        this.currentDownloadingVin = currentDownloadingVin
        updateLocalData()
    }

    fun getCurrentDownloadingVin() = currentDownloadingVin

    fun setVehicleInfo(vehicleInfo: VehicleInfo?) {
        this.vehicleInfo = vehicleInfo
        updateLocalData()
    }

    fun getVehicleInfo() = vehicleInfo

    fun getGuid() = guid

    fun setDigitalKey(
        key: String,
        value: String,
    ) {
        digitalKeys[key] = value
        updateLocalData()
    }

    fun getDigitalKeys() = digitalKeys

    fun setInviteSize(
        key: String,
        inviteSize: Int,
    ) {
        this.sharedCount[key] = inviteSize
        updateLocalData()
    }

    fun getInviteSize(key: String): Int? = sharedCount[key]

    fun isKeyExist(keyInfo: String): Boolean = digitalKeys.values.any { it == keyInfo }

    fun setPersona(value: PersonaLocal) {
        personas[value.personaId] = value
        updateLocalData()
    }

    fun getPersona(personaId: String): PersonaLocal? = personas[personaId]

    fun isPersonaExist(personaId: String): Boolean = personas.any { it.value.personaId == personaId }

    fun removePersona(personaId: String) {
        personas.remove(personaId)
        updateLocalData()
    }

//    LUK Status Invite Methods
    fun setInvite(value: LuksStatusInvite) {
        invites[value.phoneNo] = value
        updateLocalData()
    }

    fun getInvite(phoneNo: String): LuksStatusInvite? = invites[phoneNo]

    fun removeInvite(phoneNo: String) {
        invites.remove(phoneNo)
        updateLocalData()
    }

    fun getInvites(vin: String): MutableList<LuksStatusInvite> =
        invites
            .toList()
            .filter {
                it.second.vin == vin
            }.map { it.second }
            .toMutableList()

    fun clearInvites(vin: String) {
        val itr =
            invites
                .toList()
                .filter { it.second.vin == vin }
                .map { it.second }
                .toList()
        itr.forEach {
            it.vin?.let { it1 -> removeInvite(it1) }
        }
    }

    //    END LUK Status Invite Methods
    fun addVehicle(vehicleInfo: VehicleInfo) {
        val index = secondaryVehicles.indexOfFirst { it.vin == vehicleInfo.vin }
        if (-1 == index) {
            secondaryVehicles.add(vehicleInfo)
        } else {
            secondaryVehicles[index] = vehicleInfo
        }
    }

    fun removeVehicle(vin: String) {
        secondaryVehicles.removeIf { it.vin == vin }
    }

    fun getVehicle(vin: String): VehicleInfo? = secondaryVehicles.find { it.vin == vin }

    fun isVehicleExist(vin: String): Boolean =
        secondaryVehicles.any {
            it.vin == vin
        }

    fun setCalibrationStatus(
        keyInfo: String,
        digitalkeyCalibration: DkCalibrationStatus,
    ) {
        dkCalMap.put(keyInfo, digitalkeyCalibration)
        updateLocalData()
    }

    fun getCalibrationMap() = dkCalMap

    fun getPersonas(vin: String): MutableList<PersonaLocal> =
        personas
            .toList()
            .filter {
                it.second.vin == vin
            }.map { it.second }
            .toMutableList()

    fun clearPersonas(vin: String) {
        val itr =
            personas
                .toList()
                .filter { it.second.vin == vin }
                .map { it.second }
                .toList()
        itr.forEach {
            removePersona(it.personaId)
        }
    }

    private fun updateLocalData() {
        preferenceModel?.setDigitalData(digitalKeyLocalData)
    }

    fun getDeviceId() = deviceId

    fun setDeviceId(deviceId: String) {
        this.deviceId = deviceId
        updateLocalData()
    }

    fun setDigitalKeyVehicleID(digitalKeyVehicleId: String) {
        this.digitalKeyVehicleId = digitalKeyVehicleId
        updateLocalData()
    }

    fun getDigitalKeyInfoId() = digitalKeyInfoId

    fun setDigitalKeyInfoId(digitalKeyInfoId: String) {
        this.digitalKeyInfoId = digitalKeyInfoId
        updateLocalData()
    }

    private fun isValidDeviceId(digitalKeyDeviceId: String): Boolean {
        val deviceIdRegex = Regex("^[A-Za-z0-9]{64}$")
        return digitalKeyDeviceId.matches(deviceIdRegex)
    }
}
