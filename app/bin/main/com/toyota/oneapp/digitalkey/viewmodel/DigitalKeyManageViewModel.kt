/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.digitalkey.viewmodel

import android.net.Uri
import android.os.Build
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.DataDogUtils
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.DigitalkeyCalibration
import com.toyota.oneapp.digitalkey.DkCalibrationStatus
import com.toyota.oneapp.digitalkey.NUMBER_0
import com.toyota.oneapp.digitalkey.NUMBER_10
import com.toyota.oneapp.digitalkey.dkCalMap
import com.toyota.oneapp.extensions.createVehicleInfo
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.application.DigitalKeyDownloadLogic
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.dataaccess.repository.DKRepository
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyErrorCodeStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyLukStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyRevokeStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.DigitalKeyInviteRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.PendingInvite
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.VehicleDetailRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.internal.toImmutableList
import toyotaone.commonlib.contact.ContactUtil
import toyotaone.commonlib.coroutine.DispatcherProvider
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class DigitalKeyManageViewModel
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val languagePreferenceModel: LanguagePreferenceModel,
        private val vehicleDetailRepository: VehicleDetailRepository,
        private val dispatcherProvider: DispatcherProvider,
        private val dkRepository: DKRepository,
        private val digitalMopKeyUtils: DigitalMopKeyUtils,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val digitalKeyLocalData: DigitalKeyLocalData,
        private val digitalKeyDownloadLogic: DigitalKeyDownloadLogic,
        private val regionManager: RegionManager,
        private val dataDogUtils: DataDogUtils,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseViewModel() {
        private val mDeactivateKeyFailed = SingleLiveEvent<Boolean>()
        val deactivateKeyFailed: LiveData<Boolean> get() = mDeactivateKeyFailed

        private val mStartSetup = SingleLiveEvent<Boolean>()
        val startSetup: LiveData<Boolean> get() = mStartSetup

        private val mPopupTask = SingleLiveEvent<Boolean>()
        val popupTask: LiveData<Boolean> get() = mPopupTask

        private val mDkRevoke = SingleLiveEvent<Boolean>()
        val dkRevoke: LiveData<Boolean> get() = mDkRevoke

        lateinit var assetPersonaTask: PendingInvite

        private val mUserDetails = SingleLiveEvent<ContactUtil.IssueUserInfo>()
        val userDetails: LiveData<ContactUtil.IssueUserInfo> get() = mUserDetails

        private val mVehicleDetail = MutableLiveData<VehicleInfo>()
        val vehicleDetail: LiveData<VehicleInfo> get() = mVehicleDetail

        private val mCalibrationStatus = MutableLiveData<DkCalibrationStatus>()
        val calibrationStatus: LiveData<DkCalibrationStatus> get() = mCalibrationStatus

        private val mCalibrationValueUpdated = MutableLiveData<Boolean>()
        val calibrationValueUpdated: LiveData<Boolean> get() = mCalibrationValueUpdated

        var isEndorsingPhoneNumber = false

        private val mContactUtil = ContactUtil()

        private val mIsPendingInviteLeft = MutableStateFlow(value = false)
        val isPendingInviteLeft = mIsPendingInviteLeft.asStateFlow()

        val sharedKeysLiveData = MutableLiveData<List<LuksStatusInvite>?>()

        private var phoneNumber = digitalKeyLocalData.getPhoneNumber()

        private var deviceId = digitalKeyLocalData.getDeviceId()

        private val mGuestInviteError = MutableStateFlow(value = "0")
        val guestInviteError = mGuestInviteError.asStateFlow()

        private val mShowInviteMessage = SingleLiveEvent<Boolean>()
        val showInviteMessage: LiveData<Boolean> get() = mShowInviteMessage

        fun getCurrentVehicle(): VehicleInfo? = applicationData.getSelectedVehicle()

        fun logEventWithParam(
            event: String,
            param: String,
        ) {
            analyticsLogger.logEventWithParameter(
                event,
                param,
            )
        }

        fun isOwner(): Boolean = getVinKeyInfo()?.keyKind == DKLib.KeyKind.OWNER

        fun getVinKeyInfo() =
            digitalMopKeyUtils.getVinKeyInfo(
                selectedVin =
                    applicationData.getSelectedVehicle()?.vin
                        ?: ToyotaConstants.EMPTY_STRING,
                preferenceModel = oneAppPreferenceModel,
            )

        fun registerVehicleCancel() = digitalMopKeyUtils.registerVehicleCancel()

        private fun syncKeys() {
            digitalMopKeyUtils.syncAllKeys(oneAppPreferenceModel)
        }

        fun isSecondaryVehicle() =
            applicationData.getSelectedVehicle()?.isDigitalkey == true || digitalKeyLocalData.getVehicleInfo()?.isDigitalkey == true

        private fun removeKeyCurrentVehicle(isSharedVehicle: Boolean) {
            digitalMopKeyUtils.removeKey(
                applicationData.getSelectedVehicle()?.vin
                    ?: ToyotaConstants.EMPTY_STRING,
                oneAppPreferenceModel,
            )
            if (isSharedVehicle) {
                if (applicationData.getSelectedVehicle()?.isRemoteOnlyUser == false) {
                    applicationData.getVehicleList()?.removeIf {
                        it.vin == applicationData.getSelectedVehicle()?.vin
                    }
                }
                applicationData.setSelectedVehicle(applicationData.getDefaultVehicle())
                digitalKeyLocalData.removeVehicle(applicationData.getSelectedVehicle()?.vin ?: "")
                digitalKeyLocalData.setVehicleInfo(null)
            }
            syncKeys()
        }

        fun getVehicleInfo(vinNumber: String) {
            viewModelScope.launch {
                val resource = vehicleDetailRepository.getVehicleDetail(vinNumber)
                hideProgress()
                if (resource is Resource.Success) {
                    resource.data?.payload?.vehicle?.let {
                        val vehInfo = resource.data?.payload?.createVehicleInfo(languagePreferenceModel)
                        vehInfo?.let {
                            mVehicleDetail.postValue(it)
                            DigitalMopKeyUtils.appendLog("Accepted Vehicle ${it.vin}")
                            digitalKeyLocalData.addVehicle(it)
                        }
                    }
                }
            }
        }

        fun revokeMopKey(
            keyInfoId: String?,
            type: String,
            isSelfPersona: Boolean = false,
            inviteId: String?,
            isSharedVehicle: Boolean = false,
            isOwner: Boolean = false,
        ) {
            showProgress()
            applicationData.getSelectedVehicle()?.let {
                viewModelScope.launch {
                    if (isOwner && !keyInfoId.isNullOrEmpty()) {
                        digitalKeyLocalData.setIsTmpKeyInfoId(it.vin, keyInfoId)
                        digitalMopKeyUtils.deactivateKey(keyInfoId)
                    } else {
                        digitalKeyDownloadLogic
                            .deleteDigitalKeyToken(
                                keyInfoId,
                                phoneNumber,
                                it.vin,
                                deviceId,
                                type,
                                inviteId,
                            ).collect { resource ->
                                hideProgress()
                                resource.error?.let { status ->
                                    if (status.code == 204) {
                                        handleRevokeSuccess(
                                            isSelfPersona,
                                            type,
                                            isSharedVehicle,
                                            keyInfoId,
                                            it,
                                        )
                                        mDkRevoke.postValue(true)
                                    } else {
                                        handleRevokeFailure()
                                    }
                                }
                            }
                    }
                }
            }
        }

        fun removeOwnerKeyFromCTP(
            type: String,
            inviteId: String,
        ) {
            showProgress()
            viewModelScope.launch {
                applicationData.getSelectedVehicle()?.let {
                    digitalKeyDownloadLogic
                        .deleteDigitalKeyToken(
                            digitalKeyLocalData.getTmpKeyInfoId(it.vin),
                            phoneNumber,
                            it.vin,
                            deviceId,
                            type,
                            inviteId,
                        ).collect { resource ->
                            hideProgress()
                            resource.error?.let { status ->
                                if (status.code == 204 || status.code == 400) {
                                    digitalKeyLocalData.setInviteSize(it.vin, 0)
                                    removeKeyCurrentVehicle(it.isDigitalkey)
                                    mDkRevoke.postValue(true)
                                } else {
                                    mDkRevoke.postValue(false)
                                }
                            }
                            digitalKeyLocalData.removeTmpKeyInfoId(it.vin)
                        }
                }
            }
        }

        private fun handleRevokeSuccess(
            isSelfPersona: Boolean,
            type: String,
            isSharedVehicle: Boolean,
            keyInfoId: String?,
            vehicleInfo: VehicleInfo,
        ) {
            if (isSelfPersona) {
                when (type) {
                    DigitalKeyRevokeStatus.PRIMARY.type -> {
                        digitalKeyLocalData.setInviteSize(vehicleInfo.vin, 0)
                        removeKeyCurrentVehicle(isSharedVehicle)
                    }
                    DigitalKeyRevokeStatus.LUK_SELF.type -> {
                        removeKeyCurrentVehicle(isSharedVehicle)
                    }
                    DigitalKeyRevokeStatus.PRIMARY_LUK.type -> {
                        keyInfoId?.let {
                            deactivateKey(it)
                            digitalMopKeyUtils.syncKey(
                                it,
                                oneAppPreferenceModel = oneAppPreferenceModel,
                            )
                        }
                    }
                }
            }
        }

        private fun handleRevokeFailure() {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.DK_REVOKE_KEY_FAILED,
            )
            hideProgress()
            DigitalMopKeyUtils.appendLog(
                "RevokeUser Failed",
                DigitalMopKeyUtils.DK_REVOKE_TAG,
                isDataDogRequired = true,
                isError = true,
            )
            mDkRevoke.postValue(false)
        }

        fun completeTask(accept: Boolean) {
            mPopupTask.postValue(accept)
        }

        fun completePersonaEvent(accept: Boolean) {
            if (!::assetPersonaTask.isInitialized) {
                mStartSetup.postValue(false)
                return
            }

            DigitalMopKeyUtils.appendLog(
                "Complete PersonaEvent Request Accept $accept AssetId ${assetPersonaTask.vin}",
                DigitalMopKeyUtils.DK_RECEIVE_TAG,
                true,
            )

            showProgress()

            if (accept) {
                val vehicleInfo = digitalKeyLocalData.getVehicle(assetPersonaTask.vin)
                vehicleInfo?.let { veh ->
                    veh.setDigitalkey(true)
                    val isRemoteOnlyUser = applicationData.getSelectedVehicle()?.isRemoteOnlyUser
                    if (isRemoteOnlyUser == false || isRemoteOnlyUser == null) {
                        val vehicleList = applicationData.getVehicleList() ?: ArrayList()
                        vehicleList.add(veh)
                        applicationData.setVehicleList(vehicleList)
                        applicationData.setSelectedVehicle(veh)
                    } else {
                        applicationData.getSelectedVehicle()?.setDigitalkey(true)
                    }
                    digitalKeyLocalData.setVehicleInfo(veh)
                    digitalMopKeyUtils.isSecondaryDkAccepted = true
                    digitalMopKeyUtils.isSecondaryDkAcceptedState.value = true
                    // Accepts Shared Key Invitation
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                        AnalyticsEventParam.VEHICLE_DIGITAL_KEY_SHARED_ACCEPT,
                    )
                    mStartSetup.postValue(true)
                    digitalKeyLocalData.setPendingInviteList(emptyList())
                } ?: mStartSetup.postValue(false)
            } else {
                // Declines Shared Key Invitation
                declineSharedKeyInvitation(assetPersonaTask.id) // assuming the assetPersonaTask has an 'id' property
                analyticsLogger.logEventWithParameter(
                    AnalyticsEvent.EVENT_GROUP_DK.eventName,
                    AnalyticsEventParam.VEHICLE_DIGITAL_KEY_SHARED_DECLINE,
                )
                mStartSetup.postValue(false)
            }
            hideProgress()
        }

        fun digitalKeyInvite(
            phoneNumber: String,
            firstName: String = ToyotaConstants.EMPTY_STRING,
            lastName: String = ToyotaConstants.EMPTY_STRING,
            onInviteResult: (Boolean) -> Unit,
        ) {
            showProgress()
            dataDogUtils.logInfo(
                DigitalMopKeyUtils.DK_INVITE_TAG,
                "Endorse persona Firstname $firstName Lastname $lastName Ph $phoneNumber",
            )
            viewModelScope.launch {
                inviteWithDeviceId(firstName, lastName, phoneNumber, onInviteResult)
                hideProgress()
            }
        }

        private fun fetchPendingInvites(onPendingInvites: (Boolean) -> Unit) {
            viewModelScope.launch {
                getPendingInvites(onPendingInvites)
            }
        }

        fun showPendingInviteTasks(activity: FragmentActivity) {
            val selectedVin = applicationData.getSelectedVehicle()?.vin
            fetchPendingInvites { isSuccess ->
                if (isSuccess) {
                    digitalKeyLocalData.getPendingInviteList().let { invites ->
                        if (!invites.isNullOrEmpty()) {
                            invites
                                .findLast {
                                    it?.status == "SMS_SENT"
                                }?.run {
                                    if (selectedVin != this.vin || getCurrentVehicle()?.isRemoteUser == true) {
                                        activity.let {
                                            DigitalMopKeyUtils.showPersonaTaskDialog(
                                                this,
                                                it,
                                                true,
                                            )
                                        }
                                    }
                                }
                        }
                    }
                } else {
                    DigitalMopKeyUtils.appendLog("Fetch pending invite failed!!")
                }
            }
        }

        private suspend fun getPendingInvites(onPendingInvites: (Boolean) -> Unit) {
            digitalKeyDownloadLogic.getPendingInvites().collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        val uniqueSmsSentInvites =
                            resource.data
                                ?.payload
                                ?.invites
                                ?.filter { it.status.equals("SMS_SENT", ignoreCase = true) }
                                ?.toSet()
                                ?.toList()
                        uniqueSmsSentInvites?.let {
                            digitalKeyLocalData.setPendingInviteList(it)
                            delay(200)
                            onPendingInvites(true)
                        }
                    }
                    is Resource.Failure -> {
                        onPendingInvites(false)
                    }
                    else -> {
                        // do nothing
                    }
                }
            }
        }

        private suspend fun inviteWithDeviceId(
            firstName: String,
            lastName: String,
            phoneNumber: String,
            onInviteResult: (Boolean) -> Unit,
        ) {
            val deviceId = getOrFetchDeviceId()
            val guidTemp = digitalKeyLocalData.getGuid()
            applicationData.getSelectedVehicle()?.let { vehicleInfo ->
                digitalKeyDownloadLogic
                    .postDigitalKeyInvite(
                        request =
                            DigitalKeyInviteRequest(
                                firstName = firstName,
                                lastName = lastName,
                                phone = phoneNumber,
                                countryCode = regionManager.getRegionBasedCountryCode(),
                                primaryGuid = guidTemp,
                                primaryDeviceId = deviceId,
                                vin = vehicleInfo.vin,
                            ),
                    ).collect { resource ->
                        when (resource) {
                            is Resource.Success -> {
                                onInviteResult(true)
                                mShowInviteMessage.postValue(true)
                            }
                            is Resource.Failure -> {
                                when (resource.responseCode) {
                                    DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_DOWNLOAD_ERROR_MSG_CODE.errorCode -> {
                                        mGuestInviteError.value =
                                            DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_DOWNLOAD_ERROR_MSG_CODE.errorCode
                                    }
                                    DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_PAIRED_ERROR_MSG_CODE.errorCode -> {
                                        mGuestInviteError.value =
                                            DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_PAIRED_ERROR_MSG_CODE.errorCode
                                    } else -> {
                                        mGuestInviteError.value = "0"
                                    }
                                }
                                onInviteResult(false)
                                mShowInviteMessage.postValue(false)
                            }
                            else -> {
                                // do nothing
                            }
                        }
                    }
            }
        }

        fun getSharedKeys() {
            viewModelScope.launch {
                val deviceId = getOrFetchDeviceId()
                if (deviceId.isEmpty()) return@launch

                applicationData.getSelectedVehicle()?.let { vehicleInfo ->
                    fetchAndProcessSharedKeys(deviceId, vehicleInfo.vin)
                }
            }
        }

        private suspend fun getOrFetchDeviceId(): String {
            digitalKeyLocalData.getDeviceId().takeIf { it.isNotEmpty() }?.let { return it }

            return withContext(dispatcherProvider.io()) {
                runCatching { digitalMopKeyUtils.getDeviceId() }
                    .getOrNull()
                    ?.takeIf { it.success }
                    ?.appendString
                    ?.also { digitalKeyLocalData.setDeviceId(it) }
                    ?: ""
            }
        }

        private suspend fun fetchAndProcessSharedKeys(
            deviceId: String,
            vin: String,
        ) {
            digitalKeyDownloadLogic.getLuksCtp(deviceId, vin).collect { resource ->
                when (resource) {
                    is Resource.Success ->
                        resource.data
                            ?.payload
                            ?.invites
                            ?.let { processSharedKeys(it, vin) }
                    is Resource.Failure -> {
                        DigitalMopKeyUtils.appendLog("Failed to fetch shared keys: ${resource.error?.message}")
                    }
                    else -> {
                        // do nothing
                    }
                }
            }
        }

        private fun processSharedKeys(
            invites: List<LuksStatusInvite>,
            vin: String,
        ) {
            val filteredInvites =
                invites.filterNot {
                    it.status == DigitalKeyLukStatus.DELETION.lukStatus ||
                        it.keyType == DigitalKeyRevokeStatus.PRIMARY_LUK.type ||
                        it.phoneNo == phoneNumber
                }

            updateLocalInvites(filteredInvites, vin)
            sharedKeysLiveData.value = filteredInvites
        }

        private fun updateLocalInvites(
            invites: List<LuksStatusInvite>,
            vin: String,
        ) {
            val localInvites = digitalKeyLocalData.getInvites(vin).toImmutableList()
            val invitesToRemove =
                localInvites.filter { localInvite ->
                    invites.none { apiInvite -> apiInvite.phoneNo == localInvite.phoneNo }
                }
            invitesToRemove.forEach { digitalKeyLocalData.removeInvite(it.phoneNo) }

            digitalKeyLocalData.setInviteSize(vin, invites.size)

            invites.forEach { invite ->
                invite.vin = vin
                digitalKeyLocalData.setInvite(invite)
            }
        }

        private fun deactivateKey(keyInfo: String) {
            val response = digitalMopKeyUtils.deactivateKey(keyInfo)
            digitalMopKeyUtils.removeKey(getCurrentVehicle()?.vin.toString(), oneAppPreferenceModel)
            if (!response.success) {
                mDeactivateKeyFailed.postValue(true)
            }
        }

        fun getUserContactDetails(
            context: FragmentActivity,
            result: Uri,
        ) {
            mContactUtil.getContactID(context, result) {
                mUserDetails.postValue(it)
            }
        }

        fun getCalibrationStatus() {
            val keyInfo = getVinKeyInfo()
            keyInfo?.let {
                mCalibrationStatus.postValue(
                    digitalKeyLocalData.getCalibrationMap().getOrDefault(
                        keyInfo.keyInfoId,
                        DkCalibrationStatus(),
                    ),
                )
            }
        }

        fun setBleCalibration(digitalKeyCalibration: DigitalkeyCalibration) {
            val keyInfo = getVinKeyInfo()
            keyInfo?.let {
                val calibrationStatus =
                    digitalKeyLocalData
                        .getCalibrationMap()
                        .getOrDefault(keyInfo.keyInfoId, DkCalibrationStatus())
                if (calibrationStatus.isManualSetting) {
                    val response =
                        digitalMopKeyUtils.setCaliber(keyInfo, digitalKeyCalibration.freqValue)
                    if (response.success) {
                        digitalKeyLocalData.getCalibrationMap()[keyInfo.keyInfoId] =
                            DkCalibrationStatus(
                                true,
                                digitalkeyCalibration = digitalKeyCalibration,
                            )
                    }

                    mCalibrationStatus.postValue(
                        digitalKeyLocalData
                            .getCalibrationMap()
                            .getOrDefault(keyInfo.keyInfoId, DkCalibrationStatus()),
                    )
                    // reconnect bluetooth to apply settings
                    setDkLibBleFunctionOFF()
                }
            }
        }

        fun setCalibrationSetting() {
            val keyInfo = getVinKeyInfo()
            keyInfo?.let {
                val calibrationStatus =
                    digitalKeyLocalData
                        .getCalibrationMap()
                        .getOrDefault(keyInfo.keyInfoId, DkCalibrationStatus())
                val response =
                    digitalMopKeyUtils.setCaliber(
                        keyInfo,
                        if (!calibrationStatus.isManualSetting) {
                            calibrationStatus.digitalkeyCalibration.freqValue
                        } else {
                            dkCalMap.getOrDefault(
                                Build.MODEL,
                                NUMBER_0,
                            )
                        },
                    )
                if (response.success) {
                    calibrationStatus.isManualSetting = !calibrationStatus.isManualSetting
                }
                digitalKeyLocalData.getCalibrationMap()[keyInfo.keyInfoId] = calibrationStatus
                mCalibrationStatus.postValue(calibrationStatus)
            }
            // reconnect bluetooth to apply settings
            setDkLibBleFunctionOFF()
        }

        private fun setDkLibBleFunctionOFF() {
            val bleOffStatus = digitalMopKeyUtils.dKLib?.setBleFunctionOff()
            if (bleOffStatus?.result() == DKLib.RES_RESULT_CODE.SUCCESS) {
                showProgress()
                DigitalMopKeyUtils.appendLog("DK Calibration updated successfully setBleFunctionOff")
            } else {
                hideProgress()
                DigitalMopKeyUtils.appendLog(
                    "DK Calibration failed update setBleFunctionOff result ${bleOffStatus?.result()}  detail ${bleOffStatus?.detail()}",
                )
            }
        }

        fun setDkLibBleFunctionON() {
            val bleOnStatus = digitalMopKeyUtils.dKLib?.setBleFunctionOn()
            if (bleOnStatus?.result() == DKLib.RES_RESULT_CODE.SUCCESS) {
                DigitalMopKeyUtils.appendLog("DK Calibration updated successfully setBleFunctionOn")
            } else {
                DigitalMopKeyUtils.appendLog(
                    "DK Calibration failed update setBleFunctionOn result ${bleOnStatus?.result()}  detail ${bleOnStatus?.detail()}",
                )
            }
        }

        fun extractUsPhoneNumber(phoneNumber: String): String {
            val curSize = phoneNumber.length
            return if (curSize <= NUMBER_10) {
                phoneNumber
            } else {
                phoneNumber.substring(curSize - NUMBER_10)
            }
        }

        private fun declineSharedKeyInvitation(inviteId: String) {
            viewModelScope.launch {
                showProgress()
                val vehicleInfo = digitalKeyLocalData.getVehicle(assetPersonaTask.vin)
                vehicleInfo?.let { vehicle ->
                    val response =
                        dkRepository.deleteDigitalKeyToken(
                            keyInfoId = null,
                            phoneNumber,
                            vehicle.vin,
                            // TODO revert this hardcoded deviceId when backend is ready with the new changes.
                            DigitalMopKeyUtils.DEVICE_ID,
                            type = DigitalKeyRevokeStatus.LUK_SELF.type,
                            inviteId,
                        )
                    response.error?.let { _ ->
                        hideProgress()
                    }
                    mIsPendingInviteLeft.value = true
                }
            }
        }
    }
