package com.toyota.oneapp.digitalkey.viewmodel

import android.app.Activity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.digitalkey.*
import com.toyota.oneapp.digitalkey.CurrentStage.*
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.dataaccess.repository.DKRepository
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.InstallKeyApiRequest
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseFragmentViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.launch
import org.forgerock.android.auth.AccessToken
import org.forgerock.android.auth.ui.FRNative
import org.forgerock.android.auth.ui.FRNativeResponse
import org.forgerock.android.auth.ui.FRNativeResultListener
import toyotaone.commonlib.lifecycle.SingleLiveEvent
import javax.inject.Inject

@HiltViewModel
class DigitalKeySyncViewModel
    @Inject
    constructor(
        private val digitalKeyRepository: DKRepository,
        private val digitalKeyMopKeyUtils: DigitalMopKeyUtils,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : BaseFragmentViewModel() {
        private val mOperationFailed = SingleLiveEvent<String>()
        val operationFailed: LiveData<String> get() = mOperationFailed

        private val mEnrollment = SingleLiveEvent<Boolean>()
        val enrollment: LiveData<Boolean> get() = mEnrollment

        private val mCurrentProcess = MutableLiveData(NONE)
        val currentProcess: LiveData<CurrentStage> get() = mCurrentProcess

        private val mProcessKeyInfo = MutableLiveData<DKLib.MyKeyInfo?>()
        val processKeyInfo: LiveData<DKLib.MyKeyInfo?> get() = mProcessKeyInfo

        var isScreenOff: Boolean = false

        private val mCloseActivity = MutableLiveData<Boolean>()
        val closeActivity: LiveData<Boolean> get() = mCloseActivity

        private var isSyncTried = false

        fun getLatestIdToken() {
            mCurrentProcess.postValue(FR_TOKEN_FETCHING_START)
            FRNative.refreshAccessToken(
                object : FRNativeResultListener {
                    override fun onResult(result: FRNativeResponse) {
                        if (result.success && result.accessToken != null) {
                            consumeFRAccessToken(result.accessToken)
                            mEnrollment.postValue(true)
                        } else {
                            mEnrollment.postValue(false)
                            mCurrentProcess.postValue(FR_TOKEN_FETCHING_FAIL)
                            DigitalMopKeyUtils.appendLog(
                                "IdToken retrieval failed ${result.message} ",
                                DigitalMopKeyUtils.DK_ACTIVATE_TAG,
                                isDataDogRequired = true,
                                isError = false,
                            )
                        }
                    }
                },
            )
        }

        fun getKeyInfo() =
            applicationData.getSelectedVehicle()?.let {
                digitalKeyMopKeyUtils.getVinKeyInfo(
                    it.vin,
                    oneAppPreferenceModel,
                )
            }

        fun enrollUserDevice(activity: Activity) {
            mCurrentProcess.postValue(ENROLLMENT_OPERATION_START)
        }

        fun syncDigitalData() {
            mCurrentProcess.postValue(SYNC_OPERATION_START)
        }

        fun updateProcessEvent(currentStage: CurrentStage) {
            mCurrentProcess.postValue(currentStage)
        }

        fun logEvent(event: AnalyticsEvent) = analyticsLogger.logEvent(event)

        fun logEventWithParam(
            event: String,
            param: String,
        ) {
            analyticsLogger.logEventWithParameter(
                event,
                param,
            )
        }

        private fun consumeFRAccessToken(accessToken: AccessToken?) {
            IDPData.getInstance(oneAppPreferenceModel).accessToken = accessToken?.accessToken ?: ToyotaConstants.EMPTY_STRING
            IDPData.getInstance(oneAppPreferenceModel).refreshToken = accessToken?.refreshToken
                ?: IDPData.getInstance(oneAppPreferenceModel).refreshToken
            IDPData.getInstance(oneAppPreferenceModel).idToken = accessToken?.idToken
            val currentTimestamp = System.currentTimeMillis()
            val defaultTimestamp = currentTimestamp + ToyotaConstants.TOKEN_EXPIRE_TIME_BUFF_TIME.toLong()
            val expireMin: Long =
                if (accessToken?.expiration != null) {
                    accessToken.expiration.time
                } else {
                    defaultTimestamp
                }
            val expireTime =
                if (expireMin >
                    defaultTimestamp
                ) {
                    expireMin - ToyotaConstants.TOKEN_EXPIRE_TIME_BUFF_TIME.toLong()
                } else {
                    expireMin
                }
            IDPData.getInstance(oneAppPreferenceModel).accessTokenExpireTime = expireTime
        }

        private fun downloadKeyStart(downloadToken: String) {
            applicationData.getSelectedVehicle()?.let {
                val response =
                    digitalKeyMopKeyUtils.downloadKeyStart(
                        downloadToken,
                        it.vin,
                    )
                if (response.success) {
                    mCurrentProcess.postValue(DOWNLOAD_OWNER_KEY)
                } else {
                    mCurrentProcess.postValue(DOWNLOAD_OWNER_KEY_FAIL)
                    mOperationFailed.postValue(response.getErrorString())
                }
            }
        }

        fun processOwnerKeyRequest() {
            // If already key present in Local and MOP we are not taking latest.
            val keyInfoMop = getKeyInfo()
            digitalKeyMopKeyUtils.registerVehicleCancel()
            if (keyInfoMop?.keyInfoId.isNullOrEmpty() || keyInfoMop?.keyStatus != DKLib.KeyStatus.INSTALLED) {
                applicationData.getSelectedVehicle()?.let {
                    // Remove the key from local data key not valid.
                    digitalKeyMopKeyUtils.removeKey(it.vin, oneAppPreferenceModel)
                    // Below Condition to check whether current key Downloading or not.
                    if (currentProcess.value != DOWNLOAD_OWNER_KEY &&
                        digitalKeyMopKeyUtils.currentVehicle.isEmpty()
                    ) {
                    }
                }
            } else {
                mProcessKeyInfo.postValue(keyInfoMop)
            }
        }

        // Checking Current Process If any fail Just restarting
        fun checkCurrentProcess() {
            DigitalMopKeyUtils.appendLog(
                "CheckCurrentProcess After back ${currentProcess.value} isScreenOff $isScreenOff",
                DigitalMopKeyUtils.DK_ACTIVATE_TAG,
                isDataDogRequired = true,
                isError = false,
            )
            val response = digitalKeyMopKeyUtils.getCurrentProcess()
            if (isScreenOff) {
                isScreenOff = false
                when (currentProcess.value) {
                    DOWNLOAD_OWNER_KEY -> {
                        if (response.detCode != DKLib.RES_DETAIL_CODE.INPROCESS_SYNC_KEY.toString()) {
                            processOwnerKeyRequest()
                        }
                    }
                    MOP_INITIALIZATION -> {
                        if (response.success) {
                            syncDigitalData()
                        } else if (DKLib.RES_DETAIL_CODE.INPROCESS_INITIALIZING.toString() != response.detCode) {
                            mCloseActivity.postValue(true)
                        }
                    }
                    SYNC_OPERATION_START -> {
                    }
                    ENROLLMENT_OPERATION_FAIL, SYNC_OPERATION_FAIL, DOWNLOAD_OWNER_KEY_FAIL -> {
                        // This we need to Identify any failure event from platform calls while in background
                    }
                    else -> {
                        // Currently Else nothing need to perform
                    }
                }
            }
        }

        fun installDigitalKeyInCtp(keyInfoMop: DKLib.MyKeyInfo) {
            viewModelScope.launch {
                applicationData.getSelectedVehicle()?.vin?.let {
                    val installKeyApiRequest =
                        InstallKeyApiRequest(
                            "1234",
                            keyInfoMop.keyInfoId,
                            it,
                            keyInfoMop.keyKind.string,
                            oneAppPreferenceModel.getGuid(),
                        )
                    val response = digitalKeyRepository.installDigitalKey(installKeyApiRequest)
                    when (response) {
                        is Resource.Success -> {
                            DigitalMopKeyUtils.appendLog(
                                "SaveKey success",
                                DigitalMopKeyUtils.DK_ACTIVATE_TAG,
                                isDataDogRequired = true,
                                isError = false,
                            )
                        }
                        is Resource.Failure -> {
                            DigitalMopKeyUtils.appendLog(
                                "SaveKey Failed Error ${response.error}",
                                DigitalMopKeyUtils.DK_ACTIVATE_TAG,
                                isDataDogRequired = true,
                                isError = false,
                            )
                        }
                        else -> {}
                    }
                }
            }
        }
    }

data class AssetInfo(
    val assetId: String,
    val deviceId: String,
)
