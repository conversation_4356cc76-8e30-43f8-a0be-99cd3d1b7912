package com.toyota.oneapp.digitalkey.viewmodel

import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.digitalkey.CurrentStage
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.INTERVAL
import com.toyota.oneapp.digitalkey.TIMER_COUNT
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.application.DigitalKeyDownloadLogic
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.dataaccess.repository.DKRepository
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.HsmUnlockGetResponse
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.HsmUnlockRequest
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseFragmentViewModel
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DigitalKeyRegistrationViewModel
    @Inject
    constructor(
        private val dkRepository: DKRepository,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val digitalKeyLocalData: DigitalKeyLocalData,
        private val digitalMopKeyUtils: DigitalMopKeyUtils,
        private val digitalKeyDownloadLogic: DigitalKeyDownloadLogic,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
    ) : BaseFragmentViewModel() {
        var isOwnerKey = false
        var afterRegistrationSuccess = false
        private var bypassHSMUnlock = false
        private val mRequestHsmRequest = MutableLiveData<RemoteRegistrationResponse>()
        val requestHsmRequest: LiveData<RemoteRegistrationResponse> get() = mRequestHsmRequest

        private val mDigitalMopResponse = MutableLiveData<MopResponse>()
        val digitalMopResponse: LiveData<MopResponse> get() = mDigitalMopResponse

        private var hsmUnlockTimer: CountDownTimer? = null

        fun getCurrentVehicleKeyInfoId() =
            applicationData.getSelectedVehicle()?.let {
                digitalMopKeyUtils.getVinKeyInfo(
                    it.vin,
                    oneAppPreferenceModel,
                )
            }

        fun isToyotaBrand(): Boolean {
            applicationData.getSelectedVehicle()?.let {
                if (it.isToyotaBrand) return true
            }
            return false
        }

        fun requestHsmUnlock() {
            DigitalMopKeyUtils.appendLog("Request Hsm Unlock Called")
            if (bypassHSMUnlock) {
                startBleRegistration()
            } else {
                viewModelScope.launch {
                    val deviceId = digitalKeyLocalData.getDeviceId()
                    val vehicle =
                        applicationData.getSelectedVehicle()?.let {
                            HsmUnlockRequest(
                                vin = it.vin,
                                deviceId = deviceId,
                            )
                        }
                    when (val resource = vehicle?.let { dkRepository.postHsmUnlock(it) }) {
                        is Resource.Success -> {
                            DigitalMopKeyUtils.appendLog(
                                "RequestHsmUnlock ${resource.data}",
                                DigitalMopKeyUtils.DK_REGISTRATION_TAG,
                                isDataDogRequired = true,
                                isError = false,
                            )
                            analyticsLogger.logEventWithParameter(
                                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                AnalyticsEventParam.DK_HSM_SUCCESS,
                            )
                            startHsmTimer()
                        }
                        is Resource.Failure -> {
                            analyticsLogger.logEventWithParameter(
                                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                AnalyticsEventParam.DK_HSM_FAILED,
                            )
                            DigitalMopKeyUtils.appendLog(
                                "RequestHsmUnlock ${resource.error}",
                                DigitalMopKeyUtils.DK_REGISTRATION_TAG,
                                isDataDogRequired = true,
                                isError = true,
                            )
                            mRequestHsmRequest.postValue(
                                RemoteRegistrationResponse.RemoteRegistrationFail(
                                    resource.error.toString(),
                                ),
                            )
                        }
                        else -> {
                            analyticsLogger.logEventWithParameter(
                                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                AnalyticsEventParam.DK_HSM_FAILED,
                            )
                            mRequestHsmRequest.postValue(
                                RemoteRegistrationResponse.RemoteRegistrationFail(
                                    resource?.error.toString(),
                                ),
                            )
                            DigitalMopKeyUtils.appendLog(
                                "RequestHsmUnlock ${resource?.error}",
                                DigitalMopKeyUtils.DK_REGISTRATION_TAG,
                                isDataDogRequired = true,
                                isError = true,
                            )
                        }
                    }
                }
            }
        }

        fun checkHsmUnlockStatus() {
            viewModelScope.launch {
                val deviceId = digitalKeyLocalData.getDeviceId()
                applicationData.getSelectedVehicle()?.let { vehicle ->
                    dkRepository.checkHsmUnlockStatus(deviceId, vehicle.vin).handleHsmUnlockResponse()
                }
            }
        }

        private fun Resource<HsmUnlockGetResponse?>.handleHsmUnlockResponse() {
            when (this) {
                is Resource.Success -> {
                    if (data?.isHsmStatusUnlocked() == true) {
                        DigitalMopKeyUtils.appendLog("HSM Unlocked Successfully!")
                        hsmUnlockTimer?.cancel()
                        startBleRegistration()
                    }
                }
                is Resource.Failure -> {
                    DigitalMopKeyUtils.appendLog("HSM Unlock Check Failed: ${error?.message}")
                }
                else -> {
                    // Do nothing for other cases
                }
            }
        }

        fun logEventWithParam(
            event: String,
            param: String,
        ) {
            analyticsLogger.logEventWithParameter(
                event,
                param,
            )
        }

        private fun startHsmTimer() {
            hsmUnlockTimer =
                object : CountDownTimer(TIMER_COUNT, INTERVAL) {
                    override fun onTick(millisUntilFinished: Long) {
                        checkHsmUnlockStatus()
                    }

                    override fun onFinish() {
                        DigitalMopKeyUtils.appendLog(
                            "HSM TIME ELAPSED",
                            DigitalMopKeyUtils.DK_REGISTRATION_TAG,
                            true,
                            isError = true,
                        )
                    }
                }.start()
        }

        fun startBleRegistration() {
            applicationData.getSelectedVehicle()?.vin?.let {
                val response =
                    digitalMopKeyUtils.registerVehicleStart(
                        digitalMopKeyUtils.getKeyInfoFromLocal(
                            vin = it,
                            preferenceModel = oneAppPreferenceModel,
                        )
                            ?: ToyotaConstants.EMPTY_STRING,
                    )
                if (!response.success) {
                    mDigitalMopResponse.postValue(
                        MopResponse.VehicleRegistrationFail(CurrentStage.REGISTER_VEHICLE_RESULT),
                    )
                }
            }
        }

        fun connectVehicle() {
            applicationData.getSelectedVehicle()?.vin?.let {
                digitalMopKeyUtils
                    .getKeyInfoFromLocal(
                        vin = it,
                        preferenceModel = oneAppPreferenceModel,
                    )?.run {
                        val response = digitalMopKeyUtils.setVehicleConnectAccept(this, true)
                        if (!response.success) {
                            mDigitalMopResponse.postValue(
                                MopResponse.VehicleRegistrationFail(CurrentStage.CONNECT_VEHICLE_START),
                            )
                        }
                    }
            }
        }

        fun putKeyStatus(status: String) {
            val deviceId = digitalKeyLocalData.getDeviceId()
            applicationData.getSelectedVehicle()?.let {
                viewModelScope.launch {
                    digitalKeyDownloadLogic
                        .putGetKeyStatus(
                            it.vin,
                            deviceId,
                            status,
                        ).collect { resource ->
                            when (resource) {
                                is Resource.Success -> {
                                    DigitalMopKeyUtils.appendLog(
                                        "Key status update successful for VIN: ${it.vin}, status: $status",
                                        isDataDogRequired = true,
                                        isError = false,
                                    )
                                }
                                else -> {
                                    DigitalMopKeyUtils.appendLog(
                                        "Key status update failed for VIN: ${it.vin}, error: ${resource.error}",
                                        isDataDogRequired = true,
                                        isError = true,
                                    )
                                }
                            }
                        }
                }
            }
        }

        fun unRegisterVehicle() {
            digitalMopKeyUtils.registerVehicleCancel()
        }

        fun setCaliberDefault(keyInfo: DKLib.MyKeyInfo) {
        }

        sealed class RemoteRegistrationResponse {
            data class RequestHsmTimedOut(
                val isConnectionTimeOut: Boolean,
            ) : RemoteRegistrationResponse()

            data class TscIdFailure(
                val error: String?,
            ) : RemoteRegistrationResponse()

            data class RemoteRegistrationFail(
                val error: String?,
            ) : RemoteRegistrationResponse()
        }

        sealed class MopResponse {
            data class VehicleRegistrationFail(
                val currentStage: CurrentStage,
            ) : MopResponse()
        }
    }
