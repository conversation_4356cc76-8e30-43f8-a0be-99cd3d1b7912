package com.toyota.oneapp.digitalkey.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.component.IDPHelper
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.account.CustomerPhoneNumber
import com.toyota.oneapp.model.account.CustomerUpdate
import com.toyota.oneapp.model.account.UpdateEmailOrPhonePayload
import com.toyota.oneapp.model.subscription.AccountInfoResponse
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.api.repository.SmsOptInRepository
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.network.models.BaseResponse
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import org.forgerock.android.auth.ui.page.REGEX_PHONE
import org.forgerock.android.auth.ui.page.REGEX_PHONE2
import java.util.regex.Pattern
import javax.inject.Inject

private const val MOBILE = "MOBILE"

@HiltViewModel
class VerifyPhoneViewModel
    @Inject
    constructor(
        private val accountApiManager: AccountAPIManager,
        val preferenceModel: OneAppPreferenceModel,
        private val smsOptInRepository: SmsOptInRepository,
        private val regionManager: RegionManager,
        private val analyticsLogger: AnalyticsLogger,
        private val idpHelper: IDPHelper,
        private val applicationData: ApplicationData,
    ) : BaseViewModel() {
        var accountInfo: AccountInfoSubscriber? = null
        private val mIsPhoneVerified = MutableLiveData<PhoneVerification>()
        val isPhoneVerified: LiveData<PhoneVerification> get() = mIsPhoneVerified
        val phoneNumber = IDPData.instance?.getPhoneNumber()

        private val mRefreshToken = MutableLiveData<Boolean>()
        val refreshToken: LiveData<Boolean> get() = mRefreshToken

        private val mConsentStatus = MutableLiveData<String>()
        val consentStatus: LiveData<String> get() = mConsentStatus

        private val mHasPhoneNumber = MutableLiveData<Boolean>()
        val hasPhoneNumber: LiveData<Boolean> get() = mHasPhoneNumber
        var phoneNumberExists = !IDPData.instance?.getPhoneNumber().isNullOrEmpty()

        fun getAccountInfo() {
            showProgress()
            mHasPhoneNumber.postValue(phoneNumberExists)
            accountApiManager.sendGetAccountInfoRequest(
                getBrand(),
                preferenceModel.getGuid(),
                object : BaseCallback<AccountInfoResponse>() {
                    override fun onSuccess(response: AccountInfoResponse) {
                        response.payload.primarySubscriber?.let {
                            accountInfo = it
                            sendVerificationCode()
                        }
                    }

                    override fun onComplete() {
                        hideProgress()
                    }
                },
            )
        }

        fun updatePhone(editValue: String) {
            showProgress()
            // Object Id removed as mandatory field
            val updatePhonePayload =
                UpdateEmailOrPhonePayload(
                    CustomerUpdate(
                        ToyotaConstants.EMPTY_STRING,
                        guid = preferenceModel.getGuid(),
                        phoneNumbers =
                            listOf(
                                CustomerPhoneNumber(
                                    phoneNumber = editValue,
                                    phoneType = "MOBILE",
                                    phoneVerified = false,
                                    countryCode = regionManager.getRegionBasedCountryCode(),
                                ),
                            ),
                    ),
                )
            accountApiManager.sendUpdateVerifyEmailOrPhone(
                BuildConfig.APP_BRAND,
                preferenceModel.getGuid(),
                ToyUtil.getAssociatedSXMGeneration(applicationData.getVehicleList()),
                ToyUtil.getAssociatedSXMBrands(applicationData.getVehicleList()),
                updatePhonePayload,
                object : BaseCallback<BaseResponse?>() {
                    override fun onComplete() {
                        hideProgress()
                        getConsentStatus()
                        phoneNumberExists = true
                        mHasPhoneNumber.postValue(true)
                    }
                },
            )
        }

        fun isPhone(phone: String): Boolean =
            (Pattern.matches(REGEX_PHONE, phone) || Pattern.matches(REGEX_PHONE2, phone)) && phone.isNotEmpty()

        fun sendVerificationCode() {
            accountInfo?.let {
                analyticsLogger.logEventWithParameter(
                    AnalyticsEvent.EVENT_GROUP_DK.eventName,
                    AnalyticsEventParam.PROFILE_DK_VERIFY_CONFIRMATION_CODE,
                )
                val updateEmailOrPhonePayload =
                    UpdateEmailOrPhonePayload(
                        CustomerUpdate(
                            objectId = it.objectId,
                            guid = preferenceModel.getGuid(),
                            phoneNumbers =
                                listOf(
                                    CustomerPhoneNumber(
                                        phoneNumber = IDPData.instance?.getPhoneNumber(),
                                        phoneType = if (!it.customerPhoneNumbers.isNullOrEmpty()) it.customerPhoneNumbers[0].phoneType else MOBILE,
                                        phoneVerified = false,
                                        countryCode = it.customerPhoneNumbers[0].countryCode,
                                    ),
                                ),
                        ),
                    )
                showProgress()
                accountApiManager.sendUpdateVerifyEmailOrPhone(
                    BuildConfig.APP_BRAND,
                    preferenceModel.getGuid(),
                    ToyUtil.getAssociatedSXMGeneration(applicationData.getVehicleList()),
                    ToyUtil.getAssociatedSXMBrands(applicationData.getVehicleList()),
                    updateEmailOrPhonePayload,
                    object : BaseCallback<BaseResponse?>() {
                        override fun onComplete() {
                            hideProgress()
                            getConsentStatus()
                        }
                    },
                )
            }
        }

        fun hasPhoneRegistered(): Boolean = accountInfo?.customerPhoneNumbers?.isNullOrEmpty() == false

        fun getConsentStatus() {
            showProgress()
            viewModelScope.launch {
                when (val response = smsOptInRepository.getConsentStatus("+1".plus(phoneNumber))) {
                    is Resource.Success -> {
                        response.data?.let {
                            mConsentStatus.postValue(it.payLoad.consent)
                        }
                    }
                    else -> {
                        mConsentStatus.postValue(ToyotaConstants.EMPTY_STRING)
                    }
                }
                hideProgress()
            }
        }

        fun verify(confirmationCode: String) {
            accountInfo?.let {
                showProgress()
                accountApiManager.sendUpdateVerifyEmailOrPhone(
                    BuildConfig.APP_BRAND,
                    preferenceModel.getGuid(),
                    ToyUtil.getAssociatedSXMGeneration(applicationData.getVehicleList()),
                    ToyUtil.getAssociatedSXMBrands(applicationData.getVehicleList()),
                    UpdateEmailOrPhonePayload(
                        CustomerUpdate(
                            objectId = it.objectId,
                            guid = preferenceModel.getGuid(),
                            verificationCode = confirmationCode,
                        ),
                    ),
                    object : BaseCallback<BaseResponse?>() {
                        override fun onComplete() {
                            hideProgress()
                        }

                        override fun onFailError(
                            httpCode: Int,
                            errorMsg: String?,
                        ) {
                            mIsPhoneVerified.postValue(
                                PhoneVerification.PhoneVerificationResponse(
                                    false,
                                    errorMsg ?: ToyotaConstants.EMPTY_STRING,
                                ),
                            )
                        }

                        override fun onSuccess(response: BaseResponse?) {
                            mIsPhoneVerified.postValue(
                                PhoneVerification.PhoneVerificationResponse(true),
                            )
                            hideProgress()
                        }
                    },
                )
            }
        }

        fun updateRefreshToken() {
            showProgress()
            idpHelper.refreshFRAccessTokenInLogin(IDPHelper.RETRY_TIMES, {
                mRefreshToken.postValue(true)
                hideProgress()
            }, { i: Int, _: Exception? ->
                mRefreshToken.postValue(false)
                hideProgress()
            }, {
                mRefreshToken.postValue(false)
                hideProgress()
            }) {
                mRefreshToken.postValue(false)
                hideProgress()
            }
        }

        fun getBrand(): String? {
            val selectedVehicle = applicationData.getSelectedVehicle()
            return if (selectedVehicle != null) selectedVehicle.brand else BuildConfig.APP_BRAND
        }

        sealed class PhoneVerification {
            data class PhoneVerificationResponse(
                val isSuccess: Boolean,
                val errorMessage: String = ToyotaConstants.EMPTY_STRING,
            ) : PhoneVerification()
        }
    }
