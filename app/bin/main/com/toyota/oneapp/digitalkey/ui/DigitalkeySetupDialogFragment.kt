package com.toyota.oneapp.digitalkey.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import com.toyota.oneapp.databinding.DialogDigitalkeySetupBinding
import com.toyota.oneapp.ui.BaseBottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DigitalkeySetupDialogFragment(
    val callBack: () -> Unit,
) : BaseBottomSheetDialogFragment() {
    companion object {
        val TAG = "DigitalkeySetupDialogFragment"

        fun newInstance(callBack: () -> Unit): DigitalkeySetupDialogFragment =
            DigitalkeySetupDialogFragment(
                callBack,
            )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ) = DialogDigitalkeySetupBinding
        .inflate(
            inflater,
            container,
            false,
        ).apply {
            digitalDialogSetup.setOnClickListener {
                callBack.invoke()
                dismiss()
            }
            digitalDialogCloseImageView.setOnClickListener {
                dismiss()
            }
        }.root
}
