package com.toyota.oneapp.digitalkey.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.databinding.FragmentDigitalPersonaBinding
import com.toyota.oneapp.digitalkey.CurrentStage
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyManageViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyErrorCodeStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyLukStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyRevokeStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.helper.ErrorMessageHelper
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.toast.ToastUtil

@AndroidEntryPoint
class DigitalKeyPersonaFragment : DigitalKeyBaseFragment<FragmentDigitalPersonaBinding>() {
    private val viewModel: DigitalKeyManageViewModel by activityViewModels()
    private val arguments: DigitalKeyPersonaFragmentArgs by navArgs()

    lateinit var personaLocal: LuksStatusInvite

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(viewModel)
    }

    override fun onViewBound(
        binding: FragmentDigitalPersonaBinding,
        savedInstance: Bundle?,
    ) {
        personaLocal = arguments.localData
        if (personaLocal.name.isNotEmpty()) {
            binding.tvNameCircle.text = personaLocal.name
        }
        binding.tvNameHeader.text = personaLocal.name
        binding.tvPhone.text = personaLocal.phoneNo
        if (!personaLocal.status.isNullOrEmpty() &&
            !personaLocal.phoneNo.isNullOrEmpty() &&
            personaLocal.status == DigitalKeyLukStatus.SMS_SENT.lukStatus
        ) {
            // Show Resend button when status is Pending
            binding.clPending.visibility = View.VISIBLE
        }
        binding.btnRevokeUser.setOnClickListener {
            DialogUtil.showDialog(
                requireActivity(),
                getString(R.string.dk_revoke_key_tit),
                getString(R.string.dk_remove_key_des),
                getString(R.string.Common_remove),
                getString(R.string.Common_cancel),
                object : OnCusDialogInterface {
                    override fun onConfirmClick() {
                        viewModel.isEndorsingPhoneNumber = false
                        revokeUser()
                    }

                    override fun onCancelClick() {
                        // no-op
                    }
                },
                false,
            )
        }

        binding.btnInviteUser.setOnClickListener {
            if (personaLocal.status == DigitalKeyLukStatus.SMS_SENT.lukStatus) {
                resendInvite()
            }
        }

        viewModel.dkRevoke.observe(this) {
            if (it) {
                ToastUtil.show(
                    requireActivity(),
                    getString(R.string.dk_owner_revoke_message),
                    R.drawable.ic_toast_sign,
                )
                val broadcastIntent =
                    Intent(
                        DigitalMopKeyUtils.ACTION_SHARE_DK_SYNC,
                    )
                requireContext().sendBroadcast(broadcastIntent)

                findNavController().navigate(
                    DigitalKeyPersonaFragmentDirections.actionKeyRevokeToManageManageDigitalKey(),
                )
            } else {
                DigitalMopKeyUtils.appendLog("Revoke User $it")
                showErrorDialog(CurrentStage.REVOKE_USER, it.toString())
            }
        }
    }

    private fun revokeUser() {
        viewModel.revokeMopKey(
            personaLocal.keyInfoId,
            DigitalKeyRevokeStatus.PRIMARY_LUK.type,
            inviteId = personaLocal.id,
        )
    }

    private fun resendInvite() {
        val firstName = personaLocal.name.split("\\s".toRegex()).first()
        val lastName = personaLocal.name.split("\\s".toRegex()).last()
        DialogUtil.showDialog(
            requireActivity(),
            getString(R.string.dk_share_key_tit),
            getString(R.string.dk_share_key_des),
            getString(R.string.dk_shared_key),
            getString(R.string.Common_cancel),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    viewModel.digitalKeyInvite(
                        personaLocal.phoneNo,
                        firstName,
                        lastName,
                    ) { isSuccess ->
                        // Handle the result here based on isSuccess (true if successful, false otherwise)
                        if (isSuccess) {
                            // Invite success handling
                            findNavController().navigate(
                                DigitalKeyPersonaFragmentDirections.actionKeyRevokeToManageManageDigitalKey(),
                            )
                        } else {
                            // Invite failure handling
                            // PopUp of share key failure or display failure message
                            activity?.let {
                                context?.let {
                                    when (viewModel.guestInviteError.value) {
                                        DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_DOWNLOAD_ERROR_MSG_CODE.errorCode,
                                        DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_PAIRED_ERROR_MSG_CODE.errorCode,
                                        -> {
                                            ErrorMessageHelper.showGuestInviteErrorMessage(
                                                requireContext(),
                                            )
                                        }
                                        else -> {
                                            findNavController().navigate(
                                                DigitalKeyPersonaFragmentDirections.actionKeyRevokeToManageManageDigitalKey(),
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                    viewModel.logEventWithParam(
                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                        AnalyticsEventParam.VEHICLE_DIGITAL_KEY_SHARED_INVITE,
                    )
                }

                override fun onCancelClick() {
                    // no-op
                }
            },
            false,
        )
    }

    override fun getLayout() = R.layout.fragment_digital_persona

    override fun retryFunctionality(currentStage: CurrentStage) {
        when (currentStage) {
            CurrentStage.REVOKE_USER -> {
                revokeUser()
            }
            else -> {}
        }
    }

    override fun goBackToDashboard() {
        <EMAIL>?.runOnUiThread {
            findNavController().navigate(
                DigitalKeyPersonaFragmentDirections.actionKeyRevokeToManageManageDigitalKey(),
            )
        }
    }
}
