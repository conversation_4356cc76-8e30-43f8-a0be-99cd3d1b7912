package com.toyota.oneapp.digitalkey.ui

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemPersonaListBinding
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyLukStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.util.ToyotaConstants

class DigitalPersonaAdapter(
    private val listener: DigitalPersonaListener,
    private val context: Context,
) : RecyclerView.Adapter<DigitalPersonaAdapter.PersonaListHolder>() {
    private var personas: MutableList<LuksStatusInvite> = mutableListOf()

    inner class PersonaListHolder(
        val itemDigitalPersonaBinding: ItemPersonaListBinding,
    ) : RecyclerView.ViewHolder(
            itemDigitalPersonaBinding.root,
        )

    interface DigitalPersonaListener {
        fun onItemClickListener(personalLocal: LuksStatusInvite)
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): PersonaListHolder {
        val mDeveloperListItemBinding =
            DataBindingUtil.inflate<ItemPersonaListBinding>(
                LayoutInflater.from(parent.context),
                R.layout.item_persona_list,
                parent,
                false,
            )

        return PersonaListHolder(mDeveloperListItemBinding)
    }

    interface OnItemClickListener {
        fun onItemClick(persona: LuksStatusInvite)
    }

    override fun getItemCount(): Int = personas.size

    override fun onBindViewHolder(
        holder: PersonaListHolder,
        position: Int,
    ) {
        val personaLocal = personas[position]

        when (personaLocal.status) {
            DigitalKeyLukStatus.SMS_SENT.lukStatus, DigitalKeyLukStatus.GENERATED.lukStatus, DigitalKeyLukStatus.REGISTER_FAILURE.lukStatus, DigitalKeyLukStatus.INSTALL_FAILURE.lukStatus -> {
                holder.itemDigitalPersonaBinding.personaStatus.text =
                    context.getText(
                        R.string.dk_persona_pending_status,
                    )
            }
            DigitalKeyLukStatus.DELETION.lukStatus ->
                holder.itemDigitalPersonaBinding.personaStatus.text =
                    context.getText(
                        R.string.dk_persona_revoked_status,
                    )
            DigitalKeyLukStatus.REGISTERED.lukStatus, DigitalKeyLukStatus.INSTALLED.lukStatus ->
                holder.itemDigitalPersonaBinding.personaStatus.text =
                    context.getText(
                        R.string.Common_on,
                    )
            else ->
                holder.itemDigitalPersonaBinding.personaStatus.text =
                    context.getText(
                        R.string.dk_persona_pending_status,
                    )
        }

        holder.itemDigitalPersonaBinding.personaItemMainRl.setOnClickListener {
            personaLocal.let { listener.onItemClickListener(it) }
        }
        if (!personaLocal.name.isNullOrEmpty()) {
            holder.itemDigitalPersonaBinding.ivNameShort.text = personaLocal.name[0].toString()
        } else {
            holder.itemDigitalPersonaBinding.ivNameShort.text = ToyotaConstants.EMPTY_STRING
        }
        holder.itemDigitalPersonaBinding.itemModel = personaLocal
    }

    fun setPersonas(personas: List<LuksStatusInvite>?) {
        this.personas = personas as MutableList<LuksStatusInvite>
        notifyDataSetChanged()
    }
}
