package com.toyota.oneapp.digitalkey.ui

import android.content.Intent
import android.os.Bundle
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentLukConnectConfirmBinding
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DigitalKeyLukCompleteFragment : BaseDataBindingFragment<FragmentLukConnectConfirmBinding>() {
    override fun getLayout() = R.layout.fragment_luk_connect_confirm

    override fun onViewBound(
        binding: FragmentLukConnectConfirmBinding,
        savedInstance: Bundle?,
    ) {
        binding.btnConnect.setOnClickListener {
            findNavController().navigate(DigitalKeyLukCompleteFragmentDirections.lukComplete())
        }
        binding.cancel.setOnClickListener {
            activity?.let {
                it.startActivity(
                    Intent(OADashboardActivity.createIntent(it, Bundle())).apply {
                        putExtra(OADashboardActivity.UPDATE_DK_VEHICLE, true)
                    },
                )
            }
        }
    }
}
