package com.toyota.oneapp.digitalkey.ui

import android.content.Intent
import android.os.Bundle
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDigitalkeyRemoveBinding
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DigitalKeyRemoveConfirmationFragment : BaseDataBindingFragment<FragmentDigitalkeyRemoveBinding>() {
    override fun onViewBound(
        binding: FragmentDigitalkeyRemoveBinding,
        savedInstance: Bundle?,
    ) {
        binding.btnInviteUser.setOnClickListener {
            activity?.let {
                it.startActivity(
                    Intent(OADashboardActivity.createIntent(it, Bundle())).apply {
                        putExtra(OADashboardActivity.UPDATE_DK_VEHICLE, true)
                    },
                )
            }
        }
    }

    override fun getLayout() = R.layout.fragment_digitalkey_remove
}
