package com.toyota.oneapp.digitalkey.ui

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.FragmentDigitalkeyRegistrationBinding
import com.toyota.oneapp.digitalkey.*
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.REGISTRATION_RESULT
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.S_DETAIL
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.S_RESULT
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyRegistrationViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyStatus
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import jp.co.denso.dklib.DKLib
import javax.inject.Inject

@AndroidEntryPoint
class DigitalKeyRegistrationFragment : DigitalKeyBaseFragment<FragmentDigitalkeyRegistrationBinding>() {
    private val viewModel: DigitalKeyRegistrationViewModel by activityViewModels()
    private var isScreenOff = false
    private var bleConnected = false

    @Inject
    lateinit var applicationData: ApplicationData

    override fun onViewBound(
        binding: FragmentDigitalkeyRegistrationBinding,
        savedInstance: Bundle?,
    ) {
        initUi()
        initObservers()

        val isToyotaOrSubaruBrand =
            applicationData.getSelectedVehicle()?.isToyotaBrand == true ||
                applicationData.getSelectedVehicle()?.isSubaruBrand == true

        binding.ivKeyFob.setBackgroundResource(
            if (isToyotaOrSubaruBrand) R.drawable.ic_key_fob else R.drawable.ic_key_card,
        )
        val keyInfoId = viewModel.getCurrentVehicleKeyInfoId()
        keyInfoId?.let {
            viewModel.isOwnerKey = it.keyKind == DKLib.KeyKind.OWNER && !it.isRotation
            if (!it.bleRegister) {
                DigitalMopKeyUtils.appendLog(
                    "Is Rotation Key ${it.isRotation} KeyInfo ${it.keyInfoId}",
                )
                if (viewModel.isOwnerKey) startOwnerFlow() else startSharedUserFlow()
            } else {
                initializeConnectUi()
                viewModel.connectVehicle()
            }
        } ?: kotlin.run {
            if (requireActivity().intent.getBooleanExtra(DigitalMopKeyUtils.IS_SHARE_FLOW, false)) {
                activity?.let {
                    it.startActivity(
                        Intent(OADashboardActivity.createIntent(it, Bundle())).apply {
                            putExtra(OADashboardActivity.UPDATE_DK_VEHICLE, true)
                        },
                    )
                }
            } else {
                finishActivity()
            }
        }
    }

    private fun initObservers() {
        viewModel.run {
            requestHsmRequest.observe(this@DigitalKeyRegistrationFragment) {
                when (it) {
                    is DigitalKeyRegistrationViewModel.RemoteRegistrationResponse.TscIdFailure -> {
                        showErrorDialog(
                            CurrentStage.REMOTE_REGISTRATION_ERROR,
                            it.error
                                ?: ToyotaConstants.EMPTY_STRING,
                        )
                    }
                    is DigitalKeyRegistrationViewModel.RemoteRegistrationResponse.RemoteRegistrationFail -> {
                        showErrorDialog(
                            CurrentStage.REMOTE_REGISTRATION_ERROR,
                            it.error
                                ?: ToyotaConstants.EMPTY_STRING,
                        )
                    }
                    is DigitalKeyRegistrationViewModel.RemoteRegistrationResponse.RequestHsmTimedOut -> {
                        showErrorDialog(
                            CurrentStage.UNLOCK_ECU,
                            getString(R.string.error_message_connection_time_out),
                        )
                    }
                }
            }
            digitalMopResponse.observe(this@DigitalKeyRegistrationFragment) {
                when (it) {
                    is DigitalKeyRegistrationViewModel.MopResponse.VehicleRegistrationFail -> {
                        showErrorDialog(it.currentStage)
                    }
                }
            }
        }
    }

    private fun initUi() {
        viewDataBinding.run {
            connectBluetooth.setOnClickListener {
                initializeConnectUi()
                viewModel.requestHsmUnlock()
            }
            bluetoothBeep.setOnClickListener {
                DigitalMopKeyUtils.appendLog(
                    "User didn't hear two beeps verification",
                    DigitalMopKeyUtils.TAG,
                    isDataDogRequired = true,
                    isError = false,
                )
                viewModel.logEventWithParam(
                    AnalyticsEvent.EVENT_GROUP_DK.eventName,
                    AnalyticsEventParam.DK_SETUP_CHECKLIST,
                )
                findNavController().navigate(
                    DigitalKeyRegistrationFragmentDirections.actionRemoteToDkConnectionChecklist(),
                )
            }
            dashboardReturn.setOnClickListener {
                if (requireActivity().intent.getBooleanExtra(
                        DigitalMopKeyUtils.IS_SHARE_FLOW,
                        false,
                    )
                ) {
                    activity?.let {
                        it.startActivity(
                            Intent(OADashboardActivity.createIntent(it, Bundle())).apply {
                                putExtra(OADashboardActivity.UPDATE_DK_VEHICLE, true)
                            },
                        )
                    }
                } else {
                    finishActivity()
                }
            }
        }
    }

    private fun startSharedUserFlow() {
        initializeConnectUi()
        Handler(Looper.getMainLooper()).postDelayed({
            viewModel.startBleRegistration()
        }, LUK_BLE_DELAY_IN_MILLIS)
    }

    private fun initializeConnectUi() {
        viewDataBinding.apply {
            connectBluetooth.visibility = View.GONE
            dashboardReturn.visibility = View.VISIBLE
            bluetoothBeep.visibility = if (viewModel.isOwnerKey) View.VISIBLE else View.GONE
            ivInstrcution.visibility = View.GONE
            ivKeyFob.visibility = if (viewModel.isOwnerKey) View.VISIBLE else View.GONE
            ivBluetooth.visibility = if (!viewModel.isOwnerKey) View.VISIBLE else View.GONE
            title.text =
                if (viewModel.isOwnerKey) {
                    getText(R.string.dk_connect_vehicle_key_fob)
                } else {
                    getText(
                        R.string.connecting_to_your_vehicle,
                    )
                }
            notes.visibility = if (viewModel.isOwnerKey || viewModel.afterRegistrationSuccess) View.VISIBLE else View.GONE
            notes.text =
                if (viewModel.isOwnerKey) {
                    getText(R.string.dk_place_fob_notes)
                } else {
                    getText(
                        R.string.dk_bluetooth_connect_notes,
                    )
                }
        }
    }

    private fun startOwnerFlow() {
        Handler(Looper.getMainLooper()).postDelayed({
            viewDataBinding.connectBluetooth.isEnabled = true
        }, DELAY_IN_MILLIS)
    }

    override fun onStart() {
        super.onStart()
        val keyInfo = viewModel.getCurrentVehicleKeyInfoId()
        keyInfo?.run {
            if (this.keyStatus != DKLib.KeyStatus.SYNCING && isScreenOff && this.bleConnectFlg) {
                isScreenOff = false
                findNavController().navigate(
                    DigitalKeyRegistrationFragmentDirections.actionRemoteToBluetoothConnectComplete(),
                )
            }
        }
    }

    override fun onStop() {
        super.onStop()
        isScreenOff = true
    }

    override fun getLayout() = R.layout.fragment_digitalkey_registration

    override fun retryFunctionality(currentStage: CurrentStage) {
        when (currentStage) {
            CurrentStage.REGISTER_VEHICLE_RESULT -> {
                if (viewModel.isOwnerKey) {
                    viewModel.requestHsmUnlock()
                } else {
                    viewModel.startBleRegistration()
                }
            }
            CurrentStage.REGISTER_VEHICLE -> {
                viewModel.startBleRegistration()
            }
            CurrentStage.CONNECT_VEHICLE_START -> {
                viewModel.connectVehicle()
            }
            else -> {
                findNavController().navigate(
                    DigitalKeyRegistrationFragmentDirections.actionRemoteToDigitalKeySetup(),
                )
            }
        }
    }

    override fun goBackToDashboard() {
        viewModel.unRegisterVehicle()
        activity?.startActivity(
            Intent(activity, OADashboardActivity::class.java).setFlags(
                Intent.FLAG_ACTIVITY_CLEAR_TOP,
            ),
        )
    }

    override fun handleDigitalKeyMopEvents(intent: Intent) {
        val keyInfoId = viewModel.getCurrentVehicleKeyInfoId()
        when (intent.action) {
            DigitalMopKeyUtils.ACTION_REGISTER_VEHICLE_RESULT -> {
                val isSuccess = intent.getBooleanExtra(REGISTRATION_RESULT, false)
                if (!isSuccess) {
                    val sResult = intent.getStringExtra(S_RESULT)
                    val sDetail = intent.getStringExtra(S_DETAIL)
                    val eventParam =
                        if (viewModel.isOwnerKey) {
                            AnalyticsEventParam.DK_SETUP_FAILED_REGISTER_OWNER
                        } else {
                            AnalyticsEventParam.DK_SETUP_FAILED_REGISTER_SHARED
                        }
                    viewModel.logEventWithParam(AnalyticsEvent.EVENT_GROUP_DK.eventName, eventParam)
                    keyInfoId?.let {
                        viewModel.putKeyStatus(
                            if (it.keyKind ==
                                DKLib.KeyKind.OWNER
                            ) {
                                DigitalKeyStatus.PRIMARY_KEY_INSTALL_FAILURE.digitalKeyStatus
                            } else {
                                DigitalKeyStatus.LUK_KEY_INSTALL_FAILURE.digitalKeyStatus
                            },
                        )
                    }
                    showErrorDialog(CurrentStage.REGISTER_VEHICLE_RESULT, "$sResult $sDetail")
                } else {
                    // Once registration finishes Owner and Luk flow same . So showing LUK screen as per figma
                    val eventParam =
                        if (viewModel.isOwnerKey) {
                            AnalyticsEventParam.DK_SETUP_SUCCESS_REGISTER_OWNER
                        } else {
                            AnalyticsEventParam.DK_SETUP_SUCCESS_REGISTER_SHARED
                        }
                    viewModel.logEventWithParam(AnalyticsEvent.EVENT_GROUP_DK.eventName, eventParam)
                    keyInfoId?.let {
                        viewModel.putKeyStatus(
                            if (it.keyKind ==
                                DKLib.KeyKind.OWNER
                            ) {
                                DigitalKeyStatus.PRIMARY_KEY_INSTALL_SUCCESS.digitalKeyStatus
                            } else {
                                DigitalKeyStatus.LUK_KEY_INSTALL_SUCCESS.digitalKeyStatus
                            },
                        )
                    }
                    viewModel.isOwnerKey = false
                    viewModel.afterRegistrationSuccess = true
                    initializeConnectUi()
                }
            }
            DigitalMopKeyUtils.ACTION_KEYINFO_CHANGE -> {
                val keyInfo = viewModel.getCurrentVehicleKeyInfoId()
                // This event we will receive many time between below delay , Just added one flag to avoid multiple execution
                if (true == keyInfo?.bleConnectFlg && !bleConnected) {
                    bleConnected = true
                    viewModel.logEventWithParam(
                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                        AnalyticsEventParam.DK_SETUP_SUCCESS_ACTIVATE,
                    )
                    Handler(Looper.getMainLooper()).postDelayed({
                        viewModel.setCaliberDefault(keyInfo)
                        findNavController().navigate(
                            DigitalKeyRegistrationFragmentDirections.actionRemoteToBluetoothConnectComplete(),
                        )
                    }, REGISTRATION_DELAY)
                }
            }
            DigitalMopKeyUtils.ACTION_CONNECT_VEHICLE_RESULT -> {
                val sResult = intent.getStringExtra(S_RESULT)
                val sDetail = intent.getStringExtra(S_DETAIL)
                showErrorDialog(CurrentStage.CONNECT_VEHICLE_START, "$sResult $sDetail")
            }
        }
    }
}
