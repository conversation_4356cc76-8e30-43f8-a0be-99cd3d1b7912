package com.toyota.oneapp.digitalkey.ui

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDigitalkeyConnectionChecklistBinding
import com.toyota.oneapp.digitalkey.CurrentStage
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyRegistrationViewModel
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.util.IntentUtil.getOADashBoardIntent
import dagger.hilt.android.AndroidEntryPoint
import jp.co.denso.dklib.DKLib

@AndroidEntryPoint
class DigitalKeyConnectionChecklistFragment : DigitalKeyBaseFragment<FragmentDigitalkeyConnectionChecklistBinding>() {
    private val viewModel: DigitalKeyRegistrationViewModel by activityViewModels()

    override fun onViewBound(
        binding: FragmentDigitalkeyConnectionChecklistBinding,
        savedInstance: Bundle?,
    ) {
        binding.connectionChecklistThree.text =
            String.format(
                getString(R.string.dk_connection_checklist_desc_three),
                getString(R.string.app_name),
            )
        binding.connectionChecklistFour.text =
            String.format(
                getString(R.string.dk_connection_checklist_desc_four),
                getString(R.string.app_name),
            )

        binding.cancel.setOnClickListener {
            backToDashboard()
        }

        binding.btnConnect.setOnClickListener {
            connectToVehicle()
        }
    }

    private fun connectToVehicle() {
        val keyInfoId = viewModel.getCurrentVehicleKeyInfoId()
        keyInfoId?.let {
            viewModel.isOwnerKey = it.keyKind == DKLib.KeyKind.OWNER && !it.isRotation
            if (!it.bleRegister) {
                DigitalMopKeyUtils.appendLog(
                    "Is Rotation Key ${it.isRotation} KeyInfo ${it.keyInfoId}",
                )
                findNavController().navigate(
                    DigitalKeyConnectionChecklistFragmentDirections.dkConnectionChecklistToDigitalKeySetup(),
                )
            }
        } ?: kotlin.run {
            backToDashboard()
        }
    }

    private fun backToDashboard() {
        if (requireActivity().intent.getBooleanExtra(
                DigitalMopKeyUtils.IS_SHARE_FLOW,
                false,
            )
        ) {
            startActivity(
                getOADashBoardIntent(
                    context = activityContext,
                    isDashboardRefresh = true,
                    newAddVehicle = false,
                ),
            )
        } else {
            finishActivity()
        }
    }

    override fun getLayout() = R.layout.fragment_digitalkey_connection_checklist

    override fun retryFunctionality(currentStage: CurrentStage) {
        // Not implemented
    }

    override fun goBackToDashboard() {
        viewModel.unRegisterVehicle()
        activity?.startActivity(
            Intent(activity, OADashboardActivity::class.java).setFlags(
                Intent.FLAG_ACTIVITY_CLEAR_TOP,
            ),
        )
    }
}
