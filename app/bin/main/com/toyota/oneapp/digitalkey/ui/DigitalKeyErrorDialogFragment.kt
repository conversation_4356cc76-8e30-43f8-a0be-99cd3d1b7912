package com.toyota.oneapp.digitalkey.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDigitalErrorDialogBinding
import com.toyota.oneapp.digitalkey.CurrentStage

class DigitalKeyErrorDialogFragment : Fragment() {
    private var mListener: DigitalKeyErrorListener? = null
    private lateinit var title: CurrentStage

    companion object {
        const val TAG = "DigitalKeyErrorDialogFragment"
        private const val CONTEXT_ERROR: String = "context"
        private const val INVALID = -1
        const val TITLE: String = "title"

        fun newInstance(
            currentStage: CurrentStage,
            error: String,
        ) = DigitalKeyErrorDialogFragment().apply {
            arguments =
                Bundle().apply {
                    putString(CONTEXT_ERROR, error)
                    putSerializable(TITLE, currentStage)
                }
        }
    }

    lateinit var binding: FragmentDigitalErrorDialogBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        title = arguments?.getSerializable(TITLE) as? CurrentStage ?: CurrentStage.NONE
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        binding =
            DataBindingUtil.inflate<FragmentDigitalErrorDialogBinding>(
                inflater,
                R.layout.fragment_digital_error_dialog,
                container,
                false,
            )
        return binding.root
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        initializeExtraData()
    }

    fun setTargetListener(feedbackDialogDismissListener: DigitalKeyErrorListener) {
        mListener = feedbackDialogDismissListener
    }

    private fun initializeExtraData() {
        if (title.errorTitleId != INVALID) binding.errorTitle.text = getString(title.errorTitleId)
        if (title.errorMessageIdId != INVALID) {
            binding.enterCodeTitle.text =
                getString(
                    title.errorMessageIdId,
                )
        }

        binding.tryAgainButton.setOnClickListener {
            dismissErrorDialog()
            mListener?.retryFunctionality(title)
        }
        binding.cancel.setOnClickListener {
            dismissErrorDialog()
            mListener?.goBackToDashboard()
        }
        if (title == CurrentStage.LOCK_FAILED ||
            title == CurrentStage.UNLOCK_FAILED
        ) {
            binding.tryAgainButton.visibility = View.GONE
        }

        binding.cancel.visibility = if (title == CurrentStage.REVOKE_USER) View.GONE else View.VISIBLE
    }

    private fun dismissErrorDialog() {
        activity
            ?.supportFragmentManager
            ?.beginTransaction()
            ?.remove(this)
            ?.commit()
    }

    interface DigitalKeyErrorListener {
        fun retryFunctionality(currentStage: CurrentStage)

        fun goBackToDashboard()
    }
}
