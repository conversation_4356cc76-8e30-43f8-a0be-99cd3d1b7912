package com.toyota.oneapp.digitalkey.ui

import android.bluetooth.BluetoothAdapter
import android.os.Bundle
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDigitalKeySetupBluetoothIntroBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import javax.inject.Inject

@AndroidEntryPoint
class DigitalKeySetupBluetoothIntroFragment : BaseDataBindingFragment<FragmentDigitalKeySetupBluetoothIntroBinding>() {
    @Inject
    lateinit var blueToothAdapter: BluetoothAdapter

    override fun onViewBound(
        binding: FragmentDigitalKeySetupBluetoothIntroBinding,
        savedInstance: Bundle?,
    ) {
        initViewModelObservers(binding)
    }

    private fun initViewModelObservers(binding: FragmentDigitalKeySetupBluetoothIntroBinding) {
        setupUi(binding)
    }

    private fun setupUi(binding: FragmentDigitalKeySetupBluetoothIntroBinding) {
        binding.connectBluetooth.setOnClickListener {
            if (!blueToothAdapter.isEnabled) {
                DialogUtil.showDialog(
                    requireActivity(),
                    getString(R.string.bluetooth_notification_title),
                    getString(R.string.bluetooth_notification_des),
                    getString(R.string.Common_ok),
                    getString(R.string.Common_don_allow),
                    object : OnCusDialogInterface {
                        override fun onConfirmClick() {
                            blueToothAdapter.enable().run {
                                if (this) {
                                    findNavController().navigate(
                                        DigitalKeySetupBluetoothIntroFragmentDirections.vehicleConnect(),
                                    )
                                }
                            }
                        }

                        override fun onCancelClick() {
                            // no-op
                        }
                    },
                    false,
                )
            } else {
                findNavController().navigate(
                    DigitalKeySetupBluetoothIntroFragmentDirections.vehicleConnect(),
                )
            }
        }
    }

    override fun getLayout(): Int = R.layout.fragment_digital_key_setup_bluetooth_intro
}
