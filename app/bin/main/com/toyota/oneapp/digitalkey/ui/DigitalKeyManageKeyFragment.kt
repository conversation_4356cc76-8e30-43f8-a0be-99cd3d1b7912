/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.digitalkey.ui

import android.app.AlertDialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.button.MaterialButton
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.databinding.FragmentManageDigitalkeyBinding
import com.toyota.oneapp.digitalkey.CurrentStage
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.DEACTIVATE_RESULT
import com.toyota.oneapp.digitalkey.onboarding.DkOnBoardingActivity
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyManageViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyRevokeStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import jp.co.denso.dklib.DKLib
import jp.co.denso.dklib.DKLib.KeyKind
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.toast.ToastUtil

@AndroidEntryPoint
class DigitalKeyManageKeyFragment :
    DigitalKeyBaseFragment<FragmentManageDigitalkeyBinding>(),
    DigitalPersonaAdapter.DigitalPersonaListener {
    private val viewModel: DigitalKeyManageViewModel by activityViewModels()
    private var digitalKeyAdapter: DigitalPersonaAdapter? = null
    private var personaSize = 0

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(viewModel)
    }

    private val broadcastReceiver =
        object : BroadcastReceiver() {
            override fun onReceive(
                context: Context?,
                intent: Intent?,
            ) {
                when (intent?.action) {
                    ToyotaConstants.REMOTE_SILENT_PUSH_ACTION -> {
                        viewModel.getSharedKeys()
                    }
                }
            }
        }

    override fun onStart() {
        super.onStart()
        requireContext().registerReceiver(
            broadcastReceiver,
            IntentFilter(ToyotaConstants.REMOTE_SILENT_PUSH_ACTION),
            Context.RECEIVER_EXPORTED,
        )
    }

    override fun onStop() {
        super.onStop()
        requireContext().unregisterReceiver(broadcastReceiver)
    }

    override fun onResume() {
        getPendingInviteList()
        super.onResume()
    }

    override fun onViewBound(
        binding: FragmentManageDigitalkeyBinding,
        savedInstance: Bundle?,
    ) {
        initViewModelObservers()
    }

    override fun getLayout() = R.layout.fragment_manage_digitalkey

    private fun initViewModelObservers() {
        val vehicle = viewModel.getCurrentVehicle()
        vehicle?.vin?.let {
            viewModel.sharedKeysLiveData.observe(viewLifecycleOwner) { invites ->
                if (invites != null) {
                    personaSize = invites.size
                    updatePersonaSizeDisplay()
                }
            }
            viewModel.sharedKeysLiveData.observe(this) {
                refreshPersona(it)
            }
            processKeyInfo()

            viewModel.showInviteMessage.observe(this) {
                if (it) {
                    ToastUtil.show(
                        requireActivity(),
                        getString(R.string.dk_share_key_toast_success),
                        R.drawable.toast_check,
                    )
                } else {
                    ToastUtil.show(
                        requireActivity(),
                        getString(R.string.dk_share_key_toast_error),
                        R.drawable.toast_remove,
                    )
                }
            }

            viewDataBinding.tvSharedKeyTitle.text =
                getString(
                    R.string.dk_shared_keys_title,
                    personaSize,
                )

            viewModel.deactivateKeyFailed.observe(this) {
                if (viewModel.getVinKeyInfo()?.bleConnectFlg != true) {
                    showErrorDialog(CurrentStage.CONNECT_TO_BLE_FAILED)
                } else {
                    showErrorDialog(CurrentStage.REVOKE_USER)
                }
            }

            viewModel.dkRevoke.observe(this) {
                if (it) {
                    findNavController().navigate(
                        DigitalKeyManageKeyFragmentDirections.actionManageToDigitalRemove(),
                    )
                } else {
                    Toast
                        .makeText(
                            requireContext(),
                            requireContext().getString(R.string.revoke_failure),
                            Toast.LENGTH_LONG,
                        ).show()
                    DigitalMopKeyUtils.appendLog("Revoke User $it")
                    showErrorDialog(CurrentStage.REVOKE_USER, it.toString())
                }
            }
            initUi(vehicle)
        }
    }

    private fun refreshPersona(personas: List<LuksStatusInvite>?) {
        digitalKeyAdapter?.let {
            it.setPersonas(personas)
            if (personas != null) {
                personaSize = personas.size
            }
            updatePersonaSizeDisplay()
            viewDataBinding.tvSharedKeyTitle.text =
                getString(
                    R.string.dk_shared_keys_title,
                    personaSize,
                )
            if (personas != null) {
                viewDataBinding.connectText.visibility =
                    isVisible(
                        viewModel.getVinKeyInfo()?.keyKind == KeyKind.OWNER && personas.isEmpty(),
                    )
            }
        }
    }

    private fun initUi(vehicle: VehicleInfo) {
        GlideUtil.loadImage(
            context,
            vehicle.image,
            GlideUtil.CropTransparentTransform(),
            R.drawable.image_not_found,
            viewDataBinding.carImg,
        )
        viewDataBinding.run {
            tvCarTitleTxt.text =
                String.format(
                    "%s %s",
                    vehicle.modelYear
                        ?: ToyotaConstants.EMPTY_STRING,
                    vehicle.modelDescription ?: ToyotaConstants.EMPTY_STRING,
                )
            tvCarVin.text = vehicle.vin
            btnInviteUser.setOnClickListener {
                <EMAIL>?.let {
                    findNavController().navigate(
                        DigitalKeyManageKeyFragmentDirections.actionManageKeySharing(),
                    )
                }
            }
            btnDemo.setOnClickListener {
                startActivity(Intent(activity, DkOnBoardingActivity::class.java))
            }
            viewModel.logEventWithParam(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.VEHICLE_DIGITAL_KEY_MANAGE,
            )
        }
    }

    private fun updatePersonaSizeDisplay() {
        viewDataBinding.tvSharedKeyTitle.text =
            getString(
                R.string.dk_shared_keys_title,
                personaSize,
            )
        viewDataBinding.btnInviteUser.isEnabled = personaSize < MAX_LUK_SHARE
    }

    private fun processKeyInfo() {
        viewDataBinding.run {
            btnRemoveKey.visibility = isVisible(true)
            val keyInfo = viewModel.getVinKeyInfo()
            tvSharedNotification.visibility = if (!BuildConfig.DIGITALKEY_SHARE) View.VISIBLE else View.GONE
            tvSharedNotificationDes.visibility = if (!BuildConfig.DIGITALKEY_SHARE) View.VISIBLE else View.GONE
            if (null != keyInfo) {
                val isEligibleForSharing = BuildConfig.DIGITALKEY_SHARE && keyInfo.keyKind == KeyKind.OWNER
                // We will show invite options once Ble registration step finishes
                btnInviteUser.visibility =
                    isVisible(
                        isEligibleForSharing && keyInfo.isVehicleConnectAccept && keyInfo.bleRegister,
                    )
                btnConnectVehicle.visibility =
                    isVisible(
                        !(keyInfo.isVehicleConnectAccept && keyInfo.bleRegister),
                    )
                btnCustomKeyRange.visibility =
                    isVisible(
                        (keyInfo.isVehicleConnectAccept && keyInfo.bleRegister),
                    )
                rvMapCategory.visibility = isVisible(isEligibleForSharing)
                tvSharedKeyTitle.visibility = isVisible(isEligibleForSharing)
                // We don't need to populate Persona is shared user.
                digitalKeyAdapter?.let {} ?: run {
                    digitalKeyAdapter =
                        DigitalPersonaAdapter(
                            this@DigitalKeyManageKeyFragment,
                            requireContext(),
                        )
                }
                digitalKeyAdapter?.let {
                    rvMapCategory.apply {
                        layoutManager = LinearLayoutManager(context as Context)
                        this.adapter = adapter
                        addItemDecoration(
                            DividerItemDecoration(requireContext(), LinearLayout.VERTICAL),
                        )

                        // 3. Notify the adapter about the data change.
                        adapter?.notifyDataSetChanged()
                        layoutManager = LinearLayoutManager(context as Context)
                        adapter = digitalKeyAdapter
                        addItemDecoration(
                            DividerItemDecoration(requireContext(), LinearLayout.VERTICAL),
                        )
                    }
                }
            }
            removeClickListener(keyInfo)
            customKeyRangeListener()
            connectVehicleListener()
        }
    }

    private fun removeClickListener(keyInfo: DKLib.MyKeyInfo?) {
        viewDataBinding.run {
            btnRemoveKey.setOnClickListener {
                DialogUtil.showDialog(
                    requireActivity(),
                    getString(R.string.dk_remove_key_tit),
                    getString(R.string.dk_key_removal_confirmation_desc),
                    getString(R.string.Common_remove),
                    getString(R.string.Common_cancel),
                    object : OnCusDialogInterface {
                        override fun onConfirmClick() {
                            removeDigitalKey(keyInfo)
                        }

                        override fun onCancelClick() {
                            // no-op
                        }
                    },
                    false,
                )
            }
        }
    }

    private fun customKeyRangeListener() {
        viewDataBinding.run {
            btnCustomKeyRange.setOnClickListener {
                if (viewModel.getVinKeyInfo()?.bleConnectFlg == true) {
                    findNavController().navigate(
                        DigitalKeyManageKeyFragmentDirections.actionSyncToBluetoothCaliber(),
                    )
                } else {
                    showDkCalibrationInfo()
                }
            }
        }
    }

    private fun connectVehicleListener() {
        viewDataBinding.run {
            btnConnectVehicle.setOnClickListener {
                val keyInfoDetail = viewModel.getVinKeyInfo()
                if (keyInfoDetail?.keyKind == KeyKind.OWNER && !keyInfoDetail.isRotation) {
                    findNavController().navigate(
                        DigitalKeyManageKeyFragmentDirections.actionSyncToBluetoothInfo(),
                    )
                } else {
                    findNavController().navigate(
                        DigitalKeyManageKeyFragmentDirections.actionSyncToLukComplete(),
                    )
                }
            }
        }
    }

    private fun showDkCalibrationInfo() {
        val bottomSheetDialog = BottomSheetDialog(requireContext())
        bottomSheetDialog.setContentView(R.layout.bottom_sheet_dk_caliber)
        val dkClose: MaterialButton? = bottomSheetDialog.findViewById(R.id.btn_caliber_info)
        dkClose?.setOnClickListener { bottomSheetDialog.dismiss() }
        bottomSheetDialog.show()
    }

    private fun removeDigitalKey(keyInfo: DKLib.MyKeyInfo?) {
        if (keyInfo?.bleConnectFlg == true &&
            keyInfo.keyKind == KeyKind.OWNER &&
            keyInfo.isVehicleConnectAccept
        ) {
            val mDialogView =
                LayoutInflater.from(context).inflate(
                    R.layout.fragment_digital_key_remove_info_dialog,
                    null,
                )
            val mBuilder =
                AlertDialog
                    .Builder(context)
                    .setView(mDialogView)
            val mAlertDialog = mBuilder.show()
            mDialogView.findViewById<View>(R.id.finish_button).setOnClickListener {
                mAlertDialog.dismiss()
                if (keyInfo.keyKind == KeyKind.OWNER) {
                    removePersonaKey(isOwner = true)
                } else {
                    showErrorDialog(CurrentStage.CONNECT_TO_BLE_FAILED)
                }
            }
            mDialogView.findViewById<View>(R.id.back_btn).setOnClickListener {
                mAlertDialog.dismiss()
            }
        } else {
            removePersonaKey(isOwner = keyInfo?.keyKind == KeyKind.OWNER)
        }
    }

    /*
     * If User is owner and Key is Luk , we will not remove persona.
     * It might delete whole owner persona and shared persona
     * So we are only deactivating key for this case
     */
    private fun removePersonaKey(isOwner: Boolean = false) {
        viewModel.registerVehicleCancel()
        val keyInfo = viewModel.getVinKeyInfo()
        keyInfo?.run {
            when (keyKind) {
                KeyKind.OWNER -> {
                    viewModel.revokeMopKey(
                        keyInfoId,
                        DigitalKeyRevokeStatus.PRIMARY.type,
                        isSelfPersona = true,
                        isSharedVehicle = false,
                        inviteId = null,
                        isOwner = isOwner,
                    )
                }
                KeyKind.LIMITED -> {
                    viewModel.revokeMopKey(
                        keyInfoId,
                        DigitalKeyRevokeStatus.LUK_SELF.type,
                        isSelfPersona = true,
                        inviteId = null,
                        isSharedVehicle = viewModel.isSecondaryVehicle(),
                        isOwner = isOwner,
                    )
                }
                else -> {
                    // do nothing
                }
            }
        }
    }

    private fun getPendingInviteList() {
        val keyInfo = viewModel.getVinKeyInfo()
        keyInfo?.run {
            if (keyKind == KeyKind.OWNER) {
                viewModel.getSharedKeys()
            }
        }
    }

    private fun isVisible(isVisible: Boolean): Int = if (isVisible) View.VISIBLE else View.GONE

    override fun retryFunctionality(currentStage: CurrentStage) {
        when (currentStage) {
            CurrentStage.REVOKE_USER -> {
                removePersonaKey()
            }
            else -> {
                // do nothing
            }
        }
    }

    override fun goBackToDashboard() {
        // do nothing
    }

    override fun handleDigitalKeyMopEvents(intent: Intent) {
        when (intent.action) {
            DigitalMopKeyUtils.ACTION_REMOTE_DEACTIVATE -> {
                viewModel.hideProgress()
                val result = intent.getBooleanExtra(DEACTIVATE_RESULT, false)
                if (result) {
                    viewModel.removeOwnerKeyFromCTP(DigitalKeyRevokeStatus.PRIMARY.type, "")
                }
            }
            DigitalMopKeyUtils.ACTION_SYNC_ALL_KEY_RESULT -> {
                viewModel.hideProgress()
            }
            DigitalMopKeyUtils.ACTION_KEYINFO_CHANGE -> {
                val keyInfo = viewModel.getVinKeyInfo()
                keyInfo?.let {
                    DigitalMopKeyUtils.appendLog("KeyInfo status ${it.keyStatus}")
                    viewModel.getCurrentVehicle()?.let { vehicleInfo ->
                        initUi(vehicleInfo)
                    }
                } ?: kotlin.run {
                    DigitalMopKeyUtils.appendLog("KeyInfo is not available")
                }
            }
        }
    }

    override fun onItemClickListener(personalLocal: LuksStatusInvite) {
        personalLocal.let {
            findNavController().navigate(
                DigitalKeyManageKeyFragmentDirections.actionManageToDigitalRevoke(personalLocal),
            )
        }
    }

    companion object {
        private const val MAX_LUK_SHARE = 7
    }
}
