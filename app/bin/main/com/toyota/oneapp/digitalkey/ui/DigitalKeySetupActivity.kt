package com.toyota.oneapp.digitalkey.ui

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.navigation.fragment.NavHostFragment
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityDigitalKeySetupBinding
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyRegistrationViewModel
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.ui.baseClasses.DataBindingBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DigitalKeySetupActivity : DataBindingBaseActivity<ActivityDigitalKeySetupBinding>() {
    @Inject
    lateinit var applicationData: ApplicationData

    private val registrationViewModel: DigitalKeyRegistrationViewModel by viewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun getLayoutId() = R.layout.activity_digital_key_setup

    override fun initViews(savedInstance: Bundle?) {
        navigateToSetup()
    }

    override fun onBackPressed() {
        handleDkBackNavigation()
    }

    private fun navigateToSetup() {
        binding.apply {
            performActivitySetup(dkSetupInfoToolbar)
            dkSetupInfoToolbar.setNavigationOnClickListener {
                handleDkBackNavigation()
            }
        }
        initOnDestinationChangedListener()
    }

    private fun handleDkBackNavigation() {
        registrationViewModel.unRegisterVehicle()
        val navController = (supportFragmentManager.findFragmentById(binding.navHost.id) as NavHostFragment).navController
        val prevFrag = navController.previousBackStackEntry?.destination?.id

        if (prevFrag == null) {
            if (intent.getBooleanExtra(DigitalMopKeyUtils.IS_SHARE_FLOW, false)) {
                startActivity(
                    Intent(
                        OADashboardActivity
                            .createIntent(
                                this@DigitalKeySetupActivity,
                                Bundle(),
                            ).apply {
                                putExtra(OADashboardActivity.UPDATE_DK_VEHICLE, true)
                            },
                    ),
                )
                finish()
            } else {
                if (!navController.popBackStack()) {
                    finish()
                }
            }
        } else {
            navController.popBackStack()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        super.onCreateOptionsMenu(menu)
        return false
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean = false

    private fun initOnDestinationChangedListener() {
        val navHostFragment =
            (supportFragmentManager.findFragmentById(binding.navHost.id) as NavHostFragment)

        navHostFragment.navController.addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                R.id.digital_key_setup, R.id.verify_device, R.id.sync_digital_key,
                R.id.vehicle_connect, R.id.remote_registration, R.id.luk_complete,
                -> {
                    binding.dkSetupInfoToolbar.apply {
                        visibility = View.VISIBLE
                        title = getString(R.string.digitalkey_dashboard_title)
                        binding.dkSetupInfoToolbar.navigationIcon =
                            getDrawable(
                                R.drawable.menu_back,
                            )
                    }
                }

                R.id.manage_key_sharing -> {
                    binding.dkSetupInfoToolbar.apply {
                        visibility = View.VISIBLE
                        title =
                            getString(
                                R.string.manage_dk_key,
//                            if (manageViewModel.isOwnerPersonaWithOwnerKey()) R.string.manage_amp_share_my_key else R.string.manage_dk_key
                            )
                        binding.dkSetupInfoToolbar.navigationIcon =
                            getDrawable(
                                R.drawable.menu_back,
                            )
                    }
                }

                R.id.digital_revoke -> {
                    binding.dkSetupInfoToolbar.apply {
                        visibility = View.VISIBLE
                        title = getString(R.string.dk_remove_user_title)
                        binding.dkSetupInfoToolbar.navigationIcon =
                            getDrawable(
                                R.drawable.menu_back,
                            )
                    }
                }
                R.id.bluetooth_connect_complete, R.id.dk_connection_checklist -> {
                    binding.dkSetupInfoToolbar.apply {
                        visibility = View.VISIBLE
                        title = getString(R.string.digitalkey_dashboard_title)
                        binding.dkSetupInfoToolbar.navigationIcon = null
                    }
                }
                R.id.digital_remove_key, R.id.key_sharing, R.id.digital_caliber -> {
                    binding.dkSetupInfoToolbar.visibility = View.GONE
                }
            }
        }
    }

    companion object {
        const val TAG: String = "DigitalKeySetupActivity"
    }
}
