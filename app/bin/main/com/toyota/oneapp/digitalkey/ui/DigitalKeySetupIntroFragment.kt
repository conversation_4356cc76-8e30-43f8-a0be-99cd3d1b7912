package com.toyota.oneapp.digitalkey.ui

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.databinding.FragmentDigitalKeySetupIntroBinding
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.IS_SETUP_FLOW
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyRegistrationViewModel
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import dagger.hilt.android.AndroidEntryPoint
import jp.co.denso.dklib.DKLib
import javax.inject.Inject

@AndroidEntryPoint
class DigitalKeySetupIntroFragment : BaseDataBindingFragment<FragmentDigitalKeySetupIntroBinding>() {
    @Inject lateinit var oneAppPreferenceModel: OneAppPreferenceModel
    private val viewModel: DigitalKeyRegistrationViewModel by activityViewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (!requireActivity().intent.getBooleanExtra(IS_SETUP_FLOW, true)) {
            findNavController().navigate(
                DigitalKeySetupIntroFragmentDirections.actionSetupToManageDigitalKey(),
            )
        }
    }

    override fun onViewBound(
        binding: FragmentDigitalKeySetupIntroBinding,
        savedInstance: Bundle?,
    ) {
        setupUi()
    }

    private fun setupUi() {
        DigitalMopKeyUtils.appendLog(
            "Idp verification ${IDPData.getInstance(oneAppPreferenceModel).phoneNumberVerified()}",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
        navigateToSetup()
    }

    private fun isOwnerKey(): Boolean {
        val keyInfoId = viewModel.getCurrentVehicleKeyInfoId()

        return keyInfoId?.run {
            keyKind == DKLib.KeyKind.OWNER && !isRotation
        } ?: false
    }

    private fun navigateToSetup() {
        val idpData = IDPData.getInstance(oneAppPreferenceModel)
        val navController = findNavController()
        if (idpData.phoneNumberVerified()) {
            if (isOwnerKey()) {
                navController.navigate(
                    DigitalKeySetupIntroFragmentDirections.actionSyncToBluetoothInfo(),
                )
            } else {
                navController.navigate(
                    DigitalKeySetupIntroFragmentDirections.actionSetupToRegistration(),
                )
            }
        } else {
            navController.navigate(
                DigitalKeySetupIntroFragmentDirections.actionSetupToVerifyDevice(),
            )
        }
    }

    override fun getLayout() = R.layout.fragment_digital_key_setup_intro
}
