package com.toyota.oneapp.digitalkey.ui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.databinding.ViewDataBinding
import com.toyota.oneapp.R
import com.toyota.oneapp.digitalkey.CurrentStage
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.util.ToyotaConstants

abstract class DigitalKeyBaseFragment<B : ViewDataBinding> :
    BaseDataBindingFragment<B>(),
    DigitalKeyErrorDialogFragment.DigitalKeyErrorListener {
    private val mDkMopReceiver: BroadcastReceiver =
        object : BroadcastReceiver() {
            override fun onReceive(
                context: Context?,
                intent: Intent?,
            ) {
                intent?.let {
                    handleDigitalKeyMopEvents(it)
                }
            }
        }

    fun showErrorDialog(
        title: CurrentStage,
        error: String = ToyotaConstants.EMPTY_STRING,
    ) {
        DigitalMopKeyUtils.appendLog(
            "Current Stage Failed ${title.errorId} Exception $error",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = true,
        )
        val errorDialog =
            DigitalKeyErrorDialogFragment.newInstance(title, error).apply {
                setTargetListener(this@DigitalKeyBaseFragment)
            }
        val transaction =
            activity?.supportFragmentManager?.beginTransaction()
        transaction
            ?.add(
                R.id.error_container,
                errorDialog,
                DigitalKeyErrorDialogFragment.TAG,
            )?.addToBackStack(DigitalKeyErrorDialogFragment.TAG)
            ?.commit()
    }

    override fun onStart() {
        super.onStart()
        requireContext().registerReceiver(mDkMopReceiver, DigitalMopKeyUtils.DK_FILTER, Context.RECEIVER_EXPORTED)
    }

    override fun onStop() {
        super.onStop()
        requireContext().unregisterReceiver(mDkMopReceiver)
    }

    open fun handleDigitalKeyMopEvents(intent: Intent) {
        // Will handle in child
    }
}
