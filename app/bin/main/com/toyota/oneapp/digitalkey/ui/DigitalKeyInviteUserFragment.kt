package com.toyota.oneapp.digitalkey.ui

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.ContactsContract
import android.text.Editable
import android.text.TextWatcher
import androidx.activity.result.contract.ActivityResultContract
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.tbruyelle.rxpermissions2.Permission
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.databinding.FragmentDigitalKeyInviteNewUserBinding
import com.toyota.oneapp.digitalkey.CurrentStage
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyManageViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyErrorCodeStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.helper.ErrorMessageHelper
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import toyotaone.commonlib.contact.ContactUtil
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.permission.PermissionUtil

@AndroidEntryPoint
class DigitalKeyInviteUserFragment :
    DigitalKeyBaseFragment<FragmentDigitalKeyInviteNewUserBinding>(),
    TextWatcher {
    private val digitalKeyManageViewModel: DigitalKeyManageViewModel by activityViewModels()

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(digitalKeyManageViewModel)
    }

    private val getContactDetailsContract =
        registerForActivityResult<Void?, Uri?>(
            ContactContract(),
        ) {
            (it as? Uri)?.let { uri -> displayUserDetailsFromContacts(uri) }
        }

    class ContactContract : ActivityResultContract<Void?, Uri?>() {
        override fun createIntent(
            context: Context,
            extras: Void?,
        ): Intent = Intent(Intent.ACTION_PICK, ContactsContract.Contacts.CONTENT_URI)

        override fun parseResult(
            resultCode: Int,
            intent: Intent?,
        ): Uri? = intent?.data
    }

    override fun onViewBound(
        binding: FragmentDigitalKeyInviteNewUserBinding,
        savedInstance: Bundle?,
    ) {
        binding.apply {
            viewModel = <EMAIL>
            btnShareKey.setOnClickListener {
                if (!binding.etPhoneNumber.text.isNullOrEmpty()) {
                    DialogUtil.showDialog(
                        requireActivity(),
                        getString(R.string.dk_share_key_tit),
                        getString(R.string.dk_share_key_des),
                        getString(R.string.dk_shared_key),
                        getString(R.string.Common_cancel),
                        object : OnCusDialogInterface {
                            override fun onConfirmClick() {
                                <EMAIL>(
                                    binding.etPhoneNumber.text.toString(),
                                    etFirstName.text.toString(),
                                    etLastName.text.toString(),
                                ) { isSuccess ->
                                    // Handle the result here based on isSuccess (true if successful, false otherwise)
                                    if (isSuccess) {
                                        // Invite success handling
                                        findNavController().navigate(
                                            DigitalKeyInviteUserFragmentDirections.actionKeyShareToManageDigitalKey(),
                                        )
                                    } else {
                                        // Invite failure handling
                                        // PopUp of share key failure or display failure message
                                        activity?.let {
                                            context?.let {
                                                when (digitalKeyManageViewModel.guestInviteError.value) {
                                                    DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_DOWNLOAD_ERROR_MSG_CODE.errorCode,
                                                    DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_PAIRED_ERROR_MSG_CODE.errorCode,
                                                    -> {
                                                        ErrorMessageHelper.showGuestInviteErrorMessage(
                                                            requireContext(),
                                                        )
                                                    }
                                                    else -> {
                                                        findNavController().navigate(
                                                            DigitalKeyInviteUserFragmentDirections.actionKeyShareToManageDigitalKey(),
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                viewModel?.logEventWithParam(
                                    AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                    AnalyticsEventParam.VEHICLE_DIGITAL_KEY_SHARED_INVITE,
                                )
                            }

                            override fun onCancelClick() {
                                // no-op
                            }
                        },
                        false,
                    )
                }
            }
            btnAddFromContacts.setOnClickListener {
                PermissionUtil.checkReadContacts(requireActivity()) { aBoolean: Permission ->
                    if (aBoolean.granted) {
                        getContactDetailsContract.launch(null)
                    }
                }
            }
            ivCross.setOnClickListener {
                findNavController().navigate(
                    DigitalKeyInviteUserFragmentDirections.actionKeyShareToManageDigitalKey(),
                )
            }
            etFirstName.addTextChangedListener(this@DigitalKeyInviteUserFragment)
            etLastName.addTextChangedListener(this@DigitalKeyInviteUserFragment)
            etPhoneNumber.addTextChangedListener(this@DigitalKeyInviteUserFragment)
        }
        initViewObserver(binding)
    }

    private fun initViewObserver(binding: FragmentDigitalKeyInviteNewUserBinding) {
        digitalKeyManageViewModel.run {
            userDetails.observe(this@DigitalKeyInviteUserFragment) { userDetails ->
                when (userDetails) {
                    is ContactUtil.IssueUserInfo -> {
                        binding.run {
                            if (!userDetails.hasPhoneStatus) {
                                etFirstName.setText(ToyotaConstants.EMPTY_STRING)
                                etLastName.setText(ToyotaConstants.EMPTY_STRING)
                                etPhoneNumber.setText(ToyotaConstants.EMPTY_STRING)
                            } else {
                                etPhoneNumber.setText(
                                    extractUsPhoneNumber(userDetails.phoneNumber),
                                )
                                etFirstName.setText(userDetails.userName.first)
                                etLastName.setText(userDetails.userName.second)
                            }
                        }
                    }
                }
            }
        }
    }

    override fun getLayout() = R.layout.fragment_digital_key_invite_new_user

    override fun retryFunctionality(currentStage: CurrentStage) {
        // Not implemented
    }

    override fun goBackToDashboard() {
        <EMAIL>().navigate(
            DigitalKeyInviteUserFragmentDirections.actionKeyShareToManageDigitalKey(),
        )
    }

    private fun displayUserDetailsFromContacts(result: Uri) {
        activity?.let {
            digitalKeyManageViewModel.getUserContactDetails(it, result)
        }
    }

    override fun beforeTextChanged(
        s: CharSequence?,
        start: Int,
        count: Int,
        after: Int,
    ) {
        // no-op
    }

    override fun onTextChanged(
        s: CharSequence?,
        start: Int,
        before: Int,
        count: Int,
    ) {
        shareButtonState()
    }

    override fun afterTextChanged(s: Editable?) {
        // no-op
    }

    private fun shareButtonState() {
        viewDataBinding.run {
            btnShareKey.isEnabled =
                !(
                    etFirstName.text.isNullOrEmpty() ||
                        etLastName.text.isNullOrEmpty() ||
                        etPhoneNumber.text.isNullOrEmpty()
                )
        }
    }
}
