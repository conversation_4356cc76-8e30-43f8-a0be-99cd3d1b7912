package com.toyota.oneapp.digitalkey.ui

import android.os.Bundle
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDigitalkeyConnectIntroBinding
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DigitalKeyConnectVehicleIntroFragment : BaseDataBindingFragment<FragmentDigitalkeyConnectIntroBinding>() {
    override fun onViewBound(
        binding: FragmentDigitalkeyConnectIntroBinding,
        savedInstance: Bundle?,
    ) {
        binding.run {
            connectBluetooth.setOnClickListener {
                findNavController().navigate(
                    DigitalKeyConnectVehicleIntroFragmentDirections.actionDkConnectToRegistration(),
                )
            }
        }
    }

    override fun getLayout(): Int = R.layout.fragment_digitalkey_connect_intro
}
