package com.toyota.oneapp.digitalkey.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDigitalKeyBluetoothCompleteBinding
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyManageViewModel
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DigitalKeyCompleteSetupFragment : BaseDataBindingFragment<FragmentDigitalKeyBluetoothCompleteBinding>() {
    private val viewModel: DigitalKeyManageViewModel by activityViewModels()

    fun onBackPressed() {
        if (requireActivity().intent.getBooleanExtra(DigitalMopKeyUtils.IS_SHARE_FLOW, false)) {
            activity?.let {
                it.startActivity(
                    Intent(OADashboardActivity.createIntent(it, Bundle())).apply {
                        putExtra(OADashboardActivity.UPDATE_DK_VEHICLE, true)
                    },
                )
            }
        } else {
            activity?.setResult(Activity.RESULT_OK)
            activity?.finish()
        }
    }

    override fun getLayout() = R.layout.fragment_digital_key_bluetooth_complete

    override fun onViewBound(
        binding: FragmentDigitalKeyBluetoothCompleteBinding,
        savedInstance: Bundle?,
    ) {
        binding.manageShareKey.text =
            getString(
                if (viewModel.isOwner()) R.string.manage_amp_share_my_key else R.string.manage_dk_key,
            )

        binding.dashboardReturn.setOnClickListener {
            if (requireActivity().intent.getBooleanExtra(DigitalMopKeyUtils.IS_SHARE_FLOW, false)) {
                activity?.let {
                    it.startActivity(
                        Intent(OADashboardActivity.createIntent(it, Bundle())).apply {
                            putExtra(OADashboardActivity.UPDATE_DK_VEHICLE, true)
                        },
                    )
                }
            } else {
                if (requireActivity().intent.getBooleanExtra(
                        DigitalMopKeyUtils.IS_SHARE_FLOW,
                        false,
                    )
                ) {
                    activity?.let {
                        it.startActivity(
                            Intent(OADashboardActivity.createIntent(it, Bundle())).apply {
                                putExtra(OADashboardActivity.UPDATE_DK_VEHICLE, true)
                            },
                        )
                    }
                } else {
                    activity?.setResult(Activity.RESULT_OK)
                    activity?.finish()
                }
            }
        }
        binding.manageShareKey.setOnClickListener {
            findNavController().navigate(
                DigitalKeyCompleteSetupFragmentDirections.actionBluetoothToManageDigitalKey(),
            )
        }
    }
}
