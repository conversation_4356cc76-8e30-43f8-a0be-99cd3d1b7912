package com.toyota.oneapp.digitalkey.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.viewModelScope
import com.toyota.one_ui.R.color
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.FragmentDialogDigitalkeyShareBinding
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyManageViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.PendingInvite
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseBottomSheetDialogFragment
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.time.Duration.Companion.milliseconds

@AndroidEntryPoint
class DigitalKeyShareDialogFragment : BaseBottomSheetDialogFragment() {
    private val viewModel: DigitalKeyManageViewModel by activityViewModels()
    private lateinit var binding: FragmentDialogDigitalkeyShareBinding
    private lateinit var personaInfo: PendingInvite

    @Inject
    lateinit var digitalKeyLocalData: DigitalKeyLocalData

    @Inject
    lateinit var oneAppPreferenceModel: OneAppPreferenceModel

    var isDarkMode: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observeBaseEvents(viewModel)
        isDarkMode = oneAppPreferenceModel.isDarkModeEnabled()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentDialogDigitalkeyShareBinding.inflate(inflater, container, false)
        binding.apply {
            lifecycleOwner = viewLifecycleOwner
            viewModel = <EMAIL>
        }
        initializeExtraData()
        initializeUi()
        setDarkTheme(isDarkMode)
        return binding.root
    }

    private fun setDarkTheme(isDarkMode: Boolean) {
        binding.run {
            setImageResource(imgDkShareView, isDarkMode)
            setBackgroundColor(cardViewShareDkLayout, isDarkMode)
            setTextColors(isDarkMode)
            setAcceptButtonStyle(digitalDialogAcceptButton, isDarkMode)
        }
    }

    private fun setImageResource(
        imageView: ImageView,
        isDarkMode: Boolean,
    ) {
        val resourceId = if (isDarkMode) R.drawable.dk_dark_theme_share_key_popup else R.drawable.dk_share_key_popup
        imageView.setImageResource(resourceId)
    }

    private fun setBackgroundColor(
        view: View,
        isDarkMode: Boolean,
    ) {
        val colorResId = if (isDarkMode) R.color.dk_dark_theme_share_dialog_background else R.color.white
        view.setBackgroundColor(resources.getColor(colorResId, null))
    }

    private fun FragmentDialogDigitalkeyShareBinding.setTextColors(isDarkMode: Boolean) {
        val textColorResId = if (isDarkMode) R.color.white else R.color.black
        val textColor = resources.getColor(textColorResId, null)

        listOf(digitalkeyName, digitalSharedModel, digitalSharedHeader, digitalDialogDeclineButton)
            .forEach { it.setTextColor(textColor) }
    }

    private fun setAcceptButtonStyle(
        button: Button,
        isDarkMode: Boolean,
    ) {
        val textColorResId = if (isDarkMode) R.color.black else R.color.white
        val backgroundColorResId =
            if (isDarkMode) {
                R.color.dk_dark_theme_share_dialog_accept_button_background
            } else {
                color.oneUiColorPrimaryBase
            }

        button.setTextColor(resources.getColor(textColorResId, null))
        button.setBackgroundColor(resources.getColor(backgroundColorResId, null))
    }

    private fun initializeUi() {
        binding.run {
            digitalDialogAcceptButton.text =
                if (personaInfo.status.equals(
                        "SMS_SENT",
                        false,
                    )
                ) {
                    getText(R.string.accept)
                } else {
                    getText(R.string.Common_confirm)
                }
            digitalSharedModel.text = personaInfo.vin
        }
        viewModel.run {
            vehicleDetail.observe(this@DigitalKeyShareDialogFragment) {
                GlideUtil.loadImage(
                    context,
                    it.image,
                    GlideUtil.CropTransparentTransform(),
                    R.drawable.no_vehicle_img,
                    binding.digitalShareCarImg,
                )
                binding.digitalSharedModel.text =
                    getString(
                        R.string.Common_vehicle_name_format,
                        it.modelYear,
                        it.modelDescription,
                    )
            }
            val inviteName =
                digitalKeyLocalData.getInvites(personaInfo.vin).findLast {
                    it.status == "SMS_SENT"
                }
            binding.digitalkeyName.text = inviteName?.name ?: ToyotaConstants.EMPTY_STRING
            startSetup.observe(this@DigitalKeyShareDialogFragment) {
                viewModelScope.launch {
                    delay(400.milliseconds)
                    <EMAIL>()
                }
            }
            popupTask.observe(this@DigitalKeyShareDialogFragment) { accept ->
                activity?.let { viewModel.completePersonaEvent(accept) }
            }
        }
    }

    private fun initializeExtraData() {
        arguments?.let {
            personaInfo = it.getParcelable(PERSONA_TASK) ?: PendingInvite(
                id = "",
                vin = "",
                status = "",
            )
            viewModel.assetPersonaTask = personaInfo
            viewModel.getVehicleInfo(personaInfo.vin)
        } ?: kotlin.run { dismiss() }
    }

    companion object {
        const val TAG = "DigitalKeyShareDialogFragment"
        private const val PERSONA_TASK: String = "PERSONA_TASK"
        private const val IS_FROM_DASHBOARD = "IS_FROM_DASHBOARD"

        fun newInstance(
            personaData: PendingInvite,
            isFromDashboard: Boolean,
        ): DigitalKeyShareDialogFragment =
            DigitalKeyShareDialogFragment().apply {
                arguments =
                    Bundle().also {
                        it.putParcelable(PERSONA_TASK, personaData)
                        it.putBoolean(IS_FROM_DASHBOARD, isFromDashboard)
                    }
            }
    }
}
