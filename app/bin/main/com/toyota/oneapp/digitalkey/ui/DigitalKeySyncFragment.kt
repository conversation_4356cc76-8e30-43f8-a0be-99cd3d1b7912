package com.toyota.oneapp.digitalkey.ui

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.databinding.FragmentDigitalKeyDownloadBinding
import com.toyota.oneapp.digitalkey.CurrentStage
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.IS_SUCCESS
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.KEY_INFO_ID
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.S_DETAIL
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.S_RESULT
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeySyncViewModel
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import dagger.hilt.android.AndroidEntryPoint
import jp.co.denso.dklib.DKLib

@AndroidEntryPoint
class DigitalKeySyncFragment : DigitalKeyBaseFragment<FragmentDigitalKeyDownloadBinding>() {
    private val viewModel: DigitalKeySyncViewModel by viewModels()

    override fun onViewBound(
        binding: FragmentDigitalKeyDownloadBinding,
        savedInstance: Bundle?,
    ) {
        initViewModelObservers(binding)
    }

    override fun onStart() {
        super.onStart()
        viewModel.checkCurrentProcess()
    }

    override fun onStop() {
        super.onStop()
        viewModel.isScreenOff = true
    }

    private fun initViewModelObservers(binding: FragmentDigitalKeyDownloadBinding) {
        setupUi(binding)
        viewModel.run {
            enrollment.observe(this@DigitalKeySyncFragment) {
                if (it) {
                    retryFunctionality(CurrentStage.ENROLLMENT_OPERATION_FAIL)
                } else {
                    showErrorDialog(
                        CurrentStage.FR_TOKEN_FETCHING_FAIL,
                        CurrentStage.FR_TOKEN_FETCHING_START.errorId,
                    )
                }
            }
            operationFailed.observe(this@DigitalKeySyncFragment) {
                showErrorDialog(viewModel.currentProcess.value ?: CurrentStage.NONE, it)
            }

            closeActivity.observe(this@DigitalKeySyncFragment) {
                goBackToDashboard()
            }
            processKeyInfo.observe(this@DigitalKeySyncFragment) {
                navigateToConnectScreen(it)
            }
            currentProcess.observe(this@DigitalKeySyncFragment) {
                DigitalMopKeyUtils.appendLog(
                    "Current Setup Process $it",
                    DigitalMopKeyUtils.DK_ACTIVATE_TAG,
                    isDataDogRequired = true,
                    isError = false,
                )
                setProgressBarByStep(it)
            }
        }
    }

    private fun setProgressBarByStep(currentStage: CurrentStage) {
        when (currentStage) {
            CurrentStage.ENROLLMENT_OPERATION_START, CurrentStage.MOP_INITIALIZATION, CurrentStage.FR_TOKEN_FETCHING_START -> {
                viewDataBinding.syncProgress.setProgress(33, true)
                viewDataBinding.progressStatus.text = getString(R.string.dk_download_message_1)
            }
            CurrentStage.SYNC_OPERATION_START, CurrentStage.SELF_REGISTRATION_OPERATION -> {
                viewDataBinding.syncProgress.setProgress(66, true)
                viewDataBinding.progressStatus.text = getString(R.string.dk_download_message_2)
            }
            CurrentStage.DOWNLOAD_OWNER_KEY, CurrentStage.DOWNLOAD_OWNER_TOKEN_START -> {
                viewDataBinding.dashboardReturn.isEnabled = false
                viewDataBinding.syncProgress.setProgress(100, true)
                viewDataBinding.progressStatus.text = getString(R.string.dk_download_message_3)
            }
            else -> {
                viewDataBinding.syncProgress.setProgress(0, true)
                viewDataBinding.progressStatus.text = getString(R.string.dk_download_message_1)
            }
        }
    }

    private fun setupUi(binding: FragmentDigitalKeyDownloadBinding) {
        binding.dashboardReturn.setOnClickListener {
            cancelCurrentTask()
        }
    }

    override fun getLayout() = R.layout.fragment_digital_key_download

    override fun handleDigitalKeyMopEvents(intent: Intent) {
        DigitalMopKeyUtils.appendLog("HandleDigitalKeyMopEvents ${intent.action}")
        when (intent.action) {
            DigitalMopKeyUtils.ACTION_INITIALIZE -> {
                if (intent.getBooleanExtra(IS_SUCCESS, false)) {
                    viewModel.logEventWithParam(
                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                        AnalyticsEventParam.DK_INITIALIZATION_SUCCESS,
                    )
                    retryFunctionality(CurrentStage.SYNC_OPERATION_FAIL)
                } else {
                    viewModel.logEventWithParam(
                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                        AnalyticsEventParam.DK_INITIALIZATION_FAILED,
                    )
                    val sResult = intent.getStringExtra(S_RESULT)
                    val sDetail = intent.getStringExtra(S_DETAIL)
                    showErrorDialog(CurrentStage.MOP_INITIALIZATION_ERROR, "$sResult $sDetail")
                }
            }
            DigitalMopKeyUtils.ACTION_DOWNLOAD_RESULT -> {
                val keyInfoId = intent.getStringExtra(KEY_INFO_ID)
                if (keyInfoId.isNullOrEmpty()) {
                    val sResult = intent.getStringExtra(S_RESULT)
                    val sDetail = intent.getStringExtra(S_DETAIL)
                    viewModel.updateProcessEvent(CurrentStage.DOWNLOAD_OWNER_KEY_FAIL)
                    viewModel.logEventWithParam(
                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                        AnalyticsEventParam.DK_DOWNLOAD_FAILED,
                    )
                    showErrorDialog(CurrentStage.DOWNLOAD_OWNER_KEY_FAIL, "$sResult $sDetail")
                } else {
                    viewModel.logEventWithParam(
                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                        AnalyticsEventParam.DK_DOWNLOAD_SUCCESS,
                    )
                    viewModel.processOwnerKeyRequest()
                }
            }
            DigitalMopKeyUtils.ACTION_KEYINFO_CHANGE -> {
                viewModel.processOwnerKeyRequest()
            }
        }
    }

    private fun navigateToConnectScreen(keyInfoMop: DKLib.MyKeyInfo?) {
        keyInfoMop?.run {
            DigitalMopKeyUtils.appendLog(
                "NavigateToConnectScreen KeyType $keyKind IsRotationKey $isRotation BleRegister $bleRegister VehicleConnection Status $isVehicleConnectAccept",
                isDataDogRequired = true,
            )
            if (bleRegister && isVehicleConnectAccept) {
                findNavController().navigate(
                    DigitalKeySyncFragmentDirections.actionSyncToBluetoothConnectComplete(),
                )
            } else if (keyKind == DKLib.KeyKind.OWNER && !isRotation) {
                findNavController().navigate(
                    DigitalKeySyncFragmentDirections.actionSyncToBluetoothInfo(),
                )
            } else {
                findNavController().navigate(
                    DigitalKeySyncFragmentDirections.actionSyncToLuckComplete(),
                )
            }
            viewModel.installDigitalKeyInCtp(keyInfoMop)
        }
    }

    override fun retryFunctionality(currentStage: CurrentStage) {
        viewDataBinding.dashboardReturn.isEnabled = true
        when (currentStage) {
            CurrentStage.ENROLLMENT_OPERATION_FAIL, CurrentStage.ENROLLMENT_OPERATION_START, CurrentStage.FR_TOKEN_FETCHING_FAIL -> {
                viewModel.enrollUserDevice(requireActivity())
            }
            CurrentStage.SYNC_OPERATION_FAIL, CurrentStage.SYNC_OPERATION_START -> {
                viewModel.syncDigitalData()
            }
            CurrentStage.DOWNLOAD_OWNER_TOKEN_FAIL, CurrentStage.DOWNLOAD_OWNER_KEY, CurrentStage.DOWNLOAD_OWNER_KEY_FAIL, CurrentStage.DEVICE_ID_ERROR -> {
                viewModel.processOwnerKeyRequest()
            }
            else -> {
                // do nothing for other scenarios
            }
        }
    }

    override fun goBackToDashboard() {
        cancelCurrentTask()
    }

    private fun cancelCurrentTask() {
        activity?.let {
            it.startActivity(
                Intent(OADashboardActivity.createIntent(it, Bundle())).apply {
                    putExtra(OADashboardActivity.UPDATE_DK_VEHICLE, true)
                },
            )
        }
    }
}
