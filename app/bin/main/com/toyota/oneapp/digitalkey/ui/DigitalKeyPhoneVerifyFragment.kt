package com.toyota.oneapp.digitalkey.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.FragmentDigitalKeyVerifyDeviceBinding
import com.toyota.oneapp.digitalkey.IS_SHARE_SETUP_FLOW
import com.toyota.oneapp.digitalkey.viewmodel.VerifyPhoneViewModel
import com.toyota.oneapp.ui.accountsettings.personalinfo.SmsHangTightActivity
import com.toyota.oneapp.ui.baseClasses.BaseDataBindingFragment
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import org.forgerock.android.auth.ui.extension.FRUtils
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import javax.inject.Inject

@AndroidEntryPoint
class VerifyPhoneFragment : BaseDataBindingFragment<FragmentDigitalKeyVerifyDeviceBinding>() {
    private val viewModel: VerifyPhoneViewModel by viewModels()

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    private lateinit var editValue: String

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        observeBaseEvents(viewModel)
        viewModel.getAccountInfo()
    }

    override fun onViewBound(
        binding: FragmentDigitalKeyVerifyDeviceBinding,
        savedInstance: Bundle?,
    ) {
        binding.viewmodel = viewModel
        binding.verifyDevice.isEnabled = false
        initViewModelObservers(binding)
        viewModel.isPhoneVerified.observe(this) {
            if (it.equals(false)) {
                startHangTightScreen()
            }
        }
    }

    private fun initViewModelObservers(binding: FragmentDigitalKeyVerifyDeviceBinding) {
        setupVerifyDeviceClickListener(binding)
        setupVerificationCodeTextWatcher(binding)
        observeHasPhoneNumber(binding)
        observeIsPhoneVerified()
        observeRefreshToken()
        observeConsentStatus()
    }

    private fun setupVerifyDeviceClickListener(binding: FragmentDigitalKeyVerifyDeviceBinding) {
        binding.verifyDevice.setOnClickListener {
            if (viewModel.hasPhoneNumber.value == false) {
                handlePhoneNumberUpdate(binding)
            } else {
                viewModel.verify(binding.verificationCode.text.toString())
            }
        }
    }

    private fun handlePhoneNumberUpdate(binding: FragmentDigitalKeyVerifyDeviceBinding) {
        val inputText = binding.verificationCode.text.toString()
        if (viewModel.isPhone(inputText)) {
            editValue = inputText
            viewModel.updatePhone(editValue)
        }
    }

    private fun setupVerificationCodeTextWatcher(binding: FragmentDigitalKeyVerifyDeviceBinding) {
        binding.verificationCode.addTextChangedListener(
            object : TextWatcher {
                override fun afterTextChanged(s: Editable) {
                    // This method is intentionally empty
                }

                override fun beforeTextChanged(
                    s: CharSequence,
                    start: Int,
                    count: Int,
                    after: Int,
                ) {
                    // This method is intentionally empty
                }

                override fun onTextChanged(
                    s: CharSequence,
                    start: Int,
                    before: Int,
                    count: Int,
                ) {
                    updateVerifyButtonState(binding)
                }
            },
        )
    }

    private fun updateVerifyButtonState(binding: FragmentDigitalKeyVerifyDeviceBinding) {
        binding.verifyDevice.isEnabled =
            if (viewModel.phoneNumberExists) {
                binding.verificationCode.text
                    .toString()
                    .isNotEmpty()
            } else {
                binding.verificationCode.text
                    .toString()
                    .length == getRequiredPhoneLength()
            }
    }

    private fun getRequiredPhoneLength(): Int = if (FRUtils.isMexico() || FRUtils.isUSorCanada()) 10 else 13

    private fun observeHasPhoneNumber(binding: FragmentDigitalKeyVerifyDeviceBinding) {
        viewModel.hasPhoneNumber.observe(this) { hasPhoneNumber ->
            updateUIForPhoneNumber(binding, hasPhoneNumber)
        }
    }

    private fun updateUIForPhoneNumber(
        binding: FragmentDigitalKeyVerifyDeviceBinding,
        hasPhoneNumber: Boolean,
    ) {
        binding.apply {
            if (hasPhoneNumber) {
                alert1.visibility = View.VISIBLE
                verificationCode.text?.clear()
                subtitle.text =
                    getString(
                        R.string.verify_device_text,
                        FRUtils.phoneFormat(if (::editValue.isInitialized) editValue else viewModel.phoneNumber.toString()),
                    )
            } else {
                subtitle.text = getString(R.string.dk_verify_enter_prompt)
                alert1.visibility = View.GONE
            }
        }
    }

    private fun observeIsPhoneVerified() {
        viewModel.isPhoneVerified.observe(this) { verificationResponse ->
            when (verificationResponse) {
                is VerifyPhoneViewModel.PhoneVerification.PhoneVerificationResponse -> {
                    handlePhoneVerificationResponse(verificationResponse)
                }
            }
        }
    }

    private fun handlePhoneVerificationResponse(response: VerifyPhoneViewModel.PhoneVerification.PhoneVerificationResponse) {
        if (response.isSuccess) {
            navigateBasedOnSetupFlow()
        } else {
            showError(response.errorMessage)
        }
    }

    private fun navigateBasedOnSetupFlow() {
        if (!requireActivity().intent.getBooleanExtra(IS_SHARE_SETUP_FLOW, false)) {
            findNavController().navigate(VerifyPhoneFragmentDirections.actionVerifyToSyncDigitalKey())
        } else {
            viewModel.updateRefreshToken()
        }
    }

    private fun observeRefreshToken() {
        viewModel.refreshToken.observe(this) { isRefreshSuccessful ->
            if (isRefreshSuccessful) {
                activity?.setResult(Activity.RESULT_OK)
                finishActivity()
            } else {
                (activity as UiBaseActivity).performLogout()
            }
        }
    }

    private fun observeConsentStatus() {
        viewModel.consentStatus.observe(this) { status ->
            handleConsentStatus(status)
        }
    }

    private fun handleConsentStatus(status: String) {
        when (status) {
            ToyotaConstants.PHONE_VERIFICATION_CONSENT_NO -> showConsentErrorDialog()
            ToyotaConstants.PHONE_VERIFICATION_CONSENT_NC -> startHangTightScreen()
        }
    }

    private fun showError(errorMessage: String) {
        viewDataBinding.codeTextBox.isErrorEnabled = true
        viewDataBinding.codeTextBox.error =
            errorMessage.ifEmpty {
                getString(R.string.Verify_email_phone_confirmation_incorrect)
            }
    }

    fun consentStatus(consentStatus: String) {
        when (consentStatus) {
            ToyotaConstants.PHONE_VERIFICATION_CONSENT_NO -> {
                showConsentErrorDialog()
            }
            ToyotaConstants.PHONE_VERIFICATION_CONSENT_NC -> {
                startHangTightScreen()
            }
            else -> {
                // Yes is stayin current Screen
            }
        }
    }

    private fun showConsentErrorDialog() {
        DialogUtil.showDialog(
            activity,
            getString(R.string.important),
            getString(R.string.consent_error_dialog_message),
            null,
            getString(R.string.Common_ok),
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    finishActivity()
                }

                override fun onCancelClick() {
                    // This method is intentionally empty
                }
            },
            false,
        )
    }

    // CMS-73121: Handling a crashlytics issue when the editValue is not initialized
    private fun isEditValueInitialized() = this::editValue.isInitialized

    private fun startHangTightScreen() {
        val phoneNumber = viewModel.phoneNumber ?: ""
        val intent =
            Intent(activity, SmsHangTightActivity::class.java)
                .putExtra(ToyotaConstants.PHONE_NUMBER, phoneNumber)
        if (viewModel.hasPhoneNumber.value == true) {
            phoneVerificationContract?.launch(intent)
        } else {
            if (isEditValueInitialized()) {
                editValue = phoneNumber
            }
            phoneUpdatingContract?.launch(intent)
        }
    }

    private val phoneVerificationContract =
        activity?.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            phoneVerificationResult(result.resultCode)
        }

    private val phoneUpdatingContract =
        activity?.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
        ) { result ->
            phoneUpdatingResult(result.resultCode)
        }

    private fun phoneVerificationResult(resultCode: Int) {
        when (resultCode) {
            Activity.RESULT_OK -> {
                viewModel.getConsentStatus()
            }
            else -> {
                showSmsOptErrorDialog()
            }
        }
    }

    private fun phoneUpdatingResult(resultCode: Int) {
        when (resultCode) {
            Activity.RESULT_OK -> {
                viewModel.getConsentStatus()
                activity?.recreate()
            }
            else -> {
                showSmsOptErrorDialog()
            }
        }
    }

    private fun showSmsOptErrorDialog() {
        DialogUtil.showDialog(
            requireActivity(),
            getString(org.forgerock.android.auth.ui.R.string.important_title),
            getString(org.forgerock.android.auth.ui.R.string.show_sms_opt_time_out),
            getString(org.forgerock.android.auth.ui.R.string.fr_ok),
            null,
            object : OnCusDialogInterface {
                override fun onConfirmClick() {
                    finishActivity()
                }

                override fun onCancelClick() {
                    finishActivity()
                }
            },
            false,
        )
    }

    override fun getLayout() = R.layout.fragment_digital_key_verify_device
}
