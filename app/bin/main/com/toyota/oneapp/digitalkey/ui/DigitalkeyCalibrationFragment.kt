/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.digitalkey.ui

import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.button.MaterialButton
import com.toyota.oneapp.R
import com.toyota.oneapp.adapter.LanguageAdapter
import com.toyota.oneapp.databinding.FragmentDigitalkeyCalibrationBinding
import com.toyota.oneapp.digitalkey.CurrentStage
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.DigitalkeyCalibration
import com.toyota.oneapp.digitalkey.viewmodel.DigitalKeyManageViewModel
import com.toyota.oneapp.model.language.LanguageItem
import com.toyota.oneapp.ui.BaseViewModelNavigationEvent
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DigitalkeyCalibrationFragment :
    DigitalKeyBaseFragment<FragmentDigitalkeyCalibrationBinding>(),
    LanguageAdapter.LanguageItemClickListener {
    private val viewModel: DigitalKeyManageViewModel by activityViewModels()

    override fun retryFunctionality(currentStage: CurrentStage) {
        // do nothing
    }

    // using this var to suppress showing ble calibration being set when the view is reloaded
    private var hasViewLoaded = false

    companion object {
        private const val DK_CAL_LOW = 0
        private const val DK_CAL_HIGH = 2
        private const val DK_CAL_MED = 1
    }

    override fun goBackToDashboard() {
        <EMAIL>?.runOnUiThread {
            findNavController().navigate(
                DigitalKeyPersonaFragmentDirections.actionKeyRevokeToManageManageDigitalKey(),
            )
        }
    }

    /**
     * Show calibration update popup when calibration setting change
     */
    private fun showCalibrationSettingChange() {
        val bottomSheetDialog = BottomSheetDialog(requireContext())
        bottomSheetDialog.setContentView(R.layout.dialog_digitalkey_ble_calibration_notice)
        bottomSheetDialog.findViewById<ImageView>(R.id.dk_ble_calibration_notice_close)?.setOnClickListener {
            bottomSheetDialog.dismiss()
        }
        bottomSheetDialog.findViewById<MaterialButton>(R.id.bt_ok)?.setOnClickListener {
            bottomSheetDialog.dismiss()
        }
        bottomSheetDialog.show()
    }

    private fun isBluetoothServiceEnabled(context: Context): Boolean {
        val manager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        val adapter = manager.adapter
        return adapter.isEnabled
    }

    override fun onViewBound(
        binding: FragmentDigitalkeyCalibrationBinding,
        savedInstance: Bundle?,
    ) {
        viewModel.progressEvents.observe(
            this,
        ) {
            when (it) {
                is BaseViewModelNavigationEvent.ShowProgress -> showProgressDialog()
                is BaseViewModelNavigationEvent.HideProgress -> hideProgressDialog()
                else -> {
                    // do nothing
                }
            }
        }
        context?.let {
            ArrayAdapter
                .createFromResource(
                    it,
                    R.array.ble_cal_array,
                    android.R.layout.simple_spinner_item,
                ).also { adapter ->
                    adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
                    viewDataBinding.tvManualKeyLevel.adapter = adapter
                }
        }
        binding.ivCross.setOnClickListener {
            findNavController().navigate(
                DigitalkeyCalibrationFragmentDirections.actionCaliberToManageDigitalKey(),
            )
        }

        binding.tvManualKeyLevel.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long,
                ) {
                    if (hasViewLoaded) {
                        showCalibrationSettingChange()
                    } else {
                        hasViewLoaded = true
                    }
                    if (!isBluetoothServiceEnabled(context as Context)) {
                        showDkCaliberInfo()
                        return
                    }
                    when (position) {
                        DK_CAL_LOW -> {
                            viewModel.setBleCalibration(DigitalkeyCalibration.LOW)
                        }
                        DK_CAL_MED -> {
                            viewModel.setBleCalibration(DigitalkeyCalibration.MEDIUM)
                        }
                        DK_CAL_HIGH -> {
                            viewModel.setBleCalibration(DigitalkeyCalibration.HIGH)
                        }
                    }
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {
                    // do nothing
                }
            }
        viewModel.getCalibrationStatus()
        setCalibrationObserver(binding)
    }

    private fun setCalibrationObserver(binding: FragmentDigitalkeyCalibrationBinding) {
        with(viewModel) {
            calibrationStatus.observe(this@DigitalkeyCalibrationFragment) {
                binding.tvManualKeyLevel.setSelection(
                    when (it.digitalkeyCalibration) {
                        DigitalkeyCalibration.HIGH -> {
                            DK_CAL_HIGH
                        }
                        DigitalkeyCalibration.MEDIUM -> {
                            DK_CAL_MED
                        }
                        DigitalkeyCalibration.LOW -> {
                            DK_CAL_LOW
                        }
                        else -> {
                            DK_CAL_MED
                        }
                    },
                )

                binding.btnDkControl.text =
                    if (it.isManualSetting) {
                        getText(R.string.Common_on)
                    } else {
                        getText(
                            R.string.Common_off,
                        )
                    }
                context?.let { context ->
                    binding.btnDkControl.background.setTint(
                        ContextCompat.getColor(
                            context,
                            R.color.digitalBleCalibrationOn,
                        ),
                    )
                }
                binding.tvManualKeyLevel.isEnabled = it.isManualSetting
                binding.btnDkControl.setOnClickListener {
                    if (!isBluetoothServiceEnabled(context as Context)) {
                        showDkCaliberInfo()
                    } else {
                        showCalibrationSettingChange()
                        viewModel.setCalibrationSetting()
                    }
                }
            }

            calibrationValueUpdated.observe(this@DigitalkeyCalibrationFragment) {
                showCalibrationSettingChange()
            }
        }
    }

    override fun handleDigitalKeyMopEvents(intent: Intent) {
        DigitalMopKeyUtils.appendLog(
            "Calibration Screen HandleDigitalKeyMopEvents ${intent.action}",
        )
        when (intent.action) {
            DigitalMopKeyUtils.ACTION_REMOTE_BLE_OFF -> {
                viewModel.hideProgress()
                viewModel.setDkLibBleFunctionON()
            }
        }
    }

    private fun showDkCaliberInfo() {
        val bottomSheetDialog = BottomSheetDialog(requireContext())
        bottomSheetDialog.setContentView(R.layout.bottom_sheet_dk_caliber)
        val dkClose: MaterialButton? = bottomSheetDialog.findViewById(R.id.btn_caliber_info)
        dkClose?.setOnClickListener { bottomSheetDialog.dismiss() }
        bottomSheetDialog.show()
    }

    override fun getLayout() = R.layout.fragment_digitalkey_calibration

    override fun onLanguageItemClick(item: LanguageItem?) {
        // do nothing
    }
}
