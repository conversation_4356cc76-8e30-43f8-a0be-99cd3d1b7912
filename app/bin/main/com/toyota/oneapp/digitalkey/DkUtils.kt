package com.toyota.oneapp.digitalkey

import com.google.common.base.Strings
import com.toyota.oneapp.util.ToyotaConstants
import java.util.*

enum class RcCmd(
    val tag: Int = 0,
    val par1: Int = 0,
    val par2: Date? = null,
    val par3: Date? = null,
) {
    LOCK(0x01, 0x01),
    UNLOCK(0x01, 0x05),
    AUTHORIZATION(0x02, 0x02),
}

data class DigitalMopResponse(
    val success: Boolean,
    val funName: String,
    val resCode: String?,
    val detCode: String?,
    val appendString: String = ToyotaConstants.EMPTY_STRING,
)

fun DigitalMopResponse.getErrorString(): String =
    String.format(
        "%s %s %s",
        Strings.nullToEmpty(resCode),
        Strings.nullToEmpty(detCode),
        appendString,
    )

sealed class DkMopListenerEvent(
    val mThrowable: Throwable? = null,
) {
    data class MopSyncDKResult(
        val throwable: Throwable?,
    ) : DkMopListenerEvent(throwable)
}

sealed class DkMopControlParamListenerEvent {
    data class DkDeviceParam(
        val digitalMopResponse: DigitalMopResponse?,
    ) : DkMopControlParamListenerEvent()
}

sealed class DkMopResultStatus {
    data class DkMopInitialization(
        val digitalMopResponse: DigitalMopResponse?,
    ) : DkMopResultStatus()

    data class DkMopDownload(
        val digitalMopResponse: DigitalMopResponse?,
    ) : DkMopResultStatus()

    data class DKKeyRotationDownload(
        val digitalMopResponse: DigitalMopResponse?,
    ) : DkMopResultStatus()

    data class DkMopSync(
        val digitalMopResponse: DigitalMopResponse?,
    ) : DkMopResultStatus()
}

enum class DigitalKeySyncStatus {
    SYNC_NOT_STARTED,
    SYNC_PROGRESS,
    SYNC_COMPLETED,
    SYNC_FAILED,
}

enum class DigitalkeyCalibration(
    val freqValue: Int,
) {
    LOW(-2),
    MEDIUM(8),
    HIGH(14),
    NONE(0),
}

data class DkCalibrationStatus(
    var isManualSetting: Boolean = false,
    var digitalkeyCalibration: DigitalkeyCalibration = DigitalkeyCalibration.MEDIUM,
)

val dkCalMap =
    mapOf(
        "PIXEL 4A (5G)" to -8,
        "PIXEL 5" to -11,
        "SM-G960U" to 2,
        "PIXEL 4A" to -1,
        "PIXEL 4 XL" to -4,
        "PIXEL 3A XL" to -7,
    )
