package com.toyota.oneapp.digitalkey

import android.app.Service
import android.content.Intent
import android.os.IBinder
import javax.inject.Inject

class DigitalCardActionService : Service() {
    @Inject
    lateinit var digitalMopKeyUtils: DigitalMopKeyUtils

    override fun onBind(intent: Intent): IBinder? = null

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
    }
}
