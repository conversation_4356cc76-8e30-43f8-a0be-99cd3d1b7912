package com.toyota.oneapp.digitalkey

import com.toyota.oneapp.R
import com.toyota.oneapp.util.ToyotaConstants

private const val TOKEN_FETCH = "Token Fetch"
private const val VEHICLE_REGISTRATION = "Registration failed to start"
private const val BLE_REGISTRATION = "Ble connection failed"
private const val DK_ENROLLMENT = "Enrollment fail"
private const val DK_MOP_INITIALIZATION = "Mop Initialize"
private const val DK_SYNC = "Digitalkey sync fail"
private const val DK_OWNER_TOKEN = "Failed download owner token"
private const val DK_OWNER_KEY = "Failed download owner key"
private const val DK_SELF_REGISTRATION = "Self registration failed"
private const val DK_REMOTE_REGISTRATION = "Remote registration failed"
private const val DK_DEVICE_ID_SUCCESS = "DeviceId Success"
private const val DK_DEVICE_ID = "DeviceId Failure"
private const val DK_INVITE_USER = "Failed to Invite"
private const val DK_REVOKE_USER = "Failed to revoke"
private const val DK_UNLOCK_ECU = "Dcu command failed"
private const val DK_UNLOCKED = "failed to unlock"
private const val DK_LOCKED = "failed to lock"
private const val BLE_CONNECT_ERROR = "failed to connect to the vehicle"

enum class CurrentStage(
    var errorId: String = ToyotaConstants.EMPTY_STRING,
    var errorTitleId: Int = -1,
    var errorMessageIdId: Int = -1,
) {
    NONE,
    FR_TOKEN_FETCHING_START(TOKEN_FETCH),
    FR_TOKEN_FETCHING_FAIL(
        TOKEN_FETCH,
        R.string.dk_download_error_title,
        R.string.dk_download_error_message,
    ),
    ENROLLMENT_OPERATION_START(DK_ENROLLMENT),
    ENROLLMENT_OPERATION_FAIL(
        DK_ENROLLMENT,
        R.string.dk_download_error_title,
        R.string.dk_download_error_message,
    ),
    MOP_INITIALIZATION(DK_MOP_INITIALIZATION),
    MOP_INITIALIZATION_ERROR(
        DK_MOP_INITIALIZATION,
        R.string.dk_download_error_title,
        R.string.dk_download_error_message,
    ),
    SYNC_OPERATION_START(DK_SYNC),
    SYNC_OPERATION_FAIL(
        DK_SYNC,
        R.string.dk_download_error_title,
        R.string.dk_download_error_message,
    ),
    DOWNLOAD_OWNER_TOKEN_FAIL(
        DK_OWNER_TOKEN,
        R.string.dk_download_error_title,
        R.string.dk_download_error_message,
    ),
    DOWNLOAD_OWNER_TOKEN_START(DK_OWNER_TOKEN),
    DOWNLOAD_OWNER_KEY(DK_OWNER_KEY),
    DOWNLOAD_OWNER_KEY_FAIL(
        DK_OWNER_KEY,
        R.string.dk_download_error_title,
        R.string.dk_download_error_message,
    ),
    SELF_REGISTRATION_OPERATION(DK_SELF_REGISTRATION),
    SELF_REGISTRATION_OPERATION_FAIL(
        DK_SELF_REGISTRATION,
        R.string.dk_download_error_title,
        R.string.dk_download_error_message,
    ),
    REMOTE_REGISTRATION_ERROR(
        DK_REMOTE_REGISTRATION,
        R.string.dk_registration_title,
        R.string.dk_registration_message,
    ),
    DEVICE_ID_SUCCESS(
        DK_DEVICE_ID_SUCCESS,
        R.string.dk_registration_title,
        R.string.dk_registration_message,
    ),
    DEVICE_ID_ERROR(DK_DEVICE_ID, R.string.dk_registration_title, R.string.dk_registration_message),
    UNLOCK_ECU(DK_UNLOCK_ECU, R.string.dk_registration_title, R.string.dk_registration_message),
    REGISTER_VEHICLE(
        VEHICLE_REGISTRATION,
        R.string.dk_registration_title,
        R.string.dk_registration_message,
    ),
    REGISTER_VEHICLE_RESULT(
        VEHICLE_REGISTRATION,
        R.string.dk_registration_title,
        R.string.dk_registration_message,
    ),
    CONNECT_VEHICLE_START(
        BLE_REGISTRATION,
        R.string.dk_registration_title,
        R.string.dk_registration_message,
    ),
    INVITE_USER(
        DK_INVITE_USER,
        R.string.dk_invite_new_user_error_title,
        R.string.dk_invite_new_user_error_message,
    ),
    REVOKE_USER(
        DK_REVOKE_USER,
        R.string.dk_remove_communicate_error,
        R.string.dk_remove_communicate_error_desc,
    ),
    UNLOCK_FAILED(DK_UNLOCKED, R.string.dk_unlock_error_title, R.string.dk_lock_error_message),
    LOCK_FAILED(DK_LOCKED, R.string.dk_lock_error_title, R.string.dk_lock_error_message),
    CONNECT_TO_BLE_FAILED(
        BLE_CONNECT_ERROR,
        R.string.dk_remove_communicate_error,
        R.string.dk_remove_communicate_error_desc,
    ),
}
