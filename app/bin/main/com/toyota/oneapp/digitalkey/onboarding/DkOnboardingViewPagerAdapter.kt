package com.toyota.oneapp.digitalkey.onboarding

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

class DkOnboardingViewPagerAdapter(private val layouts: <PERSON><PERSON>yList<Int>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder> () {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): DkOnboardingSliderViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(viewType, parent, false)
        return DkOnboardingSliderViewHolder(view)
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {}

    override fun getItemViewType(position: Int): Int {
        return layouts[position]
    }

    override fun getItemCount(): Int {
        return layouts.size
    }

    inner class DkOnboardingSliderViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
