package com.toyota.oneapp.digitalkey.onboarding

import android.os.Bundle
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.viewpager2.widget.ViewPager2
import com.toyota.oneapp.R
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.databinding.ActivityDkOnboardingBinding
import com.toyota.oneapp.ui.baseClasses.UiBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import java.util.*
import javax.inject.Inject

@AndroidEntryPoint
class DkOnBoardingActivity : UiBaseActivity() {
    companion object {
        const val DEFAULT_SLIDE_INDEX = 0
    }

    @Inject
    lateinit var applicationData: ApplicationData

    private lateinit var dataBinding: ActivityDkOnboardingBinding

    private val slides: ArrayList<Int> by lazy {
        arrayListOf(
            R.layout.dk_slide_use_mobile,
            R.layout.dk_slide_near_vehicle,
            R.layout.dk_slide_unlock,
            R.layout.dk_slide_start,
            R.layout.dk_slide_lock,
        )
    }

    override fun onCreate(savedInstance: Bundle?) {
        super.onCreate(savedInstance)
        dataBinding = DataBindingUtil.setContentView(this, R.layout.activity_dk_onboarding)
        dataBinding.vpDkOnboarding.adapter = DkOnboardingViewPagerAdapter(slides)

        dataBinding.vpDkOnboarding.registerOnPageChangeCallback(
            object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    updateSlideIndicators(position)

                    val buttonText =
                        if (position == slides.lastIndex) {
                            R.string.Common_close
                        } else {
                            R.string.Common_next
                        }

                    dataBinding.btnDkOnboardingNext.text =
                        getString(buttonText).toUpperCase(
                            Locale.getDefault(),
                        )
                }
            },
        )

        dataBinding.btnDkOnboardingNext.setOnClickListener {
            val next = dataBinding.vpDkOnboarding.currentItem + 1
            if (next < slides.size) {
                dataBinding.vpDkOnboarding.setCurrentItem(next, true)
            } else {
                finish()
            }
        }

        dataBinding.ivDkClose.setOnClickListener { finish() }

        updateSlideIndicators(DEFAULT_SLIDE_INDEX)
    }

    private fun updateSlideIndicators(currentSlide: Int) {
        dataBinding.llDkOnboardingIndicators.removeAllViews()

        for (slide in slides.indices) {
            val indicator = ImageView(this)
            indicator.setImageDrawable(
                ContextCompat.getDrawable(
                    this,
                    if (slide == currentSlide) R.drawable.dk_onboarding_indicator_filled else R.drawable.dk_onboarding_indicator_empty,
                ),
            )
            dataBinding.llDkOnboardingIndicators.addView(indicator)
        }
    }
}
