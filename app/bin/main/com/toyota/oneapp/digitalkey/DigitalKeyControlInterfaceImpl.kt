package com.toyota.oneapp.digitalkey

import jp.co.denso.dkctllib.DKCtlLib
import jp.co.denso.dklib.DKLib.*

class DigitalKeyControlInterfaceImpl : DKCtlLib.DKCtlLibInterface {
    override fun initializationResult(
        p0: NOT_RESULT_CODE?,
        p1: NOT_DETAIL_CODE?,
    ) {
        DigitalMopKeyUtils.appendLog(
            "DigitalControl SDK InitializationResult DkCore lib $p0",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
    }

    override fun vehicleInfoAdvice(
        keyInfoId: String,
        par1: Int,
        par2: Int,
    ) {
        val strPar1 = String.format("0x%02x", par1)
        val strPar2 = String.format("0x%02x", par2)
        val lineSeparator = System.getProperty("line.separator")
        var outPar1 = "Kind=$strPar1:"
        var outPar2 = "Content=$strPar2:"
        when (par1) {
            NUMBER_1 -> {
                outPar1 += SMART_OPERATION
                outPar2 =
                    when (par2) {
                        1 -> {
                            outPar2 + SMART_WORKING
                        }
                        2 -> {
                            outPar2 + SMART_STOP
                        }
                        else -> {
                            outPar2 + UNKNOWN
                        }
                    }
            }
            NUMBER_2 -> {
                outPar1 += POWER_SAVING
                outPar2 =
                    when (par2) {
                        1 -> {
                            outPar2 + POWER_SAVING_MODE
                        }
                        2 -> {
                            outPar2 + POWER_NORMAL_MODE
                        }
                        else -> {
                            outPar2 + UNKNOWN
                        }
                    }
            }
            NUMBER_3 -> {
                outPar1 += POWER_SAVING_MODE_10MIN
                outPar2 =
                    when (par2) {
                        1 -> {
                            outPar2 + POWER_SAVING_MODE_10MIN
                        }
                        2 -> {
                            outPar2 + POWER_NORMAL_MODE
                        }
                        else -> {
                            outPar2 + UNKNOWN
                        }
                    }
            }
            NUMBER_4 -> {
                outPar1 += ENGINE_OFF
                outPar2 =
                    if (par2 != 1) {
                        outPar2 + UNKNOWN
                    } else {
                        outPar2 + ENGINE_OFF
                    }
            }
            NUMBER_5 -> {
                outPar1 += FEED_BACK_DOOR_LOCKED
                outPar2 =
                    if (par2 == 1) {
                        outPar2 + FEED_BACK_ALL_DOOR_LOCKED
                    } else {
                        outPar2 + UNKNOWN
                    }
            }
            NUMBER_6 -> {
                outPar1 += INGREDIENT_NOTIFY
                outPar2 =
                    if (par2 != 1) {
                        outPar2 + UNKNOWN
                    } else {
                        outPar2 + INGREDIENT_UNLOCKING
                    }
            }
            else -> {
                outPar1 += UNKNOWN
                outPar2 += UNKNOWN
            }
        }

        val strGuide = keyInfoId + lineSeparator + outPar1 + lineSeparator + outPar2
        DigitalMopKeyUtils.appendLog(
            "VehicleInfoAdvice  $strGuide",
            DigitalMopKeyUtils.TAG,
            isDataDogRequired = true,
            isError = false,
        )
    }

    // These methods are not required for Current requirement
    override fun batteryLowAlarm(
        keyInfoId: String,
        batteryTh: BatteryTh,
    ) {
        // This method is intentionally left empty.
    }

    override fun bleIcVersionCheckResult(
        result: NOT_RESULT_CODE?,
        detail: NOT_DETAIL_CODE?,
        keyInfoId: String?,
        fwUpdStatus: FwUpdStatus?,
        vehicleBleIcVersion: ByteArray?,
        updBleIcVersion: ByteArray?,
    ): RES_RESULT_CODE = RES_RESULT_CODE.SUCCESS

    override fun getDeviceParameterResult(
        p0: NOT_RESULT_CODE?,
        p1: NOT_DETAIL_CODE?,
        p2: Byte,
        p3: Byte,
        p4: Byte,
        p5: Byte,
    ) {
        // This method is intentionally left empty.
    }

    override fun bleUpdSoftTransferResult(
        p0: NOT_RESULT_CODE?,
        p1: NOT_DETAIL_CODE?,
        p2: String?,
    ) {
        // This method is intentionally left empty.
    }
}
