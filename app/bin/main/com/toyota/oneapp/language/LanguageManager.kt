package com.toyota.oneapp.language

import android.text.TextUtils
import com.toyota.oneapp.model.language.RegionItem
import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import java.util.*
import javax.inject.Inject

class LanguageManager
    @Inject
    constructor(
        private val languagePreferenceModel: LanguagePreferenceModel,
    ) {
        fun getCurrentLanguage(): String {
            return languagePreferenceModel.getLanguage()
        }

        fun getCurrentRegion(): RegionItem {
            return languagePreferenceModel.getRegion()
        }

        fun getCurrentLocale(): Locale {
            val region = getCurrentRegion()
            val language = getCurrentLanguage()
            return getLocaleFromCode(language + "-" + region.regionCode)
        }

        fun getCurrentLocaleString(): String {
            val locale = getCurrentLocale()
            return getStringFromLocale(locale)
        }

        fun getLanguageStringFromLocale(locale: String): String = getLocaleFromCode(locale).language

        fun getSXMPaymentLocaleString(): String {
            return when (getCurrentLocale()) {
                LanguagePreferenceModel.US_ENGLISH ->
                    getStringFromLocale(
                        LanguagePreferenceModel.US_ENGLISH,
                    )
                LanguagePreferenceModel.TDPR_ENGLISH ->
                    getStringFromLocale(
                        LanguagePreferenceModel.US_ENGLISH,
                    )
                LanguagePreferenceModel.CANADA_ENGLISH ->
                    getStringFromLocale(
                        LanguagePreferenceModel.CANADA_ENGLISH,
                    )
                LanguagePreferenceModel.CANADA_FRANCE ->
                    getStringFromLocale(
                        LanguagePreferenceModel.CANADA_FRANCE,
                    )
                LanguagePreferenceModel.TDPR_SPANISH ->
                    getStringFromLocale(
                        LanguagePreferenceModel.TDPR_SPANISH,
                    )
                else -> getStringFromLocale(LanguagePreferenceModel.US_ENGLISH)
            }
        }

        fun getLocaleFromCode(code: String): Locale {
            return when {
                code.equals(getStringFromLocale(LanguagePreferenceModel.US_ENGLISH), ignoreCase = true) -> {
                    LanguagePreferenceModel.US_ENGLISH
                }
                code.equals(
                    getStringFromLocale(LanguagePreferenceModel.TDPR_ENGLISH),
                    ignoreCase = true,
                ) -> {
                    LanguagePreferenceModel.TDPR_ENGLISH
                }
                code.equals(
                    getStringFromLocale(LanguagePreferenceModel.TDPR_SPANISH),
                    ignoreCase = true,
                ) -> {
                    LanguagePreferenceModel.TDPR_SPANISH
                }
                code.equals(
                    getStringFromLocale(LanguagePreferenceModel.CANADA_ENGLISH),
                    ignoreCase = true,
                ) -> {
                    LanguagePreferenceModel.CANADA_ENGLISH
                }
                code.equals(
                    getStringFromLocale(LanguagePreferenceModel.CANADA_FRANCE),
                    ignoreCase = true,
                ) -> {
                    LanguagePreferenceModel.CANADA_FRANCE
                }
                code.equals(
                    getStringFromLocale(LanguagePreferenceModel.MEXICO_ENGLISH),
                    ignoreCase = true,
                ) -> {
                    LanguagePreferenceModel.MEXICO_ENGLISH
                }
                code.equals(
                    getStringFromLocale(LanguagePreferenceModel.MEXICO_SPANISH),
                    ignoreCase = true,
                ) -> {
                    LanguagePreferenceModel.MEXICO_SPANISH
                }
                else -> {
                    LanguagePreferenceModel.US_ENGLISH
                }
            }
        }

        companion object {
            fun getStringFromLocale(locale: Locale): String {
                val country = if (TextUtils.isEmpty(locale.country)) "US" else locale.country
                return locale.language.toLowerCase() + "-" + country.toUpperCase()
            }
        }
    }
