package com.toyota.oneapp.language

import com.toyota.oneapp.app.ApplicationData
import java.text.NumberFormat
import java.util.Locale
import javax.inject.Inject

class RegionManager
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val languageManager: LanguageManager,
    ) {
        companion object {
            const val CODE_US = "US"
            const val CODE_CA = "CA"
            const val CODE_PR = "PR"
            const val CODE_HI = "HI"
            const val CODE_MX = "MX"
            const val MX_COUNTRY_CODE = "52"
            const val NA_COUNTRY_CODE = "1"
        }

        fun isSelectedVehicleAmerican(): Boolean = isRegionCodeAmerican(applicationData.getSelectedVehicle()?.region)

        fun isSelectedVehicleCanadian(): Boolean = isRegionCodeCanadian(applicationData.getSelectedVehicle()?.region)

        fun isRegionCodeAmerican(code: String?): Boolean = CODE_US.equals(code, ignoreCase = true)

        fun isRegionCodeCanadian(code: String?): Boolean = CODE_CA.equals(code, ignoreCase = true)

        fun isRegionCodeHawaii(code: String): Boolean = CODE_HI.equals(code, ignoreCase = true)

        fun isRegionCodeMexico(code: String?): Boolean = CODE_MX.equals(code, ignoreCase = true)

        fun isPreferredRegionUS(): Boolean = isRegionCodeAmerican(languageManager.getCurrentRegion().regionCode)

        fun isPreferredRegionCanada(): Boolean = isRegionCodeCanadian(languageManager.getCurrentRegion().regionCode)

        fun isPreferredRegionHawaii(): Boolean = isRegionCodeHawaii(languageManager.getCurrentRegion().regionCode)

        fun isPreferredRegionMexico(): Boolean = isRegionCodeMexico(languageManager.getCurrentRegion().regionCode)

        fun getSelectedVehicleLocale(): Locale =
            if (isSelectedVehicleCanadian()) {
                Locale.CANADA
            } else {
                Locale.US
            }

        fun getRegionalNumberFormat(): NumberFormat {
            val locale =
                when {
                    isSelectedVehicleAmerican() -> Locale.US
                    isPreferredRegionCanada() -> Locale.CANADA
                    else -> Locale.US
                }
            return NumberFormat.getNumberInstance(locale)
        }

        fun getRegionBasedCountryCode(): String {
            return if (isPreferredRegionMexico()) MX_COUNTRY_CODE else NA_COUNTRY_CODE
        }
    }
