package com.toyota.oneapp.language

import com.toyota.oneapp.util.AppLanguageUtils
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import java.text.NumberFormat

@InstallIn(SingletonComponent::class)
@Module
class AppLanguageModule {
    @CurrentCurrencyFormat
    @Provides
    fun providesCurrencyFormat(): NumberFormat = NumberFormat.getCurrencyInstance(AppLanguageUtils.getCurrentLocale())
}
