package com.toyota.oneapp.util.language

import android.content.Context
import com.toyota.oneapp.R
import java.util.*

enum class SupportedLanguages(
    val acronym: String,
    val nameResId: Int,
) {
    ENGLISH("EN", R.string.Language_English),
    SPANISH("ES", R.string.Language_Spanish),
    FRENCH("FR", R.string.Language_French),
    ;

    fun getName(context: Context) = context.getString(nameResId)

    fun doesMatchAcronym(otherAcronym: String) = (otherAcronym.uppercase(Locale.getDefault()) == acronym)

    companion object Selector {
        fun getLanguageFromAcronym(
            acronym: String?,
            isExactMatch: Boolean = true,
        ): SupportedLanguages {
            if (acronym == null) {
                return ENGLISH
            }

            val upperCaseAcronym = acronym.uppercase(Locale.getDefault())

            values().forEach {
                if ((isExactMatch && upperCaseAcronym == it.acronym) ||
                    (!isExactMatch && upperCaseAcronym.contains(it.acronym))
                ) {
                    return it
                }
            }

            return ENGLISH
        }

        @JvmStatic
        fun getLanguageName(
            acronym: String?,
            context: Context,
        ) = getLanguageFromAcronym(
            acronym,
            false,
        ).getName(context)

        @JvmStatic
        fun getLanguageNameFromAcronym(
            acronym: String?,
            context: Context,
        ) = getLanguageFromAcronym(
            acronym,
            true,
        ).getName(context)

        @JvmStatic
        fun getLanguageCode(acronym: String?) = getLanguageFromAcronym(acronym, false).acronym
    } // Selector companion object
} // SupportedLanguages enum class
