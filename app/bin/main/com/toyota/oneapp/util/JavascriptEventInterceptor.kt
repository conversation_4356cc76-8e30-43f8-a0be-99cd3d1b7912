package com.toyota.oneapp.util

import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import com.toyota.oneapp.customviews.FlexibleWebView
import org.apache.commons.lang3.StringUtils
import toyotaone.commonlib.log.LogTool
import java.util.UUID

class AndroidInterface(
    private val eventHandler: (rawEvent: String) -> Unit,
) {
    @JavascriptInterface
    fun processJavascriptEventOnAndroid(rawEvent: String) {
        LogTool.d("PaymentPage", "rawEvent: $rawEvent")
        eventHandler(rawEvent)
    }

    @JavascriptInterface
    fun postMessage(message: String) {
        LogTool.d("PaymentPage", "message ----$message")
    }
}

fun WebView.setJavascriptEventInterceptor(
    elementReference: String,
    eventName: String,
    eventScope: String = "",
    eventHandler: (event: String) -> Unit,
) {
    val objectName = "a${StringUtils.replaceAll(UUID.randomUUID().toString(), "-", "")}"
    addJavascriptInterface(AndroidInterface(eventHandler), objectName)
    webViewClient =
        object : WebViewClient() {
            override fun onPageFinished(
                view: WebView?,
                url: String?,
            ) {
                super.onPageFinished(view, url)
                evaluateJavascript(
                    """
                    (function() {
                      $elementReference.addEventListener("$eventName", function(options) {
                        $objectName.processJavascriptEventOnAndroid(JSON.stringify(options$eventScope));
                      });
                    })();
                    """.trimIndent(),
                ) {}
            }
        }
}

fun FlexibleWebView.setJavascriptEventInterceptor(
    elementReference: String,
    eventName: String,
    eventScope: String = "",
    eventHandler: (event: String) -> Unit,
) {
    LogTool.d("PaymentPage", "eventName: $eventName")
    LogTool.d("PaymentPage", "elementReference: $elementReference")

    val objectName = "a${StringUtils.replaceAll(
        UUID.randomUUID().toString(),
        "-",
        "",
    )}"

    LogTool.d("PaymentPage", "objectName: $objectName")
    addJavascriptInterface(AndroidInterface(eventHandler), objectName)

    client.onPageFinishedOverrides.add { _, _ ->
        evaluateJavascript(
            """
            (function() {
              $elementReference.addEventListener("$eventName", function(options) {
                $objectName.processJavascriptEventOnAndroid(JSON.stringify(options$eventScope));
              });
            })();
            """.trimIndent(),
        ) {}
    }
}
