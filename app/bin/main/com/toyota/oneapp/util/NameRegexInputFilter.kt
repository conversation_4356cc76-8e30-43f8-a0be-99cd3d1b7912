package com.toyota.oneapp.util

import android.text.InputFilter
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils

class NameRegexInputFilter(
    var name: String,
    val regexPattern: String,
) : InputFilter {
    override fun filter(
        source: CharSequence?,
        start: Int,
        end: Int,
        dest: Spanned?,
        dstart: Int,
        dend: Int,
    ): CharSequence? {
        var keepOriginal = true
        val sb = StringBuilder(end - start)
        val newName = name + source
        source?.let {
            for (i in start until end) {
                val c = source.get(i)
                if (newName.matches(regexPattern.toRegex())) {
                    sb.append(c)
                    name += sb
                } else {
                    keepOriginal = false
                }
            }

            if (keepOriginal) {
                return null
            } else {
                if (source is Spanned) {
                    val sp = SpannableString(sb)
                    TextUtils.copySpansFrom(source, start, end, null, sp, 0)
                    return sp
                } else {
                    return sb
                }
            }
        }
        return null
    }
}
