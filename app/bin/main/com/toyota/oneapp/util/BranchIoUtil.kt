package com.toyota.oneapp.util

import com.toyota.oneapp.BuildConfig

enum class BranchIoUtil(
    val prodBranchLink: String,
    val otherBranchLink: String,
) {
    TOYOTA("cttoyotaapp.com", "toyota.test-app.link"),
    LEXUS("ctlexusapp.com", "lexus.test-app.link"),
    SUBARU("subaru.app.link", "subaru.test-app.link"),
    ;

    companion object {
        @JvmStatic
        fun getBranchIoString() =
            (BuildConfig.FLAVOR_environment == "prod").run {
                when {
                    Brand.currentAppBrand().isLexus() -> if (this) LEXUS.prodBranchLink else LEXUS.otherBranchLink
                    Brand.currentAppBrand().isToyota() -> if (this) TOYOTA.prodBranchLink else TOYOTA.otherBranchLink
                    else -> if (this) SUBARU.prodBranchLink else SUBARU.otherBranchLink
                }
            }
    }
}
