package com.toyota.oneapp.util

import android.location.Geocoder
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class AddressUtil
    @Inject
    constructor(
        private val geocoder: Geocoder,
    ) {
        fun getAddress(
            latitude: String,
            longitude: String,
        ): String? =
            try {
                geocoder
                    .getFromLocation(latitude.toDouble(), longitude.toDouble(), 1)
                    ?.firstOrNull()
                    ?.getAddressLine(0)
            } catch (e: Exception) {
                LogTool.w(TAG, "Failed to get address from lat/long", e)
                null
            }

        fun getAddressForPre17(
            latitude: Double,
            longitude: Double,
        ): String? =
            try {
                geocoder
                    .getFromLocation(latitude, longitude, 1)
                    ?.firstOrNull()
                    ?.getAddressLine(0)
            } catch (e: Exception) {
                LogTool.w(TAG, "Failed to get address from lat/long", e)
                null
            }

        companion object {
            private const val TAG = "AddressUtil"
        }
    }
