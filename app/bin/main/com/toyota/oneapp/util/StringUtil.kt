/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.util

import android.annotation.SuppressLint
import java.util.Locale

object StringUtil {
    fun String.isAlphaNumeric(): Boolean =
        this.matches(".*[A-Za-z].*".toRegex()) &&
            this.matches(".*[0-9].*".toRegex()) &&
            this.matches("[A-Za-z0-9]*".toRegex())

    fun String.capitalizeFirstChar(): String =
        this.replaceFirstChar {
            if (it.isLowerCase()) {
                it.titlecase(Locale.getDefault())
            } else {
                it.toString()
            }
        }

    @SuppressLint("DefaultLocale")
    fun String.capitalizeWords(): String =
        split(" ").joinToString(" ") {
            it.lowercase().replaceFirstChar { char -> char.titlecase() }
        }

    fun maskVin(vin: String): String? =
        if (vin.isNotBlank()) {
            val blankUniCode = "\u2022"
            val numVisible = 4
            val length = vin.length

            blankUniCode.repeat(length - numVisible).plus(vin.takeLast(numVisible))
        } else {
            null
        }
}
