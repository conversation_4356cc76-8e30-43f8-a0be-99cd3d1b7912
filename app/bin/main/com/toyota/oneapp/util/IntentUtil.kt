package com.toyota.oneapp.util

import android.content.Context
import android.content.Intent
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.garage.subscriptionV2.SubscriptionV2Activity
import com.toyota.oneapp.ui.privacyportal.PrivacyPortalParentActivity

object IntentUtil {
    fun getEditConsentIntent(
        context: Context,
        vehicleInfo: VehicleInfo?,
        regionManager: RegionManager? = null,
    ): Intent =
        Intent(context, PrivacyPortalParentActivity::class.java).apply {
            putExtra(ToyotaConstants.VEHICLE, vehicleInfo)
        }

    fun getManageSubscriptionIntent(
        context: Context,
        vehicle: VehicleInfo,
        isAddVehicleFlow: Boolean = false,
        productLine: String? = null,
    ): Intent = SubscriptionV2Activity.getIntent(context, vehicle, isAddVehicleFlow, productLine)

    fun getOADashBoardIntent(
        context: Context,
        isDashboardRefresh: Boolean = false,
        newAddVehicle: Boolean = false,
    ): Intent {
        val intent =
            OADashboardActivity.createIntent(context).apply {
                putExtra(OADashboardActivity.REFRESH_DASHBOARD, isDashboardRefresh)
                putExtra(OADashboardActivity.NEW_VEHICLE_ADDED_KEY, newAddVehicle)
            }
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK
        return intent
    }
}
