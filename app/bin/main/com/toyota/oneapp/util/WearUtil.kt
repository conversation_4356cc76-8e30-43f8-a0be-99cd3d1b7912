package com.toyota.oneapp.util

import com.google.android.gms.wearable.PutDataMapRequest
import com.google.android.gms.wearable.Wearable
import com.google.gson.Gson
import com.toyota.oneapp.app.ToyotaApplication
import com.toyota.oneapp.model.vehicle.VehicleInfo
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.wear.WearAPIType
import toyotaone.commonlib.wear.WearConstants
import toyotaone.commonlib.wear.WearResponse

object WearUtil {
    private val mWearAPITypeMap = HashMap<String, WearAPIType>()

    fun isSendingStatus(selectedVehicle: VehicleInfo?): Boolean {
        if (selectedVehicle != null && selectedVehicle.vin != null) {
            val vin = selectedVehicle.vin
            return mWearAPITypeMap[vin] === WearAPIType.SENDING_REMOTE_ACTION
        }
        return false
    }

    fun sendToWear(
        destinationPath: String,
        key: String,
        data: String,
    ) {
        val putDataMapRequest =
            PutDataMapRequest.create(destinationPath + System.currentTimeMillis())
        putDataMapRequest.dataMap.putString(key, data)
        val request = putDataMapRequest.asPutDataRequest()
        request.setUrgent()
        Wearable
            .getDataClient(ToyotaApplication.getAppContext())
            .putDataItem(request)
            .addOnCompleteListener { task ->
                LogTool.d(
                    "WearUtil",
                    String.format("isSuccessful=%s sendToWear data: %s", task.isSuccessful, data),
                )
            }
    }

    private fun sendRemoteActionToWear(
        wearAPIType: WearAPIType,
        vin: String,
    ) {
        val gson = Gson()
        val wearResponse = WearResponse(apiType = wearAPIType, vin = vin)
        sendToWear(
            WearConstants.BUS_OUTBOUND_PATH,
            WearConstants.BUS_OUTBOUND_DATA_KEY,
            gson.toJson(wearResponse),
        )
    }

    fun setSendingStatus(
        isSending: Boolean,
        vin: String,
        selectedVehicle: VehicleInfo? = null,
    ) {
        val apiType =
            if (isSending) WearAPIType.SENDING_REMOTE_ACTION else WearAPIType.NO_ACTION
        mWearAPITypeMap[vin] = apiType
        sendRemoteActionToWear(apiType, vin)
    }
}
