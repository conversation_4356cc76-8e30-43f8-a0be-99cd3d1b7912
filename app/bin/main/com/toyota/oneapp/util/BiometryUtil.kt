package com.toyota.oneapp.util

import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricManager.Authenticators.BIOMETRIC_WEAK
import androidx.biometric.BiometricManager.Authenticators.DEVICE_CREDENTIAL
import androidx.biometric.BiometricPrompt
import androidx.fragment.app.FragmentActivity
import java.security.Signature
import java.util.concurrent.Executor

/**
 * Created by <PERSON> on 2019-08-21.
 */
object BiometryUtil {
    private val mainThreadExecutor: Executor
        get() = MainThreadExecutor()

    /**
     * Indicate whether this device can authenticate the user with biometrics
     *
     * @return true if there are any available biometric sensors and biometrics are enrolled on the device, if not, return false
     */
    fun canAuthenticate(context: Context): Boolean {
        val biometricManager = BiometricManager.from(context)
        return (biometricManager.canAuthenticate(BIOMETRIC_WEAK or DEVICE_CREDENTIAL) == BiometricManager.BIOMETRIC_SUCCESS)
    }

    fun showBiometryDialog(
        context: FragmentActivity,
        title: String?,
        description: String,
        cancelBtn: String,
        callback: BiometryCallback,
    ) {
        if (canAuthenticate(context.applicationContext)) {
            showBiometricPrompt(context, title, null, description, cancelBtn, callback)
        }
    }

    private fun showBiometricPrompt(
        context: FragmentActivity,
        title: String?,
        subTitle: String?,
        description: String,
        cancelBtn: String,
        callback: BiometryCallback,
    ) {
        val authenticationCallback = getAuthenticationCallback(callback)
        val mBiometricPrompt = BiometricPrompt(context, mainThreadExecutor, authenticationCallback)

        // Set prompt info
        val promptBuild =
            BiometricPrompt.PromptInfo
                .Builder()
                .setDescription(description)
                .setTitle(title ?: "")
                .setSubtitle(subTitle)
                .setAllowedAuthenticators(BIOMETRIC_WEAK or DEVICE_CREDENTIAL)
        val promptInfo = promptBuild.build()

        // Show biometric prompt
        mBiometricPrompt.cancelAuthentication()
        mBiometricPrompt.authenticate(promptInfo)
    }

    private class MainThreadExecutor : Executor {
        private val handler = Handler(Looper.getMainLooper())

        override fun execute(r: Runnable) {
            handler.post(r)
        }
    }

    private fun getAuthenticationCallback(callback: BiometryCallback?): BiometricPrompt.AuthenticationCallback {
        // Callback for biometric authentication result
        return object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationError(
                errorCode: Int,
                errString: CharSequence,
            ) {
                super.onAuthenticationError(errorCode, errString)
                when (errorCode) {
                    BiometricPrompt.ERROR_CANCELED -> callback?.onCancelled()
                    BiometricPrompt.ERROR_LOCKOUT -> callback?.onLockedFailure()
                    BiometricPrompt.ERROR_LOCKOUT_PERMANENT -> callback?.onLockedFailure()
                    BiometricPrompt.ERROR_USER_CANCELED -> callback?.onCancelled()
                    else -> callback?.onError(errorCode)
                }
            }

            override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                callback?.onSuccess(result.cryptoObject?.signature)
            }

            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                callback?.onFailure()
            }
        }
    }

    interface BiometryCallback {
        fun onSuccess(signature: Signature?)

        fun onFailure()

        fun onLockedFailure()

        fun onCancelled()

        fun onError(errorCode: Int)
    }
}
