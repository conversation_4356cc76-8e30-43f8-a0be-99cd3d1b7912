package com.toyota.oneapp.util

import com.toyota.oneapp.R

enum class RegistrationErrorType(
    val titleId: Int,
    val detailsId: Int,
    val firstButtonId: Int,
    val secondButtonId: Int,
) {
    QR_SCAN(
        R.string.registration_error,
        R.string.registration_error_detailed,
        R.string.AddVehicle_qr_scan_code,
        R.string.enter_manual_code_2,
    ),

    REMOTE_ACTIVATION_FAILURE(
        R.string.remote_auth_error,
        R.string.registration_error_detailed,
        R.string.AddVehicle_qr_scan_code,
        R.string.enter_manual_code_2,
    ),

    VEHICLE_LINK_FAILURE(
        R.string.linked_vehicle_error,
        R.string.linked_vehicle_error_detailed,
        R.string.AddVehicle_qr_scan_code,
        R.string.Common_Back_to_Dashboard,
    ),

    INVALID_USER_CODE(
        R.string.AddVehicle_code_not_recognized,
        R.string.linked_vehicle_error_detailed,
        R.string.AddVehicle_qr_scan_code,
        R.string.enter_manual_code_title,
    ),

    USER_LOGIN_MISMATCH(
        R.string.account_not_match,
        R.string.credential_not_match_message,
        R.string.aa_common_retry_button,
        R.string.Common_cancel,
    ),
    ;

    companion object {
        const val ERROR_TYPE = "error_type"
    }
}
