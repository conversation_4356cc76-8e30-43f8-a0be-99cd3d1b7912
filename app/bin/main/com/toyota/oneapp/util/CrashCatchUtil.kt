package com.toyota.oneapp.util

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.toyota.oneapp.app.ToyotaApplication

/**
 * Created by <PERSON> on 2020/3/3.
 */
object CrashCatchUtil {
    const val MAP_CRASH_ACTION = "com.toyota.oneapp.util.MAP_CRASH_ACTION"

    @JvmStatic
    fun catchGoogleMapsCrash() {
        val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler(
            object : Thread.UncaughtExceptionHandler {
                override fun uncaughtException(
                    thread: Thread,
                    ex: Throwable,
                ) {
                    if (ex is NullPointerException && thread.name.startsWith("GLThread")) {
                        for (stackTraceElement in ex.getStackTrace()) {
                            if (stackTraceElement.className.contains("maps")) {
                                val intent = Intent(MAP_CRASH_ACTION)
                                intent.putExtra("MapCrash", true)
                                LocalBroadcastManager.getInstance(ToyotaApplication.getAppContext()).sendBroadcast(
                                    intent,
                                )
                                return
                            }
                        }
                    }
                    defaultHandler?.uncaughtException(thread, ex)
                }
            },
        )
    }
}
