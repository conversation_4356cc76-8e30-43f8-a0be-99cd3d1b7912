package com.toyota.oneapp.util

import java.text.DecimalFormat
import java.util.*

object CurrencyUtils {
    private val currencyFormat = DecimalFormat("0.00")

    fun toCurrencyString(totalPrice: Double): String? = currencyFormat.format(totalPrice)

    /**
     * Get currency symbol for given currencyCode.
     * For Canada - Use currency code (CAD)
     * For other region - use currency symbol (like $)
     */
    fun getCurrencySymbol(currencyCode: String): String {
        val currency = Currency.getInstance(currencyCode)
        return if (ToyotaConstants.CANADA_CURRENCY_CODE == currencyCode) {
            currency.currencyCode
        } else {
            currency.symbol
        }
    }
}
