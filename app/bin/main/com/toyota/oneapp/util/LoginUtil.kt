package com.toyota.oneapp.util

import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.android.material.snackbar.BaseTransientBottomBar
import com.google.android.material.snackbar.Snackbar
import com.toyota.oneapp.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by <PERSON> on 3/12/21.
 */
object LoginUtil {
    @JvmStatic
    fun delayWith(
        lifecycleOwner: LifecycleOwner,
        delayTime: Long = 5000L,
        onExecute: () -> Unit,
    ) {
        lifecycleOwner.lifecycleScope.launch {
            delay(delayTime)
            onExecute()
        }
    }

    @JvmStatic
    fun showLowNetworkBandwidth(
        view: View,
        message: String,
        drawable: Int,
    ): Snackbar {
        val snackBarView =
            Snackbar.make(
                view,
                message,
                Snackbar.LENGTH_INDEFINITE,
            )
        snackBarView.apply {
            animationMode = BaseTransientBottomBar.ANIMATION_MODE_FADE
        }
        val layout = snackBarView.view as Snackbar.SnackbarLayout
        layout.apply {
            findViewById<TextView>(com.google.android.material.R.id.snackbar_text).apply {
                setCompoundDrawablesWithIntrinsicBounds(drawable, 0, 0, 0)
                compoundDrawablePadding = resources.getDimensionPixelOffset(R.dimen.lineSpace_big)
            }
        }
        val params = layout.layoutParams as FrameLayout.LayoutParams
        params.gravity = Gravity.TOP
        layout.layoutParams = params
        snackBarView.show()
        return snackBarView
    }
}
