package com.toyota.oneapp.util

import com.toyota.oneapp.BuildConfig

interface ToyotaConstants {
    companion object {
        // Simple Global Values
        const val ZERO_INT = 0
        const val ZERO_FLOAT = 0.0F
        const val ZERO_DOUBLE = 0.0

        // Date Format
        const val STANDARD_UTC_DATE_FORMAT: String = "yyyy-MM-dd'T'HH:mm:ss'Z'"
        const val URL_REGEX: String =
            "https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&\\/\\/\\=]*)"

        // Time Constants
        const val PM_INDICATOR: Int = 1
        const val HOURS_TO_ADD_FOR_PM: Int = 12

        // Odometer Constants
        const val ODOMETER_1500: String = "1500"
        const val ODOMETER_123: String = "123"
        const val ODOMETER_12345: String = "12345"
        const val ODOMETER_123456: String = "123456"
        const val ODOMETER_10000: String = "10000"
        const val ODOMETER_FORMATTED_1500: String = "1,500"
        const val ODOMETER_FORMATTED_123: String = "123"
        const val ODOMETER_FORMATTED_12345: String = "12,345"
        const val ODOMETER_FORMATTED_123456: String = "123,456"
        const val ODOMETER_FORMATTED_10000: String = "10,000"

        const val REMOTE_SILENCE_ACTION: String = "com.toyota.oneapp.REMOTE_SILENCE_ACTION"
        const val GUEST_DRIVER_SILENCE_ACTION: String =
            "com.toyota.oneapp.GUEST_DRIVER_SILENCE_ACTION"
        const val VEHICLE_ASSOCIATION_ACTION: String =
            "com.toyota.oneapp.VEHICLE_ASSOCIATION_ACTION"
        const val VEHICLE_DE_ASSOCIATION_ACTION: String =
            "com.toyota.oneapp.VEHICLE_DE_ASSOCIATION_ACTION"
        const val REMOTE_USER_ACTIVATED: String = "remote_user_activated"
        const val ACTION_NOTIFICATION_RECEIVED: String =
            "com.toyota.oneapp.ACTION_NOTIFICATION_RECEIVED"
        const val FLUTTER_FCM_ACTION: String = "com.toyota.oneapp.flutter.FLUTTER_FCM_ACTION"
        const val FLUTTER_FCM_TOKEN: String = "com.toyota.oneapp.flutter.FLUTTER_FCM_TOKEN"

        const val REMOTE_SILENT_DK_SDK_PUSH: String = "ACTION_REMOTE_SILENT_DK_SDK_PUSH_CHANGED"
        const val REMOTE_SILENT_PUSH_ACTION: String = "ACTION_REMOTE_SILENT_PUSH_CHANGED"
        const val NET_CAPABILITY_VALIDATED: String = "VALIDATED"

        const val DK_SILENT_SDK_PUSH_JSON = "dk_silent_sdk_push_json"

        // Activity Request Codes
        const val TOKEN_EXPIRE_TIME_BUFF_TIME: Int = 1000 * 60 * 3
        const val BATTERY_BARS: Int = 12
        const val IDP_AUTH_CODE_SCHEMA: String = "driverslogin"

        // Branch DeepLink
        const val IS_DEEP_LINK_ROUTING: String = "is_deep_link_routing"
        const val BRANCH_REFERRING_PARAM: String = "branch_referring_param"
        const val BRANCH_CLICKED_BRANCH_LINK_KEY: String = "+clicked_branch_link"
        const val BRANCH_DEEPLINK_PATH_KEY: String = "\$deeplink_path"
        const val BRANCH_USER_CODE_KEY: String = "\$user_code"
        const val PASSWORD_RESET: String = "password_reset"
        const val RESET_PASSWORD_KEY: String = "isResetPassword"
        const val MANAGE_EXISTING_ACCOUNT_DEEPLINK_PATH: String = "existing_account"

        const val VEHICLE_PROFILE: String = "vehicleProfile"
        const val SELECTED_VEHICLE: String = "selectedVehicle"

        const val VEHICLE: String = "vehicle"
        const val PAID_FLOW: String = "paidFlow"

        const val REGISTRATION_REQUEST_ID: String = "registrationRequestId"

        const val PURCHASE_LIST: String = "purchaseList"
        const val TRIAL_LIST: String = "trail_list"
        const val ACCESS_TOKEN: String = "accessToken"
        const val IS_AZURE: String = "isAzure"
        const val COUNTRY: String = "country"
        const val SUBSCRIPTION_PAYLOAD: String = "subscriptionPayload"
        const val CITY: String = "city"
        const val ZIP_CODE: String = "zipcode"
        const val STREET_ADDRESS: String = "streetAddress"
        const val STATE: String = "state"
        const val VIN: String = "vin"
        const val SUGGESTED_ADDRESS_OBJ: String = "suggestedAddressObj"
        const val ENTERED_ADDRESS_OBJ: String = "enteredAddressObj"
        const val PAYMENT_METHOD_AVAILABLE: String = "paymentMethodsAvailable"
        const val PAYMENT_TOKEN: String = "paymentToken"
        const val SERVICES: String = "Services"
        const val PAYMENT_TYPE: String = "payment_type"
        const val TIMELINE_LIST: String = "time_line_list"
        const val ODOMETER_VALUE: String = "odometer"
        const val ODOMETER_VALUE_AVAILABLE: String = "checkvalue"
        const val VEHICLE_MODEL: String = "vehicleModel"
        const val VEHICLE_SUBSCRIPTION_ALERTS: String = "vehicleAlerts"
        const val SELECTED_SUBSCRIPTION_NAMES: String = "selectedSubscriptionNames"
        const val TRIAL_SUBSCRIPTIONS: String = "trialSubscriptions"
        const val PAID_SUBSCRIPTIONS: String = "paidSubscriptions"
        const val SUBSCRIPTION_GET_PAYLOAD: String = "SubscriptionGetPayload"
        const val IS_TOYOTA: String = "IsToyota"
        const val MTH: String = "MTH"
        const val KM: String = "km"
        const val MI: String = "mi"
        const val REGION_CA: String = "CA"
        const val REGION_US: String = "US"
        const val REGION_HI: String = "HI"
        const val REGION_PR: String = "PR"
        const val REGION_MX: String = "MX"
        const val CHECK_NETWORK_ACTION: String = "NetworkStateReceiver.CHECK_NETWORK_ACTION"
        const val IS_FROM_API: String = "FromAPI"
        const val REGION: String = "Region"
        const val LATEST_SOFTWARE_VERSION: String = "getLatestVersion"
        const val NOTIFICATION_STATUS: String = "notification_status"
        const val IS_DASHBOARD_OR_MY_GARAGE: String = "isDashboardOrMyGarage"
        const val DATA_CONSENT_UBI_TITLE: String = "header"
        const val UBI_LEARN_MORE: String = "body"
        const val PRE17CY: String = "PRE17CY"
        const val CONSENT_ACCEPTED: String = "Accepted"
        const val CONSENT_DECLINED: String = "Declined"
        const val TRUE: String = "True"
        const val FALSE: String = "False"
        const val TRIAL: String = "Trial"
        const val PAID: String = "PAID"
        const val FREE: String = "Free"
        const val DATA_CONSENT_KEY: String = "data_consent"
        const val VIN_CAPS: String = "VIN"
        const val BRAND: String = "Brand"
        const val CONSENTS: String = "CONSENTS"
        const val HOUR: String = "Hour"
        const val HOURS: String = "Hours"
        const val EMPTY_STRING: String = ""

        const val CURRENT: String = "current"
        const val REMOTE_COMMAND: String = "RemoteCommand"
        const val ACCOUNT_INFO: String = "AccountInfo"
        const val NAVIGATE_TO_PERSONAL_DETAIL: String = "navigateToPersonalDetail"
        const val IMAGE_URI: String = "ImageUri"

        // feedback thumbs-up context strings
        const val ALL_DATA_CONSENT_ITEMS: String = "all_data_consent_items"
        const val HOME: String = "HOME"
        const val TYPE: String = "type"

        const val DK_NOTICE_PREFERENCE: String = "digital_key_notice_dismissed_"
        const val VEHICLE_REGISTRATION_PUSH_TIMEOUT_MS: Long = 30000
        const val SOFT_KEYBOARD_OPEN: Long = 300
        const val TOYOTA: String = "Toyota"
        const val LEXUS: String = "Lexus"
        const val SUBARU: String = "Subaru"
        const val DEEP_LINK_ROUTING: String = "deep_link_routing"
        const val DASHBOARD_ROUTE: String = "dashboard_route"
        const val CANADA_CURRENCY_CODE: String = "CAD"
        const val US_CURRENCY_CODE: String = "USD"
        const val DECLINE_CONSENT_SUCCESS_MESSAGE: String = "decline_consent_success_message"
        const val PP_SUBSCRIPTION_PRODUCT: String = "pp_susbscription_product"
        const val APPLE: String = "apple"
        const val AMAZON: String = "amazon"
        const val NONE: String = "none"
        const val IS_ON_BOARDING_FLOW: String = "isOnBoardingFlow"

        const val PROCESS_ERROR: String = "PROCESS_ERROR"
        const val UTC_TIMESTAMP_FORMAT: String = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        const val SECONDS_IN_YEAR: Int = ********
        const val SECONDS_IN_MONTH: Int = 2628000
        const val SECONDS_IN_DAY: Int = 86400
        const val SECONDS_IN_HOUR: Int = 3600
        const val SECONDS_IN_MINUTE: Int = 60
        const val CY17: String = "17CY"
        const val CY17PLUS: String = "17CYPLUS"
        const val MM21: String = "21MM"
        const val GR86: String = "GR86"

        // Error Code
        const val WAIVE_SUBSCRIPTION_ATT_ACTIVE_WIFI_ERROR_CODE: String = "ORCH-8014"
        const val ONE_VL_10001_MESSAGE: String = "ONE-VL-10001-errorMsg"
        const val APP_UPDATE_ERROR: String = "APP-UPDATE-ERROR"
        const val DELETE_ACCOUNT_ALREADY_SUBMITTED: String = "OCPR-0019"
        const val DELETE_ACCOUNT_HEADERS_MISSING: String = "OCPR-0001"

        // App Google Play URL
        val APP_GOOGLE_PLAY_URL: String =
            "https://play.google.com/store/apps/details?id=" + BuildConfig.APPLICATION_ID

        const val VERIZON_WIFI_CUSTOMER_SUPPORT: String = "1-800-922-0204"
        const val ATT_WIFI_CUSTOMER_SUPPORT: String = "1-866-595-1242"

        const val DASHBOARD_POI_DATA: String = "dashboard_poi_data"
        const val ADDRESS_REGEX: String = "[\"+\n" + "\u0000\"]"
        const val PIN_REGEX: String =
            "^(?!(.)\\1{5})(?!012345|123456|234567|345678|456789|543210|654321|765432|876543|987654)\\d{6}$"
        const val GRPC_CERT_TYPE: String = "X.509"
        const val GRPC_CERT_ALIAS: String = "ca"
        const val KEY_MANAGER_ALGO: String = "X509"
        const val SSL_PROTOCOL: String = "TLS"

        // Bluetooth - User Profile
        const val BLE_NOTIFICATION_CHANNEL_ID_SUFFIX: String = "ble"
        const val BLE_NOTIFICATION_FOREGROUND_ID: Int = 456
        const val BLE_SCAN_PERIOD: Int = 1100
        const val BLE_BETWEEN_SCAN_PERIOD: Int = 0
        const val BLUETOOTH_TAG: String = "OneApp_Bluetooth"
        const val BLE_ADVERTISING_UUID_TOYOTA: String = "60323118-d9f1-4bdf-af72-ed68af1edcd2"
        const val BLE_ADVERTISING_UUID_LEXUS: String = "16255adf-8b4d-4e2f-80a0-e99193217028"
        const val BLE_ADVERTISING_UUID_SUBARU: String = "7b012ada-8d77-11eb-8dcd-0242ac130003"
        const val UP_GATT_SERVICE_UUID_TOYOTA: String = "60323118-d9f1-4bdf-af72-ed68af1edcd2"
        const val UP_GATT_SERVICE_UUID_LEXUS: String = "16255adf-8b4d-4e2f-80a0-e99193217028"
        const val UP_GATT_SERVICE_UUID_SUBARU: String = "7b012ada-8d77-11eb-8dcd-0242ac130003"
        const val UP_TX_CHARACTERISTIC_UUID: String = "45fff39a-4c31-11e9-8646-d663bd873d93"
        const val UP_RX_CHARACTERISTIC_UUID: String = "7dbf6f28-4f2d-11e9-8647-d663bd873d93"
        const val UP_COMMAND_PACKET_1: String = "0x1A10"
        const val UP_COMMAND_PACKET_2: String = "0x1A11"
        const val UP_COMMAND_ACK: String = "0x1A01"
        const val AD_COMMAND_REQUEST: String = "0x2010"
        const val CLIENT_CHARACTERISTIC_CONFIGURATION_UUID: String =
            "********-0000-1000-8000-00805f9b34fb"

        const val PHONE_VERIFICATION_CONSENT_NO: String = "N"
        const val PHONE_VERIFICATION_CONSENT_NC: String = "NC"
        const val PHONE_VERIFICATION_CONSENT_YES: String = "Y"
        const val EDIT_TYPE: String = "type"
        const val EDIT_VALUE: String = "value"
        const val EDITING_ACCOUNTINFO: String = "AccountInfo"
        const val PHONE_NUMBER: String = "PhNumber"
        const val OFFLINE_VEHICLE_IMG_FILE_NAME: String = "offline_vehicle_img"
        const val EMPTY_SPACE = " "

        /**
         * m - matching byte sequence for this beacon type to parse (exactly one required)
         * s - ServiceUuid for this beacon type to parse (optional, only for Gatt-based beacons)
         * i - identifier (at least one required, multiple allowed)
         * p - power calibration field (exactly one required)
         * d - data field (optional, multiple allowed)
         * x - extra layout.  Signifies that the layout is secondary to a primary layout with the same
         matching byte sequence (or ServiceUuid).  Extra layouts do not require power or
         identifier fields and create Beacon objects without identifiers.
         * l suffix indicates little endian
         * */
        const val BLE_TOYOTA_BEACON_LAYOUT: String = "m:2-3=c0ac,i:4-19l,p:20-20"

        // Manufacturer identifiers required by the manufacturer data BLE advertisement.
        const val MANF_ID_PANA: Int =
            0x003A // Manufacturer identifier number assigned by Bluetooth SIG to PANASONIC
        const val MANF_ID_DTEN: Int =
            0x010D // Manufacturer identifier number assigned by Bluetooth SIG to DENSO TEN
        val BEACON_MATCHING_LE: ByteArray = byteArrayOf(0xC0.toByte(), 0xAC.toByte())

        // Added to avoid endianness conversion operations consuming time during scan
        val BLE_ADVERTISING_BYTE_UUID_TOYOTA: ByteArray =
            byteArrayOf(
                0xD2.toByte(),
                0xDC.toByte(),
                0x1E.toByte(),
                0xAF.toByte(),
                0x68.toByte(),
                0xED.toByte(),
                0x72.toByte(),
                0xAF.toByte(),
                0xDF.toByte(),
                0x4B.toByte(),
                0xF1.toByte(),
                0xD9.toByte(),
                0x18.toByte(),
                0x31.toByte(),
                0x32.toByte(),
                0x60.toByte(),
            )
        val BLE_ADVERTISING_BYTE_UUID_LEXUS: ByteArray =
            byteArrayOf(
                0x28.toByte(),
                0x70.toByte(),
                0x21.toByte(),
                0x93.toByte(),
                0x91.toByte(),
                0xE9.toByte(),
                0xA0.toByte(),
                0x80.toByte(),
                0x2F.toByte(),
                0x4E.toByte(),
                0x4D.toByte(),
                0x8B.toByte(),
                0xDF.toByte(),
                0x5A.toByte(),
                0x25.toByte(),
                0x16.toByte(),
            )
        val BLE_ADVERTISING_BYTE_UUID_SUBARU: ByteArray =
            byteArrayOf(
                0x03.toByte(),
                0x00.toByte(),
                0x13.toByte(),
                0xAC.toByte(),
                0x42.toByte(),
                0x02.toByte(),
                0xCD.toByte(),
                0x8D.toByte(),
                0xEB.toByte(),
                0x11.toByte(),
                0x77.toByte(),
                0x8D.toByte(),
                0xDA.toByte(),
                0x2A.toByte(),
                0x01.toByte(),
                0x7B.toByte(),
            )

        // Mexico - ZipCode
        const val MX_ZIPCODE_MAX_LENGTH: Int = 5
        const val MX_ZIPCODE_LENGTH: Int = 6

        // Haptic Touch
        const val SHORTCUT_START_ENGINE_ACTION: String = "oneapp.shortcut.flutter.engine.start"
        const val SHORTCUT_DOOR_LOCK_ACTION: String = "oneapp.shortcut.flutter.door.lock"
        const val SHORTCUT_DOOR_UNLOCK_ACTION: String = "oneapp.shortcut.flutter.door.unlock"

        // 3g Network Terms
        const val TOYOTA_3G_TERMS: String =
            "https://support.toyota.com/s/article/3G-Wireless-Services-10599?language=en_US"
        const val LEXUS_3G_TERMS: String =
            "https://support.lexus.com/s/article/3G-Wireless-Services-10537"

        // Bundle
        const val BUNDLE: String = "BUNDLE"

        // SXM JANUS
        const val FLUTTER_DATA: String = "flutterData"
        const val KEY_ANNOUNCEMENT_LIST = "announcementsList"

        const val SOFTWARE_UPDATE_AVAILABLE_21MM: Int = 5

        // Appointment Service
        const val CUSTOM_SERVICE_NAME = "Custom Service"
        const val CUSTOM_SERVICE_DESCRIPTION = "TEXT_BOX"

        const val ADVISOR_RANDOM_ID = "999999-99999"

        const val REGION_AU: String = "AU"
        const val SUCCEEDED = "succeeded"
        const val TOYOTA_FLAVOR_BRAND = "toyotaOne"
        const val LEXUS_FLAVOR_BRAND = "lexusOne"
        const val SUBARU_FLAVOR_BRAND = "subaruOne"
    }
}
