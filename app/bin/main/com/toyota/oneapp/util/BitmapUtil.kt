package com.toyota.oneapp.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.Drawable

object BitmapUtil {
    fun <T : Drawable> createBitmapFromDrawable(
        drawableId: Int,
        context: Context,
    ): Bitmap {
        val drawable = context.getDrawable(drawableId) as T
        val bitmap =
            Bitmap.createBitmap(
                drawable.intrinsicWidth,
                drawable.intrinsicHeight,
                Bitmap.Config.ARGB_8888,
            )
        val canvas = Canvas(bitmap)

        drawable.setBounds(0, 0, canvas.width, canvas.height)
        drawable.draw(canvas)

        return bitmap
    }

    @JvmStatic
    fun saveBitmapToFile(
        context: Context,
        bitmap: Bitmap?,
        fileName: String,
    ) {
        bitmap?.let { img ->
            try {
                context.openFileOutput(fileName, Context.MODE_PRIVATE).use { stream ->
                    img.compress(Bitmap.CompressFormat.PNG, 100, stream)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
} // BitmapUtil object
