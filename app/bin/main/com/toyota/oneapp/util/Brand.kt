package com.toyota.oneapp.util

import com.toyota.oneapp.BuildConfig

enum class Brand(
    val brandName: String,
    val brandCode: String,
    val appBrand: String,
    val forgerockUrl: String,
) {
    TOYOTA("Toyota", "TOY", "T", BuildConfig.forgerock_url),
    LEXUS("Lexus", "LEX", "L", BuildConfig.forgerock_lexus_url),
    SUBARU("Subaru", "SUB", "S", BuildConfig.forgerock_subaru_url),
    ;

    val pinBaseUrl: String = String.format(BuildConfig.PIN_BASE_URL, BuildConfig.IDP_DOMAIN)

    fun isToyota() = this == TOYOTA

    fun isLexus() = this == LEXUS

    companion object {
        fun getBrand(brandCode: String?): Brand =
            if (TOYOTA.brandCode.equals(brandCode, ignoreCase = true) ||
                TOYOTA.appBrand.equals(brandCode, ignoreCase = true)
            ) {
                TOYOTA
            } else if (SUBARU.brandCode.equals(brandCode, ignoreCase = true) ||
                SUBARU.appBrand.equals(brandCode, ignoreCase = true)
            ) {
                SUBARU
            } else {
                LEXUS
            }

        fun withAppBrand(appBrand: String?): Brand =
            if (TOYOTA.appBrand.equals(appBrand, ignoreCase = true)) {
                TOYOTA
            } else if (SUBARU.appBrand.equals(
                    appBrand,
                    ignoreCase = true,
                )
            ) {
                SUBARU
            } else {
                LEXUS
            }

        fun currentAppBrand(): Brand = withAppBrand(BuildConfig.APP_BRAND)
    }
}
