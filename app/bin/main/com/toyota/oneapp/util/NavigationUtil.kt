/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.util

import android.content.Context
import android.os.Bundle
import androidx.navigation.NavController
import com.toyota.oneapp.BuildConfig.CHARGE_STATIONS_FLUTTER_DEPRECIATION
import com.toyota.oneapp.BuildConfig.DEALER_SERVICE_FLUTTER_DEPRECATION
import com.toyota.oneapp.BuildConfig.FIND_STATIONS_FLUTTER_DEPRECIATION
import com.toyota.oneapp.BuildConfig.WALLET_HOME_FLUTTER_DEPRECATION
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.DealerDetails
import com.toyota.oneapp.features.dealerservice.application.navigation.DealerServiceAppointmentRoute.PreferredDealerSearchEntry
import com.toyota.oneapp.features.dealerservice.application.navigation.createRoute
import com.toyota.oneapp.features.dealerservice.domain.model.Dealer
import com.toyota.oneapp.features.fuelwidget.presentation.navigateToFlutter
import com.toyota.oneapp.ui.flutter.Flutter_Preferred_Dealer
import com.toyota.oneapp.ui.flutter.Flutter_Select_Preferred_Dealer
import com.toyota.oneapp.ui.flutter.GO_TO_FIND_STATIONS
import com.toyota.oneapp.ui.flutter.GO_TO_WALLET_HOME

object NavigationUtil {
    fun navigateToStations(
        context: Context,
        navController: NavController,
        isEVPublicChargingEnabled: Boolean,
        isChargeStationsFlutterDepreciation: Boolean = CHARGE_STATIONS_FLUTTER_DEPRECIATION,
        isFindStationsFlutterDepreciation: Boolean = FIND_STATIONS_FLUTTER_DEPRECIATION,
    ) {
        if (isEVPublicChargingEnabled) {
            navigateToChargeStations(
                context = context,
                navController = navController,
                isChargeStationsFlutterDepreciation = isChargeStationsFlutterDepreciation,
            )
        } else {
            navigateToFindStations(
                context = context,
                navController = navController,
                isFindStationsFlutterDepreciation = isFindStationsFlutterDepreciation,
            )
        }
    }

    fun navigateToDealers(
        context: Context,
        navController: NavController,
        bundle: Bundle = Bundle(),
        preferredDealer: Dealer? = null,
        isDealerServiceFlutterDepreciation: Boolean = DEALER_SERVICE_FLUTTER_DEPRECATION,
    ) {
        if (isDealerServiceFlutterDepreciation) {
            navigateToPreferredDealers(
                dealerCode = preferredDealer?.dealerCode,
                navController = navController,
            )
        } else {
            navigateToFlutterPreferredDealers(
                context = context,
                preferredDealer = preferredDealer,
                bundle = bundle,
            )
        }
    }

    fun navigateToWalletHome(
        context: Context,
        navController: NavController,
        isWalletHomeFlutterDeprecation: Boolean = WALLET_HOME_FLUTTER_DEPRECATION,
    ) {
        if (isWalletHomeFlutterDeprecation) {
            navController.navigate(OAScreen.WalletHomeScreen.route)
        } else {
            GO_TO_WALLET_HOME.navigateToFlutter(context)
        }
    }

    private fun navigateToChargeStations(
        context: Context,
        navController: NavController,
        isChargeStationsFlutterDepreciation: Boolean,
    ) {
        if (isChargeStationsFlutterDepreciation) {
            navController.navigate(OAScreen.ChargeStation.route)
        } else {
            GO_TO_FIND_STATIONS.navigateToFlutter(context)
        }
    }

    private fun navigateToFindStations(
        context: Context,
        navController: NavController,
        isFindStationsFlutterDepreciation: Boolean,
    ) {
        if (isFindStationsFlutterDepreciation) {
            navController.navigate(OAScreen.FindStation.route)
        } else {
            GO_TO_FIND_STATIONS.navigateToFlutter(context)
        }
    }

    private fun navigateToFlutterPreferredDealers(
        context: Context,
        preferredDealer: Dealer?,
        bundle: Bundle,
    ) {
        if (preferredDealer != null) {
            navigateToFlutterPrerferedDealer(
                preferredDealer = preferredDealer,
                context = context,
                bundle = bundle,
            )
        } else {
            navigateToFlutterEmptyPreferredDealer(context, bundle)
        }
    }

    private fun navigateToPreferredDealers(
        dealerCode: String?,
        navController: NavController,
    ) {
        val route =
            if (dealerCode != null) {
                DealerDetails.createRoute(
                    dealerCode = dealerCode,
                    isPreferredDealer = true,
                )
            } else {
                PreferredDealerSearchEntry.route
            }
        navController.navigate(route)
    }

    private fun navigateToFlutterPrerferedDealer(
        preferredDealer: Dealer,
        context: Context,
        bundle: Bundle,
    ) {
        val data = NavigationDataUtil.createPreferredDealerData(dealer = preferredDealer)
        Flutter_Preferred_Dealer.navigateToFlutterDataPayload(context, data, bundle)
    }

    private fun navigateToFlutterEmptyPreferredDealer(
        context: Context,
        bundle: Bundle,
    ) {
        val data = NavigationDataUtil.createEmptyPreferredDealerData()
        Flutter_Select_Preferred_Dealer.navigateToFlutterDataPayload(context, data, bundle)
    }
}

fun String.navigateToFlutterDataPayload(
    context: Context,
    data: HashMap<String, Any?>,
    bundle: Bundle = Bundle(),
) {
    bundle.putSerializable(ToyotaConstants.FLUTTER_DATA, data)
    navigateToFlutter(context, bundle)
}
