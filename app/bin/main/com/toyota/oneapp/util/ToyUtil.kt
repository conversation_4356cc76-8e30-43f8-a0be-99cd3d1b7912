package com.toyota.oneapp.util

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.Uri
import android.text.Spannable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.browser.customtabs.CustomTabsIntent
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.constants.AccountTermsAndPrivacyUrls
import com.toyota.oneapp.constants.LexusAccountUrls
import com.toyota.oneapp.constants.LexusVehicleAccessUrls
import com.toyota.oneapp.constants.LexusVehicleUrls
import com.toyota.oneapp.constants.SubaruAccountURLS
import com.toyota.oneapp.constants.SubaruVehicleAccessUrls
import com.toyota.oneapp.constants.ToyAccountUrls
import com.toyota.oneapp.constants.ToyVehicleUrls
import com.toyota.oneapp.constants.ToyotaVehicleAccessUrls
import com.toyota.oneapp.constants.VehicleAccessUrls
import com.toyota.oneapp.constants.VehicleTermsAndPrivacyUrls
import com.toyota.oneapp.model.vehicle.CapabilityItem
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.ui.web.InternalWebActivity
import toyotaone.commonlib.customtextview.PinEntryEditText
import toyotaone.commonlib.log.LogTool
import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Date
import java.util.GregorianCalendar
import java.util.Locale
import java.util.TimeZone
import java.util.regex.Pattern

object ToyUtil {
    @JvmField
    var dateFormatYYYY: SimpleDateFormat =
        SimpleDateFormat(
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
            AppLanguageUtils.getCurrentLocale(),
        ) // 2018-03-12T21:44:34.117+0000
    var dateFormatYYYYTNoMillis: SimpleDateFormat =
        SimpleDateFormat(
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
            AppLanguageUtils.getCurrentLocale(),
        )

    @JvmField
    var dateFormatMMMMDDYYYY: SimpleDateFormat =
        SimpleDateFormat("MMMM-dd-yyyy", AppLanguageUtils.getCurrentLocale()) // Oct-17-2018

    private const val URL = "URL"
    private const val TITLE = "TITLE"
    private const val IS_ALL_SETTINGS_REQUIRED = "IS_ALL_SETTINGS_REQUIRED"
    private const val TAG = "ToyUtil"

    @JvmStatic
    fun hasNetwork(context: Context): Boolean {
        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = cm.activeNetworkInfo
        val connected = activeNetwork != null && activeNetwork.isConnected
        var hasNetwork = false
        if (connected) {
            val networkCapabilities = (
                NetworkRequest
                    .Builder()
                    .addCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
                    .build()
            )
            hasNetwork =
                networkCapabilities.toString().contains(ToyotaConstants.NET_CAPABILITY_VALIDATED)
        }
        return connected && hasNetwork
    }

    fun isBlank(str: String?): Boolean = str.isNullOrBlank()

    @JvmStatic
    fun isNotBlank(str: String?): Boolean = !isBlank(str)

    @JvmStatic
    fun getDateNow(dtFormat: SimpleDateFormat?): String = getFormattedDate(dtFormat, Date())

    fun convertFromUTCTime(
        timeStr: String?,
        pattern: String?,
    ): String {
        try {
            val dateFormat = SimpleDateFormat(pattern, AppLanguageUtils.getCurrentLocale())
            dateFormat.timeZone = TimeZone.getTimeZone("UTC")
            val date = dateFormat.parse(timeStr)
            val cal = GregorianCalendar()
            cal.timeInMillis = date.time
            return SimpleDateFormat(pattern, AppLanguageUtils.getCurrentLocale()).format(cal.time)
        } catch (e: ParseException) {
            if (e.message != null) {
                LogTool.e(TAG, e.message)
            }
        } catch (e: NullPointerException) {
            if (e.message != null) {
                LogTool.e(TAG, e.message)
            }
        }
        return timeStr ?: ""
    }

    fun convertFromUTCTime(
        timeStr: String,
        parsePattern: String?,
        finalPattern: String?,
    ): String {
        try {
            val dateFormat = SimpleDateFormat(parsePattern, AppLanguageUtils.getCurrentLocale())
            dateFormat.timeZone = TimeZone.getTimeZone("UTC")
            val date = dateFormat.parse(timeStr)
            val cal = GregorianCalendar()
            cal.timeInMillis = date.time
            val simpleDateFormat =
                SimpleDateFormat(finalPattern, AppLanguageUtils.getCurrentLocale())
            return simpleDateFormat.format(cal.time)
        } catch (e: ParseException) {
            if (e.message != null) {
                LogTool.e(TAG, e.message)
            }
        } catch (e: NullPointerException) {
            if (e.message != null) {
                LogTool.e(TAG, e.message)
            }
        }
        return timeStr
    }

    // NOTE: New users who want to use this should be moving to DateUtil which can be Injected
    fun convertFromUTCTime(
        timeStr: String,
        parsePattern: String,
        finalPattern: String?,
        locale: Locale?,
    ): String {
        try {
            val dateFormat = SimpleDateFormat(parsePattern, locale)
            val date = dateFormat.parse(timeStr)
            val cal = GregorianCalendar()
            cal.timeInMillis = date.time
            val simpleDateFormat = SimpleDateFormat(finalPattern, locale)
            return simpleDateFormat.format(cal.time)
        } catch (e: ParseException) {
            if (e.message != null) {
                LogTool.e(TAG, e.message)
            }
        } catch (e: NullPointerException) {
            if (e.message != null) {
                LogTool.e(TAG, e.message)
            }
        }
        return timeStr
    }

    @JvmStatic
    fun getFormattedDate(
        dtFormat: SimpleDateFormat?,
        date: Date?,
    ): String {
        if (null != dtFormat && null != date) {
            return dtFormat.format(date)
        }
        return ""
    }

    @JvmStatic
    fun getCalendarFromString(
        dtFormat: SimpleDateFormat?,
        date: String?,
    ): Calendar {
        val cal = Calendar.getInstance()
        if (dtFormat != null && !date.isNullOrEmpty()) {
            try {
                cal.time = dtFormat.parse(date) ?: cal.time
            } catch (e: ParseException) {
                if (e.message != null) {
                    LogTool.e(TAG, e.message)
                }
            } catch (e: NullPointerException) {
                if (e.message != null) {
                    LogTool.e(TAG, e.message)
                }
            }
        }

        return cal
    }

    fun isNumeric(str: String?): Boolean {
        if (TextUtils.isEmpty(str)) {
            return false
        }
        val pattern = Pattern.compile("[0-9]*")
        val isNum = pattern.matcher(str)
        return isNum.matches()
    }

    @JvmStatic
    fun nickNameValidation(str: String?): Boolean {
        if (TextUtils.isEmpty(str)) {
            return true
        }
        val pattern = Pattern.compile("^[a-zA-Z0-9 ]*$")
        val isNickName = pattern.matcher(str)
        return isNickName.matches()
    }

    fun isPinStrong(str: String) = Pattern.compile(ToyotaConstants.PIN_REGEX).matcher(str).find()

    fun matchZipCode(
        str: String,
        countryCode: String?,
    ): Boolean {
        var str = str
        str = str.uppercase(Locale.getDefault())

        val patternString =
            if (ToyotaConstants.REGION_CA.equals(
                    countryCode,
                    ignoreCase = true,
                )
            ) {
                "^(?!.*[DFIOQU])[A-VXY][0-9][A-Z] ?[0-9][A-Z][0-9]$"
            } else {
                // canada pattern
                "^\\d{5}$|^\\d{5}-\\d{4}$" // US pattern
            }

        return Pattern.compile(patternString).matcher(str).matches()
    }

    fun setTransparentStatusBar(context: Activity) {
        val window = context.window
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.statusBarColor = Color.TRANSPARENT
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
    }

    fun openKeyboard(
        activity: Activity,
        entryEditText: PinEntryEditText,
    ) {
        entryEditText.requestFocus()
        entryEditText.postDelayed(
            Runnable {
                val keyboard =
                    activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                keyboard.showSoftInput(entryEditText, 0)
            },
            ToyotaConstants.SOFT_KEYBOARD_OPEN,
        )
    }

    fun toUpperFirstChar(string: String): String =
        string.replaceFirstChar {
            if (it.isLowerCase()) it.titlecase(Locale.ROOT) else it.toString()
        }

    fun hasProtocol(url: String?): Boolean {
        if (url == null) {
            return false
        }

        return (url.startsWith("http://") || url.startsWith("https://"))
    }

    @JvmStatic
    fun getMMMddyyyyTime(timeStr: String?): String {
        val cal = getCalendarFromString(dateFormatYYYYTNoMillis, timeStr)
        val dateFormat = SimpleDateFormat("MMM dd, yyyy", AppLanguageUtils.getCurrentLocale())
        return dateFormat.format(cal.time)
    }

    @JvmStatic
    fun phoneCall(
        context: Context,
        number: String?,
    ) {
        if (number != null) {
            try {
                val phoneNumber = number.trim { it <= ' ' }.replace(" ", "")

                // DO NOT use ACTION_CALL. It requires permissions which we would rather not have in
                // the app since it poses a potential security risk to our users. ACTION_DIAL is simpler
                // and cleaner.
                val callIntent =
                    Intent(
                        Intent.ACTION_DIAL,
                        Uri.parse(
                            "tel:$phoneNumber",
                        ),
                    )
                callIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(callIntent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun phoneNumberFormat(number: String?): String {
        if (number != null) {
            var newNumber = number
            val number1 = number.replace(" ", "")
            if (number1.length == 10 && isNumeric(number1)) {
                val part1 = number.substring(0, 3)
                val part2 = number.substring(3, 6)
                newNumber = "(" + part1 + ") " + part2 + "-" + number.substring(6)
            }
            return newNumber
        }
        return ""
    }

    @JvmStatic
    fun isCY17(generationCode: String?): Boolean =
        ToyotaConstants.CY17.equals(
            generationCode,
            ignoreCase = true,
        ) ||
            ToyotaConstants.PRE17CY.equals(generationCode, ignoreCase = true)

    fun getAccountPrivacy(brand: String): String {
        val constantURLS: AccountTermsAndPrivacyUrls =
            when (brand) {
                Brand.TOYOTA.appBrand -> ToyAccountUrls
                Brand.LEXUS.appBrand -> LexusAccountUrls
                Brand.SUBARU.appBrand -> SubaruAccountURLS
                else -> ToyAccountUrls
            }
        return when {
            AppLanguageUtils.isTDPRRegion() -> {
                if (AppLanguageUtils.isSpanish()) {
                    constantURLS.prEsPrivacy
                } else {
                    constantURLS.prEnPrivacy
                }
            }
            AppLanguageUtils.isCanadaRegion() -> {
                if (AppLanguageUtils.isFrench()) {
                    constantURLS.canadaFrPrivacy
                } else {
                    constantURLS.canadaEnPrivacy
                }
            }
            AppLanguageUtils.isMexicoRegion() -> {
                if (AppLanguageUtils.isSpanish()) {
                    constantURLS.mxEsPrivacy
                } else {
                    constantURLS.mxEnPrivacy
                }
            }
            else -> {
                constantURLS.usPrivacy
            }
        }
    }

    fun getAccountTerms(brand: String): String {
        val constantURLS: AccountTermsAndPrivacyUrls =
            when (brand) {
                Brand.TOYOTA.appBrand -> ToyAccountUrls
                Brand.LEXUS.appBrand -> LexusAccountUrls
                Brand.SUBARU.appBrand -> SubaruAccountURLS
                else -> LexusAccountUrls
            }
        return when {
            AppLanguageUtils.isTDPRRegion() -> {
                if (AppLanguageUtils.isSpanish()) {
                    constantURLS.prEsTerms
                } else {
                    constantURLS.prEnTerms
                }
            }
            AppLanguageUtils.isCanadaRegion() -> {
                if (AppLanguageUtils.isFrench()) {
                    constantURLS.canadaFrTerms
                } else {
                    constantURLS.canadaEnTerms
                }
            }
            AppLanguageUtils.isMexicoRegion() -> {
                if (AppLanguageUtils.isSpanish()) {
                    constantURLS.mxEsTerms
                } else {
                    constantURLS.mxEnTerms
                }
            }
            else -> {
                constantURLS.usTerms
            }
        }
    }

    @JvmStatic
    fun getVehicleTerms(
        brand: String?,
        isCY17: Boolean,
        region: String?,
    ): String {
        val constantURLS: VehicleTermsAndPrivacyUrls =
            when (brand) {
                Brand.TOYOTA.appBrand -> ToyVehicleUrls
                Brand.LEXUS.appBrand -> LexusVehicleUrls
                Brand.SUBARU.appBrand -> ToyVehicleUrls
                else -> ToyVehicleUrls
            }
        return when {
            ToyotaConstants.REGION_PR.equals(region, ignoreCase = true) -> {
                tDPRVehicleTermsUrl(isCY17, constantURLS)
            }
            ToyotaConstants.REGION_CA.equals(region, ignoreCase = true) -> {
                canadaVehicleTermsUrl(isCY17, constantURLS)
            }
            ToyotaConstants.REGION_MX.equals(region, ignoreCase = true) -> {
                mexicoVehicleTermsUrl(constantURLS)
            }
            ToyotaConstants.REGION_HI.equals(region, ignoreCase = true) -> {
                hawaiVehicleTermsUrl(isCY17, constantURLS)
            }
            else -> constantURLS.usTerms
        }
    }

    private fun hawaiVehicleTermsUrl(
        isCY17: Boolean,
        constantURLS: VehicleTermsAndPrivacyUrls,
    ) = if (isCY17) {
        constantURLS.hiTerms17En
    } else {
        constantURLS.hiTermsEn
    }

    private fun mexicoVehicleTermsUrl(constantURLS: VehicleTermsAndPrivacyUrls) =
        if (AppLanguageUtils.isSpanish()) {
            constantURLS.mxTerms
        } else {
            constantURLS.mxTermsEn
        }

    private fun canadaVehicleTermsUrl(
        isCY17: Boolean,
        constantURLS: VehicleTermsAndPrivacyUrls,
    ) = if (isCY17) {
        if (AppLanguageUtils.isFrench()) {
            constantURLS.tciVehicleDetail17FrTerms
        } else {
            constantURLS.tciVehicleDetail17EnTerms
        }
    } else {
        if (AppLanguageUtils.isFrench()) {
            constantURLS.tciVehicleDetail17plusFrTerms
        } else {
            constantURLS.tciVehicleDetail17plusEnTerms
        }
    }

    private fun tDPRVehicleTermsUrl(
        isCY17: Boolean,
        constantURLS: VehicleTermsAndPrivacyUrls,
    ) = when {
        isCY17 -> {
            if (AppLanguageUtils.isSpanish()) {
                constantURLS.prTerms17Es
            } else {
                constantURLS.prTerms17En
            }
        }
        else -> {
            if (AppLanguageUtils.isSpanish()) {
                constantURLS.prTermsEs
            } else {
                constantURLS.prTermsEn
            }
        }
    }

    @JvmStatic
    fun getVehiclePrivacy(
        brand: String?,
        region: String?,
    ): String {
        val constantURLS: VehicleTermsAndPrivacyUrls =
            when (brand) {
                Brand.TOYOTA.appBrand -> ToyVehicleUrls
                Brand.LEXUS.appBrand -> LexusVehicleUrls
                Brand.SUBARU.appBrand -> ToyVehicleUrls
                else -> ToyVehicleUrls
            }
        return when {
            ToyotaConstants.REGION_PR.equals(region, ignoreCase = true) -> {
                if (AppLanguageUtils.isSpanish()) {
                    constantURLS.prPrivacyEs
                } else {
                    constantURLS.prPrivacyEn
                }
            }
            ToyotaConstants.REGION_CA.equals(region, ignoreCase = true) -> {
                if (AppLanguageUtils.isFrench()) {
                    constantURLS.tciVehicleDetailFrPrivacy
                } else {
                    constantURLS.tciVehicleDetailEnPrivacy
                }
            }
            ToyotaConstants.REGION_MX.equals(region, ignoreCase = true) -> {
                if (AppLanguageUtils.isSpanish()) {
                    constantURLS.mxPrivacy
                } else {
                    constantURLS.mxPrivacyEn
                }
            }
            else -> {
                constantURLS.usPrivacy
            }
        }
    }

    @JvmStatic
    fun contactSupport(
        context: Context,
        region: String?,
        brand: String,
    ) {
        phoneCall(context, getPhoneNO(context, region, brand))
    }

    @JvmStatic
    fun getPhoneNO(
        context: Context,
        region: String?,
        brand: String,
    ): String =
        when {
            ToyotaConstants.REGION_CA.equals(region, ignoreCase = true) -> {
                customerSupportCAPhoneNumber(brand, context)
            }
            ToyotaConstants.REGION_PR.equals(region, ignoreCase = true) -> {
                customerSupportPRPhoneNumber(brand, context)
            }
            ToyotaConstants.REGION_HI.equals(region, ignoreCase = true) -> {
                customerSupportHIPhoneNumber(brand, context)
            }
            ToyotaConstants.REGION_MX.equals(region, ignoreCase = true) -> {
                context.getString(R.string.MX_Lexus_customer_support)
            }
            else -> {
                customerSupportUSPhoneNumber(brand, context)
            }
        }

    private fun customerSupportUSPhoneNumber(
        brand: String,
        context: Context,
    ) = when (brand) {
        Brand.TOYOTA.appBrand -> context.getString(R.string.US_Toyota_customer_support)
        Brand.LEXUS.appBrand -> context.getString(R.string.US_Lexus_customer_support)
        Brand.SUBARU.appBrand -> context.getString(R.string.US_Subaru_customer_support)
        else -> context.getString(R.string.US_Toyota_customer_support)
    }

    private fun customerSupportHIPhoneNumber(
        brand: String,
        context: Context,
    ) = when (brand) {
        Brand.TOYOTA.appBrand ->
            context.getString(
                R.string.SERVCO_Toyota_customer_support,
            )

        Brand.LEXUS.appBrand ->
            context.getString(
                R.string.SERVCO_Lexus_customer_support,
            )

        else -> context.getString(R.string.SERVCO_Toyota_customer_support)
    }

    private fun customerSupportPRPhoneNumber(
        brand: String,
        context: Context,
    ) = when (brand) {
        Brand.TOYOTA.appBrand ->
            context.getString(
                R.string.TDPR_Toyota_customer_support,
            )

        Brand.LEXUS.appBrand -> context.getString(R.string.TDPR_Lexus_customer_support)
        else -> context.getString(R.string.TDPR_Toyota_customer_support)
    }

    private fun customerSupportCAPhoneNumber(
        brand: String,
        context: Context,
    ) = when (brand) {
        Brand.TOYOTA.appBrand -> context.getString(R.string.TCI_Toyota_customer_support)
        Brand.LEXUS.appBrand -> context.getString(R.string.TCI_Lexus_customer_support)
        Brand.SUBARU.appBrand -> context.getString(R.string.TCI_Subaru_customer_support)
        else -> context.getString(R.string.TCI_Toyota_customer_support)
    }

    fun openBrowser(
        context: Context,
        url: String?,
        title: String?,
    ) {
        try {
            val intent = Intent(context, InternalWebActivity::class.java)
            intent.putExtra(URL, url)
            intent.putExtra(TITLE, title)
            context.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun openBrowserWithoutAllSettingsEnabled(
        context: Context,
        url: String?,
    ) {
        try {
            val intent = Intent(context, InternalWebActivity::class.java)
            intent.putExtra(URL, url)
            intent.putExtra(TITLE, "")
            intent.putExtra(IS_ALL_SETTINGS_REQUIRED, false)
            context.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @JvmStatic
    fun openCustomChromeTab(
        context: Context?,
        url: String?,
    ) {
        val builder = CustomTabsIntent.Builder()
        val customTabsIntent = builder.build()
        customTabsIntent.launchUrl(context!!, Uri.parse(url))
    }

    fun openPDF(url: String?): String {
        val googleDocsUrl = "http://docs.google.com/viewer?embedded=true&url="
        return googleDocsUrl + (url ?: "")
    }

    fun getAssociatedSXMBrands(vehicles: ArrayList<VehicleInfo>?): String {
        var brand: String = BuildConfig.APP_BRAND
        vehicles?.let {
            for (vehicle in it) {
                if (vehicle.isToyotaBrand && !brand.contains("T")) {
                    brand += "T"
                } else if (!brand.contains("L")) {
                    brand += "L"
                }
            }
        }
        return brand
    }

    fun getAssociatedSXMGeneration(vehicles: ArrayList<VehicleInfo>?): String {
        var generation = ToyotaConstants.CY17PLUS
        if (vehicles != null && vehicles.stream().anyMatch { obj: VehicleInfo -> obj.isCY17 }) {
            generation = ToyotaConstants.CY17
        }
        return generation
    }

    fun isRemoteCapable(capabilities: ArrayList<CapabilityItem>?): Boolean {
        if (!capabilities.isNullOrEmpty()) {
            for (item in capabilities) {
                if (item.name != null &&
                    (
                        item.name.equals("RemoteCapable", ignoreCase = true) ||
                            item.name.equals("EvRemoteService", ignoreCase = true)
                    )
                ) {
                    return true
                }
            }
        }
        return false
    }

    fun bytesToHexString(bytes: ByteArray): String {
        val builder = StringBuilder()
        for (b in bytes) {
            builder.append(String.format("%02X", b))
        }

        return builder.toString()
    }

    fun sendEmail(
        context: Context,
        to: Array<String?>,
        subject: String,
        text: String,
    ) {
        val emailIntent = Intent(Intent.ACTION_SEND)
        emailIntent.setData(Uri.parse("mailto:"))
        emailIntent.putExtra(Intent.EXTRA_EMAIL, to)
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, subject)
        emailIntent.putExtra(Intent.EXTRA_TEXT, text)
        try {
            context.startActivity(Intent.createChooser(emailIntent, "Send mail..."))
        } catch (ex: ActivityNotFoundException) {
            Toast
                .makeText(
                    context,
                    context.getString(R.string.email_client_not_available),
                    Toast.LENGTH_SHORT,
                ).show()
        }
    }

    fun monthNumToText(
        context: Context,
        monthNum: String?,
    ): String {
        val monthInt: Int
        try {
            monthInt = monthNum?.toInt() ?: 1
            if (monthInt < 0 || monthInt > 12) {
                throw ParseException("", -1)
            }
        } catch (e: ParseException) {
            LogTool.e(TAG, "Unable to parse value to proper month integer")
            return ""
        } catch (e: NumberFormatException) {
            LogTool.e(TAG, "Unable to parse value to proper month integer")
            return ""
        }

        val months = context.resources.getStringArray(R.array.month_array)

        return months[monthInt - 1]
    }

    fun getFormattedTime(
        dtFormat: DateTimeFormatter?,
        date: String?,
    ): String {
        if (null != dtFormat && null != date) {
            return dtFormat.format(ZonedDateTime.parse(date))
        }
        return ToyotaConstants.EMPTY_STRING
    }

    fun getServerDtFormat(): DateTimeFormatter =
        if (AppLanguageUtils.isEnglish()) {
            DateTimeFormatter.ofPattern("h:mm a")
        } else {
            DateTimeFormatter.ofPattern(
                "HH:mm",
            )
        }

    @JvmStatic
    fun getLastDigitColorSpannable(
        body: String,
        color: Int,
    ): Spannable {
        val spannableStringBuilder: Spannable = SpannableString(body)
        spannableStringBuilder.setSpan(
            ForegroundColorSpan(color),
            body.length - 1,
            body.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE,
        )
        return spannableStringBuilder
    }

    @JvmStatic
    fun isSubaru(): Boolean = BuildConfig.APP_BRAND == Brand.SUBARU.appBrand

    @JvmStatic
    fun isNotSubaru(): Boolean = !isSubaru()

    fun getDisconnectRemoteVehicleAccessUrls(brand: Brand): String {
        val constantURLS: VehicleAccessUrls =
            when (brand) {
                Brand.TOYOTA -> ToyotaVehicleAccessUrls
                Brand.LEXUS -> LexusVehicleAccessUrls
                Brand.SUBARU -> SubaruVehicleAccessUrls
            }
        return constantURLS.disconnect
    }
}
