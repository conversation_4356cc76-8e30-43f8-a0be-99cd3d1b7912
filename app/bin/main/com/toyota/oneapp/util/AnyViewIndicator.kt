package com.toyota.oneapp.util

import android.animation.Animator
import android.animation.AnimatorInflater
import android.annotation.TargetApi
import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.animation.Interpolator
import android.widget.LinearLayout
import androidx.annotation.AnimatorRes
import androidx.annotation.DrawableRes
import com.toyota.oneapp.R
import kotlin.math.abs

open class AnyViewIndicator : LinearLayout {
    protected var mLastPosition = -1
    private var mIndicatorMargin = -1
    private var mIndicatorWidth = -1
    private var mIndicatorHeight = -1

    @AnimatorRes
    private var mAnimatorResId = R.animator.scale_with_alpha
    private var mAnimatorReverseResId = 0
    private var mIndicatorBackgroundResId = R.drawable.white_radius
    private var mIndicatorUnselectedBackgroundResId = R.drawable.white_radius
    private var mAnimatorOut: Animator? = null
    private var mAnimatorIn: Animator? = null
    private var mImmediateAnimatorOut: Animator? = null
    private var mImmediateAnimatorIn: Animator? = null
    private var isAnimationEnable = true
    open var currentPosition: Int = 0
        set(currentPosition) {
            field = currentPosition
            onCurrentLocationChange()
        }
    protected open var itemCount: Int = 0
        set(itemCount) {
            field = itemCount
            updateCircleIndicator()
        }

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    ) {
        init(context, attrs)
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int, defStyleRes: Int) : super(
        context,
        attrs,
        defStyleAttr,
        defStyleRes,
    ) {
        init(context, attrs)
    }

    private fun init(
        context: Context,
        attrs: AttributeSet?,
    ) {
        handleTypedArray(context, attrs)
        checkIndicatorConfig(context)
    }

    private fun handleTypedArray(
        context: Context,
        attrs: AttributeSet?,
    ) {
        if (attrs == null) {
            return
        }

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.AnyViewIndicator)
        mIndicatorWidth =
            typedArray.getDimensionPixelSize(R.styleable.AnyViewIndicator_avi_width, -1)
        mIndicatorHeight =
            typedArray.getDimensionPixelSize(R.styleable.AnyViewIndicator_avi_height, -1)
        mIndicatorMargin =
            typedArray.getDimensionPixelSize(R.styleable.AnyViewIndicator_avi_margin, -1)

        mAnimatorResId =
            typedArray.getResourceId(
                R.styleable.AnyViewIndicator_avi_animator,
                R.animator.scale_with_alpha,
            )
        mAnimatorReverseResId =
            typedArray.getResourceId(R.styleable.AnyViewIndicator_avi_animator_reverse, 0)
        mIndicatorBackgroundResId =
            typedArray.getResourceId(
                R.styleable.AnyViewIndicator_avi_drawable,
                R.drawable.white_radius,
            )
        mIndicatorUnselectedBackgroundResId =
            typedArray.getResourceId(
                R.styleable.AnyViewIndicator_avi_drawable_unselected,
                mIndicatorBackgroundResId,
            )
        isAnimationEnable =
            typedArray.getBoolean(R.styleable.AnyViewIndicator_avi_animation_enable, true)

        val orientation = typedArray.getInt(R.styleable.AnyViewIndicator_avi_orientation, -1)
        setOrientation(if (orientation == VERTICAL) VERTICAL else HORIZONTAL)

        val gravity = typedArray.getInt(R.styleable.AnyViewIndicator_avi_gravity, -1)
        setGravity(if (gravity >= 0) gravity else Gravity.CENTER)

        typedArray.recycle()
    }

    @JvmOverloads
    fun configureIndicator(
        indicatorWidth: Int,
        indicatorHeight: Int,
        indicatorMargin: Int,
        @AnimatorRes animatorId: Int = R.animator.scale_with_alpha,
        @AnimatorRes animatorReverseId: Int = 0,
        @DrawableRes indicatorBackgroundId: Int = R.drawable.white_radius,
        @DrawableRes indicatorUnselectedBackgroundId: Int = R.drawable.white_radius,
    ) {
        mIndicatorWidth = indicatorWidth
        mIndicatorHeight = indicatorHeight
        mIndicatorMargin = indicatorMargin

        mAnimatorResId = animatorId
        mAnimatorReverseResId = animatorReverseId
        mIndicatorBackgroundResId = indicatorBackgroundId
        mIndicatorUnselectedBackgroundResId = indicatorUnselectedBackgroundId

        checkIndicatorConfig(context)
    }

    private fun checkIndicatorConfig(context: Context) {
        mIndicatorWidth =
            if (mIndicatorWidth < 0) dip2px(DEFAULT_INDICATOR_WIDTH) else mIndicatorWidth
        mIndicatorHeight =
            if (mIndicatorHeight < 0) dip2px(DEFAULT_INDICATOR_WIDTH) else mIndicatorHeight
        mIndicatorMargin =
            if (mIndicatorMargin < 0) dip2px(DEFAULT_INDICATOR_WIDTH) else mIndicatorMargin

        mAnimatorResId = if (mAnimatorResId == 0) R.animator.scale_with_alpha else mAnimatorResId

        mAnimatorOut = createAnimatorOut(context)
        mImmediateAnimatorOut =
            createAnimatorOut(context).apply {
                duration = 0
            }

        mAnimatorIn = createAnimatorIn(context)
        mImmediateAnimatorIn =
            createAnimatorIn(context).apply {
                duration = 0
            }

        mIndicatorBackgroundResId =
            if (mIndicatorBackgroundResId == 0) {
                R.drawable.white_radius
            } else {
                mIndicatorBackgroundResId
            }
        mIndicatorUnselectedBackgroundResId =
            if (mIndicatorUnselectedBackgroundResId == 0) {
                mIndicatorBackgroundResId
            } else {
                mIndicatorUnselectedBackgroundResId
            }
    }

    private fun createAnimatorOut(context: Context): Animator = AnimatorInflater.loadAnimator(context, mAnimatorResId)

    private fun createAnimatorIn(context: Context): Animator {
        val animatorIn: Animator
        if (mAnimatorReverseResId == 0) {
            animatorIn = AnimatorInflater.loadAnimator(context, mAnimatorResId)

            animatorIn.interpolator = ReverseInterpolator()
        } else {
            animatorIn = AnimatorInflater.loadAnimator(context, mAnimatorReverseResId)
        }
        return animatorIn
    }

    /**
     * Force the circle indicator upgrade
     */
    protected fun updateCircleIndicator() {
        val newCount = itemCount
        val currentCount = childCount

        mLastPosition =
            when {
                newCount == currentCount -> return // No change
                mLastPosition < newCount -> currentPosition
                else -> -1
            }

        // show the first
        if (mLastPosition == -1 && newCount > 0) {
            mLastPosition = 0
        }

        createIndicators()
    }

    protected fun onCurrentLocationChange() {
        if (isAnimationEnable) {
            if (mAnimatorIn!!.isRunning) {
                mAnimatorIn!!.end()
                mAnimatorIn!!.cancel()
            }

            if (mAnimatorOut!!.isRunning) {
                mAnimatorOut!!.end()
                mAnimatorOut!!.cancel()
            }
        }

        val currentIndicator: View? = getChildAt(mLastPosition)
        if (mLastPosition >= 0 && currentIndicator != null) {
            currentIndicator.setBackgroundResource(mIndicatorUnselectedBackgroundResId)

            if (isAnimationEnable) {
                mAnimatorIn!!.setTarget(currentIndicator)
                mAnimatorIn!!.start()
            }
        }
        val position = currentPosition
        val selectedIndicator = getChildAt(position)
        if (selectedIndicator != null) {
            selectedIndicator.setBackgroundResource(mIndicatorBackgroundResId)
            if (isAnimationEnable) {
                mAnimatorOut!!.setTarget(selectedIndicator)
                mAnimatorOut!!.start()
            }
        }
        mLastPosition = position
    }

    private fun createIndicators() {
        removeAllViews()
        val count = itemCount
        if (count <= 0) {
            return
        }
        var currentItem = currentPosition
        val orientation = orientation

        if (currentItem < 0) currentItem = 0
        for (i in 0 until count) {
            if (currentItem == i) {
                addIndicator(orientation, mIndicatorBackgroundResId, mImmediateAnimatorOut)
            } else {
                addIndicator(
                    orientation,
                    mIndicatorUnselectedBackgroundResId,
                    mImmediateAnimatorIn,
                )
            }
        }
    }

    private fun addIndicator(
        orientation: Int,
        @DrawableRes backgroundDrawableId: Int,
        animator: Animator?,
    ) {
        if (isAnimationEnable) {
            if (animator!!.isRunning) {
                animator.end()
                animator.cancel()
            }
        }

        val indicator = View(context)
        indicator.setBackgroundResource(backgroundDrawableId)
        addView(indicator, mIndicatorWidth, mIndicatorHeight)
        val lp = indicator.layoutParams as LayoutParams

        if (orientation == HORIZONTAL) {
            lp.leftMargin = mIndicatorMargin
            lp.rightMargin = mIndicatorMargin
        } else {
            lp.topMargin = mIndicatorMargin
            lp.bottomMargin = mIndicatorMargin
        }

        indicator.layoutParams = lp

        if (isAnimationEnable) {
            animator!!.setTarget(indicator)
            animator.start()
        }
    }

    private fun dip2px(dpValue: Float): Int {
        val scale = resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }

    private class ReverseInterpolator : Interpolator {
        override fun getInterpolation(value: Float): Float = abs(1.0f - value)
    }

    companion object {
        private const val DEFAULT_INDICATOR_WIDTH = 5f
    }
}
