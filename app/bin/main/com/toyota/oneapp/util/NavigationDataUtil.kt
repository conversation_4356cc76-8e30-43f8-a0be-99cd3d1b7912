/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.util

import com.toyota.oneapp.features.dealerservice.domain.model.Dealer
import com.toyota.oneapp.features.find.domain.model.PreferredDealerStatus.EMPTY_PREFERRED_DEALER
import com.toyota.oneapp.features.find.domain.model.PreferredDealerStatus.SET_EXSIT_PREFERRED

object NavigationDataUtil {
    fun createPreferredDealerData(dealer: Dealer): HashMap<String, Any?> =
        hashMapOf(
            "preferredDealerStatus" to SET_EXSIT_PREFERRED.name,
            "dealerName" to dealer.dealerName,
            "dealerCode" to dealer.dealerCode,
            "zip" to dealer.zip,
            "state" to dealer.state,
            "country" to dealer.country,
            "city" to dealer.city,
            "address" to dealer.address,
            "isFromNative" to "true",
        )

    fun createEmptyPreferredDealerData(): HashMap<String, Any?> =
        hashMapOf(
            "preferredDealerStatus" to EMPTY_PREFERRED_DEALER.name,
            "isFromNative" to "true",
        )
}
