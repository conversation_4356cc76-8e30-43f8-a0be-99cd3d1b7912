package com.toyota.oneapp.util

import android.content.Context
import com.toyota.oneapp.core.ApplicationContext
import javax.inject.Inject

class AdUtil
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
    ) {
        fun getAdvertisingId(callback: AdUtilCallback?) {
            AdvertisingIdClient.getAdvertisingId(
                context,
                object : AdvertisingIdClient.Listener {
                    override fun onAdvertisingIdClientFinish(adInfo: AdvertisingIdClient.AdInfo?) {
                        callback?.onComplete(adInfo?.id, true)
                    }

                    override fun onAdvertisingIdClientFail(exception: Exception?) {
                        callback?.onComplete(null, false)
                    }
                },
            )
        }

        interface AdUtilCallback {
            fun onComplete(
                adId: String?,
                success: Boolean,
            )
        }
    }
