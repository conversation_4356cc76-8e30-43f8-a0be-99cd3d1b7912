package com.toyota.oneapp.util

import android.content.Context
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.ui.flutter.ApiConfig
import com.toyota.oneapp.ui.flutter.GlobalConfigParams
import com.toyota.oneapp.ui.flutter.TFSServiceConfig
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.StandardMessageCodec
import java.io.File

/**
 * Created by <PERSON> on 2020/11/25.
 */
object FlutterOneAppUtil {
    @JvmStatic
    fun preWarmFlutter(
        context: Context,
        vararg engineIds: String,
    ) {
        /**
         * Remove Secured SharePreferences for Android to avoid crashes
         */
        val sharedPreferencesKeys =
            context.getSharedPreferences(
                "FlutterSecureKeyStorage",
                Context.MODE_PRIVATE,
            )
        val keyEditor = sharedPreferencesKeys.edit()
        keyEditor.clear()
        keyEditor.apply() // or editor.commit() to block the calling thread

        val sharedPreferencesValues =
            context.getSharedPreferences(
                "FlutterSecureStorage",
                Context.MODE_PRIVATE,
            )
        val valuesEditor = sharedPreferencesValues.edit()
        valuesEditor.clear()
        valuesEditor.apply() // or editor.commit() to block the calling thread

        /**
         * Remove cache_database for Android to avoid crashes which is encrypted
         */
        // Get the path of the cache_database folder
        try {
            val cacheDatabaseDir = File(context.cacheDir, "cache_databases")
            // Delete all files in the folder
            cacheDatabaseDir.listFiles()?.forEach { file ->
                file.delete()
            }
        } catch (exception: Exception) {
            FirebaseCrashlytics.getInstance().log(
                "Cache Database Delete ${exception.message ?: ""}",
            )
        }

        var proxyAddress: String?
        try {
            val proxy = System.getProperty("http.proxyHost")
            val port = System.getProperty("http.proxyPort")
            proxyAddress =
                if (proxy == null && port == null) {
                    null
                } else {
                    "$proxy:$port"
                }
            engineIds.forEach {
                val flutterEngine = FlutterEngine(context.applicationContext)
                // Start executing Dart code to pre-warm the FlutterEngine.
                flutterEngine.dartExecutor.executeDartEntrypoint(
                    DartExecutor.DartEntrypoint.createDefault(),
                    listOf(
                        Gson().toJson(
                            GlobalConfigParams(
                                isProxyEnabled = BuildConfig.DEBUG,
                                localeString = AppLanguageUtils.getCurrentLocaleString(),
                                apiEnvironment = BuildConfig.FLAVOR_environment,
                                appBrand = BuildConfig.APP_BRAND,
                                proxyAddress = proxyAddress,
                            ),
                        ),
                        Gson().toJson(
                            ApiConfig(
                                baseURL = BuildConfig.CDN_BASE_URL,
                                baseCFAIUrl = BuildConfig.CDN_BASE_URL,
                                evBaseURL = BuildConfig.CDN_BASE_URL,
                                cpWebAuthBaseUrl = BuildConfig.CP_WEB_AUTH_BASE_URL,
                                xFlexApiKey = BuildConfig.API_GATEWAY_KEY,
                                xApiKey = BuildConfig.API_GATEWAY_KEY,
                                deviceApiKey = BuildConfig.DEVICE_STATUS_API_KEY,
                                serviceShopBaseUrl = BuildConfig.CDN_BASE_URL,
                                financeAccountBaseUrl = BuildConfig.TFS_API_TOYOTA_URL,
                                googleBaseUrl = BuildConfig.GOOGLE_BASEURL,
                                evxApiKey = BuildConfig.API_GATEWAY_KEY,
                                evgoWebUrl = BuildConfig.EVGOURL,
                                cpWebAuthCallBackUrl = BuildConfig.CP_WEB_AUTH_CALLBACK_URL,
                                evAddressSearchUrl = BuildConfig.EV_ADDRESS_SEARCH_URL,
                                evgoForgetPasswordUrl = BuildConfig.EVGO_FORGET_PASSWORD_URL,
                                wattTimeURL = BuildConfig.WATT_TIME_URL,
                                wattTimeElectricityAndHealthURL = BuildConfig.WATT_TIME_ELECTRICITY_AND_HEALTH_URL,
                            ),
                        ),
                        Gson().toJson(
                            TFSServiceConfig(
                                evBaseURL = BuildConfig.CDN_BASE_URL,
                                oneAppBaseURL = BuildConfig.CDN_BASE_URL,
                                tfsAuthenticateLexusURL = BuildConfig.TFS_API_LEXUS_AUTHENTICATE_URL,
                                tfsAuthenticateToyotaURL = BuildConfig.TFS_API_TOYOTA_AUTHENTICATE_URL,
                                tfsAuthorizeLexusURL = BuildConfig.TFS_API_LEXUS_AUTHORIZE_URL,
                                tfsAuthorizeToyotaURL = BuildConfig.TFS_API_TOYOTA_AUTHORIZE_URL,
                                tfsEEFLexusURL = BuildConfig.TFS_API_LEXUS_URL,
                                tfsEEFToyotaURL = BuildConfig.TFS_API_TOYOTA_URL,
                                tfsAddBankURL = BuildConfig.TFS_ADD_BANK_URL,
                                tfsAddBankZuoraURL = BuildConfig.TFS_ADD_BANK_ZUORA_URL,
                                tfsAddBankZuoraHost = BuildConfig.TFS_ADD_BANK_ZUORA_HOST,
                                evxApiKey = BuildConfig.API_GATEWAY_KEY,
                                oneAppApiKey = BuildConfig.API_GATEWAY_KEY,
                                tfsFrClientSecret = BuildConfig.TFS_CLIENT_SECRET,
                                tfsAddBankPageId = BuildConfig.TFS_ADD_BANK_PAGE_ID,
                            ),
                        ),
                    ),
                )
                // Cache the FlutterEngine to be used by FlutterActivity.
                FlutterEngineCache
                    .getInstance()
                    .put(it, flutterEngine)
            }
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().log("Flutter PreWarm Exception ${e.message ?: ""}")
        }
    }

    @JvmStatic
    fun clearFlutter() {
        FlutterEngineCache.getInstance().clear()
    }

    @JvmStatic
    fun sendBasicChannelMessage(
        engineId: String,
        channelName: String,
        param: Any?,
        callback: BasicMessageChannel.Reply<Any>? = null,
    ) {
        val flutterEngine = FlutterEngineCache.getInstance().get(engineId)
        if (flutterEngine != null) {
            val messageChannel =
                BasicMessageChannel(
                    flutterEngine.dartExecutor.binaryMessenger,
                    channelName,
                    StandardMessageCodec.INSTANCE,
                )
            messageChannel.send(param, callback)
        }
    }
}
