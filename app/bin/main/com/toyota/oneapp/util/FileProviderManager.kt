package com.toyota.oneapp.util

import android.content.Context
import android.net.Uri
import androidx.core.content.FileProvider
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.core.ApplicationContext
import java.io.File
import javax.inject.Inject

class FileProviderManager
    @Inject
    constructor(
        @ApplicationContext
        var mContext: Context,
    ) {
        fun openFiles(file: File): Uri? {
            val path: Uri? =
                FileProvider.getUriForFile(
                    mContext,
                    BuildConfig.APPLICATION_ID + ".providers",
                    file,
                )
            return path
        }
    }
