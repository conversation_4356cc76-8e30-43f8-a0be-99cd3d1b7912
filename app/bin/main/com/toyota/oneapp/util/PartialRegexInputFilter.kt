package com.toyota.oneapp.util

import android.text.InputFilter
import android.text.Spanned
import java.util.regex.Matcher
import java.util.regex.Pattern

class PartialRegexInputFilter(
    pattern: String,
) : InputFilter {
    private val mPattern: Pattern = Pattern.compile(pattern)

    override fun filter(
        source: CharSequence,
        sourceStart: Int,
        sourceEnd: Int,
        destination: Spanned,
        destinationStart: Int,
        destinationEnd: Int,
    ): CharSequence? {
        val textToCheck =
            destination.subSequence(0, destinationStart).toString() +
                source.subSequence(sourceStart, sourceEnd) +
                destination.subSequence(destinationEnd, destination.length).toString()
        val matcher: Matcher = mPattern.matcher(textToCheck)
        // Entered text does not match the pattern
        if (!matcher.matches()) { // It does not match partially too
            if (!matcher.hitEnd()) {
                return ""
            }
        }
        return null
    }
}
