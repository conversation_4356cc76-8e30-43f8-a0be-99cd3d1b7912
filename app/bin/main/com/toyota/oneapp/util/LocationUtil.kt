package com.toyota.oneapp.util

import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.ctp.v1.ProfileServiceTier1
import com.toyota.oneapp.model.dashboard.card.MyDestinationsItem
import com.toyota.oneapp.model.poi.Address
import com.toyota.oneapp.model.poi.Coordinates
import com.toyota.oneapp.model.poi.LocationDetails

object LocationUtil {
    const val LOCATION_ADDRESS = "locationDetail"
    const val LOCATION_OBJECT = "locationAddress"
    const val IS_EDIT_MODE = "isEditMode"
    const val ROUTE = "route"

    @JvmStatic
    fun getLocationDetails(userProfile: ProfileServiceServer.UserProfile): MyDestinationsItem {
        val home =
            userProfile.commonVehicleSettings
                .navigationSettings.home.value
        val work =
            userProfile.commonVehicleSettings
                .navigationSettings.work.value
        val locationItem = MyDestinationsItem()
        locationItem.home = home.toLocationDetails()
        locationItem.work = work.toLocationDetails()
        locationItem.favList =
            userProfile.commonVehicleSettings.navigationSettings
                .favorites
                .toLocationDetailsList()
        locationItem.recentDestinations =
            userProfile.commonVehicleSettings.navigationSettings
                .recentDestinations
                .toLocationDetailsList()
        return locationItem
    }

    private fun ProfileServiceTier1.NavigationSettings.PoiList.toLocationDetailsList(): ArrayList<LocationDetails> =
        ArrayList(
            valueList.map {
                it.toLocationDetails()
            },
        )
}

fun ProfileServiceTier1.NavigationSettings.Poi.toLocationDetails(): LocationDetails =
    LocationDetails(
        formattedAddress,
        Coordinates(
            location?.latitude ?: 0.0,
            location?.longitude ?: 0.0,
        ),
        name,
        address?.getAddressDetail()
            ?: Address(
                "",
                "",
                "",
                "",
                "",
                "",
                "",
            ),
        placeId,
        Coordinates(
            routing?.latitude ?: 0.0,
            routing?.longitude ?: 0.0,
        ),
        address?.intersection ?: "",
        locationType.toInt(),
        phoneNumber ?: "",
        refreshDate?.seconds ?: 0,
        timestamp?.seconds ?: 0,
    )

fun ProfileServiceTier1.NavigationSettings.Poi.Address.getAddressDetail(): Address =
    Address(
        street,
        houseNumber,
        city,
        postalCode,
        countryShort,
        adminRegion,
        adminRegionShort,
    )
