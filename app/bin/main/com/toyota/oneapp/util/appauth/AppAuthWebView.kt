package com.toyota.oneapp.util.appauth

import android.annotation.SuppressLint
import android.net.Uri
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings.LOAD_NO_CACHE
import android.webkit.WebView
import android.webkit.WebViewClient
import net.openid.appauth.AppAuthConfiguration
import net.openid.appauth.AuthorizationRequest
import net.openid.appauth.AuthorizationResponse
import net.openid.appauth.AuthorizationService
import net.openid.appauth.AuthorizationServiceConfiguration
import net.openid.appauth.CodeVerifierUtil
import java.util.Locale

class AppAuthWebView(
    private val appAuthWebViewData: AppAuthData,
    private val webView: WebView,
) {
    val authorizationService: AuthorizationService =
        AuthorizationService(
            webView.context,
            AppAuthConfiguration.Builder().build(),
        )
    private val authorizationRequest: AuthorizationRequest
    private val allowedHost: String

    init {
        // From AppAuth Library
        val mAuthConfig =
            AuthorizationServiceConfiguration(
                Uri.parse(appAuthWebViewData.authorizationEndpointUri),
                Uri.parse(appAuthWebViewData.tokenEndpointUri),
                Uri.parse(appAuthWebViewData.registrationEndpointUri),
            )
        val authRequestBuilder =
            AuthorizationRequest
                .Builder(
                    mAuthConfig,
                    appAuthWebViewData.clientId,
                    appAuthWebViewData.responseType,
                    Uri.parse(appAuthWebViewData.redirectLoginUri),
                ).setScope(appAuthWebViewData.scope)

        val mCodeVerifier = if (appAuthWebViewData.isGenerateCodeVerifier) CodeVerifierUtil.generateRandomCodeVerifier() else null
        authorizationRequest = authRequestBuilder.setCodeVerifier(mCodeVerifier).build()
        allowedHost = Uri.parse(appAuthWebViewData.authorizationEndpointUri).host!!
    }

    @SuppressLint("SetJavaScriptEnabled")
    fun loadView(callback: (String?) -> Unit) {
        webView.apply {
            webViewClient = AppAuthWebViewClient(callback, appAuthWebViewData)
            settings.javaScriptEnabled = true
            settings.cacheMode = LOAD_NO_CACHE
            webChromeClient = WebChromeClient()
            loadUrl(authorizationRequest.toUri().toString())
        }
    }

    inner class AppAuthWebViewClient(
        val callback: (String?) -> Unit,
        private val webViewData: AppAuthData,
    ) : WebViewClient() {
        override fun shouldOverrideUrlLoading(
            view: WebView,
            request: WebResourceRequest,
        ): Boolean {
            val url = request.url.toString()
            val allowRedirectUrl =
                url.lowercase(Locale.getDefault()).startsWith(
                    webViewData.redirectLoginUri.lowercase(Locale.getDefault()),
                )
            if (allowRedirectUrl) {
                val response =
                    AuthorizationResponse
                        .Builder(authorizationRequest)
                        .fromUri(Uri.parse(url))
                        .build()
                authorizationService.dispose()
                callback.invoke(response.authorizationCode)
            }
            val host = request.url.host
            return !(allowRedirectUrl || (host != null && host.endsWith(allowedHost)))
        }
    }

    fun closeAuthService() {
        authorizationService.dispose()
    }

    data class AppAuthData(
        val clientId: String,
        val redirectLoginUri: String,
        val scope: String,
        val authorizationEndpointUri: String,
        val tokenEndpointUri: String,
        val responseType: String = "code",
        val registrationEndpointUri: String = "",
        val clientSecret: String = "",
        val redirectLogoutUri: String = "",
        val isGenerateCodeVerifier: Boolean = false,
    )
}
