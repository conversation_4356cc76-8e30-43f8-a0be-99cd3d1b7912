package com.toyota.oneapp.util

import android.content.Context
import android.text.format.DateUtils
import com.google.gson.internal.bind.util.ISO8601Utils
import com.idemia.acs.exceptions.DateFormatException
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.features.core.util.DateTimeUtil
import com.toyota.oneapp.language.LanguageManager
import com.toyota.oneapp.language.RegionManager
import toyotaone.commonlib.log.LogTool
import java.text.ParseException
import java.text.ParsePosition
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import javax.inject.Inject

class DateUtil
    @Inject
    constructor(
        private val languageManager: LanguageManager,
        private val regionManager: RegionManager,
    ) {
        companion object {
            const val H_MM_A = "h:mm a"
            const val HH_MM_SS = "HH:mm:ss"
            const val HH_MM_A = "hh:mm a"
            const val YYYY_MM_DD = "yyyy-MM-dd"
            const val MMM_DD_YYYY = "MMM dd, yyyy"
            const val MMM_DD_YYYY_NO_COMMA = "MMM dd yyyy"
            const val YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss"
            const val YYYY_MM_DD_HH_MM_SS_Z_LITERAL_UTC = "yyyy-MM-dd HH:mm:ss Z 'UTC'"
            const val MMM_DD_AT_HH_MMA = "MMM dd 'at' hh:mma"
            const val HH_MMA = "hh:mma"
            const val TAG = "DateUtil"
        }

        fun getDateFormat(s: String?): String {
            var dateStr = ""
            if (s != null) {
                try {
                    val sdf = SimpleDateFormat("MMM d, yyyy")
                    val netDate = Date(s.toLong() * Constants.LONG_1000L)
                    dateStr = sdf.format(netDate)
                } catch (e: DateFormatException) {
                    LogTool.d(TAG, "**** Error in getTimeFormat -${e.message} **** ")
                    return e.toString()
                }
            }
            return dateStr
        }

        fun getCurrentTime(): String {
            val sdf = SimpleDateFormat(ToyotaConstants.STANDARD_UTC_DATE_FORMAT)
            val currentDate = Calendar.getInstance().time
            return sdf.format(currentDate)
        }

        fun getTimeFormat(s: String?): String {
            var timeStr = ""
            if (s != null) {
                try {
                    val sdf = SimpleDateFormat(HH_MM_A)
                    val netDate = Date(s.toLong() * Constants.LONG_1000L)
                    timeStr = sdf.format(netDate)
                } catch (e: DateFormatException) {
                    LogTool.d(TAG, "**** Error in getTimeFormat -${e.message} **** ")
                    return e.toString()
                }
            }
            return timeStr
        }

        fun getDateUSFormat(s: String): String {
            try {
                val sdf = SimpleDateFormat("MM/dd/yyyy")
                val netDate = Date(s.toLong() * Constants.LONG_1000L)
                return sdf.format(netDate)
            } catch (e: DateFormatException) {
                LogTool.d(TAG, "**** Error in getDateUSFormat -${e.message} **** ")
                return ""
            }
        }

        /**
         * Formats a date as "2020-07-20"
         */
        fun formatLocalDate(date: Date): String =
            SimpleDateFormat(
                YYYY_MM_DD,
                languageManager.getCurrentLocale(),
            ).apply {
                timeZone = TimeZone.getTimeZone(Constants.UTC)
            }.format(date)

        /**
         * Formats a date as "Jan 01, 2020"
         */
        fun formatMediumDate(date: Date): String =
            SimpleDateFormat(
                MMM_DD_YYYY,
                languageManager.getCurrentLocale(),
            ).format(date)

        /**
         * Parse a string "2020-07-20" to date
         */
        fun parseLocalDate(dateStr: String): Date? {
            try {
                val sdf =
                    SimpleDateFormat(
                        YYYY_MM_DD,
                        languageManager.getCurrentLocale(),
                    )
                return sdf.parse(dateStr)
            } catch (e: DateFormatException) {
                LogTool.d(TAG, "**** Error in parseLocalDate -${e.message} **** ")
                return null
            }
        }

        private fun getSelectedVehicleTimeFormat(): String = if (regionManager.isSelectedVehicleCanadian()) "HH:mm" else H_MM_A

        fun getDateFromString(date: String?): Date? =
            try {
                val input =
                    SimpleDateFormat(
                        ToyotaConstants.STANDARD_UTC_DATE_FORMAT,
                        Locale.getDefault(),
                    )
                input.timeZone = TimeZone.getTimeZone(Constants.UTC)
                var d: Date? = null
                d = date?.let { input.parse(it) }
                d
            } catch (e: DateFormatException) {
                LogTool.d(TAG, "**** Error in getDateFromString -${e.message} **** ")
                null
            }

        fun formatDate(inputDate: String?): String? {
            return try {
                val inputFormat = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
                val date: Date = inputDate?.let { inputFormat.parse(it) } ?: return null
                val outputFormat = SimpleDateFormat("MMM d, yyyy", Locale.getDefault())
                outputFormat.format(date)
            } catch (e: ParseException) {
                LogTool.d(TAG, "**** Error in getDateFromString -${e.message} **** ")
                null
            }
        }
    }

fun Int.toFormattedDuration(context: Context): String {
    var remaining = this
    val minutes = remaining % 60
    remaining = ((remaining - minutes) / 60)
    val hours = remaining % 24
    val days = ((remaining - hours) / 24)

    var result = ""
    if (days > 0) {
        result += days.takeIf { it == 1 }?.let {
            context.getString(R.string.one_day_count)
        } ?: run {
            context.getString(R.string.day_count, days)
        }
    }
    if (hours > 0) {
        result += context.getString(R.string.hour_count, hours)
    }
    result += context.getString(R.string.minute_count, minutes)
    return result
}

fun Date.add(
    field: Int,
    value: Int,
): Date {
    val calendar = Calendar.getInstance()
    calendar.time = this
    calendar.add(field, value)
    return calendar.time
}

fun Date.toCalendar(): Calendar =
    Calendar.getInstance().apply {
        time = this@toCalendar
    }

fun String.toDate(): Date = ISO8601Utils.parse(this, ParsePosition(0))

fun String.toCalendar(): Calendar = this.toDate().toCalendar()

fun getDateFromMillis(millisecond: Long): String {
    val sdf = SimpleDateFormat("yyyy-MM-dd")
    val calendar = Calendar.getInstance()
    val date = Date()
    calendar.timeInMillis = millisecond
    return sdf.format(calendar.time)
}

fun convertDateTimeFormat(
    initialDateTime: String,
    convertLocal: Boolean = false,
): String? {
    var tempDate: Date? = null
    var convertedDateTime: String? = null
    try {
        if (convertLocal) {
            tempDate =
                SimpleDateFormat(DateUtil.YYYY_MM_DD, AppLanguageUtils.getCurrentLocale()).parse(
                    initialDateTime,
                )
            convertedDateTime =
                tempDate?.let {
                    SimpleDateFormat(
                        DateUtil.MMM_DD_YYYY,
                        AppLanguageUtils.getCurrentLocale(),
                    ).format(it)
                }
        } else {
            tempDate = SimpleDateFormat(DateUtil.YYYY_MM_DD).parse(initialDateTime)
            convertedDateTime = tempDate?.let { SimpleDateFormat(DateUtil.MMM_DD_YYYY).format(it) }
        }
        return convertedDateTime
    } catch (e: DateFormatException) {
        LogTool.d(DateUtil.TAG, "**** Error in convertDateTimeFormat -${e.message} **** ")
        return ToyotaConstants.EMPTY_STRING
    }
}

fun getAnnouncementDisplayTimeFormat(): DateTimeFormatter =
    DateTimeFormatter.ofPattern(
        "yyyy-MM-dd HH:mm:ss",
    )

fun getTodayDate(): Date? =
    SimpleDateFormat(DateUtil.YYYY_MM_DD).parse(
        LocalDateTime.now(ZoneOffset.UTC).toString(),
    )

fun getEvSwapExpiredDate(evValidTo: String?): LocalDate? =
    try {
        val evSwapExpireDateFormat =
            DateTimeFormatter.ofPattern(
                ToyotaConstants.STANDARD_UTC_DATE_FORMAT,
            )
        evValidTo?.let {
            LocalDateTime
                .parse(
                    evValidTo,
                    evSwapExpireDateFormat,
                ).toLocalDate()
        }
    } catch (e: DateFormatException) {
        LogTool.d(DateUtil.TAG, "**** Error in getEvSwapExpiredDate -${e.message} **** ")
        null
    }

fun String.timeRange(endDate: String): String {
    if (this.isEmpty() || endDate.isEmpty()) {
        return ""
    }
    val startTime =
        getDateNameAsStringFormats(
            this,
            ToyotaConstants.STANDARD_UTC_DATE_FORMAT,
            DateUtil.HH_MM_A,
            true,
        )
    val endTime =
        getDateNameAsStringFormats(
            endDate,
            ToyotaConstants.STANDARD_UTC_DATE_FORMAT,
            DateUtil.HH_MM_A,
            true,
        )

    return "$startTime - $endTime"
}

fun getDateNameAsStringFormats(
    date: String,
    inputFormat: String,
    outputFormat: String,
    isUTC: Boolean = false,
    inputLocale: Locale = AppLanguageUtils.getCurrentLocale(),
): String =
    try {
        val inputParser =
            SimpleDateFormat(
                inputFormat,
                inputLocale,
            )
        inputParser.timeZone =
            if (isUTC) {
                TimeZone.getTimeZone(Constants.UTC)
            } else {
                TimeZone.getDefault()
            }
        val formattedDate = inputParser.parse(date)

        if (formattedDate != null) {
            SimpleDateFormat(outputFormat, AppLanguageUtils.getCurrentLocale()).format(
                formattedDate,
            )
        } else {
            ToyotaConstants.EMPTY_STRING
        }
    } catch (e: DateFormatException) {
        LogTool.d(DateUtil.TAG, "**** Error in getDateNameAsStringFormats -${e.message} **** ")
        ToyotaConstants.EMPTY_STRING
    } catch (e: ParseException) {
        LogTool.d(DateUtil.TAG, "**** ParseError in getDateNameAsStringFormats -${e.message} **** ")
        ToyotaConstants.EMPTY_STRING
    }

fun convertTo12HourFormat(
    time24: String,
    convertLocal: Boolean,
): String {
    try {
        val localTime = if (convertLocal) convertToLocalTime(time24) else time24
        val date24Format = SimpleDateFormat(DateUtil.HH_MM_SS, Locale.US)
        val date12Format = SimpleDateFormat(DateUtil.HH_MM_A, Locale.US)
        val date = date24Format.parse(localTime)
        return date?.let { date12Format.format(it) } ?: ""
    } catch (e: DateFormatException) {
        LogTool.d(DateUtil.TAG, "**** DateFormatError in convertTo12HourFormat -${e.message} **** ")
    } catch (e: ParseException) {
        LogTool.d(DateUtil.TAG, "**** ParseError in convertTo12HourFormat -${e.message} **** ")
    }
    return ""
}

fun convertTo24HourFormat(
    time12: String,
    convertUTC: Boolean,
): String {
    try {
        val localTime = if (convertUTC) convertLocalToUTCTime(time12) else time12
        val date24Format = SimpleDateFormat(DateUtil.HH_MM_SS, Locale.US)
        val date12Format = SimpleDateFormat(DateUtil.H_MM_A, Locale.US)
        val date = date12Format.parse(localTime)
        return date?.let { date24Format.format(it) } ?: ""
    } catch (e: DateFormatException) {
        LogTool.d(DateUtil.TAG, "**** Error in convertTo24HourFormat -${e.message} **** ")
    }
    return ""
}

private fun convertToLocalTime(timeString: String): String {
    var localTime = ""
    try {
        val pattern =
            if (timeString.lowercase().contains("am") ||
                timeString.lowercase().contains("pm")
            ) {
                DateUtil.H_MM_A
            } else {
                DateUtil.HH_MM_SS
            }
        val inputFormat = SimpleDateFormat(pattern, Locale.US)
        inputFormat.timeZone = TimeZone.getTimeZone(Constants.UTC)

        val outputFormat = SimpleDateFormat(pattern, Locale.US)
        outputFormat.timeZone = TimeZone.getDefault() // Local timezone

        val time = inputFormat.parse(timeString)
        localTime = time?.let { outputFormat.format(it) } ?: ""
    } catch (e: DateFormatException) {
        localTime = ""
        LogTool.d(DateUtil.TAG, "**** Error in convertToLocalTime -${e.message} **** ")
    } catch (e: ParseException) {
        localTime = ""
        LogTool.d(DateUtil.TAG, "**** Error in convertToLocalTime -${e.message} **** ")
    }
    return localTime
}

fun convertLocalToUTCTime(timeString: String): String {
    try {
        val pattern =
            if (timeString.lowercase().contains("am") ||
                timeString.lowercase().contains("pm")
            ) {
                DateUtil.H_MM_A
            } else {
                DateUtil.HH_MM_SS
            }
        val inputFormat = SimpleDateFormat(pattern, Locale.US)
        inputFormat.timeZone = TimeZone.getDefault() // Local timezone

        val outputFormat = SimpleDateFormat(pattern, Locale.US)
        outputFormat.timeZone = TimeZone.getTimeZone(Constants.UTC)

        val time = inputFormat.parse(timeString)
        return time?.let { outputFormat.format(it) } ?: ""
    } catch (e: DateFormatException) {
        LogTool.d(DateUtil.TAG, "**** Error in convertLocalToUTCTime -${e.message} **** ")
    }
    return ""
}

fun convertSecondsToHours(seconds: String): String {
    var convertedValue = ""

    val value = seconds.toInt() / Constants.INT_3600; // Converting hours to seconds.
    convertedValue = value.toString()

    if (convertedValue == "1.0") {
        return convertedValue.split(".").first() + " hr"
    } else if (convertedValue.contains(".0")) {
        convertedValue = convertedValue.split('.').first()
    }

    return convertedValue
}

fun convertMinutesToHours(minutes: Int): String = (minutes.toDouble() / Constants.INT_60).toString()

fun convertHoursToMinutes(hours: Double): String = (hours * Constants.INT_60).toString()

fun Date.lastUpdatedDate(): String {
    val todayAt = "Today at"
    val yesterdayAt = "Yesterday at"
    return when {
        DateUtils.isToday(this.time) ->
            "$todayAt ${DateTimeUtil.todayTimeFormat.format(this).lowercase()}"
        DateUtils.isToday(this.time + DateUtils.DAY_IN_MILLIS) ->
            "$yesterdayAt ${DateTimeUtil.todayTimeFormat.format(this).lowercase()}"
        else -> {
            DateTimeUtil.timeFormat_DateMonthYearWithTime.format(this)
        }
    }
}
