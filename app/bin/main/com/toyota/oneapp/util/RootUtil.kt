package com.toyota.oneapp.util

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.text.TextUtils
import toyotaone.commonlib.log.LogTool
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import java.util.Locale

object RootUtil {
    fun isDeviceRooted(context: Context): Boolean =
        (
            buildContainsTestTags() ||
                isSuperAppInstalled() ||
                canExecuteRootCommands() ||
                isRootManagementAppInstalled(context) ||
                checkEmulator()
        )

    val isProxyDetected: Boolean
        get() {
            val proxyAddress = System.getProperty("http.proxyHost")
            LogTool.d("ProxyAddress: ", proxyAddress)
            val portstr = System.getProperty("http.proxyPort")
            LogTool.d("portstr: ", portstr)
            var proxyPort = -1
            if (portstr != null) proxyPort = portstr.toInt()
            return !TextUtils.isEmpty(proxyAddress) && proxyPort != -1
        }

    private fun buildContainsTestTags(): Boolean {
        val buildTags = Build.TAGS
        return buildTags != null && buildTags.contains("test-keys")
    }

    private fun isSuperAppInstalled(): Boolean {
        val paths =
            arrayOf(
                "/system/app/Superuser.apk",
                "/sbin/su",
                "/system/bin/su",
                "/system/xbin/su",
                "/data/local/xbin/su",
                "/data/local/bin/su",
                "/system/sd/xbin/su",
                "/system/bin/failsafe/su",
                "/data/local/su",
                "/su/bin/su",
            )
        for (path in paths) {
            if (File(path).exists()) return true
        }
        return false
    }

    private fun canExecuteRootCommands(): Boolean {
        var process: Process? = null
        try {
            process = Runtime.getRuntime().exec(arrayOf("/system/xbin/which", "su"))
            val `in` = BufferedReader(InputStreamReader(process.inputStream))
            return `in`.readLine() != null
        } catch (t: Throwable) {
            return false
        } finally {
            process?.destroy()
        }
    }

    private fun isRootManagementAppInstalled(context: Context): Boolean {
        val packages =
            arrayOf(
                "eu.chainfire.supersu",
                "com.koushikdutta.superuser",
                "com.thirdparty.superuser",
                "com.noshufou.android.su",
                "com.zachspong.temprootremovejb",
                "com.ramdroid.appquarantine",
            )
        for (packageName in packages) {
            if (isPackageInstalled(packageName, context)) {
                return true
            }
        }
        return false
    }

    private fun isPackageInstalled(
        packageName: String,
        context: Context,
    ): Boolean {
        val pm = context.packageManager
        try {
            pm.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            return true
        } catch (e: PackageManager.NameNotFoundException) {
            return false
        }
    }

    @JvmStatic
    fun checkEmulator(): Boolean {
        var isEmulatorEnabled =
            (
                (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic")) ||
                    Build.FINGERPRINT.startsWith("google/sdk_gphone_") ||
                    Build.FINGERPRINT.startsWith("samsung/sdk_gphone_") ||
                    Build.FINGERPRINT.startsWith("OnePlus/sdk_gphone_") ||
                    Build.FINGERPRINT.startsWith("generic") ||
                    Build.FINGERPRINT.startsWith("unknown") ||
                    Build.HARDWARE.contains("goldfish") ||
                    Build.HARDWARE.contains("ranchu") ||
                    Build.MODEL.contains("google_sdk") ||
                    Build.MODEL.contains("Emulator") ||
                    Build.MODEL.contains("Android SDK built for x86") ||
                    Build.MANUFACTURER.contains("Genymotion") ||
                    Build.PRODUCT.contains("sdk_google") ||
                    Build.PRODUCT.contains("google_sdk") ||
                    Build.PRODUCT.contains("sdk") ||
                    Build.PRODUCT.contains("sdk_x86") ||
                    Build.PRODUCT.contains("vbox86p") ||
                    Build.PRODUCT.contains("emulator") ||
                    Build.PRODUCT.contains("simulator") ||
                    Build.MODEL.lowercase(Locale.getDefault()).contains("droid4x") ||
                    (Build.HARDWARE == "vbox86") ||
                    Build.HARDWARE
                        .lowercase(Locale.getDefault())
                        .contains("nox") ||
                    Build.BOARD.lowercase(Locale.getDefault()).contains("nox")
            )

        if (!isEmulatorEnabled) {
            isEmulatorEnabled = checkEmulatorFiles()
        }
        return isEmulatorEnabled
    }

    private val GENY_FILES =
        arrayOf(
            "/dev/socket/genyd",
            "/dev/socket/baseband_genyd",
        )

    private val PIPES =
        arrayOf(
            "/dev/socket/qemud",
            "/dev/qemu_pipe",
        )
    private val X86_FILES =
        arrayOf(
            "ueventd.android_x86.rc",
            "x86.prop",
            "ueventd.ttVM_x86.rc",
            "init.ttVM_x86.rc",
            "fstab.ttVM_x86",
            "fstab.vbox86",
            "init.vbox86.rc",
            "ueventd.vbox86.rc",
        )
    private val ANDY_FILES =
        arrayOf(
            "fstab.andy",
            "ueventd.andy.rc",
        )
    private val NOX_FILES =
        arrayOf(
            "fstab.nox",
            "init.nox.rc",
            "ueventd.nox.rc",
        )

    private fun checkFiles(targets: Array<String>): Boolean {
        for (pipe in targets) {
            val file = File(pipe)
            if (file.exists()) {
                return true
            }
        }
        return false
    }

    private fun checkEmulatorFiles(): Boolean =
        (
            checkFiles(GENY_FILES) ||
                checkFiles(ANDY_FILES) ||
                checkFiles(NOX_FILES) ||
                checkFiles(X86_FILES) ||
                checkFiles(PIPES)
        )
}
