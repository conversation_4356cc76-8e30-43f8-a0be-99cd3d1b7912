package com.toyota.oneapp.util

import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.Locale
import kotlin.math.absoluteValue
import kotlin.math.roundToInt

object DoubleUtil {
    /**
     * Extension function - Used to convert amount in double to display amount (example 4.30 => $ 4.30, 4 => $ 4.00).
     */
    fun Double.toDisplayPrice(locale: Locale): String {
        val format = NumberFormat.getCurrencyInstance(locale)
        if (format is DecimalFormat) {
            val dfs = format.decimalFormatSymbols
            dfs.currency = format.currency
            dfs.currencySymbol = CurrencyUtils.getCurrencySymbol(dfs.currency.currencyCode) + " "
            format.decimalFormatSymbols = dfs
        }
        return format.format(this)
    }

    fun Double.roundOffTo(decimalPlaces: Int): Double = this.toBigDecimal().setScale(decimalPlaces, RoundingMode.UP).toDouble()

    fun Double.abs(): Int = this.roundToInt().absoluteValue

    fun Double.isNotPositive(): Boolean = this <= 0

    fun Double.stripTrailingZeros(): Number =
        if (this % 1.0 == 0.0) {
            this.toInt()
        } else {
            this
        }
}
