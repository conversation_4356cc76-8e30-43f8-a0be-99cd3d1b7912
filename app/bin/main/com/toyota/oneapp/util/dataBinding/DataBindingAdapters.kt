package com.toyota.oneapp.util.dataBinding

import android.annotation.SuppressLint
import android.content.res.Resources
import android.graphics.drawable.Drawable
import android.text.Html
import android.text.method.LinkMovementMethod
import android.view.View
import android.webkit.WebSettings.LOAD_NO_CACHE
import android.widget.Button
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import androidx.core.view.isGone
import androidx.core.view.isInvisible
import androidx.databinding.BindingAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.button.MaterialButton
import com.google.android.material.switchmaterial.SwitchMaterial
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.toyota.oneapp.R
import com.toyota.oneapp.customviews.FlexibleWebView
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.NameRegexInputFilter
import com.toyota.oneapp.util.PartialRegexInputFilter
import java.text.NumberFormat
import java.util.*

object DataBindingAdapters {
    @BindingAdapter("isVisible")
    @JvmStatic
    fun setIsVisible(
        view: View,
        isVisible: Boolean = false,
    ) {
        view.visibility = if (isVisible) View.VISIBLE else View.GONE
    }

    @BindingAdapter("isInvisible")
    @JvmStatic
    fun setIsInvisible(
        view: View,
        isInvisible: Boolean,
    ) {
        view.isInvisible = isInvisible
    }

    @BindingAdapter("setConstrainId")
    @JvmStatic
    fun setConstrainId(
        view: View,
        vehicleType: Boolean,
    ) {
        val layoutParams: ConstraintLayout.LayoutParams = view.layoutParams as ConstraintLayout.LayoutParams
        if (vehicleType) {
            layoutParams.topToBottom = R.id.mm21_remove_vehicle_content
        } else {
            layoutParams.topToBottom = R.id.remove_vehicle_address
        }
        view.layoutParams = layoutParams
    }

    @BindingAdapter("isGone")
    @JvmStatic
    fun setIsGone(
        view: View,
        isGone: Boolean,
    ) {
        view.isGone = isGone
    }

    @BindingAdapter("imageResource")
    @JvmStatic
    fun setImageResource(
        textView: TextView,
        resourceId: Int,
    ) {
        textView.setCompoundDrawablesWithIntrinsicBounds(resourceId, 0, 0, 0)
    }

    @BindingAdapter("loadHTMLData", requireAll = false)
    @JvmStatic
    fun loadHTMLData(
        webView: FlexibleWebView,
        htmlData: String,
    ) {
        webView.settings.cacheMode = LOAD_NO_CACHE
        webView.loadDataWithBaseURL(
            "",
            htmlData,
            "text/html",
            "UTF-8",
            "",
        )
    }

    @BindingAdapter("htmlText", requireAll = false)
    @JvmStatic
    fun htmlText(
        textView: TextView,
        htmlData: String?,
    ) {
        htmlData?.let {
            textView.text = Html.fromHtml(it)
        }
    }

    @BindingAdapter("textResourceId")
    @JvmStatic
    fun setResourceId(
        textView: TextView,
        resourceId: Int,
    ) {
        try {
            textView.setText(resourceId)
        } catch (ex: Resources.NotFoundException) {
            textView.text = "" // Sets the value to empty string if the resource was not found.
        }
    }

    @BindingAdapter("textResourceId")
    @JvmStatic
    fun setResourceId(
        button: Button,
        resourceId: Int,
    ) {
        try {
            button.setText(resourceId)
        } catch (ex: Resources.NotFoundException) {
            button.text = "" // Sets the value to empty string if the resource was not found.
        }
    }

    @BindingAdapter("resourceId")
    @JvmStatic
    fun setResourceId(
        imageView: ImageView,
        resourceId: Int,
    ) {
        imageView.setImageResource(resourceId)
    }

    @BindingAdapter(value = ["adapterData", "adapterInitialSelection"], requireAll = false)
    @JvmStatic
    fun <T> setRecyclerViewAdapterData(
        recyclerView: RecyclerView,
        data: List<T>?,
        initialSelection: Collection<T>?,
    ) {
        if (recyclerView.adapter is BindableRecyclerViewAdapter<*>) {
            @Suppress("UNCHECKED_CAST")
            (recyclerView.adapter as BindableRecyclerViewAdapter<T>).apply {
                setData(data)
                setInitialSelection(initialSelection)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @BindingAdapter("adapterData")
    @JvmStatic
    fun <T> setViewPagerAdapterData(
        viewPager: ViewPager2,
        data: List<T>,
    ) {
        if (viewPager.adapter is BindableAdapter<*>) {
            @Suppress("UNCHECKED_CAST")
            (viewPager.adapter as BindableAdapter<T>).apply {
                setData(data)
                viewPager.adapter!!.notifyDataSetChanged()
            }
        }
    }

    @BindingAdapter("adapter")
    @JvmStatic
    fun setViewPagerAdapter(
        viewPager: ViewPager2,
        adapter: RecyclerView.Adapter<*>,
    ) {
        viewPager.adapter = adapter
    }

    @BindingAdapter("adapter")
    @JvmStatic
    fun setAdapter(
        view: RecyclerView,
        adapter: RecyclerView.Adapter<*>,
    ) {
        view.adapter = adapter
    }

    @BindingAdapter("onPageChanged")
    @JvmStatic
    fun registerOnPageChangedListener(
        viewPager: ViewPager2,
        listener: (ViewPager2, Int) -> Unit,
    ) {
        viewPager.registerOnPageChangeCallback(
            object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    listener(viewPager, position)
                }
            },
        )
    }

    @BindingAdapter("offScreenPageLimit")
    @JvmStatic
    fun setOffScreenPageLimit(
        viewPager: ViewPager2,
        limit: Int,
    ) {
        viewPager.offscreenPageLimit = limit
    }

    @BindingAdapter("endDrawableResourceId")
    @JvmStatic
    fun setEndDrawableResourceId(
        editText: TextInputEditText,
        resourceId: Int,
    ) {
        editText.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, resourceId, 0)
    }

    @BindingAdapter("materialButtonIconTint")
    @JvmStatic
    fun setMaterialButtonIconTint(
        button: MaterialButton,
        resourceId: Int,
    ) {
        button.setIconTintResource(resourceId)
    }

    @BindingAdapter("app:errorText")
    @JvmStatic
    fun setErrorMessage(
        view: TextInputLayout,
        errorMessage: String?,
    ) {
        view.error = if (errorMessage != "") errorMessage else null
    }

    @BindingAdapter("errorRes")
    @JvmStatic
    fun setErrorRes(
        textInputLayout: TextInputLayout,
        @StringRes resId: Int?,
    ) {
        textInputLayout.error =
            if (resId == null || resId == 0) {
                null
            } else {
                textInputLayout.context.getString(resId)
            }
    }

    @BindingAdapter("month")
    @JvmStatic
    fun setMonth(
        textView: TextView,
        date: Date?,
    ) {
        date?.let { d ->
            getCalendar(d).get(Calendar.MONTH).let { month ->
                textView.text =
                    textView.context.resources.getStringArray(R.array.simple_months)[month]
            }
        } ?: run {
            textView.text = textView.context.resources.getString(R.string.Common_Dashes)
        }
    }

    @BindingAdapter("dayOfMonth")
    @JvmStatic
    fun setDayOfMonth(
        textView: TextView,
        date: Date?,
    ) {
        date?.let { d ->
            getCalendar(d).get(Calendar.DAY_OF_MONTH).let { dayOfMonth ->
                textView.text = "$dayOfMonth"
            }
        } ?: run {
            textView.text = textView.context.resources.getString(R.string.Common_Dashes)
        }
    }

    @BindingAdapter("year")
    @JvmStatic
    fun setYear(
        textView: TextView,
        date: Date?,
    ) {
        date?.let { d ->
            getCalendar(d).get(Calendar.YEAR).let { year ->
                textView.text = "$year"
            }
        } ?: run {
            textView.text = textView.context.resources.getString(R.string.Common_Dashes)
        }
    }

    private fun getCalendar(date: Date) = Calendar.getInstance().apply { time = date }

    @BindingAdapter("mileage", "unit")
    @JvmStatic
    fun setOdometer(
        textView: TextView,
        mileageString: String?,
        unit: String?,
    ) {
        val mileage = mileageString?.toLongOrNull()
        textView.text =
            if (mileage != null && unit != null) {
                String.format(
                    "%s %s",
                    NumberFormat
                        .getInstance()
                        .apply {
                            isGroupingUsed = true
                        }.format(mileage),
                    unit,
                )
            } else {
                textView.context.getString(R.string.Common_Dashes)
            }
    }

    @BindingAdapter("nameRegex")
    @JvmStatic
    fun setNameRegex(
        textView: TextView,
        regex: String,
    ) {
        val filter = NameRegexInputFilter(textView.text.toString(), regex)
        textView.filters = arrayOf(filter)
    }

    @BindingAdapter("regex")
    @JvmStatic
    fun setRegexFilter(
        textView: TextView,
        regex: String,
    ) {
        val filters = textView.filters.toMutableList()
        filters.removeIf { it is PartialRegexInputFilter }
        filters += PartialRegexInputFilter(regex)
        textView.filters = filters.toTypedArray()
    }

    @BindingAdapter("imageUrl", "placeHolder", requireAll = false)
    @JvmStatic
    fun loadImage(
        imageView: ImageView,
        url: String?,
        placeHolder: Drawable? = null,
    ) {
        if (url != null) {
            GlideUtil.loadImage(
                imageView.context,
                url,
                GlideUtil.CropTransparentTransform(),
                placeHolder,
                imageView,
            )
        }
    }

    @BindingAdapter("otaImageUrl")
    @JvmStatic
    fun loadOtaContentImage(
        imageView: ImageView,
        url: String?,
    ) {
        if (url != null) {
            GlideUtil.loadImage(
                imageView.context,
                url,
                GlideUtil.CropTransparentTransform(),
                R.drawable.image_not_found,
                imageView,
            )
        }
    }

    @BindingAdapter("vehicle")
    @JvmStatic
    fun setVehicle(
        imageView: ImageView,
        vehicle: VehicleInfo?,
    ) {
        GlideUtil.loadImage(
            imageView.context,
            vehicle?.image,
            R.drawable.image_not_found,
            imageView,
        )
    }

    @BindingAdapter("preffered")
    @JvmStatic
    fun isPreffered(
        switchMaterial: SwitchMaterial,
        preffered: Int,
    ) {
        switchMaterial.isChecked = preffered == 1
    }

    @BindingAdapter("expirationMonth", "expirationYear")
    @JvmStatic
    fun setExpiration(
        textView: TextView,
        month: Int,
        year: Int,
    ) {
        textView.text = String.format("%02d/%d", month, year)
    }

    @BindingAdapter("hideShowView")
    @JvmStatic
    fun hideShowView(
        view: View,
        visibility: Boolean,
    ) {
        if (visibility) view.visibility = View.VISIBLE else view.visibility = View.INVISIBLE
    }

    @BindingAdapter("goneVisible")
    @JvmStatic
    fun goneVisible(
        view: View,
        visibility: Boolean,
    ) {
        if (visibility) view.visibility = View.VISIBLE else view.visibility = View.GONE
    }

    @BindingAdapter("setBackground")
    @JvmStatic
    fun setBackground(
        view: View,
        resId: Int,
    ) {
        view.setBackgroundResource(resId)
    }

    @BindingAdapter("formatStringId", "dataString")
    @JvmStatic
    fun setFormattedString(
        textView: TextView,
        @StringRes formatStringId: Int?,
        formattingData: String?,
    ) {
        textView.text =
            if (formatStringId != null && formatStringId != 0) {
                textView.context.getString(formatStringId, formattingData ?: "")
            } else {
                formattingData
            }
    }

    @BindingAdapter("formatStringId", "dataInt")
    @JvmStatic
    fun setFormattedIntString(
        textView: TextView,
        @StringRes formatStringId: Int?,
        formattingData: Int?,
    ) {
        textView.text =
            if (formatStringId != null && formatStringId != 0) {
                textView.context.getString(formatStringId, formattingData ?: 0)
            } else {
                ""
            }
    }

    @BindingAdapter("colorFilterId")
    @JvmStatic
    fun setColorFilterId(
        imageView: ImageView,
        @ColorRes colorFilterId: Int,
    ) {
        imageView.setColorFilter(colorFilterId)
    }

    @BindingAdapter("isSelected")
    @JvmStatic
    fun isSelected(
        imageButton: ImageButton,
        selected: Boolean,
    ) {
        imageButton.isSelected = selected
    }

    @BindingAdapter("tint")
    @JvmStatic
    fun setTint(
        imageView: ImageView,
        @ColorRes color: Int,
    ) {
        DrawableCompat.setTint(imageView.drawable, ContextCompat.getColor(imageView.context, color))
    }

    @BindingAdapter("movementMethod")
    @JvmStatic
    fun setMovementMethod(
        textView: TextView,
        movementMethod: LinkMovementMethod,
    ) {
        textView.movementMethod = movementMethod
    }
} // DataBindingAdapters object

interface BindableAdapter<T> {
    fun setData(data: List<T>?)
}
