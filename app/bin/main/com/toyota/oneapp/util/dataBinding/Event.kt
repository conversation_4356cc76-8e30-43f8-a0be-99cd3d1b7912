package com.toyota.oneapp.util.dataBinding

open class Event<out T>(
    private val content: T,
) {
    var hasBeenHandled = false
        private set

    fun processEventIfNotHandled(): T? =
        if (hasBeenHandled) {
            null
        } else {
            hasBeenHandled = true
            content
        }

    fun peekContent(): T = content

    class Observer<T>(
        private val onEventUnhandledContent: (T) -> Unit,
    ) : androidx.lifecycle.Observer<Event<T>> {
        override fun onChanged(event: Event<T>) {
            event.processEventIfNotHandled()?.let { onEventUnhandledContent(it) }
        }
    } // Observer class
} // Event open class
