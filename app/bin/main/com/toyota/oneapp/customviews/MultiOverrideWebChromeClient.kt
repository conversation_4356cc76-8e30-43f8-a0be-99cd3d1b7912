package com.toyota.oneapp.customviews

import android.app.Activity
import android.content.Context
import android.webkit.ConsoleMessage
import android.webkit.JsResult
import android.webkit.WebChromeClient
import android.webkit.WebView
import toyotaone.commonlib.log.LogTool

class MultiOverrideWebChromeClient internal constructor(
    private val context: Context,
) : WebChromeClient() {
    override fun onJsAlert(
        view: WebView?,
        url: String?,
        message: String?,
        result: JsResult?,
    ): Boolean = super.onJsAlert(view, url, message, result)

    override fun onConsoleMessage(consoleMessage: ConsoleMessage): Boolean {
        LogTool.d("MultiOverrideWebChromeClient", consoleMessage.message())
        if ("User Need to Retry" == consoleMessage.message()) {
            (context as Activity).finish()
        }
        return true
    }
}
