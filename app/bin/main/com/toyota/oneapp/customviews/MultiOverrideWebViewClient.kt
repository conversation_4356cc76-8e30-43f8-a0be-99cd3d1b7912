package com.toyota.oneapp.customviews

import android.graphics.Bitmap
import android.os.Message
import android.webkit.*
import android.webkit.MimeTypeMap.getFileExtensionFromUrl
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.logging.HttpLoggingInterceptor
import toyotaone.commonlib.log.LogTool
import java.io.InputStream

open class MultiOverrideWebViewClient : WebViewClient() {
    val onPageFinishedOverrides = mutableListOf<(view: WebView?, url: String?) -> Unit>()

    lateinit var paymentHeaders: Map<String, String>

    lateinit var paymentURL: String

    lateinit var paymentHost: String

    lateinit var paymentBody: String

    var isExecute = false

    override fun onPageFinished(
        view: WebView?,
        url: String?,
    ) {
        super.onPageFinished(view, url)
        LogTool.e("URL", "---onPageFinished: URL = $url")
        isExecute = false
        onPageFinishedOverrides.forEach { it(view, url) }
    }

    override fun onPageStarted(
        view: WebView?,
        url: String?,
        favicon: Bitmap?,
    ) {
        LogTool.e("URL", "---onPageStarted: URL = $url")
        super.onPageStarted(view, url, favicon)
    }

    override fun shouldInterceptRequest(
        view: WebView?,
        request: WebResourceRequest?,
    ): WebResourceResponse? {
        LogTool.e(
            "URL",
            "---shouldInterceptRequest: URL = ${request?.url} + isRedirect: ${request?.isRedirect}",
        )

        return super.shouldInterceptRequest(view, request)
    }

    override fun onReceivedError(
        view: WebView?,
        request: WebResourceRequest?,
        error: WebResourceError?,
    ) {
        LogTool.e("URL", "---onReceivedError: URL = ${request?.url} + Error: ${error?.description}")
        super.onReceivedError(view, request, error)
    }

    override fun shouldOverrideUrlLoading(
        view: WebView?,
        url: String?,
    ): Boolean {
        LogTool.e("URL", "---shouldOverrideUrlLoading deprecated: URL = $url")
        return false
    }

    override fun shouldOverrideUrlLoading(
        view: WebView?,
        request: WebResourceRequest?,
    ): Boolean {
        LogTool.e("URL", "---shouldOverrideUrlLoading: URL = ${request?.url}")
        return super.shouldOverrideUrlLoading(view, request)
    }

    override fun onLoadResource(
        view: WebView?,
        url: String?,
    ) {
        LogTool.e("URL", "---onLoadResource: URL = $url")
        super.onLoadResource(view, url)
    }

    override fun onFormResubmission(
        view: WebView?,
        dontResend: Message?,
        resend: Message?,
    ) {
        LogTool.e(
            "URL",
            "---onFormResubmission: dontResend = ${dontResend?.obj} + resend = ${resend?.obj}",
        )
        super.onFormResubmission(view, dontResend, resend)
    }

    override fun onReceivedHttpAuthRequest(
        view: WebView?,
        handler: HttpAuthHandler?,
        host: String?,
        realm: String?,
    ) {
        LogTool.e("URL", "---onReceivedHttpAuthRequest host: $host ---- realm: $realm")
        super.onReceivedHttpAuthRequest(view, handler, host, realm)
    }

    override fun onReceivedLoginRequest(
        view: WebView?,
        realm: String?,
        account: String?,
        args: String?,
    ) {
        LogTool.e(
            "URL",
            "---onReceivedLoginRequest account: $account ---- realm: $realm ---- args: $args",
        )
        super.onReceivedLoginRequest(view, realm, account, args)
    }

    override fun onReceivedHttpError(
        view: WebView?,
        request: WebResourceRequest?,
        errorResponse: WebResourceResponse?,
    ) {
        LogTool.e(
            "URL",
            "---onReceivedHttpError error: ${request!!.url} + errorResponse: ${errorResponse?.statusCode}",
        )
        super.onReceivedHttpError(view, request, errorResponse)
    }

    private fun getNewResponse(
        view: WebView?,
        url: String,
        method: String?,
        contentType: String?,
    ): WebResourceResponse? {
        try {
            val interceptor = HttpLoggingInterceptor()
            interceptor.level = HttpLoggingInterceptor.Level.BODY
            val httpClient = OkHttpClient.Builder().addInterceptor(interceptor).build()
            val request: Request.Builder
            val isPaymentURL: Boolean
            if ("GET".equals(method, true)) {
                val finalURL: String =
                    if (url.contains("favicon.ico", true)) {
                        "https://sta.esp.telematics.net/payment-app/favicon.ico"
                    } else {
                        url
                    }
                request =
                    Request
                        .Builder()
                        .get()
                        .url(finalURL.trim { it <= ' ' })
            } else {
                val requestBody: RequestBody =
                    if (url == paymentURL) {
                        if (!isExecute) {
                            isExecute = true
                            isPaymentURL = true
                            paymentBody.toRequestBody("application/json".toMediaTypeOrNull())
                        } else {
                            return null
                        }
                    } else {
                        isPaymentURL = false
                        ByteArray(0).toRequestBody(
                            (contentType ?: "application/json").toMediaTypeOrNull(),
                        )
                    }
                request =
                    Request
                        .Builder()
                        .post(requestBody)
                        .url(url.trim { it <= ' ' })
                if (isPaymentURL) {
                    paymentHeaders.forEach {
                        request.addHeader(it.key, it.value)
                    }
                }
            }

            val response = httpClient.newCall(request.build()).execute()

            val inputStream = response.body!!.byteStream()

            return WebResourceResponse(
                getMimeType(url),
                "UTF-8",
                inputStream,
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    private fun getMimeType(url: String): String {
        var type: String? = null
        val extension = getFileExtensionFromUrl(url)

        if (extension != null) {
            type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
        }
        if (type == null) {
            type = "text/html"
        }
        return type
    }

    private fun parseInputStream(inputStream: InputStream): String {
        val bytes = ByteArray(inputStream.available())
        inputStream.read(bytes)
        return String(bytes)
    }
}
