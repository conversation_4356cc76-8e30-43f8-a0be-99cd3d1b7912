package com.toyota.oneapp.customviews

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.TargetApi
import android.content.Context
import android.graphics.drawable.Drawable
import android.graphics.drawable.LayerDrawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.MotionEvent
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.math.MathUtils
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import toyotaone.commonlib.log.LogTool

class SwipeLayout : RelativeLayout {
    private lateinit var slidingButton: RelativeLayout
    private var initialX = 0f
    private var active = false
    private var initialButtonWidth = 0
    private var centerText: TextView? = null
    private var enabledDrawable: Drawable? = null
    private var background: RelativeLayout? = null
    private var swipeImage: ImageView? = null

    private var onSwipeCompleteListener: OnSwipeCompleteListener? = null

    constructor(context: Context) : super(context) {
        init(context, null, -1, -1)
    }

    constructor(
        context: Context,
        attrs: AttributeSet?,
    ) : super(context, attrs) {
        init(context, attrs, -1, -1)
    }

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
    ) : super(context, attrs, defStyleAttr) {
        init(context, attrs, defStyleAttr, -1)
    }

    @TargetApi(21)
    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int,
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        init(context, attrs, defStyleAttr, defStyleRes)
    }

    private fun init(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int,
    ) {
        // Don't worry about this method right now... I'm going to talk about this latter.
        background = RelativeLayout(context)
        val layoutParamsView =
            LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
            )
        layoutParamsView.addRule(
            CENTER_IN_PARENT,
            TRUE,
        )
        addView(background, layoutParamsView)
        val centerText: TextView = RainbowTextView(context)
        this.centerText = centerText
        centerText.gravity = Gravity.CENTER
        val layoutParams =
            LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
            )
        layoutParams.addRule(CENTER_IN_PARENT, TRUE)
        background?.setPadding(
            context.resources.getDimension(R.dimen.swipe_layout_padding).toInt(),
            context.resources.getDimension(R.dimen.swipe_layout_padding).toInt(),
            context.resources.getDimension(R.dimen.swipe_layout_padding).toInt(),
            context.resources.getDimension(R.dimen.swipe_layout_padding).toInt(),
        )
        background?.addView(centerText, layoutParams)
        swipeImage = ImageView(context)
        slidingButton = RelativeLayout(context)
        val img =
            LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,
            )
        img.height = convertDpToPx(context, 25f)
        img.width = convertDpToPx(context, 25f)
        img.addRule(CENTER_IN_PARENT, TRUE)
        slidingButton.addView(swipeImage, img)
        val layoutParamsButton =
            LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
            )
        layoutParamsButton.addRule(ALIGN_PARENT_LEFT, TRUE)
        layoutParamsButton.addRule(CENTER_VERTICAL, TRUE)
        slidingButton.background = ContextCompat.getDrawable(context, R.drawable.shape_button)
        enabledDrawable = ContextCompat.getDrawable(getContext(), R.drawable.ic_tfs_arrow_right)
        swipeImage?.setImageDrawable(enabledDrawable)
        addView(slidingButton, layoutParamsButton)
        setOnTouchListener(getButtonTouchListener(context))
        changeDrawable(0f)
    }

    // Public API
    fun setText(text: CharSequence) {
        centerText?.text = text
        centerText?.isAllCaps = !BuildConfig.IS_TOYOTA_APP
        (centerText as RainbowTextView?)?.changeColor(0f)
    }

    fun setOnSwipeCompleteListener(onSwipeCompleteListener: OnSwipeCompleteListener) {
        this.onSwipeCompleteListener = onSwipeCompleteListener
    }

    private fun getButtonTouchListener(context: Context): OnTouchListener {
        return OnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    changeDrawable(0f)
                    moveButtonBack()
                    return@OnTouchListener true
                }
                MotionEvent.ACTION_MOVE -> {
                    if (initialX == 0f) {
                        initialX = slidingButton.x
                    }
                    val dist = event.x
                    val wid = width.toFloat()
                    val dist2 = dist / wid
                    (centerText as RainbowTextView?)!!.changeColor(dist2)
                    changeDrawable(dist)
                    if (event.x > initialX + slidingButton.width / 2 &&
                        event.x + slidingButton.width / 2 < width
                    ) {
                        slidingButton.x = event.x - slidingButton.width / 2
                        LogTool.v("SwipeToPay", "Action Move 1:" + slidingButton.x)
                        changeDrawable(event.x)
                    }
                    if (event.x + slidingButton.width / 2 > width &&
                        slidingButton.x + slidingButton.width / 2 < width
                    ) {
                        slidingButton.x = width - slidingButton.width.toFloat()
                        LogTool.v("SwipeToPay", "Action Move 2:" + slidingButton.x)
                    }
                    if (event.x < slidingButton.width / 2 &&
                        slidingButton.x > 0
                    ) {
                        slidingButton.x = 0f
                        LogTool.v("SwipeToPay", "Action Move 3:" + slidingButton.x)
                    }
                    return@OnTouchListener true
                }
                MotionEvent.ACTION_UP -> {
                    LogTool.v("SwipeToPay", "Action Up:" + slidingButton.x)
                    if (slidingButton.x == 0f) {
                        (centerText as RainbowTextView?)?.changeColor(0f)
                        return@OnTouchListener true
                    }
                    if (active) {
                        collapseButton()
                    } else {
                        initialButtonWidth = slidingButton.width
                        if (slidingButton.x + slidingButton.width > width * 0.95) {
                            val rotationAnimation =
                                AnimationUtils.loadAnimation(
                                    context,
                                    R.anim.rotating_spinner,
                                )
                            swipeImage?.setImageResource(R.drawable.tfs_progress_bar)
                            swipeImage?.startAnimation(rotationAnimation)
                            centerText?.setText(R.string.FinancialServices_processing)
                            changeDrawable(width + (slidingButton.width / 2).toFloat())
                            (centerText as RainbowTextView?)!!.changeColor(1f)
                            onSwipeCompleteListener?.onSwipeComplete(this)
                        } else {
                            (centerText as RainbowTextView?)?.changeColor(0f)
                            changeDrawable(0f)
                            moveButtonBack()
                        }
                    }
                    return@OnTouchListener true
                }
                MotionEvent.ACTION_CANCEL -> {
                    LogTool.v("SwipeToPay", "Action Cancelled:" + slidingButton.x)
                    (centerText as RainbowTextView?)?.changeColor(0f)
                    changeDrawable(0f)
                    moveButtonBack()
                    return@OnTouchListener true
                }
            }
            false
        }
    }

    private fun collapseButton() {
        val widthAnimator = ValueAnimator.ofInt(slidingButton.width, initialButtonWidth)
        widthAnimator.addUpdateListener {
            val params = slidingButton.layoutParams
            params?.width = (widthAnimator.animatedValue as Int)
            slidingButton.layoutParams = params
        }
        widthAnimator.addListener(
            object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    active = false
                }
            },
        )
        centerText?.let {
            val objectAnimator = ObjectAnimator.ofFloat(it, "alpha", 1f)
            val animatorSet = AnimatorSet()
            animatorSet.playTogether(objectAnimator, widthAnimator)
            animatorSet.start()
        }
    }

    fun moveButtonBack() {
        val positionAnimator = ValueAnimator.ofFloat(slidingButton.x, 0f)
        positionAnimator.interpolator = AccelerateDecelerateInterpolator()
        positionAnimator.addUpdateListener {
            val x = positionAnimator.animatedValue as Float
            slidingButton.x = x
        }
        centerText?.let {
            val objectAnimator = ObjectAnimator.ofFloat(it, "alpha", 1f)
            positionAnimator.duration = 200
            val animatorSet = AnimatorSet()
            animatorSet.playTogether(objectAnimator, positionAnimator)
            animatorSet.start()
        }
        swipeImage?.animation?.cancel()
        swipeImage?.setImageDrawable(enabledDrawable)
        changeDrawable(0f)
    }

    override fun onSizeChanged(
        w: Int,
        h: Int,
        oldw: Int,
        oldh: Int,
    ) {
        super.onSizeChanged(w, h, oldw, oldh)
        changeDrawable(0f)
    }

    fun changeDrawable(`val`: Float) {
        val gray = resources.getDrawable(R.drawable.shape_rounded)
        val swipe = resources.getDrawable(R.drawable.shape_rounded_swipe)
        val layers = arrayOf(gray, swipe)
        val layerDrawable = LayerDrawable(layers)
        layerDrawable.setLayerInset(0, 0, 0, 0, 0)
        var cutoff = width - (`val`.toInt() + slidingButton.width / 2)
        cutoff = MathUtils.clamp(cutoff, 0, width - slidingButton.width)
        layerDrawable.setLayerInset(1, 0, 0, cutoff, 0)
        background?.background = layerDrawable
        invalidate()
    }

    private fun convertDpToPx(
        context: Context,
        dp: Float,
    ): Int = (dp * context.resources.displayMetrics.density).toInt()

    fun setCenterText(amount: Double) {
        centerText?.text = context.getString(R.string.FinancialServices_swipe_to_pay, amount)
        (centerText as RainbowTextView?)?.changeColor(0f)
    }

    interface OnSwipeCompleteListener {
        fun onSwipeComplete(view: SwipeLayout)
    }
}
