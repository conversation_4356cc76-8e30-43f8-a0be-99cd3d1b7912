package com.toyota.oneapp.customviews

import android.content.Context
import android.util.AttributeSet
import android.webkit.WebView
import android.webkit.WebViewClient
import com.toyota.oneapp.BuildConfig

class FlexibleWebView(
    context: Context,
    attributeSet: AttributeSet,
) : WebView(context, attributeSet) {
    val client = MultiOverrideWebViewClient()
    private val chromeCleint = MultiOverrideWebChromeClient(context)

    init {
        webViewClient = client
        webChromeClient = chromeCleint
        setWebContentsDebuggingEnabled(BuildConfig.DEBUG)
    }

    @Deprecated(
        "This already has a specialized web view client",
        ReplaceWith("super.setWebViewClient(client)", "android.webkit.WebView"),
    )
    override fun setWebViewClient(client: WebViewClient) {
        super.setWebViewClient(client)
    }
}
