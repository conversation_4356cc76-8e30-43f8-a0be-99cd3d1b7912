package com.toyota.oneapp.apptentive

import apptentive.com.android.feedback.Apptentive
import apptentive.com.android.feedback.Apptentive.register
import apptentive.com.android.feedback.ApptentiveConfiguration
import apptentive.com.android.util.LogLevel
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.app.ToyotaApplication
import com.toyota.oneapp.util.Brand
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ApptentiveServiceImpl
    @Inject
    constructor() : ApptentiveService {
        override fun register(application: ToyotaApplication) {
            val configuration =
                when (BuildConfig.APP_BRAND) {
                    Brand.TOYOTA.appBrand ->
                        ApptentiveConfiguration(
                            BuildConfig.APPTENTIVE_TOYOTA_KEY,
                            BuildConfig.APPTENTIVE_TOYOTA_SIGNATURE,
                        )
                    Brand.LEXUS.appBrand ->
                        ApptentiveConfiguration(
                            BuildConfig.APPTENTIVE_LEXUS_KEY,
                            BuildConfig.APPTENTIVE_LEXUS_SIGNATURE,
                        )
                    else ->
                        ApptentiveConfiguration(
                            BuildConfig.APPTENTIVE_TOYOTA_KEY,
                            BuildConfig.APPTENTIVE_TOYOTA_SIGNATURE,
                        )
                }

            if (BuildConfig.DEBUG) {
                configuration.logLevel = LogLevel.Verbose
            }
            register(application, configuration)
        }

        override fun engageEvent(eventName: String) {
            Apptentive.engage(eventName)
        }
    }
