package com.toyota.oneapp.core

import android.app.Application
import android.content.Context
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module
abstract class AbstractCoreModule {
    @ApplicationContext
    @Binds
    abstract fun bindApplicationContext(application: Application): Context
}
