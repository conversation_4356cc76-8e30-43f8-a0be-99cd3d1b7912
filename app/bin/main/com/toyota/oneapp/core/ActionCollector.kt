/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.core

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest

/**
 * A reusable composable that collects emissions from a [Flow] and triggers the provided [emit] lambda for each new value.
 *
 * This is useful for performing side effects in Compose when values are emitted from a `Flow`,
 * such as showing a snackbar, navigating to another screen, or dispatching UI actions from a view model to the composable.
 *
 * The collection is launched using a [LaunchedEffect], ensuring that it only restarts if the key changes.
 * In this case, the key is constant (`Unit`), so the collector is launched once when the composition enters the composition tree.
 *
 * Example usage:
 * ```kotlin
 * ActionCollector(viewModel.uiAction) { uiAction ->
 *     when (uiAction) {
 *         is ShowSnackbar -> scaffoldState.snackbarHostState.showSnackbar(uiAction.message)
 *     }
 * }
 * ```
 *
 * @param T The type of values emitted by the [flow].
 * @param flow The [Flow] to collect from.
 * @param emit A suspend function that will be called with each latest value emitted by the [flow].
 *
 */
@Composable
fun <T> ActionCollector(
    flow: Flow<T>,
    emit: suspend (T) -> Unit,
) {
    LaunchedEffect(key1 = Unit) {
        flow.collectLatest {
            emit(it)
        }
    }
}
