package com.toyota.oneapp.core

import android.app.Application
import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.content.res.AssetManager
import android.content.res.Resources
import android.location.Geocoder
import android.os.Build
import android.os.Looper
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.gms.common.util.concurrent.HandlerExecutor
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.component.QRScanHelper
import com.toyota.oneapp.component.VprsSyncHelper
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.ErrorMessageParserImpl
import com.toyota.oneapp.network.api.manager.VehicleAPIManager
import com.toyota.oneapp.network.api.repository.IdpRepository
import com.toyota.oneapp.network.api.repository.VehiclePendingRegistrationRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import toyotaone.commonlib.coroutine.DefaultDispatcherProvider
import toyotaone.commonlib.coroutine.DispatcherProvider
import toyotaone.commonlib.location.RealUserLocationProvider
import toyotaone.commonlib.location.UserLocationProvider
import java.util.Locale
import java.util.concurrent.Executor
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.CoroutineContext

@InstallIn(SingletonComponent::class)
@Module(includes = [AbstractCoreModule::class])
class CoreModule {
    @Provides
    fun provideAssetManager(application: Application): AssetManager = application.assets

    @MainThreadExecutor
    @Provides
    fun provideMainThreadExecutor(application: Application): Executor =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            application.mainExecutor
        } else {
            HandlerExecutor(Looper.getMainLooper())
        }

    @IoThreadExecutor
    @Provides
    fun provideIoThreadExecutor(): ScheduledExecutorService = Executors.newScheduledThreadPool(3)

    @Provides
    fun provideCoroutineContext(): CoroutineContext = Dispatchers.Default

    @MainCoroutineDispatcher
    @Provides
    fun provideCoroutineMainDispatcher(): CoroutineDispatcher = Dispatchers.Main

    @Provides
    @Inject
    fun provideApplicationData(oneAppPreferenceModel: OneAppPreferenceModel): ApplicationData =
        ApplicationData.getInstance(
            oneAppPreferenceModel,
        )

    @Provides
    fun provideGeocoder(
        @ApplicationContext context: Context,
    ): Geocoder =
        Geocoder(
            context,
            Locale.getDefault(),
        )

    @Provides
    fun provideLocalBroadcastManager(
        @ApplicationContext context: Context,
    ): LocalBroadcastManager = LocalBroadcastManager.getInstance(context)

    @Provides
    fun provideDispatcherProvider(): DispatcherProvider = DefaultDispatcherProvider()

    @Provides
    fun provideBuildWrapper(): BuildWrapper = BuildWrapper()

    @Provides
    fun provideErrorMessageParser(
        @ApplicationContext context: Context,
    ): ErrorMessageParser =
        ErrorMessageParserImpl(
            context,
        )

    @Provides
    fun provideResources(application: Application): Resources = application.resources

    @Provides
    fun provideUserLocationProvider(
        @ApplicationContext context: Context,
    ): UserLocationProvider = RealUserLocationProvider(context)

    @Provides
    @Inject
    fun provideVprsSyncHelper(
        vprsRepository: VehiclePendingRegistrationRepository,
        vehicleAPIManager: VehicleAPIManager,
        applicationData: ApplicationData,
    ): VprsSyncHelper =
        VprsSyncHelper(
            vprsRepository,
            vehicleAPIManager,
            applicationData,
        )

    @Provides
    @Inject
    fun provideQrScanHelper(
        idpRepository: IdpRepository,
        oneAppPreferenceModel: OneAppPreferenceModel,
        buildWrapper: BuildWrapper,
    ): QRScanHelper = QRScanHelper(idpRepository, oneAppPreferenceModel, buildWrapper)

    @Provides
    fun provideDigitalKeyLocalData(preferenceModel: OneAppPreferenceModel): DigitalKeyLocalData =
        DigitalKeyLocalData.getInstance(
            oneAppPreferenceModel = preferenceModel,
        )

    @Provides
    fun provideBluetoothAdapter(): BluetoothAdapter = BluetoothAdapter.getDefaultAdapter()

    @ApplicationScope
    @Provides
    @Singleton
    fun provideApplicationScope(): CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
}
