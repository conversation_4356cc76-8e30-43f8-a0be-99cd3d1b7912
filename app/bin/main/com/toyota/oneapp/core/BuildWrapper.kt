/*
 * Copyright © 2024 Toyota. All rights reserved.
 */

package com.toyota.oneapp.core

import android.os.Build
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.util.Brand

class BuildWrapper {
    fun isAuRegion() = BuildConfig.FLAVOR_region_brand.contains("au", true)

    fun isSubaruApp() = BuildConfig.APP_BRAND == Brand.SUBARU.appBrand

    fun isProdEnv() = BuildConfig.FLAVOR_environment.contains("prod", true)

    fun getAppVersion() = BuildConfig.VERSION_NAME

    fun appBrand() = BuildConfig.APP_BRAND

    fun isNewerAndroidTiramisu() = Build.VERSION.SDK_INT > Build.VERSION_CODES.TIRAMISU
}
