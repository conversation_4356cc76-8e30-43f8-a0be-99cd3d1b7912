package com.toyota.oneapp.core

import android.app.Application
import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.content.res.AssetManager
import android.content.res.Resources
import android.location.Geocoder
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.sharedpref.SharedPreferencesModule
import dagger.BindsInstance
import dagger.Component
import kotlinx.coroutines.CoroutineDispatcher
import toyotaone.commonlib.coroutine.DispatcherProvider
import toyotaone.commonlib.location.UserLocationProvider
import java.util.concurrent.Executor
import java.util.concurrent.ScheduledExecutorService
import kotlin.coroutines.CoroutineContext

@Component(modules = [CoreModule::class, SharedPreferencesModule::class])
interface CoreComponent {
    @ApplicationContext
    fun applicationContext(): Context

    fun assetManager(): AssetManager

    @MainThreadExecutor
    fun mainThreadExecutor(): Executor

    @IoThreadExecutor
    fun ioThreadExecutor(): ScheduledExecutorService

    fun coroutineContext(): CoroutineContext

    @MainCoroutineDispatcher
    fun coroutineMainDispatcher(): CoroutineDispatcher

    fun applicationData(): ApplicationData

    fun dklocaldata(): DigitalKeyLocalData

    fun geocoder(): Geocoder

    fun localBroadcastManager(): LocalBroadcastManager

    fun dispatcherProvider(): DispatcherProvider

    fun errorMessageParser(): ErrorMessageParser

    fun bluetoothAdapter(): BluetoothAdapter

    fun resources(): Resources

    fun userLocationProvider(): UserLocationProvider

    @Component.Factory
    interface Factory {
        fun create(
            @BindsInstance application: Application,
        ): CoreComponent
    }
}
