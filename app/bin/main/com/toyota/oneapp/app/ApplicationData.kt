package com.toyota.oneapp.app

import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.model.vehicle.VehicleDetail
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import toyotaone.commonlib.log.LogTool

data class ApplicationDataProvider(
    val vehicleInfoList: ArrayList<VehicleInfo>? = null,
    val selectedVehicle: VehicleInfo? = null,
    val vehicleDetailList: ArrayList<VehicleDetail>? = null,
    val nickName: String? = null,
)

class ApplicationData {
    companion object {
        private var applicationData: ApplicationData? = null
        private var oneAppPreferenceModel: OneAppPreferenceModel? = null

        fun getInstance(preferenceModel: OneAppPreferenceModel): ApplicationData {
            if (applicationData == null) {
                synchronized(ApplicationData::class.java) {
                    if (null == applicationData) {
                        applicationData = loadCachedApplicationData(preferenceModel)
                        oneAppPreferenceModel = preferenceModel

                        // If we needed to get the application data from shared preferences instead of
                        // the private static var, it means the app was killed and is no longer an
                        // active session.
                        applicationData!!.appSessionActive = false
                    }
                }
            }
            return applicationData!!
        }

        private fun loadCachedApplicationData(preferenceModel: OneAppPreferenceModel): ApplicationData {
            // load pref applicationData
            val newApplicationData = ApplicationData()
            val applicationDataProvider = preferenceModel.getApplicationDataProvider()
            // update all pref data into prefApplicationData
            // vehicleList
            newApplicationData.vehicleListState.value = applicationDataProvider.vehicleInfoList

            // selectedVehicle
            newApplicationData._selectedVehicleState.value = applicationDataProvider.selectedVehicle

            // savedVehicleDetailList
            applicationDataProvider.vehicleDetailList?.let {
                newApplicationData.savedVehicles = it
            } ?: newApplicationData.savedVehicles.clear()

            // nickName
            newApplicationData._selectedVehicleNickName.value = applicationDataProvider.nickName

            return newApplicationData
        }
    }

    var appSessionActive = false
        set(value) {
            updateApplicationDataCache()
            field = value
        }

    // addVehicleSuccess flag will be true once the vehicle is successfully associated with the account.
    var addVehicleSuccess = false

    var savedVehicles: ArrayList<VehicleDetail> = ArrayList()
        private set

    private val vehicleListState: MutableStateFlow<ArrayList<VehicleInfo>?> =
        MutableStateFlow(
            value = null,
        )

    private val _selectedVehicleState =
        MutableStateFlow<VehicleInfo?>(
            value = null,
        )
    private val selectedVehicleState = _selectedVehicleState.asStateFlow()

    fun getSelectedVehicleNickNameState(): StateFlow<String?> = selectedVehicleNickName

    private val _selectedVehicleNickName =
        MutableStateFlow<String?>(
            getVehicleNickeName(),
        )
    private val selectedVehicleNickName = _selectedVehicleNickName.asStateFlow()

    fun getUpdatedLanguageState(): StateFlow<String?> = updateLanguage

    private val _updateLanguage =
        MutableStateFlow<String?>(
            ToyotaConstants.EMPTY_STRING,
        )
    val updateLanguage = _updateLanguage.asStateFlow()

    // force refresh the dashboard
    private val _forceVinListRefresh = MutableStateFlow(value = false)
    val forceVinListRefresh: StateFlow<Boolean> get() = _forceVinListRefresh.asStateFlow()

    // Used to track haptic touch and delay EV Real Time status
    private val _threeDShortActionStatus = MutableStateFlow(value = false)
    val threeDShortActionStatus: StateFlow<Boolean> get() = _threeDShortActionStatus.asStateFlow()

    // prevVehicle holds the previously selected vehicle for add Vehicle Flow.
    private var prevVehicle: VehicleInfo? = null

    var mileage: HashMap<String, Int> = HashMap()
    var generationCode: String? = ToyotaConstants.CY17
    var subscriber: AccountInfoSubscriber? = null
    var threeDShortAction: String? = null
    var isFeedbackThumbsUpProfileUpdate = false
    var hasAutoLaunchPermission = false

    private fun updateApplicationDataCache() {
        oneAppPreferenceModel?.setApplicationDataProvider(
            data =
                ApplicationDataProvider(
                    vehicleInfoList = getVehicleList(),
                    selectedVehicle = getSelectedVehicle(),
                    vehicleDetailList = savedVehicles,
                    nickName = getVehicleNickeName(),
                ),
        )
    }

    fun clear() {
        appSessionActive = false
        savedVehicles.clear()
        setVehicleList(null)
        setSelectedVehicle(null)

        mileage.clear()
        subscriber = null
        threeDShortAction = null
        isFeedbackThumbsUpProfileUpdate = false
        hasAutoLaunchPermission = false
        oneAppPreferenceModel?.clearApplicationData()
    }

    fun setVehicleList(vehicleList: ArrayList<VehicleInfo>?) {
        this.vehicleListState.value = vehicleList
        updateApplicationDataCache()
    }

    fun getVehicleListState(): StateFlow<ArrayList<VehicleInfo>?> = this.vehicleListState.asStateFlow()

    fun getVehicleList(): ArrayList<VehicleInfo>? = this.vehicleListState.value

    fun getSelectedVehicleState(): StateFlow<VehicleInfo?> = selectedVehicleState

    fun getSelectedVehicle(): VehicleInfo? = selectedVehicleState.value

    fun setSelectedVehicle(vehicleInfo: VehicleInfo?) {
        prevVehicle = _selectedVehicleState.value
        _selectedVehicleState.value = vehicleInfo
        updateApplicationDataCache()
        setSelectedVehicleNickName(getVehicleNickeName())
    }

    fun setSelectedVehicleNickName(nickName: String?) {
        updateApplicationDataCache()
        _selectedVehicleNickName.value = nickName
    }

    fun getVehicleNickeName(): String =
        if (selectedVehicleState.value?.nickName?.isNotEmpty() == true) {
            selectedVehicleState.value?.nickName!!
        } else {
            "${selectedVehicleState.value?.modelYear} ${selectedVehicleState.value?.modelName}"
        }

    fun setSelectedLanguage(language: String?) {
        language?.also { _updateLanguage.value = it }
    }

    fun saveVehicleToList(vehicle: VehicleDetail) {
        savedVehicles.clear()
        savedVehicles.add(vehicle)
        updateApplicationDataCache()
    }

    fun clearSavedVehicle() {
        savedVehicles.clear()
    }

    /**
     * shouldClearPrevVehicle should be set to TRUE when calling from a vehicle registration failed state
     * */
    fun handleAddVehicleBackPressed(shouldClearPrevVehicle: Boolean = true) {
        clearSavedVehicle()
        if (addVehicleSuccess) {
            addVehicleSuccess = false
            _forceVinListRefresh.value = true
        } else {
            resetToPreviouslySelectedVehicle(shouldClearPrevVehicle)
        }
    }

    private fun resetToPreviouslySelectedVehicle(shouldClearPrevVehicle: Boolean = true) {
        val previousVehicle =
            getVehicleList()?.firstOrNull { it.vin == prevVehicle?.vin } ?: getDefaultVehicle()
        setSelectedVehicle(previousVehicle)
        if (shouldClearPrevVehicle) {
            prevVehicle = null
        }
    }

    fun resetForceRefreshFlag() {
        _forceVinListRefresh.value = false
    }

    fun updateThreeDShortActionStatus(status: Boolean = false) {
        if ((!_threeDShortActionStatus.value && status) || (_threeDShortActionStatus.value && !status)) {
            LogTool.d("ThreeDShortActionStatus", status.toString())
            _threeDShortActionStatus.value = status
        }
    }

    /**
     * @return The first vehicle set as preferred.  If none is set as preferred, return the first
     * vehicle in the list. If the list is empty, return null.
     */
    fun getDefaultVehicle(): VehicleInfo? {
        val vehicleList = getVehicleList()
        return if (vehicleList?.isNotEmpty() == true) {
            for (vehicle in vehicleList) {
                if (vehicle.preferred == 1) {
                    return vehicle
                }
            }
            vehicleList[0]
        } else {
            null
        }
    }

    fun mergeSecondaryVehicleIfNeeded(
        secondaryVehicle: VehicleInfo?,
        shouldSelect: Boolean = true,
    ) {
        if (secondaryVehicle == null || !secondaryVehicle.isDigitalkey) return

        val currentVehicleList = getVehicleList() ?: ArrayList()

        // Check if vehicle already exists in the list
        if (currentVehicleList.none { it.vin == secondaryVehicle.vin }) {
            currentVehicleList.add(secondaryVehicle)
            setVehicleList(currentVehicleList)
            if (shouldSelect) {
                setSelectedVehicle(secondaryVehicle)
            }
            updateApplicationDataCache()
        }
    }
}
