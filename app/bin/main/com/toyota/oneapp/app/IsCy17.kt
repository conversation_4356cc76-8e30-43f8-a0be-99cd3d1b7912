package com.toyota.oneapp.app

import com.toyota.oneapp.util.ToyotaConstants
import javax.inject.Inject
import javax.inject.Provider

class IsCy17
    @Inject
    constructor(private val applicationData: ApplicationData) : Provider<Boolean> {
        override fun get(): Boolean =
            when {
                ToyotaConstants.CY17.equals(applicationData.generationCode, ignoreCase = true) -> true
                ToyotaConstants.PRE17CY.equals(applicationData.generationCode, ignoreCase = true) -> true
                else -> false
            }
    }
