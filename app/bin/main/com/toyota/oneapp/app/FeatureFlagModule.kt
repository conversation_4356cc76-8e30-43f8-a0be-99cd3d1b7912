package com.toyota.oneapp.app

import com.toyota.oneapp.core.ApplicationContext
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject

@InstallIn(SingletonComponent::class)
@Module
class FeatureFlagModule {
    @Provides
    @Inject
    fun provideAppFeatureFlags(
        @ApplicationContext applicationData: ApplicationData,
        oneAppPreferenceModel: OneAppPreferenceModel,
    ): AppFeatureFlags = AppFeatureFlagsImpl(applicationData, oneAppPreferenceModel)
}
