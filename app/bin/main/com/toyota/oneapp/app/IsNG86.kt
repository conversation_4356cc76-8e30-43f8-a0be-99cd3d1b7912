package com.toyota.oneapp.app

import com.toyota.oneapp.util.ToyotaConstants
import javax.inject.Inject
import javax.inject.Provider

class IsNG86
    @Inject
    constructor(private val applicationData: ApplicationData) : Provider<Boolean> {
        override fun get(): Boolean {
            val vehicle = applicationData.getSelectedVehicle()
            return vehicle != null && ToyotaConstants.GR86.equals(vehicle.generation, ignoreCase = true)
        }
    }
