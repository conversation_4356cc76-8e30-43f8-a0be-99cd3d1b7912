package com.toyota.oneapp.app

import android.content.Context
import com.toyota.oneapp.core.ApplicationContext
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module
class DataDogModule {
    @Provides
    fun providesDataDogUtils(
        @ApplicationContext context: Context,
    ): DataDogUtils = DataDogUtils.getInstance(context)
}
