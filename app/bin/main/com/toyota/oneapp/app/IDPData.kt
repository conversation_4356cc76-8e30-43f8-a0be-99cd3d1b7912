package com.toyota.oneapp.app

import android.text.TextUtils
import com.auth0.android.jwt.JWT
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.ToyotaConstants
import org.forgerock.android.auth.AccessToken
import java.util.Locale

class IDPData {
    /**
     * IMPORTANT: Update the clear method whenever new fields are added
     */

    companion object {
        internal var instance: IDPData? = null
        internal var preferenceModel: OneAppPreferenceModel? = null

        @JvmStatic
        fun getInstance(oneAppPreferenceModel: OneAppPreferenceModel): IDPData {
            if (instance == null) {
                instance = oneAppPreferenceModel.getIDPData()
                preferenceModel = oneAppPreferenceModel
            }
            return instance!!
        }
    }

    private val localeParams = HashMap<String, String>()
    private var userProfileName = getUserFirstName() + " " + getUserFirstName()

    fun handleAccessToken(
        result: AccessToken,
        oneAppPreferenceModel: OneAppPreferenceModel,
    ) {
        accessToken = result.accessToken
        idToken = result.idToken
        refreshToken = result.refreshToken
        accessTokenExpireTime = result.expiration.time - ToyotaConstants.TOKEN_EXPIRE_TIME_BUFF_TIME
        val jwt =
            JWT(
                if (result.idToken == null || result.idToken.isEmpty()) result.accessToken else result.idToken,
            )
        val guid = jwt.getClaim("sub").asString()
        oneAppPreferenceModel.setGuid(guid ?: "")
        clearLimitedToken()
    }

    var accessToken: String = ""
        set(accessToken) {
            field = accessToken
            updateCache()
        }

    var limitedToken: String = ToyotaConstants.EMPTY_STRING
        set(limitedToken) {
            field = limitedToken
            updateCache()
        }

    var refreshToken: String? = null
        set(refreshToken) {
            field = refreshToken
            updateCache()
        }

    var idToken: String? = null
        set(idToken) {
            field = idToken
            updateCache()
        }

    var accessTokenExpireTime: Long = 0
        set(accessTokenExpireTime) {
            field = accessTokenExpireTime
            updateCache()
        }

    var limitedTokenExpireTime: Long = 0
        set(limitedTokenExpireTime) {
            field = limitedTokenExpireTime
            updateCache()
        }

    fun getUserFirstName(): String? =
        if (!TextUtils.isEmpty(idToken)) {
            JWT(idToken!!).getClaim("given_name").asString()
        } else {
            ""
        }

    fun getUserLastName(): String? =
        if (!TextUtils.isEmpty(idToken)) {
            JWT(idToken!!).getClaim("family_name").asString()
        } else {
            ""
        }

    fun getUserEmail(): String? =
        if (!TextUtils.isEmpty(idToken)) {
            JWT(idToken!!).getClaim("email").asString()
        } else {
            ""
        }

    fun getOID(): String? =
        if (!TextUtils.isEmpty(idToken)) {
            JWT(idToken!!).getClaim("oid").asString()
        } else {
            ""
        }

    fun getPhoneNumber(): String? =
        if (!TextUtils.isEmpty(idToken)) {
            JWT(idToken!!).getClaim("phone_number").asString()
        } else {
            ""
        }

    fun phoneNumberVerified(): Boolean = !idToken.isNullOrEmpty() && JWT(idToken!!).getClaim("phone_number_verified").asBoolean() == true

    fun isRefreshTokenExpired(): Boolean {
        val expTime: Long? =
            if (!TextUtils.isEmpty(refreshToken)) {
                JWT(refreshToken!!).getClaim("exp").asLong()
            } else {
                0L
            }
        return System.currentTimeMillis() / 1000L >= expTime ?: 0L
    }

    fun setLocaleParams(
        brand: String?,
        preferredLanguage: String?,
        countryCode: String?,
        languageCode: String?,
    ) {
        brand?.let { localeParams.put("brand", it) }
        if (preferredLanguage != null) {
            localeParams["claims_locales"] = preferredLanguage
        }
        if (countryCode != null) {
            localeParams["market"] = countryCode
        }
        if (languageCode != null) {
            localeParams["ui_locales"] = languageCode.lowercase(Locale.getDefault())
        }
        updateCache()
    }

    fun getLocaleParams(param: String?): String? =
        if (!TextUtils.isEmpty(param)) {
            localeParams[param]
        } else {
            ""
        }

    /**
     * IMPORTANT: Do not construct a new object here as it will break congruency between different injection points
     */
    fun clear() {
        accessToken = ToyotaConstants.EMPTY_STRING
        refreshToken = ToyotaConstants.EMPTY_STRING
        idToken = ToyotaConstants.EMPTY_STRING
        accessTokenExpireTime = 0
        localeParams.clear()
        clearLimitedToken()

        updateCache()
    }

    fun clearToken() {
        accessToken = ToyotaConstants.EMPTY_STRING
        refreshToken = null
        idToken = null
        accessTokenExpireTime = 0
        clearLimitedToken()
        updateCache()
    }

    private fun clearLimitedToken() {
        limitedToken = ToyotaConstants.EMPTY_STRING
        limitedTokenExpireTime = 0
    }

    private fun updateCache() {
        instance?.let { preferenceModel?.setIDPData(it) }
    }

    fun upDateIDPData(idpData: IDPData) {
        instance = idpData
        updateCache()
    }

    fun setUserProfileName(profileName: String) {
        userProfileName = profileName
    }

    fun getUserProfileName(): String = userProfileName
}
