package com.toyota.oneapp.app

import android.content.Context
import android.util.Log
import com.datadog.android.Datadog.initialize
import com.datadog.android.Datadog.isInitialized
import com.datadog.android.Datadog.setVerbosity
import com.datadog.android.core.configuration.Configuration
import com.datadog.android.core.configuration.Credentials
import com.datadog.android.log.Logger
import com.datadog.android.privacy.TrackingConsent
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.JsonObject
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.component.SingletonHolder
import org.forgerock.android.auth.ui.FRNative
import toyotaone.commonlib.sharepreference.HawkUtil
import java.util.Date

class DataDogUtils private constructor(
    private val context: Context,
) {
    companion object : SingletonHolder<DataDogUtils, Context>(::DataDogUtils) {
        private const val DEVICE_TOKEN = "DEVICE_TOKEN"
        private var logger: Logger? = null
        private const val GUID_ATTRIBUTE = "GUID"
        private const val APP_BRAND_ATTRIBUTE = "APPBRAND"
        private const val APP_VERSION_ATTRIBUTE = "APPVERSION"
        private const val APP = "app"
        private const val DEVICE_ID = "device"
        private const val ID = "id"
        private const val DATE = "date"
        private const val USER = "user"
        private const val VERSION = "version"
        private const val CATEGORY = "category"
        private const val LOG_LEVEL = "logLevel"
    }

    private fun logAppData(): JsonObject {
        val appLogObject =
            JsonObject().apply {
                add(VERSION, logAppVersion())
                addProperty(DEVICE_TOKEN, HawkUtil.get(DEVICE_TOKEN, ""))
                addProperty(APP_BRAND_ATTRIBUTE, BuildConfig.APP_BRAND)
            }
        return appLogObject
    }

    private fun logAppVersion(): JsonObject {
        val appVersionObject =
            JsonObject().apply {
                addProperty(APP_VERSION_ATTRIBUTE, BuildConfig.VERSION_NAME.let { it.split("-")[0] })
            }
        return appVersionObject
    }

    private fun logDeviceID(): JsonObject {
        val deviceIdObject =
            JsonObject().apply {
                addProperty(ID, FRNative.deviceId)
            }
        return deviceIdObject
    }

    private fun logUserID(): JsonObject {
        val userIdObject =
            JsonObject().apply {
                addProperty(ID, HawkUtil.get(GUID_ATTRIBUTE, ""))
            }
        return userIdObject
    }

    fun setCategory(category: String) {
        val categoryObject =
            JsonObject().apply {
                addProperty(CATEGORY, category)
            }
        logger?.addAttribute(CATEGORY, categoryObject)
    }

    fun setLogLevel(logLevel: String) {
        val logLevelObject =
            JsonObject().apply {
                addProperty(LOG_LEVEL, logLevel)
            }
        logger?.addAttribute(LOG_LEVEL, logLevelObject)
    }

    fun appendLog(logInfo: String) {
        logger?.i(logInfo)
    }

    fun logInfo(
        tag: String,
        logInfo: String,
    ) = logger?.i(tag.plus(":").plus(logInfo))

    fun logError(
        tag: String,
        logInfo: String,
    ) = logger?.e(tag.plus(":").plus(logInfo))

    fun initializeDataDog() {
        try {
            val configuration =
                Configuration
                    .Builder(
                        logsEnabled = true,
                        tracesEnabled = true,
                        crashReportsEnabled = true,
                        rumEnabled = true,
                    ).build()
            val credentials =
                Credentials(
                    BuildConfig.DATADOG_CLIENT_ID,
                    BuildConfig.FLAVOR_environment,
                    BuildConfig.IDP_CLIENT_ID,
                    context.packageName,
                    BuildConfig.IDP_CLIENT_ID,
                )
            initialize(context, credentials, configuration, TrackingConsent.GRANTED)

            if (isInitialized()) {
                setVerbosity(Log.INFO)
                logger =
                    Logger
                        .Builder()
                        .setNetworkInfoEnabled(true)
                        .setLogcatLogsEnabled(true)
                        .setDatadogLogsEnabled(true)
                        .setBundleWithTraceEnabled(true)
                        .setLoggerName(BuildConfig.APPLICATION_ID)
                        .build()
                logger?.addAttribute(APP, logAppData())
                logger?.addAttribute(USER, logUserID())
                logger?.addAttribute(DEVICE_ID, logDeviceID())
                logger?.addAttribute(DATE, Date())
            }
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().log("DataDog Initilization ${ex.message}")
        }
    }
}
