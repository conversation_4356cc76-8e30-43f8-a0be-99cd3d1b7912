package com.toyota.oneapp.app

import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.Features
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import javax.inject.Inject

class AppFeatureFlagsImpl
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : AppFeatureFlags {
        private var featureFlagMap: HashMap<String, Int> = HashMap<String, Int>()

        override fun isFeatureEnabled(appFeature: Feature): Boolean {
            applicationData.getSelectedVehicle()?.let {
                getFeatureFlagMap(it.vin)
                return if (featureFlagMap.containsKey(appFeature.featureName)) {
                    featureFlagMap[appFeature.featureName] == Features.ENABLED
                } else {
                    it.isFeatureEnabled(appFeature)
                }
            }
            return false
        }

        override fun isMaintenanceEnabled(appFeature: Feature): Boolean {
            applicationData.getSelectedVehicle()?.let {
                getFeatureFlagMap(it.vin)
            }

            return if (featureFlagMap.containsKey(appFeature.featureName)) {
                featureFlagMap[appFeature.featureName] == Features.MAINTENANCE
            } else {
                (
                    applicationData.getSelectedVehicle()?.isFeatureMaintenance(appFeature)
                ) == true
            }
        }

        override fun reset(vin: String) {
            oneAppPreferenceModel.resetFeatureFlag(vin)
            featureFlagMap.clear()
        }

        override fun setFeatureOverrides(
            appFeature: Feature,
            enabled: Int,
        ) {
            applicationData.getSelectedVehicle()?.let {
                getFeatureFlagMap(it.vin)
            }
            when (enabled) {
                // DISABLED = 0
                // ENABLED = 1
                // MAINTENANCE = 2
                Features.DISABLED -> {
                    checkIfFeatureEnabledOverride(
                        false,
                        applicationData.getSelectedVehicle(),
                        appFeature,
                    )
                }
                Features.ENABLED -> {
                    checkIfFeatureEnabledOverride(
                        true,
                        applicationData.getSelectedVehicle(),
                        appFeature,
                    )
                }
                Features.MAINTENANCE -> {
                    checkIfMaintenanceOverride(applicationData.getSelectedVehicle(), appFeature)
                }
            }

            applicationData.getSelectedVehicle()?.let {
                oneAppPreferenceModel.setFeatureFlagOverride(
                    it.vin,
                    featureFlagMap,
                )
            }
        }

        private fun checkIfFeatureEnabledOverride(
            enabled: Boolean,
            vehicleInfo: VehicleInfo?,
            appFeature: Feature,
        ) {
            if (enabled == vehicleInfo?.isFeatureEnabled(appFeature)) {
                featureFlagMap.remove(appFeature.featureName)
            } else {
                featureFlagMap[appFeature.featureName] =
                    if (enabled) Features.ENABLED else Features.DISABLED
            }
        }

        private fun checkIfMaintenanceOverride(
            vehicleInfo: VehicleInfo?,
            appFeature: Feature,
        ) {
            if (vehicleInfo?.isFeatureMaintenance(appFeature) == true) {
                featureFlagMap.remove(appFeature.featureName)
            } else {
                featureFlagMap[appFeature.featureName] = Features.MAINTENANCE
            }
        }

        override fun getFeatureOverrides(vin: String): Map<String, Int> {
            getFeatureFlagMap(vin)
            return featureFlagMap
        }

        private fun getFeatureFlagMap(vin: String) {
            featureFlagMap =
                if (oneAppPreferenceModel.contains(vin)) {
                    (
                        {
                            oneAppPreferenceModel.getFeatureFlagOverride(vin)
                        }
                    ) as HashMap<String, Int>
                } else {
                    HashMap<String, Int>()
                }
        }
    }
