@file:JvmName("CommonConstants")

package com.toyota.oneapp.constants

const val MESSAGE = "MESSAGE"
const val VIN_LIST_RETRY_TIMES = 2

interface AccountTermsAndPrivacyUrls {
    // US terms and privacy
    val usTerms: String
    val usPrivacy: String

    // Canada terms and privacy
    val canadaEnTerms: String
    val canadaFrTerms: String
    val canadaEnPrivacy: String
    val canadaFrPrivacy: String

    // PR terms and privacy
    val prEnTerms: String
    val prEsTerms: String
    val prEnPrivacy: String
    val prEsPrivacy: String

    // MX terms and privacy
    val mxEnTerms: String
    val mxEsTerms: String
    val mxEnPrivacy: String
    val mxEsPrivacy: String
}

interface VehicleTermsAndPrivacyUrls {
    // US terms and privacy
    val usTerms: String
    val usPrivacy: String

    // TCI 17 vehicle detail terms
    val tciVehicleDetail17EnTerms: String
    val tciVehicleDetail17FrTerms: String

    // TCI 17+ vehicle detail terms
    val tciVehicleDetail17plusEnTerms: String
    val tciVehicleDetail17plusFrTerms: String

    // TCI vehicle detail privacy
    val tciVehicleDetailEnPrivacy: String
    val tciVehicleDetailFrPrivacy: String

    // sign up privacy and terms
    val prTerms17En: String
    val prTermsEn: String
    val prTermsEs: String
    val prTerms17Es: String

    // sign up privacy and terms
    val hiTerms17En: String
    val hiTermsEn: String

    // PR setting privacy and terms
    val prPrivacyEn: String
    val prPrivacyEs: String

    // MX terms and privacy
    val mxTerms: String
    val mxTermsEn: String
    val mxPrivacy: String
    val mxPrivacyEn: String
}

private const val NA_TOYOTA_TERMS = "https://www.toyota.com/privacyvts/"

private const val NA_LEXUS_VEHICLE_TERMS = "https://www.lexus.com/privacyvts/"

private const val MX_LEXUS_MX_TERMS =
    "https://www.lexus.mx/content/dam/lexus-mx/documentos/Connected_Services/Terminos_y_Condiciones.pdf"

private const val MX_LEXUS_EN_TERMS =
    "https://www.lexus.mx/content/dam/lexus-mx/documentos/Connected_Services/Terminos_y_Condiciones_ENG.pdf"

private const val MX_LEXUS_MX_PRIVACY =
    "https://www.lexus.mx/content/dam/lexus-mx/documentos/Connected_Services/Aviso_de_Privacidad.pdf"

private const val MX_LEXUS_EN_PRIVACY =
    "https://www.lexus.mx/content/dam/lexus-mx/documentos/Connected_Services/Aviso_de_Privacidad_ENG.pdf"

private const val PR_TOYOTA_TERMS = "https://www.toyotapr.com/terminosycondiciones"

private const val PR_LEXUS_VEHICLE_TERMS = "https://www.lexuspr.com/assets/pdf/Lexus_Enform_Terms_and_Conditions.pdf"

private const val CA_TOYOTA_EN_TERMS = "https://www.toyota.ca/connectedservices-termsofuse"

private const val CA_TOYOTA_FR_TERMS = "https://www.toyota.ca/servicesconnectés-conditionsdutilisation"

private const val CA_TOYOTA_EN_PRIVACY = "https://www.toyota.ca/connectedservices-privacy"

private const val CA_TOYOTA_FR_PRIVACY = "https://www.toyota.ca/servicesconnectés-confidentialite"

object ToyVehicleUrls : VehicleTermsAndPrivacyUrls {
    override val usTerms: String
        get() = NA_TOYOTA_TERMS
    override val usPrivacy: String
        get() = NA_TOYOTA_TERMS
    override val tciVehicleDetail17EnTerms: String
        get() = CA_TOYOTA_EN_TERMS
    override val tciVehicleDetail17FrTerms: String
        get() = CA_TOYOTA_FR_TERMS
    override val tciVehicleDetail17plusEnTerms: String
        get() = CA_TOYOTA_EN_TERMS
    override val tciVehicleDetail17plusFrTerms: String
        get() = CA_TOYOTA_FR_TERMS
    override val tciVehicleDetailEnPrivacy: String
        get() = CA_TOYOTA_EN_PRIVACY
    override val tciVehicleDetailFrPrivacy: String
        get() = CA_TOYOTA_FR_PRIVACY
    override val prTermsEn: String
        get() = "https://www.toyotausvi.com/assets/pdf/connected_services_terms%20_of_use.pdf"
    override val prTerms17En: String
        get() = "https://www.toyotausvi.com/assets/pdf/connected_services_terms%20_of_use.pdf"
    override val prPrivacyEn: String
        get() = "https://www.toyotausvi.com/connectedservices/privacy"
    override val prTermsEs: String
        get() = "https://www.toyotapr.com/assets/pdf/terminos_de_uso_servicios_conectados.pdf"
    override val prTerms17Es: String
        get() = "https://www.toyotapr.com/assets/pdf/terminos_de_uso_servicios_conectados.pdf"
    override val hiTerms17En: String
        get() = NA_TOYOTA_TERMS
    override val hiTermsEn: String
        get() = NA_TOYOTA_TERMS
    override val prPrivacyEs: String
        get() = "https://www.toyotapr.com/serviciosconectados/privacidad"
    override val mxTerms: String
        get() = MX_LEXUS_MX_TERMS
    override val mxTermsEn: String
        get() = MX_LEXUS_EN_TERMS
    override val mxPrivacy: String
        get() = MX_LEXUS_MX_PRIVACY
    override val mxPrivacyEn: String
        get() = MX_LEXUS_EN_PRIVACY
}

object LexusVehicleUrls : VehicleTermsAndPrivacyUrls {
    override val usTerms: String
        get() = NA_LEXUS_VEHICLE_TERMS
    override val usPrivacy: String
        get() = NA_LEXUS_VEHICLE_TERMS
    override val tciVehicleDetail17EnTerms: String
        get() = "https://www.lexus.ca/enform-termsofuse"
    override val tciVehicleDetail17FrTerms: String
        get() = "https://www.lexus.ca/enform-conditionsdutilisation"
    override val tciVehicleDetail17plusEnTerms: String
        get() = "https://www.lexus.ca/enform-termsofuse"
    override val tciVehicleDetail17plusFrTerms: String
        get() = "https://www.lexus.ca/enform-conditionsdutilisation"
    override val tciVehicleDetailEnPrivacy: String
        get() = "https://www.lexus.ca/enform-privacy"
    override val tciVehicleDetailFrPrivacy: String
        get() = "https://www.lexus.ca/enform-confidentialite"
    override val prTermsEn: String
        get() = PR_LEXUS_VEHICLE_TERMS
    override val prTerms17En: String
        get() = PR_LEXUS_VEHICLE_TERMS
    override val prPrivacyEn: String
        get() = "https://www.lexuspr.com/enform/privacidad"
    override val prTermsEs: String
        get() = PR_LEXUS_VEHICLE_TERMS
    override val prTerms17Es: String
        get() = PR_LEXUS_VEHICLE_TERMS
    override val hiTerms17En: String
        get() = NA_LEXUS_VEHICLE_TERMS
    override val hiTermsEn: String
        get() = NA_LEXUS_VEHICLE_TERMS
    override val prPrivacyEs: String
        get() = "https://www.lexuspr.com/enform/privacidad"
    override val mxTerms: String
        get() = MX_LEXUS_MX_TERMS
    override val mxTermsEn: String
        get() = MX_LEXUS_EN_TERMS
    override val mxPrivacy: String
        get() = MX_LEXUS_MX_PRIVACY
    override val mxPrivacyEn: String
        get() = MX_LEXUS_EN_PRIVACY
}

object ToyAccountUrls : AccountTermsAndPrivacyUrls {
    override val usTerms: String
        get() = "https://www.toyota.com/support/legal-terms"
    override val usPrivacy: String
        get() = "https://www.toyota.com/support/privacy-notice"
    override val canadaEnTerms: String
        get() = "https://www.toyota.ca/toyota/en/legal"
    override val canadaFrTerms: String
        get() = "https://www.toyota.ca/toyota/fr/legal"
    override val canadaEnPrivacy: String
        get() = "https://www.toyota.ca/toyota/en/privacy"
    override val canadaFrPrivacy: String
        get() = "https://www.toyota.ca/toyota/fr/privacy"
    override val prEnTerms: String
        get() = PR_TOYOTA_TERMS
    override val prEsTerms: String
        get() = PR_TOYOTA_TERMS
    override val prEnPrivacy: String
        get() = "https://www.toyotausvi.com/privacy"
    override val prEsPrivacy: String
        get() = "https://www.toyotapr.com/privacidad"
    override val mxEnTerms: String
        get() = MX_LEXUS_EN_TERMS
    override val mxEsTerms: String
        get() = MX_LEXUS_MX_TERMS
    override val mxEnPrivacy: String
        get() = MX_LEXUS_EN_PRIVACY
    override val mxEsPrivacy: String
        get() = MX_LEXUS_MX_PRIVACY
}

object LexusAccountUrls : AccountTermsAndPrivacyUrls {
    override val usTerms: String
        get() = "https://www.lexus.com/privacy/legal-terms"
    override val usPrivacy: String
        get() = "https://www.lexus.com/privacy"
    override val canadaEnTerms: String
        get() = "https://www.lexus.ca/lexus/en/legal"
    override val canadaFrTerms: String
        get() = "https://www.lexus.ca/lexus/fr/legal"
    override val canadaEnPrivacy: String
        get() = "https://www.lexus.ca/lexus/en/privacy"
    override val canadaFrPrivacy: String
        get() = "https://www.lexus.ca/lexus/fr/privacy"
    override val prEnTerms: String
        get() = "https://www.lexuspr.com/terminosycondiciones"
    override val prEsTerms: String
        get() = "https://www.lexuspr.com/terminosycondiciones"
    override val prEnPrivacy: String
        get() = "https://www.lexuspr.com/privacidad"
    override val prEsPrivacy: String
        get() = "https://www.lexuspr.com/privacidad"
    override val mxEnTerms: String
        get() = MX_LEXUS_EN_TERMS
    override val mxEsTerms: String
        get() = MX_LEXUS_MX_TERMS
    override val mxEnPrivacy: String
        get() = MX_LEXUS_EN_PRIVACY
    override val mxEsPrivacy: String
        get() = MX_LEXUS_MX_PRIVACY
}

object SubaruAccountURLS : AccountTermsAndPrivacyUrls {
    override val usTerms: String = "https://www.subaru.com/company/conditions.html"
    override val usPrivacy: String = "https://www.subaru.com/company/privacy"
    override val canadaEnTerms: String = "https://www.subaru.ca/tou#website"
    override val canadaFrTerms: String = "https://www.subaru.ca/cdu#siteweb"
    override val canadaEnPrivacy: String = "https://www.subaru.ca/privacy#general"
    override val canadaFrPrivacy: String = "https://www.subaru.ca/confidentialit%c3%a9#general"
    override val prEnTerms: String = PR_TOYOTA_TERMS
    override val prEsTerms: String = PR_TOYOTA_TERMS
    override val prEnPrivacy: String = "https://www.toyotausvi.com/privacy"
    override val prEsPrivacy: String = "https://www.toyotapr.com/privacidad"
    override val mxEnTerms: String = MX_LEXUS_EN_TERMS
    override val mxEsTerms: String = "https://www.lexus.mx/terminos-y-condiciones"
    override val mxEnPrivacy: String = MX_LEXUS_EN_PRIVACY
    override val mxEsPrivacy: String = "https://www.lexus.mx/politica-de-privacidad"
}

interface VehicleAccessUrls {
    val disconnect: String
}

object ToyotaVehicleAccessUrls : VehicleAccessUrls {
    override val disconnect = "https://disconnectaccess.toyota.com/"
}

object LexusVehicleAccessUrls : VehicleAccessUrls {
    override val disconnect = "https://disconnectaccess.lexus.com/"
}

object SubaruVehicleAccessUrls : VehicleAccessUrls {
    override val disconnect = "https://disconnectaccess.toyota.com/"
}
