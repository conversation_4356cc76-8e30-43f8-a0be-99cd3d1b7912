package com.toyota.oneapp.component

import android.content.Context
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.core.ApplicationContext
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject

@InstallIn(SingletonComponent::class)
@Module
class IDPModule {
    @Provides
    @Inject
    fun provideIDPHelper(
        @ApplicationContext context: Context,
        oneAppPreferenceModel: OneAppPreferenceModel,
    ): IDPHelper {
        return IDPHelper.getInstance(context, oneAppPreferenceModel)
    }

    @Provides
    fun provideIdpData(oneAppPreferenceModel: OneAppPreferenceModel): IDPData =
        IDPData.getInstance(
            oneAppPreferenceModel,
        )
}
