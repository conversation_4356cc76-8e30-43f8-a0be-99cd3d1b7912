package com.toyota.oneapp.component

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.constants.VIN_LIST_RETRY_TIMES
import com.toyota.oneapp.model.account.VehicleRegistrationPendingPayload
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.model.vehicle.VehiclelListResponse
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.manager.VehicleAPIManager
import com.toyota.oneapp.network.api.repository.VehiclePendingRegistrationRepository
import com.toyota.oneapp.network.callback.BaseCallback
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import toyotaone.commonlib.log.LogTool
import java.util.ArrayList
import javax.inject.Inject
import kotlin.RuntimeException

class VprsSyncHelper
    @Inject
    constructor(
        private val vehiclePendingRegistrationRepository: VehiclePendingRegistrationRepository,
        private val vehicleAPIManager: VehicleAPIManager,
        private val applicationData: ApplicationData,
    ) {
        companion object {
            val TAG = VprsSyncHelper::class.java.simpleName
        }

        fun sync(vehiclesList: List<VehicleInfo>? = null) {
            GlobalScope.launch { execute(vehiclesList) }
        }

        private suspend fun execute(vehiclesList: List<VehicleInfo>?) {
            LogTool.d(TAG, "VPRS sync stated")
            try {
                val allVehicles =
                    (
                        vehiclesList ?: getVehicleList().also {
                            applicationData.setVehicleList(ArrayList(it))
                        }
                    ).map { it.vin to it }
                        .toMap()
                val pendingVehicles = getRegistrationPendingVehicles().map { it.vin }
                processVehicles(allVehicles, pendingVehicles)
            } catch (ex: RuntimeException) {
                LogTool.w(TAG, "Error occurred while synchronizing vehicles in VRPS: ${ex.message}", ex)
            }
        }

        private suspend fun processVehicles(
            allVehicles: Map<String, VehicleInfo>,
            pendingVehicles: List<String>,
        ) {
            pendingVehicles.forEach { vin ->
                allVehicles[vin].let { vehicleInfo ->
                    vehicleInfo?.let {
                        completeRegistrationStatus(vin)
                    } ?: kotlin.run {
                        deleteVehicle(vin)
                    }
                }
            }
        }

        @Throws(RuntimeException::class)
        private suspend fun getVehicleList(): List<VehicleInfo> {
            val deferred = CompletableDeferred<List<VehicleInfo>>()
            vehicleAPIManager.sendGetVehicleListRequest(
                VIN_LIST_RETRY_TIMES,
                object : BaseCallback<VehiclelListResponse>() {
                    override fun onSuccess(response: VehiclelListResponse) {
                        deferred.complete(response.payload)
                    }

                    override fun onFailError(
                        httpCode: Int,
                        errorMsg: String?,
                    ) {
                        deferred.completeExceptionally(
                            RuntimeException(errorMsg ?: "Error occurred while getting vin list"),
                        )
                    }
                },
            )
            return deferred.await()
        }

        @Throws(RuntimeException::class)
        private suspend fun getRegistrationPendingVehicles(): List<VehicleRegistrationPendingPayload> =
            when (val resource = vehiclePendingRegistrationRepository.getPendingVehicles()) {
                is Resource.Success -> {
                    resource.data?.payload ?: emptyList()
                }

                else -> {
                    throw RuntimeException(
                        resource.error?.message ?: "Error occurred while getting pending vehicles",
                    )
                }
            }

        private suspend fun completeRegistrationStatus(vin: String) {
            LogTool.d(TAG, "Completing registration status in VPRS service for vin: $vin")
            vehiclePendingRegistrationRepository.completeRegistration(vin)
        }

        private suspend fun deleteVehicle(vin: String) {
            LogTool.d(TAG, "Deleting the vin form VPRS service: $vin")
            vehiclePendingRegistrationRepository.removeVehicle(vin)
        }
    }
