package com.toyota.oneapp.component.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils.Companion.KEY_INFO_ID
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import com.toyota.oneapp.util.ToyotaConstants.Companion.IS_FROM_API

class NetworkStateReceiver(private val networkStateChangeListener: NetworkStateChangeListener) : BroadcastReceiver() {
    override fun onReceive(
        context: Context,
        intent: Intent,
    ) {
        when (intent.action) {
            ToyotaConstants.CHECK_NETWORK_ACTION -> {
                val isFromAPICheck = intent.getBooleanExtra(IS_FROM_API, false)
                networkStateChangeListener.onNetworkStateChanged(
                    ToyUtil.hasNetwork(context),
                    isFromAPICheck,
                )
            }
            DigitalMopKeyUtils.ACTION_REMOTE_AUTH -> {
                networkStateChangeListener.dkAuthRequired(intent.getStringExtra(KEY_INFO_ID))
            } else -> {
                networkStateChangeListener.onNetworkStateChanged(ToyUtil.hasNetwork(context), false)
            }
        }
    }

    interface NetworkStateChangeListener {
        fun onNetworkStateChanged(
            isConnected: Boolean,
            isFromAPICheck: Boolean,
        )

        fun dkAuthRequired(keyInfoId: String?)
    }
}
