package com.toyota.oneapp.component.receiver

import android.bluetooth.BluetoothAdapter
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.toyota.oneapp.services.UserProfileTransferService
import com.toyota.oneapp.util.ToyotaConstants
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.permission.PermissionUtil

object BluetoothStateReceiver : BroadcastReceiver() {
    var isRegistered = false
        private set

    override fun onReceive(
        context: Context?,
        intent: Intent?,
    ) {
        if (null == context) return

        if (intent?.action == BluetoothAdapter.ACTION_STATE_CHANGED) {
            if (!PermissionUtil.hasBluetoothPermissions(context.applicationContext)) return

            when (intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)) {
                BluetoothAdapter.STATE_ON -> {
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Phone Bluetooth turned ON")
                    UserProfileTransferService.startService(context.applicationContext)
                }

                BluetoothAdapter.STATE_TURNING_OFF -> {
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Phone Bluetooth turned OFF")
                    UserProfileTransferService.stopService(context.applicationContext)
                }
            }
        }
    }

    fun register(context: Context?) {
        if (isRegistered) {
            return
        }

        try {
            context?.registerReceiver(
                this,
                IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED),
            )
            isRegistered = true
            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "BluetoothStateReceiver registered")
        } catch (e: Exception) {
            LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Failed to register receiver", e)
        }
    }

    fun unregister(context: Context?) {
        if (!isRegistered) return

        try {
            context?.unregisterReceiver(this)
            isRegistered = false
            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "BluetoothStateReceiver Unregistered")
        } catch (e: java.lang.Exception) {
            LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Failed to unregister receiver", e)
        }
    }

    interface BluetoothAdapterStateChangeListener {
        fun onBluetoothAdapterStateChange(isOn: Boolean)
    }
}
