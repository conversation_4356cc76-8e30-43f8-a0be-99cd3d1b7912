package com.toyota.oneapp.component

import android.content.Context
import android.location.Geocoder
import android.location.Location
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.util.ToyotaConstants
import org.jetbrains.anko.configuration
import toyotaone.commonlib.location.UserLocationProvider
import toyotaone.commonlib.location.UserLocationProvider.LocationCallback
import toyotaone.commonlib.location.UserLocationProvider.LocationRequestResult
import toyotaone.commonlib.log.LogTool
import java.util.Locale
import javax.inject.Inject

class LinkedAccountsEnabler
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val regionManager: RegionManager,
        private val context: Context,
    ) {
        companion object {
            val TAG = LinkedAccountsEnabler::class.java.simpleName
        }

        fun shouldEnableLinkedAccounts(
            userLocationProvider: UserLocationProvider,
            callback: (outcome: LinkedAccountsEnableOutcome) -> Unit,
        ) {
            if (applicationData.getVehicleList().isNullOrEmpty()) {
                LogTool.d(TAG, "Vehicles are empty")
                handleNoVehicleCase(userLocationProvider, callback)
            } else {
                checkIfLinkedAccountsEnabled(callback)
            }
        }

        private fun checkIfLinkedAccountsEnabled(callback: (outcome: LinkedAccountsEnableOutcome) -> Unit) {
            applicationData.getVehicleList()?.forEach { info ->
                if (info.isFeatureEnabled(Feature.LINKED_ACCOUNTS)) {
                    callback.invoke(LinkedAccountsEnableOutcome.ENABLE)
                    return
                }
            }
            callback.invoke(LinkedAccountsEnableOutcome.DISABLE)
        }

        private fun handleNoVehicleCase(
            userLocationProvider: UserLocationProvider,
            callback: (outcome: LinkedAccountsEnableOutcome) -> Unit,
        ) {
            val locationRequestResult = handleWithUserLocation(userLocationProvider, callback)
            LogTool.d(TAG, "The location result is : ${locationRequestResult.name}")
            if (locationRequestResult != LocationRequestResult.FETCHING_LOCATION) {
                handleWhenLocationNotAccessible(locationRequestResult, callback)
            }
        }

        private fun handleWhenLocationNotAccessible(
            locationRequestResult: LocationRequestResult,
            callback: (outcome: LinkedAccountsEnableOutcome) -> Unit,
        ) {
            val outcome =
                if (isDeviceLocaleUS()) {
                    LinkedAccountsEnableOutcome.ENABLE
                } else {
                    LinkedAccountsEnableOutcome.getLocationErrorOutcome(
                        locationRequestResult,
                    )
                }
            callback.invoke(outcome)
        }

        private fun isDeviceLocaleUS(): Boolean = regionManager.isRegionCodeAmerican(context.configuration.locales[0]?.country)

        private fun handleWithUserLocation(
            userLocationProvider: UserLocationProvider,
            callback: (outcome: LinkedAccountsEnableOutcome) -> Unit,
        ): LocationRequestResult {
            return userLocationProvider.getUserLocationAsync(
                object : LocationCallback {
                    override fun locationReceived(location: Location) {
                        LogTool.d(TAG, "Location is: $location")
                        try {
                            val addresses =
                                Geocoder(context, Locale.getDefault()).getFromLocation(
                                    location.latitude,
                                    location.longitude,
                                    1,
                                )
                            addresses?.firstOrNull()?.let {
                                LogTool.d(
                                    TAG,
                                    "Address country code: ${it.countryCode} and state code: ${it.adminArea}",
                                )
                                val outcome =
                                    if (it.countryCode.equals(
                                            ToyotaConstants.REGION_US,
                                            true,
                                        )
                                    ) {
                                        LinkedAccountsEnableOutcome.ENABLE
                                    } else {
                                        LinkedAccountsEnableOutcome.DISABLE_LOCATION_NOT_US
                                    }
                                callback.invoke(outcome)
                                return
                            }
                        } catch (e: Exception) {
                            LogTool.d(
                                TAG,
                                "Error occurred while fetching location details: ${e.message}",
                                e,
                            )
                        }
                        // Address could not be fetched or error occurred.
                        handleWhenLocationNotAccessible(
                            LocationRequestResult.FETCHING_LOCATION,
                            callback,
                        )
                    }
                },
            )
        }

        enum class LinkedAccountsEnableOutcome {
            ENABLE,
            DISABLE,
            DISABLE_VEHICLE_REGION_UNSUPPORTED,
            DISABLE_LOCATION_NOT_US,
            DISABLE_LOCATION_NOT_ENABLED_AND_FALLBACK_REGION_NOT_US,
            DISABLE_LOCATION_PERMISSION_NOT_GRANTED_AND_FALLBACK_REGION_NOT_US,
            ;

            companion object {
                fun getLocationErrorOutcome(locationRequestResult: LocationRequestResult): LinkedAccountsEnableOutcome =
                    when (locationRequestResult) {
                        LocationRequestResult.PERMISSION_NOT_GRANTED -> DISABLE_LOCATION_PERMISSION_NOT_GRANTED_AND_FALLBACK_REGION_NOT_US
                        LocationRequestResult.SYSTEM_LOCATION_DISABLED -> DISABLE_LOCATION_NOT_ENABLED_AND_FALLBACK_REGION_NOT_US
                        else -> DISABLE
                    }
            }
        }
    }
