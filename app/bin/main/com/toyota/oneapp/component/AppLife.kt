/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.component

import android.app.Activity
import android.app.Application
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.text.TextUtils
import com.toyota.oneapp.app.ToyotaApplication
import com.toyota.oneapp.digitalkey.ui.DigitalKeySetupActivity
import com.toyota.oneapp.features.dashboard.dashboard.presentation.OADashboardActivity
import com.toyota.oneapp.model.dashboard.BackToForeground
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.accountsettings.BiometricActivity
import com.toyota.oneapp.ui.accountsettings.SecuritySettingsExpActivity
import com.toyota.oneapp.ui.splash.SplashActivity
import com.toyota.oneapp.util.BiometryUtil
import com.toyota.oneapp.util.FeatureFlagUtil
import com.toyota.oneapp.xcapp.XcappLockScreenActivity
import com.toyota.oneapp.xcapp.XcappLockScreenActivity.Companion.showLockScreen
import com.toyota.oneapp.xcapp.manager.XcappManagerProvider.isXcappInitialized
import com.toyota.oneapp.xcapp.manager.XcappManagerProvider.xcappManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.forgerock.android.auth.ui.FRMainActivity
import toyotaone.commonlib.eventbus.RxBus

/**
 * Created by Daniel on 2019-07-07.
 */
class AppLife(
    var oneAppPreferenceModel: OneAppPreferenceModel,
) : Application
        .ActivityLifecycleCallbacks {
    private var mInBackgroundTs: Long = 0
    private var showBiometricDelayMillis = 15 * 60 * 1000.toLong()
    private var showLockScreen = false

    // These activities will not be blocked by the biometric feature
    private val unprotectedActivityList =
        listOf(
            SplashActivity::class.java.simpleName,
            FeatureFlagUtil.loginActivity.simpleName,
            DigitalKeySetupActivity::class.java.simpleName,
            BiometricActivity::class.java.simpleName,
            FRMainActivity::class.java.simpleName,
            XcappLockScreenActivity::class.java.simpleName,
            "InteractiveManualActivity",
            // TODO When changing to flutter sign in, it should comment back
            // DashboardFlutterActivity::class.java.simpleName
        )

    override fun onActivityCreated(
        activity: Activity,
        savedInstanceState: Bundle?,
    ) {
        // This method is intentionally left empty.
        // We don't need any specific logic when the activity is created,
        // but we're required to override this method as part of the interface implementation.
    }

    override fun onActivityStarted(activity: Activity) {
        if (activateActivityStartStopCount == 0) {
            /** Adding a delay here, since onActivityStarted() callback right away after
             coming back from setting screen so it won't allow to set the flag to true
             on result launcher. **/
            CoroutineScope(Dispatchers.Main).launch {
                delay(500)
                if (OADashboardActivity.isReturningFromSettings) {
                    OADashboardActivity.isReturningFromSettings = false
                    return@launch
                }
                val currentElapsedTime = SystemClock.elapsedRealtime()

                if (!unprotectedActivityList.contains(activity.javaClass.simpleName) &&
                    oneAppPreferenceModel.isBiometricEnabled() &&
                    BiometryUtil.canAuthenticate(activity.applicationContext) &&
                    currentElapsedTime - mInBackgroundTs > showBiometricDelayMillis
                ) {
                    val intent = Intent(activity, BiometricActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    activity.startActivity(intent)
                    mInBackgroundTs = 0
                }

                if (isXcappInitialized() && xcappManager.isConnected()) {
                    showLockScreen = true
                }
            }
        }
        activateActivityStartStopCount++
    }

    override fun onActivityResumed(activity: Activity) {
        resumedActivityName = activity.javaClass.simpleName
        if (activateActivityCount == 0 &&
            (
                TextUtils.equals(
                    activity.javaClass.simpleName,
                    OADashboardActivity::class.java.simpleName,
                )
            )
        ) {
            RxBus.get().post(BackToForeground(false))
        }
        if (showLockScreen) {
            showLockScreen = false
            showLockScreen(activity)
        }
        activateActivityCount++
    }

    override fun onActivityPaused(activity: Activity) {
        activateActivityCount--
    }

    override fun onActivityStopped(activity: Activity) {
        activateActivityStartStopCount--
        if (activateActivityStartStopCount == 0) {
            mInBackgroundTs = SystemClock.elapsedRealtime()
            if (!unprotectedActivityList.contains(activity.javaClass.simpleName) &&
                BiometryUtil.canAuthenticate(ToyotaApplication.getAppContext()) &&
                oneAppPreferenceModel.isBiometricEnabled()
            ) {
                when (oneAppPreferenceModel.getBiometricSettingsTime()) {
                    SecuritySettingsExpActivity.IMMEDIATE -> showBiometricDelayMillis = 100
                    SecuritySettingsExpActivity.ONE_MIN ->
                        showBiometricDelayMillis =
                            60 * 1000.toLong()

                    SecuritySettingsExpActivity.FIVE_MIN ->
                        showBiometricDelayMillis =
                            5 * 60 * 1000.toLong()

                    SecuritySettingsExpActivity.FIFTEEN_MIN ->
                        showBiometricDelayMillis =
                            15 * 60 * 1000.toLong()

                    SecuritySettingsExpActivity.ONE_HOUR ->
                        showBiometricDelayMillis =
                            60 * 60 * 1000.toLong()

                    SecuritySettingsExpActivity.SIX_HOUR ->
                        showBiometricDelayMillis =
                            6 * 60 * 60 * 1000.toLong()

                    SecuritySettingsExpActivity.TWELVE_HOUR ->
                        showBiometricDelayMillis =
                            12 * 60 * 60 * 1000.toLong()
                }
            }
        }
    }

    override fun onActivitySaveInstanceState(
        activity: Activity,
        outState: Bundle,
    ) {
        // This method is intentionally left empty.
        // We don't need to save any additional state in this implementation,
        // but we're required to override this method as part of the interface contract.
    }

    override fun onActivityDestroyed(activity: Activity) {
        // This method is intentionally left empty.
        // We don't need to save any additional state in this implementation,
        // but we're required to override this method as part of the interface contract.
    }

    companion object {
        var activateActivityCount = 0
        private var activateActivityStartStopCount = 0
        var resumedActivityName: String? = null
    }
}
