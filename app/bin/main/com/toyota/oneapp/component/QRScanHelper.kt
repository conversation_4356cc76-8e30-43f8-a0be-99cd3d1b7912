/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.component

import androidx.appcompat.app.AppCompatActivity
import com.google.gson.JsonParser
import com.toyota.oneapp.R
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.core.BuildWrapper
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.network.api.repository.IdpRepository
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.BiometryUtil.canAuthenticate
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.forgerock.android.auth.ui.FRNative
import org.forgerock.android.auth.ui.FRNative.biometricSupport
import org.forgerock.android.auth.ui.FRNative.login
import org.forgerock.android.auth.ui.FRNative.pushTokenId
import org.forgerock.android.auth.ui.FRNativeResponse
import org.forgerock.android.auth.ui.FRNativeResultListener
import org.forgerock.android.auth.ui.entity.Constants
import org.jsoup.Jsoup
import toyotaone.commonlib.dialog.DialogUtil
import toyotaone.commonlib.dialog.OnCusDialogInterface
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

class QRScanHelper
    @Inject
    constructor(
        private val idpRepository: IdpRepository,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val buildWrapper: BuildWrapper,
    ) {
        companion object {
            val TAG = QRScanHelper::class.java.simpleName
            private const val HTML_PAGE_TAG = "pageData"
            private const val HTML_DONE_TAG = "done"
            private const val HTML_CLIENT_ID_TAG = "clientId"
            private const val HTML_SCRIPT_TAG = "script"
            private const val HTML_AUTH_DATA_TAG = "oauth2Data"
            private const val SUBARU_CLINENT_ID = "subaru"
        }

        @OptIn(DelicateCoroutinesApi::class)
        fun processUserCode(
            activity: AppCompatActivity,
            userCode: String,
            callback: (result: ResultType) -> Unit,
        ) {
            GlobalScope.launch {
                if (isUserSessionValid()) {
                    fireAuthorizationRequest(userCode, callback)
                } else {
                    launchLoginPopup(activity, userCode, callback)
                }
            }
        }

        private fun launchLoginPopup(
            activity: AppCompatActivity,
            userCode: String,
            callback: (result: ResultType) -> Unit,
        ) {
            activity.runOnUiThread {
                DialogUtil.showMessageDialog(
                    activity,
                    activity.getString(R.string.verification_title),
                    activity.getString(R.string.verification_message),
                    activity.getString(R.string.Common_confirm),
                    activity.getString(R.string.Common_cancel),
                    object : OnCusDialogInterface {
                        override fun onConfirmClick() {
                            doSignIn(
                                activity,
                                object : FRNativeResultListener {
                                    override fun onResult(result: FRNativeResponse) {
                                        onSignInResultReceived(result, userCode, callback)
                                    }
                                },
                            )
                        }

                        override fun onCancelClick() {
                            callback.invoke(ResultType.IDP_VALIDATION_CANCELLED)
                        }
                    },
                    false,
                )
            }
        }

        private fun doSignIn(
            activity: AppCompatActivity,
            listener: FRNativeResultListener,
        ) {
            pushTokenId = oneAppPreferenceModel.getDeviceToken()
            biometricSupport = canAuthenticate(activity)
            FRNative.setCurrentUserPersist(true)
            activity.runOnUiThread { login(activity, listener, forceLogin = true) }
        }

        @OptIn(DelicateCoroutinesApi::class)
        private fun onSignInResultReceived(
            result: FRNativeResponse,
            userCode: String,
            callback: (result: ResultType) -> Unit,
        ) {
            if (result.success && result.accessToken != null) {
                IDPData.getInstance(oneAppPreferenceModel).handleAccessToken(
                    result.accessToken!!,
                    oneAppPreferenceModel,
                )
                GlobalScope.launch { fireAuthorizationRequest(userCode, callback) }
            } else {
                val error =
                    when (result.message) {
                        Constants.userAuthError -> ResultType.IDP_TAMPERED_LOGIN
                        null -> ResultType.IDP_VALIDATION_CANCELLED
                        else -> ResultType.IDP_VALIDATION_FAILED
                    }
                callback.invoke(error)
            }
        }

        private suspend fun fireAuthorizationRequest(
            userCode: String,
            callback: (result: ResultType) -> Unit,
        ) {
            val ssoToken = FRNative.getCurrentUser()?.idToken?.token ?: return
            var result = ResultType.QR_AUTH_FAILED
            val clientId =

                verifyVehicleBrand(userCode, ssoToken)

            if (clientId != ToyotaConstants.EMPTY_STRING && checkValidCodeForApp(clientId)) {
                result = startVehicleRegistration(userCode, ssoToken)
            }
            callback.invoke(result)
        }

        fun checkValidCodeForApp(clientId: String): Boolean {
            val isSubaru = buildWrapper.isSubaruApp()
            return (isSubaru && clientId.contains(SUBARU_CLINENT_ID, true)) ||
                (!isSubaru && !clientId.contains(SUBARU_CLINENT_ID, true))
        }

        private suspend fun verifyVehicleBrand(
            userCode: String,
            ssoToken: String,
        ): String =
            when (val resource = idpRepository.verifyVehicleBrand(userCode, ssoToken)) {
                is Resource.Success -> {
                    try {
                        val responseBody = resource.data?.string()
                        responseBody?.let { body ->
                            parseHtmlResponseForClientId(body)
                        } ?: ToyotaConstants.EMPTY_STRING
                    } catch (t: Throwable) {
                        LogTool.e(
                            TAG,
                            "Error while reading vehicle registration response: ${t.message}",
                            t,
                        )
                        ToyotaConstants.EMPTY_STRING
                    }
                }

                else -> ToyotaConstants.EMPTY_STRING
            }

        private suspend fun startVehicleRegistration(
            userCode: String,
            ssoToken: String,
        ): ResultType =
            when (val resource = idpRepository.startVehicleRegistration(userCode, ssoToken)) {
                is Resource.Success -> {
                    try {
                        val responseBody = resource.data?.string()
                        responseBody?.let { body -> parseResponse(body) } ?: ResultType.QR_AUTH_FAILED
                    } catch (t: Throwable) {
                        LogTool.e(
                            TAG,
                            "Error while reading vehicle registration response: ${t.message}",
                            t,
                        )
                        ResultType.QR_AUTH_FAILED
                    }
                }

                else -> ResultType.QR_AUTH_FAILED
            }

        private fun parseHtmlResponseForClientId(response: String): String {
            val document = Jsoup.parse(response)
            val scripts = document.select(HTML_SCRIPT_TAG)
            if (scripts.firstOrNull()?.data()?.contains(HTML_PAGE_TAG) == true) {
                val jsonData =
                    scripts[0]
                        .data()
                        .replace("$HTML_PAGE_TAG = ", ToyotaConstants.EMPTY_STRING)
                        .replace(";", ToyotaConstants.EMPTY_STRING)

                val jsonObject =
                    JsonParser
                        .parseString(
                            jsonData,
                        ).asJsonObject
                        .getAsJsonObject(HTML_AUTH_DATA_TAG)
                if (jsonObject.has(HTML_CLIENT_ID_TAG)) {
                    return jsonObject.get(HTML_CLIENT_ID_TAG).asString
                }
            }
            return ToyotaConstants.EMPTY_STRING
        }

        private fun parseResponse(response: String): ResultType =
            if (parseHtmlResponse(
                    response,
                )
            ) {
                ResultType.SUCCESS
            } else {
                ResultType.INVALID_QR
            }

        private fun parseHtmlResponse(response: String): Boolean {
            val document = Jsoup.parse(response)
            val scripts = document.select(HTML_SCRIPT_TAG)
            if (scripts.firstOrNull()?.data()?.contains(HTML_PAGE_TAG) == true) {
                val responseBody = scripts[0].data()
                return responseBody.contains(HTML_DONE_TAG)
            }
            return false
        }

        private suspend fun isUserSessionValid(): Boolean {
            val ssoToken = FRNative.getCurrentUser()?.idToken?.token
            return ssoToken?.let {
                when (val resource = idpRepository.getSsoTokenSessionValidity(it)) {
                    is Resource.Success -> {
                        resource.data?.valid == true
                    }

                    else -> false
                }
            } == true
        }

        enum class ResultType {
            SUCCESS,
            INVALID_QR,
            QR_AUTH_FAILED,
            IDP_VALIDATION_FAILED,
            IDP_TAMPERED_LOGIN,
            IDP_VALIDATION_CANCELLED,
        }
    }
