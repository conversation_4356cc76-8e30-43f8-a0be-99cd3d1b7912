package com.toyota.oneapp.component

import android.content.Context
import com.toyota.oneapp.core.ApplicationContext
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject

@InstallIn(SingletonComponent::class)
@Module
class DigitalKeyModule {
    @Provides
    @Inject
    fun provideDigitalKeyUtils(
        @ApplicationContext context: Context,
    ): DigitalMopKeyUtils {
        return DigitalMopKeyUtils.getInstance(context)
    }
}
