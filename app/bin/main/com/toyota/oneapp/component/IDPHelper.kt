package com.toyota.oneapp.component

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import com.auth0.android.jwt.JWT
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.analytics.AppLogsRecordData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.LoginActivity
import com.toyota.oneapp.util.FeatureFlagUtil
import com.toyota.oneapp.util.ToyotaConstants
import org.forgerock.android.auth.AccessToken
import org.forgerock.android.auth.exception.AuthenticationRequiredException
import org.forgerock.android.auth.ui.FRNative
import org.forgerock.android.auth.ui.FRNativeResponse
import org.forgerock.android.auth.ui.FRNativeResultListener
import org.forgerock.android.auth.ui.FR_REFRESH_TOKEN_INVALID_GRANT
import org.json.JSONObject
import toyotaone.commonlib.log.LogTool

class IDPHelper private constructor(
    private val context: Context,
    private val oneAppPreferenceModel: OneAppPreferenceModel,
) {
    var analyticsLogger: AnalyticsLogger? = null

    private val logStringBuilder = StringBuilder()
    private var logBuilder: AppLogsRecordData? = null

    private fun isTokenValid(): Boolean {
        val accessToken = IDPData.getInstance(oneAppPreferenceModel).accessToken
        val tokenExpireTime: Long = IDPData.getInstance(oneAppPreferenceModel).accessTokenExpireTime
        return !TextUtils.isEmpty(accessToken) && System.currentTimeMillis() < tokenExpireTime
    }

    private fun isLimitedTokenValid(): Boolean {
        val limitedToken = IDPData.getInstance(oneAppPreferenceModel).limitedToken
        val tokenExpireTime: Long = IDPData.getInstance(oneAppPreferenceModel).limitedTokenExpireTime
        return limitedToken.isNotBlank() && System.currentTimeMillis() < tokenExpireTime
    }

    private fun isRefreshTokenAvailable(): Boolean {
        val refreshToken = IDPData.getInstance(oneAppPreferenceModel).refreshToken
        return !TextUtils.isEmpty(refreshToken) && !IDPData.getInstance(oneAppPreferenceModel).isRefreshTokenExpired()
    }

    private fun uploadRefreshTokenLog(exception: Exception) {
        val tag =
            if (exception.message?.contains(FR_REFRESH_TOKEN_INVALID_GRANT) == true) {
                AnalyticsEvent.LOGIN_REFRESH_TOKEN_INVALID
            } else {
                AnalyticsEvent.LOGIN_UNABLE_TO_GET_TOKENS
            }
    }

    private fun addRefreshTokenFailedLog(logItem: AppLogsRecordData) {
        logStringBuilder.apply {
            append(logItem.toLogString())
            append("\n")
        }
        logBuilder?.let { appLog ->
            appLog.apply {
                statusCode = statusCode + "," + logItem.statusCode
                retries = retries?.put(logItem.retryAttempt.toString(), logItem.response ?: "")
            }
        }

        if (logBuilder == null) {
            logBuilder =
                logItem.apply {
                    retries = JSONObject().put(logItem.retryAttempt.toString(), logItem.response ?: "")
                }
        }
    }

    fun clearRefreshTokenLogCache() {
        logStringBuilder.clear()
        logBuilder = null
    }

    @Synchronized
    @Throws(java.lang.Exception::class)
    fun getLimitedAccessToken(): String? {
        return if (isLimitedTokenValid()) {
            IDPData.getInstance(oneAppPreferenceModel).limitedToken
        } else {
            return try {
                if (isRefreshTokenAvailable()) {
                    val frAccessToken =
                        FRNative
                            .refreshTokenRetryTimes(true, RETRY_TIMES, onError = { times, message, httpInfo ->
                                addRefreshTokenFailedLog(
                                    AppLogsRecordData(
                                        retryAttempt = times,
                                        eventName = AnalyticsEvent.LOGIN_RETRY_ATTEMPTS_LOGS.eventName,
                                        response = message,
                                        statusCode = "${httpInfo?.httpCode ?: 0}",
                                        url = httpInfo?.url ?: ToyotaConstants.EMPTY_STRING,
                                    ),
                                )
                            })
                            ?.accessToken
                    frAccessToken?.let {
                        saveLimitedAccessToken(it)
                        LogTool.d(TAG, "ATT TOKEN : ${it.accessToken}")
                        it.accessToken
                    }
                } else {
                    throw AuthenticationRequiredException(FR_REFRESH_TOKEN_INVALID_GRANT)
                }
            } catch (e: Exception) {
                forceUserToLogin(e)
                analyticsLogger?.logEvent(AnalyticsEvent.LOGIN_RENEW_TOKEN_ERROR)
                null
            }
        }
    }

    @Throws(java.lang.Exception::class)
    fun getAccessToken(forceFetch: Boolean? = false): String? =
        if (forceFetch != true && isTokenValid()) {
            IDPData.getInstance(oneAppPreferenceModel).accessToken
        } else {
            try {
                if (isRefreshTokenAvailable()) {
                    clearRefreshTokenLogCache()
                    val frAccessToken =
                        FRNative
                            .refreshTokenRetryTimes(false, RETRY_TIMES, onError = { times, message, httpInfo ->
                                addRefreshTokenFailedLog(
                                    AppLogsRecordData(
                                        retryAttempt = times,
                                        eventName = AnalyticsEvent.LOGIN_RETRY_ATTEMPTS_LOGS.eventName,
                                        response = message,
                                        statusCode = "${httpInfo?.httpCode ?: 0}",
                                        url = httpInfo?.url ?: "",
                                    ),
                                )
                            })
                            ?.accessToken
                    if ((frAccessToken?.expiration?.time ?: 0) < System.currentTimeMillis()) {
                        analyticsLogger?.logEvent(AnalyticsEvent.LOGIN_RECEIVED_TOKEN_EXPIRED)
                    }
                    if (!frAccessToken?.refreshToken.isNullOrBlank()) {
                        consumeFRAccessToken(frAccessToken)
                    }
                    frAccessToken?.accessToken
                } else {
                    throw AuthenticationRequiredException(FR_REFRESH_TOKEN_INVALID_GRANT)
                }
            } catch (e: Exception) {
                uploadRefreshTokenLog(e)
                forceUserToLogin(e)
                analyticsLogger?.logEvent(AnalyticsEvent.LOGIN_RENEW_TOKEN_ERROR)
                null
            }
        }

    fun refreshFRAccessTokenInLogin(
        retryTimes: Int = RETRY_TIMES,
        successCallback: ((tokenResponse: AccessToken?) -> Unit)? = null,
        retryCallback: ((retryTimes: Int, e: Exception?) -> Unit)? = null,
        invalidRefreshTokenCallback: ((e: Exception?) -> Unit)? = null,
        unknownExceptionCallback: ((e: Exception?) -> Unit)? = null,
    ) {
        if (retryTimes > 0) {
            FRNative.refreshAccessToken(
                object : FRNativeResultListener {
                    override fun onResult(result: FRNativeResponse) {
                        if (result.success) {
                            consumeFRAccessToken(result.accessToken)
                            successCallback?.invoke(result.accessToken)
                        } else {
                            addRefreshTokenFailedLog(
                                AppLogsRecordData(
                                    retryAttempt = retryTimes,
                                    eventName = AnalyticsEvent.LOGIN_RETRY_ATTEMPTS_LOGS.eventName,
                                    timeStamp = System.currentTimeMillis(),
                                    statusCode = "${result.httpErrorInfo?.httpCode ?: 0}",
                                    url =
                                        result.httpErrorInfo?.url
                                            ?: ToyotaConstants.EMPTY_STRING,
                                    response = result.httpErrorInfo?.responseBody,
                                ),
                            )
                            if (result.message?.contains(FR_REFRESH_TOKEN_INVALID_GRANT) == true) {
                                // Refresh token invalid
                                analyticsLogger?.logEvent(AnalyticsEvent.LOGIN_RENEW_TOKEN_ERROR)
                                invalidRefreshTokenCallback?.invoke(Exception(result.message))
                            } else {
                                if (retryTimes <= 1) {
                                    // At last time, still get exception
                                    unknownExceptionCallback?.invoke(Exception(result.message))
                                } else {
                                    // Others unknown exception, need to retry.
                                    retryCallback?.invoke(retryTimes, result.exception)
                                    refreshFRAccessTokenInLogin(
                                        retryTimes - 1,
                                        successCallback,
                                        retryCallback,
                                        invalidRefreshTokenCallback,
                                        unknownExceptionCallback,
                                    )
                                }
                            }
                        }
                    }
                },
            )
        }
    }

    private fun consumeFRAccessToken(accessToken: AccessToken?) {
        with(IDPData.getInstance(oneAppPreferenceModel)) {
            this.accessToken = accessToken?.accessToken ?: ToyotaConstants.EMPTY_STRING
            refreshToken = accessToken?.refreshToken ?: refreshToken
            idToken = accessToken?.idToken
            val defaultTimestamp = System.currentTimeMillis() + ToyotaConstants.TOKEN_EXPIRE_TIME_BUFF_TIME.toLong()
            val expireMin: Long =
                if (accessToken?.expiration != null) {
                    accessToken.expiration.time
                } else {
                    defaultTimestamp
                }
            // mins
            val expireTime = expireMin - (if (expireMin > defaultTimestamp) ToyotaConstants.TOKEN_EXPIRE_TIME_BUFF_TIME.toLong() else 0)
            accessTokenExpireTime = expireTime
            limitedToken = ToyotaConstants.EMPTY_STRING
            limitedTokenExpireTime = 0
            LogTool.d(TAG, String.format("[ updated refresh Token: %s]", refreshToken))
            LogTool.d(TAG, String.format("[ updated access Token: %s]", this.accessToken))
            LogTool.d(TAG, String.format("[ updated id Token: %s]", idToken))
        }
    }

    private fun saveLimitedAccessToken(accessToken: AccessToken) {
        IDPData.getInstance(oneAppPreferenceModel).limitedToken = accessToken.accessToken
        val defaultTimestamp = System.currentTimeMillis() + ToyotaConstants.TOKEN_EXPIRE_TIME_BUFF_TIME.toLong()
        val expireMin = accessToken.expiration?.time ?: defaultTimestamp
        val expireTime = if (expireMin > defaultTimestamp) expireMin - ToyotaConstants.TOKEN_EXPIRE_TIME_BUFF_TIME.toLong() else expireMin
        IDPData.getInstance(oneAppPreferenceModel).limitedTokenExpireTime = expireTime
    }

    private fun forceUserToLogin() {
        val intent =
            Intent(context, LoginActivity::class.java)
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                .apply {
                    putExtra(KEY_SHOW_GENERIC_ERROR, true)
                    putExtra(KEY_LOGOUT, true)
                }
        context.startActivity(intent)
    }

    private fun forceUserToLogin(exception: Exception) {
        if (exception.message?.contains(FR_REFRESH_TOKEN_INVALID_GRANT) == true) {
            IDPData.getInstance(oneAppPreferenceModel).clearToken()
            if (AppLife.resumedActivityName != FeatureFlagUtil.loginActivity.simpleName &&
                AppLife.resumedActivityName != FeatureFlagUtil.frMainActivity.simpleName
            ) {
                forceUserToLogin()
            }
        }
    }

    companion object : SingletonHolderTwo<IDPHelper, Context, OneAppPreferenceModel>(::IDPHelper) {
        private const val TAG = "IDPHelper"
        const val RETRY_TIMES = 3
        const val KEY_SHOW_GENERIC_ERROR = "show_generic_error"
        const val KEY_LOGOUT = "Logout"

        @JvmStatic
        fun isRefreshTokenValid(
            interval: Int,
            oneAppPreferenceModel: OneAppPreferenceModel,
        ): Boolean {
            val jwt =
                JWT(
                    IDPData.getInstance(oneAppPreferenceModel).refreshToken ?: ToyotaConstants.EMPTY_STRING,
                )
            val createdAt = jwt.getClaim("iat").asLong()
            return createdAt != null &&
                createdAt.plus(interval) >
                System.currentTimeMillis().div(
                    1000,
                )
        }
    }
}
