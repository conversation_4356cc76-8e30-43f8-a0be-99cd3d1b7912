package com.toyota.oneapp.services

import android.annotation.SuppressLint
import android.app.ForegroundServiceStartNotAllowedException
import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothGattService
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothProfile
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanFilter
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.work.CoroutineWorker
import androidx.work.ForegroundInfo
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import androidx.work.WorkRequest
import androidx.work.WorkerParameters
import com.toyota.ctp.v1.ProfileServiceServer
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ToyotaApplication
import com.toyota.oneapp.network.api.manager.UserProfileAPIManager
import com.toyota.oneapp.threads.BluetoothSPPConnectThread
import com.toyota.oneapp.ui.splash.SplashActivity
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyotaConstants
import dagger.hilt.android.AndroidEntryPoint
import io.grpc.stub.StreamObserver
import org.jetbrains.anko.runOnUiThread
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.permission.PermissionUtil
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.nio.ByteBuffer
import java.util.Timer
import java.util.UUID
import javax.inject.Inject
import kotlin.concurrent.schedule

@AndroidEntryPoint
class UserProfileTransferService : Service() {
    private val btAdapter: BluetoothAdapter by lazy {
        (getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager).adapter
    }
    private val bleScanner by lazy { btAdapter.bluetoothLeScanner }
    private val scanFilters by lazy { setupScanFilters() }
    private val foregroundNotification: Notification by lazy {
        createForegroundNotification(
            applicationContext,
        )
    }
    private lateinit var btGatt: BluetoothGatt
    private lateinit var gattReadCharacteristic: BluetoothGattCharacteristic
    private lateinit var upTxCharacteristicUuid: UUID
    private lateinit var upServiceUuid: UUID
    private lateinit var cachedHeader: ByteArray
    private lateinit var sppThread: BluetoothSPPConnectThread
    private var cachedProfile: ByteArray? = null
    private var packetToTransfer: ByteArray? = null
    private var isConnecting = false
    private var shouldRestartScan = true
    private var isLongCharacteristicWriteInProgress = false
    private var nextByteIndex = 0
    private var scanStartTimes = mutableListOf<Long>()

    // Queue for keeping track of scan frequency limitations
    @Inject
    lateinit var userProfileAPIManager: UserProfileAPIManager

    @Inject
    lateinit var analyticsLogger: AnalyticsLogger

    companion object {
        private const val MAX_PACKET_SIZE = 512
        private const val SIGNAL_THRESHOLD = -80
        private const val MAX_COMMAND_SIZE = 7 // BLE commands may have a 1 byte payload
        private const val COMMAND_SIZE = 6 // The size of command without payload
        private const val COMMAND_FIRST_BYTE_INDEX = 4
        private const val COMMAND_SECOND_BYTE_INDEX = 5
        private const val PACKET_HEADER_SIZE = 10
        private const val MANF_DATA_MASK_SIZE = 19
        private const val MANF_DATA_BEGIN_INDEX = 2
        private const val CONTINUATION_FLAG_INDEX = 6
        private const val INITIATE_FLAG_BYTE = 0x00.toByte()
        private const val CONTINUE_FLAG_BYTE = 0x01.toByte()
        private const val COMMAND_ID_BYTE_PACKET_1 = 0X10.toByte()
        private const val COMMAND_ID_BYTE_PACKET_2 = 0X11.toByte()
        private const val NOTIFICATION_ID = 101
        private const val MAX_SCANS_ALLOWED_IN_PERIOD = 5 // Defined by Android
        private const val EXCESSIVE_SCANNING_PERIOD_MS = 30 * 1000 // Defined by Android

        @JvmStatic
        fun startService(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                WorkManager
                    .getInstance(context)
                    .enqueue(UserProfileServiceLauncherWorker.newInstance())
            } else {
                stopService(context)
                (context.applicationContext as ToyotaApplication).stopBeaconMonitoring()
                Handler(Looper.getMainLooper()).post {
                    ContextCompat.startForegroundService(
                        context,
                        Intent(context, UserProfileTransferService::class.java),
                    )
                }
            }
        }

        @JvmStatic
        fun stopService(context: Context) {
            context.stopService(Intent(context, UserProfileTransferService::class.java))
        }

        @JvmStatic
        fun createForegroundNotification(context: Context): Notification {
            val applicationContext = context.applicationContext
            val startIntent = Intent(applicationContext, SplashActivity::class.java)
            val pendingIntent =
                PendingIntent.getActivity(
                    applicationContext,
                    0,
                    startIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                )

            val builder =
                NotificationCompat
                    .Builder(
                        applicationContext,
                        "${applicationContext.packageName}.${ToyotaConstants.BLE_NOTIFICATION_CHANNEL_ID_SUFFIX}",
                    )
                    // The channel should has been created on ToyotaApplication during configuration
                    .setContentIntent(pendingIntent)
                    .setSmallIcon(R.drawable.ic_notification)
                    .setContentTitle(
                        applicationContext.getString(R.string.bluetooth_notification_ble_scanning_title),
                    )

            return builder.build()
        }
    }

    class UserProfileServiceLauncherWorker(
        context: Context,
        workerParams: WorkerParameters,
    ) : CoroutineWorker(context, workerParams) {
        companion object {
            @JvmStatic
            fun newInstance(): WorkRequest {
                val builder = OneTimeWorkRequestBuilder<UserProfileServiceLauncherWorker>()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    builder.setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
                }

                return builder.build()
            }
        }

        override suspend fun getForegroundInfo(): ForegroundInfo =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                ForegroundInfo(
                    NOTIFICATION_ID,
                    createForegroundNotification(applicationContext),
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE,
                )
            } else {
                ForegroundInfo(NOTIFICATION_ID, createForegroundNotification(applicationContext))
            }

        @RequiresApi(Build.VERSION_CODES.S)
        override suspend fun doWork(): Result =
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    setForeground(getForegroundInfo())
                }
                stopService(applicationContext)
                (applicationContext as ToyotaApplication).stopBeaconMonitoring()
                Handler(Looper.getMainLooper()).post {
                    ContextCompat.startForegroundService(
                        applicationContext,
                        Intent(applicationContext, UserProfileTransferService::class.java),
                    )
                }

                Result.success()
            } catch (e: java.lang.Exception) {
                val logMessage =
                    if (e is ForegroundServiceStartNotAllowedException) {
                        "Attempted to start UP service from background"
                    } else {
                        e.message
                    }
                LogTool.e(ToyotaConstants.BLUETOOTH_TAG, logMessage, e)

                Result.failure()
            }
    }

    override fun onBind(intent: Intent): IBinder? = null

    override fun onStartCommand(
        intent: Intent?,
        flags: Int,
        startId: Int,
    ): Int {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            startForeground(
                ToyotaConstants.BLE_NOTIFICATION_FOREGROUND_ID,
                foregroundNotification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE,
            )
        } else {
            startForeground(ToyotaConstants.BLE_NOTIFICATION_FOREGROUND_ID, foregroundNotification)
        }

        btAdapter.run {
            LogTool.d(
                ToyotaConstants.BLUETOOTH_TAG,
                """ UP BT service started
                | ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})
                | ${Build.MANUFACTURER} ${Build.MODEL} Android:${Build.VERSION.RELEASE}
                """.trimMargin(),
            )
            if (isEnabled && PermissionUtil.hasBluetoothPermissions(applicationContext)) {
                shouldRestartScan = true
                startScanning()

                sppThread =
                    BluetoothSPPConnectThread(
                        this,
                        userProfileAPIManager,
                        analyticsLogger,
                    )
                sppThread.start()
            }
        }
        return START_STICKY
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Service removed with task")
        disconnect()
    }

    override fun onDestroy() {
        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Service stopped")
        super.onDestroy()
    }

    // Device scan callback.
    private val leScanCallback =
        object : ScanCallback() {
            override fun onScanResult(
                callbackType: Int,
                result: ScanResult,
            ) {
                result.scanRecord?.manufacturerSpecificData?.valueAt(0)?.let { manfData ->
                    val uuid = manfData.copyOfRange(MANF_DATA_BEGIN_INDEX, MANF_DATA_MASK_SIZE)
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        """Device detected
                | Device: ${result.device?.address}
                | Data: ${ToyUtil.bytesToHexString(manfData)}
                | UUID: ${ToyUtil.bytesToHexString(uuid)}
                | RSSI: ${result.rssi}
                        """.trimMargin(),
                    )
                }
                // TODO: Look for a way to opt out of "Bluetooth scanning" setting instead of checking for adapter status
                if (!isConnecting && result.rssi >= SIGNAL_THRESHOLD && btAdapter.isEnabled) {
                    connectToDevice(result.device)
                }
            }

            override fun onScanFailed(errorCode: Int) {
                LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Failed to start scan: error $errorCode")
            }
        }

    // Device connect call back
    private val btleGattCallback =
        object : BluetoothGattCallback() {
            override fun onCharacteristicChanged(
                gatt: BluetoothGatt,
                characteristic: BluetoothGattCharacteristic,
            ) {
                LogTool.d(
                    ToyotaConstants.BLUETOOTH_TAG,
                    "${characteristic.uuid} onCharacteristicChanged",
                )
                val notifiedString = ToyUtil.bytesToHexString(characteristic.value)
                LogTool.d(
                    ToyotaConstants.BLUETOOTH_TAG,
                    """Characteristic's data received
                |device: ${gatt.device.address}
                |characteristic: ${characteristic.uuid}
                |message: $notifiedString
                    """.trimMargin(),
                )

                if (characteristic.value.size >= COMMAND_SIZE) {
                    handleCommand(characteristic)
                }
            }

            @SuppressLint("MissingPermission") // Service only runs after permission check
            override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                if (BuildConfig.DEBUG) {
                    val statusString =
                        if (status == BluetoothGatt.GATT_SUCCESS) "succeeded" else "failed"
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Gatt $statusString")
                    if (status != BluetoothGatt.GATT_SUCCESS) {
                        LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Gatt failed with status: $status")
                    }
                }

                // this will get called when a device connects or disconnects
                isConnecting =
                    when (newState) {
                        BluetoothProfile.STATE_DISCONNECTED -> {
                            LogTool.d(
                                ToyotaConstants.BLUETOOTH_TAG,
                                """device disconnected
                        |Address: ${btGatt.device.address}
                        |Name: ${btGatt.device.name}
                        |Status: $status
                                """.trimMargin(),
                            )
                            isLongCharacteristicWriteInProgress = false
                            cachedProfile = null
                            packetToTransfer = null
                            gatt.close()
                            removeConnectedNotification()
                            // Restart scan for next device
                            if (shouldRestartScan) {
                                startScanning()
                            }
                            false
                        }
                        BluetoothProfile.STATE_CONNECTED -> {
                            LogTool.d(
                                ToyotaConstants.BLUETOOTH_TAG,
                                """device CONNECTED
                        |Address: ${btGatt.device.address}
                        |Name: ${btGatt.device.name}
                                """.trimMargin(),
                            )

                            sendConnectedNotification()

                            // discover services and characteristics for this device
                            btGatt.discoverServices()
                            false
                        }
                        BluetoothProfile.STATE_CONNECTING -> true
                        else -> {
                            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "we encountered an unknown state")
                            false
                        }
                    }
            }

            override fun onServicesDiscovered(
                gatt: BluetoothGatt,
                status: Int,
            ) {
                // this will get called after the client initiates a BluetoothGatt.discoverServices() call
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "device services have been discovered")
                displayGattServices(gatt.services)
            }

            // Result of a characteristic read operation
            @SuppressLint("MissingPermission") // Service only runs after permission check
            override fun onCharacteristicRead(
                gatt: BluetoothGatt,
                characteristic: BluetoothGattCharacteristic,
                status: Int,
            ) {
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "onCharacteristicRead ${characteristic.uuid}")
                val value = characteristic.value
                val stringValue: String = if (value != null) ToyUtil.bytesToHexString(value) else "null"
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "onCharacteristicRead Value: $stringValue")

                val result = gatt.setCharacteristicNotification(gattReadCharacteristic, true)
                if (result) {
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Characteristic set for notification (App)",
                    )
                } else {
                    LogTool.e(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Could not set characteristic for notification (App)",
                    )
                }

                readClientConfigurationDescriptor(characteristic)

                if (value.size >= COMMAND_SIZE) {
                    handleCommand(characteristic)
                }
            }

            override fun onDescriptorWrite(
                gatt: BluetoothGatt?,
                descriptor: BluetoothGattDescriptor?,
                status: Int,
            ) {
                LogTool.d(
                    ToyotaConstants.BLUETOOTH_TAG,
                    """onDescriptorWrite ${descriptor?.uuid}
                |write status: $status
                |new value: ${ToyUtil.bytesToHexString(descriptor?.value ?: ByteArray(0))} 
                    """.trimMargin(),
                )
                super.onDescriptorWrite(gatt, descriptor, status)
            }

            @SuppressLint("MissingPermission") // Service only runs after permission check
            override fun onMtuChanged(gatt: BluetoothGatt, mtu: Int, status: Int) {
                super.onMtuChanged(gatt, mtu, status)
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "MTU changed to: $mtu")
                if (packetToTransfer != null) {
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "* Getting gatt service, UUID:$upServiceUuid",
                    )

                    btGatt.getService(upServiceUuid)?.run {
                        LogTool.d(
                            ToyotaConstants.BLUETOOTH_TAG,
                            "* Getting gatt Characteristic. UUID: $upTxCharacteristicUuid",
                        )

                        val myGatChar = getCharacteristic(upTxCharacteristicUuid)
                        if (myGatChar != null) {
                            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "* Writing trigger")
                            myGatChar.value = packetToTransfer
                            val writingStatus = btGatt.writeCharacteristic(myGatChar)
                            LogTool.d(
                                ToyotaConstants.BLUETOOTH_TAG,
                                "* Writing trigger status :$writingStatus",
                            )
                        }
                    }
                }
            }
        }

    @SuppressLint("MissingPermission") // Service only runs after permission check
    private fun readClientConfigurationDescriptor(characteristic: BluetoothGattCharacteristic) {
        if (characteristic.descriptors.isEmpty()) {
            LogTool.e(
                ToyotaConstants.BLUETOOTH_TAG,
                "No descriptors found. Unable to subscribe to notifications",
            )
            return
        }
        val descriptor =
            characteristic.descriptors.find {
                it.uuid.toString() == ToyotaConstants.CLIENT_CHARACTERISTIC_CONFIGURATION_UUID
            }

        descriptor?.run {
            value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
            if (btGatt.writeDescriptor(this)) {
                LogTool.d(
                    ToyotaConstants.BLUETOOTH_TAG,
                    "Descriptor write to HU initiated successfully",
                )
            } else {
                LogTool.e(
                    ToyotaConstants.BLUETOOTH_TAG,
                    "Failed to subscribe to characteristic on HU",
                )
            }
        }
    }

    private fun handleCommand(characteristic: BluetoothGattCharacteristic) {
        val command =
            byteArrayOf(
                characteristic.value[COMMAND_FIRST_BYTE_INDEX],
                characteristic.value[COMMAND_SECOND_BYTE_INDEX],
            )
        val commandString = "0x${ToyUtil.bytesToHexString(command)}"

        if (!isLongCharacteristicWriteInProgress) {
            when (commandString) {
                ToyotaConstants.UP_COMMAND_PACKET_1 -> {
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Getting packet for command: ${ToyUtil.bytesToHexString(command)}",
                    )
                    getBasicProfile(command)
                }
                ToyotaConstants.UP_COMMAND_PACKET_2 -> {
                    handleCommandSecondPacket(characteristic, command)
                }
                ToyotaConstants.UP_COMMAND_ACK -> {
                    acknowledgeTransferCompletion()
                }
                else -> {
                    LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Unknown Command: $commandString")
                }
            }
        } else if (commandString.equals(ToyotaConstants.UP_COMMAND_PACKET_2, true) &&
            characteristic.value[CONTINUATION_FLAG_INDEX].compareTo(CONTINUE_FLAG_BYTE) == 0
        ) {
            continueLongWrite()
        }
    }

    private fun handleCommandSecondPacket(
        characteristic: BluetoothGattCharacteristic,
        command: ByteArray,
    ) {
        if (MAX_COMMAND_SIZE == characteristic.value.size) {
            if (characteristic.value[CONTINUATION_FLAG_INDEX].compareTo(INITIATE_FLAG_BYTE) == 0) {
                cachedProfile?.let {
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Restarting packet transfer",
                    )
                    isLongCharacteristicWriteInProgress = true
                    continueLongWrite()
                } ?: run {
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Getting packet for command: ${ToyUtil.bytesToHexString(command)}",
                    )
                    getBasicProfile(command)
                }
            }
        } else {
            LogTool.d(
                ToyotaConstants.BLUETOOTH_TAG,
                "Getting packet for command: ${ToyUtil.bytesToHexString(command)}",
            )
            getBasicProfile(command)
        }
    }

    private fun acknowledgeTransferCompletion() {
        LogTool.d(
            ToyotaConstants.BLUETOOTH_TAG,
            "Packet transfer complete acknowledged",
        )
        if (BuildConfig.DEBUG && BuildConfig.FLAVOR_environment != "prod") {
            runOnUiThread {
                Toast
                    .makeText(
                        applicationContext,
                        "Packet transfer complete acknowledged",
                        Toast.LENGTH_LONG,
                    ).show()
            }
        }
        cachedProfile = null
    }

    private fun displayGattServices(gattServices: List<BluetoothGattService>?) {
        gattServices?.run {
            val service =
                gattServices.firstOrNull {
                    if (ToyUtil.isNotSubaru()) {
                        it.uuid.toString().contains(ToyotaConstants.UP_GATT_SERVICE_UUID_TOYOTA) ||
                            it.uuid.toString().contains(ToyotaConstants.UP_GATT_SERVICE_UUID_LEXUS)
                    } else {
                        it.uuid.toString().contains(ToyotaConstants.UP_GATT_SERVICE_UUID_SUBARU)
                    }
                }

            service?.run {
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Service discovered: $uuid")
                upServiceUuid = uuid

                // Loops through available Characteristics.
                characteristics.forEach { gattCharacteristic ->
                    val charUuid = gattCharacteristic.uuid.toString()
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Characteristic discovered for service: $charUuid",
                    )
                    if (charUuid.contains(ToyotaConstants.UP_RX_CHARACTERISTIC_UUID)) {
                        LogTool.d(
                            ToyotaConstants.BLUETOOTH_TAG,
                            "Starting read for characteristic $charUuid",
                        )
                        enableNotifications(gattCharacteristic)
                    } else if (charUuid.contains(ToyotaConstants.UP_TX_CHARACTERISTIC_UUID)) {
                        upTxCharacteristicUuid = gattCharacteristic.uuid
                    }
                }
            }
        }
    }

    @SuppressLint("MissingPermission") // Service only runs after permission check
    private fun enableNotifications(gattCharacteristic: BluetoothGattCharacteristic) {
        gattReadCharacteristic = gattCharacteristic
        val result = btGatt.setCharacteristicNotification(gattReadCharacteristic, true)
        if (result) {
            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Characteristic set for notification")
        } else {
            LogTool.e(
                ToyotaConstants.BLUETOOTH_TAG,
                "Could not set characteristic for notification",
            )
        }

        readClientConfigurationDescriptor(gattCharacteristic)
    }

    @SuppressLint("MissingPermission") // Service only runs after permission check
    private fun writeDataThroughBle(data: ByteArray) {
        packetToTransfer = data
        btGatt.requestMtu(data.size + 3) // Android will add a 3 byte header
    }

    @SuppressLint("MissingPermission") // Service only runs after permission check
    private fun stopScanning() {
        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "stopping scanning")
        if (btAdapter.isEnabled) {
            bleScanner.stopScan(leScanCallback)
        }
    }

    @SuppressLint("MissingPermission") // Service only runs after permission check
    private fun startScanning() {
        if (btAdapter.isEnabled) { // Checking Bluetooth is still on
            val settingsBuilder = ScanSettings.Builder()
            settingsBuilder.setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES)
            settingsBuilder.setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE)
            settingsBuilder.setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY) // As app must complete connection in under 5s
            try {
                if (isSafeToStartScan()) { // Checking for Android BLE scan frequency limitations
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Initiating ble scan")
                    scanStartTimes.add(System.currentTimeMillis())
                    bleScanner?.startScan(scanFilters, settingsBuilder.build(), leScanCallback)
                } else {
                    // Calculate minimum delay needed to avoid scan frequency limitation
                    val timeSinceOldestScan = System.currentTimeMillis() - scanStartTimes.first()
                    val delay = EXCESSIVE_SCANNING_PERIOD_MS - timeSinceOldestScan + 100 // Adding 100ms as safety
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Delaying next scan by ${delay / 1000} seconds",
                    )
                    Timer().schedule(delay) {
                        startScanning()
                    }
                }
            } catch (e: Exception) {
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, e.localizedMessage)
            }
        } else {
            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Bluetooth is off")
            disconnect()
        }
    }

    private fun isSafeToStartScan(): Boolean {
        if (scanStartTimes.isEmpty()) {
            return true
        }

        scanStartTimes.removeIf { startTime ->
            (System.currentTimeMillis() - startTime) > EXCESSIVE_SCANNING_PERIOD_MS
        }

        return scanStartTimes.size < MAX_SCANS_ALLOWED_IN_PERIOD - 1
    }

    private fun getBasicProfile(command: ByteArray) {
        val commandDifferentiatorByte = command[1]
        when {
            commandDifferentiatorByte.compareTo(COMMAND_ID_BYTE_PACKET_1) == 0 -> { // Packet 1 request command
                downloadBasicProfile(1, command)
            }
            commandDifferentiatorByte.compareTo(COMMAND_ID_BYTE_PACKET_2) == 0 -> { // Packet 2 request command
                // Can be assumed that Packet 1 is always called first so packets should've been
                // acquired and cached
                if (userProfileAPIManager.hasCachedProfile()) {
                    handlePayload(
                        command,
                        userProfileAPIManager.getCachedPacket2().toByteArray(),
                        2,
                    )
                } else {
                    downloadBasicProfile(2, command)
                }
            }
        }
    }

    private fun downloadBasicProfile(
        packetId: Int,
        command: ByteArray,
    ) {
        userProfileAPIManager.getBasicProfile(
            object : StreamObserver<ProfileServiceServer.GetBaseProfileResponse> {
                override fun onNext(value: ProfileServiceServer.GetBaseProfileResponse) {
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "UP packets acquired")
                    // Always caching packet 2
                    val payload =
                        when (packetId) {
                            1 -> value.packet1
                            2 -> value.packet2
                            else -> null
                        }

                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "packet $packetId: ${payload?.toStringUtf8()}",
                    )

                    handlePayload(command, payload?.toByteArray(), packetId)
                }

                override fun onError(t: Throwable) {
                    LogTool.e(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Failed to acquire profile: ${t.message}",
                    )
                }

                override fun onCompleted() {}
            },
        )
    }

    private fun handlePayload(
        command: ByteArray,
        payload: ByteArray?,
        packetId: Int,
    ) {
        payload?.run {
            LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Payload $packetId size : $size")
            val byteArrayOutputStream = ByteArrayOutputStream()
            try {
                byteArrayOutputStream.write(
                    ByteBuffer
                        .allocate(4)
                        .putInt(size)
                        .array(),
                )
                byteArrayOutputStream.write(command)
                LogTool.d(
                    ToyotaConstants.BLUETOOTH_TAG,
                    "Packet $packetId header : ${ToyUtil.bytesToHexString(
                        byteArrayOutputStream.toByteArray(),
                    )}",
                )
                LogTool.d(
                    ToyotaConstants.BLUETOOTH_TAG,
                    "Packet $packetId payload : ${ToyUtil.bytesToHexString(this)}",
                )
            } catch (e: IOException) {
                LogTool.e(ToyotaConstants.BLUETOOTH_TAG, "Failed to write output byte stream", e)
            }

            if (packetId == 1 && size < MAX_PACKET_SIZE - PACKET_HEADER_SIZE) {
                try {
                    byteArrayOutputStream.write(this)
                    LogTool.d(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Packet $packetId: ${ToyUtil.bytesToHexString(
                            byteArrayOutputStream.toByteArray(),
                        )}",
                    )
                } catch (e: IOException) {
                    LogTool.e(
                        ToyotaConstants.BLUETOOTH_TAG,
                        "Failed to write output byte stream",
                        e,
                    )
                }
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Sending packet $packetId through BLE")
                if (BuildConfig.DEBUG && BuildConfig.FLAVOR_environment != "prod") {
                    runOnUiThread {
                        Toast
                            .makeText(
                                applicationContext,
                                "Sending packet $packetId through BLE",
                                Toast.LENGTH_LONG,
                            ).show()
                    }
                }
                analyticsLogger.logEvent(AnalyticsEvent.BLUETOOTH_SEND_PACKET1_BLE)
                writeDataThroughBle(byteArrayOutputStream.toByteArray())
            } else {
                cachedHeader = byteArrayOutputStream.toByteArray()
                isLongCharacteristicWriteInProgress = true
                cachedProfile = this
                analyticsLogger.logEvent(AnalyticsEvent.BLUETOOTH_SEND_PACKET2_BLE)
                continueLongWrite()
            }
        }
    }

    private fun continueLongWrite() {
        cachedProfile?.run {
            val payloadMaxSize = MAX_PACKET_SIZE - 10 // Reserving 10 bytes for header size
            var payloadSize = payloadMaxSize
            val notCompletedFlag =
                if (nextByteIndex + payloadMaxSize < size) {
                    LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Sending next chunk")
                    1
                } else {
                    isLongCharacteristicWriteInProgress = false
                    payloadSize = size - nextByteIndex
                    0
                }
            val payload =
                byteArrayOf(notCompletedFlag.toByte()) +
                    copyOfRange(nextByteIndex, nextByteIndex + payloadSize)

            writeDataThroughBle(cachedHeader + payload)
            when (notCompletedFlag) {
                1 -> nextByteIndex += payloadSize
                0 -> nextByteIndex = 0
            }
        }
    }

    private fun sendConnectedNotification() {
        val notificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Notification channel has been created on application
        val notification =
            NotificationCompat
                .Builder(
                    this,
                    "${applicationContext.packageName}.${ToyotaConstants.BLE_NOTIFICATION_CHANNEL_ID_SUFFIX}",
                ).setSmallIcon(R.drawable.ic_notification)
                .setContentTitle("BLE Service")
                .setContentText(" Head Unit Connected")
                .build()

        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun removeConnectedNotification() {
        val notificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        notificationManager.cancel(NOTIFICATION_ID)
    }

    @SuppressLint("MissingPermission") // Service only runs after permission check
    private fun disconnect() {
        if (PermissionUtil.hasBluetoothPermissions(applicationContext)) {
            stopScanning()
            if (::btGatt.isInitialized &&
                (getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager)
                    .getConnectedDevices(BluetoothProfile.GATT_SERVER)
                    .isNotEmpty()
            ) {
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Disconnecting gatt")
                btGatt.disconnect()
            }
            if (::sppThread.isInitialized) {
                LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Closing SPP connections")
                sppThread.closeSppConnection()
            }
        }

        LogTool.d(ToyotaConstants.BLUETOOTH_TAG, "Stopping foreground transfer service")
        stopForeground(STOP_FOREGROUND_DETACH)
        stopSelf()
    }

    @SuppressLint("MissingPermission") // Service only runs after permission check
    private fun connectToDevice(device: BluetoothDevice) {
        btAdapter.cancelDiscovery()
        stopScanning()
        LogTool.d(
            ToyotaConstants.BLUETOOTH_TAG,
            "Requesting connection to GATT server",
        )
        btGatt =
            device.connectGatt(
                applicationContext,
                false,
                btleGattCallback,
                BluetoothDevice.TRANSPORT_LE,
            )
        isConnecting = true
        analyticsLogger.logEvent(AnalyticsEvent.BLUETOOTH_APP_CONNECTED_HU)
    }

    private fun setupScanFilters(): List<ScanFilter> {
        val bleFilters = ArrayList<ScanFilter>()
        val filterBuilder = ScanFilter.Builder()

        // Only matching data and UUID are relevant.
        val manufacturerDataMask = ByteBuffer.allocate(MANF_DATA_MASK_SIZE)
        for (i in 0 until MANF_DATA_MASK_SIZE) {
            manufacturerDataMask.put(0x01.toByte())
        }

        val manufacturerData = ByteBuffer.allocate(MANF_DATA_MASK_SIZE)
        // Setting Little-Endian representation of matching sequence
        manufacturerData.put(ToyotaConstants.BEACON_MATCHING_LE)

        return if (ToyUtil.isSubaru()) {
            // Set up UUID for SUBARU
            manufacturerData.put(ToyotaConstants.BLE_ADVERTISING_BYTE_UUID_SUBARU)
            val subaruManfData = manufacturerData.array().copyOf()

            filterBuilder.setManufacturerData(
                ToyotaConstants.MANF_ID_PANA,
                subaruManfData,
                manufacturerDataMask.array(),
            )

            bleFilters.add(filterBuilder.build())

            filterBuilder.setManufacturerData(
                ToyotaConstants.MANF_ID_DTEN,
                subaruManfData,
                manufacturerDataMask.array(),
            )

            bleFilters.add(filterBuilder.build())

            bleFilters
        } else {
            // Set up UUID for TOYOTA
            manufacturerData.put(ToyotaConstants.BLE_ADVERTISING_BYTE_UUID_TOYOTA)
            val toyotaManfData = manufacturerData.array().copyOf()

            filterBuilder.setManufacturerData(
                ToyotaConstants.MANF_ID_PANA,
                toyotaManfData,
                manufacturerDataMask.array(),
            )

            bleFilters.add(filterBuilder.build())

            filterBuilder.setManufacturerData(
                ToyotaConstants.MANF_ID_DTEN,
                toyotaManfData,
                manufacturerDataMask.array(),
            )

            bleFilters.add(filterBuilder.build())

            // Resetting position to reuse matching sequence
            manufacturerData.position(MANF_DATA_BEGIN_INDEX)

            // Set up UUID for LEXUS
            manufacturerData.put(ToyotaConstants.BLE_ADVERTISING_BYTE_UUID_LEXUS)
            val lexusManfData = manufacturerData.array().copyOf()

            filterBuilder.setManufacturerData(
                ToyotaConstants.MANF_ID_PANA,
                lexusManfData,
                manufacturerDataMask.array(),
            )

            bleFilters.add(filterBuilder.build())

            filterBuilder.setManufacturerData(
                ToyotaConstants.MANF_ID_DTEN,
                lexusManfData,
                manufacturerDataMask.array(),
            )

            bleFilters.add(filterBuilder.build())

            bleFilters
        }
    }
}
