package com.toyota.oneapp.receiver

import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.toyota.oneapp.xcapp.manager.XcappManagerProvider

class XcappBluetoothReceiver : BroadcastReceiver() {
    override fun onReceive(
        context: Context?,
        intent: Intent?,
    ) {
        when (intent?.action) {
            BluetoothDevice.ACTION_ACL_CONNECTED -> {
                @Suppress("ConstantConditionIf")
                if (XcappManagerProvider.isXcappInitialized()) {
                    val xcapp = XcappManagerProvider.xcappManager
                    if (context != null && xcapp.isTermsAccepted(context)) {
                        xcapp.start()
                    }
                }
            }
        }
    }
}
