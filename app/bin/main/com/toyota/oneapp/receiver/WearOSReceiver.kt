package com.toyota.oneapp.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationManagerCompat
import com.toyota.oneapp.fcm.ToyotaFCMService
import com.toyota.oneapp.util.WearUtil.sendToWear
import toyotaone.commonlib.wear.WearAPIType
import toyotaone.commonlib.wear.WearConstants.BUS_OUTBOUND_DATA_KEY
import toyotaone.commonlib.wear.WearConstants.BUS_OUTBOUND_PATH
import toyotaone.commonlib.wear.WearResponse

class WearOSReceiver : BroadcastReceiver() {
    override fun onReceive(
        context: Context?,
        intent: Intent?,
    ) {
        if (context != null && intent?.action == ToyotaFCMService.OPEN_WEAR_OS) {
            NotificationManagerCompat.from(context).cancel(
                intent.getIntExtra(ToyotaFCMService.NOTIFICATION_ID, 0),
            )
            sendToWear(
                BUS_OUTBOUND_PATH,
                BUS_OUTBOUND_DATA_KEY,
                WearResponse(WearAPIType.OPEN_WEAR_OS, null, null, null).toJsonString(),
            )
        }
    }
}
