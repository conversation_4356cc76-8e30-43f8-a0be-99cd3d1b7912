package com.toyota.oneapp.extensions

import android.graphics.Paint
import android.text.Html
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ForegroundColorSpan
import android.text.style.URLSpan
import android.view.View
import android.widget.CheckBox
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import com.toyota.oneapp.model.combineddataconsent.CombineDataConsent

fun TextView.applyUnderline() {
    this.paintFlags = paintFlags.or(Paint.UNDERLINE_TEXT_FLAG)
}

fun View.setTextViewHTML(
    html: String,
    item: CombineDataConsent,
    listener: HTMLTextClickListener,
) {
    if (this is TextView || this is CheckBox) {
        val sequence: Spanned = Html.fromHtml(html, Html.FROM_HTML_MODE_LEGACY)
        val strBuilder = SpannableStringBuilder(sequence)
        val urls =
            strBuilder.getSpans(0, sequence.length, URLSpan::class.java)

        for (span in urls) {
            val start = strBuilder.getSpanStart(span)
            val end = strBuilder.getSpanEnd(span)
            val flags = strBuilder.getSpanFlags(span)
            val clickableSpan: URLSpan =
                object : URLSpan(span.url) {
                    override fun onClick(widget: View) {
                        val message = getMessageFromScheme(span.url, item)
                        if (message != null) {
                            listener.showLinkMessage(dialogMessageFixer(message))
                            if (this@setTextViewHTML is CheckBox) {
                                <EMAIL> = <EMAIL>()
                            }
                        } else {
                            super.onClick(widget)
                        }
                    }
                }

            strBuilder.setSpan(clickableSpan, start, end, flags)
            strBuilder.removeSpan(span)
        }
        (this as TextView).text = strBuilder
        this.movementMethod = LinkMovementMethod.getInstance()
    }
}

private fun dialogMessageFixer(message: String): String {
    // TODO: Fix for removing the unwanted part of the consent description. Will be refactored once we have the mechanism to get the consent description separately for this screen
    return message.replace(
        "What is the Benefit of Enrolling?",
        "\nWhat is the Benefit of Enrolling?",
    )
}

@VisibleForTesting
fun getMessageFromScheme(
    scheme: String,
    item: CombineDataConsent,
): String? {
    val dialogs = item.description?.dialogs
    val foundDialog = dialogs?.find { it.scheme == scheme }

    foundDialog?.let {
        return it.body
    }

    return null
}

fun TextView.changeLastDigitColor(
    body: String,
    color: Int,
) {
    val spannableStringBuilder = SpannableStringBuilder(body)
    spannableStringBuilder.setSpan(ForegroundColorSpan(color), body.length - 1, body.length, 0)
    this.text = spannableStringBuilder
}
