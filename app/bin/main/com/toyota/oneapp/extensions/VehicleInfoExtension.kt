package com.toyota.oneapp.extensions

import com.toyota.oneapp.model.pref.LanguagePreferenceModel
import com.toyota.oneapp.model.vehicle.CapabilityNew
import com.toyota.oneapp.model.vehicle.VehicleDetailPayload
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.util.Brand
import com.toyota.oneapp.util.ToyotaConstants
import java.util.*

fun VehicleDetailPayload.createVehicleInfo(languagePreferenceModel: LanguagePreferenceModel): VehicleInfo {
    val vehicleInfo =
        VehicleInfo().also {
            with(vehicle) {
                it.vin = vin
                it.image = stockPicReference
                it.modelYear = model.modelYear
                it.modelName = model.modelCode
                it.modelDescription = model.modelDescription.getString(languagePreferenceModel)
                it.asiCode = asiCode
                it.imei = imei
                it.isNonCvtVehicle = isNonCvtVehicle
                it.region = region
                it.nickName = getNickName(languagePreferenceModel)
                it.generation = generation
                it.brand = Brand.getBrand(brandCode).appBrand
                it.hwType = dcm?.hardwareType ?: ""
                it.fleetInd = fleetInd
            }
        }

    val capabilityItems = ArrayList<CapabilityNew>()
    vehicle.capabilities.forEach { item ->
        val capability = CapabilityNew(item)
        capabilityItems.add(capability)
    }
    vehicleInfo.capabilities = capabilityItems
    return vehicleInfo
}

/**
 * The Extension Method - Used to get Vehicle Locale.
 */
fun VehicleInfo.getLocale(language: String): Locale =
    when {
        region.equals(ToyotaConstants.REGION_CA, true) -> {
            if (language.equals(LanguagePreferenceModel.CODE_FR, true)) {
                LanguagePreferenceModel.CANADA_FRANCE
            } else {
                LanguagePreferenceModel.CANADA_ENGLISH
            }
        }
        region.equals(ToyotaConstants.REGION_PR, true) -> {
            if (language.equals(LanguagePreferenceModel.CODE_PR, true)) {
                LanguagePreferenceModel.TDPR_SPANISH
            } else {
                LanguagePreferenceModel.TDPR_ENGLISH
            }
        }
        else -> {
            LanguagePreferenceModel.US_ENGLISH
        }
    }

/**
 * The extension Property - Used to get Vehicle Model.
 */
val VehicleInfo.model: String
    get() = "${modelYear.orEmpty()} ${modelName.orEmpty()}"
