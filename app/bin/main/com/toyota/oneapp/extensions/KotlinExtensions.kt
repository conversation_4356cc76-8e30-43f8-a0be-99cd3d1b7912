package com.toyota.oneapp.extensions

import com.toyota.oneapp.util.ToyotaConstants
import java.util.regex.Pattern

/**
 * General kotlin extension methods
 */
object KotlinExtensions {
    /**
     * A simple method to force a when statement to be exhaustive
     *
     * Usage:
     *
     * when(item) {
     *     A -> {}
     *     B -> {}
     * }.exhaustive
     *
     * enum class Items {A, B}
     *
     * This will cause a compile error if more values are later added to Items, ensuring developers
     * do not miss new cases.
     */
    val <T> T.exhaustive: T
        get() = this

    fun String.fetchUrl(): String? {
        val pattern = Pattern.compile(ToyotaConstants.URL_REGEX)
        val matcher = pattern.matcher(this)
        return if (matcher.find()) matcher.group() else null
    }
}
