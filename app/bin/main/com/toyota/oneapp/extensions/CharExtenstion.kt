package com.toyota.oneapp.extensions

import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.util.ToyotaConstants

fun filterInvalidCharacters(locationDetails: List<LocationDetails>) {
    for (locationDetail in locationDetails) {
        val re = Regex(ToyotaConstants.ADDRESS_REGEX)
        val address = locationDetail.formattedAddress
        val name = locationDetail.name
        locationDetail.formattedAddress = address?.replace(re, ToyotaConstants.EMPTY_STRING)
        locationDetail.name = name?.replace(re, ToyotaConstants.EMPTY_STRING)
    }
}

fun String.getPhoneNumberFormat(): String {
    if (!this.matches("[0-9]{10}".toRegex())) {
        return this
    }

    val first = this.subSequence(0, 3)
    val second = this.subSequence(3, 6)
    val third = this.subSequence(6, this.length)

    return "$first $second-$third"
}
