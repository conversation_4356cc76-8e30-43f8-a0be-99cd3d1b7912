package com.toyota.oneapp.adapter

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.PlayerConstants
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.YouTubePlayer
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.YouTubePlayerListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.views.YouTubePlayerView
import com.toyota.oneapp.R
import com.toyota.oneapp.features.glovebox.gloveboxlist.domain.model.HowToVideosPayload
import com.toyota.oneapp.model.howtovideos.FullScreenBack
import com.toyota.oneapp.ui.garage.HowToVideoFullScreenActivity
import io.reactivex.disposables.Disposable
import toyotaone.commonlib.eventbus.RxBus
import toyotaone.commonlib.log.LogTool
import java.lang.Exception

class HowToVideosAdapter(
    private val context: Context,
    private var videosList: List<HowToVideosPayload>,
) :
    RecyclerView.Adapter<HowToVideosAdapter.ViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_how_to_video, parent, false)
        val holder = ViewHolder(view)
        (context as? AppCompatActivity)?.lifecycle?.addObserver(holder)
        return holder
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.onBind(position)
    }

    override fun getItemCount(): Int {
        return videosList.size
    }

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        holder.disposable?.dispose()
    }

    private fun enterFullScreen(
        videoId: String?,
        second: Float,
        position: Int,
    ) {
        val intent = Intent(context, HowToVideoFullScreenActivity::class.java)
        intent.putExtra("VideoId", videoId)
        intent.putExtra("CurrentSec", second)
        intent.putExtra("Position", position)
        context.startActivity(intent)
    }

    inner class ViewHolder(itemView: View) :
        RecyclerView.ViewHolder(itemView),
        LifecycleObserver,
        YouTubePlayerListener {
        private val title: TextView = itemView.findViewById(R.id.tv_htv_title)
        private val description: TextView = itemView.findViewById(R.id.tv_htv_desc)
        private val youtubePlayer: YouTubePlayerView = itemView.findViewById(R.id.youtube_view)

        private var currentSec = 0f
        private var player: YouTubePlayer? = null

        private var fullScreenBack: FullScreenBack? = null
        var disposable: Disposable? = null

        fun onBind(position: Int) {
            registerFullScreenBack()
            val mYoutubeVideo = videosList[position]
            if (!mYoutubeVideo.title.isNullOrBlank()) {
                title.apply {
                    visibility = View.VISIBLE
                    text = mYoutubeVideo.title
                }
            }

            if (!mYoutubeVideo.description.isNullOrBlank()) {
                description.apply {
                    visibility = View.VISIBLE
                    text = mYoutubeVideo.description
                }
            }
            (context as? AppCompatActivity)?.lifecycle?.addObserver(youtubePlayer)
            try {
                youtubePlayer.initialize(this, true)
            } catch (ex: Exception) {
                LogTool.d("Player", ex.localizedMessage)
            }
            youtubePlayer.getPlayerUiController()
                .showPlayPauseButton(true)
                .showBufferingProgress(true)
                .showCurrentTime(true)
                .showDuration(true)
                .showFullscreenButton(false)
                .showMenuButton(false)
                .showSeekBar(false)
                .showYouTubeButton(true)
        }

        @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
        fun onPageResume() {
            if (fullScreenBack?.position == layoutPosition) {
                if (fullScreenBack != null) {
                    currentSec = fullScreenBack!!.currentSec
                    player?.seekTo(fullScreenBack!!.currentSec)
                }
            }
        }

        private fun registerFullScreenBack() {
            disposable?.dispose()
            disposable =
                RxBus.get().toFlowable(FullScreenBack::class.java).subscribe {
                    fullScreenBack = it
                }
        }

        override fun onApiChange(youTubePlayer: YouTubePlayer) {
        }

        override fun onCurrentSecond(
            youTubePlayer: YouTubePlayer,
            second: Float,
        ) {
            currentSec = second
        }

        override fun onError(
            youTubePlayer: YouTubePlayer,
            error: PlayerConstants.PlayerError,
        ) {
        }

        override fun onPlaybackQualityChange(
            youTubePlayer: YouTubePlayer,
            playbackQuality: PlayerConstants.PlaybackQuality,
        ) {
        }

        override fun onPlaybackRateChange(
            youTubePlayer: YouTubePlayer,
            playbackRate: PlayerConstants.PlaybackRate,
        ) {
        }

        override fun onReady(youTubePlayer: YouTubePlayer) {
            player = youTubePlayer
            try {
                val mYoutubeVideo = videosList[position]
                if (mYoutubeVideo.videoId != null) {
                    youTubePlayer.mute()
                    youTubePlayer.cueVideo(mYoutubeVideo.videoId, 0f)
                }
            } catch (ex: Exception) {
                LogTool.d("Loading issue catch", ex.localizedMessage)
            }
        }

        override fun onStateChange(
            youTubePlayer: YouTubePlayer,
            state: PlayerConstants.PlayerState,
        ) {
            if (state == PlayerConstants.PlayerState.PLAYING) {
                val mYoutubeVideo = videosList[layoutPosition]
                enterFullScreen(mYoutubeVideo.videoId, currentSec, layoutPosition)
            }
        }

        override fun onVideoDuration(
            youTubePlayer: YouTubePlayer,
            duration: Float,
        ) {
        }

        override fun onVideoId(
            youTubePlayer: YouTubePlayer,
            videoId: String,
        ) {
        }

        override fun onVideoLoadedFraction(
            youTubePlayer: YouTubePlayer,
            loadedFraction: Float,
        ) {
        }
    }
}
