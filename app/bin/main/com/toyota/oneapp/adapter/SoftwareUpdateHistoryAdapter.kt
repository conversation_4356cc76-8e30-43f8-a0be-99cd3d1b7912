package com.toyota.oneapp.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemSoftwareUpdateHistoryBinding
import com.toyota.oneapp.model.garage.Updates
import com.toyota.oneapp.util.DateUtil
import java.util.concurrent.Executor

class SoftwareUpdateHistoryAdapter(executor: Executor, val dateUtil: DateUtil, val context: Context) : DataBindingAdapter<
    Updates,
    ItemSoftwareUpdateHistoryBinding,
    >(executor, SoftwareHistoryDiffUtil()) {
    override fun createBinding(
        parent: ViewGroup,
        viewType: Int,
    ): ItemSoftwareUpdateHistoryBinding {
        val binding =
            DataBindingUtil.inflate<ItemSoftwareUpdateHistoryBinding>(
                LayoutInflater.from(parent.context),
                R.layout.item_software_update_history,
                parent,
                false,
            )
        return binding
    }

    override fun bind(
        binding: ItemSoftwareUpdateHistoryBinding,
        position: Int,
    ) {
        binding.vehicleHistoryStatus = getItem(position)
        val lastInstallDate: String = dateUtil.getDateFormat(getItem(position).notificationTime)
        val lastInstallTime: String = dateUtil.getTimeFormat(getItem(position).notificationTime)
        if (getItem(position).notificationCode.equals("1")) {
            binding.tvSoftwareUpdateComplete.text =
                context.getString(
                    R.string.software_update_available_txt,
                )
            binding.tvReleaseDate.text =
                context.getString(
                    R.string.release_date,
                    lastInstallDate,
                    lastInstallTime,
                )
        } else if (getItem(position).notificationCode.equals("2")) {
            binding.tvSoftwareUpdateComplete.text =
                context.getString(
                    R.string.software_install_initiated,
                )
            binding.tvReleaseDate.text =
                context.getString(
                    R.string.initiated_date,
                    lastInstallDate,
                    lastInstallTime,
                )
        } else {
            binding.tvSoftwareUpdateComplete.text =
                context.getString(
                    R.string.software_update_complete_txt,
                )
            binding.tvReleaseDate.text =
                context.getString(
                    R.string.last_installed_date,
                    lastInstallDate,
                    lastInstallTime,
                )
        }
    }
}

class SoftwareHistoryDiffUtil : DiffUtil.ItemCallback<Updates>() {
    override fun areItemsTheSame(
        oldItem: Updates,
        newItem: Updates,
    ): Boolean {
        return oldItem == newItem
    }

    override fun areContentsTheSame(
        oldItem: Updates,
        newItem: Updates,
    ): Boolean {
        return oldItem == newItem
    }
}
