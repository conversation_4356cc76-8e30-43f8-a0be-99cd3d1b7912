package com.toyota.oneapp.adapter

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.toyota.oneapp.model.dashboard.NotificationHistoryPayload
import com.toyota.oneapp.ui.newdashboard.NotificationHistoryFragment
import com.toyota.oneapp.ui.newdashboard.fragments.NotificationHistoryPageFragment

class NotificationHistoryPageAdapter(
    fa: NotificationHistoryFragment,
    private var nhList: List<NotificationHistoryPayload>?,
) :
    FragmentStateAdapter(fa) {
    override fun getItemCount(): Int {
        return nhList?.size ?: 0
    }

    override fun createFragment(position: Int): Fragment {
        return NotificationHistoryPageFragment.newInstance(nhList?.getOrNull(position))
    }

    fun refresh(list: List<NotificationHistoryPayload>) {
        nhList = list
        notifyDataSetChanged()
    }
}
