package com.toyota.oneapp.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.ui.newdashboard.fragments.NotificationCategoryListener
import com.toyota.oneapp.ui.newdashboard.fragments.NotificationHistoryPageFragment

class NotificationCategoryAdapter(
    var categories: List<String>,
    val callback: NotificationCategoryListener,
    var selectedCategory: String,
) :
    RecyclerView.Adapter<NotificationCategoryAdapter.ViewHolder>() {
    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var category: TextView = itemView.findViewById(R.id.tv_notification_category)
        private var radioButton: RadioButton = itemView.findViewById(R.id.rd_notification_category)

        fun bind(title: String) {
            category.text =
                if (NotificationHistoryPageFragment.ALL_NOTIFICATION == title) {
                    itemView.context.getString(R.string.Notification_bottom_all_notification)
                } else {
                    title
                }
            radioButton.tag = title
            radioButton.isChecked = (selectedCategory == title)
            radioButton.setOnCheckedChangeListener {
                    compoundButton, isChecked ->
                if (isChecked) {
                    callback.onCategorySelected(compoundButton.tag as String)
                }
            }
        }
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        holder.bind(categories[position])
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val view =
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_notification_category,
                parent,
                false,
            )
        return ViewHolder(view)
    }

    fun refreshList(
        categories: List<String>,
        selectedCategory: String,
    ) {
        this.categories = categories
        if (this.selectedCategory != selectedCategory) {
            this.selectedCategory = selectedCategory
        }
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return categories.size
    }
}
