package com.toyota.oneapp.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.TextView
import com.toyota.oneapp.R
import com.toyota.oneapp.model.Advisor
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.ToyUtil
import de.hdodenhof.circleimageview.CircleImageView

class AdvisorAdapter(
    private val context: Context,
    private val advisorList: List<Advisor>,
    private val clickListener: AdvisorSelectedListener,
) : androidx.recyclerview.widget.RecyclerView.Adapter<AdvisorAdapter.ViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_advisor, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        val adviser = advisorList[position]
        if (adviser.advisorId.isNullOrBlank()) {
            holder.cvProfilePic.visibility = View.INVISIBLE
        } else {
            holder.cvProfilePic.visibility = View.VISIBLE
        }
        holder.tvAdvisor.text = adviser.name

        if (ToyUtil.isNotBlank(adviser.photoUrl)) {
            GlideUtil.loadImage(context, adviser.photoUrl, R.drawable.pro_pic2, holder.cvProfilePic)
            // holder.cvProfilePic.setImageURI(Uri.parse(advisor.photoUrl))
        } else {
            holder.cvProfilePic.setImageResource(R.drawable.pro_pic2)
        }
    }

    override fun getItemCount(): Int {
        return advisorList.size
    }

    inner class ViewHolder(itemView: View) : androidx.recyclerview.widget.RecyclerView.ViewHolder(
        itemView,
    ) {
        internal var cvProfilePic: CircleImageView
        internal var tvAdvisor: TextView
        internal var ivSelected: RadioButton

        init {
            cvProfilePic = itemView.findViewById(R.id.cv_pro_pic)
            tvAdvisor = itemView.findViewById(R.id.tv_dealer_name)
            ivSelected = itemView.findViewById(R.id.iv_selected)
            ivSelected.isClickable = false

            itemView.setOnClickListener {
                clickListener.setSelectedAdvisor(
                    advisorList[layoutPosition],
                )
            }
        }
    }

    interface AdvisorSelectedListener {
        fun setSelectedAdvisor(advisor: Advisor)
    }
}
