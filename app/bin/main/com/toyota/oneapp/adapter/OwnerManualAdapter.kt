package com.toyota.oneapp.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.model.vehicle.OwnerManualItem
import toyotaone.commonlib.recyclerview.HorizontalDividerItemDecoration

class OwnerManualAdapter(
    private val onOwnerManualClickListener: OwnerManualClickListener,
    private val listOfManuals: ArrayList<Section>,
) : RecyclerView.Adapter<OwnerManualAdapter.OwnerManualViewHolder>() {
    private var itemAdapter: ItemAdapter? = null

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): OwnerManualViewHolder {
        return OwnerManualViewHolder(
            LayoutInflater.from(parent.context).inflate(R.layout.list_section, parent, false),
        )
    }

    override fun onBindViewHolder(
        holder: OwnerManualViewHolder,
        position: Int,
    ) {
        holder.bindData(listOfManuals.get(position))
    }

    override fun getItemCount(): Int {
        return listOfManuals.size
    }

    interface OwnerManualClickListener {
        fun onOwnerManualItemClick(item: OwnerManualItem?)
    }

    inner class OwnerManualViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var sectionName: TextView? = null
        private var itemRecyclerView: RecyclerView? = null

        init {
            sectionName = itemView.findViewById(R.id.section_item_text_view)
            itemRecyclerView = itemView.findViewById(R.id.item_recycler_view)
        }

        internal fun bindData(item: Section?) {
            if (!item?.sectionTitle.isNullOrEmpty()) {
                sectionName?.text = item?.sectionTitle
            } else {
                sectionName?.visibility = View.GONE
            }
            // RecyclerView for items
            val linearLayoutManager =
                LinearLayoutManager(
                    itemView.context,
                    LinearLayoutManager.VERTICAL,
                    false,
                )
            itemRecyclerView?.layoutManager = linearLayoutManager
            itemRecyclerView?.addItemDecoration(
                HorizontalDividerItemDecoration.Builder(itemView.context).size(
                    itemView.context.resources.getDimensionPixelSize(R.dimen.separator_height),
                ).color(Color.LTGRAY).build(),
            )
            itemAdapter = ItemAdapter(item?.allItemsInSection, onOwnerManualClickListener)
            itemRecyclerView?.adapter = itemAdapter
        }
    }
}

class ItemAdapter(
    internal var items: List<OwnerManualItem>?,
    private val onOwnerManualClickListener: OwnerManualAdapter.OwnerManualClickListener,
) : RecyclerView.Adapter<ItemAdapter.ViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ItemAdapter.ViewHolder {
        val view =
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_owner_manual,
                parent,
                false,
            )
        return ViewHolder(view)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        val itemName = items?.get(position)
        holder.bind(itemName?.manualTitle)
    }

    override fun getItemCount(): Int {
        return items?.size!!
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val itemName: TextView = itemView.findViewById(R.id.manual_title)

        fun bind(item: String?) {
            itemName.text = item
        }

        init {
            itemView.setOnClickListener {
                onOwnerManualClickListener.onOwnerManualItemClick(items?.get(adapterPosition))
            }
        }
    }
}
