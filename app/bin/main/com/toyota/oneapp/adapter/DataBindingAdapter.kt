package com.toyota.oneapp.adapter

import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.AsyncDifferConfig
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import com.toyota.oneapp.core.IoThreadExecutor
import com.toyota.oneapp.ui.viewholder.DataBindingViewHolder
import java.util.concurrent.Executor

/**
 * This is a generic adapter to use [ViewDataBinding]
 * If want to create adapter for the multiple view types then keep the type of the adapter as [ViewDataBinding],
 * since the [ViewDataBinding] is the super class for all the data bindings
 */
abstract class DataBindingAdapter<T, B : ViewDataBinding>(
    @IoThreadExecutor val executor: Executor,
    diffCallback: DiffUtil.ItemCallback<T>,
) :
    ListAdapter<T, DataBindingViewHolder<B>>(
            AsyncDifferConfig.Builder<T>(diffCallback)
                .setBackgroundThreadExecutor(executor)
                .build(),
        ) {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): DataBindingViewHolder<B> {
        val dataBinding = createBinding(parent, viewType)
        return DataBindingViewHolder(dataBinding)
    }

    override fun onBindViewHolder(
        holder: DataBindingViewHolder<B>,
        position: Int,
    ) {
        bind(holder.binding, position)
        holder.binding.executePendingBindings()
    }

    override fun onBindViewHolder(
        holder: DataBindingViewHolder<B>,
        position: Int,
        payloads: MutableList<Any>,
    ) {
        if (payloads.isEmpty()) {
            onBindViewHolder(holder, position)
        } else {
            bind(holder.binding, position, payloads)
            holder.binding.executePendingBindings()
        }
    }

    /**
     * This method is used to initialize the data binding instances, if multiple view types are there
     * then use conditional expression to initialise the instance of the binding.
     */
    protected abstract fun createBinding(
        parent: ViewGroup,
        viewType: Int,
    ): B

    /**
     * This method is used to set the data to the view data binding so that the details can be updated to the UI
     * If there are multiple view types then use view type with conditional expression and cast the view binding to the
     * correct binding class and pass the data to the binding.
     */
    protected abstract fun bind(
        binding: B,
        position: Int,
    )

    /**
     * Override this method when need to change only some specific views in the item view
     */
    protected open fun bind(
        binding: B,
        position: Int,
        payloads: MutableList<Any>,
    ) {}
}
