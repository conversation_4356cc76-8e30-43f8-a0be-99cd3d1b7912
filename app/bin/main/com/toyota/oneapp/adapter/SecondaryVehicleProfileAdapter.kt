package com.toyota.oneapp.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.databinding.ItemVehicleProfileBinding
import com.toyota.oneapp.model.vehicle.SecondaryVehicleProfilePayload
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class SecondaryVehicleProfileAdapter(
    private val context: Context,
    private val secondaryVehicleProfileSelectedListener: SecondaryVehicleProfileSelectedListener,
) : RecyclerView.Adapter<SecondaryVehicleProfileAdapter.VehicleProfileViewHolder>() {
    private var secondaryVehicleProfilePayloadList: List<SecondaryVehicleProfilePayload>? = null

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): VehicleProfileViewHolder {
        val mDeveloperListItemBinding =
            DataBindingUtil.inflate<ItemVehicleProfileBinding>(
                LayoutInflater.from(context),
                R.layout.item_vehicle_profile,
                parent,
                false,
            )
        return VehicleProfileViewHolder(mDeveloperListItemBinding)
    }

    override fun getItemCount(): Int = secondaryVehicleProfilePayloadList?.size ?: 0

    override fun onBindViewHolder(
        holder: VehicleProfileViewHolder,
        position: Int,
    ) {
        secondaryVehicleProfilePayloadList?.let {
            val model = it[position]
            holder.itemVehicleProfileBinding.apply {
                executePendingBindings()
                holder.itemVehicleProfileBinding.savedVehicleProfileLayout.setOnClickListener {
                    secondaryVehicleProfileSelectedListener.setSelectedVehicleProfile(
                        secondaryVehicleProfilePayloadList?.get(position),
                        position,
                    )
                }
                DataBindingAdapters.loadImage(holder.itemVehicleProfileBinding.modelImage, model.image)
                holder.itemVehicleProfileBinding.modelYearText.text = model.modelYear
                holder.itemVehicleProfileBinding.modelNameText.text = model.modelName
                holder.itemVehicleProfileBinding.modelDescriptionText.text = model.modelDescription
                holder.itemVehicleProfileBinding.modelVinNumber.text = model.vin
            }
        }
    }

    fun notifyDataSetChanged(secondaryVehicleProfilePayloadList: List<SecondaryVehicleProfilePayload>) {
        this.secondaryVehicleProfilePayloadList = secondaryVehicleProfilePayloadList
        notifyDataSetChanged()
    }

    inner class VehicleProfileViewHolder(
        val itemVehicleProfileBinding: ItemVehicleProfileBinding,
    ) : RecyclerView.ViewHolder(itemVehicleProfileBinding.root)

    interface SecondaryVehicleProfileSelectedListener {
        fun setSelectedVehicleProfile(
            secondaryVehicleProfilePayload: SecondaryVehicleProfilePayload?,
            position: Int,
        )
    }
}
