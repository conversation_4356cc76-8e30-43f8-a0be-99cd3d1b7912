package com.toyota.oneapp.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import apptentive.com.android.util.isNotNullOrEmpty
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.CAS_CALL_TURN_OFF_SWITCH
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.CAS_CALL_TURN_ON_SWITCH
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.CAS_PUSH_TURN_OFF_SWITCH
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.CAS_PUSH_TURN_ON_SWITCH
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.CAS_SMS_TURN_OFF_SWITCH
import com.toyota.oneapp.analytics.AnalyticsEventParam.Companion.CAS_SMS_TURN_ON_SWITCH
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.databinding.ItemAlertPreferenceBinding
import com.toyota.oneapp.databinding.ItemNotificationPreferenceBinding
import com.toyota.oneapp.features.core.util.Constants
import com.toyota.oneapp.model.account.AlertPreference
import com.toyota.oneapp.model.account.NotificationPreference
import com.toyota.oneapp.model.subscription.AccountInfoSubscriber
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.dataBinding.DataBindingAdapters

class NotificationsPreferenceAdapter(
    val analyticsLogger: AnalyticsLogger,
    val oneAppPreferenceModel: OneAppPreferenceModel,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    var updated: Boolean = false
        private set

    private val onItemUpdated = {
        updated = true
        notifyDataSetChanged()
    }

    interface OnEventClickListener {
        fun onEventClick(event: NotificationSettingsEvent)
    }

    private var onEventClickListener: OnEventClickListener? = null

    fun setOnEventClickListener(listener: OnEventClickListener) {
        onEventClickListener = listener
    }

    private val combinedPreferencesList: MutableList<PreferenceItem> = mutableListOf()

    fun updatePreferencesList(preferencesList: List<NotificationPreference>) {
        updated = false

        for (notifPref in preferencesList.filter { it.isAvailable() }) {
            combinedPreferencesList.add(
                PreferenceItem(PreferenceItem.Type.NOTIFICATION_PREFERENCE, notifPref, null),
            )
            if (notifPref.alertPreferences != null && notifPref.alertPreferences.any { it.available }) {
                for (alertPref in notifPref.alertPreferences.filter { it.available }) {
                    combinedPreferencesList.add(
                        PreferenceItem(PreferenceItem.Type.ALERT_PREFERENCE, notifPref, alertPref),
                    )
                }
                combinedPreferencesList.add(PreferenceItem(PreferenceItem.Type.SPACER, null, null))
            }
        }

        notifyDataSetChanged()
    }

    override fun getItemViewType(position: Int): Int = combinedPreferencesList[position].type.id

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder =
        when (viewType) {
            PreferenceItem.Type.NOTIFICATION_PREFERENCE.id -> {
                NpViewHolder(
                    DataBindingUtil.inflate(
                        LayoutInflater.from(parent.context),
                        R.layout.item_notification_preference,
                        parent,
                        false,
                    ),
                )
            }
            PreferenceItem.Type.ALERT_PREFERENCE.id -> {
                ApViewHolder(
                    DataBindingUtil.inflate(
                        LayoutInflater.from(parent.context),
                        R.layout.item_alert_preference,
                        parent,
                        false,
                    ),
                )
            }
            PreferenceItem.Type.SPACER.id -> {
                SpacerViewHolder(
                    LayoutInflater.from(parent.context).inflate(
                        R.layout.recyclerview_thin_line_spacer,
                        parent,
                        false,
                    ),
                )
            }
            else -> super.createViewHolder(parent, viewType)
        }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        when (holder) {
            is NpViewHolder -> {
                val notificationPreference = combinedPreferencesList[position].notificationPreference
                holder.bind(
                    notificationPreference!!,
                    onItemUpdated,
                    analyticsLogger,
                    oneAppPreferenceModel,
                ) { event ->
                    onEventClickListener?.onEventClick(event)
                }
            }
            is ApViewHolder -> {
                val notificationPreference = combinedPreferencesList[position].notificationPreference!!
                val alertPreference = combinedPreferencesList[position].alertPreference!!
                holder.bind(alertPreference, notificationPreference, onItemUpdated)
            }
            is SpacerViewHolder -> {
                // Nothing needed, the spacer just sits there.
            }
        }
    }

    fun getList(): List<NotificationPreference> =
        combinedPreferencesList
            .filter { it.type == PreferenceItem.Type.NOTIFICATION_PREFERENCE }
            .map { it.notificationPreference!! }

    override fun getItemCount(): Int = combinedPreferencesList.size

    private class PreferenceItem(
        val type: Type,
        val notificationPreference: NotificationPreference?,
        val alertPreference: AlertPreference?,
    ) {
        enum class Type(
            val id: Int,
        ) {
            NOTIFICATION_PREFERENCE(1),
            ALERT_PREFERENCE(2),
            SPACER(3),
        }
    }

    class NpViewHolder(
        private val binding: ItemNotificationPreferenceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        private val viewModel = NpViewModel()
        private var oneAppPreferenceModel: OneAppPreferenceModel? = null
        private var handleEventClick: (NotificationSettingsEvent) -> Unit = {}

        fun bind(
            notificationPreference: NotificationPreference,
            onItemUpdated: () -> Unit,
            analyticsLogger: AnalyticsLogger,
            oneAppPreferenceModel: OneAppPreferenceModel,
            handleEventClick: (NotificationSettingsEvent) -> Unit,
        ) {
            this.oneAppPreferenceModel = oneAppPreferenceModel
            this.handleEventClick = handleEventClick
            viewModel.bind(
                notificationPreference,
                onItemUpdated,
                analyticsLogger,
                isPhoneVerified(),
            )

            viewModel.titleVisible.observeForever {
                DataBindingAdapters.setIsVisible(binding.npTitle, it)
            }
            viewModel.title.observeForever {
                binding.npTitle.text = it
            }
            viewModel.subTitleVisible.observeForever {
                DataBindingAdapters.setIsVisible(binding.npSubtitle, it)
            }
            viewModel.subTitle.observeForever {
                binding.npSubtitle.text = it
            }
            viewModel.pushAvailable.observeForever {
                DataBindingAdapters.setIsVisible(binding.npPushNotification, it)
                DataBindingAdapters.setIsVisible(binding.npSwitchPn, it)
                DataBindingAdapters.setIsVisible(binding.linePn, it)
            }
            viewModel.pushEnabled.observeForever {
                binding.npSwitchPn.isChecked = it
            }
            viewModel.emailAvailable.observeForever {
                DataBindingAdapters.setIsVisible(binding.npEmailNotification, it)
                DataBindingAdapters.setIsVisible(binding.npSwitchEmail, it)
                DataBindingAdapters.setIsVisible(binding.lineEmail, it)
            }
            viewModel.emailEnabled.observeForever {
                binding.npSwitchEmail.isChecked = it
            }
            viewModel.smsAvailable.observeForever {
                DataBindingAdapters.setIsVisible(binding.npSmsNotification, it)
                DataBindingAdapters.setIsVisible(binding.npSwitchSms, it)
                DataBindingAdapters.setIsVisible(binding.lineCall, it)
            }
            viewModel.smsEnabled.observeForever {
                binding.npSwitchSms.isChecked = it
            }
            viewModel.callAvailable.observeForever {
                DataBindingAdapters.setIsVisible(binding.npCall, it)
                DataBindingAdapters.setIsVisible(binding.npSwitchCall, it)
                DataBindingAdapters.setIsVisible(binding.lineSms, it)
            }
            viewModel.callEnabled.observeForever {
                binding.npSwitchCall.isChecked = it
            }

            binding.npSwitchPn.setOnCheckedChangeListener { _, isChecked ->
                viewModel.onPushPreferenceUpdated(
                    isChecked,
                )
            }
            binding.npSwitchEmail.setOnCheckedChangeListener { _, isChecked ->
                viewModel.onEmailPreferenceUpdated(
                    isChecked,
                )
            }
            binding.npSwitchSms.setOnCheckedChangeListener { _, isChecked ->
                viewModel.onSmsPreferenceUpdated(
                    isChecked,
                )
            }
            binding.npSwitchCall.setOnCheckedChangeListener { _, isChecked ->
                viewModel.onCallPreferenceUpdated(
                    isChecked,
                )
            }

            binding.executePendingBindings()
            binding.npSwitchPn.jumpDrawablesToCurrentState()
            binding.npSwitchEmail.jumpDrawablesToCurrentState()
            binding.npSwitchSms.jumpDrawablesToCurrentState()
            binding.npSwitchCall.setOnClickListener {
                if (!isPhoneVerified()) {
                    checkSmsCallFeatureAvailableAndShowDialog()
                    binding.npSwitchCall.isChecked = notificationPreference.callEnabled
                }
            }
            binding.npSwitchSms.setOnClickListener {
                if (!isPhoneVerified()) {
                    checkSmsCallFeatureAvailableAndShowDialog()
                    binding.npSwitchSms.isChecked = notificationPreference.smsEnabled
                }
            }
        }

        private fun isPhoneVerified(): Boolean =
            oneAppPreferenceModel?.getAccountInfoSubscriber()?.let {
                hasPhoneNumber(it) && isPhoneNumberVerified(it)
            } ?: false

        private fun checkSmsCallFeatureAvailableAndShowDialog() {
            if (oneAppPreferenceModel?.getAccountInfoSubscriber() != null) {
                val subscriber = oneAppPreferenceModel?.getAccountInfoSubscriber()
                val hasPhoneNumber = hasPhoneNumber(subscriber)
                val isPhoneNumberVerified = isPhoneNumberVerified(subscriber)
                val profileData = ProfileData(subscriber, subscriber?.firstName)

                when {
                    !hasPhoneNumber ->
                        handleEventClick(
                            NotificationSettingsEvent.UpdatePhoneNumber(profileData),
                        )

                    !isPhoneNumberVerified ->
                        handleEventClick(
                            NotificationSettingsEvent.ShowSignOutDialog,
                        )
                }
            }
        }

        private fun hasPhoneNumber(subscriber: AccountInfoSubscriber?): Boolean =
            subscriber?.customerPhoneNumbers?.any {
                it.phoneNumber.isNotNullOrEmpty()
            } == true

        private fun isPhoneNumberVerified(subscriber: AccountInfoSubscriber?): Boolean =
            subscriber?.customerPhoneNumbers?.any {
                it.phoneVerified
            } == true
    }

    class ApViewHolder(
        private val binding: ItemAlertPreferenceBinding,
    ) : RecyclerView.ViewHolder(
            binding.root,
        ) {
        private val viewModel = ApViewModel()

        fun bind(
            alertPreference: AlertPreference,
            notificationPreference: NotificationPreference,
            onItemUpdated: () -> Unit,
        ) {
            viewModel.bind(alertPreference, notificationPreference, onItemUpdated)
            binding.viewModel = viewModel

            binding.executePendingBindings()
            binding.alertItemSwitch.jumpDrawablesToCurrentState()
        }
    }

    inner class SpacerViewHolder(
        itemView: View,
    ) : RecyclerView.ViewHolder(itemView)

    class NpViewModel : BaseViewModel() {
        val title = MutableLiveData<String>()
        val titleVisible = MutableLiveData<Boolean>()
        val subTitle = MutableLiveData<String>()
        val subTitleVisible = MutableLiveData<Boolean>()
        val pushEnabled = MutableLiveData<Boolean>()
        val emailEnabled = MutableLiveData<Boolean>()
        val smsEnabled = MutableLiveData<Boolean>()
        val callEnabled = MutableLiveData<Boolean>()
        val pushAvailable = MutableLiveData<Boolean>()
        val emailAvailable = MutableLiveData<Boolean>()
        val smsAvailable = MutableLiveData<Boolean>()
        val callAvailable = MutableLiveData<Boolean>()

        private var notificationPreference: NotificationPreference? = null
        private var onItemUpdated: () -> Unit = {}
        private var analyticsLogger: AnalyticsLogger? = null
        private var isPhoneVerified: Boolean = false

        fun bind(
            notificationPreference: NotificationPreference,
            onItemUpdated: () -> Unit,
            analyticsLogger: AnalyticsLogger,
            isPhoneVerified: Boolean,
        ) {
            this.notificationPreference = notificationPreference
            this.onItemUpdated = onItemUpdated
            this.analyticsLogger = analyticsLogger
            this.isPhoneVerified = isPhoneVerified

            title.value = notificationPreference.name
            titleVisible.value = ToyUtil.isNotBlank(notificationPreference.name)
            subTitle.value = notificationPreference.subtitle
            subTitleVisible.value = ToyUtil.isNotBlank(notificationPreference.subtitle)
            pushEnabled.value = notificationPreference.pushEnabled
            emailEnabled.value = notificationPreference.emailEnabled
            smsEnabled.value = notificationPreference.smsEnabled
            callEnabled.value = notificationPreference.callEnabled
            pushAvailable.value = notificationPreference.pushAvailable
            emailAvailable.value = notificationPreference.emailAvailable
            smsAvailable.value = notificationPreference.smsAvailable
            callAvailable.value = notificationPreference.callAvailable
        }

        fun onPushPreferenceUpdated(isChecked: Boolean) {
            tagEvent(isChecked, CAS_PUSH_TURN_ON_SWITCH, CAS_PUSH_TURN_OFF_SWITCH)
            // Only update if something actually changed.
            if (notificationPreference?.pushEnabled != isChecked) {
                notificationPreference?.pushEnabled = isChecked
                onItemUpdated()
            }
        }

        fun onEmailPreferenceUpdated(isChecked: Boolean) {
            // Only update if something actually changed.
            if (notificationPreference?.emailEnabled != isChecked) {
                notificationPreference?.emailEnabled = isChecked
                onItemUpdated()
            }
        }

        fun onSmsPreferenceUpdated(isChecked: Boolean) {
            tagEvent(isChecked, CAS_SMS_TURN_ON_SWITCH, CAS_SMS_TURN_OFF_SWITCH)
            // Only update if something actually changed.
            if (notificationPreference?.smsEnabled != isChecked && isPhoneVerified) {
                notificationPreference?.smsEnabled = isChecked
                onItemUpdated()
            }
        }

        fun onCallPreferenceUpdated(isChecked: Boolean) {
            tagEvent(isChecked, CAS_CALL_TURN_ON_SWITCH, CAS_CALL_TURN_OFF_SWITCH)
            // Only update if something actually changed.
            if (notificationPreference?.callEnabled != isChecked && isPhoneVerified) {
                notificationPreference?.callEnabled = isChecked
                onItemUpdated()
            }
        }

        private fun tagEvent(
            isChecked: Boolean,
            on: String,
            off: String,
        ) {
            if (notificationPreference?.capability == Constants.ADVANCED_REAR_SEAT_REMINDER) {
                val param =
                    if (isChecked) {
                        on
                    } else {
                        off
                    }
                analyticsLogger?.logEventWithParameter(AnalyticsEvent.CAS.eventName, param)
            }
        }
    }

    class ApViewModel : BaseViewModel() {
        val title = MutableLiveData<String>()
        val preferenceEnabled = MutableLiveData<Boolean>()
        val switchEnabled = MutableLiveData<Boolean>()

        private var alertPreference: AlertPreference? = null
        private var onItemUpdated: () -> Unit = {}

        fun bind(
            alertPreference: AlertPreference,
            notificationPreference: NotificationPreference,
            onItemUpdated: () -> Unit,
        ) {
            this.alertPreference = alertPreference
            this.onItemUpdated = onItemUpdated

            title.value = alertPreference.preferenceName
            preferenceEnabled.value = alertPreference.enabled
            switchEnabled.value = notificationPreference.isSomethingEnabled()
        }

        fun onPreferenceUpdated(isChecked: Boolean) {
            // Only update if something actually changed.
            if (alertPreference?.enabled != isChecked) {
                alertPreference?.enabled = isChecked
                onItemUpdated()
            }
        }
    }
}

sealed class NotificationSettingsEvent {
    data class UpdatePhoneNumber(
        val profileData: ProfileData? = null,
    ) : NotificationSettingsEvent()

    object SignOut : NotificationSettingsEvent()

    object ShowSignOutDialog : NotificationSettingsEvent()
}

data class ProfileData(
    val accountSubscriber: AccountInfoSubscriber?,
    val profileName: String?,
)
