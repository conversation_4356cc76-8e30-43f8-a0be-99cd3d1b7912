package com.toyota.oneapp.adapter

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.fcm.ToyotaFCMService
import com.toyota.oneapp.model.dashboard.NotificationHistoryItem
import com.toyota.oneapp.util.GlideUtil
import com.toyota.oneapp.util.ToyUtil
import com.toyota.oneapp.util.ToyUtil.convertFromUTCTime
import com.toyota.oneapp.util.ToyotaConstants
import toyotaone.commonlib.log.LogTool
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.Date

private const val TAG = "NotificationHistoryAdapter"

class NotificationHistoryAdapter(
    private var historyItemMap: HashMap<String, MutableList<NotificationHistoryItem>>,
    var filterCategory: String?,
    private var callBackListener: NotificationDeepLinkListener,
) : RecyclerView.Adapter<NotificationHistoryAdapter.ViewHolder>() {
    enum class NotificationFilter(
        val service: String,
    ) {
        TOYOTA_FINANCIAL_SERVICE("Toyota Financial Service"),
        MAINTENANCE("Maintenance"),
        ACCOUNT("Account"),
        REMOTE_COMMAND("payment_alerts"),
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ViewHolder {
        val view =
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_notification_history,
                parent,
                false,
            )
        return ViewHolder(view)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int,
    ) {
        historyItemMap[filterCategory]?.get(position)?.let { holder.bind(it) }
    }

    private fun setSubtitleText(
        notificationDate: String?,
        readTimestamp: String?,
        subtitleTextView: TextView,
    ) {
        subtitleTextView.text =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val diff = getTimeDifference(notificationDate)
                subtitleTextView.context.run {
                    when {
                        (diff / ToyotaConstants.SECONDS_IN_YEAR) > 0 ->
                            if ((diff / ToyotaConstants.SECONDS_IN_YEAR) < 2) {
                                getString(
                                    R.string.nh_year,
                                )
                            } else {
                                getString(R.string.nh_years, diff / ToyotaConstants.SECONDS_IN_YEAR)
                            }
                        (diff / ToyotaConstants.SECONDS_IN_MONTH) > 0 ->
                            if ((diff / ToyotaConstants.SECONDS_IN_MONTH) < 2) {
                                getString(
                                    R.string.nh_month,
                                )
                            } else {
                                getString(R.string.nh_months, diff / ToyotaConstants.SECONDS_IN_MONTH)
                            }
                        (diff / ToyotaConstants.SECONDS_IN_DAY) > 0 ->
                            if ((diff / ToyotaConstants.SECONDS_IN_DAY) < 2) {
                                getString(R.string.nh_day)
                            } else {
                                getString(R.string.nh_days, diff / ToyotaConstants.SECONDS_IN_DAY)
                            }
                        (diff / ToyotaConstants.SECONDS_IN_HOUR) > 0 ->
                            if ((diff / ToyotaConstants.SECONDS_IN_HOUR) < 2) {
                                getString(
                                    R.string.nh_hour,
                                )
                            } else {
                                val hr = (diff / ToyotaConstants.SECONDS_IN_HOUR.toDouble()).toInt()
                                getString(R.string.nh_hours, hr)
                            }
                        else ->
                            when {
                                ((diff / 60) > 1) ->
                                    getString(
                                        R.string.nh_minutes,
                                        diff / ToyotaConstants.SECONDS_IN_MINUTE,
                                    )
                                ((diff / 60) > 0) -> getString(R.string.nh_minute)
                                (diff > 1) -> getString(R.string.nh_seconds, diff)
                                else -> getString(R.string.nh_second)
                            }
                    }
                }
            } else {
                readTimestamp
            }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun getTimeDifference(notificationDate: String?): Long {
        val dateInString =
            convertFromUTCTime(
                notificationDate,
                ToyotaConstants.UTC_TIMESTAMP_FORMAT,
            )
        val date = ZonedDateTime.parse(dateInString)
        val currentDate: LocalDateTime = LocalDateTime.now()
        var diff: Long =
            Date.from(currentDate.atZone(ZoneId.systemDefault()).toInstant()).time -
                Date
                    .from(
                        date.toLocalDateTime().atZone(ZoneId.systemDefault()).toInstant(),
                    ).time
        diff /= 1000
        return diff
    }

    private fun setIcon(
        iconUrl: String?,
        iconView: ImageView,
    ) {
        val context = iconView.context
        if (ToyUtil.isBlank(iconUrl)) {
            iconView.setImageResource(R.drawable.alert)
        } else {
            GlideUtil.loadImageWithoutPlaceholder(context, iconUrl, R.drawable.alert, iconView)
        }
    }

    private fun displayDeepLinkValue(
        deepLinkTextView: TextView,
        notificationHistoryItem: NotificationHistoryItem,
    ) {
        val notificationFilter = getNotificationFilter(notificationHistoryItem.category)
        if (notificationFilter == null) {
            deepLinkTextView.visibility = View.GONE
        } else {
            deepLinkTextView.apply {
                visibility = View.VISIBLE
                text = notificationHistoryItem.displayCategory
            }
        }
    }

    private fun sendDeepLink(notificationHistoryItem: NotificationHistoryItem) {
        callBackListener.onDeepLinkClicked(notificationHistoryItem)
    }

    override fun getItemCount(): Int = historyItemMap[filterCategory]?.size ?: 0

    fun filter(category: String) {
        if (filterCategory == category) {
            return
        }
        this.filterCategory = category
        LogTool.d(
            TAG,
            "The filter category: $category and payload: ${historyItemMap[category]}" +
                " and the size is ${historyItemMap[category]?.size}",
        )
        notifyDataSetChanged()
    }

    fun getViewedMessageIds(lastViewedItemPosition: Int): MutableList<String>? {
        val items = historyItemMap[filterCategory]
        return items
            ?.subList(0, lastViewedItemPosition.coerceAtMost(items.size - 1) + 1)
            ?.filter {
                it.isRead != true && !it.messageId.isNullOrEmpty()
            }?.map { it.messageId!! }
            ?.toMutableList()
    }

    private fun getNotificationFilter(category: String?): NotificationFilter? =
        NotificationFilter.values().find {
            it.service.equals(category, true)
        }

    private fun handleTouchAction(
        historyItem: NotificationHistoryItem,
        context: Context,
    ) {
        when (historyItem.category) {
            ToyotaFCMService.NAVI_SEND_TO_PHONE -> {
                launchMaps(historyItem, context)
            }
            ToyotaFCMService.TM_INSTALL_CATEGORY,
            ToyotaFCMService.TM_RELEASE_CATEGORY,
            ToyotaFCMService.TM_UPDATE_CATEGORY,
            ToyotaFCMService.OTA_UPDATES_21MM,
            -> {
                sendDeepLink(historyItem)
            }
            else -> { // No action needed as yet
            }
        }
    }

    private fun launchMaps(
        historyItem: NotificationHistoryItem,
        context: Context,
    ) {
        if (historyItem.latitude != null && historyItem.longitude != null) {
            val navString =
                String.format(
                    context.getString(R.string.maps_directions_url),
                    historyItem.latitude,
                    historyItem.longitude,
                )
            val mapIntent =
                Intent(Intent.ACTION_VIEW, Uri.parse(navString)).setPackage(
                    context.getString(R.string.google_map_package),
                )
            mapIntent.resolveActivity(context.packageManager)?.let {
                context.startActivity(mapIntent)
            }
        }
    }

    inner class ViewHolder(
        itemView: View,
    ) : RecyclerView.ViewHolder(itemView) {
        private var title: TextView = itemView.findViewById(R.id.nh_title)
        private var subTitle: TextView = itemView.findViewById(R.id.nh_date)
        private var image: ImageView = itemView.findViewById(R.id.nh_icon)
        private var unReadNot: ImageView = itemView.findViewById(R.id.iv_unread_mes)
        private var deepLinkName: TextView = itemView.findViewById(R.id.nh_deepLink)

        fun bind(historyItem: NotificationHistoryItem) {
            title.text = historyItem.message
            setSubtitleText(historyItem.notificationDate, historyItem.readTimestamp, subTitle)
            unReadNot.visibility = if (historyItem.isRead != true) View.VISIBLE else View.INVISIBLE
            setIcon(historyItem.iconUrl, image)
            itemView.setOnClickListener {
                handleTouchAction(historyItem, it.context)
            }
        }
    }

    fun updateReadStatus(newHistoryItem: HashMap<String, MutableList<NotificationHistoryItem>>) {
        historyItemMap = newHistoryItem
        notifyDataSetChanged()
    }

    interface NotificationDeepLinkListener {
        fun onDeepLinkClicked(notificationHistoryItem: NotificationHistoryItem)
    }
}
