package com.toyota.oneapp.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.toyota.oneapp.R
import com.toyota.oneapp.model.dashboard.NotificationHistoryPayload
import com.toyota.oneapp.ui.newdashboard.NotificationHistoryViewModel

class NotificationTitleAdapter(
    private var nhList: List<NotificationHistoryPayload>?,
    private val callBackListener: NotificationListener,
) : RecyclerView.Adapter<NotificationTitleAdapter.ViewHolder>() {
    var selectedItem: Int = 0

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): NotificationTitleAdapter.ViewHolder {
        val view =
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_notification_title,
                parent,
                false,
            )
        return ViewHolder(view)
    }

    override fun getItemCount(): Int {
        return nhList?.size ?: 0
    }

    override fun onBindViewHolder(
        holder: NotificationTitleAdapter.ViewHolder,
        position: Int,
    ) {
        nhList?.get(position)?.let { holder.bind(it, callBackListener, position) }
    }

    fun refresh(list: List<NotificationHistoryPayload>) {
        nhList = list
        notifyDataSetChanged()
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var titleTextView: TextView = itemView.findViewById(R.id.tv_notification_title)

        fun bind(
            payload: NotificationHistoryPayload,
            callBackListener: NotificationListener,
            position: Int,
        ) {
            titleTextView.apply {
                val backgroundResource: Int?
                val textColor: Int
                if (position == selectedItem) {
                    textColor = R.color.plain_black_russian
                    backgroundResource = R.drawable.bg_notification_title
                } else {
                    textColor = R.color.lightGray
                    backgroundResource = null
                }
                setTextColor(ContextCompat.getColor(context, textColor))
                background =
                    backgroundResource?.let {
                        ContextCompat.getDrawable(
                            context,
                            backgroundResource,
                        )
                    }
                text = getText(payload.modelDesc, context)
                setOnClickListener {
                    selectedItem = position
                    callBackListener.onPageSelected(position)
                }
            }
        }

        private fun getText(
            text: String?,
            context: Context,
        ): String? {
            val resource =
                when (text) {
                    NotificationHistoryViewModel.ALL_VEHICLES -> R.string.Notification_all_vehicles
                    NotificationHistoryViewModel.GENERAL_NOTIFICATIONS -> R.string.Notification_general
                    else -> null
                }
            return resource?.let { context.getString(resource) } ?: text
        }
    }

    interface NotificationListener {
        fun onPageSelected(position: Int)
    }
}
