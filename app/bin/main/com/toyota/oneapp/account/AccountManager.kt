package com.toyota.oneapp.account

import android.content.Context
import androidx.core.app.NotificationManagerCompat
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.core.ApplicationContext
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.features.pay.tfs.application.TFSLogic
import com.toyota.oneapp.network.api.manager.AccountAPIManager
import com.toyota.oneapp.network.callback.LogoutCallback
import com.toyota.oneapp.services.UserProfileTransferService
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.WearUtil
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.wear.WearAPIType
import toyotaone.commonlib.wear.WearConstants.BUS_OUTBOUND_DATA_KEY
import toyotaone.commonlib.wear.WearConstants.BUS_OUTBOUND_PATH
import toyotaone.commonlib.wear.WearResponse
import javax.inject.Inject

class AccountManager
    @Inject
    constructor(
        private val accountAPIManager: AccountAPIManager,
        private val preferenceModel: OneAppPreferenceModel,
        private val applicationData: ApplicationData,
        private val tfsLogic: TFSLogic,
        private val sharedDataSource: SharedDataSource,
        @ApplicationContext private val context: Context,
    ) {
        fun logout(callback: LogoutCallback? = null) {
            applicationData.clear()
            tfsLogic.clearTFSTokens()
            sharedDataSource.resetDataSource()
            sharedDataSource.resetMarketingBanner()

            // Clear all of the notifications
            val notificationManager = NotificationManagerCompat.from(context)
            notificationManager.cancelAll()

            accountAPIManager.logout(
                preferenceModel.getGuid(),
                preferenceModel.getDeviceToken(),
                callback,
            )
            applicationData.getSelectedVehicle()?.let { vehicle ->
                WearUtil.sendToWear(
                    BUS_OUTBOUND_PATH,
                    BUS_OUTBOUND_DATA_KEY,
                    WearResponse(WearAPIType.LOGOUT, vehicle.vin, null, null)
                        .toJsonString(),
                )
            }
            UserProfileTransferService.stopService(context)

            /**
             * Removing Shared Preferences of Flutter on Logout
             */
            try {
                val sharedPreferencesKeys =
                    context.getSharedPreferences(
                        "FlutterSecureKeyStorage",
                        Context.MODE_PRIVATE,
                    )
                val keyEditor = sharedPreferencesKeys.edit()
                keyEditor.clear()
                keyEditor.apply() // or editor.commit() to block the calling thread

                val sharedPreferencesValues =
                    context.getSharedPreferences(
                        "FlutterSecureStorage",
                        Context.MODE_PRIVATE,
                    )
                val valuesEditor = sharedPreferencesValues.edit()
                valuesEditor.clear()
                valuesEditor.apply() // or editor.commit() to block the calling thread
            } catch (ex: Exception) {
                LogTool.d("Clear SP", ex.message)
            }
        }
    }
