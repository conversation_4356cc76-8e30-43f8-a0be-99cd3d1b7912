package com.toyota.oneapp.analytics

import com.toyota.oneapp.app.ApplicationData
import javax.inject.Inject

class AAAnalyticsLogger
    @Inject
    constructor(
        private val analyticsLogger: AnalyticsLogger,
        private val applicationData: ApplicationData,
    ) : AnalyticsLogger {
        override fun logEvent(
            event: AnalyticsEvent,
            vararg args: Pair<String, Any?>,
        ) {
            logStringEvent(event.eventName, *args)
        }

        override fun logStringEvent(
            event: String,
            vararg args: Pair<String, Any?>,
        ) {
            analyticsLogger.logStringEvent(
                event,
                FirebaseAnalyticsLogger.SELECTED_VEHICLE_VIN_KEY to applicationData.getDefaultVehicle()?.vin,
                FirebaseAnalyticsLogger.SELECTED_VEHICLE_BRAND_KEY to applicationData.getDefaultVehicle()?.brand,
                FirebaseAnalyticsLogger.SELECTED_VEHICLE_REGION_KEY to applicationData.getDefaultVehicle()?.region,
                FirebaseAnalyticsLogger.SCREEN_TYPE_ID_KEY to FirebaseAnalyticsLogger.SCREEN_TYPE_VALUE_AUTO,
                *args,
                // Args is deliberately last to be inserted into the bundle.
                // This is so that any values explicitly set in args overwrite default values set above.
            )
        }

        override fun logUser(guid: String) {
            analyticsLogger.logUser(guid)
        }

        override fun logEventWithParameter(
            event: String,
            paramValue: String,
            paramKey: String,
        ) {
            analyticsLogger.logEventWithParameter(event, paramValue, paramKey)
        }
    }
