/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.analytics

class AnalyticsEventParam {
    companion object {
        const val EVENT_NAME = "eventName"
        const val EVENT_REASON = "reason"
        const val SIGNIN = "signin"
        const val ANDROID_BIOMETRIC_START = "android_biometric_start"
        const val ANDROID_EVENT = "android_event"
        const val ANDROID_BIOMETRIC_FAIL = "android_biometric_fail"
        const val ANDROID_KEEP_ME_LOGIN_START = "android_keep_me_login_start"
        const val ANDROID_INVALID_GRANT = "android_invalid_grant"
        const val USER_EVENT = "user_event"
        const val USER_SIGNIN_CTA = "User_SignIn_CTA"
        const val USER_SIGNUP_CTA = "User_SignUp_CTA"
        const val USER_APPTOUR_CTA = "User_AppTour_CTA"
        const val USER_CHANGE_LANGUAGE_REGION_CTA = "User_Change_Language_Region_CTA"
        const val USER_DEMO_APP_CTA = "User_Demo_App_CTA"
        const val ACCOUNT_SETTING_SELECT = "account_setting_select"
        const val PROFILE_FEEDBACK = "profile_feedback"
        const val PROFILE_INFO = "profile_info"
        const val PROFILE_INFO_SAVE = "profile_info_save"
        const val PROFILE_UPDATE_NAME = "profile_update_name"
        const val PROFILE_UPDATE_ADDRESS = "profile_update_address"
        const val PROFILE_NSETTINGS = "profile_nsettings"
        const val PROFILE_DATACONSENTS = "profile_dataconsents"
        const val EMAIL = "email"
        const val PHONE = "phone"
        const val CONNECTED_SERVICES_WAIVE = "connected_services_waive"
        const val SERVICE_CONNECT = "service_connect"
        const val REMOTE_CONNECT = "remote_connect"
        const val DYNAMIC_NAVIGATION = "dynamic_navigation"
        const val DESTINATION_ASSIST = "destination_assist"
        const val SAFETY_CONNECT = "safety_connect"
        const val SETUP_SUBSCRIPTION_DETAILS_1 = "setup_subscription_details_1"
        const val DEEPLINK = "deeplink"
        const val DEEPLINK_MANAGE_SUBSCRIPTION = "deeplink_manage_subscription"
        const val GARAGE_DEALER = "garage_dealer"
        const val GARAGE_SELECT = "garage_select"
        const val GARAGE_NICKNAME = "garage_nickname"
        const val GARAGE_REMOTEUSER = "garage_remoteuser"
        const val GARAGE_REMOVE = "garage_remove"
        const val GARAGE_SUBCRIPTION = "garage_subscriptions"
        const val GARAGE_TIRE = "garage_tire"
        const val GARAGE_FAQ = "garage_faq"
        const val GARAGE_PERSONAL_SETTINGS = "MG_GloveBox_PersonalSettings_LDlink_CTA"
        const val GARAGE_HOW_TO_VIDEOS = "garage_how_to_videos"
        const val GARAGE_VEHICLE_SOFTWARE = "garage_vehicle_software"
        const val DASHBOARD_CARD = "dashboard_card"
        const val DASHBOARD_CAMPAIGN = "dashboard_campaign"
        const val DASHBOARD_GARAGE = "dashboard_garage"
        const val DASHBOARD_RECALL = "dashboard_recall"
        const val DASHBOARD_SCORE = "dashboard_score"
        const val DASHBOARD_TRIPS = "dashboard_trips"
        const val DASHBOARD_UBI = "dashboard_ubi"
        const val DASHBOARD_VHR = "dashboard_vhr"
        const val DASHBOARD_VLOCATION = "dashboard_vlocation"
        const val DASHBOARD_OPTIN_DRIVERSCORE = "dashboard_optin_driverscore"
        const val DASHBOARD_COLLISION_ASSIST_DISPLAYED = "dashboard_collision_assist_displayed"
        const val DASHBOARD_HIDE = "dashboard_hide"
        const val DASHBOARD_CAMPAIGN_HIDE = "dashboard_campaign_hide"
        const val DASHBOARD_RECALL_HIDE = "dashboard_recall_hide"
        const val DASHBOARD_SCORE_HIDE = "dashboard_score_hide"
        const val DASHBOARD_TRIPS_HIDE = "dashboard_trips_hide"
        const val DASHBOARD_UBI_HIDE = "dashboard_ubi_hide"
        const val DASHBOARD_VALERTS_HIDE = "dashboard_valerts_hide"
        const val DASHBOARD_VLOCATION_HIDE = "dashboard_vlocation_hide"
        const val DASHBOARD_COLLISION_ASSIST_HIDE = "dashboard_collision_assist_hide"
        const val DASHBOARD_VHR_HIDE = "dashboard_vhr_hide"
        const val DASHBOARD_SERVICE_HISTORY = "dashboard_service_history"
        const val PAYMENT_SUBSCRIPTION = "payment_subscription"
        const val PAYMENT_SUBSCRIPTION_SUCCESS = "payment_subscription_success"
        const val PAYMENT_SUBSCRIPTION_FAILURE = "payment_subscription_failure"
        const val ZUORA_PAYMENT = "zuora_payment"
        const val ZUORA_EDIT_PAYMENT_METHOD_EDIT = "zuora_edit_payment_method_edit"
        const val ZUORA_EDIT_PAYMENT_METHOD_DONE = "zuora_edit_payment_method_done"
        const val ZUORA_DELETE_PAYMENT_METHOD = "zuora_delete_payment_method"
        const val ZUORA_DELETE_PAYMENT_METHOD_SUCCESS = "zuora_delete_payment_method_success"
        const val ZUORA_DELETE_PAYMENT_METHOD_FAILED = "zuora_delete_payment_method_failed"
        const val ZUORA_GET_PAYMENT_METHODS_SUCCESS = "zuora_get_payment_methods_success"
        const val ZUORA_GET_PAYMENT_METHODS_FAILED = "zuora_get_payment_methods_failed"
        const val ZUORA_ADD_CARD_PAYMENT_METHOD = "zuora_add_card_payment_method"
        const val ZUORA_ADD_ACH_PAYMENT_METHOD = "zuora_add_ach_payment_method"
        const val ZUORA_PAYMENT_PAGE_ADD_PAYMENT_SUCCESS = "zuora_payment_page_add_payment_success"
        const val ZUORA_PAYMENT_PAGE_ADD_PAYMENT_FAIL = "zuora_payment_page_add_payment_fail"
        const val ZUORA_PAYMENT_PAGE_PRIVACY = "zuora_payment_page_privacy"
        const val ZUORA_EDIT_PAYMENT_METHOD = "zuora_edit_payment_method"
        const val ZUORA_MAKE_DEFAULT_PAYMENT_METHOD = "zuora_make_default_payment_method"
        const val ZUORA_MAKE_DEFAULT_PAYMENT_METHOD_SUCCESS = "zuora_make_default_payment_method_success"
        const val ZUORA_MAKE_DEFAULT_PAYMENT_METHOD_FAILED = "zuora_make_default_payment_method_failed"
        const val ZUORA_EDIT_PAYMENT_METHOD_SUCCESS = "zuora_edit_payment_method_success"
        const val ZUORA_EDIT_PAYMENT_METHOD_FAILED = "zuora_edit_payment_method_failed"
        const val ZUORA_MANAGE_FLOW_GET_PAYMENTS = "zuora_manage_flow_get_payments"
        const val ZUORA_ADD_PAYMENT_WITHOUT_SUBSCRIPTIONS = "zuora_add_payment_without_subscriptions"
        const val ZUORA_PAYMENT_TOKEN_RECEIVED = "zuora_payment_token_received"
        const val ZUORA_PAYMENT_TOKEN_FAILED = "zuora_payment_token_failed"
        const val CANCEL_PAID_SUBSCRIPTION_SUCCESS = "cancel_paid_subscription_success"
        const val CANCEL_PAID_SUBSCRIPTION_FAILED = "cancel_paid_subscription_failed"
        const val CANCEL_PAID_SUBSCRIPTION_MANUAL_REFUND = "cancel_paid_subscription_manual_refund"
        const val PAYMENT_SUBSCRIPTION_UPDATE_AUTORENEW_SUCCESS = "payment_subscription_update_autorenew_success"
        const val PAYMENT_SUBSCRIPTION_UPDATE_AUTORENEW_FAILED = "payment_subscription_update_autorenew_failed"
        const val DRIVER_SCORE_OPTIN = "driver_score_optin"
        const val DRIVER_SCORE_OPTIN_SUCCESS = "driver_score_optin_success"
        const val DRIVER_SCORE_OPTIN_FAILED = "driver_score_optin_failed"
        const val DRIVER_SCORE_OPTOUT = "driver_score_optout"
        const val DRIVER_SCORE_OPTOUT_SUCCESS = "driver_score_optout_success"
        const val DRIVER_SCORE_OPTOUT_FAILED = "driver_score_optout_failed"
        const val ASSIGN_REMOTE_USER = "assign_remoteuser"
        const val ASSIGN_REMOTE_USER_INVITE = "assign_remoteuser_invite"
        const val ASSIGN_REMOTE_USER_INVITE_COMPLETED = "assign_remoteuser_invite_completed"
        const val ASSIGN_REMOTE_USER_SEARCH = "assign_remoteuser_search"
        const val ASSIGN_REMOTE_USER_SELF = "assign_remoteuser_self"
        const val ASSOCIATION_FAILURE = "association_failure"
        const val GARAGE_REMOVE_SUCCESSFUL = "garage_remove_successful"
        const val GARAGE_REMOVE_UNSUCCESSFUL = "garage_remove_unsuccessful"
        const val PAYMENT_PAGE_LAUNCHED_ZUORA = "payment_page_launched_zuora"
        const val DEEPLINK_PAYMENT_METHODS = "deeplink_payment_methods"
        const val USERS_FINISHSETUP_COMPLETEFAILURE_CTA = "Users_FinishSetup_CompleteFailure_CTA"
        const val TRIAL = "trial"
        const val PAID = "paid"

        // AMBER ALERT
        const val AMBER_ALERT = "Amber"
        const val AMBER_ALERT_ACCEPT = "Dashboard_Amber_Alert_Accept"
        const val AMBER_ALERT_DECLINE = "Dashboard_Amber_Alert_Decline"
        const val PRIVACY_PORTAL_CONSENT_ACCEPT = "Privacy_Consent_Accept"
        const val PRIVACY_PORTAL_CONSENT_DECLINE = "Privacy_Consent_Decline"
        const val PRIVACY_PORTAL_CONSENT_DETAIL = "Privacy_Consent_Detail"
        const val PRIVACY_PORTAL_CONSENT_CATEGORY = "Privacy_Consent_Category"

        // Vehicle Software update for 20TM and 21MM
        const val VEHICLE_SOFTWARE_OTA = "vehicle_software_ota"

        const val DASHBOARD_VEHICLE_SWITCHER_CHEVRON = "dashboard_vehicle_switcher_chevron"

        // Vehicle Info
        const val VEHICLE_INFO = "vehicle_info"
        const val VEHICLE_INFO_EDIT_NICKNAME = "vehicle_info_edit_nickname"
        const val VEHICLE_INFO_GLOVEBOX_TILE_TAP = "vehicle_info_glovebox_tile_tap"
        const val VEHICLE_INFO_SUBSCRIPTIONS_TILE_TAP = "vehicle_info_subscriptions_tile_tap"
        const val VEHICLE_INFO_CONNSERVICESSUPP_TILE_TAP = "vehicle_info_conn_services_supp_tile_tap"
        const val VEHICLE_INFO_APPSUITE_TILE_TAP = "vehicle_info_app_suite_tile_tap"
        const val VEHICLE_INFO_VEHICLESOFTWARE_TILE_TAP = "vehicle_info_vehicle_software_tile_tap"
        const val VEHICLE_INFO_REMOVEVEHICLE_CTA = "vehicle_info_remove_vehicle_cta"

        // Glovebox
        const val GARAGE_VEHICLE_CAPABILITY = "garage_vehicle_capability"
        const val VEHICLE_SPECIFICATIONS = "vehicle_specifications"
        const val WARNING_LIGHTS_GUIDE = "tap_warning_lights_guide"
        const val GARAGE_MANUALS = "garage_manuals"
        const val SIENNA_USERMANUAL_CLICKED = "sienna_user_manual_clicked"
        const val TOYOTA_FOR_FAMILIES_CLICKED = "toyota_for_families_clicked"

        // Vehicle Switcher
        const val VEHICLE_SWITCHER = "vehicle_switcher"
        const val VEHICLE_SWITCHER_ADD_VEHICLE = "vehicle_switcher_add_plus_vehicle_cta"
        const val VEHICLE_SWITCHER_MAKE_DEFAULT = "vehicle_switcher_make_default_cta"
        const val VEHICLE_SWITCHER_REMOVE_VEHICLE = "vehicle_switcher_remove_vehicle_cta"
        const val VEHICLE_SWITCHER_SELECT_VEHICLE = "vehicle_switcher_select_vehicle_cta"
        const val VEHICLE_SWITCHER_FETCH_SUBSCRIPTION = "vehicle_switcher_fetch_subsc_success"

        // WIFI REMINDER
        const val WIFI_REMINDER = "wifi_reminder"
        const val WIFI_TRIAL_REMINDER_DASHBOARD_CTA = "wifi_trial_dashboard_reminder_cta"
        const val WIFI_TRIAL_VIEW_ACCOUNT_CTA = "wifi_trial_view_account_cta"
        const val WIFI_TRIAL_POPUP_CLOSE_CTA = "wifi_trial_popup_close_cta"

        // BottomNav_ShopTab Event Group
        const val BOTTOM_NAV_SHOP_TAP = "bottomnav_shoptab"

        // SHOP
        const val SXM_RADIO = "sirius_xm_radio"
        const val SHOPTAB_SUBSC_MANAGESUBSCRIPTIONS_CTA = "shoptab_subsc_manage_subsc_cta"
        const val SHOPTAB_INSURANCEOFFERS_TILE_TAP = "shoptab_insurance_offers_tile_tap"
        const val SHOPTAB_SIRIUS_XM_TILE_TAP = "shoptab_siriusxm_tile_tap"
        const val SHOPTAB_PARTS_ACCESSORIES_TILE_TAP = "shoptab_parts_accessories_tile_tap"
        const val SHOPTAB_DOWNLOADSXMAPP_CTA = "shoptab_download_siriusxm_app_cta"
        const val SHOPTAB_SIRIUSXMLEARNMORE_CTA = "shoptab_siriusxm_learn_more_cta"
        const val SHOPTAB_ANNOUNCEMENT_TILE_TAP = "shoptab_announcements_tile_tap"

        const val SIRIUS_XM_DISPLAYED = "sirius_xm_radio_card_displayed"
        const val SXM_RADIO_DETAILS_PAGE_CLICK_CTA = "sirius_xm_radio_details_page_click_cta"

        // Service Page
        const val SERVICE_PAGE = "bottomnav_servicetab"
        const val SERVICE_PAGE_MAKE_AN_APPOINTMENT_CTA = "servicetab_serviceappt_makeanappt_cta"
        const val SERVICE_PAGE_ROADSIDE_ASSIST_FAQ_CTA = "servicetab_roadside_assistance_faqs_cta"
        const val SERVICE_PAGE_ROADSIDE_ASSIST_CALL_CTA = "servicetab_roadside_assistance_call_cta"

        // Advance Remote Commands
        const val REMOTE_LIGHTS = "remote_commands_lights"
        const val REMOTE_HAZARD = "remote_commands_hazard"
        const val REMOTE_CLIMATE = "remote_commands_climate"
        const val REMOTE_BUZZER = "remote_commands_buzzer"
        const val REMOTE_TRUNK_LOCK = "remote_commands_trunk_lock"
        const val REMOTE_TRUNK_UN_LOCK = "remote_commands_trunk_unlock"
        const val REMOTE_TAILGATE_LOCK = "remote_commands_tailgate_lock"
        const val REMOTE_TAILGATE_UN_LOCK = "remote_commands_tailgate_unlock"
        const val REMOTE_HORN = "remote_commands_horn"
        const val REMOTE_GUEST = "remote_commands_guest"

        // Dashboard card
        const val DASHBOARD_REMOTE_START = "remote_start"
        const val DASHBOARD_REMOTE_STOP = "remote_stop"
        const val DASHBOARD_REMOTE_DOOR_LOCK = "remote_lock"
        const val DASHBOARD_REMOTE_DOOR_UN_LOCK = "remote_unlock"
        const val DASHBOARD_VEHICLE_INFO = "dashboard_vehicle_info_cta"
        const val DASHBOARD_SWIPE_REFRESH = "dashboard_swipe_down_to_refresh_action"

        // Find
        const val FIND_PAGE = "bottomnav_findtab"
        const val FINDTAB_LAST_PARKED_LOCATION_TAP = "findtab_last_parked_location_tap"
        const val FINDTAB_CURRENT_LOCATION_TILE_TAP = "findtab_current_location_cursor_icon_tap"
        const val FINDTAB_STATIONS_TILE_TAP = "findtab_stations_tile_tap"
        const val FINDTAB_DEALERS_TILE_TAP = "findtab_dealers_tile_tap"
        const val FINDTAB_RENTALS_TILE_TAP = "findtab_rentals_tile_tap"
        const val FINDTAB_DESTINATIONS_TILE_TAP = "findtab_destinations_tile_tap"
        const val FINDTAB_DRIVE_PULSE_TILE_TAP = "findtab_recent_trips_tile_tap"

        // Pay Page
        const val BOTTOM_NAV_PAY_TAP = "bottom_nav_pay_tab"
        const val PAY_TAB_WALLET = "paytab_wallet"

        // No Vehicle Dashboard
        const val DASHBOARD_NO_VEHICLE_ADD_VEHICLE_CTA = "dashboard_no_vehicle_add_a_vehicle_cta"
        const val DASHBOARD_NO_VEHICLE_MAKE_RES_CTA = "dashboard_no_vehicle_make_a_res_cta"
        const val DASHBOARD_NO_VEHICLE_UPCOMING_RES_CTA = "dashboard_no_vehicle_upcoming_res_cta"

        // Account Notification
        const val ACCOUNT_NOTIFICATION_ICON_TAP = "acct_ntfns_account_icon_tap"
        const val ACCOUNT_NOTIFICATION_SIGN_OUT_TAP = "acct_ntfns_sign_out_tap"
        const val ACCOUNT_NOTIFICATION_ACCOUNT_TAP = "acct_ntfns_account_cta"
        const val ACCOUNT_NOTIFICATION_NOTIFICATION_TAP = "acct_ntfns_notifications_tile_tap"
        const val ACCOUNT_NOTIFICATION_ANNOUNCEMENTS_TAP = "acct_ntfns_announcements_tile_tap"
        const val ACCOUNT_NOTIFICATION_TAKE_A_TOUR_TAP = "acct_ntfns_take_a_tour_tile_tap"
        const val ACCOUNT_NOTIFICATION_DAR_MODE_TOGGLE = "acct_ntfns_dark_mode_toggle"

        // Remote States
        const val RS_NOTIFICATION_DISABLED = "dashboard_remote_disabled_turn_on_cta"
        const val RS_SUBSCRIPTION_EXPIRED = "dashboard_subsc_expired_renew_cta"
        const val RS_SUBSCRIPTION_CANCELLED = "dashboard_subsc_cancelled_activate_cta"
        const val RS_ACTIVATE_REMOTE = "dashboard_remote_activate_cta"
        const val RS_ACTIVATE_REMOTE_PENDING = "dashboard_remote_pending_refresh_cta"
        const val RS_UNABLE_TO_ACTIVATE_REMOTE = "dashboard_unable_activation_activate_cta"
        const val RS_REMOTE_ACTIVATION_ERROR = "dashboard_activation_error_contact_cta"
        const val RS_VEHICLE_STOLEN = "dashboard_stolen_vehicle_reactivate_cta"
        const val RS_REMOTE_SHARE_CTA = "dashboard_primary_user_remove_driver_cta"

        // Remote States Popup
        const val RS_REMOTE_LEXUS_AUTH_CTA = "dashboard_remote_verify_ownership_ok_cta"
        const val RS_REMOTE_SHARE_POPUP_CTA = "dashboard_primary_user_remove_cta"
        const val RS_REMOTE_STOLEN_VEHICLE_POPUP_CANCEL_CTA = "dashboard_stolen_vehicle_cancel_cta"
        const val RS_REMOTE_STOLEN_VEHICLE_POPUP_CONFIRM_CTA = "dashboard_stolen_vehicle_confirm_cta"

        // EV
        const val DASHBOARD_FUEL_WIDGET_FIND_STATION = "vehicle_ev_dashboard_find_stations_cta"
        const val DASHBOARD_FUEL_WIDGET_CHARGE_INFO = "vehicle_ev_dashboard_charge_info_cta"
        const val DASHBOARD_FUEL_WIDGET_CHARGE_STATUS = "vehicle_ev_dashboard_charge_status_cta"
        const val CHARGE_INFO_LANDING_PAGE = "vehicle_charge_station_page"

        // Charge schedule
        const val VEHICLE_EV_CREATE_A_SCHEDULE_BUTTON_CTA = "vehicle_ev_create_a_schedule_button_cta"
        const val VEHICLE_EV_ECO_CHARGE_TOGGLE = "vehicle_ev_eco_charge_toggle"
        const val LEARN_MORE = "vehicle_ev_eco_charge_find_out_more"
        const val SCHEDULE_START_TIME = "vehicle_ev_schedule_start_time"
        const val SCHEDULE_DEP_TIME = "vehicle_ev_schedule_departure_time"

        // Digital Key (migrated from Analytics Event to now use Event Groups)
        const val PROFILE_DK_VERIFY_CONFIRMATION_CODE = "profile_dk_verify_confirmation_code"
        const val PROFILE_DK_NEW_CONFIRMATION_CODE = "profile_dk_request_new_confirmation_code"
        const val DK_SETUP_ENROLL_FAILED = "dk_setup_failed_enroll"
        const val DK_SETUP_ENROLL_SUCCESS = "dk_setup_success_enroll"
        const val DK_INITIALIZATION_FAILED = "dk_initialization_failed"
        const val DK_INITIALIZATION_SUCCESS = "dk_initialization_success"
        const val DK_REVOKE_KEY_FAILED = "dk_revoke_key_failed"
        const val DK_REVOKE_KEY_SUCCESS = "dk_revoke_key_success"
        const val DK_ENDORSEMENT_FAILED = "dk_setup_failed_endorsement"
        const val DK_ENDORSEMENT_SUCCESS = "dk_setup_success_endorsement"
        const val DK_DOWNLOAD_FAILED = "dk_setup_failed_download_key"
        const val DK_DOWNLOAD_TOKEN_FAILED = "dk_setup_failed_download_token"
        const val DK_DOWNLOAD_TOKEN_SUCCESS = "dk_setup_success_download_token"
        const val DK_SELF_REGISTRATION_SUCCESS = "dk_selfregistration_success"
        const val DK_SELF_REGISTRATION_FAILED = "dk_selfregistration_failed"
        const val DK_DOWNLOAD_SUCCESS = "dk_setup_success_dk_download"
        const val DK_REQUEST_TSC_FAILED = "dk_requesttsc_failed"
        const val DK_REQUEST_TSC_SUCCESS = "dk_requesttsc_success"
        const val DK_HSM_FAILED = "dk_setup_failed_hsm_unlock"
        const val DK_HSM_SUCCESS = "dk_setup_success_hsm_unlock"
        const val DK_SETUP_FAILED_ACTIVATE = "dk_setup_failed_activate"
        const val DK_SETUP_SUCCESS_ACTIVATE = "dk_setup_success_activate"
        const val DK_SETUP_FAILED_REGISTER_OWNER = "dk_setup_failed_register_owner"
        const val DK_SETUP_FAILED_REGISTER_SHARED = "dk_setup_failed_register_shared"
        const val DK_SETUP_SUCCESS_REGISTER_OWNER = "dk_setup_success_register_owner"
        const val DK_SETUP_SUCCESS_REGISTER_SHARED = "dk_setup_success_register_shared"
        const val DK_SETUP_CHECKLIST = "dk_setup_checklist"

        // These events are only on iOS since we do not interface with the device agent
        // const val DK_SETUP_DA_BACKGROUND_TASK = "dk_setup_da_background_task";
        // const val DK_SETUP_DA_BACKGROUND_TASK_REGISTER = "dk_setup_da_background_task_register";

        const val DK_LOCK_COMMAND = "dk_lock_command"
        const val DK_UNLOCK_COMMAND = "dk_unlock_command"

        // Digital Key Popups
        const val VEHICLE_DIGITAL_KEY_SETUP = "vehicle_digital_key_setup"
        const val VEHICLE_DIGITAL_KEY_MANAGE = "vehicle_digital_key_manage"
        const val VEHICLE_DIGITAL_KEY_SETUP_ERROR = "vehicle_digital_key_setup_error"
        const val VEHICLE_DIGITAL_KEY_BLUETOOTH_ERROR = "vehicle_digital_key_bluetooth_error"
        const val VEHICLE_DIGITAL_KEY_TRANSFER = "vehicle_digital_key_transfer"
        const val VEHICLE_DIGITAL_KEY_SHARED_ACCEPT = "vehicle_digital_key_shared_accept"
        const val VEHICLE_DIGITAL_KEY_SHARED_DECLINE = "vehicle_digital_key_shared_decline"
        const val VEHICLE_DIGITAL_KEY_SHARED_INVITE = "vehicle_digital_key_shared_invite"

        // Dashboard card
        const val NC_PREFERRED_DEALER = "dashboard_preferred_dealer_tile_tap"
        const val NC_MAINTENANCE_SCHEDULE = "dashboard_maintenance_schedule_tile_tap"
        const val NC_SERVICE_HISTORY = "dashboard_service_history_tile_tap"
        const val NC_APPOINTMENTS = "dashboard_appointments_tile_tap"

        const val TFS_MAKE_A_PAYMENT_CTA = "tfs_OneTimePymt_make_payment_card_cta"

        // Ftue
        const val FTUE_SKIP_FOR_NOW_CTA = "ftue_skip_for_now_cta"
        const val FTUE_WHATS_NEW_CTA = "ftue_see_whats_new_cta"
        const val FTUE_SWITCHER_NEXT_CTA = "ftue_vehicle_switcher_next_cta"
        const val FTUE_REMOTE_NEXT_CTA = "ftue_remote_connect_next_cta"
        const val FTUE_STATUS_NEXT_CTA = "ftue_status_tab_next_cta"
        const val FTUE_HEALTH_NEXT_CTA = "ftue_health_tab_next_cta"
        const val FTUE_VEHICLE_INFO_DONE_CTA = "ftue_vehicle_info_done_cta"
        const val FTUE_SKIP_FOR_NOW_OK_CTA = "ftue_skip_for_now_ok_cta"

        // Subscription Snippet

        const val DASHBOARD_SUBSCRIPTION_SNIPPET_CTA = "dashboard_subsc_expiring_snippet_tap"
        const val DASHBOARD_SUBSCRIPTION_SNIPPET_CLOSE_CTA = "dashboard_subsc_expiring_snippet_close"

        // Announcement
        const val DASHBOARD_ANNOUNCEMENT_CTA = "dashboard_announcements_cta"

        // Adding vehicle / onboarding
        const val ADD_VEHICLE = "add_vehicle"
        const val ADD_VEHICLE_BY_CODE_CTA = "add_vehicle_connect_by_code_CTA"

        // Cabin Awareness
        const val CAS_REMOTE_DETECTED_TILE = "cas_remote_motion_detected"
        const val CAS_VEHICLE_DETECTED_TILE = "cas_status_motion_detected"
        const val CAS_STOP_ALERT_DROP_DOWN = "cas_motion_detected_stop_alert_drop"
        const val CAS_MUTE_ALERT_DROP_DOWN = "cas_motion_detected_mute_alert_drop"
        const val CAS_FALSE_DETECTION_DROP_DOWN = "cas_motion_detected_report_false_drop"
        const val CAS_OWNERS_MANUAL_DROP_DOWN = "cas_motion_detected_more_info_drop"
        const val CAS_NOTIFICATION_SETTINGS_DROP_DOWN = "cas_motion_detected_notif_setting_drop"
        const val CAS_FALSE_DETECTION_HYPERLINK = "cas_motion_detected_report_false_click"
        const val CAS_OWNERS_MANUAL_HYPERLINK = "cas_motion_detected_owners_manual_click"
        const val CAS_NOTIFICATION_SETTINGS_HYPERLINK = "cas_motion_detected_notif_setting_click"
        const val CAS_REPORT_CTA = "cas_report_cta"
        const val CAS_REPORT_NOTHING_IN_VEHICLE = "nothing in vehicle"
        const val CAS_REPORT_UNWANTED_ALERT = "unwanted alert"
        const val CAS_REPORT_OTHERS = "other"
        const val CAS_REPORT_CANCEL_CTA = "cas_report_cancel_cta"
        const val CAS_PUSH_TURN_ON_SWITCH = "adv_rear_seat_reminder_push_toggle_on"
        const val CAS_PUSH_TURN_OFF_SWITCH = "adv_rear_seat_reminder_push_toggle_off"
        const val CAS_SMS_TURN_ON_SWITCH = "adv_rear_seat_reminder_sms_toggle_on"
        const val CAS_SMS_TURN_OFF_SWITCH = "adv_rear_seat_reminder_sms_toggle_off"
        const val CAS_CALL_TURN_ON_SWITCH = "adv_rear_seat_reminder_call_toggle_on"
        const val CAS_CALL_TURN_OFF_SWITCH = "adv_rear_seat_reminder_call_toggle_off"

        // Climate
        const val FRONT_DEFROST = "front_defrost"
        const val BACK_DEFROST = "back_defrost"
        const val REMOTE_AIR_CIRC_INSIDE = "Remote_Air_Circ_Total_inside"
        const val REMOTE_AIR_CIRC_OUTSIDE = "Remote_Air_Circ_Total_outside"
        const val STEERING_WHEEL = "steering_wheel"
        const val REAR_SEAT = "rear_seat"
        const val REAR_PASSENGER = "rear_passenger"
        const val FRONT_SEAT = "front_seat"
        const val PASSENGER_SEAT = "passenger_seat"

        // Charge Info
        const val CHARGE_HISTORY_CARD = "vehicle_ev_charge_history"
        const val CHARGE_SCHEDULE_CARD = "vehicle_ev_charge_schedule"
        const val STATISTICS_CARD = "vehicle_ev_charge_statistics"
        const val STATISTICS_CARD_TAP = "vehicle_ev_charge_info_stats_card_tap"
        const val STATISTICS_LEARN_MORE = "vehicle_ev_eco_charge_find_out_more"
        const val STATISTICS_MONTH_YEAR_PICKER = "vehicle_ev_statistics_date_selection"
        const val CLEAN_ASSIST_ENROLL = "vehicle_ev_clean_assist"
        const val CLEAN_ASSIST_VIEW = "vehicle_ev_clean_assist_view"
        const val CHARGE_INFO_FIND_NEARBY_STATIONS = "vehicle_ev_pub_find_from_chargeinfo_link"

        // Charge History
        const val CHARGE_HISTORY_DETAILS = "vehicle_ev_charge_details_view"
        const val CHARGE_HISTORY_DATE_FILTER = "vehicle_ev_history_date_selection"

        // Guest Driver
        const val GSETTINGS_CURFEW = "gsettings_curfew"
        const val GSETTINGS_MILE = "gsettings_mile"
        const val GSETTINGS_SPEED = "gsettings_speed"
        const val GSETTINGS_TIME = "gsettings_time"
        const val GSETTINGS_AREA = "gsettings_area"

        // Dealer Service Appointment
        const val VEHICLE_PAGE = "vehicle"
        const val DEALER_SERVICE_APPOINTMENT_INITIALIZE_PAGE = "dealer_service_appointment_initialize_page"
        const val VEHICLE_FETCH_SERVICEDEALER_SUCCESS = "vehicle_fetch_servicedealer_success"
        const val VEHICLE_FETCH_SERVICEDEALER_FAILURE = "vehicle_fetch_servicedealer_failure"
        const val VEHICLE_MAINTENANCE_TIMELINE_SUCCESS = "vehicle_maintenance_timeline_success"
        const val VEHICLE_MAINTENANCE_TIMELINE_FAILURE = "vehicle_maintenance_timeline_failure"
        const val SERVICE_HISTORY_SUCCESS = "service_history_success"
        const val SERVICE_HISTORY_FAILURE = "service_history_failure"
        const val VEHICLE_FETCH_SERVICE_APPOINTMENT_SUCCESS = "vehicle_fetch_service_appointment_success"
        const val VEHICLE_FETCH_SERVICE_APPOINTMENT_FAILURE = "vehicle_fetch_service_appointment_failure"
        const val DEALER_SERVICE_APPOINTMENT_INITIALIZE_SUCCESS = "dealer_service_appointment_initialize_success"
        const val DEALER_SERVICE_APPOINTMENT_INITIALIZE_FAILURE = "dealer_service_appointment_initialize_failure"
        const val DEALER_SEARCH_INITIALIZE_PAGE = "dealer_search_initialize_page"
        const val DEALER_SEARCH_FETCH_DEALERS_SUCCESS = "dealer_search_fetch_dealers_success"
        const val DEALER_SEARCH_FETCH_DEALERS_FAILURE = "dealer_search_fetch_dealers_failure"
        const val DEALER_SEARCH_FETCH_FILTERS_SUCCESS = "dealer_search_fetch_dealers_success"
        const val DEALER_SEARCH_FETCH_FILTERS_FAILURE = "dealer_search_fetch_dealers_failure"
        const val DEALER_SEARCH_FETCH_DEALERS_FILTERED_SUCCESS = "dealer_search_fetch_dealers_filtered_success"
        const val DEALER_SEARCH_FETCH_DEALERS_FILTERED_FAILURE = "dealer_search_fetch_dealers_filtered_failure"

        const val VEHICLE_SERVICE_APPOINTMENT_ODOMETER_SETUP_PAGE = "vehicle_service_appointment_odometer_setup_page"

        // Dealer Service Call
        const val PREFERREDDEALER_CALL = "preferreddealer_call"
        const val SERVICE_SCHEDULE = "SERVICE_SCHEDULE"
        const val APPOINTMENT_FROM_PREFEREDDEALER = "appointment_from_preffereddealer"
        const val SUPPORTDC_MAKEAPPOINTMENT_CHANGEDEALER = "supportDC_makeappointment_changedealer"

        const val DEALER_SERVICE_UPCOMING_APPOINTMENTS_SERVICE = "appointmentview_upcomingservice"
        const val DEALER_SERVICE_PAST_APPOINTMENTS_SERVICE = "appointmentview_pastservice"
        const val DEALER_SERVICE_APPOINTMENT_NEW_BIDIR_SYNC = "appointmentnew_bidir_sync"
        const val DEALER_SERVICE_APPOINTMENT_MODIFIED_BIDIR_SYNC =
            "appointmentmodified_bidir_sync"
        const val DEALER_SERVICE_APPOINTMENT_CANCEL_BIDIR_SYNC = "appointmentcancel_bidir_sync"

        // Dealer Appointment Service
        const val SUPPORTDC_REPAIRS_DEALERRECOMMENDED =
            "supportDC_repairs_dealerrecommended"
        const val SUPPORTDC_REPAIRS_FACTORYRECOMMENDED =
            "supportDC_repairs_factoryrecommended"
        const val SUPPORTDC_REPAIRS_ALLSERVICES =
            "supportDC_repairs_allservices"

        // Dealer Appointment Details
        const val ADD_TO_CALENDER = "supportDC_addtocalendar"
        const val SERVICE_DETAILS = "appointmentview_servicedetails"
        const val ADVISOR_V_CARD = "appointmentview_selectserviceVcard"
        const val MAKE_APPOINTMENT_VIEW_AVAILABLE_TIME_SLOTS = "supportDC_viewavailabledatetime"
        const val MAKE_APPOINTMENT_SELECT_DATE = "supportDC_makeappointment_selectdate"
        const val MAKE_APPOINTMENT_SELECT_TIME = "supportDC_makeappointment_selecttime"

        // EV Wallet
        const val WALLET_GROUP = "wallet_group"
        const val WALLET_ADD_CARD_SUCCESS = "wallet_add_card_success"
        const val WALLET_ADD_CARD_FAILURE = "wallet_add_card_failure"
        const val CLEAN_ASSIST = "vehicle_ev_what_is_clean_assist"
        const val CLEAN_ASSIST_DECLINE = "vehicle_ev_clean_assist_decline"
    }
}
