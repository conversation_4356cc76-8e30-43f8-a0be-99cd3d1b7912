package com.toyota.oneapp.analytics

interface AnalyticsLogger {
    @SafeVarargs
    fun logEvent(
        event: AnalyticsEvent,
        vararg args: Pair<String, Any?>,
    )

    @SafeVarargs
    fun logStringEvent(
        event: String,
        vararg args: Pair<String, Any?>,
    )

    fun logUser(guid: String)

    fun logEventWithParameter(
        event: String,
        paramValue: String,
        paramKey: String = AnalyticsEventParam.EVENT_NAME,
    )
}
