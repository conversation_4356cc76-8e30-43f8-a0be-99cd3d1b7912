package com.toyota.oneapp.analytics

import android.os.Build
import android.os.Bundle
import androidx.core.os.bundleOf
import com.google.firebase.analytics.FirebaseAnalytics
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.network.api.CorrelationIdProvider
import javax.inject.Inject

internal class FirebaseAnalyticsLogger
    @Inject
    constructor(
        private val firebaseAnalytics: FirebaseAnalytics,
        private val correlationIdProvider: CorrelationIdProvider,
        private val applicationData: ApplicationData,
    ) : AnalyticsLogger {
        companion object {
            private const val SESSION_CORRELATION_ID_KEY = "sessionCorrelationId"
            const val SELECTED_VEHICLE_VIN_KEY = "selectedVehicleVin"
            const val SELECTED_VEHICLE_BRAND_KEY = "selectedVehicleBrand"
            const val SELECTED_VEHICLE_REGION_KEY = "selectedVehicleRegion"
            const val DEVICE_MODEL = "device_model"

            const val SCREEN_TYPE_ID_KEY = "screenType"
            const val SCREEN_TYPE_VALUE_MOBILE = "mobile"
            const val SCREEN_TYPE_VALUE_AUTO = "auto"
        }

        override fun logEvent(
            event: AnalyticsEvent,
            vararg args: Pair<String, Any?>,
        ) = logEvent(
            event.eventName,
            *args,
        )

        override fun logStringEvent(
            event: String,
            vararg args: Pair<String, Any?>,
        ) = logEvent(
            event,
            *args,
        )

        override fun logUser(guid: String) {
            firebaseAnalytics.setUserId(guid)
        }

        override fun logEventWithParameter(
            event: String,
            paramValue: String,
            paramKey: String,
        ) {
            firebaseAnalytics.logEvent(event, Bundle().apply { putString(paramKey, paramValue) })
        }

        private fun logEvent(
            eventName: String,
            vararg args: Pair<String, Any?>,
        ) {
            firebaseAnalytics.logEvent(
                eventName,
                bundleOf(
                    SESSION_CORRELATION_ID_KEY to correlationIdProvider.get(),
                    SELECTED_VEHICLE_VIN_KEY to applicationData.getSelectedVehicle()?.vin,
                    SELECTED_VEHICLE_BRAND_KEY to applicationData.getSelectedVehicle()?.brand,
                    SELECTED_VEHICLE_REGION_KEY to applicationData.getSelectedVehicle()?.region,
                    SCREEN_TYPE_ID_KEY to SCREEN_TYPE_VALUE_MOBILE,
                    DEVICE_MODEL to Build.MODEL,
                    *args,
                ),
            )
        }
    }
