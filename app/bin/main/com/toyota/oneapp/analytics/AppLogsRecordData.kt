package com.toyota.oneapp.analytics

import org.json.JSONObject

/**
 * Created by <PERSON> on 2021/5/18.
 */
class AppLogsRecordData(
    val retryAttempt: Int,
    val eventName: String,
    val timeStamp: Long = System.currentTimeMillis(),
    var statusCode: String = "",
    var retries: JSONObject? = null,
    val url: String = "",
    val response: String? = null,
) {
    fun toLogString(): String {
        return "retryAttempt: $retryAttempt, eventName: $eventName, timeStamp: $timeStamp, statusCode: $statusCode, url: $url, response: $response"
    }
}
