package com.toyota.oneapp.analytics

import android.content.Context
import com.google.firebase.analytics.FirebaseAnalytics
import com.toyota.oneapp.core.ApplicationContext
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module
internal abstract class FirebaseAnalyticsModule {
    @Binds
    abstract fun bindLogger(logger: FirebaseAnalyticsLogger): AnalyticsLogger

    companion object {
        @Provides
        fun provideFirebaseAnalytics(
            @ApplicationContext context: Context,
        ): FirebaseAnalytics = FirebaseAnalytics.getInstance(context)
    }
}
