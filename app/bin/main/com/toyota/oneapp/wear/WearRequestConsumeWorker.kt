package com.toyota.oneapp.wear

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import com.google.gson.Gson
import com.toyota.oneapp.BuildConfig
import com.toyota.oneapp.R
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.IDPData
import com.toyota.oneapp.app.ToyotaApplication
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.model.dashboard.OdometerDetailsResponse
import com.toyota.oneapp.model.remote.RemoteBaseResponse
import com.toyota.oneapp.model.remote.VehicleStatusResponse
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.network.api.manager.RemoteServiceAPIManager
import com.toyota.oneapp.network.api.manager.TelemetryAPIManager
import com.toyota.oneapp.network.callback.BaseCallback
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.util.AppLanguageUtils
import com.toyota.oneapp.util.WearUtil
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.wear.ChargeInfoDetails
import toyotaone.commonlib.wear.OdometerDetails
import toyotaone.commonlib.wear.Sections
import toyotaone.commonlib.wear.TelemetryValue
import toyotaone.commonlib.wear.Values
import toyotaone.commonlib.wear.VehicleStatus
import toyotaone.commonlib.wear.VehicleStatusResponsePayload
import toyotaone.commonlib.wear.WearAPIType
import toyotaone.commonlib.wear.WearConstants
import toyotaone.commonlib.wear.WearRequest
import toyotaone.commonlib.wear.WearResponse
import toyotaone.commonlib.wear.WearableFeatureFlag
import javax.inject.Inject

class WearRequestConsumeWorker
    @Inject
    constructor(
        private val remoteManager: RemoteServiceAPIManager,
        private val telemetryManager: TelemetryAPIManager,
        private val applicationData: ApplicationData,
        private val analyticsLogger: AnalyticsLogger,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) {
        private val gson: Gson = Gson()

        val guid: String
            get() = oneAppPreferenceModel.getGuid()

        val generation: String
            get() {
                return applicationData.getSelectedVehicle()?.generation ?: ""
            }

        val brand: String
            get() {
                return applicationData.getSelectedVehicle()?.brand ?: BuildConfig.APP_BRAND
            }

        private val getVin: String
            get() {
                return applicationData.getSelectedVehicle()?.vin ?: ""
            }

        fun startWork(
            stringData: String,
            context: Context,
        ) {
            val reqEvent = gson.fromJson(stringData, WearRequest::class.java)
            if (reqEvent?.header != null) {
                val apiType = reqEvent.header?.apiType
                if (!isLogin(apiType)) {
                    return
                }
                if (!isSelectedVehicle(apiType)) {
                    sendGetAppLanguage(WearAPIType.GET_APP_LANGUAGE)
                    return
                }
                val timeStamp = reqEvent.header!!.timeStamp
                val isTimeout = System.currentTimeMillis() - timeStamp > WearConstants.TIMEOUT * 1000
                LogTool.i(
                    TAG,
                    "-----onStartCommand-----" + reqEvent.header?.apiType + " isTimeout: " + isTimeout,
                )
                if (!isTimeout) {
                    when (apiType) {
                        WearAPIType.REMOTE_START -> sendStartCommand(context, apiType)
                        WearAPIType.REMOTE_STOP -> sendStopCommand(context, apiType)
                        WearAPIType.REMOTE_LOCK -> sendLockCommand(context, apiType)
                        WearAPIType.REMOTE_UNLOCK -> sendUnlockCommand(context, apiType)
                        WearAPIType.REMOTE_HAZARD_LIGHT_ON ->
                            sendHazardLightsOnCommand(
                                context,
                                apiType,
                            )
                        WearAPIType.REMOTE_HAZARD_LIGHT_OFF ->
                            sendHazardLightsOffCommand(
                                context,
                                apiType,
                            )
                        WearAPIType.REMOTE_HORN -> sendSoundHornCommand(context)
                        WearAPIType.REMOTE_LIGHTS -> sendLightsCommand(context, apiType)
                        WearAPIType.REMOTE_TRUNK_LOCK -> sendTrunkLockCommand(context, apiType)
                        WearAPIType.REMOTE_TRUNK_UNLOCK -> sendTrunkUnlockCommand(context, apiType)
                        WearAPIType.REMOTE_BUZZER -> sendBuzzerCommand(context, apiType)
                        WearAPIType.VEHICLE_DETAIL -> getVehicleNickName(apiType)
                        WearAPIType.VEHICLE_IMAGE -> getVehicleImage(apiType)
                        WearAPIType.ODOMETER_DETAIL -> getTelemetryRequest(apiType)
                        WearAPIType.CHECK_REMOTE_ACTIVATED -> sendCheckRemoteActivatedRequest(apiType)
                        WearAPIType.GET_APP_LANGUAGE -> sendGetAppLanguage(apiType)
                        WearAPIType.VEHICLE_STATUSES -> sendVehicleStatusRequest(apiType)
                        WearAPIType.FEATURE_FLAG_ENABLED -> getFeatureFlagEnabled(apiType)
                        WearAPIType.REMOTE_CAPABILITY_CHECK -> getRemoteCapability(apiType)
                        WearAPIType.LAST_PARKED_LOCATION -> getLastParkedLocation(apiType)
                        WearAPIType.LAST_PARKED_LOCATION_DETAILS ->
                            getLastParkedLocationDetails(
                                apiType,
                                reqEvent,
                                context,
                            )
                        WearAPIType.EV_VEHICLE_STATUSES -> sendEvVehicleStatusRequest(apiType)
                        else -> {}
                    }
                }
            }
        }

        private fun sendBuzzerCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }

            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_UNLOCK)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendBuzzerWarningCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
            )
        }

        private fun sendTrunkUnlockCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_UNLOCK)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendTrunkCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                false,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
                false,
            )
        }

        private fun sendTrunkLockCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_UNLOCK)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendTrunkCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                false,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
                true,
            )
        }

        private fun sendLightsCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_UNLOCK)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendLightsOnlyCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
            )
        }

        // will be separated into another handler later in wear revamp
        internal fun sendSoundHornCommand(context: Context) {
            val selectedVehicle = applicationData.getSelectedVehicle()
            if (selectedVehicle == null || WearUtil.isSendingStatus(selectedVehicle)) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.REMOTE_HORN)
            WearUtil.setSendingStatus(true, getVin, selectedVehicle)
            remoteManager.sendSoundHornCommand(
                context,
                selectedVehicle,
                object : WearBaseCallback<RemoteBaseResponse>(WearAPIType.REMOTE_HORN) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        // suppose nothing to be here since this sendToWear call in this legacy code invoke itself
                        // sendToWear(wearResponse)
                    }
                },
            )
        }

        private fun getLastParkedLocationDetails(
            apiType: WearAPIType,
            reqEvent: WearRequest,
            context: Context,
        ) {
            val mLatitude = reqEvent.header?.latitude
            val mLongitude = reqEvent.header?.longitude
            val gmmIntentUri = Uri.parse("google.navigation:q=$mLatitude,$mLongitude&mode=d")
            val mapIntent = Intent(Intent.ACTION_VIEW, gmmIntentUri)
            mapIntent.setPackage("com.google.android.apps.maps")
            mapIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(mapIntent)
        }

        private fun getLastParkedLocation(apiType: WearAPIType) {
            applicationData.getSelectedVehicle()?.let { vehicle ->
                telemetryManager.sendGetTelemetryRequest(
                    vehicle,
                    object : WearBaseCallback<OdometerDetailsResponse?>(apiType) {
                        override fun onSuccess(response: OdometerDetailsResponse?) {
                            response?.payload?.let {
                                val latitude = response.payload.vehicleLocation?.latitude
                                val longitude = response.payload.vehicleLocation?.longitude

                                sendToWear(
                                    WearResponse(
                                        apiType = apiType,
                                        vin = vehicle.vin,
                                        payload = response.payload.fuelLevel.toString(),
                                        latitude = latitude,
                                        longitude = longitude,
                                    ),
                                )
                            }
                        }
                    },
                )
            }
        }

        private fun getRemoteCapability(apiType: WearAPIType) {
            val remoteDisplay = applicationData.getSelectedVehicle()?.remoteDisplay
            if (remoteDisplay == 7) {
                updateRemoteCapability(apiType, true)
            } else {
                updateRemoteCapability(apiType, false)
            }
        }

        private fun updateRemoteCapability(
            apiType: WearAPIType,
            remoteCapable: Boolean,
        ) {
            val wearableFeatureFlag =
                WearableFeatureFlag(
                    remoteCommandFlag = remoteCapable,
                )
            val response =
                WearResponse(
                    apiType = apiType,
                    vin = getVin,
                    wearableFeatureFlag = wearableFeatureFlag,
                )
            sendToWear(response)
        }

        private fun getFeatureFlagEnabled(apiType: WearAPIType) {
            applicationData.getSelectedVehicle()?.let {
                val vehicleStatusFlag = it.isVehicleStatusEnabled
                val telemetryFlag = it.isFeatureEnabled(Feature.TELEMETRY)
                val lastParkedFlag = it.isRemoteActivated && it.isFeatureEnabled(Feature.LAST_PARKED)
                val evLastParkedFlag = it.isFeatureEnabled(Feature.EV_CHARGE_STATION)
                val wearableFeatureFlag =
                    WearableFeatureFlag(
                        vehicleStatusFlag = vehicleStatusFlag,
                        telemetryFlag = telemetryFlag,
                        lastParkedFlag = lastParkedFlag,
                        evLastParkedFlag = evLastParkedFlag,
                        generation = it.generation,
                    )
                val response =
                    WearResponse(
                        apiType = apiType,
                        vin = getVin,
                        wearableFeatureFlag = wearableFeatureFlag,
                    )
                sendToWear(response)
            }
        }

        private fun sendGetAppLanguage(apiType: WearAPIType) {
            val response =
                WearResponse(
                    apiType = apiType,
                    vin = getVin,
                    payload = AppLanguageUtils.getAppLanguage(),
                )
            sendToWear(response)
        }

        private fun getWearResponseJSON(response: WearResponse): String = gson.toJson(response)

        private fun isLogin(wearAPIType: WearAPIType?): Boolean {
            if (IDPData.getInstance(oneAppPreferenceModel).accessToken.isBlank()) {
                val response =
                    WearResponse(
                        apiType = wearAPIType,
                        vin = getVin,
                        errorMessage =
                            ToyotaApplication
                                .getAppContext()
                                .getString(R.string.SmartWatch_Please_Login),
                    )
                sendToWear(response)
                return false
            }
            return true
        }

        private fun isSelectedVehicle(wearAPIType: WearAPIType?): Boolean {
            if (applicationData.getSelectedVehicle() == null) {
                val response =
                    WearResponse(
                        apiType = wearAPIType,
                        vin = getVin,
                        errorMessage =
                            when {
                                AppLanguageUtils.getAppLanguage().equals("FR", true) -> {
                                    ToyotaApplication
                                        .getAppContext()
                                        .getString(R.string.SmartWatch_Please_Add_Vehicle_fr)
                                }
                                AppLanguageUtils.getAppLanguage().equals("ES", true) -> {
                                    ToyotaApplication
                                        .getAppContext()
                                        .getString(R.string.SmartWatch_Please_Add_Vehicle_es)
                                }
                                else -> {
                                    ToyotaApplication
                                        .getAppContext()
                                        .getString(R.string.SmartWatch_Please_Add_Vehicle)
                                }
                            },
                    )
                sendToWear(response)
                return false
            }
            return true
        }

        private fun sendStartCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_START)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendStartCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
            )
        }

        private fun sendStopCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_STOP)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendStopCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
            )
        }

        private fun sendLockCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_LOCK)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendLockCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                false,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
            )
        }

        private fun sendUnlockCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_UNLOCK)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendUnlockCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
            )
        }

        private fun sendHazardLightsOnCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_LIGHTS)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendHazardLightsOnCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
            )
        }

        private fun sendHazardLightsOffCommand(
            context: Context,
            wearAPIType: WearAPIType,
        ) {
            if (WearUtil.isSendingStatus(applicationData.getSelectedVehicle())) {
                return
            }
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_LIGHTS)
            WearUtil.setSendingStatus(true, getVin, applicationData.getSelectedVehicle())
            remoteManager.sendHazardLightsOffCommand(
                context,
                applicationData.getSelectedVehicle()!!,
                object : WearBaseCallback<RemoteBaseResponse>(wearAPIType) {
                    override fun sendToWear(wearResponse: WearResponse) {
                        sendToWear(wearResponse)
                    }
                },
            )
        }

        private fun sendRemoteActivatedResult(
            wearAPIType: WearAPIType,
            result: Int,
        ) {
            val response =
                WearResponse(
                    apiType = wearAPIType,
                    vin = getVin,
                    payload = applicationData.getSelectedVehicle()?.toJsonString(),
                )
            when (result) {
                REMOTE_ACTIVATE_SUCCESS -> {
                }
                REMOTE_HIDE -> {
                    response.apply {
                        errorMessage = " "
                    }
                }
                REMOTE_ACTIVATE_ERROR ->
                    response.apply {
                        when {
                            AppLanguageUtils.getAppLanguage().equals("FR", true) -> {
                                errorMessage = " "
                            }
                            AppLanguageUtils.getAppLanguage().equals("ES", true) -> {
                                errorMessage = " "
                            }
                            AppLanguageUtils.getAppLanguage().equals("EN", true) -> {
                                errorMessage = " "
                            }
                        }
                    }
                else ->
                    response.apply {
                        when {
                            AppLanguageUtils.getAppLanguage().equals("FR", true) -> {
                                errorMessage = " "
                            }
                            AppLanguageUtils.getAppLanguage().equals("ES", true) -> {
                                errorMessage = " "
                            }
                            AppLanguageUtils.getAppLanguage().equals("EN", true) -> {
                                errorMessage = " "
                            }
                        }
                    }
            }
            sendToWear(response)
        }

        private fun sendCheckRemoteActivatedRequest(wearAPIType: WearAPIType) {
            applicationData.getSelectedVehicle()?.let {
                val shouldShowFor17CYAnd17CYPlus =
                    !it.hasEvRemoteCapability() &&
                        it.remoteServicesExceptions?.run { hazardCapable || dlockUnlockCapable || estartStopCapable } == true &&
                        it.remoteDisplay == 7
                it.hasRemoteCapable()
                val shouldShowFor21MM = it.remoteDisplay == 7
                val remoteActivated = if (it.is21MMVehicle) shouldShowFor21MM else shouldShowFor17CYAnd17CYPlus
                if (remoteActivated) {
                    sendRemoteActivatedResult(wearAPIType, REMOTE_ACTIVATE_SUCCESS)
                } else {
                    sendRemoteActivatedResult(wearAPIType, REMOTE_ACTIVATE_ERROR)
                }
            } ?: sendRemoteActivatedResult(wearAPIType, REMOTE_ACTIVATE_ERROR)
        }

        private fun getVehicleNickName(wearAPIType: WearAPIType) {
            val title =
                if (!TextUtils.isEmpty(applicationData.getSelectedVehicle()?.nickName)) {
                    applicationData.getSelectedVehicle()?.nickName
                } else {
                    applicationData.getSelectedVehicle()?.let {
                        String.format(
                            "%s %s",
                            it.modelYear,
                            it.modelDescription,
                        )
                    }
                }
            val response =
                WearResponse(
                    apiType = wearAPIType,
                    vin = getVin,
                    payload = title,
                )
            sendToWear(response)
        }

        private fun sendToWear(response: WearResponse) {
            WearUtil.sendToWear(
                WearConstants.BUS_OUTBOUND_PATH,
                WearConstants.BUS_OUTBOUND_DATA_KEY,
                getWearResponseJSON(response),
            )
            analyticsLogger.logEvent(AnalyticsEvent.WATCH_SYNC_DATA)
        }

        private fun sendEvVehicleStatusRequest(wearAPIType: WearAPIType) {
            applicationData.getSelectedVehicle()?.let { vehicle ->
                if (vehicle.isEvVehicle && vehicle.isRemoteActivated) {
                    telemetryManager.sendEVVehicleStatusRequest(
                        vehicle.vin,
                        vehicle.brand,
                        vehicle.generation,
                        vehicle.region,
                        object : WearBaseCallback<ElectricStatusResponse?>(wearAPIType) {
                            override fun onSuccess(response: ElectricStatusResponse?) {
                                response?.payload?.vehicleInfo?.chargeInfo?.let {
                                    sendToWear(
                                        WearResponse(
                                            apiType = wearAPIType,
                                            vin = vehicle.vin,
                                            chargeInfoDetails = (
                                                ChargeInfoDetails(
                                                    it.evDistance,
                                                    it.evDistanceUnit,
                                                    it.chargeRemainingAmount?.toInt(),
                                                )
                                            ),
                                        ),
                                    )
                                }
                            }
                        },
                    )
                }
            }
        }

        private fun getTelemetryRequest(wearAPIType: WearAPIType) {
            applicationData.getSelectedVehicle()?.let { vehicle ->
                telemetryManager.sendGetTelemetryRequest(
                    vehicle,
                    object : WearBaseCallback<OdometerDetailsResponse?>(wearAPIType) {
                        override fun onSuccess(response: OdometerDetailsResponse?) {
                            response?.payload.let {
                                val flTirePressure =
                                    TelemetryValue(
                                        it?.flTirePressure?.unit,
                                        it?.flTirePressure?.value,
                                    )
                                val frTirePressure =
                                    TelemetryValue(
                                        it?.frTirePressure?.unit,
                                        it?.frTirePressure?.value,
                                    )
                                val odometer =
                                    TelemetryValue(
                                        it?.odometer?.unit,
                                        it?.odometer?.value,
                                    )
                                val rlTirePressure =
                                    TelemetryValue(
                                        it?.rlTirePressure?.unit,
                                        it?.rlTirePressure?.value,
                                    )
                                val rrTirePressure =
                                    TelemetryValue(
                                        it?.rrTirePressure?.unit,
                                        it?.rrTirePressure?.value,
                                    )
                                val spareTirePressure =
                                    TelemetryValue(
                                        it?.spareTirePressure?.unit,
                                        it?.spareTirePressure?.value,
                                    )
                                val nextService =
                                    TelemetryValue(
                                        it?.nextService?.unit,
                                        it?.nextService?.value,
                                    )
                                val speed = TelemetryValue(it?.speed?.unit, it?.speed?.value)
                                val distanceToEmpty =
                                    TelemetryValue(
                                        it?.distanceToEmpty?.unit,
                                        it?.distanceToEmpty?.value,
                                    )

                                val value =
                                    OdometerDetails(
                                        driverWindow = it?.driverWindow,
                                        flTirePressure = flTirePressure,
                                        frTirePressure = frTirePressure,
                                        fuelLevel = it?.fuelLevel,
                                        lastTimestamp = it?.lastTimestamp,
                                        odometer = odometer,
                                        passengerWindow = it?.passengerWindow,
                                        rlTirePressure = rlTirePressure,
                                        rlWindow = it?.rlWindow,
                                        rrTirePressure = rrTirePressure,
                                        rrWindow = it?.rrWindow,
                                        spareTirePressure = spareTirePressure,
                                        nextService = nextService,
                                        speed = speed,
                                        sunRoof = it?.sunRoof,
                                        vehicleName = it?.vehicleName,
                                        vin = it?.vin,
                                        displayNextService = it?.displayNextService,
                                        distanceToEmpty = distanceToEmpty,
                                        displayDistanceToEmpty = it?.displayDistanceToEmpty,
                                    )
                                sendToWear(
                                    WearResponse(
                                        apiType = wearAPIType,
                                        vin = vehicle.vin,
                                        payload = response?.payload?.fuelLevel.toString(),
                                        response = value,
                                    ),
                                )
                            }
                        }
                    },
                )
            }
        }

        private fun getVehicleImage(wearAPIType: WearAPIType) {
            val vehicle = applicationData.getSelectedVehicle()?.image

            val response =
                WearResponse(
                    apiType = wearAPIType,
                    vin = applicationData.getSelectedVehicle()?.vin,
                    payload = vehicle,
                )
            sendToWear(response)
        }

        fun sendVehicleStatusRequest(wearAPIType: WearAPIType) {
            applicationData.getSelectedVehicle()?.let { vehicle ->
                analyticsLogger.logEvent(AnalyticsEvent.WATCH_REMOTE_STATUS)
                remoteManager.sendGetVehicleStatusRequest(
                    vehicle.vin,
                    vehicle.brand,
                    object : WearBaseCallback<VehicleStatusResponse?>(wearAPIType) {
                        override fun onSuccess(response: VehicleStatusResponse?) {
                            val vehicleStatusResponsePayload =
                                fetchVehicleStatusResponse(
                                    response!!,
                                )

                            sendToWear(
                                WearResponse(
                                    apiType = wearAPIType,
                                    vin = getVin,
                                    payload = vehicleStatusResponsePayload?.latitude,
                                    vehicleStatusResponse = vehicleStatusResponsePayload,
                                ),
                            )
                        }
                    },
                )
            }
        }

        private fun fetchVehicleStatusResponse(res: VehicleStatusResponse): VehicleStatusResponsePayload {
            val list = ArrayList<VehicleStatus>()
            res.payload.vehicleStatus?.forEach { sectionValue ->
                val secList = ArrayList<Sections>()
                sectionValue.sections?.forEach { section ->
                    val valList = ArrayList<Values>()
                    section.values?.forEach {
                        val valu = it.value?.let { it1 -> Values(it1, it.status) }
                        if (valu != null) {
                            valList.add(valu)
                        }
                    }
                    val secval = section.section?.let { it1 -> Sections(it1, valList) }
                    if (secval != null) {
                        secList.add(secval)
                    }
                }
                val listt =
                    sectionValue.category?.let { it1 ->
                        VehicleStatus(
                            it1,
                            sectionValue.displayOrder,
                            secList,
                        )
                    }
                if (listt != null) {
                    list.add(listt)
                }
            }
            return VehicleStatusResponsePayload(
                list,
                res.payload.occurrenceDate,
                res.payload.cautionOverallCount,
                res.payload.latitude,
                res.payload.longitude,
                res.payload.address,
            )
        }

        private abstract inner class WearBaseCallback<T>(
            private val wearAPIType: WearAPIType,
        ) : BaseCallback<T>() {
            override fun onFailError(
                httpCode: Int,
                errorMsg: String?,
            ) {
                sendToWear(WearResponse(apiType = wearAPIType, vin = getVin))
            }

            open fun sendToWear(wearResponse: WearResponse) {
                WearUtil.sendToWear(
                    WearConstants.BUS_OUTBOUND_PATH,
                    WearConstants.BUS_OUTBOUND_DATA_KEY,
                    getWearResponseJSON(wearResponse),
                )
            }
        }

        companion object {
            private const val TAG = "WearRequestWorker"
            private const val REMOTE_ACTIVATE_SUCCESS = 0
            private val REMOTE_ACTIVATE_ERROR = 1
            private val REMOTE_HIDE = 2
        }
    }
