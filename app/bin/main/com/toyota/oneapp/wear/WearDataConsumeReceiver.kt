package com.toyota.oneapp.wear

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class WearDataConsumeReceiver : BroadcastReceiver() {
    @Inject lateinit var wearRequestConsumeWorker: WearRequestConsumeWorker

    override fun onReceive(
        context: Context,
        intent: Intent?,
    ) {
        if (intent != null && WEAR_DATA_ACTION == intent.action) {
            val dataString = intent.getStringExtra("Data")
            if (dataString != null) {
                wearRequestConsumeWorker.startWork(dataString, context)
            }
        }
    }

    companion object {
        const val WEAR_DATA_ACTION = "WearDataConsumeReceiver.WEAR_DATA_ACTION"
    }
}
