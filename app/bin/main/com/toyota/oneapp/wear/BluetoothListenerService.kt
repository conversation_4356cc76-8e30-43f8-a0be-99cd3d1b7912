package com.toyota.oneapp.wear

import android.content.Intent
import android.text.TextUtils
import com.google.android.gms.common.data.FreezableUtils
import com.google.android.gms.wearable.DataEvent
import com.google.android.gms.wearable.DataEventBuffer
import com.google.android.gms.wearable.DataMapItem
import com.google.android.gms.wearable.MessageEvent
import com.google.android.gms.wearable.WearableListenerService
import toyotaone.commonlib.log.LogTool
import toyotaone.commonlib.wear.WearConstants

class BluetoothListenerService : WearableListenerService() {
    override fun onDataChanged(dataEvents: DataEventBuffer) {
        super.onDataChanged(dataEvents)
        val events = FreezableUtils.freezeIterable(dataEvents)
        events
            .stream()
            .filter { dataEvent -> dataEvent.type == DataEvent.TYPE_CHANGED }
            .map { dataEvent ->
                DataMapItem
                    .fromDataItem(dataEvent.dataItem)
                    .dataMap
                    .getString(WearConstants.BUS_INBOUND_DATA_KEY)
            }.filter { resultJson -> !TextUtils.isEmpty(resultJson) }
            .forEach { resultJson ->
                LogTool.d(TAG, String.format("onDataChanged resultJson=%%s%s", resultJson))
                val intent = Intent()
                intent.action = WearDataConsumeReceiver.WEAR_DATA_ACTION
                intent.putExtra("Data", resultJson)
                applicationContext.sendBroadcast(intent)
            }
        dataEvents.release()
    }

    override fun onMessageReceived(messageEvent: MessageEvent) {
        super.onMessageReceived(messageEvent)
        val path = messageEvent.path
        if ("notification/open".equals(path, true)) {
        }
    }

    companion object {
        private var TAG = "BluetoothListenerService"
    }
}
