/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CdrSessionResponse
import com.toyota.oneapp.features.core.commonapicalls.domain.repository.CommonApiRepository
import com.toyota.oneapp.features.core.network.Resource
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.dataprovider.NetworkDataProvider
import com.toyota.oneapp.util.ToyotaConstants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import toyotaone.commonlib.log.LogTool
import java.util.UUID
import javax.inject.Inject

class FetchCdrDetailsUseCase
    @Inject
    constructor(
        private val commonApiRepository: CommonApiRepository,
        private val networkDataProvider: NetworkDataProvider,
    ) {
        companion object {
            private val TAG = FetchCdrDetailsUseCase::class.java.simpleName
        }

        operator fun invoke(
            chargingId: String,
            partnerName: String,
            vehicleInfo: VehicleInfo?,
        ): Flow<CdrSessionResponse?> =
            flow {
                if (vehicleInfo == null) {
                    LogTool.e(TAG, "Vehicle info is null, cannot fetch CDR details")
                    emit(null)
                    return@flow
                }

                if (chargingId.isBlank()) {
                    LogTool.e(TAG, "Charging ID is blank, cannot fetch CDR details")
                    emit(null)
                    return@flow
                }

                val requestId = UUID.randomUUID().toString()
                val normalizedPartnerName = normalizePartnerName(partnerName)

                LogTool.d(
                    TAG,
                    "Fetching CDR details - chargingId: $chargingId, partner: $normalizedPartnerName, make: ${vehicleInfo.make}"
                )

                try {
                    when (val result = commonApiRepository.fetchCdrSessionDetails(
                        requestId = requestId,
                        chargingId = chargingId,
                        apiKey = networkDataProvider.apiKey(),
                        make = vehicleInfo.make,
                        partnerName = normalizedPartnerName,
                    )) {
                        is Resource.Success -> {
                            val response = result.data
                            if (response?.messages?.responseCode == 200 && 
                                response.messages.description.uppercase() == "SUCCESS") {
                                
                                LogTool.d(TAG, "CDR details fetched successfully")
                                emit(response)
                            } else {
                                LogTool.w(
                                    TAG, 
                                    "CDR API returned non-success response: ${response?.messages?.responseCode} - ${response?.messages?.description}"
                                )
                                emit(null)
                            }
                        }
                        is Resource.Error -> {
                            LogTool.e(TAG, "Error fetching CDR details: ${result.message}")
                            emit(null)
                        }
                        is Resource.Loading -> {
                            // Do nothing for loading state in this use case
                        }
                    }
                } catch (e: Exception) {
                    LogTool.e(TAG, "Exception while fetching CDR details: ${e.message}", e)
                    emit(null)
                }
            }

        private fun normalizePartnerName(partnerName: String?): String {
            if (partnerName.isNullOrBlank()) return ToyotaConstants.EMPTY_STRING

            val lowercased = partnerName.lowercase().trim()
            return when (lowercased) {
                "chargepoint" -> "chargepoint"
                "evgo" -> "evgo"
                "electrify america" -> "electrifyamerica"
                "electrifyamerica" -> "electrifyamerica"
                "flo" -> "flo"
                else -> lowercased
            }
        }
    }
