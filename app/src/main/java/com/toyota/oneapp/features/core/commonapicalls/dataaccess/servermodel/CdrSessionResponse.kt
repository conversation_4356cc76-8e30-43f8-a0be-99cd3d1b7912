package com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel

import com.google.gson.annotations.SerializedName

data class CdrSessionResponse(
    val messages: CdrMessages,
    val payload: CdrPayload?,
)

data class CdrMessages(
    @SerializedName("response_code")
    val responseCode: Int,
    val description: String,
    @SerializedName("detailed_description")
    val detailedDescription: String,
)

data class CdrPayload(
    @SerializedName("cdr_details")
    val cdrDetails: CdrDetails?,
)

data class CdrDetails(
    @SerializedName("_id")
    val id: String?,
    val status: String?,
    @SerializedName("payment_ref_id")
    val paymentRefId: String?,
    @SerializedName("payment_info")
    val paymentInfo: String?,
    val data: CdrData?,
    @SerializedName("charging_id")
    val chargingId: String,
    @SerializedName("status_code")
    val statusCode: Int?,
    val timestamp: String?,
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("updated_at")
    val updatedAt: String?,
)

data class CdrData(
    @SerializedName("stop_date_time")
    val stopDateTime: String?,
    @SerializedName("total_energy")
    val totalEnergy: Double?,
    @SerializedName("last_updated")
    val lastUpdated: String?,
    @SerializedName("charging_periods")
    val chargingPeriods: List<ChargingPeriod>?,
    @SerializedName("total_cost")
    val totalCost: Double?,
    @SerializedName("average_power")
    val averagePower: Double?,
    @SerializedName("max_power")
    val maxPower: Double?,
    @SerializedName("auth_method")
    val authMethod: String?,
    @SerializedName("total_parking_time")
    val totalParkingTime: Double?,
    @SerializedName("funding_source")
    val fundingSource: FundingSource?,
    val location: CdrLocation?,
    val currency: String?,
    val id: String?,
    @SerializedName("start_date_time")
    val startDateTime: String?,
    @SerializedName("total_time")
    val totalTime: Double?,
    @SerializedName("startOn")
    val startOn: String?,
    @SerializedName("stopOn")
    val stopOn: String?,
    @SerializedName("lastMeterReadOn")
    val lastMeterReadOn: String?,
    // Calculated field for duration
    var duration: String? = null,
) {
    val formattedTotalCost: String
        get() = if (totalCost == null || totalCost < 0.50) {
            "FREE"
        } else {
            "$${String.format("%.2f", totalCost)}"
        }
}

data class ChargingPeriod(
    @SerializedName("start_date_time")
    val startDateTime: String?,
    val dimensions: List<Dimension>?,
)

data class Dimension(
    val type: String?,
    val volume: Double?,
)

data class FundingSource(
    @SerializedName("source_type")
    val sourceType: String?,
)

data class CdrLocation(
    val id: String?,
    val type: String?,
    val name: String?,
    val address: String?,
    val city: String?,
    @SerializedName("postal_code")
    val postalCode: String?,
    val province: String?,
    val country: String?,
    val coordinates: CdrCoordinates?,
    val evses: List<CdrEvse>?,
    val operator: CdrOperator?,
    @SerializedName("opening_times")
    val openingTimes: CdrOpeningTimes?,
    @SerializedName("time_zone")
    val timeZone: String?,
    @SerializedName("last_updated")
    val lastUpdated: String?,
)

data class CdrCoordinates(
    val latitude: String?,
    val longitude: String?,
)

data class CdrEvse(
    val uid: String?,
    @SerializedName("evse_id")
    val evseId: String?,
    val status: String?,
    val capabilities: List<String>?,
    val connectors: List<CdrConnector>?,
    val coordinates: CdrCoordinates?,
    @SerializedName("physical_reference")
    val physicalReference: String?,
    @SerializedName("last_updated")
    val lastUpdated: String?,
)

data class CdrConnector(
    val id: String?,
    val standard: String?,
    val format: String?,
    @SerializedName("power_type")
    val powerType: String?,
    val voltage: Int?,
    val amperage: Int?,
    @SerializedName("tariff_id")
    val tariffId: String?,
    @SerializedName("last_updated")
    val lastUpdated: String?,
) {
    val friendlyName: String
        get() = when (standard) {
            "J1772" -> "Level 2"
            "CCS1" -> "DCFast"
            else -> standard ?: "Unknown"
        }
}

data class CdrOperator(
    val name: String?,
    val website: String?,
    val logo: CdrLogo?,
)

data class CdrLogo(
    val url: String?,
    val thumbnail: String?,
    val category: String?,
    val type: String?,
    val width: Int?,
    val height: Int?,
)

data class CdrOpeningTimes(
    val twentyfourseven: Boolean?,
)
