package com.toyota.oneapp.features.core.commonapicalls.dataaccess.service

import com.toyota.oneapp.features.accountnotification.dataaccess.remotemodel.AccountNotificationPhotoResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CdrSessionResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ChargeTimerRequest
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CommonScheduleResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.EVChargeSessionResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ElectricStatusResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoResponse
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.RealTimeStatusResponse
import com.toyota.oneapp.features.vehicleinfo.dataaccess.servermodel.SoftwareUpdateResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

// TODO: We have made changes to Dashboard once it's done then we might remove all and use it from the dashboard
interface CommonApi {
    @GET("/oneapi/v3/electric/status")
    suspend fun fetchChargeManagementDetail(
        @Header("X-BRAND") brand: String?,
        @Header("X-GENERATION") generation: String?,
        @Header("VIN") vin: String?,
    ): Response<ElectricStatusResponse?>

    @GET("/oneapi/v4/account")
    suspend fun fetchProfileDetails(
        @Header("X-BRAND") brand: String?,
        @Header("GUID") guid: String?,
    ): Response<ProfileInfoResponse?>

    @GET("/oneapi/charging/charger/session/{chargingid}")
    suspend fun fetchEvChargingSessionDetails(
        @Header("x-correlation-id") requestId: String,
        @Path("chargingid") chargingId: String,
        @Header("x-api-key") apiKey: String,
        @Query("partner") partnerName: String,
    ): Response<EVChargeSessionResponse?>

    @POST("/oneapi/v2/electric/realtime-status")
    suspend fun postElectricVehicleRealTimeStatus(
        @Header("x-correlation-id") requestId: String,
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Header("X-GENERATION") generation: String,
        @Header("device-id") deviceId: String,
    ): Response<RealTimeStatusResponse?>

    @GET("/oneapi/charging/charger/cdr/{chargingid}")
    suspend fun fetchCdrSessionDetails(
        @Header("x-correlation-id") requestId: String,
        @Path("chargingid") chargingId: String,
        @Header("x-api-key") apiKey: String,
        @Header("x-make") make: String,
        @Query("partner") partnerName: String,
    ): Response<CdrSessionResponse?>

    @POST("/oneapi/v2/electric/realtime-status")
    suspend fun postElectricVehicleRealTimeStatusForChargeInfo(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Header("X-GENERATION") generation: String,
        @Header("device-id") deviceId: String,
    ): Response<ElectricStatusResponse?>

    @GET("/oneapi/v3/electric/status")
    suspend fun getElectricVehicleRealTimeStatus(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Header("X-GENERATION") generation: String,
        @Query("realtime-status") appRequestNo: String,
    ): Response<ElectricStatusResponse?>

    @GET("/oneapi/v3/electric/status")
    suspend fun getElectricVehicleRemoteControlStatus(
        @Header("X-BRAND") brand: String,
        @Header("VIN") vin: String,
        @Header("X-GENERATION") generation: String,
        @Query("remote-control") appRequestNo: String,
    ): Response<ElectricStatusResponse?>

    @POST("/oneapi/v2/electric/command")
    suspend fun postElectricVehicleCommand(
        @Body requestBody: ChargeTimerRequest,
        @Header("X-BRAND") brand: String,
        @Header("vin") vin: String,
        @Header("X-GENERATION") generation: String,
        @Header("device-id") deviceId: String,
    ): Response<CommonScheduleResponse?>

    @GET("/oa21mm/v2/profile/picture/{guid}")
    suspend fun getProfilePicture(
        @Path("guid") guid: String?,
    ): Response<AccountNotificationPhotoResponse?>

    @GET("/oa21mm/v1/ota/update/check")
    suspend fun fetchSoftwareUpdates(
        @Header("vin") vin: String,
    ): Response<SoftwareUpdateResponse?>
}
