/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.repository.PlugAndChargeEnrollmentRepository
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.usecase.PostPlugAndChargeEnrollmentUseCase
import javax.inject.Inject

class PostPlugAndChargeEnrollmentLogic
    @Inject
    constructor(
        private val repository: PlugAndChargeEnrollmentRepository,
    ) : PostPlugAndChargeEnrollmentUseCase {
        override suspend fun invoke(): Result<Unit> = repository.initiateEnrollment()
    }
