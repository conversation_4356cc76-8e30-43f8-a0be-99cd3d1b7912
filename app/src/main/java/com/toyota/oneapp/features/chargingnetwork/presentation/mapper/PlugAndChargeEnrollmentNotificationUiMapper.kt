/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.mapper

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState

fun interface PlugAndChargeEnrollmentNotificationUiMapper {
    fun map(
        stateNotified: PlugAndChargeEnrollmentState,
        currentChargingNetworksState: ChargingNetworksState,
    ): ChargingNetworksState
}
