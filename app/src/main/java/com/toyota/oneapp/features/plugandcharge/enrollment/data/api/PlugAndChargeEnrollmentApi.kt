/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.data.api
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeEnrollmentStatusDto
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeStartEnrollmentRequestDto
import com.toyota.oneapp.network.models.ApiResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

interface PlugAndChargeEnrollmentApi {
    @POST("${BASE}enroll")
    suspend fun initiateEnrollment(
        @Header("x-vin") vin: String,
        @Header("x-region") region: String,
        @Header("x-brand") brand: String,
        @Body body: PlugAndChargeStartEnrollmentRequestDto,
    ): Response<ApiResponse<Unit>?>

    @GET("${BASE}status")
    suspend fun getPlugAndChargeEnrollmentStatus(
        @Header("x-vin") vin: String,
    ): Response<ApiResponse<PlugAndChargeEnrollmentStatusDto>?>

    companion object {
        private const val BASE = "charging/v2/pnc/"
    }
}
