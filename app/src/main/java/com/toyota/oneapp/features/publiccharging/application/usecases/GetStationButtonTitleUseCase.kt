/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/
package com.toyota.oneapp.features.publiccharging.application.usecases

import com.toyota.oneapp.features.entrollment.application.EnrollmentState
import com.toyota.oneapp.features.entrollment.application.WalletState
import com.toyota.oneapp.features.entrollment.domain.model.EnrollmentData
import com.toyota.oneapp.features.entrollment.domain.model.WalletData
import com.toyota.oneapp.features.entrollment.util.Constance
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

class GetStationButtonTitleUseCase
    @Inject
    constructor() {
        companion object {
            private const val UNLOCK_CAPABLE = "UNLOCK_CAPABLE"
        }

        operator fun invoke(
            station: ChargeStationInfo?,
            enrollmentState: StateFlow<EnrollmentState>,
            walletState: StateFlow<WalletState>,
        ): String {
            if (!isStateSuccessful(walletState, enrollmentState)) {
                return Constance.SETUP_WALLET
            }

            val enrollmentStateData = (enrollmentState.value as EnrollmentState.Success).data
            val walletStateData = (walletState.value as WalletState.Success).data

            return when {
                station?.evIsPartner == true ->
                    getPartnerStationTitle(
                        station,
                        enrollmentStateData,
                        walletStateData,
                    )

                station?.evEvSource == Constance.CHARGE_POINT -> getChargePointTitle(enrollmentStateData)
                else -> Constance.SETUP_WALLET
            }
        }

        private fun isStateSuccessful(
            walletState: StateFlow<WalletState>,
            enrollmentState: StateFlow<EnrollmentState>,
        ): Boolean = walletState.value is WalletState.Success && enrollmentState.value is EnrollmentState.Success

        private fun getPartnerStationTitle(
            station: ChargeStationInfo,
            enrollmentData: EnrollmentData,
            walletData: WalletData?,
        ): String {
            val hasWalletSetup =
                enrollmentData.isWalletSetupDone ||
                    walletData?.paymentMethod !=
                    null
            val hasEnrollmentSetup = enrollmentData.isEnrollmentSetupDone
            val isUnlockCapable =
                station.evEvses.any { evse -> evse.capabilities?.contains(UNLOCK_CAPABLE) == true }

            return when {
                !hasWalletSetup || !hasEnrollmentSetup -> Constance.SETUP_WALLET
                enrollmentData.chargePointStatus || enrollmentData.evGoPointStatus -> {
                    when {
                        isUnlockCapable -> Constance.UNLOCK_STATION
                        else -> Constance.START_CHARGING
                    }
                }
                else -> Constance.REGISTER
            }
        }

        private fun getChargePointTitle(enrollmentData: EnrollmentData): String =
            when {
                enrollmentData.chargePointStatus -> Constance.UNLOCK_STATION
                enrollmentData.evGoPointStatus -> Constance.START_CHARGING
                else -> Constance.REGISTER
            }
    }
