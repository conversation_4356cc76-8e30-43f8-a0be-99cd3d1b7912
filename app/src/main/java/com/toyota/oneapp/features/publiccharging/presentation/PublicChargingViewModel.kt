/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.publiccharging.presentation

import androidx.annotation.VisibleForTesting
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.google.android.libraries.places.api.net.PlacesClient
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworkType
import com.toyota.oneapp.features.chargingnetwork.domain.usecase.GetEnabledChargingNetworksTypesUseCase
import com.toyota.oneapp.features.core.commonapicalls.application.ElectricStatusState
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.CdrSessionResponse
import com.toyota.oneapp.features.core.location.domain.GeoLocationUseCase
import com.toyota.oneapp.features.dashboard.dashboard.domain.SharedDataSource
import com.toyota.oneapp.features.entrollment.application.EnrollmentState
import com.toyota.oneapp.features.entrollment.application.WalletState
import com.toyota.oneapp.features.publiccharging.application.ChargeStationFavoriteState
import com.toyota.oneapp.features.publiccharging.application.ChargeStationFavoriteUIState
import com.toyota.oneapp.features.publiccharging.application.PublicChargingState
import com.toyota.oneapp.features.publiccharging.application.SendToCarState
import com.toyota.oneapp.features.publiccharging.application.usecases.ApplyFiltersUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.FetchCdrDetailsUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.FetchDefaultPaymentMethodUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.FetchElectricStatusUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.GetChargeSessionUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.GetPlaceDetailsUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.GetPlacePredictionsUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.GetStationButtonTitleUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.GetStationsForLocationUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.GetStationsWithFiltersUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.GetVehicleLocationUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.ManageFiltersUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.PublicChargingUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.SetInitialLoadingStateUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.SetLastPositionUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.StartChargingUseCase
import com.toyota.oneapp.features.publiccharging.application.usecases.StopChargingUseCase
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.domain.model.StationListInfo
import com.toyota.oneapp.features.publiccharging.presentation.state.ChargeSessionUiState
import com.toyota.oneapp.features.publiccharging.presentation.state.ElectricStatusUiState
import com.toyota.oneapp.features.publiccharging.presentation.state.StartChargingUiState
import com.toyota.oneapp.features.publiccharging.presentation.state.StopChargingUiState
import com.toyota.oneapp.features.publiccharging.util.toLatLng
import com.toyota.oneapp.model.poi.Address
import com.toyota.oneapp.model.poi.Coordinates
import com.toyota.oneapp.model.poi.LocationDetails
import com.toyota.oneapp.model.poi.SendPOIToCarRequest
import com.toyota.oneapp.model.poi.SendToCarLocation
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.model.vehicle.VehicleInfo.FUELTYPE_HYDROGENFUELCELL
import com.toyota.oneapp.model.vehicle.VehicleInfo.REMOTE_STATUS_ACTIVATED
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import com.toyota.oneapp.ui.destinations.PlaceInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import toyotaone.commonlib.log.LogTool
import javax.inject.Inject

@HiltViewModel
class PublicChargingViewModel
    @Inject
    constructor(
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        private val publicChargingUseCase: PublicChargingUseCase,
        private val getStationsForLocationUseCase: GetStationsForLocationUseCase,
        private val getPlacePredictionsUseCase: GetPlacePredictionsUseCase,
        private val getPlaceDetailsUseCase: GetPlaceDetailsUseCase,
        private val getStationsWithFiltersUseCase: GetStationsWithFiltersUseCase,
        private val getStationButtonTitleUseCase: GetStationButtonTitleUseCase,
        private val startChargingUseCase: StartChargingUseCase,
        private val stopChargingUseCase: StopChargingUseCase,
        private val getChargeSessionUseCase: GetChargeSessionUseCase,
        private val fetchElectricStatusUseCase: FetchElectricStatusUseCase,
        private val fetchDefaultPaymentMethodUseCase: FetchDefaultPaymentMethodUseCase,
        private val applyFiltersUseCase: ApplyFiltersUseCase,
        private val setInitialLoadingStateUseCase: SetInitialLoadingStateUseCase,
        private val setLastPositionUseCase: SetLastPositionUseCase,
        private val manageFiltersUseCase: ManageFiltersUseCase,
        private val fetchCdrDetailsUseCase: FetchCdrDetailsUseCase,
        private val geoLocationUseCase: GeoLocationUseCase,
        private val getVehicleLocationUseCase: GetVehicleLocationUseCase,
        private val getEnabledChargingNetworksTypesUseCase: GetEnabledChargingNetworksTypesUseCase,
        applicationData: ApplicationData,
        private val sharedDataSource: SharedDataSource,
    ) : BaseViewModel() {
        companion object {
            private const val TAG = "PublicChargingViewModel"
            private const val ERROR = "Unknown Error"
        }

        private var _lastPosition: MutableStateFlow<LatLng?> = MutableStateFlow(null)
        val lastPosition = _lastPosition.asStateFlow()
        private val _stationState =
            MutableStateFlow<PublicChargingState>(value = PublicChargingState.Init)
        val stationState = _stationState.asStateFlow()

        private val _favoriteStations = MutableStateFlow<Set<String>>(emptySet())
        val favoriteStations: StateFlow<Set<String>> = _favoriteStations

        var vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()

        private val _chargeSessionUiState = MutableStateFlow<ChargeSessionUiState>(ChargeSessionUiState.Empty)
        val chargeSessionUiState: StateFlow<ChargeSessionUiState> = _chargeSessionUiState.asStateFlow()

        private val _electricStatusUiState = MutableStateFlow<ElectricStatusUiState>(ElectricStatusUiState.Empty)
        val electricStatusUiState: StateFlow<ElectricStatusUiState> = _electricStatusUiState.asStateFlow()

        // Track place predictions for search suggestions
        private val _searchPredictions = MutableStateFlow<List<PlaceInfo>>(emptyList())
        val searchPredictions = _searchPredictions.asStateFlow()

        private val _sendToCarState = MutableStateFlow<SendToCarState>(SendToCarState.Init)
        val sendToCarState: StateFlow<SendToCarState> = _sendToCarState.asStateFlow()

        private val _startChargingState = MutableStateFlow<StartChargingUiState>(StartChargingUiState.Idle)
        val startChargingState: StateFlow<StartChargingUiState> = _startChargingState.asStateFlow()

        private val _stopChargingUiState = MutableStateFlow<StopChargingUiState>(StopChargingUiState.Empty)
        val stopChargingUiState: StateFlow<StopChargingUiState> = _stopChargingUiState.asStateFlow()

        // Keep private for encapsulation
        private val _favoriteStationsState = MutableStateFlow(ChargeStationFavoriteUIState())
        val favoriteStationsState: StateFlow<ChargeStationFavoriteUIState> =
            _favoriteStationsState.asStateFlow()

        // Internal function for testing state manipulation
        internal fun setFavoriteUiStateForTest(state: ChargeStationFavoriteUIState) {
            _favoriteStationsState.value = state
        }

        @VisibleForTesting
        internal var sessionPollingJob: Job? = null

        @VisibleForTesting
        internal var electricStatusPollingJob: Job? = null

        // Add state flow for wallet payment method
        private val _defaultPaymentMethod = MutableStateFlow<String?>(null)
        val defaultPaymentMethod = _defaultPaymentMethod.asStateFlow()

        private val _vehicleLocation = MutableStateFlow<LatLng?>(null)
        val vehicleLocation = _vehicleLocation.asStateFlow()

        var selectedFilter by mutableStateOf<FilterMenuData?>(null)

        var selectedPartnerFilters = mutableStateListOf<Int>()
        var selectedPlugFilters = mutableStateListOf<Int>()

        fun isHydrogenFuelVehicle() = vehicleInfo?.fuelType == FUELTYPE_HYDROGENFUELCELL

        // Add state flow for charging ID
        private val _currentChargingId = MutableStateFlow<String?>(null)
        val currentChargingId: StateFlow<String?> = _currentChargingId.asStateFlow()

        // Add state flow for CDR details
        private val _cdrSessionResponse = MutableStateFlow<CdrSessionResponse?>(null)
        val cdrSessionResponse: StateFlow<CdrSessionResponse?> = _cdrSessionResponse.asStateFlow()

        // Track current partner name for CDR fetching
        private var currentPartnerName: String? = null

        // Add methods to update your filters if needed
        fun updateSelectedFilter(filter: FilterMenuData?) {
            selectedFilter = filter
        }

        fun updateSelectedPartnerFilters(filter: SnapshotStateList<Int>) {
            selectedPartnerFilters = filter
        }

        fun updateSelectedPlugFilters(filter: SnapshotStateList<Int>) {
            selectedPlugFilters = filter
        }

        // Explicitly set the last position to ensure it's available for filtering
        fun setLastPosition(position: LatLng?) {
            viewModelScope.launch {
                setLastPositionUseCase(position).collect { _ -> }
            }
            _lastPosition.value = position
        }

        fun getLastPosition(): LatLng? = _lastPosition.value

        /**
         * Resets all charging-related states to their initial values
         */
        fun resetChargingState() {
            _currentChargingId.value = null
            _cdrSessionResponse.value = null
            currentPartnerName = null
        }

        fun initializeForTest(paymentMethodId: String?) {
            _defaultPaymentMethod.value = paymentMethodId
        }

        fun initializeStartChargingStateForTest(startChargingUiState: StartChargingUiState) {
            _startChargingState.value = startChargingUiState
        }

        fun resetSendToCarState() {
            _sendToCarState.value = SendToCarState.Init
        }

        fun stopChargeSessionPolling() {
            sessionPollingJob?.cancel()
            sessionPollingJob = null
        }

        fun stopElectricStatusPolling() {
            electricStatusPollingJob?.cancel()
            electricStatusPollingJob = null
        }

        fun fetchStations(positionInfo: LatLng?) {
            setLastPosition(positionInfo)
            _lastPosition.value = positionInfo
            val currentFilters = manageFiltersUseCase.getCurrentFilters()
            applyFilters(currentFilters.partnerFilters, currentFilters.plugFilters)
        }

        fun applyFilters(
            partnerTypeFilters: List<Int>,
            plugTypeFilters: List<Int>,
        ) {
            viewModelScope.launch {
                // Update filter state
                manageFiltersUseCase(
                    partnerTypeFilters = partnerTypeFilters,
                    plugTypeFilters = plugTypeFilters,
                ).collect { filterState ->
                    // Apply filters with updated state
                    applyFiltersUseCase(
                        positionInfo = setLastPositionUseCase.getLastPosition(),
                        partnerTypeFilters = filterState.partnerFilters,
                        plugTypeFilters = filterState.plugFilters,
                        vehicleInfo = vehicleInfo,
                    ).collect { state ->
                        _stationState.value = processFilteredStations(state)
                    }
                }
            }
        }

        private fun processFilteredStations(state: PublicChargingState): PublicChargingState {
            if (state !is PublicChargingState.Success) return state

            if (_favoriteStationsState.value.favoriteStations.isEmpty()) {
                fetchFavoriteStations()
            }

            val favoriteIds = favoriteStationsState.value.favoriteStations
            val filteredStations =
                if (favoriteStationsState.value.isFilteringFavorite) {
                    state.data.chargeStationsInfoList.filter { it.evId in favoriteIds }
                } else {
                    state.data.chargeStationsInfoList
                }

            return when {
                favoriteStationsState.value.isFilteringFavorite && filteredStations.isEmpty() -> {
                    PublicChargingState.NoFavoriteStation
                }

                filteredStations.isEmpty() -> PublicChargingState.EmptyStations
                else -> PublicChargingState.Success(StationListInfo(filteredStations, filteredStations.size), state.selectedLatLng)
            }
        }

        private fun fetchFavoriteStations(preserveExisting: Boolean = false) {
            viewModelScope.launch {
                _favoriteStationsState.value =
                    _favoriteStationsState.value.copy(
                        favoriteFetchState = ChargeStationFavoriteState.Loading,
                    )
                publicChargingUseCase.getFavoriteStation().collect { state ->
                    when (state) {
                        is ChargeStationFavoriteState.Success -> {
                            val existingFavorites =
                                if (preserveExisting) {
                                    _favoriteStationsState.value.favoriteLocationDetails
                                } else {
                                    emptyList()
                                }
                            val mergedFavorites =
                                (existingFavorites + state.data.filterNotNull())
                                    .distinctBy { it.placeId }

                            _favoriteStationsState.value =
                                _favoriteStationsState.value.copy(
                                    favoriteStations = state.data.mapNotNull { it?.placeId }.toSet(),
                                    favoriteLocationDetails = mergedFavorites,
                                    favoriteFetchState = ChargeStationFavoriteState.Success(state.data),
                                )
                        }

                        is ChargeStationFavoriteState.Error -> {
                            _favoriteStationsState.value =
                                _favoriteStationsState.value.copy(
                                    favoriteFetchState = ChargeStationFavoriteState.Error(state.message),
                                )
                        }

                        else -> {
                            // do nothing
                        }
                    }
                }
            }
        }

        fun fetchSearchStations(searchText: String) {
            if (searchText.isBlank()) {
                fetchStations(_lastPosition.value)
                return
            }
            _stationState.value = PublicChargingState.Loading
            viewModelScope.launch {
                geoLocationUseCase.getByLocationName(searchText).collect {
                    it
                        .onSuccess { addresses ->
                            if (addresses.isEmpty()) {
                                _stationState.value = PublicChargingState.EmptyStations
                            } else {
                                fetchStationsForSearchedLocation(addresses.first().toLatLng())
                            }
                        }.onFailure {
                            _stationState.value = PublicChargingState.EmptyStations
                        }
                }
            }
        }

        fun fetchStationsForSearchedLocation(latLng: LatLng) {
            _stationState.value = PublicChargingState.Loading
            viewModelScope.launch {
                val currentFilters = manageFiltersUseCase.getCurrentFilters()
                getStationsForLocationUseCase(
                    position = latLng,
                    partnerFilters = currentFilters.partnerFilters,
                    plugFilters = currentFilters.plugFilters,
                    vehicleInfo = vehicleInfo,
                ).collect { state ->
                    _stationState.value = state
                    if (state is PublicChargingState.Success) {
                        setLastPosition(state.selectedLatLng)
                    }
                }
            }
        }

        fun sendPoiToCar(chargeStationInfo: ChargeStationInfo) {
            val locCoordinates = getCoordinates(chargeStationInfo)
            val formattedAddress = buildFormattedAddress(chargeStationInfo)

            val locationDetails =
                LocationDetails(
                    placeId = chargeStationInfo.evPlaceId ?: "",
                    formattedAddress = formattedAddress,
                    name = chargeStationInfo.stationName ?: "",
                    address =
                        Address(
                            street = chargeStationInfo.evAddress ?: "",
                            adminRegionShort = chargeStationInfo.evProvince ?: "",
                            city = chargeStationInfo.evCity ?: "",
                            postalCode = chargeStationInfo.evPostalCode ?: "",
                        ),
                    locCoordinate = locCoordinates,
                    phoneNumber = chargeStationInfo.evPhoneNumber ?: "",
                    location_type = 0,
                )

            val sendPOIToCarRequest =
                SendPOIToCarRequest(
                    guid = oneAppPreferenceModel.getGuid(),
                    poiName = locationDetails.formattedAddress.orEmpty(),
                    location =
                        SendToCarLocation(
                            latitude = locCoordinates.latitude,
                            longitude = locCoordinates.longitude,
                            source = "google_maps",
                            name = chargeStationInfo.evAddress ?: "",
                            locCoordinate = locCoordinates,
                            formattedAddress = chargeStationInfo.evAddress ?: "",
                            address = locationDetails.address ?: Address(),
                            locationType = locationDetails.location_type ?: 0,
                            routingCoordinates = locCoordinates,
                            placeId = locationDetails.placeId ?: "",
                            phoneNumber = locationDetails.phoneNumber ?: "",
                        ),
                )
            viewModelScope.launch {
                publicChargingUseCase.sendToCar(sendPOIToCarRequest).collect { state ->
                    _sendToCarState.value = state
                }
            }
        }

        private fun getCoordinates(chargeStationInfo: ChargeStationInfo): Coordinates =
            Coordinates(
                chargeStationInfo.markerInfo?.stationPosition?.latitude ?: 0.0,
                chargeStationInfo.markerInfo?.stationPosition?.longitude ?: 0.0,
            )

        private fun buildFormattedAddress(chargeStationInfo: ChargeStationInfo): String =
            buildString {
                if (!chargeStationInfo.evAddress.isNullOrBlank()) append(", ${chargeStationInfo.evAddress}")
                if (!chargeStationInfo.evProvince.isNullOrBlank()) append(" ${chargeStationInfo.evProvince}")
                if (!chargeStationInfo.evPostalCode.isNullOrBlank()) append(" ${chargeStationInfo.evPostalCode}")
            }

        fun fetchPlacePredictions(
            query: String,
            placesClient: PlacesClient,
        ) {
            viewModelScope.launch {
                getPlacePredictionsUseCase(query, placesClient)
                    .collect { predictions ->
                        _searchPredictions.value = predictions
                    }
            }
        }

        fun fetchPlaceDetails(
            placeId: String,
            placesClient: PlacesClient,
        ) {
            viewModelScope.launch {
                val currentFilters = manageFiltersUseCase.getCurrentFilters()
                getPlaceDetailsUseCase(
                    placeId = placeId,
                    placesClient = placesClient,
                    partnerFilters = currentFilters.partnerFilters,
                    plugFilters = currentFilters.plugFilters,
                    vehicleInfo = vehicleInfo,
                ).collect { state ->
                    _stationState.value = state
                    if (state is PublicChargingState.Success) {
                        setLastPosition(state.selectedLatLng)
                    }
                }
            }
        }

        fun fetchNearByStations(
            positionInfo: LatLng?,
            partnerFilters: List<Int>,
            plugFilters: List<Int>,
        ) {
            viewModelScope.launch {
                getStationsWithFiltersUseCase(
                    positionInfo = positionInfo,
                    partnerFilters = partnerFilters,
                    plugFilters = plugFilters,
                    vehicleInfo = vehicleInfo,
                ).collect { state ->
                    _stationState.value = state
                }
            }
        }

        fun toggleFavorite(station: ChargeStationInfo) {
            val currentState = _favoriteStationsState.value
            val currentFavorites = currentState.favoriteLocationDetails.toMutableList()
            val isAdding = station.evId != null && currentFavorites.none { it.placeId == station.evId }

            station.evId?.let { evId ->
                val newFavorite =
                    LocationDetails(
                        placeId = evId,
                        formattedAddress = station.evAddress ?: "",
                        name = station.stationName,
                        address =
                            Address(
                                street = station.addressLine1 ?: "",
                                city = station.evCity ?: "",
                                postalCode = station.evPostalCode ?: "",
                                adminRegion = station.evProvince ?: "",
                            ),
                        locCoordinate =
                            station.markerInfo?.stationPosition?.let {
                                Coordinates(it.latitude, it.longitude)
                            } ?: Coordinates(),
                    )

                val updatedFavorites =
                    if (isAdding) {
                        (currentFavorites + newFavorite).distinctBy { it.placeId }
                    } else {
                        currentFavorites.filterNot { it.placeId == evId }
                    }

                viewModelScope.launch {
                    publicChargingUseCase.updateFavoriteLocation(updatedFavorites).collect { state ->
                        when (state) {
                            is ChargeStationFavoriteState.Loading -> {
                                _favoriteStationsState.value =
                                    currentState.copy(
                                        favoriteFetchState = ChargeStationFavoriteState.Loading,
                                    )
                            }

                            is ChargeStationFavoriteState.Success -> {
                                val mergedFavorites =
                                    (currentFavorites + state.data.filterNotNull()).distinctBy { it.placeId }

                                _favoriteStationsState.value =
                                    currentState.copy(
                                        favoriteStations =
                                            state.data
                                                .mapNotNull { it?.placeId }
                                                .toSet(),
                                        favoriteLocationDetails = mergedFavorites,
                                        favoriteFetchState = ChargeStationFavoriteState.Success(state.data),
                                    )
                                fetchFavoriteStations(preserveExisting = isAdding)
                            }

                            is ChargeStationFavoriteState.Error -> {
                                _favoriteStationsState.value =
                                    currentState.copy(
                                        favoriteFetchState = ChargeStationFavoriteState.Error(state.message),
                                    )
                            }
                        }
                    }
                }
            }
        }

        fun toggleFavoriteFilter() {
            viewModelScope.launch {
                if (!_favoriteStationsState.value.isFilteringFavorite) {
                    fetchFavoriteStations()
                }
                _favoriteStationsState.value =
                    _favoriteStationsState.value.copy(
                        isFilteringFavorite = !_favoriteStationsState.value.isFilteringFavorite,
                    )
                fetchStations(_lastPosition.value)
            }
        }

        fun clearFavoriteFilter() {
            _favoriteStationsState.value =
                _favoriteStationsState.value.copy(
                    isFilteringFavorite = false,
                )
        }

        fun mapStationButtonTitle(
            station: ChargeStationInfo?,
            enrollmentState: StateFlow<EnrollmentState>,
            walletState: StateFlow<WalletState>,
        ): String = getStationButtonTitleUseCase(station, enrollmentState, walletState)

        /**
         * Initiates the charging process at a station with the selected connector.
         * Makes a real API call to start the charging session.
         *
         * @param station The charging station information
         * @param connectorId The selected connector ID
         * @param evseUid The EVSE (Electric Vehicle Supply Equipment) unique identifier
         * @return Pair of result JSON string and charging ID if successful
         */
        fun startCharging(
            station: ChargeStationInfo,
            connectorId: String,
            evseUid: String,
        ) {
            // Store partner name for CDR fetching later
            currentPartnerName = station.evEvSource

            viewModelScope.launch {
                startChargingUseCase(
                    station = station,
                    connectorId = connectorId,
                    evseUid = evseUid,
                    vehicleInfo = vehicleInfo,
                    paymentMethodId = _defaultPaymentMethod.value ?: "",
                ).collect { result ->
                    if (result != null) {
                        _currentChargingId.value = result.chargingId
                        _startChargingState.value = StartChargingUiState.Success(result.chargingId, result.status)
                    } else {
                        _startChargingState.value = StartChargingUiState.Error("Failed to start charging session.")
                    }
                }
            }
        }

        fun startChargeSessionPolling() {
            sessionPollingJob?.cancel()

            sessionPollingJob =
                viewModelScope.launch {
                    _chargeSessionUiState.value = ChargeSessionUiState.Loading
                    vehicleInfo?.let {
                        getChargeSessionUseCase(it.make, it.vin)
                            .catch { e ->
                                _chargeSessionUiState.value =
                                    ChargeSessionUiState.Error(
                                        e.message
                                            ?: ERROR,
                                    )
                            }.collect { session ->
                                if (session != null) {
                                    val currentState = _chargeSessionUiState.value
                                    val newState = ChargeSessionUiState.Success(session)
                                    _chargeSessionUiState.value = newState

                                    // Check if charging just stopped and fetch CDR details
                                    checkForChargingCompletionAndFetchCdr(currentState, newState)
                                } else {
                                    _chargeSessionUiState.value = ChargeSessionUiState.Empty
                                }
                            }
                    }
                }
        }

        fun startElectricStatusPolling() {
            electricStatusPollingJob?.cancel()
            electricStatusPollingJob =
                viewModelScope.launch {
                    _electricStatusUiState.value = ElectricStatusUiState.Loading
                    vehicleInfo?.let {
                        fetchElectricStatusUseCase(it, oneAppPreferenceModel.getDeviceToken())
                            .catch { e ->
                                _electricStatusUiState.value =
                                    ElectricStatusUiState.Error(
                                        e.message
                                            ?: ERROR,
                                    )
                            }.collect { status ->
                                if (status != null) {
                                    _electricStatusUiState.value = ElectricStatusUiState.Success(status)
                                } else {
                                    _electricStatusUiState.value = ElectricStatusUiState.Empty
                                }
                            }
                    }
                }
        }

        /**
         * Cleans up resources when ViewModel is cleared
         */
        override fun onCleared() {
            super.onCleared()
            resetChargingState()
        }

        /**
         * Sets the station state to Loading to show a loading indicator
         * until the real location is obtained
         */
        fun setInitialLoadingState() {
            viewModelScope.launch {
                setInitialLoadingStateUseCase().collect { state ->
                    _stationState.value = state
                }
            }
        }

        // Fetch the default payment method from wallet
        private fun fetchDefaultPaymentMethod() {
            viewModelScope.launch {
                fetchDefaultPaymentMethodUseCase().collect { paymentMethodId ->
                    _defaultPaymentMethod.value = paymentMethodId
                }
            }
        }

        fun stopCharging(chargingId: String) {
            viewModelScope.launch {
                _stopChargingUiState.value = StopChargingUiState.Loading
                LogTool.d(TAG, "Attempting to stop charging with ID: $chargingId")

                stopChargingUseCase(
                    chargingId = chargingId,
                    vehicleInfo = vehicleInfo,
                ).catch { e ->
                    LogTool.e(TAG, "Error stopping charging: ${e.message}")
                    _stopChargingUiState.value =
                        StopChargingUiState.Error(
                            e.message
                                ?: ERROR,
                        )
                    resetChargingState()
                }.collect { result ->
                    if (result != null) {
                        _stopChargingUiState.value = StopChargingUiState.Success(result)
                    } else {
                        _stopChargingUiState.value = StopChargingUiState.Empty
                    }
                    resetChargingState()
                }
            }
        }

        init {
            // Fetch default payment method when ViewModel is created
            fetchDefaultPaymentMethod()
            _currentChargingId
                .onEach { id ->
                    LogTool.d(TAG, "Charging ID updated in ViewModel: $id")
                }.launchIn(viewModelScope)
            vehicleInfo?.let { vehicle ->
                if (vehicle.remoteDisplay == REMOTE_STATUS_ACTIVATED) {
                    _stationState.update { PublicChargingState.ShowVehicleLocationMapIconsInPublicCharging }
                }
            }
        }

        fun fetchVehicleLocation() {
            viewModelScope.launch {
                vehicleInfo?.let {
                    if (!it.isCY17 && it.hasEvRemoteCapability()) {
                        sharedDataSource.getElectricStatusState().collect { electricStatusState ->
                            if (electricStatusState is ElectricStatusState.Success) {
                                _vehicleLocation.value =
                                    getVehicleLocationUseCase.getVehicleLocationFromElectricStatus(
                                        electricStatusState.response,
                                    )
                            }
                        }
                    }
                }
            }
        }

        fun resetStartChargingState() {
            _startChargingState.value = StartChargingUiState.Idle
        }

        fun getEnabledChargingNetworks(): List<ChargingNetworkType> = getEnabledChargingNetworksTypesUseCase()

        /**
         * Checks if charging has just completed and fetches CDR details if so
         */
        private fun checkForChargingCompletionAndFetchCdr(
            previousState: ChargeSessionUiState,
            newState: ChargeSessionUiState.Success,
        ) {
            // Only proceed if we have a charging ID and partner name
            val chargingId = _currentChargingId.value
            val partnerName = currentPartnerName

            if (chargingId.isNullOrBlank() || partnerName.isNullOrBlank()) {
                LogTool.d(TAG, "Missing charging ID or partner name for CDR fetch")
                return
            }

            // Check if status changed to STOPPED
            val newStatus = newState.session.status.uppercase()
            val wasNotStopped = when (previousState) {
                is ChargeSessionUiState.Success -> previousState.session.status.uppercase() != "STOPPED"
                else -> true
            }

            if (newStatus == "STOPPED" && wasNotStopped) {
                LogTool.d(TAG, "Charging completed, fetching CDR details for charging ID: $chargingId")
                fetchCdrDetails(chargingId, partnerName)
            }
        }

        /**
         * Fetches CDR details for the completed charging session
         */
        private fun fetchCdrDetails(chargingId: String, partnerName: String) {
            viewModelScope.launch {
                fetchCdrDetailsUseCase(
                    chargingId = chargingId,
                    partnerName = partnerName,
                    vehicleInfo = vehicleInfo,
                ).collect { cdrResponse ->
                    _cdrSessionResponse.value = cdrResponse
                    if (cdrResponse != null) {
                        LogTool.d(TAG, "CDR details fetched successfully")
                    } else {
                        LogTool.w(TAG, "CDR details not available")
                    }
                }
            }
        }
    }
