/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.publiccharging.application.usecases

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.entrollment.application.EnrollmentState
import com.toyota.oneapp.features.entrollment.application.WalletState
import com.toyota.oneapp.features.entrollment.domain.model.EnrollmentData
import com.toyota.oneapp.features.entrollment.domain.model.WalletData
import com.toyota.oneapp.features.entrollment.util.Constance
import com.toyota.oneapp.features.findstations.domain.model.MarkerInfo
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.Address
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.BillingDetails
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletCard
import com.toyota.oneapp.features.pay.wallet.dataaccess.servermodel.WalletPaymentMethod
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.domain.model.Coordinates
import com.toyota.oneapp.features.publiccharging.domain.model.EvConnector
import com.toyota.oneapp.features.publiccharging.domain.model.EvConnectorDetails
import com.toyota.oneapp.features.publiccharging.domain.model.EvConnectorSum
import com.toyota.oneapp.features.publiccharging.domain.model.EvEVSE
import com.toyota.oneapp.features.publiccharging.domain.model.EvOpeningTimes
import com.toyota.oneapp.features.publiccharging.domain.model.EvOperator
import com.toyota.oneapp.features.publiccharging.domain.model.EvPriceComponent
import com.toyota.oneapp.features.publiccharging.domain.model.EvPriceElement
import com.toyota.oneapp.features.publiccharging.domain.model.EvTariffAltText
import com.toyota.oneapp.features.publiccharging.domain.model.EvTariffInfo
import io.mockk.unmockkAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GetStationButtonTitleUseCaseTest {
    private lateinit var sut: GetStationButtonTitleUseCase

    private val walletStateRealFlowSuccess: StateFlow<WalletState> =
        MutableStateFlow(
            WalletState.Success(
                data = WalletData(),
            ),
        )

    private val walletStateRealFlowError: StateFlow<WalletState> =
        MutableStateFlow(
            WalletState.Error,
        )

    @Before
    fun setUp() {
        sut = GetStationButtonTitleUseCase()
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    private fun createBasicStation(
        evEvSource: String = "OTHER",
        evIsPartner: Boolean = false,
        evEvses: List<EvEVSE> =
            listOf(
                getUnlockCapableEvse(),
            ),
    ): ChargeStationInfo =
        ChargeStationInfo(
            evId = "test-station",
            stationName = "Test Station",
            evEvSource = evEvSource,
            evIsPartner = evIsPartner,
            evEvses = evEvses,
            addressLine1 = "123 Test Street",
            addressLine2 = "Suite 100",
            evConnectorSum =
                EvConnectorSum(
                    evCcs1 = EvConnectorDetails(total = 1, active = 1),
                    evChademo = EvConnectorDetails(total = 0, active = 0),
                    evJ1772 = EvConnectorDetails(total = 0, active = 0),
                    evNacs = EvConnectorDetails(total = 0, active = 0),
                ),
            is24hoursOpen = true,
            markerInfo =
                MarkerInfo(
                    stationPosition = LatLng(37.7749, -122.4194),
                    stationName = "Test Station",
                ),
            evName = "Test Station",
            evConnectorTypes = listOf("CCS1"),
            evTariffInfo =
                listOf(
                    EvTariffInfo(
                        evCurrency = "USD",
                        evElements =
                            listOf(
                                EvPriceElement(
                                    evPriceComponents =
                                        listOf(
                                            EvPriceComponent(
                                                evPrice = 0.25,
                                                evStepSize = 1,
                                                evType = "ENERGY",
                                            ),
                                        ),
                                ),
                            ),
                        evId = "tariff-1",
                        evPartnerName = "Test Partner",
                        evTariffAltURL = "https://test-tariff.com",
                        evTariffAltText =
                            listOf(
                                EvTariffAltText(
                                    language = "en",
                                    text = "Test Tariff",
                                ),
                            ),
                    ),
                ),
            evOperator =
                EvOperator(
                    name = "Test Operator",
                    website = "https://test-operator.com",
                ),
            evPhoneNumber = "************",
            evPlaceId = "test-place-id",
            evPostalCode = "12345",
            evProvince = "Test Province",
            evAddress = "123 Test Street, Test City, Test Province 12345",
            evCity = "Test City",
            evStatusCode = "Available",
            evStatusSum = "1",
            evTimeZone = "America/New_York",
            evEvDcFastNum = 1,
            evEvLevel1EvseNum = 0,
            evEvLevel2EvseNum = 0,
            evOpeningTimes =
                EvOpeningTimes(
                    evTiming = "24/7",
                    evRegularHour = "24/7",
                ),
            partnerInfoId = "test-partner-id",
        )

    private fun createEnrollmentState(
        isWalletSetupDone: Boolean = true,
        isEnrollmentSetupDone: Boolean = true,
        chargePointStatus: Boolean = true,
        evGoPointStatus: Boolean = true,
    ): StateFlow<EnrollmentState> =
        MutableStateFlow(
            EnrollmentState.Success(
                EnrollmentData().apply {
                    this.isWalletSetupDone = isWalletSetupDone
                    this.isEnrollmentSetupDone = isEnrollmentSetupDone
                    this.chargePointStatus = chargePointStatus
                    this.evGoPointStatus = evGoPointStatus
                },
            ),
        ).asStateFlow()

    private fun createWalletState(hasPaymentMethod: Boolean = true): StateFlow<WalletState> =
        MutableStateFlow(
            WalletState.Success(
                WalletData(
                    paymentMethod =
                        if (hasPaymentMethod) {
                            WalletPaymentMethod(
                                billingDetails =
                                    BillingDetails(
                                        address =
                                            Address(
                                                city = "Test City",
                                                country = "US",
                                                line1 = "123 Test Street",
                                                line2 = "Suite 100",
                                                postalCode = "12345",
                                                state = "Test State",
                                            ),
                                        email = "<EMAIL>",
                                        name = "Test User",
                                        phone = "************",
                                    ),
                                card =
                                    WalletCard(
                                        brand = "visa",
                                        country = "US",
                                        expiryMonth = 12,
                                        expiryYear = 2025,
                                        funding = "credit",
                                        last4 = "4242",
                                    ),
                                createdAt = "2024-01-01T00:00:00Z",
                                default = true,
                                deleted = false,
                                id = "pm_123456789",
                                type = "card",
                                updatedAt = "2024-01-01T00:00:00Z",
                            )
                        } else {
                            null
                        },
                ),
            ),
        ).asStateFlow()

    private fun getUnlockCapableEvse(capabilities: List<String> = listOf("UNLOCK_CAPABLE")) =
        EvEVSE(
            uid = "evse-1",
            evseId = "evse-1",
            status = "AVAILABLE",
            capabilities = capabilities,
            connectors =
                listOf(
                    EvConnector(
                        evAmperage = 50,
                        evChargerLevel = 3,
                        evChargerType = "CCS1",
                        evFormat = "DC",
                        evId = "connector-1",
                        evLastUpdated = "2024-01-01T00:00:00Z",
                        evMaxPower = 50.0,
                        evPowerType = "DC",
                        evStandard = "CCS1",
                        evTariffId = "tariff-1",
                        evVoltage = 400,
                    ),
                ),
            coordinates =
                Coordinates(
                    evLatitude = "37.7749",
                    evLongitude = "-122.4194",
                ),
            floorLevel = "1",
            parkingRestrictions = emptyList(),
            physicalReference = "A1",
            lastUpdated = "2024-01-01T00:00:00Z",
            openingTimes =
                EvOpeningTimes(
                    evTiming = "24/7",
                    evRegularHour = "24/7",
                ),
        )

    @Test
    fun `should return SETUP_WALLET when walletState is Failure`() {
        val result = sut(createBasicStation(), createEnrollmentState(), walletStateRealFlowError)
        Assert.assertEquals(Constance.SETUP_WALLET, result)
    }

    @Test
    fun `should return SETUP_WALLET when not isPartner`() {
        val result = sut(createBasicStation(), createEnrollmentState(), walletStateRealFlowSuccess)
        Assert.assertEquals(Constance.SETUP_WALLET, result)
    }

    @Test
    fun `should return UNLOCK_STATION when not isPartner and partner is chargepoint and status found`() {
        val result =
            sut(
                createBasicStation(
                    evEvSource = Constance.CHARGE_POINT,
                ),
                createEnrollmentState(),
                walletStateRealFlowSuccess,
            )
        Assert.assertEquals(Constance.UNLOCK_STATION, result)
    }

    @Test
    fun `should return REGISTER when not isPartner and partner chargepoint and status not found`() {
        val result =
            sut(
                createBasicStation(
                    evEvSource = Constance.CHARGE_POINT,
                ),
                createEnrollmentState(
                    chargePointStatus = false,
                    evGoPointStatus = false,
                ),
                createWalletState(hasPaymentMethod = false),
            )
        Assert.assertEquals(Constance.REGISTER, result)
    }

    @Test
    fun `should return REGISTER when not isPartner paymentMethod notNull partner chargepoint and status not found`() {
        val result =
            sut(
                createBasicStation(
                    evEvSource = Constance.CHARGE_POINT,
                ),
                createEnrollmentState(
                    chargePointStatus = false,
                    evGoPointStatus = false,
                ),
                createWalletState(hasPaymentMethod = true),
            )
        Assert.assertEquals(Constance.REGISTER, result)
    }

    @Test
    fun `should return SETUP_WALLET when isPartner true wallet not available partner status false`() {
        val result =
            sut(
                createBasicStation(
                    evIsPartner = true,
                ),
                createEnrollmentState(
                    chargePointStatus = false,
                    evGoPointStatus = false,
                    isWalletSetupDone = false,
                    isEnrollmentSetupDone = false,
                ),
                walletStateRealFlowSuccess,
            )
        Assert.assertEquals(Constance.SETUP_WALLET, result)
    }

    @Test
    fun `should return REGISTER when isPartner true wallet available partner status false`() {
        val result =
            sut(
                createBasicStation(
                    evIsPartner = true,
                ),
                createEnrollmentState(
                    chargePointStatus = false,
                    evGoPointStatus = false,
                    isWalletSetupDone = true,
                    isEnrollmentSetupDone = true,
                ),
                walletStateRealFlowSuccess,
            )
        Assert.assertEquals(Constance.REGISTER, result)
    }

    @Test
    fun `should return UNLOCK_STATION when isPartner and partner chargepoint and unlock capable`() {
        val result =
            sut(
                createBasicStation(
                    evEvSource = Constance.CHARGE_POINT,
                    evIsPartner = true,
                ),
                createEnrollmentState(
                    chargePointStatus = true,
                    evGoPointStatus = false,
                ),
                walletStateRealFlowSuccess,
            )
        Assert.assertEquals(Constance.UNLOCK_STATION, result)
    }

    @Test
    fun `should return UNLOCK_STATION when isPartner and partner evgo and unlock capable`() {
        val result =
            sut(
                createBasicStation(
                    evEvSource = Constance.CHARGE_POINT,
                    evIsPartner = true,
                ),
                createEnrollmentState(
                    chargePointStatus = false,
                    evGoPointStatus = true,
                ),
                walletStateRealFlowSuccess,
            )
        Assert.assertEquals(Constance.UNLOCK_STATION, result)
    }

    @Test
    fun `should return START_CHARGING when isPartner and partner chargepoint and not unlock capable`() {
        val result =
            sut(
                createBasicStation(
                    evEvSource = Constance.CHARGE_POINT,
                    evIsPartner = true,
                    evEvses =
                        listOf(
                            getUnlockCapableEvse(
                                capabilities = listOf("DC_FAST"),
                            ),
                        ),
                ),
                createEnrollmentState(
                    chargePointStatus = true,
                    evGoPointStatus = false,
                ),
                walletStateRealFlowSuccess,
            )
        Assert.assertEquals(Constance.START_CHARGING, result)
    }
}
