/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.plugandcharge.enrollment.data.repository

import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.plugandcharge.enrollment.data.api.PlugAndChargeEnrollmentApi
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeEnrollmentStatusDto
import com.toyota.oneapp.features.plugandcharge.enrollment.data.dto.PlugAndChargeStartEnrollmentRequestDto
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.ApiError
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.NetworkStatus
import com.toyota.oneapp.network.models.ApiResponse
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import okhttp3.ResponseBody.Companion.toResponseBody
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test
import retrofit2.Response

class PlugAndChargeEnrollmentRepositoryImplTest {
    private lateinit var repository: PlugAndChargeEnrollmentRepositoryImpl
    private val testDispatcher: TestDispatcher = StandardTestDispatcher()
    private val testScope = TestScope(testDispatcher)

    private val vehicle = VehicleInfo().apply { vin = MOCKED_VIN }

    @Test
    fun `when selected vehicle exists and API returns success, then returns mapped result`() =
        testScope.runTest {
            val expected = PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted

            prepareScenario(
                selectedVehicle = vehicle,
                getStatusResponse =
                    Response.success(
                        ApiResponse(
                            payload =
                                PlugAndChargeEnrollmentStatusDto(
                                    toggleStatus = null,
                                    certificates = null,
                                ),
                        ),
                    ),
                mappedResult = expected,
            )

            val result = repository.getStatusModel()

            assertEquals(Result.success(expected), result)
        }

    @Test
    fun `when selected vehicle is null, then returns failure`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle = null,
            )

            val result = repository.getStatusModel()

            assertTrue(result.isFailure)
        }

    @Test
    fun `when API returns null payload, then returns failure`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle = vehicle,
                getStatusResponse = Response.success(ApiResponse(payload = null)),
            )

            val result = repository.getStatusModel()

            assertTrue(result.isFailure)
        }

    @Test
    fun `when API returns error, then returns failure`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle = vehicle,
                getStatusResponse = Response.error(500, "Internal Server Error".toResponseBody()),
            )

            val result = repository.getStatusModel()

            assertTrue(result.isFailure)
        }

    @Test
    fun `when vehicle info is complete and API returns success, then returns success`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle =
                    VehicleInfo().apply {
                        vin = "VIN123"
                        region = "US"
                        brand = "T"
                    },
                initiateEnrollmentStatus = NetworkStatus.SUCCESS,
            )

            val result = repository.initiateEnrollment()

            assertEquals(Result.success(Unit), result)
        }

    @Test
    fun `when vehicle is null, then returns failure for enrollment`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle = null,
            )

            val result = repository.initiateEnrollment()

            assertTrue(result.isFailure)
        }

    @Test
    fun `when vin is null, then returns failure for enrollment`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle =
                    VehicleInfo().apply {
                        vin = null
                        region = "US"
                        brand = "T"
                    },
            )

            val result = repository.initiateEnrollment()

            assertTrue(result.isFailure)
        }

    @Test
    fun `when region is null, then returns failure for enrollment`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle =
                    VehicleInfo().apply {
                        vin = "VIN123"
                        region = null
                        brand = "T"
                    },
            )

            val result = repository.initiateEnrollment()

            assertTrue(result.isFailure)
        }

    @Test
    fun `when enrollment API returns error, then returns failure`() =
        testScope.runTest {
            prepareScenario(
                selectedVehicle =
                    VehicleInfo().apply {
                        vin = "VIN123"
                        region = "US"
                        brand = "T"
                    },
                initiateEnrollmentStatus = NetworkStatus.FAILED,
            )

            val result = repository.initiateEnrollment()

            assertTrue(result.isFailure)
        }

    private fun prepareScenario(
        selectedVehicle: VehicleInfo? = vehicle,
        getStatusResponse: Response<ApiResponse<PlugAndChargeEnrollmentStatusDto>?>? = null,
        mappedResult: PlugAndChargeEnrollmentStatus = PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted,
        initiateEnrollmentStatus: NetworkStatus = NetworkStatus.SUCCESS,
    ) {
        repository =
            PlugAndChargeEnrollmentRepositoryImpl(
                errorParser = FakeErrorMessageParser(),
                ioContext = testDispatcher,
                api =
                    FakePlugAndChargeEnrollmentApi(
                        getStatusResponse = getStatusResponse,
                        initiateEnrollmentStatus = initiateEnrollmentStatus,
                    ),
                statusMapper = { mappedResult },
                applicationData =
                    ApplicationData().apply {
                        setSelectedVehicle(selectedVehicle)
                    },
            )
    }

    private class FakeErrorMessageParser : ErrorMessageParser {
        override fun onNetworkFailure(throwable: Throwable): ApiError =
            ApiError(
                code = 0,
                message = "network error",
                isNetworkError = true,
            )

        override fun <T> onApiCallFailure(response: Response<T>): ApiError =
            ApiError(
                code = response.code(),
                message = "api error",
                isNetworkError = false,
            )
    }

    private class FakePlugAndChargeEnrollmentApi(
        private val getStatusResponse: Response<ApiResponse<PlugAndChargeEnrollmentStatusDto>?>?,
        private val initiateEnrollmentStatus: NetworkStatus,
    ) : PlugAndChargeEnrollmentApi {
        override suspend fun getPlugAndChargeEnrollmentStatus(vin: String): Response<ApiResponse<PlugAndChargeEnrollmentStatusDto>?> {
            return getStatusResponse ?: error("getStatus should not be called")
        }

        override suspend fun initiateEnrollment(
            vin: String,
            region: String,
            brand: String,
            body: PlugAndChargeStartEnrollmentRequestDto,
        ): Response<ApiResponse<Unit>?> {
            return if (initiateEnrollmentStatus == NetworkStatus.SUCCESS) {
                Response.success(ApiResponse(payload = Unit))
            } else {
                Response.error(500, "error".toResponseBody())
            }
        }
    }

    companion object {
        private const val MOCKED_VIN = "123VIN"
    }
}
