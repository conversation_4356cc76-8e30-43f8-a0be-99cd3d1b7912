/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.chargingnetwork.presentation.mapper

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.chargingnetwork.domain.model.PlugAndChargeNetworkModel
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import org.junit.Assert.assertEquals
import org.junit.Test

class PlugAndChargeEnrollmentNotificationUiMapperImplTest {
    private val mapper = PlugAndChargeEnrollmentNotificationUiMapperImpl()

    private val initialState =
        ChargingNetworksState.Loaded(
            withDataConsent = null,
            plugAndChargeNetwork = PlugAndChargeNetworkModel.Loaded.DataConsentNotAccepted,
        )

    @Test
    fun `when state is InstallationComplete and current is Loaded, then returns Loaded with DataConsentAccepted and enrolled true`() {
        val result =
            mapper.map(
                stateNotified = PlugAndChargeEnrollmentState.InstallationComplete,
                currentChargingNetworksState = initialState,
            )

        val expected =
            ChargingNetworksState.Loaded(
                withDataConsent = null,
                plugAndChargeNetwork =
                    PlugAndChargeNetworkModel.Loaded.DataConsentAccepted(
                        isEnrolled = true,
                    ),
            )

        assertEquals(expected, result)
    }

    @Test
    fun `when state is not InstallationComplete, then returns original ChargingNetworksState`() {
        val result =
            mapper.map(
                stateNotified = PlugAndChargeEnrollmentState.ContainsError.PowerYourVehicleOn(false),
                currentChargingNetworksState = initialState,
            )

        assertEquals(initialState, result)
    }

    @Test
    fun `when current state is not Loaded, then returns original ChargingNetworksState`() {
        val loadingState = ChargingNetworksState.Loading(enabledChargingNetworksAmount = 2)

        val result =
            mapper.map(
                stateNotified = PlugAndChargeEnrollmentState.InstallationComplete,
                currentChargingNetworksState = loadingState,
            )

        assertEquals(loadingState, result)
    }
}
