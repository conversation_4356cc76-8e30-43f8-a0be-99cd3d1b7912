package com.toyota.oneapp.features.chargingnetwork.presentation.viewmodel

import com.toyota.oneapp.features.chargingnetwork.domain.model.ChargingNetworksState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.PlugAndChargeEnrollmentState
import com.toyota.oneapp.features.plugandcharge.enrollment.presentation.state.holder.PlugAndChargeEnrollmentStateHolder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class ChargingNetworksViewModelTest {
    private val testDispatcher = StandardTestDispatcher()
    private val testScope = TestScope(testDispatcher)

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `when viewmodel is created, then state is updated with emitted values`() =
        testScope.runTest {
            val loadedState =
                ChargingNetworksState.Loaded(
                    withDataConsent = null,
                    plugAndChargeNetwork = null,
                )

            val initialChargingNetworksState =
                ChargingNetworksState.Loading(
                    enabledChargingNetworksAmount = 0,
                )

            val initialEnrollmentState = PlugAndChargeEnrollmentState.InstallationComplete

            val viewModel =
                ChargingNetworksViewModel(
                    getEnabledChargingNetworks = { MutableStateFlow(initialChargingNetworksState) },
                    plugAndChargeEnrollmentStateHolder =
                        FakePlugAndChargeEnrollmentStateHolder(
                            initialEnrollmentState,
                        ),
                    plugAndChargeEnrollmentNotificationUiMapper = { _, _ -> loadedState },
                )

            advanceUntilIdle()

            assertEquals(
                loadedState,
                viewModel.uiState.value,
            )
        }

    private class FakePlugAndChargeEnrollmentStateHolder(
        initialEnrollmentState: PlugAndChargeEnrollmentState,
    ) : PlugAndChargeEnrollmentStateHolder {
        private val mutableState = MutableStateFlow(initialEnrollmentState)

        override val state: StateFlow<PlugAndChargeEnrollmentState> = mutableState

        override fun update(newState: PlugAndChargeEnrollmentState) {
            mutableState.value = newState
        }
    }
}
