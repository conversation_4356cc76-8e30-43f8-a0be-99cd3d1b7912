package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.repository.PlugAndChargeEnrollmentRepository
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class GetPlugAndChargeEnrollmentStatusLogicTest {
    private lateinit var logic: GetPlugAndChargeEnrollmentStatusLogic

    @Test
    fun `when repository returns success, then use case returns the same`() =
        runTest {
            val repositoryResult = Result.success(PlugAndChargeEnrollmentStatus.NotEnrolled.NotStarted)
            prepareScenario(repositoryResult)

            val result = logic()

            assertEquals(repositoryResult, result)
        }

    @Test
    fun `when repository returns failure, then use case returns the same`() =
        runTest {
            val exception = Exception("Repository error")
            val repositoryResult = Result.failure<PlugAndChargeEnrollmentStatus>(exception)
            prepareScenario(repositoryResult)

            val result = logic()

            assertEquals(repositoryResult, result)
        }

    private fun prepareScenario(result: Result<PlugAndChargeEnrollmentStatus>) {
        logic = GetPlugAndChargeEnrollmentStatusLogic(FakePlugAndChargeEnrollmentRepository(result))
    }

    private class FakePlugAndChargeEnrollmentRepository(
        private val result: Result<PlugAndChargeEnrollmentStatus>,
    ) : PlugAndChargeEnrollmentRepository {
        override suspend fun initiateEnrollment(): Result<Unit> {
            error("Not used for this test")
        }

        override suspend fun getStatusModel(): Result<PlugAndChargeEnrollmentStatus> = result
    }
}
