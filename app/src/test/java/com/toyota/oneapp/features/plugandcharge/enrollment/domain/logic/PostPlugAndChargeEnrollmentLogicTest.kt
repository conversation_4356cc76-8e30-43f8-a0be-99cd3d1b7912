package com.toyota.oneapp.features.plugandcharge.enrollment.domain.logic

import com.toyota.oneapp.features.plugandcharge.enrollment.domain.model.PlugAndChargeEnrollmentStatus
import com.toyota.oneapp.features.plugandcharge.enrollment.domain.repository.PlugAndChargeEnrollmentRepository
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class PostPlugAndChargeEnrollmentLogicTest {
    private lateinit var logic: PostPlugAndChargeEnrollmentLogic

    @Test
    fun `when repository returns success then logic returns success`() =
        runTest {
            val expectedResult = Result.success(Unit)
            prepareScenario(expectedResult)

            val result = logic()

            assertEquals(Result.success(Unit), result)
        }

    @Test
    fun `when repository returns failure then logic returns failure`() =
        runTest {
            val expectedError = Throwable("Enrollment failed")
            val expectedResult = Result.failure<Unit>(expectedError)
            prepareScenario(expectedResult)

            val result = logic()

            assertEquals(expectedResult, result)
        }

    private fun prepareScenario(result: Result<Unit>) {
        logic = PostPlugAndChargeEnrollmentLogic(FakePlugAndChargeEnrollmentRepository(result))
    }

    private class FakePlugAndChargeEnrollmentRepository(
        private val result: Result<Unit>,
    ) : PlugAndChargeEnrollmentRepository {
        override suspend fun initiateEnrollment(): Result<Unit> = result

        override suspend fun getStatusModel(): Result<PlugAndChargeEnrollmentStatus> {
            error("Not used for this test")
        }
    }
}
